module.exports = {
  default: {
    // Feature files location
    paths: ['features/**/*.feature'],

    // Step definitions and support files
    import: ['tests/bdd/step-definitions/**/*.ts', 'tests/bdd/support/**/*.ts'],

    // TypeScript configuration
    requireModule: ['ts-node/register'],

    // Output format
    format: ['progress-bar', 'json:reports/bdd-results.json', 'html:reports/bdd-report.html'],

    // Parallel execution
    parallel: 1,

    // Retry failed scenarios
    retry: 0,

    // World parameters
    worldParameters: {
      apiBaseUrl: process.env.API_BASE_URL || 'http://localhost:8080/api/v1',
      timeout: 30000,
    },

    // Tags (can be overridden by environment variable)
    tags: process.env.CUCUMBER_TAGS || 'not @skip',
  },

  // Auth-specific profile
  auth: {
    paths: ['features/auth/**/*.feature'],
    import: ['tests/bdd/step-definitions/**/*.ts', 'tests/bdd/support/**/*.ts'],
    requireModule: ['ts-node/register'],
    format: ['progress-bar', 'json:reports/bdd-auth-results.json', 'html:reports/bdd-auth-report.html'],
    parallel: 1,
    retry: 0,
    worldParameters: {
      apiBaseUrl: process.env.API_BASE_URL || 'http://localhost:8080/api/v1',
      timeout: 30000,
    },
    tags: process.env.CUCUMBER_TAGS || '@auth and not @skip',
  },

  // CI profile for continuous integration
  ci: {
    paths: ['features/**/*.feature'],
    import: ['tests/bdd/step-definitions/**/*.ts', 'tests/bdd/support/**/*.ts'],
    requireModule: ['ts-node/register'],
    format: ['progress-bar', 'json:reports/bdd-ci-results.json', 'html:reports/bdd-ci-report.html', 'junit:reports/bdd-junit.xml'],
    parallel: 2,
    retry: 1,
    worldParameters: {
      apiBaseUrl: process.env.API_BASE_URL || 'http://localhost:8080/api/v1',
      timeout: 45000,
    },
    tags: process.env.CUCUMBER_TAGS || '@smoke or @core',
  },

  // Smoke test profile
  smoke: {
    paths: ['features/**/*.feature'],
    import: ['tests/bdd/step-definitions/**/*.ts', 'tests/bdd/support/**/*.ts'],
    requireModule: ['ts-node/register'],
    format: ['progress-bar', 'json:reports/bdd-smoke-results.json', 'html:reports/bdd-smoke-report.html'],
    parallel: 1,
    retry: 0,
    worldParameters: {
      apiBaseUrl: process.env.API_BASE_URL || 'http://localhost:8080/api/v1',
      timeout: 20000,
    },
    tags: process.env.CUCUMBER_TAGS || '@smoke',
  },
};
