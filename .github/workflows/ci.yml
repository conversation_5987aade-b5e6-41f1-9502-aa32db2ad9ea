name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

permissions:
  actions: read
  contents: read

jobs:
  test:
    name: Test & Build
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: '8'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Setup NX
        uses: nrwl/nx-set-shas@v4

      - name: Lint affected projects
        run: pnpm nx affected -t lint

      - name: Test affected projects
        run: pnpm nx affected -t test --coverage

      - name: Build affected projects
        run: pnpm nx affected -t build

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        if: always()
        with:
          directory: ./coverage
