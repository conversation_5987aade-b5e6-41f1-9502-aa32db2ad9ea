name: API Tests

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  workflow_dispatch:

jobs:
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run unit tests
        run: |
          npm run test:auth-service
          npm run test:user-service
          npm run test:tenant-service
          npm run test:notification-service
          npm run test:audit-service

      - name: Upload unit test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: unit-test-results
          path: |
            apps/*/coverage/
            apps/*/test-results.xml

  api-tests:
    name: API Integration Tests
    runs-on: ubuntu-latest
    needs: unit-tests

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: qeep_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Newman
        run: npm install -g newman newman-reporter-htmlextra

      - name: Setup environment variables
        run: |
          echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/qeep_test" >> $GITHUB_ENV
          echo "REDIS_URL=redis://localhost:6379" >> $GITHUB_ENV
          echo "JWT_SECRET=test-jwt-secret-key" >> $GITHUB_ENV
          echo "NODE_ENV=test" >> $GITHUB_ENV

      - name: Run database migrations
        run: |
          npm run db:migrate:auth
          npm run db:migrate:user
          npm run db:migrate:tenant
          npm run db:migrate:notification
          npm run db:migrate:audit

      - name: Start API gateway and services
        run: |
          # Start all services and API gateway
          npm run dev:start

          # Wait for services and gateway to be ready
          sleep 45

      - name: Health check API gateway
        run: |
          curl -f http://localhost:8080/api/v1/health || exit 1

      - name: Run registration API tests
        run: |
          newman run tests/postman/registration-flow.json \
            --environment tests/postman/environments/local.json \
            --reporters cli,junit,htmlextra \
            --reporter-junit-export test-results/registration-flow.xml \
            --reporter-htmlextra-export test-results/registration-flow.html \
            --delay-request 100 \
            --timeout-request 10000

      - name: Run security tests
        run: |
          newman run tests/postman/registration-security-tests.json \
            --environment tests/postman/environments/local.json \
            --reporters cli,junit,htmlextra \
            --reporter-junit-export test-results/security-tests.xml \
            --reporter-htmlextra-export test-results/security-tests.html \
            --delay-request 100 \
            --timeout-request 10000

      - name: Run data-driven tests
        run: |
          newman run tests/postman/registration-flow.json \
            --environment tests/postman/environments/local.json \
            --data tests/postman/data/test-users.csv \
            --reporters cli,junit,htmlextra \
            --reporter-junit-export test-results/data-driven-tests.xml \
            --reporter-htmlextra-export test-results/data-driven-tests.html \
            --delay-request 200 \
            --timeout-request 10000

      - name: Run BDD tests
        run: |
          # Run BDD tests with CI profile
          npx @cucumber/cucumber --config cucumber.config.js --profile ci
        env:
          API_BASE_URL: http://localhost:8080/api/v1
          NODE_ENV: test

      - name: Upload API test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: api-test-results
          path: |
            test-results/
            tests/reports/
            reports/bdd-*.html
            reports/bdd-*.json
            reports/bdd-*.xml

      - name: Publish test results
        uses: dorny/test-reporter@v1
        if: always()
        with:
          name: API Test Results
          path: 'test-results/*.xml'
          reporter: java-junit

  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: api-tests
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install Newman
        run: npm install -g newman

      - name: Run performance tests
        run: |
          newman run tests/postman/registration-flow.json \
            --environment tests/postman/environments/local.json \
            --iteration-count 10 \
            --delay-request 50 \
            --reporters cli,json \
            --reporter-json-export performance-results.json

      - name: Analyze performance results
        run: |
          node -e "
            const results = require('./performance-results.json');
            const avgResponseTime = results.run.timings.responseAverage;
            console.log('Average response time:', avgResponseTime + 'ms');
            if (avgResponseTime > 2000) {
              console.error('Performance degradation detected!');
              process.exit(1);
            }
          "

      - name: Upload performance results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: performance-test-results
          path: performance-results.json
