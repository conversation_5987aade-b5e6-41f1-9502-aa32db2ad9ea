{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@prisma/aml-client": ["node_modules/.prisma/aml-client"], "@prisma/audit-client": ["node_modules/.prisma/audit-client"], "@prisma/auth-client": ["node_modules/.prisma/auth-client"], "@prisma/customer-client": ["node_modules/.prisma/customer-client"], "@prisma/integration-client": ["node_modules/.prisma/integration-client"], "@prisma/monitoring-client": ["node_modules/.prisma/monitoring-client"], "@prisma/notification-client": ["node_modules/.prisma/notification-client"], "@prisma/surveillance-client": ["node_modules/.prisma/surveillance-client"], "@prisma/tenant-client": ["node_modules/.prisma/tenant-client"], "@prisma/transaction-client": ["node_modules/.prisma/transaction-client"], "@prisma/user-client": ["node_modules/.prisma/user-client"], "@qeep/common": ["libs/common/src/index.ts"], "@qeep/contracts": ["libs/contracts/src/index.ts"], "@qeep/contracts/*": ["libs/contracts/src/*"], "@qeep/proto": ["libs/proto/src/index.ts"]}}, "exclude": ["node_modules", "tmp"], "assets": ["proto"]}