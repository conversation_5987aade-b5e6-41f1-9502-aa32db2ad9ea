# Qeep Testing Strategy

This directory contains comprehensive tests for the Qeep microservices architecture using our **Unit Tests + Postman** strategy.

## 🧪 Testing Philosophy

We use a simple, effective two-tier testing approach:

- **Unit Tests (Jest)** - Fast, focused tests for business logic
- **API Tests (Postman + Newman)** - Comprehensive integration and end-to-end testing

## 📁 Directory Structure

```
tests/
├── postman/                    # Postman collections and environments
│   ├── registration-flow.json          # Main registration API tests
│   ├── registration-security-tests.json # Security-focused tests
│   ├── environments/
│   │   └── local.json                   # Local development environment
│   ├── data/
│   │   └── test-users.csv              # Test data for data-driven tests
│   └── newman.config.json              # Newman configuration
├── scripts/
│   └── run-registration-tests.sh       # Test runner script
├── reports/                    # Generated test reports
└── README.md                  # This file
```

## 🚀 Quick Start

### Prerequisites

1. **Newman** (Postman CLI):

   ```bash
   npm install -g newman newman-reporter-htmlextra
   ```

2. **API Gateway and services running**:

   ```bash
   pnpm run dev:start
   ```

   This starts all microservices and the API gateway that routes requests to the appropriate services.

### Running Tests

#### Unit Tests

```bash
# Run all unit tests
pnpm test

# Run specific service tests
pnpm test:auth-service
pnpm test:user-service
pnpm test:tenant-service
```

#### API Tests

```bash
# Run all registration API tests
pnpm test:api

# Run specific test suites
pnpm test:api:registration
pnpm test:api:security

# Run with Newman directly
newman run tests/postman/registration-flow.json \
  --environment tests/postman/environments/local.json
```

## 📊 Test Coverage

### Unit Tests Coverage

#### Auth Service

- ✅ Email validation logic
- ✅ Password strength validation
- ✅ Password hashing/verification
- ✅ Token generation and validation
- ✅ Business logic and data formatting
- ✅ MFA operations
- ✅ WebAuthn functionality
- ✅ Health monitoring

**Total: 96 unit tests**

### API Tests Coverage

#### Registration Flow

- ✅ Happy path user registration
- ✅ Input validation (email, password, names)
- ✅ Duplicate email handling
- ✅ Email verification workflow
- ✅ Login after verification
- ✅ User profile access

#### Security Tests

- ✅ SQL injection prevention
- ✅ XSS attack prevention
- ✅ Large payload handling
- ✅ Invalid content-type rejection
- ✅ Rate limiting enforcement
- ✅ Password complexity bypass attempts
- ✅ Unicode/emoji handling

#### Data-Driven Tests

- ✅ Multiple user scenarios from CSV
- ✅ Valid and invalid data combinations
- ✅ Edge cases and boundary testing

## 🔧 Configuration

### Environment Variables

The tests use these environment variables:

```bash
# API Gateway URL
BASE_URL=http://localhost:8080/api/v1

### Postman Environment

Edit `tests/postman/environments/local.json` to customize:

- Service URLs
- Test credentials
- Timeout settings
- Default values

## 📈 Continuous Integration

### GitHub Actions

Our CI/CD pipeline runs:

1. **Unit Tests** - Fast feedback on every commit
2. **API Tests** - Integration testing on PRs
3. **Performance Tests** - Load testing on main branch

### Test Reports

After running tests, find reports in:

- `tests/reports/registration-flow-report.html`
- `tests/reports/security-tests-report.html`
- `tests/reports/data-driven-tests-report.html`

## 🎯 Writing New Tests

### Adding Unit Tests

1. Create `*.spec.ts` files next to your source code
2. Focus on pure business logic
3. Mock external dependencies
4. Keep tests fast and isolated

Example:

```typescript
describe('UserValidation', () => {
  it('should validate email format', () => {
    expect(isValidEmail('<EMAIL>')).toBe(true);
    expect(isValidEmail('invalid')).toBe(false);
  });
});
```

### Adding API Tests

1. Add requests to existing Postman collections
2. Include comprehensive test scripts
3. Use environment variables for configuration
4. Add both positive and negative test cases

Example test script:

```javascript
pm.test('Registration succeeds', function () {
  pm.response.to.have.status(201);
  const jsonData = pm.response.json();
  pm.expect(jsonData).to.have.property('user');
});
```

## 🚨 Troubleshooting

### Common Issues

1. **Services not running**:

   ```bash
   pnpm run dev:status
   pnpm run dev:start
   ```

2. **Newman not found**:

   ```bash
   npm install -g newman newman-reporter-htmlextra
   ```

3. **Database connection issues**:

   ```bash
   pnpm run prisma:reset
   pnpm run prisma:setup
   ```

4. **Port conflicts**:
   - Check if ports 3001-3005 are available
   - Stop other services using these ports

### Debug Mode

Run tests with verbose output:

```bash
newman run tests/postman/registration-flow.json \
  --environment tests/postman/environments/local.json \
  --verbose
```

## 📚 Best Practices

### Unit Tests

- ✅ Test business logic, not implementation details
- ✅ Use descriptive test names
- ✅ Keep tests independent and isolated
- ✅ Mock external dependencies
- ✅ Aim for fast execution (< 1 second total)

### API Tests

- ✅ Test complete user workflows
- ✅ Include error scenarios
- ✅ Validate response structure and data
- ✅ Check performance requirements
- ✅ Test security vulnerabilities

### General

- ✅ Run tests frequently during development
- ✅ Keep test data realistic but safe
- ✅ Document complex test scenarios
- ✅ Review test failures promptly
- ✅ Update tests when APIs change

## 🎉 Success Metrics

Our testing strategy achieves:

- **Fast Feedback**: Unit tests run in < 1 second
- **High Confidence**: API tests cover real user scenarios
- **Security Assurance**: Comprehensive security testing
- **Easy Maintenance**: Simple, reliable test patterns
- **Great Documentation**: Tests serve as living API docs

Happy testing! 🧪✨
