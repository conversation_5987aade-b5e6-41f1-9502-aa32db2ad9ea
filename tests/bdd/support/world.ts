/* eslint-disable @typescript-eslint/no-explicit-any */
import { IWorldOptions, setWorldConstructor, World } from '@cucumber/cucumber';
import { ApiClient } from '../helpers/api-client';
import { AssertionHelper } from '../helpers/assertion-helper';
import { TestDataManager } from '../helpers/test-data-manager';

export interface QeepWorldParameters {
  apiBaseUrl: string;
  timeout: number;
  retryDelay: number;
}

/**
 * Custom World class for Qeep BDD tests
 * Provides shared context and utilities across all step definitions
 */
export class QeepWorld extends World<QeepWorldParameters> {
  public apiClient: ApiClient;
  public testData: TestDataManager;
  public assertions: AssertionHelper;

  // Test context storage
  public context: {
    userRole: string;
    onboardingData: { institution_name: string; institution_type: string; primary_contact: any; regulatory_info: any; expected_go_live: string; subscription_plan: string };
    workflowId: any;
    tenantCode: any;
    currentUser?: any;
    currentTenant?: any;
    lastResponse?: any;
    lastError?: any;
    responseTime?: number;
    authTokens?: {
      accessToken?: string;
      refreshToken?: string;
    };
    existingUserEmail?: string;
    userType?: string;
    testUsers: Map<string, any>;
    testTenants: Map<string, any>;
  };

  constructor(options: IWorldOptions<QeepWorldParameters>) {
    super(options);

    // Initialize helpers
    this.apiClient = new ApiClient(this.parameters.apiBaseUrl, this.parameters.timeout);
    this.testData = new TestDataManager();
    this.assertions = new AssertionHelper();

    // Initialize context
    this.context = {
      testUsers: new Map(),
      testTenants: new Map(),
    };
  }

  /**
   * Store user data for later reference
   */
  storeUser(key: string, userData: any): void {
    this.context.testUsers.set(key, userData);
  }

  /**
   * Retrieve stored user data
   */
  getUser(key: string): any {
    return this.context.testUsers.get(key);
  }

  /**
   * Store tenant data for later reference
   */
  storeTenant(key: string, tenantData: any): void {
    this.context.testTenants.set(key, tenantData);
  }

  /**
   * Retrieve stored tenant data
   */
  getTenant(key: string): any {
    return this.context.testTenants.get(key);
  }

  /**
   * Set authentication tokens
   */
  setAuthTokens(accessToken: string, refreshToken?: string): void {
    this.context.authTokens = {
      accessToken,
      refreshToken,
    };
    this.apiClient.setAuthToken(accessToken);
  }

  /**
   * Clear authentication tokens
   */
  clearAuthTokens(): void {
    this.context.authTokens = {};
    this.apiClient.clearAuthToken();
  }

  /**
   * Store the last API response
   */
  setLastResponse(response: any): void {
    this.context.lastResponse = response;
    // Store response time if available
    if (response.responseTime) {
      this.context.responseTime = response.responseTime;
    }
  }

  /**
   * Store the last API error
   */
  setLastError(error: any): void {
    this.context.lastError = error;
  }

  /**
   * Get the last response status code
   */
  getLastStatusCode(): number {
    // Prioritize error status if available
    if (this.context.lastError?.status) {
      return this.context.lastError.status;
    }

    // Check for HTTP status code from the response
    if (this.context.lastResponse?.status) {
      return this.context.lastResponse.status;
    }

    // Check for status_code in StandardApiResponse format
    const responseBody = this.getLastResponseBody();
    if (responseBody?.status_code) {
      return responseBody.status_code;
    }

    return 0;
  }

  /**
   * Get the last response body
   */
  getLastResponseBody(): any {
    return this.context.lastResponse?.data || this.context.lastError?.response?.data;
  }

  /**
   * Get the last response data (for StandardApiResponse format)
   */
  getLastResponseData(): any {
    return this.getLastResponseBody();
  }

  /**
   * Get the last error
   */
  getLastError(): any {
    return this.context.lastError;
  }

  /**
   * Parse a data table into a key-value object
   */
  parseDataTable(dataTable: any): Record<string, string> {
    const data: Record<string, string> = {};
    const rows = dataTable.raw();

    for (const row of rows) {
      if (row.length >= 2) {
        data[row[0]] = row[1];
      }
    }

    return data;
  }

  /**
   * Parse a JSON field from string format
   */
  parseJsonField(value: string): any {
    if (!value) return undefined;

    try {
      return JSON.parse(value);
    } catch (error) {
      // If it's not valid JSON, return as string
      return value;
    }
  }

  /**
   * Clean up test data after scenario
   */
  async cleanup(): Promise<void> {
    // Clear authentication
    this.clearAuthTokens();

    // Clear context
    this.context.currentUser = undefined;
    this.context.currentTenant = undefined;
    this.context.lastResponse = undefined;
    this.context.lastError = undefined;

    // Clear test data cache to prevent data leakage between scenarios
    this.testData.clearTestData();

    // Clean up test users and tenants if needed
    // Note: In a real implementation, you might want to delete test data
    // For now, we'll just clear the references
    this.context.testUsers.clear();
    this.context.testTenants.clear();
  }
}

// Set the custom world constructor
setWorldConstructor(QeepWorld);
