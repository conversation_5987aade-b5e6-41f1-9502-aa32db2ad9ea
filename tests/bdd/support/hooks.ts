/* eslint-disable @typescript-eslint/no-explicit-any */
import { After, AfterAll, Before, BeforeAll, Status, setDefaultTimeout } from '@cucumber/cucumber';
import { QeepWorld } from './world';

// Set default timeout for all steps
setDefaultTimeout(30 * 1000); // 30 seconds

/**
 * Global setup before all scenarios
 */
BeforeAll(async function () {
  console.log('🚀 Starting Qeep BDD Test Suite');

  // Verify API is running
  try {
    const world = new QeepWorld({
      parameters: {
        apiBaseUrl: process.env.API_BASE_URL || 'http://localhost:8080/api/v1',
        timeout: 30000,
        retryDelay: 1000,
      },
    } as any);

    await world.apiClient.healthCheck();
    console.log('✅ API health check passed');
  } catch (error) {
    console.error('❌ API health check failed:', error);
    console.error('Make sure the API gateway and services are running with: pnpm run dev:start');
    process.exit(1);
  }
});

/**
 * Setup before each scenario
 */
Before(async function (this: QeepWorld, scenario) {
  console.log(`\n🧪 Starting scenario: ${scenario.pickle.name}`);

  // Log scenario tags for debugging
  const tags = scenario.pickle.tags.map((tag) => tag.name).join(', ');
  if (tags) {
    console.log(`🏷️  Tags: ${tags}`);
  }

  // Clear any existing context
  await this.cleanup();

  // Set default tenant for multi-tenant scenarios
  if (scenario.pickle.tags.some((tag) => tag.name === '@tenant')) {
    // You can set a default tenant ID here if needed
    // this.apiClient.setTenantId('default-tenant-id');
  }
});

/**
 * Cleanup after each scenario
 */
After(async function (this: QeepWorld, scenario) {
  const status = scenario.result?.status;

  if (status === Status.FAILED) {
    console.log(`❌ Scenario failed: ${scenario.pickle.name}`);

    // Log the last response/error for debugging
    if (this.context.lastError) {
      console.log('Last error:', JSON.stringify(this.context.lastError, null, 2));
    }
    if (this.context.lastResponse) {
      console.log('Last response:', JSON.stringify(this.context.lastResponse, null, 2));
    }

    // Attach screenshot or additional debug info if needed
    // this.attach(JSON.stringify(this.context, null, 2), 'application/json');
  } else if (status === Status.PASSED) {
    console.log(`✅ Scenario passed: ${scenario.pickle.name}`);
  }

  // Clean up test data
  await this.cleanup();
});

/**
 * Global cleanup after all scenarios
 */
AfterAll(async function () {
  console.log('\n🏁 Qeep BDD Test Suite completed');

  // Perform any global cleanup here
  // For example, clean up test databases, stop test services, etc.
});

/**
 * Hook for scenarios tagged with @auth
 * Sets up authentication context
 */
Before({ tags: '@auth' }, async function (this: QeepWorld) {
  console.log('🔐 Setting up authentication context');

  // You can pre-create test users or set up auth context here
  // For now, we'll let each scenario handle its own auth setup
});

/**
 * Hook for scenarios tagged with @cleanup
 * Ensures thorough cleanup after scenario
 */
After({ tags: '@cleanup' }, async function (this: QeepWorld) {
  console.log('🧹 Performing thorough cleanup');

  // Additional cleanup for scenarios that need it
  // This could include deleting test users, clearing caches, etc.

  // Clear all stored test data
  this.testData.clearTestData();
});

/**
 * Hook for scenarios tagged with @slow
 * Increases timeout for slow scenarios
 */
Before({ tags: '@slow' }, async function (this: QeepWorld) {
  console.log('⏱️  Setting extended timeout for slow scenario');

  // Cucumber timeout is set globally, but you can add retries or other handling here
});

/**
 * Hook for scenarios tagged with @skip
 * Skips scenarios that shouldn't run in current environment
 */
Before({ tags: '@skip' }, async function () {
  console.log('⏭️  Skipping scenario marked with @skip tag');
  return 'skipped';
});

/**
 * Hook for scenarios tagged with @manual
 * Skips scenarios that require manual intervention
 */
Before({ tags: '@manual' }, async function () {
  if (process.env.CUCUMBER_MANUAL !== 'true') {
    console.log('⏭️  Skipping manual scenario (set CUCUMBER_MANUAL=true to run)');
    return 'skipped';
  }
});

/**
 * Hook for debugging - only runs when DEBUG environment variable is set
 */
Before(async function (this: QeepWorld, scenario) {
  if (process.env.DEBUG === 'true') {
    console.log('🐛 Debug mode enabled');
    console.log('Scenario:', scenario.pickle.name);
    console.log('World context:', this.context);
  }
});
