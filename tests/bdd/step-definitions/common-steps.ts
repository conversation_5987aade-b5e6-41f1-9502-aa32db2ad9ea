import { Given, Then, When } from '@cucumber/cucumber';
import { QeepWorld } from '../support/world';

/**
 * Common step definitions used across multiple features
 */

// Background steps
Given('the API gateway is running at {string}', async function (this: QeepWorld, apiUrl: string) {
  // Update the API client base URL if different from default
  if (apiUrl !== this.parameters.apiBaseUrl) {
    this.apiClient = new (require('../helpers/api-client').ApiClient)(apiUrl, this.parameters.timeout);
  }

  // Verify the API is accessible
  try {
    const response = await this.apiClient.healthCheck();
    this.setLastResponse(response);
    this.assertions.assertStatusCode(response.status, 200, 'API health check');
  } catch (error) {
    this.setLastError(error);
    throw new Error(`API gateway is not accessible at ${apiUrl}. Make sure services are running.`);
  }
});

// Health check steps
When('I check the health endpoint', async function (this: QeepWorld) {
  const startTime = Date.now();

  try {
    const response = await this.apiClient.healthCheck();
    this.setLastResponse(response);
    this.context.responseTime = Date.now() - startTime;
  } catch (error) {
    this.setLastError(error);
    this.context.responseTime = Date.now() - startTime;
    throw error;
  }
});

Then('the system should be available', function (this: QeepWorld) {
  const statusCode = this.getLastStatusCode();
  this.assertions.assertStatusCode(statusCode, 200, 'system availability');
});

Then('response time should be under {int} seconds', function (this: QeepWorld, maxSeconds: number) {
  const responseTime = this.context.responseTime || 0;
  const maxTime = maxSeconds * 1000; // Convert to milliseconds

  if (responseTime >= maxTime) {
    throw new Error(`Response time ${responseTime}ms exceeded maximum ${maxTime}ms`);
  }
});

// Response validation steps
Then('I should receive a {int} {word} response', function (this: QeepWorld, statusCode: number, statusText: string) {
  const actualStatusCode = this.getLastStatusCode();
  this.assertions.assertStatusCode(actualStatusCode, statusCode, `${statusCode} ${statusText} response`);
});

Then('I should receive a {int} Bad Request response', function (this: QeepWorld, statusCode: number) {
  const actualStatusCode = this.getLastStatusCode();
  this.assertions.assertStatusCode(actualStatusCode, statusCode, `${statusCode} Bad Request response`);
});

Then('the response should contain my user data', function (this: QeepWorld) {
  const responseBody = this.getLastResponseBody();

  // The API returns StandardApiResponse format: { success, status_code, message, data: { user_id, email, ... } }
  this.assertions.assertFieldExists(responseBody, 'data', 'response data');

  const userData = responseBody.data;

  // Check for common user data fields (API uses snake_case)
  const expectedFields = ['user_id', 'email', 'first_name', 'last_name'];
  this.assertions.assertResponseContains(userData, expectedFields, 'user data');

  // Ensure sensitive data is not included
  this.assertions.assertResponseDoesNotContain(userData, ['password', 'password_hash'], 'user data security');

  // Verify the response follows StandardApiResponse format
  this.assertions.assertFieldValue(responseBody, 'success', true, 'response success indicator');
});

Then('my password should not be returned', function (this: QeepWorld) {
  const responseBody = this.getLastResponseBody();
  this.assertions.assertResponseDoesNotContain(responseBody, ['password'], 'password security');
});

Then('the error should mention {string}', function (this: QeepWorld, expectedText: string) {
  const error = this.context.lastError;
  this.assertions.assertErrorMessage(error, expectedText, 'error message validation');
});

// Token validation steps
Then('I should receive an access token', function (this: QeepWorld) {
  const responseBody = this.getLastResponseBody();

  // Check for access_token in the data field (snake_case from API)
  if (responseBody.data && responseBody.data.access_token) {
    this.assertions.assertFieldExists(responseBody.data, 'access_token', 'access token');
    this.assertions.assertValidJWT(responseBody.data.access_token, 'access token format');
    // Store the token for future use
    this.setAuthTokens(responseBody.data.access_token, responseBody.data.refresh_token);
  } else {
    // Fallback to camelCase for backward compatibility
    this.assertions.assertFieldExists(responseBody, 'accessToken', 'access token');
    this.assertions.assertValidJWT(responseBody.accessToken, 'access token format');
    // Store the token for future use
    this.setAuthTokens(responseBody.accessToken, responseBody.refreshToken);
  }
});

Then('I should receive a refresh token', function (this: QeepWorld) {
  const responseBody = this.getLastResponseBody();

  // Check for refresh_token in the data field (snake_case from API)
  if (responseBody.data && responseBody.data.refresh_token) {
    this.assertions.assertFieldExists(responseBody.data, 'refresh_token', 'refresh token');
    this.assertions.assertValidJWT(responseBody.data.refresh_token, 'refresh token format');
  } else {
    // Fallback to camelCase for backward compatibility
    this.assertions.assertFieldExists(responseBody, 'refreshToken', 'refresh token');
    this.assertions.assertValidJWT(responseBody.refreshToken, 'refresh token format');
  }
});

Then('the tokens should be valid', function (this: QeepWorld) {
  const responseBody = this.getLastResponseBody();

  // Check for tokens in data field (snake_case from API)
  if (responseBody.data) {
    if (responseBody.data.access_token) {
      this.assertions.assertValidJWT(responseBody.data.access_token, 'access token validity');
    }
    if (responseBody.data.refresh_token) {
      this.assertions.assertValidJWT(responseBody.data.refresh_token, 'refresh token validity');
    }
  } else {
    // Fallback to camelCase for backward compatibility
    if (responseBody.accessToken) {
      this.assertions.assertValidJWT(responseBody.accessToken, 'access token validity');
    }
    if (responseBody.refreshToken) {
      this.assertions.assertValidJWT(responseBody.refreshToken, 'refresh token validity');
    }
  }
});

// Data table parsing steps
When('I register with:', async function (this: QeepWorld, dataTable) {
  const userData = this.testData.parseTableData(dataTable.raw());

  // Replace placeholders in the data
  const processedData = this.testData.replacePlaceholders(userData, {
    testUserEmail: this.testData.generateEmail(),
  });

  // Store the user data for later reference
  this.storeUser('currentRegistration', processedData);

  try {
    const response = await this.apiClient.register(processedData);
    this.setLastResponse(response);
  } catch (error) {
    this.setLastError(error);
    throw error;
  }
});

// Email verification steps
Then('a verification email should be sent', function (this: QeepWorld) {
  const responseBody = this.getLastResponseBody();

  // Ensure we have the StandardApiResponse format
  this.assertions.assertFieldExists(responseBody, 'data', 'response data');

  const userData = responseBody.data;

  // Check if response indicates email was sent (API uses snake_case)
  this.assertions.assertFieldExists(userData, 'verification_email_sent', 'verification email status');
  this.assertions.assertFieldValue(userData, 'verification_email_sent', true, 'verification email sent');

  // Verify token exists and is valid format
  this.assertions.assertFieldExists(userData, 'verification_token', 'verification token');

  const token = userData.verification_token;

  // Verify token is a valid UUID format (verification tokens are UUIDs)
  if (token) {
    this.assertions.assertValidUUID(token, 'verification token format');
  }

  // Store token for potential use in verification tests
  if (token) {
    this.testData.storeTestData('latestVerificationToken', token);
  }
});

// User context steps
Given('I have a registered account', async function (this: QeepWorld) {
  // Create a test user for scenarios that need an existing account
  const userData = this.testData.generateUserData();

  try {
    const response = await this.apiClient.register(userData);
    this.setLastResponse(response);

    // Store user data with verification token from response
    const responseData = response.data.data;
    const userWithToken = {
      ...userData,
      user_id: responseData.user_id,
      verification_token: responseData.verification_token,
    };

    this.storeUser('currentUser', userWithToken);
    this.storeUser('currentRegistration', userWithToken); // Also store as currentRegistration for compatibility
    this.context.currentUser = userWithToken;
  } catch (error) {
    this.setLastError(error);
    throw new Error('Failed to create test user for scenario');
  }
});

Given('I have verified my email address', async function (this: QeepWorld) {
  try {
    // Get the current user
    const currentUser = this.getUser('currentRegistration');
    if (!currentUser) {
      throw new Error('No user available for email verification');
    }

    // Get the user ID from the last response (registration response)
    const lastResponse = this.context.lastResponse;
    if (!lastResponse || !lastResponse.data || !lastResponse.data.data) {
      throw new Error('No registration response available');
    }

    const userId = lastResponse.data.data.user_id;

    if (!userId) {
      console.log('📧 Registration response data:', JSON.stringify(lastResponse.data.data, null, 2));
      throw new Error('No user ID available from registration response');
    }

    console.log('📧 Looking up verification token for user:', userId);

    // Get verification token from Redis (same approach as verification tests)
    const Redis = require('ioredis');
    const redis = new Redis({
      host: 'localhost',
      port: 6379,
      password: 'redis_dev_password',
      db: 0,
    });

    // Add a small delay to ensure the token is stored in Redis
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Find the verification token for this user
    const pattern = 'verification:*';
    const keys = await redis.keys(pattern);

    let verificationToken = null;
    for (const key of keys) {
      const storedUserId = await redis.get(key);
      if (storedUserId === userId) {
        verificationToken = key.replace('verification:', '');
        break;
      }
    }

    if (!verificationToken) {
      console.log(`🔍 Searched ${keys.length} verification keys in Redis`);
      console.log(`🔍 Looking for userId: ${userId}`);
      throw new Error(`No verification token found in Redis for user: ${userId}`);
    }

    console.log('📧 Retrieved verification token from Redis:', verificationToken);

    // Call the actual verification endpoint (GET request with query parameter)
    const response = await this.apiClient.get(`/auth/verify-email?token=${verificationToken}`);

    console.log('✅ Email verification successful');

    await redis.disconnect();

    // Update user status and ensure email is available for login
    if (this.context.currentUser) {
      this.context.currentUser.emailVerified = true;
      this.context.currentUser.status = 'ACTIVE';
      // Ensure email is available from the verification response
      if (response.data.data.user?.email) {
        this.context.currentUser.email = response.data.data.user.email;
      }
    }

    // Store the verification response
    this.setLastResponse(response);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.log('❌ Email verification failed:', errorMessage);
    this.setLastError(error);
    throw new Error('Failed to verify email address');
  }
});

// Authentication context steps
Given('I am authenticated with a valid token', async function (this: QeepWorld) {
  // Ensure we have a user and are logged in
  if (!this.context.currentUser) {
    // Create a user account
    const userData = this.testData.generateUserData();

    try {
      const response = await this.apiClient.register(userData);
      this.setLastResponse(response);
      this.storeUser('authenticatedUser', userData);
      this.context.currentUser = userData;

      // Mark as verified (in real implementation, this would involve email verification)
      this.context.currentUser.emailVerified = true;
    } catch (error) {
      this.setLastError(error);
      throw new Error('Failed to create authenticated test user');
    }
  }

  // Login to get tokens
  const credentials = {
    email: this.context.currentUser.email,
    password: this.context.currentUser.password,
  };

  try {
    const response = await this.apiClient.login(credentials);
    this.setLastResponse(response);

    const responseBody = this.getLastResponseBody();
    if (responseBody.accessToken) {
      this.setAuthTokens(responseBody.accessToken, responseBody.refreshToken);
    }
  } catch (error) {
    this.setLastError(error);
    throw new Error('Failed to authenticate test user');
  }
});

// Profile management steps
When('I request my profile information', async function (this: QeepWorld) {
  try {
    const response = await this.apiClient.getProfile();
    this.setLastResponse(response);
  } catch (error) {
    this.setLastError(error);
    throw error;
  }
});

Then('the response should include my email', function (this: QeepWorld) {
  const responseBody = this.getLastResponseBody();
  this.assertions.assertFieldExists(responseBody, 'email', 'profile email');
  this.assertions.assertValidEmail(responseBody.email, 'profile email format');
});

Then('the response should include my first and last name', function (this: QeepWorld) {
  const responseBody = this.getLastResponseBody();
  this.assertions.assertFieldExists(responseBody, 'firstName', 'profile first name');
  this.assertions.assertFieldExists(responseBody, 'lastName', 'profile last name');
});
