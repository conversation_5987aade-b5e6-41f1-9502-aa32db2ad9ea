import { Given, Then, When } from '@cucumber/cucumber';
import { QeepWorld } from '../support/world';

// =============================================================================
// GIVEN STEPS - Setup
// =============================================================================

Given('I have registered an account with email {string}', async function (this: QeepWorld, email: string) {
  // Generate a unique email based on the provided email to avoid conflicts
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 8);
  const [localPart, domain] = email.split('@');
  const uniqueEmail = `${localPart}.${timestamp}.${randomSuffix}@${domain}`;

  const userData = this.testData.generateUserData({
    email: uniqueEmail,
  });

  try {
    const response = await this.apiClient.register(userData);
    this.setLastResponse(response);
    this.storeUser('registeredUser', userData);

    // Extract user_id from the response and add it to currentUser
    const responseData = response.data?.data;
    const userId = responseData?.user_id;

    this.context.currentUser = {
      ...userData,
      userId: userId,
      email: responseData?.email || userData.email,
    };

    // Store both the original email pattern and the actual unique email used
    this.testData.storeTestData('originalEmailPattern', email);
    this.testData.storeTestData('registeredEmail', uniqueEmail);

    console.log(`✅ Registered user with unique email: ${uniqueEmail} (based on pattern: ${email})`);
  } catch (error) {
    this.setLastError(error);
    console.error(`❌ Failed to register user with email ${uniqueEmail}:`, error instanceof Error ? error.message : String(error));
    throw new Error(`Failed to register user with email ${email}`);
  }
});

// =============================================================================
// WHEN STEPS - Actions
// =============================================================================

When('I check if email {string} exists', async function (this: QeepWorld, email: string) {
  try {
    const response = await this.apiClient.checkEmail(email);
    this.setLastResponse(response);
  } catch (error) {
    this.setLastError(error);
    // Don't throw here - we want to test error responses
  }
});

When('I check if the registered email exists', async function (this: QeepWorld) {
  const registeredEmail = this.testData.getTestData('registeredEmail');
  if (!registeredEmail) {
    throw new Error('No registered email found. Make sure to register an account first.');
  }

  try {
    const response = await this.apiClient.checkEmail(registeredEmail);
    this.setLastResponse(response);
    console.log(`🔍 Checking if registered email exists: ${registeredEmail}`);
  } catch (error) {
    this.setLastError(error);
    // Don't throw here - we want to test error responses
  }
});

When('I check if the registered email exists in lowercase', async function (this: QeepWorld) {
  const registeredEmail = this.testData.getTestData('registeredEmail');
  if (!registeredEmail) {
    throw new Error('No registered email found. Make sure to register an account first.');
  }

  const lowercaseEmail = registeredEmail.toLowerCase();

  try {
    const response = await this.apiClient.checkEmail(lowercaseEmail);
    this.setLastResponse(response);
    console.log(`🔍 Checking if registered email exists in lowercase: ${lowercaseEmail} (original: ${registeredEmail})`);
  } catch (error) {
    this.setLastError(error);
    // Don't throw here - we want to test error responses
  }
});

When('I check email existence without providing email parameter', async function (this: QeepWorld) {
  try {
    // Make request without email parameter
    const response = await this.apiClient.checkEmailWithoutParam();
    this.setLastResponse(response);
  } catch (error) {
    this.setLastError(error);
    // Don't throw here - we want to test error responses
  }
});

When('I check if email with 255+ characters exists', async function (this: QeepWorld) {
  // Create an email that exceeds 255 characters
  const longEmail = 'a'.repeat(250) + '@example.com'; // 261 characters

  try {
    const response = await this.apiClient.checkEmail(longEmail);
    this.setLastResponse(response);
  } catch (error) {
    this.setLastError(error);
    // Don't throw here - we want to test error responses
  }
});

When('I check multiple emails rapidly', async function (this: QeepWorld) {
  const emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];

  const responses = [];

  try {
    // Make multiple rapid requests
    for (const email of emails) {
      const response = await this.apiClient.checkEmail(email);
      responses.push(response);
    }

    // Store the last response and all responses for validation
    this.setLastResponse(responses[responses.length - 1]);
    this.testData.storeTestData('multipleResponses', responses);
  } catch (error) {
    this.setLastError(error);
    // Don't throw here - we want to test error responses
  }
});

// =============================================================================
// THEN STEPS - Assertions
// =============================================================================

Then('the response should indicate email exists', function (this: QeepWorld) {
  const responseBody = this.getLastResponseBody();

  // Check that the response indicates the email exists
  if (responseBody.data && typeof responseBody.data.exists === 'boolean') {
    if (!responseBody.data.exists) {
      throw new Error('Response should indicate email exists but it shows as not existing');
    }
  } else {
    throw new Error('Response should contain exists field in data');
  }
});

Then('the response should indicate email does not exist', function (this: QeepWorld) {
  const responseBody = this.getLastResponseBody();

  // Check that the response indicates the email does not exist
  if (responseBody.data && typeof responseBody.data.exists === 'boolean') {
    if (responseBody.data.exists) {
      throw new Error('Response should indicate email does not exist but it shows as existing');
    }
  } else {
    throw new Error('Response should contain exists field in data');
  }
});

Then('the error should mention invalid email format', function (this: QeepWorld) {
  const error = this.context.lastError;

  const expectedMessages = ['invalid', 'email', 'format', 'valid'];
  let found = false;

  for (const message of expectedMessages) {
    try {
      this.assertions.assertErrorMessage(error, message, 'invalid email format');
      found = true;
      break;
    } catch {
      // Continue checking other messages
    }
  }

  if (!found) {
    throw new Error('Error message should mention invalid email format');
  }
});

Then('the error should mention missing email parameter', function (this: QeepWorld) {
  const error = this.context.lastError;

  const expectedMessages = ['missing', 'required', 'email', 'parameter'];
  let found = false;

  for (const message of expectedMessages) {
    try {
      this.assertions.assertErrorMessage(error, message, 'missing email parameter');
      found = true;
      break;
    } catch {
      // Continue checking other messages
    }
  }

  if (!found) {
    throw new Error('Error message should mention missing email parameter');
  }
});

Then('the error should mention email too long', function (this: QeepWorld) {
  const error = this.context.lastError;

  const expectedMessages = ['too long', 'length', 'maximum', 'exceed'];
  let found = false;

  for (const message of expectedMessages) {
    try {
      this.assertions.assertErrorMessage(error, message, 'email too long');
      found = true;
      break;
    } catch {
      // Continue checking other messages
    }
  }

  if (!found) {
    throw new Error('Error message should mention email too long');
  }
});

Then('all requests should receive 200 OK responses', function (this: QeepWorld) {
  const responses = this.testData.getTestData('multipleResponses');

  if (!responses || !Array.isArray(responses)) {
    throw new Error('Multiple responses not found in test data');
  }

  for (let i = 0; i < responses.length; i++) {
    const response = responses[i];
    if (response.status !== 200) {
      throw new Error(`Request ${i + 1} returned status ${response.status} instead of 200`);
    }
  }
});

Then('no rate limiting should be applied', function (this: QeepWorld) {
  const responses = this.testData.getTestData('multipleResponses');

  if (!responses || !Array.isArray(responses)) {
    throw new Error('Multiple responses not found in test data');
  }

  for (let i = 0; i < responses.length; i++) {
    const response = responses[i];
    if (response.status === 429) {
      throw new Error(`Request ${i + 1} was rate limited (status 429)`);
    }
  }
});
