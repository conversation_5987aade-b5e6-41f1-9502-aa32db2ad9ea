# Qeep BDD Tests

Behavior-Driven Development tests for the Qeep platform using Cucumber.js.

## 🚀 Quick Start

```bash
# Run all BDD tests
pnpm test:bdd

# Run authentication tests only
pnpm test:bdd:auth

# Run smoke tests
pnpm test:bdd:smoke
```

## 📁 Structure

```
tests/bdd/
├── step-definitions/     # Step implementations
├── support/             # Test infrastructure
├── helpers/             # Utility classes
└── README.md           # This file
```

## 🧪 Available Step Definitions

### Common Steps

```gherkin
Given the API gateway is running at "http://localhost:8080/api/v1"
When I check the health endpoint
Then I should receive a 200 OK response
Then the response should contain my user data
Then my password should not be returned
```

### Registration Steps

```gherkin
Given I am a new user with valid business email
When I register with:
  | email     | <EMAIL> |
  | password  | SecurePass123!   |
  | firstName | John             |
  | lastName  | Doe              |
Then I should receive a 201 Created response
Then the error should mention required fields
```

### Login Steps

```gherkin
Given I have a verified account
When I login with correct credentials:
  | email    | <EMAIL> |
  | password | SecurePass123!   |
Then I should receive an access token
Then I should receive a refresh token
Then the tokens should be valid
```

### Verification Steps

```gherkin
Given I have received a verification email with valid token
When I click the verification link with my token
Then my email should be marked as verified
Then the verification message should confirm success
```

## 🏷️ Available Tags

| Tag | Description | Example Usage |
|-----|-------------|---------------|
| `@auth` | Authentication features | `--tags "@auth"` |
| `@smoke` | Critical path tests | `--tags "@smoke"` |
| `@happy-path` | Success scenarios | `--tags "@happy-path"` |
| `@negative` | Error scenarios | `--tags "@negative"` |
| `@security` | Security tests | `--tags "@security"` |

## 🔧 Writing New Tests

### 1. Create Feature File

```gherkin
@auth
Feature: New Feature
  As a user
  I want to do something
  So that I can achieve a goal

  @smoke @happy-path
  Scenario: Successful Action
    Given I have the required context
    When I perform the action
    Then I should see the expected result
```

### 2. Implement Step Definitions

```typescript
import { Given, When, Then } from '@cucumber/cucumber';
import { QeepWorld } from '../support/world';

Given('I have the required context', function (this: QeepWorld) {
  // Setup test context
});

When('I perform the action', async function (this: QeepWorld) {
  // Execute the action
  const response = await this.apiClient.post('/endpoint', data);
  this.setLastResponse(response);
});

Then('I should see the expected result', function (this: QeepWorld) {
  // Verify the result
  const statusCode = this.getLastStatusCode();
  this.assertions.assertStatusCode(statusCode, 200);
});
```

## 🧰 Helper Classes

### API Client

```typescript
// HTTP requests
await this.apiClient.get('/users');
await this.apiClient.post('/auth/login', credentials);
await this.apiClient.put('/users/123', userData);

// Authentication
this.apiClient.setAuthToken(token);
this.apiClient.setTenantId(tenantId);
```

### Test Data Manager

```typescript
// Generate test data
const userData = this.testData.generateUserData();
const email = this.testData.generateEmail();
const password = this.testData.generatePassword();

// Parse data tables
const data = this.testData.parseTableData(dataTable.raw());

// Replace placeholders
const processed = this.testData.replacePlaceholders(data, {
  random: 'abc123'
});
```

### Assertions

```typescript
// Status codes
this.assertions.assertStatusCode(response.status, 201);

// Field validation
this.assertions.assertFieldExists(response, 'id');
this.assertions.assertValidEmail(response.email);
this.assertions.assertValidJWT(response.accessToken);

// Error messages
this.assertions.assertErrorMessage(error, 'invalid credentials');
```

## 🎯 Context Management

### Storing Data

```typescript
// Store user data
this.storeUser('registeredUser', userData);
this.storeTenant('testTenant', tenantData);

// Store authentication tokens
this.setAuthTokens(accessToken, refreshToken);

// Store API responses
this.setLastResponse(response);
this.setLastError(error);
```

### Retrieving Data

```typescript
// Get stored data
const user = this.getUser('registeredUser');
const tenant = this.getTenant('testTenant');

// Get response data
const statusCode = this.getLastStatusCode();
const responseBody = this.getLastResponseBody();
```

## 🐛 Debugging

### Debug Mode

```bash
DEBUG=true pnpm test:bdd
```

### Run Single Scenario

```bash
npx cucumber-js --config cucumber.config.js --name "Scenario Name"
```

### Check Step Definitions

```bash
npx cucumber-js --config cucumber.config.js --dry-run
```

## 📊 Reports

After running tests, check the `reports/` directory:

- `bdd-report.html` - Interactive HTML report
- `bdd-results.json` - JSON results
- `bdd-junit.xml` - JUnit format

## 🔗 Related Documentation

- [BDD Framework Guide](../../docs/testing/bdd-framework.md) - Comprehensive documentation
- [Testing Strategy](../../docs/testing/testing-framework.md) - Overall testing approach
- [API Documentation](../../docs/services/api-reference.md) - API reference

## 💡 Tips

1. **Start services first**: `pnpm run dev:start`
2. **Use unique test data**: Avoid conflicts between tests
3. **Tag scenarios appropriately**: Enable selective execution
4. **Write readable scenarios**: Use business language
5. **Keep steps atomic**: One action per step
6. **Handle errors gracefully**: Don't throw on expected failures

## 🆘 Common Issues

### API Not Running
```bash
pnpm run dev:start
# Wait for services to start
```

### Step Definition Not Found
- Check file is in `step-definitions/` directory
- Verify step text matches exactly
- Ensure proper imports

### TypeScript Errors
```bash
npx tsc --noEmit -p tests/bdd/tsconfig.json
```

### Test Failures
- Check API health: `curl http://localhost:8080/api/v1/health`
- Review test reports in `reports/` directory
- Enable debug mode for detailed output
