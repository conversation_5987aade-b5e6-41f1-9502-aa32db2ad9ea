{"extends": "../../tsconfig.base.json", "compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "../..", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "incremental": true, "types": ["node", "jest"]}, "include": ["**/*.ts", "../../features/**/*.feature"], "exclude": ["node_modules", "dist"], "ts-node": {"files": true, "transpileOnly": true, "compiler": "typescript", "esm": false, "compilerOptions": {"module": "commonjs"}}}