/* eslint-disable @typescript-eslint/no-explicit-any */
import assert from 'assert';

/**
 * Assertion Helper for BDD tests
 * Provides reusable assertion methods with descriptive error messages
 */
export class AssertionHelper {
  assertProperty(response: any, arg1: string, arg2: string) {
    throw new Error('Method not implemented.');
  }
  assertIsArray(tasks_generated: any, arg1: string) {
    throw new Error('Method not implemented.');
  }
  assertGreaterThan(length: any, arg1: number, arg2: string) {
    throw new Error('Method not implemented.');
  }
  assertNotEmpty(assigned_specialist: any, arg1: string) {
    throw new Error('Method not implemented.');
  }
  assertLessThan(responseTime: number, maxReasonableTime: number, arg2: string) {
    throw new Error('Method not implemented.');
  }
  assertContains(arg0: any, arg1: string, arg2: string) {
    throw new Error('Method not implemented.');
  }
  assertUndefined(workflow_id: any, arg1: string) {
    throw new Error('Method not implemented.');
  }
  /**
   * Assert HTTP status code
   */
  assertStatusCode(actual: number, expected: number, context?: string): void {
    const message = context ? `Expected status ${expected} but got ${actual} for ${context}` : `Expected status ${expected} but got ${actual}`;

    assert.strictEqual(actual, expected, message);
  }

  /**
   * Assert response contains specific fields
   */
  assertResponseContains(response: any, fields: string[], context?: string): void {
    const message = context ? `Response should contain fields [${fields.join(', ')}] for ${context}` : `Response should contain fields [${fields.join(', ')}]`;

    fields.forEach((field) => {
      assert(Object.prototype.hasOwnProperty.call(response, field), `${message} - missing field: ${field}`);
    });
  }

  /**
   * Assert response does not contain specific fields
   */
  assertResponseDoesNotContain(response: any, fields: string[], context?: string): void {
    const message = context ? `Response should not contain fields [${fields.join(', ')}] for ${context}` : `Response should not contain fields [${fields.join(', ')}]`;

    fields.forEach((field) => {
      assert(!Object.prototype.hasOwnProperty.call(response, field), `${message} - unexpected field: ${field}`);
    });
  }

  /**
   * Assert field value matches expected
   */
  assertFieldValue(response: any, field: string, expectedValue: any, context?: string): void {
    const message = context ? `Field '${field}' should be '${expectedValue}' for ${context}` : `Field '${field}' should be '${expectedValue}'`;

    assert(Object.prototype.hasOwnProperty.call(response, field), `${message} - field missing`);
    assert.strictEqual(response[field], expectedValue, message);
  }

  /**
   * Assert field exists and is not null/undefined
   */
  assertFieldExists(response: any, field: string, context?: string): void {
    const message = context ? `Field '${field}' should exist for ${context}` : `Field '${field}' should exist`;

    assert(Object.prototype.hasOwnProperty.call(response, field), `${message} - field missing`);
    assert(response[field] !== undefined, `${message} - field is undefined`);
    assert(response[field] !== null, `${message} - field is null`);
  }

  /**
   * Assert field is a valid email
   */
  assertValidEmail(email: string, context?: string): void {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const message = context ? `'${email}' should be a valid email for ${context}` : `'${email}' should be a valid email`;

    assert(emailRegex.test(email), message);
  }

  /**
   * Assert field is a valid JWT token
   */
  assertValidJWT(token: string, context?: string): void {
    const jwtRegex = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/;
    const message = context ? `'${token}' should be a valid JWT token for ${context}` : `'${token}' should be a valid JWT token`;

    assert(typeof token === 'string', `${message} - should be string`);
    assert(token.length > 0, `${message} - should not be empty`);
    assert(jwtRegex.test(token), message);
  }

  /**
   * Assert field is a valid UUID
   */
  assertValidUUID(uuid: string, context?: string): void {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const message = context ? `'${uuid}' should be a valid UUID for ${context}` : `'${uuid}' should be a valid UUID`;

    assert(typeof uuid === 'string', `${message} - should be string`);
    assert(uuidRegex.test(uuid), message);
  }

  /**
   * Assert array contains specific number of items
   */
  assertArrayLength(array: any[], expectedLength: number, context?: string): void {
    const message = context ? `Array should contain ${expectedLength} items for ${context}` : `Array should contain ${expectedLength} items`;

    assert(Array.isArray(array), `${message} - should be array`);
    assert.strictEqual(array.length, expectedLength, message);
  }

  /**
   * Assert array contains item with specific property value
   */
  assertArrayContainsItem(array: any[], property: string, value: any, context?: string): void {
    const message = context ? `Array should contain item with ${property}='${value}' for ${context}` : `Array should contain item with ${property}='${value}'`;

    assert(Array.isArray(array), `${message} - should be array`);
    const found = array.some((item) => item[property] === value);
    assert(found, message);
  }

  /**
   * Assert error message contains specific text
   */
  assertErrorMessage(error: any, expectedText: string, context?: string): void {
    const message = context ? `Error message should contain '${expectedText}' for ${context}` : `Error message should contain '${expectedText}'`;

    // Check main error message
    const errorMessage = error.message || error.data?.message || error.data?.error || '';

    // Also check details.message (can be array or string)
    const detailsMessage = error.data?.error?.details?.message || [];
    const detailsMessages = Array.isArray(detailsMessage) ? detailsMessage : [detailsMessage];
    const allMessages = [errorMessage, ...detailsMessages].join(' ');

    assert(allMessages.toLowerCase().includes(expectedText.toLowerCase()), message);
  }

  /**
   * Assert response time is within acceptable limits
   */
  assertResponseTime(startTime: number, maxTime: number, context?: string): void {
    const responseTime = Date.now() - startTime;
    const message = context
      ? `Response time should be under ${maxTime}ms for ${context} (actual: ${responseTime}ms)`
      : `Response time should be under ${maxTime}ms (actual: ${responseTime}ms)`;

    assert(responseTime < maxTime, message);
  }

  /**
   * Assert pagination metadata
   */
  assertPaginationMetadata(response: any, context?: string): void {
    const message = context ? `Response should contain pagination metadata for ${context}` : `Response should contain pagination metadata`;

    assert(Object.prototype.hasOwnProperty.call(response, 'data'), `${message} - missing data`);
    assert(Object.prototype.hasOwnProperty.call(response, 'meta'), `${message} - missing meta`);
    assert(Object.prototype.hasOwnProperty.call(response.meta, 'total'), `${message} - missing meta.total`);
    assert(Object.prototype.hasOwnProperty.call(response.meta, 'page'), `${message} - missing meta.page`);
    assert(Object.prototype.hasOwnProperty.call(response.meta, 'limit'), `${message} - missing meta.limit`);
    assert(Array.isArray(response.data), `${message} - data should be array`);
  }

  /**
   * Assert security headers are present
   */
  assertSecurityHeaders(headers: any, context?: string): void {
    const message = context ? `Response should contain security headers for ${context}` : `Response should contain security headers`;

    // Check for common security headers
    const securityHeaders = ['x-content-type-options', 'x-frame-options', 'x-xss-protection'];

    securityHeaders.forEach((header) => {
      assert(Object.prototype.hasOwnProperty.call(headers, header), `${message} - missing header: ${header}`);
    });
  }

  /**
   * Assert date is recent (within last few minutes)
   */
  assertRecentDate(dateString: string, maxMinutesAgo = 5, context?: string): void {
    const message = context ? `Date should be recent (within ${maxMinutesAgo} minutes) for ${context}` : `Date should be recent (within ${maxMinutesAgo} minutes)`;

    const date = new Date(dateString);
    const now = new Date();
    const diffMinutes = (now.getTime() - date.getTime()) / (1000 * 60);

    assert(date instanceof Date, `${message} - should be Date instance`);
    assert(!isNaN(date.getTime()), `${message} - should be valid date`);
    assert(diffMinutes < maxMinutesAgo, message);
  }

  /**
   * Assert object matches schema structure
   */
  assertObjectSchema(obj: any, schema: Record<string, string>, context?: string): void {
    const message = context ? `Object should match expected schema for ${context}` : `Object should match expected schema`;

    Object.entries(schema).forEach(([field, type]) => {
      assert(Object.prototype.hasOwnProperty.call(obj, field), `${message} - missing field: ${field}`);

      if (type === 'string') {
        assert(typeof obj[field] === 'string', `${message} - field '${field}' should be string`);
      } else if (type === 'number') {
        assert(typeof obj[field] === 'number', `${message} - field '${field}' should be number`);
      } else if (type === 'boolean') {
        assert(typeof obj[field] === 'boolean', `${message} - field '${field}' should be boolean`);
      } else if (type === 'array') {
        assert(Array.isArray(obj[field]), `${message} - field '${field}' should be array`);
      } else if (type === 'object') {
        assert(typeof obj[field] === 'object', `${message} - field '${field}' should be object`);
        assert(obj[field] !== null, `${message} - field '${field}' should not be null`);
      }
    });
  }

  /**
   * Assert field has specific type
   */
  assertFieldType(value: any, expectedType: string, context?: string): void {
    const message = context ? `${context}` : `Value should be of type ${expectedType}`;

    if (expectedType === 'string') {
      assert(typeof value === 'string', `${message} - expected string but got ${typeof value}`);
    } else if (expectedType === 'number') {
      assert(typeof value === 'number', `${message} - expected number but got ${typeof value}`);
    } else if (expectedType === 'boolean') {
      assert(typeof value === 'boolean', `${message} - expected boolean but got ${typeof value}`);
    } else if (expectedType === 'array') {
      assert(Array.isArray(value), `${message} - expected array but got ${typeof value}`);
    } else if (expectedType === 'object') {
      assert(typeof value === 'object' && value !== null, `${message} - expected object but got ${typeof value}`);
    }
  }

  /**
   * Assert field does not exist in object
   */
  assertFieldDoesNotExist(obj: any, field: string, context?: string): void {
    const message = context ? `${context}` : `Field '${field}' should not exist`;

    assert(!Object.prototype.hasOwnProperty.call(obj, field), `${message} - field '${field}' was found but should not exist`);
  }

  /**
   * Assert two values are equal
   */
  assertEqual(actual: any, expected: any, context?: string): void {
    const message = context ? `${context}` : `Values should be equal`;

    assert.strictEqual(actual, expected, `${message} - expected '${expected}' but got '${actual}'`);
  }
}
