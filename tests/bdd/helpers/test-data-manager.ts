/* eslint-disable @typescript-eslint/no-explicit-any */
import { v4 as uuidv4 } from 'uuid';

/**
 * Test Data Manager for BDD tests
 * Generates and manages test data for scenarios
 */
export class TestDataManager {
  private static instance: TestDataManager;
  private testDataCache: Map<string, any> = new Map();

  constructor() {
    if (TestDataManager.instance) {
      return TestDataManager.instance;
    }
    TestDataManager.instance = this;
  }

  /**
   * Generate a unique email address
   */
  generateEmail(prefix = 'test.user'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${prefix}.${timestamp}.${random}@businesscorp.com`;
  }

  /**
   * Generate a unique email address with specific domain
   */
  generateEmailWithDomain(domain: string, prefix = 'test.user'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${prefix}.${timestamp}.${random}@${domain}`;
  }

  /**
   * Generate a unique username
   */
  generateUsername(prefix = 'testuser'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${prefix}_${timestamp}_${random}`;
  }

  /**
   * Generate a secure password
   */
  generatePassword(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';

    // Ensure at least one of each required character type
    password += 'A'; // Uppercase
    password += 'a'; // Lowercase
    password += '1'; // Number
    password += '!'; // Special character

    // Fill the rest randomly
    for (let i = 4; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    // Shuffle the password
    return password
      .split('')
      .sort(() => Math.random() - 0.5)
      .join('');
  }

  /**
   * Generate user registration data
   * Uses snake_case field names as required by the API HTTP layer
   */
  generateUserData(overrides: Partial<any> = {}): any {
    const defaultData = {
      email: this.generateEmail(),
      password: this.generatePassword(),
      first_name: 'John',
      last_name: 'Doe',
      accept_terms: true,
      accept_privacy_policy: true,
    };

    return { ...defaultData, ...overrides };
  }

  /**
   * Generate tenant data
   */
  generateTenantData(overrides: Partial<any> = {}): any {
    const defaultData = {
      name: `Test Tenant ${Date.now()}`,
      domain: `tenant-${Date.now()}.example.com`,
      plan: 'basic',
      status: 'active',
      settings: {
        maxUsers: 100,
        features: ['alerts', 'reports'],
      },
    };

    return { ...defaultData, ...overrides };
  }

  /**
   * Generate login credentials
   */
  generateLoginCredentials(email?: string, password?: string): any {
    return {
      email: email || this.generateEmail(),
      password: password || this.generatePassword(),
    };
  }

  /**
   * Generate invalid data for negative testing
   */
  generateInvalidUserData(type: 'email' | 'password' | 'missing-fields' | 'invalid-format'): any {
    const baseData = this.generateUserData();

    switch (type) {
      case 'email':
        return { ...baseData, email: 'invalid-email-format' };

      case 'password':
        return { ...baseData, password: '123' }; // Too short

      case 'missing-fields': {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { email, ...incomplete } = baseData;
        return { email };
      } // Missing required fields

      case 'invalid-format':
        return {
          ...baseData,
          email: 'not-an-email',
          firstName: '', // Empty required field
          acceptTerms: false, // Required to be true
        };

      default:
        return baseData;
    }
  }

  /**
   * Store test data for later retrieval
   */
  storeTestData(key: string, data: any): void {
    this.testDataCache.set(key, data);
  }

  /**
   * Retrieve stored test data
   */
  getTestData(key: string): any {
    return this.testDataCache.get(key);
  }

  /**
   * Clear stored test data
   */
  clearTestData(key?: string): void {
    if (key) {
      this.testDataCache.delete(key);
    } else {
      this.testDataCache.clear();
    }
  }

  /**
   * Replace placeholders in data with actual values
   */
  replacePlaceholders(data: any, replacements: Record<string, any>): any {
    if (typeof data === 'string') {
      let result = data;
      Object.entries(replacements).forEach(([key, value]) => {
        const placeholder = `{{${key}}}`;
        result = result.replace(new RegExp(placeholder, 'g'), value);
      });

      // Handle special placeholders
      result = result.replace(/{{random}}/g, Math.random().toString(36).substring(2, 8));
      result = result.replace(/{{timestamp}}/g, Date.now().toString());
      result = result.replace(/{{uuid}}/g, uuidv4());

      return result;
    }

    if (Array.isArray(data)) {
      return data.map((item) => this.replacePlaceholders(item, replacements));
    }

    if (typeof data === 'object' && data !== null) {
      const result: any = {};
      Object.entries(data).forEach(([key, value]) => {
        result[key] = this.replacePlaceholders(value, replacements);
      });
      return result;
    }

    return data;
  }

  /**
   * Parse table data from Gherkin scenarios
   */
  parseTableData(table: any[]): Record<string, any> {
    const result: Record<string, any> = {};

    table.forEach((row) => {
      if (row.length >= 2) {
        const key = row[0];
        let value = row[1];

        // Try to parse as JSON for complex values
        if (typeof value === 'string') {
          if (value === 'true') value = true;
          else if (value === 'false') value = false;
          else if (!isNaN(Number(value))) value = Number(value);
          else if (value.startsWith('{') || value.startsWith('[')) {
            try {
              value = JSON.parse(value);
            } catch {
              // Keep as string if not valid JSON
            }
          }
        }

        result[key] = value;
      }
    });

    return result;
  }

  /**
   * Generate test data based on scenario context
   */
  generateContextualData(context: string, overrides: any = {}): any {
    switch (context) {
      case 'new-user':
        return this.generateUserData(overrides);

      case 'existing-user': {
        const existingUser = this.generateUserData(overrides);
        this.storeTestData('currentUser', existingUser);
        return existingUser;
      }

      case 'invalid-user':
        return this.generateInvalidUserData('email');

      case 'new-tenant':
        return this.generateTenantData(overrides);

      default:
        return overrides;
    }
  }
}
