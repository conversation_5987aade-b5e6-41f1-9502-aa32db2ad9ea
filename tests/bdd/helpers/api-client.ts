/* eslint-disable @typescript-eslint/no-explicit-any */
import axios, { AxiosError, AxiosInstance, AxiosResponse } from 'axios';

export interface ApiResponse<T = any> {
  status: number;
  data: T;
  headers: any;
  responseTime?: number;
}

export interface ApiError {
  status: number;
  data: any;
  message: string;
}

/**
 * API Client for BDD tests
 * Handles HTTP requests to the Qeep API with authentication and error handling
 */
export class ApiClient {
  private client: AxiosInstance;
  private authToken?: string;
  private tenantId?: string;

  constructor(baseURL: string, timeout = 30000) {
    this.client = axios.create({
      baseURL,
      timeout,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    });

    // Add request interceptor for authentication
    this.client.interceptors.request.use((config) => {
      if (this.authToken) {
        config.headers.Authorization = `Bearer ${this.authToken}`;
      }
      if (this.tenantId) {
        config.headers['X-Tenant-ID'] = this.tenantId;
      }
      return config;
    });
  }

  /**
   * Set authentication token
   */
  setAuthToken(token: string): void {
    this.authToken = token;
  }

  /**
   * Clear authentication token
   */
  clearAuthToken(): void {
    this.authToken = undefined;
  }

  /**
   * Set tenant ID for multi-tenant requests
   */
  setTenantId(tenantId: string): void {
    this.tenantId = tenantId;
  }

  /**
   * Clear tenant ID
   */
  clearTenantId(): void {
    this.tenantId = undefined;
  }

  /**
   * Make a GET request
   */
  async get<T = any>(url: string, params?: any): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.client.get(url, { params });
      return {
        status: response.status,
        data: response.data,
        headers: response.headers,
      };
    } catch (error) {
      throw this.handleError(error as AxiosError);
    }
  }

  /**
   * Make a POST request
   */
  async post<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    const startTime = Date.now();
    try {
      const response: AxiosResponse<T> = await this.client.post(url, data);
      const responseTime = Date.now() - startTime;
      return {
        status: response.status,
        data: response.data,
        headers: response.headers,
        responseTime,
      };
    } catch (error) {
      throw this.handleError(error as AxiosError);
    }
  }

  /**
   * Make a PUT request
   */
  async put<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.client.put(url, data);
      return {
        status: response.status,
        data: response.data,
        headers: response.headers,
      };
    } catch (error) {
      throw this.handleError(error as AxiosError);
    }
  }

  /**
   * Make a PATCH request
   */
  async patch<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.client.patch(url, data);
      return {
        status: response.status,
        data: response.data,
        headers: response.headers,
      };
    } catch (error) {
      throw this.handleError(error as AxiosError);
    }
  }

  /**
   * Make a DELETE request
   */
  async delete<T = any>(url: string): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.client.delete(url);
      return {
        status: response.status,
        data: response.data,
        headers: response.headers,
      };
    } catch (error) {
      throw this.handleError(error as AxiosError);
    }
  }

  /**
   * Check if API is healthy
   */
  async healthCheck(): Promise<ApiResponse> {
    return this.get('/health');
  }

  /**
   * Handle API errors consistently
   */
  private handleError(error: AxiosError): ApiError {
    const apiError: ApiError = {
      status: error.response?.status || 500,
      data: error.response?.data || {},
      message: error.message,
    };

    return apiError;
  }

  /**
   * Authentication endpoints
   */
  async register(userData: any): Promise<ApiResponse> {
    return this.post('/auth/signup', userData);
  }

  async login(credentials: any): Promise<ApiResponse> {
    return this.post('/auth/login', credentials);
  }

  async verifyEmail(token: string): Promise<ApiResponse> {
    return this.get('/auth/verify-email', { token });
  }

  async resendVerification(email: string): Promise<ApiResponse> {
    return this.post('/auth/resend-verification', { email });
  }

  async getProfile(): Promise<ApiResponse> {
    return this.get('/auth/me');
  }

  async refreshToken(refreshToken: string): Promise<ApiResponse> {
    return this.post('/auth/refresh', { refreshToken });
  }

  async checkEmail(email: string): Promise<ApiResponse> {
    return this.post('/auth/check-email', { email });
  }

  async checkEmailWithoutParam(): Promise<ApiResponse> {
    return this.post('/auth/check-email', {});
  }

  /**
   * User management endpoints
   */
  async getUsers(params?: any): Promise<ApiResponse> {
    return this.get('/users', params);
  }

  async getUser(userId: string): Promise<ApiResponse> {
    return this.get(`/users/${userId}`);
  }

  async updateUser(userId: string, userData: any): Promise<ApiResponse> {
    return this.put(`/users/${userId}`, userData);
  }

  async deleteUser(userId: string): Promise<ApiResponse> {
    return this.delete(`/users/${userId}`);
  }

  /**
   * Tenant management endpoints
   */
  async getTenants(params?: any): Promise<ApiResponse> {
    return this.get('/tenants', params);
  }

  async getTenant(tenantId: string): Promise<ApiResponse> {
    return this.get(`/tenants/${tenantId}`);
  }

  async createTenant(tenantData: any): Promise<ApiResponse> {
    return this.post('/tenants', tenantData);
  }

  async updateTenant(tenantId: string, tenantData: any): Promise<ApiResponse> {
    return this.put(`/tenants/${tenantId}`, tenantData);
  }

  async deleteTenant(tenantId: string): Promise<ApiResponse> {
    return this.delete(`/tenants/${tenantId}`);
  }
}
