{"name": "@qeep/source", "version": "0.0.0", "license": "MIT", "scripts": {"start:dev": "nx run-many --target=serve --projects=auth-service,tenant-service,user-service,notification-service,audit-service,customer-service --output-style=stream", "setup": "pnpm install && pnpm run proto:generate-nx", "dev:start": "bash scripts/dev-hybrid.sh start", "dev:stop": "bash scripts/dev-hybrid.sh stop", "dev:restart": "bash scripts/dev-hybrid.sh restart", "dev:status": "bash scripts/dev-hybrid.sh status", "dev:logs": "bash scripts/dev-hybrid.sh logs", "dev:cleanup": "bash scripts/dev-hybrid.sh cleanup", "dev:infrastructure:start": "bash scripts/infrastructure.sh start", "dev:infrastructure:stop": "bash scripts/infrastructure.sh stop", "dev:infrastructure:status": "bash scripts/infrastructure.sh status", "dev:infrastructure:logs": "bash scripts/infrastructure.sh logs", "test:nginx": "bash scripts/test-nginx-proxy.sh", "db:status": "node scripts/db-manager.js status", "db:list": "node scripts/db-manager.js list", "proto:generate": "node scripts/generate-proto-standalone.js", "proto:generate-nx": "nx run proto:generate-types", "proto:watch": "nx run proto:generate-types-watch", "proto:clean": "nx run proto:clean-generated", "proto:check": "node scripts/dev-proto-check.js", "proto:test": "node scripts/test-proto-exports.js", "build": "nx run-many --target=build --all", "build:affected": "nx affected:build", "test": "nx run-many --target=test --all --passWithNoTests", "test:affected": "nx affected:test --passWithNoTests", "test:watch": "nx run-many --target=test --all --watch", "test:coverage": "nx run-many --target=test --all --coverage", "test:integration": "nx run-many --target=test --all --testPathPattern=integration", "test:all": "pnpm test && pnpm test:api", "test:setup": "node scripts/test-setup.js", "test:cleanup": "node scripts/test-cleanup.js", "lint": "nx run-many --target=lint --all", "lint:affected": "nx affected:lint", "format": "nx format:write", "format:check": "nx format:check", "docs:serve": "honkit serve", "docs:build": "honkit build", "docs:pdf": "mkdir -p docs-output && honkit pdf . ./docs-output/qeep-docs.pdf", "docs:epub": "mkdir -p docs-output && honkit epub . ./docs-output/qeep-docs.epub", "docs:mobi": "mkdir -p docs-output && honkit mobi . ./docs-output/qeep-docs.mobi", "docs:clean": "rm -rf _book docs-output", "clean": "nx reset && find . -name 'node_modules' -type d -prune -exec rm -rf {} + && find . -name 'dist' -type d -prune -exec rm -rf {} + && rm -rf .nx/cache && rm -rf libs/types/src/generated/* && echo '🧹 Complete workspace cleanup completed!' && echo '📦 Run: pnpm install to restore dependencies'"}, "private": true, "dependencies": {"@bufbuild/protobuf": "^2.6.0", "@grpc/grpc-js": "^1.13.4", "@grpc/proto-loader": "^0.7.15", "@nestjs/common": "^11.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.0", "@nestjs/microservices": "^11.1.3", "@nestjs/platform-express": "^11.0.0", "@nestjs/throttler": "^6.4.0", "@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "^6.11.0", "@simplewebauthn/server": "^13.1.2", "@simplewebauthn/types": "^12.0.0", "@simplewebauthn/typescript-types": "^8.3.4", "@types/handlebars": "^4.1.0", "@types/ioredis": "^5.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/nodemailer": "^6.4.17", "axios": "^1.6.0", "bcryptjs": "^3.0.2", "bullmq": "^5.56.2", "dotenv": "^16.4.5", "express": "^5.1.0", "handlebars": "^4.7.8", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "kafkajs": "^2.2.4", "lodash": "^4.17.21", "nodemailer": "^7.0.5", "pg": "^8.16.3", "prisma": "^6.11.0", "protobufjs": "^7.5.3", "qrcode": "^1.5.4", "redis": "^5.5.6", "reflect-metadata": "^0.1.13", "resend": "^4.6.0", "rxjs": "^7.8.0", "speakeasy": "^2.0.0", "uuid": "^11.1.0", "zod": "^4.0.5"}, "devDependencies": {"@cucumber/cucumber": "^11.3.0", "@eslint/js": "^9.8.0", "@jest/globals": "^30.0.4", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.0", "@nx/eslint": "21.2.2", "@nx/eslint-plugin": "21.2.2", "@nx/jest": "21.2.2", "@nx/js": "21.2.2", "@nx/nest": "21.2.2", "@nx/node": "21.2.2", "@nx/web": "21.2.2", "@nx/webpack": "21.2.2", "@nx/workspace": "21.2.2", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/bcryptjs": "^3.0.0", "@types/express": "^5.0.3", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.20", "@types/node": "~18.16.9", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/uuid": "^10.0.0", "chokidar-cli": "^3.0.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "dotenv-expand": "^12.0.2", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "honkit": "^6.0.3", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "jsonc-eslint-parser": "^2.1.0", "nx": "21.2.2", "prettier": "^2.6.2", "supertest": "^7.1.3", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "ts-proto": "^2.7.5", "tslib": "^2.3.0", "typescript": "~5.8.2", "typescript-eslint": "^8.29.0", "webpack-cli": "^5.1.4"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184"}