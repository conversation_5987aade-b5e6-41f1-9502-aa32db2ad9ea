# Risk Profile Management API - Postman Collection

## Overview

This Postman collection provides comprehensive testing capabilities for the Risk Profile Management System in the Qeep platform. It includes all endpoints for managing risk profiles, rules, customer assignments, and analytics.

## Collection Structure

### 📁 **Risk Profiles** (9 endpoints)
- Create, read, update, delete risk profiles
- Search and filter risk profiles
- Get system-generated vs custom profiles
- Risk profile statistics

### 📁 **Rules** (13 endpoints)
- Create, read, update, delete rules
- Get rules by category, type, and risk profile
- Search and filter rules
- Validate rule conditions and actions
- Get effective rules by date

### 📁 **Customer Assignments** (9 endpoints)
- Assign customers to risk profiles
- Manage customer assignments (CRUD operations)
- Get assignments by type (automatic vs manual)
- Bulk assignment operations

### 📁 **Analytics & Operations** (10 endpoints)
- Auto-assign customers based on criteria
- Bulk operations for multiple customers
- Customer risk profile summaries
- Rule execution history and statistics
- Import/export configurations
- Rule testing capabilities

## Environment Variables

### **Base Configuration**
- `base_url`: API base URL (default: `http://localhost:3003/api`)
- `auth_token`: Bearer token for authentication

### **Tenant IDs**
- `tenant_id`: Default tenant (Ronna Bank)
- `tenant_id_ecobank`: Ecobank Ghana
- `tenant_id_absa`: Absa Bank Ghana  
- `tenant_id_mtn`: MTN Mobile Money

### **Dynamic IDs** (Set from API responses)
- `risk_profile_id`: Current risk profile ID
- `rule_id`: Current rule ID
- `customer_id`: Current customer ID
- `assignment_id`: Current assignment ID

### **Test Data**
- `customer_id_high_risk`: High risk test customer
- `customer_id_low_risk`: Low risk test customer
- `current_date`: Current date for testing
- `future_date`: Future date for effective dates

## Getting Started

### 1. **Import Collection & Environment**
```bash
# Import the collection
Risk_Profile_Management_API.postman_collection.json

# Import the environment
Risk_Profile_Management_Environment.postman_environment.json
```

### 2. **Set Authentication**
- Set your `auth_token` in the environment variables
- The collection uses Bearer token authentication

### 3. **Configure Tenant**
- Update `tenant_id` to match your target tenant
- Use the provided tenant IDs for different banks

### 4. **Run Basic Tests**
1. **Get All Risk Profiles** - Verify system profiles exist
2. **Create Risk Profile** - Test profile creation
3. **Create Rule** - Test rule creation
4. **Assign Customer** - Test customer assignment

## API Endpoint Patterns

### **Risk Profiles**
```
GET    /tenants/{tenant_id}/risk-profiles
POST   /tenants/{tenant_id}/risk-profiles
GET    /tenants/{tenant_id}/risk-profiles/{risk_profile_id}
PUT    /tenants/{tenant_id}/risk-profiles/{risk_profile_id}
DELETE /tenants/{tenant_id}/risk-profiles/{risk_profile_id}
```

### **Rules**
```
GET    /tenants/{tenant_id}/rules
POST   /tenants/{tenant_id}/risk-profiles/{risk_profile_id}/rules
GET    /tenants/{tenant_id}/rules/{rule_id}
PUT    /tenants/{tenant_id}/rules/{rule_id}
DELETE /tenants/{tenant_id}/rules/{rule_id}
```

### **Customer Assignments**
```
GET    /tenants/{tenant_id}/customer-assignments
POST   /tenants/{tenant_id}/customers/{customer_id}/risk-profile-assignments
GET    /tenants/{tenant_id}/customer-assignments/{assignment_id}
PUT    /tenants/{tenant_id}/customer-assignments/{assignment_id}
DELETE /tenants/{tenant_id}/customer-assignments/{assignment_id}
```

## Request/Response Examples

### **Create Risk Profile**
```json
{
  "name": "Custom High Risk Profile",
  "description": "Custom profile for high-risk customers",
  "rule_category": "TRANSACTION",
  "priority": 250,
  "assignment_priority": 250,
  "metadata": {
    "custom_profile": true,
    "industry_specific": "fintech"
  }
}
```

### **Create Rule**
```json
{
  "risk_profile_id": "rp_12345",
  "name": "Large Wire Transfer Alert",
  "rule_type": "THRESHOLD",
  "rule_category": "TRANSACTION",
  "conditions": {
    "type": "AND",
    "conditions": [
      {
        "field": "transaction.amount",
        "operator": "GREATER_THAN",
        "value": 50000,
        "metadata": {"currency": "USD"}
      }
    ]
  },
  "actions": {
    "actions": [
      {
        "type": "GENERATE_ALERT",
        "severity": "HIGH",
        "message": "Large wire transfer detected"
      }
    ]
  },
  "priority": 400
}
```

### **Assign Customer**
```json
{
  "customer_id": "cust_001",
  "risk_profile_id": "rp_12345",
  "assignment_type": "MANUAL",
  "assignment_criteria": {
    "reason": "Customer risk assessment update"
  },
  "assignment_priority": 200,
  "allow_override": true
}
```

## Query Parameters

### **Pagination**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)

### **Filtering**
- `search`: Search term for name/description
- `rule_category`: Filter by rule category
- `rule_type`: Filter by rule type
- `is_active`: Filter by active status
- `is_system_generated`: Filter by system vs custom

### **Sorting**
- `sort_by`: Field to sort by
- `sort_order`: `asc` or `desc`

## Rule Categories & Types

### **Rule Categories**
- `TRANSACTION`: Transaction-based rules
- `BEHAVIORAL`: Customer behavior analysis
- `GEOGRAPHIC`: Location-based restrictions
- `FREQUENCY`: Transaction frequency monitoring
- `CHANNEL`: Transaction channel assessment
- `COUNTERPARTY`: Third-party risk evaluation
- `ANOMALY`: Anomaly detection rules

### **Rule Types**
- `THRESHOLD`: Amount or count-based thresholds
- `CONDITION`: Conditional logic rules
- `PATTERN`: Pattern matching rules
- `GEOGRAPHIC`: Geographic restriction rules
- `FREQUENCY_BASED`: Time-based frequency rules
- `AMOUNT_BASED`: Amount calculation rules
- `CUSTOM`: Custom logic rules

### **Assignment Types**
- `AUTOMATIC`: System-generated assignments
- `MANUAL`: User-created assignments
- `INHERITED`: Inherited from parent entities

## Testing Scenarios

### **Basic CRUD Operations**
1. Create risk profile → Get profile → Update profile → Delete profile
2. Create rule → Get rule → Update rule → Delete rule
3. Create assignment → Get assignment → Update assignment → Delete assignment

### **Business Logic Testing**
1. Test rule condition validation
2. Test rule action validation
3. Test customer auto-assignment
4. Test rule precedence and conflicts

### **Analytics Testing**
1. Get risk profile statistics
2. Get rule execution history
3. Test rule execution with sample data
4. Export/import configurations

## Error Handling

The API returns standard HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request (validation errors)
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `409`: Conflict (duplicate names, etc.)
- `500`: Internal Server Error

Error responses include detailed error messages and validation details.

## Notes

- All variable names use `snake_case` as requested
- Timestamps are in ISO 8601 format (`YYYY-MM-DDTHH:mm:ssZ`)
- All requests include `X-Tenant-ID` header for multi-tenancy
- JSON payloads use proper validation schemas
- Geographic rules use ISO 3166-1 alpha-2 country codes
- Rule priorities determine execution order (higher = first)

## Support

For questions or issues with the API collection:
1. Check the API documentation
2. Verify environment variables are set correctly
3. Ensure proper authentication tokens
4. Review request/response examples above
