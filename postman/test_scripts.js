/**
 * Postman Test Scripts for Risk Profile Management API
 * Add these scripts to the "Tests" tab in Postman requests
 */

// =============================================================================
// COMMON TEST UTILITIES
// =============================================================================

/**
 * Common response validation tests
 * Add this to any request's Tests tab
 */
function validateCommonResponse() {
    // Test response time
    pm.test("Response time is less than 2000ms", function () {
        pm.expect(pm.response.responseTime).to.be.below(2000);
    });

    // Test response headers
    pm.test("Response has correct content type", function () {
        pm.expect(pm.response.headers.get("Content-Type")).to.include("application/json");
    });

    // Test response structure
    pm.test("Response is valid JSON", function () {
        pm.response.to.be.json;
    });
}

/**
 * Validate pagination structure
 */
function validatePagination(responseJson) {
    pm.test("Response has pagination structure", function () {
        pm.expect(responseJson).to.have.property("pagination");
        pm.expect(responseJson.pagination).to.have.property("page");
        pm.expect(responseJson.pagination).to.have.property("limit");
        pm.expect(responseJson.pagination).to.have.property("total");
        pm.expect(responseJson.pagination).to.have.property("total_pages");
        pm.expect(responseJson.pagination).to.have.property("has_next");
        pm.expect(responseJson.pagination).to.have.property("has_prev");
    });
}

/**
 * Validate snake_case naming convention
 */
function validateSnakeCase(obj, path = "") {
    for (const key in obj) {
        const currentPath = path ? `${path}.${key}` : key;
        
        pm.test(`Property ${currentPath} uses snake_case`, function () {
            pm.expect(key).to.match(/^[a-z][a-z0-9_]*$/);
        });

        if (typeof obj[key] === "object" && obj[key] !== null && !Array.isArray(obj[key])) {
            validateSnakeCase(obj[key], currentPath);
        }
    }
}

// =============================================================================
// RISK PROFILE TESTS
// =============================================================================

/**
 * Tests for GET /risk-profiles (List Risk Profiles)
 */
function testGetRiskProfiles() {
    validateCommonResponse();

    pm.test("Status code is 200", function () {
        pm.response.to.have.status(200);
    });

    const responseJson = pm.response.json();
    
    validatePagination(responseJson);
    validateSnakeCase(responseJson);

    pm.test("Response has risk_profiles array", function () {
        pm.expect(responseJson).to.have.property("risk_profiles");
        pm.expect(responseJson.risk_profiles).to.be.an("array");
    });

    if (responseJson.risk_profiles.length > 0) {
        const profile = responseJson.risk_profiles[0];
        
        pm.test("Risk profile has required fields", function () {
            pm.expect(profile).to.have.property("id");
            pm.expect(profile).to.have.property("tenant_id");
            pm.expect(profile).to.have.property("name");
            pm.expect(profile).to.have.property("rule_category");
            pm.expect(profile).to.have.property("is_active");
            pm.expect(profile).to.have.property("is_system_generated");
            pm.expect(profile).to.have.property("priority");
            pm.expect(profile).to.have.property("assignment_priority");
            pm.expect(profile).to.have.property("created_at");
            pm.expect(profile).to.have.property("updated_at");
        });

        // Set first profile ID for subsequent tests
        pm.environment.set("risk_profile_id", profile.id);
    }
}

/**
 * Tests for POST /risk-profiles (Create Risk Profile)
 */
function testCreateRiskProfile() {
    validateCommonResponse();

    pm.test("Status code is 201", function () {
        pm.response.to.have.status(201);
    });

    const responseJson = pm.response.json();
    validateSnakeCase(responseJson);

    pm.test("Response has risk_profile object", function () {
        pm.expect(responseJson).to.have.property("risk_profile");
    });

    const profile = responseJson.risk_profile;
    
    pm.test("Created risk profile has required fields", function () {
        pm.expect(profile).to.have.property("id");
        pm.expect(profile).to.have.property("name");
        pm.expect(profile.is_system_generated).to.be.false;
    });

    // Store created profile ID for subsequent tests
    pm.environment.set("risk_profile_id", profile.id);
}

/**
 * Tests for GET /risk-profiles/{id} (Get Risk Profile by ID)
 */
function testGetRiskProfileById() {
    validateCommonResponse();

    pm.test("Status code is 200", function () {
        pm.response.to.have.status(200);
    });

    const responseJson = pm.response.json();
    validateSnakeCase(responseJson);

    pm.test("Response has risk_profile object", function () {
        pm.expect(responseJson).to.have.property("risk_profile");
    });

    const profile = responseJson.risk_profile;
    const expectedId = pm.environment.get("risk_profile_id");
    
    pm.test("Risk profile ID matches requested ID", function () {
        pm.expect(profile.id).to.equal(expectedId);
    });
}

// =============================================================================
// RULE TESTS
// =============================================================================

/**
 * Tests for GET /rules (List Rules)
 */
function testGetRules() {
    validateCommonResponse();

    pm.test("Status code is 200", function () {
        pm.response.to.have.status(200);
    });

    const responseJson = pm.response.json();
    
    validatePagination(responseJson);
    validateSnakeCase(responseJson);

    pm.test("Response has rules array", function () {
        pm.expect(responseJson).to.have.property("rules");
        pm.expect(responseJson.rules).to.be.an("array");
    });

    if (responseJson.rules.length > 0) {
        const rule = responseJson.rules[0];
        
        pm.test("Rule has required fields", function () {
            pm.expect(rule).to.have.property("id");
            pm.expect(rule).to.have.property("tenant_id");
            pm.expect(rule).to.have.property("risk_profile_id");
            pm.expect(rule).to.have.property("name");
            pm.expect(rule).to.have.property("rule_type");
            pm.expect(rule).to.have.property("rule_category");
            pm.expect(rule).to.have.property("conditions");
            pm.expect(rule).to.have.property("actions");
            pm.expect(rule).to.have.property("priority");
            pm.expect(rule).to.have.property("is_active");
        });

        pm.test("Rule conditions and actions are objects", function () {
            pm.expect(rule.conditions).to.be.an("object");
            pm.expect(rule.actions).to.be.an("object");
        });

        // Set first rule ID for subsequent tests
        pm.environment.set("rule_id", rule.id);
    }
}

/**
 * Tests for POST /rules (Create Rule)
 */
function testCreateRule() {
    validateCommonResponse();

    pm.test("Status code is 201", function () {
        pm.response.to.have.status(201);
    });

    const responseJson = pm.response.json();
    validateSnakeCase(responseJson);

    pm.test("Response has rule object", function () {
        pm.expect(responseJson).to.have.property("rule");
    });

    const rule = responseJson.rule;
    
    pm.test("Created rule has required fields", function () {
        pm.expect(rule).to.have.property("id");
        pm.expect(rule).to.have.property("name");
        pm.expect(rule).to.have.property("conditions");
        pm.expect(rule).to.have.property("actions");
    });

    // Store created rule ID for subsequent tests
    pm.environment.set("rule_id", rule.id);
}

// =============================================================================
// CUSTOMER ASSIGNMENT TESTS
// =============================================================================

/**
 * Tests for GET /customer-assignments (List Customer Assignments)
 */
function testGetCustomerAssignments() {
    validateCommonResponse();

    pm.test("Status code is 200", function () {
        pm.response.to.have.status(200);
    });

    const responseJson = pm.response.json();
    
    validatePagination(responseJson);
    validateSnakeCase(responseJson);

    pm.test("Response has assignments array", function () {
        pm.expect(responseJson).to.have.property("assignments");
        pm.expect(responseJson.assignments).to.be.an("array");
    });

    if (responseJson.assignments.length > 0) {
        const assignment = responseJson.assignments[0];
        
        pm.test("Assignment has required fields", function () {
            pm.expect(assignment).to.have.property("id");
            pm.expect(assignment).to.have.property("tenant_id");
            pm.expect(assignment).to.have.property("customer_id");
            pm.expect(assignment).to.have.property("risk_profile_id");
            pm.expect(assignment).to.have.property("assignment_type");
            pm.expect(assignment).to.have.property("assignment_priority");
            pm.expect(assignment).to.have.property("allow_override");
            pm.expect(assignment).to.have.property("assigned_at");
            pm.expect(assignment).to.have.property("is_active");
        });

        // Set first assignment ID for subsequent tests
        pm.environment.set("assignment_id", assignment.id);
    }
}

/**
 * Tests for POST /customer-assignments (Create Customer Assignment)
 */
function testCreateCustomerAssignment() {
    validateCommonResponse();

    pm.test("Status code is 201", function () {
        pm.response.to.have.status(201);
    });

    const responseJson = pm.response.json();
    validateSnakeCase(responseJson);

    pm.test("Response has assignment object", function () {
        pm.expect(responseJson).to.have.property("assignment");
    });

    const assignment = responseJson.assignment;
    
    pm.test("Created assignment has required fields", function () {
        pm.expect(assignment).to.have.property("id");
        pm.expect(assignment).to.have.property("customer_id");
        pm.expect(assignment).to.have.property("risk_profile_id");
        pm.expect(assignment).to.have.property("assignment_type");
    });

    // Store created assignment ID for subsequent tests
    pm.environment.set("assignment_id", assignment.id);
}

// =============================================================================
// ANALYTICS TESTS
// =============================================================================

/**
 * Tests for GET /analytics/risk-profile-stats (Risk Profile Statistics)
 */
function testRiskProfileStats() {
    validateCommonResponse();

    pm.test("Status code is 200", function () {
        pm.response.to.have.status(200);
    });

    const responseJson = pm.response.json();
    validateSnakeCase(responseJson);

    pm.test("Response has statistics fields", function () {
        pm.expect(responseJson).to.have.property("total_risk_profiles");
        pm.expect(responseJson).to.have.property("system_generated");
        pm.expect(responseJson).to.have.property("custom_profiles");
        pm.expect(responseJson).to.have.property("active_profiles");
        pm.expect(responseJson).to.have.property("by_category");
        pm.expect(responseJson).to.have.property("total_rules");
        pm.expect(responseJson).to.have.property("total_assignments");
    });

    pm.test("Statistics are numbers", function () {
        pm.expect(responseJson.total_risk_profiles).to.be.a("number");
        pm.expect(responseJson.system_generated).to.be.a("number");
        pm.expect(responseJson.custom_profiles).to.be.a("number");
    });
}

// =============================================================================
// ERROR HANDLING TESTS
// =============================================================================

/**
 * Tests for 404 Not Found responses
 */
function testNotFound() {
    pm.test("Status code is 404", function () {
        pm.response.to.have.status(404);
    });

    const responseJson = pm.response.json();
    
    pm.test("Error response has message", function () {
        pm.expect(responseJson).to.have.property("message");
        pm.expect(responseJson).to.have.property("error");
    });
}

/**
 * Tests for 400 Bad Request responses
 */
function testBadRequest() {
    pm.test("Status code is 400", function () {
        pm.response.to.have.status(400);
    });

    const responseJson = pm.response.json();
    
    pm.test("Validation error response has details", function () {
        pm.expect(responseJson).to.have.property("message");
        pm.expect(responseJson).to.have.property("validation_errors");
    });
}

// =============================================================================
// USAGE INSTRUCTIONS
// =============================================================================

/*
To use these test scripts in Postman:

1. Copy the appropriate test function for each request
2. Paste it into the "Tests" tab of the request
3. Call the function at the end of the script

Example for "Get All Risk Profiles" request:
```javascript
// Paste the validateCommonResponse and testGetRiskProfiles functions
// Then call:
testGetRiskProfiles();
```

Example for "Create Risk Profile" request:
```javascript
// Paste the validateCommonResponse and testCreateRiskProfile functions
// Then call:
testCreateRiskProfile();
```

The tests will:
- Validate response structure and timing
- Check snake_case naming convention
- Verify required fields are present
- Set environment variables for subsequent tests
- Validate business logic constraints
*/
