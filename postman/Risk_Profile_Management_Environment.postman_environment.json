{"id": "risk-profile-management-env", "name": "Risk Profile Management - Development", "values": [{"key": "base_url", "value": "http://localhost:3003/api", "description": "Base URL for the tenant service API", "type": "default", "enabled": true}, {"key": "tenant_id", "value": "ten_ronna_bank_12345", "description": "Default tenant ID for testing (Ronna Bank)", "type": "default", "enabled": true}, {"key": "tenant_id_ecobank", "value": "ten_ecobank_ghana_67890", "description": "Ecobank Ghana tenant ID", "type": "default", "enabled": true}, {"key": "tenant_id_absa", "value": "ten_absa_bank_ghana_11111", "description": "Absa Bank Ghana tenant ID", "type": "default", "enabled": true}, {"key": "tenant_id_mtn", "value": "ten_mtn_momo_22222", "description": "MTN Mobile Money tenant ID", "type": "default", "enabled": true}, {"key": "auth_token", "value": "", "description": "Bearer token for authentication", "type": "secret", "enabled": true}, {"key": "risk_profile_id", "value": "", "description": "Risk profile ID for testing (set dynamically from responses)", "type": "default", "enabled": true}, {"key": "risk_profile_id_standard", "value": "", "description": "Standard Risk Profile ID", "type": "default", "enabled": true}, {"key": "risk_profile_id_enhanced", "value": "", "description": "Enhanced Monitoring Profile ID", "type": "default", "enabled": true}, {"key": "risk_profile_id_high_risk", "value": "", "description": "High Risk Profile ID", "type": "default", "enabled": true}, {"key": "rule_id", "value": "", "description": "Rule ID for testing (set dynamically from responses)", "type": "default", "enabled": true}, {"key": "customer_id", "value": "cust_test_customer_001", "description": "Test customer ID", "type": "default", "enabled": true}, {"key": "customer_id_high_risk", "value": "cust_high_risk_002", "description": "High risk test customer ID", "type": "default", "enabled": true}, {"key": "customer_id_low_risk", "value": "cust_low_risk_003", "description": "Low risk test customer ID", "type": "default", "enabled": true}, {"key": "assignment_id", "value": "", "description": "Customer assignment ID (set dynamically from responses)", "type": "default", "enabled": true}, {"key": "current_date", "value": "2025-01-20T00:00:00Z", "description": "Current date for testing date-based operations", "type": "default", "enabled": true}, {"key": "future_date", "value": "2025-12-31T23:59:59Z", "description": "Future date for testing effective dates", "type": "default", "enabled": true}, {"key": "past_date", "value": "2024-01-01T00:00:00Z", "description": "Past date for testing historical data", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-01-20T12:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}