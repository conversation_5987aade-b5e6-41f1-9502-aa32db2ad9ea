{"info": {"name": "Risk Profile Management API", "description": "Comprehensive API collection for Risk Profile Management System in Qeep platform. Includes endpoints for risk profiles, rules, customer assignments, and analytics.", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:3003/api", "type": "string"}, {"key": "tenant_id", "value": "ten_ronna_bank_12345", "type": "string"}, {"key": "risk_profile_id", "value": "", "type": "string"}, {"key": "rule_id", "value": "", "type": "string"}, {"key": "customer_id", "value": "", "type": "string"}, {"key": "assignment_id", "value": "", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "item": [{"name": "Risk Profiles", "description": "Risk Profile management endpoints", "item": [{"name": "Create Risk Profile", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Custom High Risk Profile\",\n  \"description\": \"Custom profile for high-risk customers with specific requirements\",\n  \"rule_category\": \"TRANSACTION\",\n  \"priority\": 250,\n  \"assignment_priority\": 250,\n  \"metadata\": {\n    \"custom_profile\": true,\n    \"industry_specific\": \"fintech\",\n    \"compliance_level\": \"enhanced\"\n  }\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/risk-profiles", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "risk-profiles"]}, "description": "Create a new risk profile for a tenant"}, "response": []}, {"name": "Get All Risk Profiles", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/risk-profiles?page=1&limit=20&search=&rule_category=TRANSACTION&is_active=true&is_system_generated=false&sort_by=name&sort_order=asc", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "risk-profiles"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "search", "value": ""}, {"key": "rule_category", "value": "TRANSACTION"}, {"key": "is_active", "value": "true"}, {"key": "is_system_generated", "value": "false"}, {"key": "sort_by", "value": "name"}, {"key": "sort_order", "value": "asc"}]}, "description": "Get all risk profiles for a tenant with filtering and pagination"}, "response": []}, {"name": "Get Risk Profile by ID", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/risk-profiles/{{risk_profile_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "risk-profiles", "{{risk_profile_id}}"]}, "description": "Get a specific risk profile by ID"}, "response": []}, {"name": "Update Risk Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Custom High Risk Profile\",\n  \"description\": \"Updated description for custom high-risk profile\",\n  \"priority\": 300,\n  \"assignment_priority\": 300,\n  \"is_active\": true,\n  \"metadata\": {\n    \"custom_profile\": true,\n    \"industry_specific\": \"fintech\",\n    \"compliance_level\": \"enhanced\",\n    \"last_updated_reason\": \"Regulatory requirements update\"\n  }\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/risk-profiles/{{risk_profile_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "risk-profiles", "{{risk_profile_id}}"]}, "description": "Update an existing risk profile"}, "response": []}, {"name": "Delete Risk Profile", "request": {"method": "DELETE", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/risk-profiles/{{risk_profile_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "risk-profiles", "{{risk_profile_id}}"]}, "description": "Delete a risk profile (soft delete)"}, "response": []}, {"name": "Get System Generated Profiles", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/risk-profiles/system-generated", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "risk-profiles", "system-generated"]}, "description": "Get all system-generated risk profiles"}, "response": []}, {"name": "Get Custom Profiles", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/risk-profiles/custom", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "risk-profiles", "custom"]}, "description": "Get all custom (non-system) risk profiles"}, "response": []}, {"name": "Search Risk Profiles", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/risk-profiles/search?q=high%20risk&category=TRANSACTION", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "risk-profiles", "search"], "query": [{"key": "q", "value": "high%20risk"}, {"key": "category", "value": "TRANSACTION"}]}, "description": "Search risk profiles by name or description"}, "response": []}, {"name": "Get Risk Profile Statistics", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/risk-profiles/stats", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "risk-profiles", "stats"]}, "description": "Get risk profile statistics and counts"}, "response": []}]}, {"name": "Rules", "description": "Rule management endpoints", "item": [{"name": "Create Rule", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"risk_profile_id\": \"{{risk_profile_id}}\",\n  \"name\": \"Large Wire Transfer Alert\",\n  \"description\": \"Generate alert for large wire transfers exceeding threshold\",\n  \"rule_type\": \"THRESHOLD\",\n  \"rule_category\": \"TRANSACTION\",\n  \"conditions\": {\n    \"type\": \"AND\",\n    \"conditions\": [\n      {\n        \"field\": \"transaction.amount\",\n        \"operator\": \"GREATER_THAN\",\n        \"value\": 50000,\n        \"metadata\": {\n          \"currency\": \"USD\"\n        }\n      },\n      {\n        \"field\": \"transaction.channel\",\n        \"operator\": \"EQUALS\",\n        \"value\": \"WIRE_TRANSFER\",\n        \"metadata\": {\n          \"channel_type\": \"INTERNATIONAL\"\n        }\n      }\n    ]\n  },\n  \"actions\": {\n    \"actions\": [\n      {\n        \"type\": \"GENERATE_ALERT\",\n        \"severity\": \"HIGH\",\n        \"message\": \"Large wire transfer detected - requires review\",\n        \"metadata\": {\n          \"auto_resolve_hours\": 4,\n          \"escalation_required\": true\n        }\n      },\n      {\n        \"type\": \"REQUIRE_ENHANCED_DUE_DILIGENCE\",\n        \"value\": true,\n        \"metadata\": {\n          \"edd_type\": \"WIRE_TRANSFER_REVIEW\"\n        }\n      }\n    ]\n  },\n  \"thresholds\": {\n    \"amount_threshold\": 50000,\n    \"alert_threshold\": 1\n  },\n  \"priority\": 400,\n  \"effective_from\": \"2025-01-01T00:00:00Z\",\n  \"metadata\": {\n    \"custom_rule\": true,\n    \"compliance_category\": \"WIRE_TRANSFER_MONITORING\",\n    \"regulatory_requirement\": \"BSA_WIRE_TRANSFER\"\n  }\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/risk-profiles/{{risk_profile_id}}/rules", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "risk-profiles", "{{risk_profile_id}}", "rules"]}, "description": "Create a new rule for a risk profile"}, "response": []}, {"name": "Get All Rules", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/rules?page=1&limit=20&search=&rule_category=TRANSACTION&rule_type=THRESHOLD&is_active=true&sort_by=priority&sort_order=desc", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "rules"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "search", "value": ""}, {"key": "rule_category", "value": "TRANSACTION"}, {"key": "rule_type", "value": "THRESHOLD"}, {"key": "is_active", "value": "true"}, {"key": "sort_by", "value": "priority"}, {"key": "sort_order", "value": "desc"}]}, "description": "Get all rules for a tenant with filtering and pagination"}, "response": []}, {"name": "Get Rules by Risk Profile", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/risk-profiles/{{risk_profile_id}}/rules", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "risk-profiles", "{{risk_profile_id}}", "rules"]}, "description": "Get all rules for a specific risk profile"}, "response": []}, {"name": "Get Rule by ID", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/rules/{{rule_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "rules", "{{rule_id}}"]}, "description": "Get a specific rule by ID"}, "response": []}, {"name": "Update Rule", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Large Wire Transfer Alert\",\n  \"description\": \"Updated rule for large wire transfers with enhanced monitoring\",\n  \"priority\": 450,\n  \"is_active\": true,\n  \"thresholds\": {\n    \"amount_threshold\": 75000,\n    \"alert_threshold\": 1\n  },\n  \"metadata\": {\n    \"custom_rule\": true,\n    \"compliance_category\": \"WIRE_TRANSFER_MONITORING\",\n    \"regulatory_requirement\": \"BSA_WIRE_TRANSFER\",\n    \"last_updated_reason\": \"Threshold adjustment per compliance review\"\n  }\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/rules/{{rule_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "rules", "{{rule_id}}"]}, "description": "Update an existing rule"}, "response": []}, {"name": "Delete Rule", "request": {"method": "DELETE", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/rules/{{rule_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "rules", "{{rule_id}}"]}, "description": "Delete a rule (soft delete)"}, "response": []}, {"name": "Get Active Rules", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/rules/active?risk_profile_id={{risk_profile_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "rules", "active"], "query": [{"key": "risk_profile_id", "value": "{{risk_profile_id}}"}]}, "description": "Get all active rules, optionally filtered by risk profile"}, "response": []}, {"name": "Get Rules by Category", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/rules/category/GEOGRAPHIC", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "rules", "category", "GEOGRAPHIC"]}, "description": "Get all rules by category (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, B<PERSON><PERSON><PERSON><PERSON><PERSON>, GE<PERSON><PERSON><PERSON><PERSON><PERSON>, FREQUENCY, CHANNEL, COUNTERPARTY, ANOMALY)"}, "response": []}, {"name": "Get Rules by Type", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/rules/type/THRESHOLD", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "rules", "type", "THRESHOLD"]}, "description": "Get all rules by type (THRE<PERSON><PERSON><PERSON><PERSON>, CO<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>H<PERSON>, FREQUENCY_BASED, AMOUNT_BASED, CUSTOM)"}, "response": []}, {"name": "Get Effective Rules", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/rules/effective?date=2025-01-20T00:00:00Z", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "rules", "effective"], "query": [{"key": "date", "value": "2025-01-20T00:00:00Z"}]}, "description": "Get all rules effective at a specific date"}, "response": []}, {"name": "Search Rules", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/rules/search?q=transaction%20alert&category=TRANSACTION", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "rules", "search"], "query": [{"key": "q", "value": "transaction%20alert"}, {"key": "category", "value": "TRANSACTION"}]}, "description": "Search rules by name or description"}, "response": []}, {"name": "Validate Rule Conditions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"conditions\": {\n    \"type\": \"AND\",\n    \"conditions\": [\n      {\n        \"field\": \"transaction.amount\",\n        \"operator\": \"GREATER_THAN\",\n        \"value\": 10000,\n        \"metadata\": {\n          \"currency\": \"USD\"\n        }\n      }\n    ]\n  }\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/rules/validate/conditions", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "rules", "validate", "conditions"]}, "description": "Validate rule conditions structure"}, "response": []}, {"name": "Validate Rule Actions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"actions\": {\n    \"actions\": [\n      {\n        \"type\": \"GENERATE_ALERT\",\n        \"severity\": \"HIGH\",\n        \"message\": \"Test alert message\",\n        \"metadata\": {\n          \"auto_resolve_hours\": 24\n        }\n      }\n    ]\n  }\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/rules/validate/actions", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "rules", "validate", "actions"]}, "description": "Validate rule actions structure"}, "response": []}]}, {"name": "Customer Assignments", "description": "Customer risk profile assignment endpoints", "item": [{"name": "Assign Customer to Risk Profile", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": \"{{customer_id}}\",\n  \"risk_profile_id\": \"{{risk_profile_id}}\",\n  \"assignment_type\": \"MANUAL\",\n  \"assignment_criteria\": {\n    \"reason\": \"Customer risk assessment update\",\n    \"risk_factors\": [\"high_transaction_volume\", \"international_exposure\"],\n    \"assessment_date\": \"2025-01-20T00:00:00Z\"\n  },\n  \"assignment_priority\": 200,\n  \"allow_override\": true,\n  \"effective_from\": \"2025-01-20T00:00:00Z\",\n  \"metadata\": {\n    \"assigned_by_user\": \"compliance_officer_001\",\n    \"assignment_reason\": \"Quarterly risk review\",\n    \"review_required\": true\n  }\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/customers/{{customer_id}}/risk-profile-assignments", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "customers", "{{customer_id}}", "risk-profile-assignments"]}, "description": "Assign a customer to a risk profile"}, "response": []}, {"name": "Get Customer Risk Profile Assignments", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/customers/{{customer_id}}/risk-profile-assignments", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "customers", "{{customer_id}}", "risk-profile-assignments"]}, "description": "Get all risk profile assignments for a customer"}, "response": []}, {"name": "Get All Customer Assignments", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/customer-assignments?page=1&limit=20&assignment_type=MANUAL&is_active=true&sort_by=assigned_at&sort_order=desc", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "customer-assignments"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "assignment_type", "value": "MANUAL"}, {"key": "is_active", "value": "true"}, {"key": "sort_by", "value": "assigned_at"}, {"key": "sort_order", "value": "desc"}]}, "description": "Get all customer assignments with filtering and pagination"}, "response": []}, {"name": "Get Assignment by ID", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/customer-assignments/{{assignment_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "customer-assignments", "{{assignment_id}}"]}, "description": "Get a specific customer assignment by ID"}, "response": []}, {"name": "Update Customer Assignment", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"assignment_priority\": 250,\n  \"allow_override\": false,\n  \"is_active\": true,\n  \"effective_until\": \"2025-12-31T23:59:59Z\",\n  \"metadata\": {\n    \"assigned_by_user\": \"compliance_officer_001\",\n    \"assignment_reason\": \"Updated quarterly risk review\",\n    \"review_required\": true,\n    \"last_updated_reason\": \"Risk level adjustment\"\n  }\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/customer-assignments/{{assignment_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "customer-assignments", "{{assignment_id}}"]}, "description": "Update an existing customer assignment"}, "response": []}, {"name": "Remove Customer Assignment", "request": {"method": "DELETE", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/customer-assignments/{{assignment_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "customer-assignments", "{{assignment_id}}"]}, "description": "Remove a customer assignment (soft delete)"}, "response": []}, {"name": "Get Assignments by Risk Profile", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/risk-profiles/{{risk_profile_id}}/assignments", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "risk-profiles", "{{risk_profile_id}}", "assignments"]}, "description": "Get all customer assignments for a specific risk profile"}, "response": []}, {"name": "Get Automatic Assignments", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/customer-assignments/automatic", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "customer-assignments", "automatic"]}, "description": "Get all automatic customer assignments"}, "response": []}, {"name": "Get Manual Assignments", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/customer-assignments/manual", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "customer-assignments", "manual"]}, "description": "Get all manual customer assignments"}, "response": []}]}, {"name": "Analytics & Operations", "description": "Analytics, bulk operations, and advanced features", "item": [{"name": "Auto-Assign Customers", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"criteria\": {\n    \"risk_level\": \"HIGH\",\n    \"customer_status\": [\"ACTIVE\", \"VERIFIED\"],\n    \"country\": [\"US\", \"CA\"],\n    \"custom_filters\": {\n      \"transaction_volume_last_30_days\": {\n        \"operator\": \"GREATER_THAN\",\n        \"value\": 100000\n      },\n      \"kyc_status\": \"ENHANCED_DUE_DILIGENCE_REQUIRED\"\n    }\n  },\n  \"dry_run\": true\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/customer-assignments/auto-assign", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "customer-assignments", "auto-assign"]}, "description": "Automatically assign customers to risk profiles based on criteria"}, "response": []}, {"name": "Bulk Assign Customers", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"customer_ids\": [\n    \"cust_001\",\n    \"cust_002\",\n    \"cust_003\"\n  ],\n  \"risk_profile_id\": \"{{risk_profile_id}}\",\n  \"assignment_type\": \"MANUAL\",\n  \"assignment_priority\": 200,\n  \"allow_override\": true,\n  \"effective_from\": \"2025-01-20T00:00:00Z\",\n  \"metadata\": {\n    \"bulk_operation\": true,\n    \"operation_reason\": \"Quarterly risk review batch update\",\n    \"assigned_by_user\": \"compliance_officer_001\"\n  }\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/customer-assignments/bulk-assign", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "customer-assignments", "bulk-assign"]}, "description": "Bulk assign multiple customers to a risk profile"}, "response": []}, {"name": "Get Customer Risk Profile Summary", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/customers/{{customer_id}}/risk-profile-summary", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "customers", "{{customer_id}}", "risk-profile-summary"]}, "description": "Get comprehensive risk profile summary for a customer including applicable rules and precedence"}, "response": []}, {"name": "Get Applicable Rules for Customer", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/customers/{{customer_id}}/applicable-rules", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "customers", "{{customer_id}}", "applicable-rules"]}, "description": "Get all rules applicable to a specific customer based on their risk profile assignments"}, "response": []}, {"name": "Get Rule Execution History", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/rule-execution-history?page=1&limit=20&rule_id={{rule_id}}&customer_id={{customer_id}}&start_date=2025-01-01T00:00:00Z&end_date=2025-01-31T23:59:59Z", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "rule-execution-history"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "rule_id", "value": "{{rule_id}}"}, {"key": "customer_id", "value": "{{customer_id}}"}, {"key": "start_date", "value": "2025-01-01T00:00:00Z"}, {"key": "end_date", "value": "2025-01-31T23:59:59Z"}]}, "description": "Get rule execution history with filtering options"}, "response": []}, {"name": "Get Risk Profile Statistics", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/analytics/risk-profile-stats", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "analytics", "risk-profile-stats"]}, "description": "Get comprehensive risk profile statistics and metrics"}, "response": []}, {"name": "Get Rule Execution Statistics", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/analytics/rule-execution-stats?start_date=2025-01-01T00:00:00Z&end_date=2025-01-31T23:59:59Z", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "analytics", "rule-execution-stats"], "query": [{"key": "start_date", "value": "2025-01-01T00:00:00Z"}, {"key": "end_date", "value": "2025-01-31T23:59:59Z"}]}, "description": "Get rule execution statistics and performance metrics"}, "response": []}, {"name": "Test Rule Conditions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"rule_id\": \"{{rule_id}}\",\n  \"test_data\": {\n    \"transaction\": {\n      \"amount\": 75000,\n      \"currency\": \"USD\",\n      \"channel\": \"WIRE_TRANSFER\",\n      \"destination_country\": \"US\",\n      \"origin_country\": \"CA\"\n    },\n    \"customer\": {\n      \"id\": \"{{customer_id}}\",\n      \"risk_level\": \"HIGH\",\n      \"kyc_status\": \"VERIFIED\"\n    },\n    \"counterparty\": {\n      \"risk_score\": 65,\n      \"sanctions_match\": false,\n      \"pep_status\": false\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/rules/{{rule_id}}/test", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "rules", "{{rule_id}}", "test"]}, "description": "Test rule conditions against sample data"}, "response": []}, {"name": "Export Risk Profile Configuration", "request": {"method": "GET", "header": [{"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/risk-profiles/export?format=json&include_rules=true&include_assignments=false", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "risk-profiles", "export"], "query": [{"key": "format", "value": "json"}, {"key": "include_rules", "value": "true"}, {"key": "include_assignments", "value": "false"}]}, "description": "Export risk profile configuration for backup or migration"}, "response": []}, {"name": "Import Risk Profile Configuration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Tenant-ID", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"risk_profiles\": [\n    {\n      \"name\": \"Imported Custom Profile\",\n      \"description\": \"Profile imported from another tenant\",\n      \"rule_category\": \"TRANSACTION\",\n      \"priority\": 150,\n      \"assignment_priority\": 150,\n      \"rules\": [\n        {\n          \"name\": \"Imported Transaction Rule\",\n          \"description\": \"Imported rule for transaction monitoring\",\n          \"rule_type\": \"THRESHOLD\",\n          \"rule_category\": \"TRANSACTION\",\n          \"conditions\": {\n            \"type\": \"AND\",\n            \"conditions\": [\n              {\n                \"field\": \"transaction.amount\",\n                \"operator\": \"GREATER_THAN\",\n                \"value\": 25000\n              }\n            ]\n          },\n          \"actions\": {\n            \"actions\": [\n              {\n                \"type\": \"GENERATE_ALERT\",\n                \"severity\": \"MEDIUM\",\n                \"message\": \"Imported rule triggered\"\n              }\n            ]\n          },\n          \"priority\": 200\n        }\n      ]\n    }\n  ],\n  \"import_options\": {\n    \"overwrite_existing\": false,\n    \"validate_before_import\": true,\n    \"dry_run\": true\n  }\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/risk-profiles/import", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "risk-profiles", "import"]}, "description": "Import risk profile configuration from backup or another tenant"}, "response": []}]}]}