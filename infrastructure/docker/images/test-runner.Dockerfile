# Test Runner Dockerfile
# Specialized container for running tests

FROM node:18-alpine

# Install system dependencies
RUN apk add --no-cache \
    libc6-compat \
    curl \
    bash \
    git

# Set working directory
WORKDIR /app

# Enable pnpm
RUN corepack enable pnpm

# Copy package files
COPY package*.json pnpm-lock.yaml* ./

# Install all dependencies (including dev dependencies for testing)
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Generate proto types
RUN node scripts/generate-proto-standalone.js

# Create non-root user for security
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nestjs && \
    chown -R nestjs:nodejs /app

# Switch to non-root user
USER nestjs

# Set environment variables
ENV NODE_ENV=test
ENV CI=true

# Default command (can be overridden in docker-compose)
CMD ["pnpm", "run", "test:e2e"]
