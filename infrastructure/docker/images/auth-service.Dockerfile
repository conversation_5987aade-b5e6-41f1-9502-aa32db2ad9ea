# Auth Service Dockerfile
# Multi-stage build for development and production

# Base stage with Node.js
FROM node:18-alpine AS base
RUN apk add --no-cache \
  libc6-compat \
  curl \
  bash \
  git
WORKDIR /app
RUN corepack enable pnpm

# Dependencies stage
FROM base AS deps
COPY package*.json pnpm-lock.yaml* ./
RUN pnpm install --frozen-lockfile --prod

# Development stage
FROM base AS development
COPY package*.json pnpm-lock.yaml* ./
RUN pnpm install --frozen-lockfile
COPY . .
RUN node scripts/generate-proto-standalone.js

# Create non-root user
RUN addgroup --system --gid 1001 nodejs && \
  adduser --system --uid 1001 nestjs && \
  chown -R nestjs:nodejs /app

USER nestjs
EXPOSE **************
ENV NODE_ENV=development
ENV PORT=3001
ENV GRPC_PORT=3012

HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

CMD ["pnpm", "run", "dev:auth-service"]

# Build stage
FROM base AS build
COPY package*.json pnpm-lock.yaml* ./
RUN pnpm install --frozen-lockfile
COPY . .
RUN node scripts/generate-proto-standalone.js
RUN pnpm nx build auth-service --prod

# Production stage
FROM base AS production
RUN addgroup --system --gid 1001 nodejs && \
  adduser --system --uid 1001 nestjs

COPY --from=deps --chown=nestjs:nodejs /app/node_modules ./node_modules
COPY --from=build --chown=nestjs:nodejs /app/dist ./dist
COPY --from=build --chown=nestjs:nodejs /app/package*.json ./
COPY --from=build --chown=nestjs:nodejs /app/proto ./proto
COPY --from=build --chown=nestjs:nodejs /app/libs/proto/src/generated ./libs/proto/src/generated

USER nestjs
EXPOSE 3001 3012
ENV NODE_ENV=production
ENV PORT=3001
ENV GRPC_PORT=3012

HEALTHCHECK --interval=30s --timeout=3s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

CMD ["node", "dist/apps/auth-service/main.js"]

# Test stage
FROM development AS test
ENV NODE_ENV=test
CMD ["pnpm", "run", "test:auth-service"]
