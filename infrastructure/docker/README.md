# Qeep Docker Infrastructure

This directory contains Docker configurations for the Qeep microservices platform, providing containerized development, testing, and production environments.

## 📁 Directory Structure

```
infrastructure/docker/
├── compose/                    # Docker Compose files
│   ├── docker-compose.dev.yml     # Development environment
│   ├── docker-compose.prod.yml    # Production environment
│   └── docker-compose.test.yml    # Testing environment
├── images/                     # Dockerfiles for services (for production builds)
│   ├── # Note: API Gateway replaced with Nginx reverse proxy
│   ├── auth-service.Dockerfile     # Authentication service
│   ├── user-service.Dockerfile     # User management service
│   ├── tenant-service.Dockerfile   # Multi-tenancy service
│   ├── notification-service.Dockerfile # Notification service
│   └── test-runner.Dockerfile      # Test execution environment
└── README.md                   # This file
```

## 🚀 Quick Start

### Prerequisites

- Docker 20.0+ and Docker Compose 2.0+
- 8GB+ RAM and 4+ CPU cores recommended
- 20GB+ free disk space

### Development Environment

1. **Start all services:**

   ```bash
   pnpm run docker:dev:start
   # or
   ./scripts/docker-dev.sh start
   ```

2. **Start specific services:**

   ```bash
   ./scripts/docker-dev.sh start postgres redis auth-service
   ```

3. **View logs:**

   ```bash
   pnpm run docker:dev:logs
   # or for specific service
   ./scripts/docker-dev.sh logs auth-service
   ```

4. **Check status:**

   ```bash
   pnpm run docker:dev:status
   ```

5. **Stop services:**
   ```bash
   pnpm run docker:dev:stop
   ```

## 🏗️ Service Architecture

### Infrastructure Services

| Service    | Port   | Purpose                         |
| ---------- | ------ | ------------------------------- |
| Nginx      | 80/443 | Reverse proxy and load balancer |
| PostgreSQL | 5432   | Primary database                |
| Redis      | 6379   | Caching and sessions            |

### Application Services

| Service              | HTTP Port | gRPC Port | Purpose                          |
| -------------------- | --------- | --------- | -------------------------------- |
| API Gateway          | 3000      | -         | Request routing and aggregation  |
| Auth Service         | 3001      | 3012      | Authentication and authorization |
| User Service         | 3002      | 3013      | User management                  |
| Tenant Service       | 3003      | 3014      | Multi-tenancy                    |
| Notification Service | 3004      | 3015      | Communications                   |
| Audit Service        | 3005      | 3016      | Compliance logging               |

## 🌐 Service Access URLs

### Via Nginx Proxy (Recommended)

- **Main API**: http://localhost:8080/api/
- **Health Check**: http://localhost:8080/health

### Direct Service Access (Development)

Add these entries to your `/etc/hosts` file:

```
127.0.0.1 qeep.local auth.qeep.local user.qeep.local tenant.qeep.local notification.qeep.local audit.qeep.local
```

Then access services via:

- **Auth Service**: http://auth.qeep.local:8080/
- **User Service**: http://user.qeep.local:8080/
- **Tenant Service**: http://tenant.qeep.local:8080/
- **Notification Service**: http://notification.qeep.local:8080/
- **Audit Service**: http://audit.qeep.local:8080/

### Direct Port Access

- API Gateway: http://localhost:3000/api
- Auth Service: http://localhost:3001/api
- User Service: http://localhost:3002/api
- Tenant Service: http://localhost:3003/api
- Notification Service: http://localhost:3004/api
- Audit Service: http://localhost:3005/api

## 🔧 Development Workflow

### Development Services

Development services use the base `node:18-alpine` image and mount the source code as volumes for hot reload. Dependencies are installed automatically when containers start.

### Debugging

```bash
# Access service shell
docker-compose -f infrastructure/docker/compose/docker-compose.dev.yml exec auth-service bash

# View service logs in real-time
./scripts/docker-dev.sh logs auth-service

# Check service health
docker-compose -f infrastructure/docker/compose/docker-compose.dev.yml ps
```

### Database Operations

```bash
# Connect to PostgreSQL
docker-compose -f infrastructure/docker/compose/docker-compose.dev.yml exec postgres psql -U qeep_dev -d qeep_dev

# Connect to Redis
docker-compose -f infrastructure/docker/compose/docker-compose.dev.yml exec redis redis-cli -a redis_dev_password
```

## 🧪 Testing

### Run Tests in Docker

```bash
# Run all tests
pnpm run docker:dev:test

# Run specific test suite
docker-compose -f infrastructure/docker/compose/docker-compose.test.yml run test-runner pnpm run test:auth-service
```

## 🏭 Production Deployment

### Build Production Images

```bash
# Build all production images
docker-compose -f infrastructure/docker/compose/docker-compose.prod.yml build

# Build specific service
docker build -f infrastructure/docker/images/auth-service.Dockerfile --target production -t qeep/auth-service:latest .
```

### Deploy to Production

```bash
# Start production environment
docker-compose -f infrastructure/docker/compose/docker-compose.prod.yml up -d
```

## 🔍 Monitoring

### Health Checks

All services include health checks accessible at `/health` endpoints:

- API Gateway: http://localhost:3000/health
- Auth Service: http://localhost:3001/health
- User Service: http://localhost:3002/health

### Container Monitoring

```bash
# View resource usage
docker stats

# Check container health
docker-compose -f infrastructure/docker/compose/docker-compose.dev.yml ps
```

## 🛠️ Troubleshooting

### Common Issues

1. **Port conflicts:**

   ```bash
   # Check what's using a port
   lsof -i :3000

   # Kill process using port
   kill -9 $(lsof -t -i:3000)
   ```

2. **Memory issues:**

   ```bash
   # Check Docker memory usage
   docker stats

   # Increase Docker memory in Docker Desktop settings
   ```

3. **Volume issues:**

   ```bash
   # Clean up volumes
   pnpm run docker:dev:cleanup
   ```

4. **Network issues:**
   ```bash
   # Recreate network
   docker-compose -f infrastructure/docker/compose/docker-compose.dev.yml down
   docker network prune
   docker-compose -f infrastructure/docker/compose/docker-compose.dev.yml up -d
   ```

### Performance Optimization

1. **Use .dockerignore** to exclude unnecessary files
2. **Multi-stage builds** reduce image size
3. **Volume mounts** for development hot-reload
4. **Health checks** ensure service readiness

## 📝 Environment Variables

Environment variables are loaded from `.env.development` for both local and Docker development. Key variables:

```bash
# Database
POSTGRES_HOST=localhost  # For local development
POSTGRES_PORT=5432
POSTGRES_USER=qeep_dev
POSTGRES_PASSWORD=qeep_password_dev
POSTGRES_DB=qeep_dev

# Database URLs
DATABASE_URL=postgresql://qeep_dev:qeep_password_dev@localhost:5432/qeep_dev

# Redis
REDIS_HOST=localhost  # For local development
REDIS_PORT=6379
REDIS_PASSWORD=redis_dev_password
REDIS_URL=redis://:redis_dev_password@localhost:6379
REDIS_URL_DOCKER=redis://:redis_dev_password@redis:6379

# Services
AUTH_SERVICE_PORT=3001
AUTH_SERVICE_GRPC_PORT=3012
API_GATEWAY_PORT=3000

# Docker Service URLs (for container communication)
AUTH_SERVICE_GRPC_URL_DOCKER=auth-service:3012
```

## 🔐 Security

- All services run as non-root users
- Health checks use internal endpoints
- Secrets managed through environment variables
- Network isolation between services

## 📚 Additional Resources

- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [Qeep Architecture Overview](../../docs/architecture/system-overview.md)
- [Development Setup Guide](../../docs/development/environment-setup.md)
