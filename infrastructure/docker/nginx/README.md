# Qeep Nginx Reverse Proxy Configuration

This directory contains the Nginx reverse proxy configuration for the Qeep platform, providing load balancing, SSL termination, rate limiting, and request routing.

## 📁 Directory Structure

```
nginx/
├── README.md                    # This file
├── nginx.conf                   # Main Nginx configuration
├── conf.d/                      # Server configurations
│   ├── upstreams.conf          # Development upstreams
│   ├── upstreams-prod.conf     # Production upstreams
│   ├── qeep-api.conf       # Main API routing configuration
│   ├── services.conf           # Direct service access
│   └── monitoring.conf         # Monitoring tools access
├── ssl/                        # SSL certificates
│   ├── README.md              # SSL setup instructions
│   └── .gitkeep               # Ensure directory exists
└── logs/                       # Nginx logs
    └── .gitkeep               # Ensure directory exists
```

## 🚀 Features

### Load Balancing & High Availability

- **Upstream Configuration**: Automatic failover and load distribution
- **Health Checks**: Automatic detection of unhealthy services
- **Connection Pooling**: Efficient connection reuse with keepalive

### Security

- **Rate Limiting**: Configurable rate limits per endpoint type
- **Security Headers**: OWASP recommended security headers
- **CORS Support**: Configurable CORS for development and production
- **SSL/TLS**: Support for SSL termination and HTTPS redirect

### Performance

- **Gzip Compression**: Automatic compression for supported content types
- **Caching**: Static file caching with appropriate headers
- **Buffer Optimization**: Optimized buffer sizes for different content types

### Observability

- **Structured Logging**: JSON-formatted logs with correlation IDs
- **Metrics Endpoint**: Nginx status endpoint for monitoring
- **Request Tracing**: Request ID and correlation ID propagation

## 🔧 Configuration

### Environment Variables

Add these to your `.env.development` file:

```bash
# Nginx Configuration
NGINX_ENABLED=true
NGINX_PORT=8080
NGINX_HTTPS_PORT=8443
NGINX_LOG_LEVEL=warn
NGINX_PROXY_URL=http://localhost:8080

# Upstream Configuration
NGINX_UPSTREAM_API_GATEWAY=host.docker.internal:3000
NGINX_UPSTREAM_AUTH_SERVICE=host.docker.internal:3001
NGINX_UPSTREAM_USER_SERVICE=host.docker.internal:3002
NGINX_UPSTREAM_TENANT_SERVICE=host.docker.internal:3003
NGINX_UPSTREAM_NOTIFICATION_SERVICE=host.docker.internal:3004
NGINX_UPSTREAM_AUDIT_SERVICE=host.docker.internal:3005
```

### Service Access URLs

With Nginx proxy enabled, services are accessible via:

#### Main API Gateway

- **Primary**: http://localhost:8080/api/
- **Health**: http://localhost:8080/health

#### Direct Service Access (Development)

- **Auth Service**: http://auth.qeep.local:8080/
- **User Service**: http://user.qeep.local:8080/
- **Tenant Service**: http://tenant.qeep.local:8080/
- **Notification Service**: http://notification.qeep.local:8080/
- **Audit Service**: http://audit.qeep.local:8080/

#### Monitoring Tools

- **Grafana**: http://grafana.qeep.local:8080/
- **Prometheus**: http://prometheus.qeep.local:8080/
- **Jaeger**: http://jaeger.qeep.local:8080/
- **Nginx Status**: http://status.qeep.local/nginx_status

## 🛠️ Development Setup

### 1. Add Local DNS Entries

Add these entries to your `/etc/hosts` file:

```bash
127.0.0.1 qeep.local
127.0.0.1 api.qeep.local
127.0.0.1 auth.qeep.local
127.0.0.1 user.qeep.local
127.0.0.1 tenant.qeep.local
127.0.0.1 notification.qeep.local
127.0.0.1 audit.qeep.local
127.0.0.1 grafana.qeep.local
127.0.0.1 prometheus.qeep.local
127.0.0.1 jaeger.qeep.local
127.0.0.1 status.qeep.local
```

### 2. Start Infrastructure with Nginx

```bash
# Start infrastructure including Nginx
pnpm run dev:infrastructure:start

# Start application services
pnpm run dev:services:start
```

### 3. Verify Setup

```bash
# Test main API Gateway
curl http://localhost:8080/health

# Test direct service access
curl http://auth.qeep.local:8080/health

# Check Nginx status
curl http://status.qeep.local:8080/nginx_status
```

## 📊 Monitoring & Logging

### Log Files

Nginx logs are stored in `./logs/` directory:

- `access.log` - General access logs
- `error.log` - Error logs
- `qeep-api.access.log` - Main API routing logs
- `auth-service.access.log` - Auth service logs
- `grafana.access.log` - Grafana access logs

### Log Format

Logs include correlation IDs and request tracing:

```
$remote_addr - $remote_user [$time_local] "$request"
$status $body_bytes_sent "$http_referer"
"$http_user_agent" "$http_x_forwarded_for"
service="$upstream_addr"
request_id="$http_x_request_id"
correlation_id="$http_x_correlation_id"
rt=$request_time uct="$upstream_connect_time"
```

### Rate Limiting

Different rate limits are applied:

- **API Endpoints**: 10 requests/second (burst 20)
- **Auth Endpoints**: 5 requests/second (burst 10)
- **General Endpoints**: 20 requests/second (burst 15)

## 🔒 Security Configuration

### Headers Applied

- `X-Frame-Options: SAMEORIGIN`
- `X-Content-Type-Options: nosniff`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`
- `X-Robots-Tag: noindex, nofollow` (development)

### CORS Configuration

Development CORS settings allow all origins. For production, configure specific origins in the Nginx configuration.

## 🚀 Production Deployment

### SSL Configuration

1. Obtain SSL certificates (Let's Encrypt recommended)
2. Place certificates in `ssl/` directory
3. Update `qeep-api.conf` to include SSL server block
4. Enable HTTPS redirect

### Load Balancing

For production, update `upstreams-prod.conf` to include multiple service replicas:

```nginx
upstream nginx_proxy {
    server nginx-proxy-1:80 weight=1;
    server nginx-proxy-2:80 weight=1;
    server nginx-proxy-3:80 weight=1;
}
```

### Performance Tuning

Adjust worker processes and connections based on server capacity:

```nginx
worker_processes auto;
worker_connections 2048;
```

## 🛠️ Troubleshooting

### Common Issues

1. **Service Unreachable**: Check upstream configuration and service health
2. **Rate Limiting**: Adjust rate limit zones if needed
3. **SSL Issues**: Verify certificate paths and permissions
4. **DNS Resolution**: Ensure `/etc/hosts` entries are correct

### Debug Commands

```bash
# Test Nginx configuration
docker exec qeep-nginx-dev nginx -t

# Reload Nginx configuration
docker exec qeep-nginx-dev nginx -s reload

# View Nginx logs
docker logs qeep-nginx-dev

# Check upstream status
curl -H "Host: status.qeep.local" http://localhost/nginx_status
```
