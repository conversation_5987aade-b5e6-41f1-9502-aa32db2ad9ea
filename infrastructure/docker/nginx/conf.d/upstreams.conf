# Qeep Service Upstreams Configuration
# Defines upstream servers for load balancing and failover

# Auth Service Upstream
upstream auth_service {
    # For OrbStack - services running locally
    server host.orb.internal:3001 max_fails=3 fail_timeout=30s;

    # Health check
    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# User Service Upstream
upstream user_service {
    # For OrbStack - services running locally
    server host.orb.internal:3002 max_fails=3 fail_timeout=30s;

    # Health check
    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# Tenant Service Upstream
upstream tenant_service {
    # For OrbStack - services running locally
    server host.orb.internal:3003 max_fails=3 fail_timeout=30s;

    # Health check
    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# Notification Service Upstream
upstream notification_service {
    # For OrbStack - services running locally
    server host.orb.internal:3004 max_fails=3 fail_timeout=30s;

    # Health check
    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# Audit Service Upstream
upstream audit_service {
    # For OrbStack - services running locally
    server host.orb.internal:3005 max_fails=3 fail_timeout=30s;

    # Health check
    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# Transaction Service Upstream
upstream transaction_service {
    # For OrbStack - services running locally
    server host.orb.internal:3006 max_fails=3 fail_timeout=30s;

    # Health check
    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# AML Service Upstream
upstream aml_service {
    # For OrbStack - services running locally
    server host.orb.internal:3007 max_fails=3 fail_timeout=30s;

    # Health check
    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# Customer Service Upstream
upstream customer_service {
    # For OrbStack - services running locally
    server host.orb.internal:3008 max_fails=3 fail_timeout=30s;

    # Health check
    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# Surveillance Service Upstream
upstream surveillance_service {
    # For OrbStack - services running locally
    server host.orb.internal:3009 max_fails=3 fail_timeout=30s;

    # Health check
    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# Integration Service Upstream
upstream integration_service {
    # For OrbStack - services running locally
    server host.orb.internal:3010 max_fails=3 fail_timeout=30s;

    # Health check
    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# Monitoring Service Upstream
upstream monitoring_service {
    # For OrbStack - services running locally
    server host.orb.internal:3012 max_fails=3 fail_timeout=30s;

    # Health check
    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# Monitoring Services Upstreams (commented out - not running in basic infrastructure)
# upstream prometheus {
#     server prometheus:9090 max_fails=2 fail_timeout=30s;
#     keepalive 8;
# }

# upstream grafana {
#     server grafana:3000 max_fails=2 fail_timeout=30s;
#     keepalive 8;
# }

# upstream jaeger {
#     server jaeger:16686 max_fails=2 fail_timeout=30s;
#     keepalive 8;
# }
