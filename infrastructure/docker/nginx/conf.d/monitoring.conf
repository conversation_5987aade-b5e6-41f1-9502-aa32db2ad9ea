# Qeep Monitoring Services Configuration
# Access to monitoring and observability tools
# DISABLED: Uncomment when monitoring services are running

# # Prometheus
# server {
#     listen 80;
#     server_name prometheus.qeep.local;
#
#     access_log /var/log/nginx/prometheus.access.log main;
#     error_log /var/log/nginx/prometheus.error.log warn;
#
#     # Basic auth for production (disabled for development)
#     # auth_basic "Prometheus";
#     # auth_basic_user_file /etc/nginx/.htpasswd;
#
#     location / {
#         proxy_pass http://prometheus;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#
#         # Prometheus specific settings
#         proxy_connect_timeout 30s;
#         proxy_send_timeout 120s;
#         proxy_read_timeout 120s;
#         proxy_buffering off;
#     }
# }

# # Grafana
# server {
#     listen 80;
#     server_name grafana.qeep.local;
#
#     access_log /var/log/nginx/grafana.access.log main;
#     error_log /var/log/nginx/grafana.error.log warn;
#
#     location / {
#         proxy_pass http://grafana;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#
#         # Grafana specific settings
#         proxy_connect_timeout 30s;
#         proxy_send_timeout 60s;
#         proxy_read_timeout 60s;
#
#         # WebSocket support for live updates
#         proxy_http_version 1.1;
#         proxy_set_header Upgrade $http_upgrade;
#         proxy_set_header Connection "upgrade";
#     }
# }

# # Jaeger
# server {
#     listen 80;
#     server_name jaeger.qeep.local;
#
#     access_log /var/log/nginx/jaeger.access.log main;
#     error_log /var/log/nginx/jaeger.error.log warn;
#
#     location / {
#         proxy_pass http://jaeger;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#
#         # Jaeger specific settings
#         proxy_connect_timeout 30s;
#         proxy_send_timeout 60s;
#         proxy_read_timeout 60s;
#     }
# }

# Nginx Status (for monitoring Nginx itself)
server {
    listen 80;
    server_name status.qeep.local;
    
    access_log off;
    
    location /nginx_status {
        stub_status on;
        access_log off;
        
        # Restrict access to localhost only
        allow 127.0.0.1;
        allow **********/16;  # Docker network
        deny all;
    }
    
    location / {
        return 404;
    }
}
