# Qeep Production Service Upstreams Configuration
# Defines upstream servers for production load balancing and failover

# Auth Service Upstream
upstream auth_service {
    server auth-service:3001 max_fails=3 fail_timeout=30s weight=1;
    # server auth-service-2:3001 max_fails=3 fail_timeout=30s weight=1;
    
    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# User Service Upstream
upstream user_service {
    server user-service:3002 max_fails=3 fail_timeout=30s weight=1;
    # server user-service-2:3002 max_fails=3 fail_timeout=30s weight=1;
    
    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# Tenant Service Upstream
upstream tenant_service {
    server tenant-service:3003 max_fails=3 fail_timeout=30s weight=1;
    # server tenant-service-2:3003 max_fails=3 fail_timeout=30s weight=1;
    
    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# Notification Service Upstream
upstream notification_service {
    server notification-service:3004 max_fails=3 fail_timeout=30s weight=1;
    # server notification-service-2:3004 max_fails=3 fail_timeout=30s weight=1;
    
    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# Audit Service Upstream
upstream audit_service {
    server audit-service:3005 max_fails=3 fail_timeout=30s weight=1;
    # server audit-service-2:3005 max_fails=3 fail_timeout=30s weight=1;

    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# Transaction Service Upstream
upstream transaction_service {
    server transaction-service:3006 max_fails=3 fail_timeout=30s weight=1;
    # server transaction-service-2:3006 max_fails=3 fail_timeout=30s weight=1;

    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# AML Service Upstream
upstream aml_service {
    server aml-service:3007 max_fails=3 fail_timeout=30s weight=1;
    # server aml-service-2:3007 max_fails=3 fail_timeout=30s weight=1;

    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# Customer Service Upstream
upstream customer_service {
    server customer-service:3008 max_fails=3 fail_timeout=30s weight=1;
    # server customer-service-2:3008 max_fails=3 fail_timeout=30s weight=1;

    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# Surveillance Service Upstream
upstream surveillance_service {
    server surveillance-service:3009 max_fails=3 fail_timeout=30s weight=1;
    # server surveillance-service-2:3009 max_fails=3 fail_timeout=30s weight=1;

    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# Integration Service Upstream
upstream integration_service {
    server integration-service:3010 max_fails=3 fail_timeout=30s weight=1;
    # server integration-service-2:3010 max_fails=3 fail_timeout=30s weight=1;

    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# Monitoring Service Upstream
upstream monitoring_service {
    server monitoring-service:3012 max_fails=3 fail_timeout=30s weight=1;
    # server monitoring-service-2:3012 max_fails=3 fail_timeout=30s weight=1;

    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# Monitoring Services Upstreams
upstream prometheus {
    server prometheus:9090 max_fails=2 fail_timeout=30s;
    keepalive 8;
}

upstream grafana {
    server grafana:3000 max_fails=2 fail_timeout=30s;
    keepalive 8;
}

upstream jaeger {
    server jaeger:16686 max_fails=2 fail_timeout=30s;
    keepalive 8;
}
