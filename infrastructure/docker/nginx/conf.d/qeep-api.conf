# Qeep Nginx Reverse Proxy Configuration
# Main entry point for all API requests

server {
    listen 80;
    server_name localhost api.qeep.local qeep.local;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header X-Robots-Tag "noindex, nofollow" always;
    
    # CORS headers for development
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, PATCH" always;
    add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-Correlation-ID,X-Request-ID" always;
    add_header Access-Control-Expose-Headers "Content-Length,Content-Range,X-Correlation-ID,X-Request-ID" always;
    
    # Rate limiting
    limit_req zone=api burst=20 nodelay;
    limit_conn addr 10;
    
    # Logging
    access_log /var/log/nginx/qeep-api.access.log api_gateway;
    error_log /var/log/nginx/qeep-api.error.log warn;
    
    # Health check endpoint - simple response
    location /health {
        return 200 '{"status":"healthy","service":"nginx-reverse-proxy","timestamp":"$time_iso8601","message":"Nginx reverse proxy is running"}';
        add_header Content-Type application/json;
    }

    # API v1 Health endpoint
    location /api/v1/health {
        return 200 '{"success":true,"status_code":200,"message":"Request successful","data":{"status":"HEALTH_STATUS_HEALTHY","system":{"service":"nginx-reverse-proxy","version":"1.0.0","environment":"development","timestamp":"$time_iso8601","uptime":"nginx"},"message":"Nginx reverse proxy is healthy"},"meta":{"timestamp":"$time_iso8601","request_id":"$request_id"}}';
        add_header Content-Type application/json;
    }
    
    # Auth Service API routes
    location ~ ^/api/v1/auth(/.*)?$ {
        proxy_pass http://auth_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Request-ID $request_id;
        proxy_set_header X-Correlation-ID $request_id;
    }

    # User Service API routes
    location ~ ^/api/v1/users(/.*)?$ {
        proxy_pass http://user_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Request-ID $request_id;
        proxy_set_header X-Correlation-ID $request_id;
    }

    # Tenant Service API routes (includes management endpoints)
    location ~ ^/api/v1/tenants(/.*)?$ {
        proxy_pass http://tenant_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Request-ID $request_id;
        proxy_set_header X-Correlation-ID $request_id;
    }

    # Notification Service API routes
    location ~ ^/api/v1/notifications(/.*)?$ {
        proxy_pass http://notification_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Request-ID $request_id;
        proxy_set_header X-Correlation-ID $request_id;
    }

    # Audit Service API routes
    location ~ ^/api/v1/audit(/.*)?$ {
        proxy_pass http://audit_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Request-ID $request_id;
        proxy_set_header X-Correlation-ID $request_id;
    }

    # Transaction Service API routes
    location ~ ^/api/v1/transactions(/.*)?$ {
        proxy_pass http://transaction_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Request-ID $request_id;
        proxy_set_header X-Correlation-ID $request_id;
    }

    # AML Service API routes
    location ~ ^/api/v1/aml(/.*)?$ {
        proxy_pass http://aml_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Request-ID $request_id;
        proxy_set_header X-Correlation-ID $request_id;
    }

    # Customer Service API routes
    location ~ ^/api/v1/customers(/.*)?$ {
        proxy_pass http://customer_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Request-ID $request_id;
        proxy_set_header X-Correlation-ID $request_id;
    }

    # Surveillance Service API routes
    location ~ ^/api/v1/surveillance(/.*)?$ {
        proxy_pass http://surveillance_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Request-ID $request_id;
        proxy_set_header X-Correlation-ID $request_id;
    }

    # Integration Service API routes
    location ~ ^/api/v1/integrations(/.*)?$ {
        proxy_pass http://integration_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Request-ID $request_id;
        proxy_set_header X-Correlation-ID $request_id;
    }

    # Monitoring Service API routes
    location ~ ^/api/v1/monitoring(/.*)?$ {
        proxy_pass http://monitoring_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Request-ID $request_id;
        proxy_set_header X-Correlation-ID $request_id;
    }

    # Fallback for other API routes
    location /api/ {
        return 404 '{"success":false,"status_code":404,"message":"API endpoint not found","error":{"message":"The requested API endpoint does not exist","timestamp":"$time_iso8601","path":"$uri"},"meta":{"timestamp":"$time_iso8601","request_id":"$request_id"}}';
        add_header Content-Type application/json;
    }
    
    # Redirect root to API documentation or health check
    location = / {
        return 302 /api/v1/health;
    }
    
    # Static files (if any)
    location /static/ {
        alias /var/www/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Test endpoint
    location /test {
        return 200 "Nginx proxy is working!\n";
        add_header Content-Type text/plain;
    }

    # Debug endpoint
    location /debug {
        return 200 "Server block matched! Host: $host, URI: $uri\n";
        add_header Content-Type text/plain;
    }

    # Simple API test
    location /api/test {
        return 200 "API location block matched! URI: $uri\n";
        add_header Content-Type text/plain;
    }

    # Deny access to hidden files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
}
