# Qeep Direct Service Access Configuration
# Direct access to individual services for development and debugging

# Auth Service
server {
    listen 80;
    server_name auth.qeep.local;
    
    access_log /var/log/nginx/auth-service.access.log main;
    error_log /var/log/nginx/auth-service.error.log warn;
    
    # Rate limiting for auth endpoints
    limit_req zone=auth burst=10 nodelay;
    limit_conn addr 5;
    
    location / {
        proxy_pass http://auth_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeout settings
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}

# User Service
server {
    listen 80;
    server_name user.qeep.local;
    
    access_log /var/log/nginx/user-service.access.log main;
    error_log /var/log/nginx/user-service.error.log warn;
    
    limit_req zone=general burst=15 nodelay;
    limit_conn addr 10;
    
    location / {
        proxy_pass http://user_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}

# Tenant Service
server {
    listen 80;
    server_name tenant.qeep.local;
    
    access_log /var/log/nginx/tenant-service.access.log main;
    error_log /var/log/nginx/tenant-service.error.log warn;
    
    limit_req zone=general burst=15 nodelay;
    limit_conn addr 10;
    
    location / {
        proxy_pass http://tenant_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}

# Notification Service
server {
    listen 80;
    server_name notification.qeep.local;
    
    access_log /var/log/nginx/notification-service.access.log main;
    error_log /var/log/nginx/notification-service.error.log warn;
    
    limit_req zone=general burst=15 nodelay;
    limit_conn addr 10;
    
    location / {
        proxy_pass http://notification_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}

# Audit Service
server {
    listen 80;
    server_name audit.qeep.local;

    access_log /var/log/nginx/audit-service.access.log main;
    error_log /var/log/nginx/audit-service.error.log warn;

    limit_req zone=general burst=15 nodelay;
    limit_conn addr 10;

    location / {
        proxy_pass http://audit_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}

# Transaction Service
server {
    listen 80;
    server_name transaction.qeep.local;

    access_log /var/log/nginx/transaction-service.access.log main;
    error_log /var/log/nginx/transaction-service.error.log warn;

    limit_req zone=general burst=20 nodelay;
    limit_conn addr 15;

    location / {
        proxy_pass http://transaction_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}

# AML Service
server {
    listen 80;
    server_name aml.qeep.local;

    access_log /var/log/nginx/aml-service.access.log main;
    error_log /var/log/nginx/aml-service.error.log warn;

    limit_req zone=general burst=15 nodelay;
    limit_conn addr 10;

    location / {
        proxy_pass http://aml_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}

# Customer Service
server {
    listen 80;
    server_name customer.qeep.local;

    access_log /var/log/nginx/customer-service.access.log main;
    error_log /var/log/nginx/customer-service.error.log warn;

    limit_req zone=general burst=15 nodelay;
    limit_conn addr 10;

    location / {
        proxy_pass http://customer_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}

# Surveillance Service
server {
    listen 80;
    server_name surveillance.qeep.local;

    access_log /var/log/nginx/surveillance-service.access.log main;
    error_log /var/log/nginx/surveillance-service.error.log warn;

    limit_req zone=general burst=15 nodelay;
    limit_conn addr 10;

    location / {
        proxy_pass http://surveillance_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}

# Integration Service
server {
    listen 80;
    server_name integration.qeep.local;

    access_log /var/log/nginx/integration-service.access.log main;
    error_log /var/log/nginx/integration-service.error.log warn;

    limit_req zone=general burst=15 nodelay;
    limit_conn addr 10;

    location / {
        proxy_pass http://integration_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}

# Monitoring Service
server {
    listen 80;
    server_name monitoring.qeep.local;

    access_log /var/log/nginx/monitoring-service.access.log main;
    error_log /var/log/nginx/monitoring-service.error.log warn;

    limit_req zone=general burst=15 nodelay;
    limit_conn addr 10;

    location / {
        proxy_pass http://monitoring_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
