# SSL Certificates for Qeep

This directory contains SSL certificates for the Qeep development environment.

## Development Setup

For development, you can generate self-signed certificates:

```bash
# Generate self-signed certificate for development
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout qeep-dev.key \
  -out qeep-dev.crt \
  -subj "/C=US/ST=State/L=City/O=Qeep/OU=Development/CN=qeep.local"

# Generate certificate with SAN for multiple domains
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout qeep-dev.key \
  -out qeep-dev.crt \
  -config <(
    echo '[dn]'
    echo 'CN=qeep.local'
    echo '[req]'
    echo 'distinguished_name = dn'
    echo '[SAN]'
    echo 'subjectAltName=DNS:qeep.local,DNS:api.qeep.local,DNS:auth.qeep.local,DNS:user.qeep.local,DNS:tenant.qeep.local,DNS:notification.qeep.local,DNS:audit.qeep.local,DNS:grafana.qeep.local,DNS:prometheus.qeep.local,DNS:jaeger.qeep.local'
  )
```

## Production Setup

For production, use proper SSL certificates from a trusted CA like Let's Encrypt:

```bash
# Using certbot for Let's Encrypt
certbot certonly --webroot -w /var/www/html -d qeep.yourdomain.com
```

## File Structure

```
ssl/
├── README.md
├── qeep-dev.crt      # Development certificate
├── qeep-dev.key      # Development private key
├── qeep-prod.crt     # Production certificate (not in repo)
├── qeep-prod.key     # Production private key (not in repo)
└── dhparam.pem          # Diffie-Hellman parameters
```

## Security Notes

- Never commit production certificates to version control
- Use proper certificate management in production
- Regularly rotate certificates
- Use strong Diffie-Hellman parameters (2048-bit minimum)

## Generate DH Parameters

```bash
openssl dhparam -out dhparam.pem 2048
```
