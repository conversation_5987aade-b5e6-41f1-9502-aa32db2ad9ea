# Prometheus configuration for Qeep monitoring
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Qeep Services (running locally)
  - job_name: 'qeep-nginx-proxy'
    static_configs:
      - targets: ['host.docker.internal:3000']
    metrics_path: '/api/telemetry/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  - job_name: 'qeep-auth-service'
    static_configs:
      - targets: ['host.docker.internal:3001']
    metrics_path: '/api/telemetry/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  - job_name: 'qeep-user-service'
    static_configs:
      - targets: ['host.docker.internal:3002']
    metrics_path: '/api/telemetry/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  - job_name: 'qeep-tenant-service'
    static_configs:
      - targets: ['host.docker.internal:3003']
    metrics_path: '/api/telemetry/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  - job_name: 'qeep-notification-service'
    static_configs:
      - targets: ['host.docker.internal:3004']
    metrics_path: '/api/telemetry/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  - job_name: 'qeep-audit-service'
    static_configs:
      - targets: ['host.docker.internal:3005']
    metrics_path: '/api/telemetry/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  # Infrastructure services
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s

  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  - job_name: 'nginx-exporter'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s

  # Node exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

# Alerting rules (optional)
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093
# Remote write configuration (optional - for long-term storage)
# remote_write:
#   - url: "http://remote-storage:9201/write"
