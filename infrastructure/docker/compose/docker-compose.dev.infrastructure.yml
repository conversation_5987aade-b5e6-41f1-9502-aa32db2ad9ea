# Qeep Development Environment - Infrastructure Only
# This file runs only infrastructure services in Docker containers
# NestJS application services should be run locally for NX hot reload support

services:
  # =============================================================================
  # INFRASTRUCTURE SERVICES
  # =============================================================================

  # Mailpit - Email Testing & Debugging
  mailpit:
    image: axllent/mailpit:latest
    container_name: qeep-mailpit-dev
    environment:
      MP_SMTP_AUTH_ACCEPT_ANY: 1
      MP_SMTP_AUTH_ALLOW_INSECURE: 1
      MP_MAX_MESSAGES: 5000
      MP_DATABASE: /data/mailpit.db
      MP_SMTP_SSL_CERT: ''
      MP_SMTP_SSL_KEY: ''
      MP_VERBOSE: 0
    ports:
      - '${MAILPIT_WEB_PORT:-8026}:8025' # Web UI
      - '${MAILPIT_SMTP_PORT:-1026}:1025' # SMTP server
    volumes:
      - mailpit_data_dev:/data
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:8025/api/v1/info']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - qeep-network
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:1.25-alpine
    container_name: qeep-nginx-dev
    ports:
      - '${NGINX_PORT:-8080}:80'
      - '${NGINX_HTTPS_PORT:-8443}:443'
    volumes:
      - ../nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ../nginx/conf.d/upstreams.conf:/etc/nginx/conf.d/upstreams.conf:ro
      - ../nginx/conf.d/api-gateway.conf:/etc/nginx/conf.d/api-gateway.conf:ro
      - ../nginx/conf.d/qeep-api.conf:/etc/nginx/conf.d/qeep-api.conf:ro
      - ../nginx/conf.d/services.conf:/etc/nginx/conf.d/services.conf:ro
      - ../nginx/conf.d/monitoring.conf:/etc/nginx/conf.d/monitoring.conf:ro
      - ../nginx/conf.d/default.conf:/etc/nginx/conf.d/default.conf:ro
      - ../nginx/ssl:/etc/nginx/ssl:ro
      - ../nginx/logs:/var/log/nginx
    depends_on:
      - postgres
      - redis
    networks:
      - qeep-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'nginx', '-t']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  postgres:
    image: postgres:15-alpine
    container_name: qeep-postgres-dev
    environment:
      POSTGRES_DB: qeep_dev
      POSTGRES_USER: ${POSTGRES_ROOT_USER:-qeep_dev}
      POSTGRES_PASSWORD: ${POSTGRES_ROOT_PASSWORD:-qeep_password_dev}
      POSTGRES_INITDB_ARGS: '--encoding=UTF8 --locale=C'
    ports:
      - '${POSTGRES_PORT:-5433}:5432'
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U ${POSTGRES_ROOT_USER:-qeep_dev} -d qeep_dev']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - qeep-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: qeep-redis-dev
    command: redis-server --requirepass "${REDIS_PASSWORD:-redis_dev_password}"
    ports:
      - '${REDIS_PORT:-6379}:6379'
    volumes:
      - redis_data_dev:/data
    healthcheck:
      test: ['CMD', 'redis-cli', '-a', '${REDIS_PASSWORD:-redis_dev_password}', 'ping']
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - qeep-network
    restart: unless-stopped

  # =============================================================================
  # MESSAGE BROKER (Optional - for future use)
  # =============================================================================

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: qeep-kafka-dev
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    ports:
      - '9092:9092'
    depends_on:
      - zookeeper
    networks:
      - qeep-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'kafka-topics', '--bootstrap-server', 'localhost:9092', '--list']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: qeep-zookeeper-dev
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - '2181:2181'
    networks:
      - qeep-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'echo', 'ruok', '|', 'nc', 'localhost', '2181']
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # MONITORING & OBSERVABILITY (Optional)
  # =============================================================================

  prometheus:
    image: prom/prometheus:latest
    container_name: qeep-prometheus-dev
    ports:
      - '${PROMETHEUS_PORT:-9090}:9090'
    volumes:
      - prometheus_data_dev:/prometheus
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - qeep-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: qeep-grafana-dev
    ports:
      - '${GRAFANA_PORT:-3001}:3000'
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_ADMIN_PASSWORD:-admin123}
      GF_SECURITY_ADMIN_USER: admin
      GF_USERS_ALLOW_SIGN_UP: false
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
      GF_FEATURE_TOGGLES_ENABLE: publicDashboards
    volumes:
      - grafana_data_dev:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
    networks:
      - qeep-network
    restart: unless-stopped
    depends_on:
      - prometheus

  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: qeep-jaeger-dev
    ports:
      - '16686:16686'
      - '14268:14268'
    environment:
      COLLECTOR_OTLP_ENABLED: true
    networks:
      - qeep-network
    restart: unless-stopped

# =============================================================================
# VOLUMES
# =============================================================================

volumes:
  postgres_data_dev:
    driver: local
  redis_data_dev:
    driver: local
  mailpit_data_dev:
    driver: local
  prometheus_data_dev:
    driver: local
  grafana_data_dev:
    driver: local

# =============================================================================
# NETWORKS
# =============================================================================

networks:
  qeep-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
