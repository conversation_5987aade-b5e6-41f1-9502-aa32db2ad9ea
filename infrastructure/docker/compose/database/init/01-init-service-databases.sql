-- =============================================================================
-- Qeep Service Database Initialization Script
-- =============================================================================
-- This script creates separate databases and users for each service
-- to improve service isolation and security
-- 
-- Execution: This script runs automatically when PostgreSQL container starts
-- for the first time via Docker's /docker-entrypoint-initdb.d/ mechanism
-- =============================================================================

-- Enable UUID extension for all databases
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================================================
-- AUTH SERVICE DATABASE
-- =============================================================================

-- Create auth service database
CREATE DATABASE qeep_auth
    WITH 
    OWNER = qeep_dev
    ENCODING = 'UTF8'
    LC_COLLATE = 'C'
    LC_CTYPE = 'C'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- Create auth service user
CREATE USER qeep_auth_user WITH PASSWORD 'qeep_auth_password';

-- Grant privileges to auth service user
GRANT ALL PRIVILEGES ON DATABASE qeep_auth TO qeep_auth_user;

-- Connect to auth database and set up permissions
\c qeep_auth;

-- Enable UUID extension in auth database
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Grant schema permissions
GRANT USAGE ON SCHEMA public TO qeep_auth_user;
GRANT CREATE ON SCHEMA public TO qeep_auth_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO qeep_auth_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO qeep_auth_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO qeep_auth_user;

-- Grant default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO qeep_auth_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO qeep_auth_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO qeep_auth_user;

-- =============================================================================
-- USER SERVICE DATABASE
-- =============================================================================

-- Connect back to postgres database to create user database
\c postgres;

-- Create user service database
CREATE DATABASE qeep_user
    WITH 
    OWNER = qeep_dev
    ENCODING = 'UTF8'
    LC_COLLATE = 'C'
    LC_CTYPE = 'C'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- Create user service user
CREATE USER qeep_user_user WITH PASSWORD 'qeep_user_password';

-- Grant privileges to user service user
GRANT ALL PRIVILEGES ON DATABASE qeep_user TO qeep_user_user;

-- Connect to user database and set up permissions
\c qeep_user;

-- Enable UUID extension in user database
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Grant schema permissions
GRANT USAGE ON SCHEMA public TO qeep_user_user;
GRANT CREATE ON SCHEMA public TO qeep_user_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO qeep_user_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO qeep_user_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO qeep_user_user;

-- Grant default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO qeep_user_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO qeep_user_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO qeep_user_user;

-- =============================================================================
-- TENANT SERVICE DATABASE
-- =============================================================================

-- Connect back to postgres database to create tenant database
\c postgres;

-- Create tenant service database
CREATE DATABASE qeep_tenant
    WITH 
    OWNER = qeep_dev
    ENCODING = 'UTF8'
    LC_COLLATE = 'C'
    LC_CTYPE = 'C'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- Create tenant service user
CREATE USER qeep_tenant_user WITH PASSWORD 'qeep_tenant_password';

-- Grant privileges to tenant service user
GRANT ALL PRIVILEGES ON DATABASE qeep_tenant TO qeep_tenant_user;

-- Connect to tenant database and set up permissions
\c qeep_tenant;

-- Enable UUID extension in tenant database
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Grant schema permissions
GRANT USAGE ON SCHEMA public TO qeep_tenant_user;
GRANT CREATE ON SCHEMA public TO qeep_tenant_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO qeep_tenant_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO qeep_tenant_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO qeep_tenant_user;

-- Grant default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO qeep_tenant_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO qeep_tenant_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO qeep_tenant_user;

-- =============================================================================
-- NOTIFICATION SERVICE DATABASE
-- =============================================================================

-- Connect back to postgres database to create notification database
\c postgres;

-- Create notification service database
CREATE DATABASE qeep_notification
    WITH 
    OWNER = qeep_dev
    ENCODING = 'UTF8'
    LC_COLLATE = 'C'
    LC_CTYPE = 'C'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- Create notification service user
CREATE USER qeep_notification_user WITH PASSWORD 'qeep_notification_password';

-- Grant privileges to notification service user
GRANT ALL PRIVILEGES ON DATABASE qeep_notification TO qeep_notification_user;

-- Connect to notification database and set up permissions
\c qeep_notification;

-- Enable UUID extension in notification database
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Grant schema permissions
GRANT USAGE ON SCHEMA public TO qeep_notification_user;
GRANT CREATE ON SCHEMA public TO qeep_notification_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO qeep_notification_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO qeep_notification_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO qeep_notification_user;

-- Grant default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO qeep_notification_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO qeep_notification_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO qeep_notification_user;

-- =============================================================================
-- AUDIT SERVICE DATABASE
-- =============================================================================

-- Connect back to postgres database to create audit database
\c postgres;

-- Create audit service database
CREATE DATABASE qeep_audit
    WITH 
    OWNER = qeep_dev
    ENCODING = 'UTF8'
    LC_COLLATE = 'C'
    LC_CTYPE = 'C'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- Create audit service user
CREATE USER qeep_audit_user WITH PASSWORD 'qeep_audit_password';

-- Grant privileges to audit service user
GRANT ALL PRIVILEGES ON DATABASE qeep_audit TO qeep_audit_user;

-- Connect to audit database and set up permissions
\c qeep_audit;

-- Enable UUID extension in audit database
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Grant schema permissions
GRANT USAGE ON SCHEMA public TO qeep_audit_user;
GRANT CREATE ON SCHEMA public TO qeep_audit_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO qeep_audit_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO qeep_audit_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO qeep_audit_user;

-- Grant default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO qeep_audit_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO qeep_audit_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO qeep_audit_user;

-- =============================================================================
-- TRANSACTION MONITORING SERVICES DATABASES
-- =============================================================================

-- =============================================================================
-- TRANSACTION SERVICE DATABASE
-- =============================================================================

-- Connect back to postgres database to create transaction database
\c postgres;

-- Create transaction service database
CREATE DATABASE qeep_transaction
    WITH
    OWNER = qeep_dev
    ENCODING = 'UTF8'
    LC_COLLATE = 'C'
    LC_CTYPE = 'C'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- Create transaction service user
CREATE USER qeep_transaction_user WITH PASSWORD 'qeep_transaction_password';

-- Grant privileges to transaction service user
GRANT ALL PRIVILEGES ON DATABASE qeep_transaction TO qeep_transaction_user;

-- Connect to transaction database and set up permissions
\c qeep_transaction;

-- Enable UUID extension in transaction database
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Grant schema permissions
GRANT USAGE ON SCHEMA public TO qeep_transaction_user;
GRANT CREATE ON SCHEMA public TO qeep_transaction_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO qeep_transaction_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO qeep_transaction_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO qeep_transaction_user;

-- Grant default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO qeep_transaction_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO qeep_transaction_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO qeep_transaction_user;

-- =============================================================================
-- AML SERVICE DATABASE
-- =============================================================================

-- Connect back to postgres database to create aml database
\c postgres;

-- Create aml service database
CREATE DATABASE qeep_aml
    WITH
    OWNER = qeep_dev
    ENCODING = 'UTF8'
    LC_COLLATE = 'C'
    LC_CTYPE = 'C'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- Create aml service user
CREATE USER qeep_aml_user WITH PASSWORD 'qeep_aml_password';

-- Grant privileges to aml service user
GRANT ALL PRIVILEGES ON DATABASE qeep_aml TO qeep_aml_user;

-- Connect to aml database and set up permissions
\c qeep_aml;

-- Enable UUID extension in aml database
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Grant schema permissions
GRANT USAGE ON SCHEMA public TO qeep_aml_user;
GRANT CREATE ON SCHEMA public TO qeep_aml_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO qeep_aml_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO qeep_aml_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO qeep_aml_user;

-- Grant default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO qeep_aml_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO qeep_aml_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO qeep_aml_user;

-- =============================================================================
-- CUSTOMER SERVICE DATABASE
-- =============================================================================

-- Connect back to postgres database to create customer database
\c postgres;

-- Create customer service database
CREATE DATABASE qeep_customer
    WITH
    OWNER = qeep_dev
    ENCODING = 'UTF8'
    LC_COLLATE = 'C'
    LC_CTYPE = 'C'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- Create customer service user
CREATE USER qeep_customer_user WITH PASSWORD 'qeep_customer_password';

-- Grant privileges to customer service user
GRANT ALL PRIVILEGES ON DATABASE qeep_customer TO qeep_customer_user;

-- Connect to customer database and set up permissions
\c qeep_customer;

-- Enable UUID extension in customer database
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Grant schema permissions
GRANT USAGE ON SCHEMA public TO qeep_customer_user;
GRANT CREATE ON SCHEMA public TO qeep_customer_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO qeep_customer_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO qeep_customer_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO qeep_customer_user;

-- Grant default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO qeep_customer_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO qeep_customer_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO qeep_customer_user;

-- =============================================================================
-- SURVEILLANCE SERVICE DATABASE
-- =============================================================================

-- Connect back to postgres database to create surveillance database
\c postgres;

-- Create surveillance service database
CREATE DATABASE qeep_surveillance
    WITH
    OWNER = qeep_dev
    ENCODING = 'UTF8'
    LC_COLLATE = 'C'
    LC_CTYPE = 'C'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- Create surveillance service user
CREATE USER qeep_surveillance_user WITH PASSWORD 'qeep_surveillance_password';

-- Grant privileges to surveillance service user
GRANT ALL PRIVILEGES ON DATABASE qeep_surveillance TO qeep_surveillance_user;

-- Connect to surveillance database and set up permissions
\c qeep_surveillance;

-- Enable UUID extension in surveillance database
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Grant schema permissions
GRANT USAGE ON SCHEMA public TO qeep_surveillance_user;
GRANT CREATE ON SCHEMA public TO qeep_surveillance_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO qeep_surveillance_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO qeep_surveillance_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO qeep_surveillance_user;

-- Grant default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO qeep_surveillance_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO qeep_surveillance_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO qeep_surveillance_user;

-- =============================================================================
-- INTEGRATION SERVICE DATABASE
-- =============================================================================

-- Connect back to postgres database to create integration database
\c postgres;

-- Create integration service database
CREATE DATABASE qeep_integration
    WITH
    OWNER = qeep_dev
    ENCODING = 'UTF8'
    LC_COLLATE = 'C'
    LC_CTYPE = 'C'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- Create integration service user
CREATE USER qeep_integration_user WITH PASSWORD 'qeep_integration_password';

-- Grant privileges to integration service user
GRANT ALL PRIVILEGES ON DATABASE qeep_integration TO qeep_integration_user;

-- Connect to integration database and set up permissions
\c qeep_integration;

-- Enable UUID extension in integration database
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Grant schema permissions
GRANT USAGE ON SCHEMA public TO qeep_integration_user;
GRANT CREATE ON SCHEMA public TO qeep_integration_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO qeep_integration_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO qeep_integration_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO qeep_integration_user;

-- Grant default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO qeep_integration_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO qeep_integration_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO qeep_integration_user;

-- =============================================================================
-- MONITORING SERVICE DATABASE
-- =============================================================================

-- Connect back to postgres database to create monitoring database
\c postgres;

-- Create monitoring service database
CREATE DATABASE qeep_monitoring
    WITH
    OWNER = qeep_dev
    ENCODING = 'UTF8'
    LC_COLLATE = 'C'
    LC_CTYPE = 'C'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- Create monitoring service user
CREATE USER qeep_monitoring_user WITH PASSWORD 'qeep_monitoring_password';

-- Grant privileges to monitoring service user
GRANT ALL PRIVILEGES ON DATABASE qeep_monitoring TO qeep_monitoring_user;

-- Connect to monitoring database and set up permissions
\c qeep_monitoring;

-- Enable UUID extension in monitoring database
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Grant schema permissions
GRANT USAGE ON SCHEMA public TO qeep_monitoring_user;
GRANT CREATE ON SCHEMA public TO qeep_monitoring_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO qeep_monitoring_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO qeep_monitoring_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO qeep_monitoring_user;

-- Grant default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO qeep_monitoring_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO qeep_monitoring_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO qeep_monitoring_user;

-- =============================================================================
-- SUMMARY
-- =============================================================================

-- Connect back to postgres database for summary
\c postgres;

-- Display created databases and users
\echo '=============================================================================';
\echo 'Database Initialization Complete';
\echo '=============================================================================';
\echo 'Created Core Service Databases:';
\echo '  - qeep_auth (for auth-service)';
\echo '  - qeep_user (for user-service)';
\echo '  - qeep_tenant (for tenant-service)';
\echo '  - qeep_notification (for notification-service)';
\echo '  - qeep_audit (for audit-service)';
\echo '';
\echo 'Created Transaction Monitoring Databases:';
\echo '  - qeep_transaction (for transaction-service)';
\echo '  - qeep_aml (for aml-service)';
\echo '  - qeep_customer (for customer-service)';
\echo '  - qeep_surveillance (for surveillance-service)';
\echo '  - qeep_integration (for integration-service)';
\echo '  - qeep_monitoring (for monitoring-service)';
\echo '';
\echo 'Created Core Service Users:';
\echo '  - qeep_auth_user (for qeep_auth database)';
\echo '  - qeep_user_user (for qeep_user database)';
\echo '  - qeep_tenant_user (for qeep_tenant database)';
\echo '  - qeep_notification_user (for qeep_notification database)';
\echo '  - qeep_audit_user (for qeep_audit database)';
\echo '';
\echo 'Created Transaction Monitoring Users:';
\echo '  - qeep_transaction_user (for qeep_transaction database)';
\echo '  - qeep_aml_user (for qeep_aml database)';
\echo '  - qeep_customer_user (for qeep_customer database)';
\echo '  - qeep_surveillance_user (for qeep_surveillance database)';
\echo '  - qeep_integration_user (for qeep_integration database)';
\echo '  - qeep_monitoring_user (for qeep_monitoring database)';
\echo '';
\echo 'All databases use the public schema with full permissions for their respective users.';
\echo '=============================================================================';
