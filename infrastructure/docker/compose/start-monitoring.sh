#!/bin/bash

# Start Qeep Monitoring Stack
# This script starts <PERSON>met<PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> for monitoring Qeep services

set -e

echo "🚀 Starting Qeep Monitoring Stack..."

# Change to the compose directory
cd "$(dirname "$0")"

# Start the monitoring services
echo "📊 Starting Prometheus..."
docker-compose -f docker-compose.dev.infrastructure.yml up -d prometheus

echo "📈 Starting Grafana..."
docker-compose -f docker-compose.dev.infrastructure.yml up -d grafana

echo "🔍 Starting Jaeger..."
docker-compose -f docker-compose.dev.infrastructure.yml up -d jaeger

# Wait a moment for services to start
echo "⏳ Waiting for services to start..."
sleep 10

# Check service status
echo "🔍 Checking service status..."
docker-compose -f docker-compose.dev.infrastructure.yml ps prometheus grafana jaeger

echo ""
echo "✅ Monitoring stack started successfully!"
echo ""
echo "📊 Access URLs:"
echo "  - Prometheus: http://localhost:9090"
echo "  - <PERSON><PERSON>:    http://localhost:3001 (admin/admin123)"
echo "  - Jaeger:     http://localhost:16686"
echo ""
echo "📈 Grafana Dashboards:"
echo "  - Qeep Overview: http://localhost:3001/d/qeep-overview"
echo ""
echo "🔧 To stop the monitoring stack:"
echo "  docker-compose -f docker-compose.dev.infrastructure.yml down prometheus grafana jaeger"
echo ""
echo "📝 Note: Make sure your Qeep services are running and exposing /telemetry/metrics endpoints"
