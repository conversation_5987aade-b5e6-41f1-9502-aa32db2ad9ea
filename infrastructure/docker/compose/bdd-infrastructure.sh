#!/bin/bash

# =============================================================================
# BDD Test Infrastructure Management Script
# =============================================================================
# This script manages the complete BDD test infrastructure including
# database, Redis, and mock services

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
COMPOSE_FILE="$SCRIPT_DIR/docker-compose.bdd.infrastructure.yml"
PROJECT_NAME="qeep-bdd"

# Service discovery and health checking
SERVICE_DISCOVERY_DIR="$SCRIPT_DIR/bdd/service-discovery"
HEALTH_CHECKER="$SERVICE_DISCOVERY_DIR/health-checker.sh"
STARTUP_ORCHESTRATOR="$SERVICE_DISCOVERY_DIR/startup-orchestrator.sh"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "\n${PURPLE}=== $1 ===${NC}\n"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to check if Docker Compose is available
check_docker_compose() {
    if ! command -v docker-compose > /dev/null 2>&1; then
        log_error "Docker Compose is not installed. Please install Docker Compose and try again."
        exit 1
    fi
}

# Function to start BDD infrastructure
start_infrastructure() {
    log_header "Starting BDD Test Infrastructure"
    
    log_info "Starting core infrastructure services..."
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" up -d postgres redis
    
    log_info "Waiting for services to be healthy..."

    # Use service health checker if available
    if [[ -x "$HEALTH_CHECKER" ]]; then
        log_info "Using advanced health checking..."
        "$HEALTH_CHECKER" wait 120
    else
        # Fallback to basic health checks
        docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" exec postgres pg_isready -U qeep_bdd -d qeep_bdd
        docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" exec redis redis-cli -a qeep_bdd_redis_password ping
    fi
    
    log_success "BDD infrastructure started successfully"
    show_status
}

# Function to start with mock services
start_with_mocks() {
    log_header "Starting BDD Infrastructure with Mock Services"
    
    log_info "Starting infrastructure and mock services..."
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" --profile mocks --profile proxy up -d
    
    log_info "Waiting for services to be healthy..."
    sleep 10
    
    log_success "BDD infrastructure with mocks started successfully"
    show_status
}

# Function to stop infrastructure
stop_infrastructure() {
    log_header "Stopping BDD Test Infrastructure"
    
    log_info "Stopping all BDD services..."
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" down
    
    log_success "BDD infrastructure stopped"
}

# Function to restart infrastructure
restart_infrastructure() {
    log_header "Restarting BDD Test Infrastructure"
    
    stop_infrastructure
    sleep 2
    start_infrastructure
}

# Function to cleanup infrastructure
cleanup_infrastructure() {
    log_header "Cleaning Up BDD Test Infrastructure"
    
    log_warning "This will remove all containers, volumes, and networks!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Cleanup cancelled"
        return 0
    fi
    
    log_info "Stopping and removing all services..."
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" down -v --remove-orphans
    
    log_info "Removing project-specific volumes..."
    docker volume ls -q | grep "^${PROJECT_NAME}_" | xargs -r docker volume rm
    
    log_info "Removing project-specific networks..."
    docker network ls -q | grep "^${PROJECT_NAME}_" | xargs -r docker network rm
    
    log_success "BDD infrastructure cleaned up"
}

# Function to show infrastructure status
show_status() {
    log_header "BDD Infrastructure Status"
    
    echo -e "${BLUE}Services:${NC}"
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" ps

    echo -e "\n${BLUE}Health Checks:${NC}"

    # Use advanced health checker if available
    if [[ -x "$HEALTH_CHECKER" ]]; then
        "$HEALTH_CHECKER" check
        return
    fi
    
    # Check PostgreSQL
    if docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" exec -T postgres pg_isready -U qeep_bdd -d qeep_bdd > /dev/null 2>&1; then
        echo -e "PostgreSQL: ${GREEN}✓ Healthy${NC}"
    else
        echo -e "PostgreSQL: ${RED}✗ Unhealthy${NC}"
    fi

    # Check Redis
    if docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" exec -T redis redis-cli -a qeep_bdd_redis_password ping > /dev/null 2>&1; then
        echo -e "Redis: ${GREEN}✓ Healthy${NC}"
    else
        echo -e "Redis: ${RED}✗ Unhealthy${NC}"
    fi

    # Check NGINX (if running)
    if docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" ps nginx | grep -q "Up"; then
        echo -e "NGINX: ${GREEN}✓ Running${NC}"
    else
        echo -e "NGINX: ${YELLOW}○ Not running${NC}"
    fi

    # Check MailHog (if running)
    if docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" ps mailhog | grep -q "Up"; then
        echo -e "MailHog: ${GREEN}✓ Running${NC}"
    else
        echo -e "MailHog: ${YELLOW}○ Not running${NC}"
    fi
    
    echo -e "\n${BLUE}Connection Information:${NC}"
    echo "PostgreSQL: localhost:5434 (qeep_bdd/qeep_bdd_password/qeep_bdd)"
    echo "Redis: localhost:6381 (password: qeep_bdd_redis_password)"
    echo "NGINX Proxy: http://localhost:8090 (if running)"
    echo "MailHog Web UI: http://localhost:8025 (if running)"
}

# Function to run database cleanup
cleanup_database() {
    log_header "Database Cleanup"
    
    local cleanup_type="${1:-scenarios}"
    
    if ! docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" ps postgres | grep -q "Up"; then
        log_error "PostgreSQL is not running. Start infrastructure first."
        return 1
    fi

    log_info "Running database cleanup: $cleanup_type"
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" exec postgres /opt/scripts/cleanup-database.sh "$cleanup_type"
}

# Function to run Redis cleanup
cleanup_redis() {
    log_header "Redis Cleanup"
    
    local cleanup_type="${1:-bdd}"
    
    if ! docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" ps redis | grep -q "Up"; then
        log_error "Redis is not running. Start infrastructure first."
        return 1
    fi

    log_info "Running Redis cleanup: $cleanup_type"
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" exec redis /scripts/cleanup-redis.sh "$cleanup_type"
}

# Function to run BDD tests
run_tests() {
    log_header "Running BDD Tests"
    
    # Check if infrastructure is running
    if ! docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" ps postgres | grep -q "Up"; then
        log_info "Infrastructure is not running. Starting infrastructure first..."
        start_infrastructure
    fi

    log_info "Running BDD test suite..."
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" --profile test run --rm test-runner
}

# Function to open database shell
db_shell() {
    log_header "Opening Database Shell"
    
    if ! docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" ps postgres | grep -q "Up"; then
        log_error "PostgreSQL is not running. Start infrastructure first."
        return 1
    fi

    log_info "Opening PostgreSQL shell..."
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" exec postgres psql -U qeep_bdd -d qeep_bdd
}

# Function to open Redis shell
redis_shell() {
    log_header "Opening Redis Shell"
    
    if ! docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" ps redis | grep -q "Up"; then
        log_error "Redis is not running. Start infrastructure first."
        return 1
    fi

    log_info "Opening Redis shell..."
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" exec redis redis-cli -a qeep_bdd_redis_password
}

# Function to check service health
check_service_health() {
    local service="${1:-}"

    log_header "Service Health Check"

    if [[ -x "$HEALTH_CHECKER" ]]; then
        if [[ -n "$service" ]]; then
            "$HEALTH_CHECKER" check "$service"
        else
            "$HEALTH_CHECKER" check
        fi
    else
        log_warning "Advanced health checker not available, using basic checks"
        show_status
    fi
}

# Function to show startup plan
show_startup_plan() {
    log_header "Service Startup Plan"

    if [[ -x "$STARTUP_ORCHESTRATOR" ]]; then
        "$STARTUP_ORCHESTRATOR" plan
    else
        log_warning "Startup orchestrator not available"
        echo "Services will start in Docker Compose dependency order"
    fi
}

# Function to validate service configuration
validate_services() {
    log_header "Service Configuration Validation"

    if [[ -x "$STARTUP_ORCHESTRATOR" ]]; then
        "$STARTUP_ORCHESTRATOR" validate
    else
        log_warning "Startup orchestrator not available for validation"
        return 0
    fi
}

# Function to show logs
show_logs() {
    local service="${1:-}"

    if [ -n "$service" ]; then
        log_info "Showing logs for $service..."
        docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" logs -f "$service"
    else
        log_info "Showing logs for all services..."
        docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" logs -f
    fi
}

# Function to show help
show_help() {
    echo "BDD Test Infrastructure Management Script"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Infrastructure Commands:"
    echo "  start        - Start core BDD infrastructure (PostgreSQL, Redis)"
    echo "  start-mocks  - Start infrastructure with mock services (MailHog)"
    echo "  stop         - Stop all BDD infrastructure"
    echo "  restart      - Restart BDD infrastructure"
    echo "  cleanup      - Remove all containers, volumes, and networks"
    echo "  status       - Show infrastructure status"
    echo ""
    echo "Data Management Commands:"
    echo "  db-cleanup [TYPE]    - Cleanup database (scenarios|truncate|sequences|vacuum|full)"
    echo "  redis-cleanup [TYPE] - Cleanup Redis (bdd|tokens|sessions|ratelimits|full)"
    echo ""
    echo "Testing Commands:"
    echo "  test         - Run BDD test suite"
    echo ""
    echo "Service Management Commands:"
    echo "  health [SERVICE]     - Check service health (all or specific service)"
    echo "  plan                 - Show service startup plan"
    echo "  validate             - Validate service configuration"
    echo ""
    echo "Utility Commands:"
    echo "  db-shell     - Open PostgreSQL shell"
    echo "  redis-shell  - Open Redis shell"
    echo "  logs [SERVICE] - Show logs (all services or specific service)"
    echo "  help         - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start                    # Start infrastructure"
    echo "  $0 db-cleanup scenarios     # Cleanup test scenarios"
    echo "  $0 redis-cleanup bdd        # Cleanup BDD Redis keys"
    echo "  $0 test                     # Run BDD tests"
    echo "  $0 logs postgres-bdd        # Show PostgreSQL logs"
}

# Main execution
main() {
    local command="${1:-help}"
    local option="${2:-}"
    
    # Check prerequisites
    check_docker
    check_docker_compose
    
    case "$command" in
        "start")
            start_infrastructure
            ;;
        "start-mocks")
            start_with_mocks
            ;;
        "stop")
            stop_infrastructure
            ;;
        "restart")
            restart_infrastructure
            ;;
        "cleanup")
            cleanup_infrastructure
            ;;
        "status")
            show_status
            ;;
        "db-cleanup")
            cleanup_database "$option"
            ;;
        "redis-cleanup")
            cleanup_redis "$option"
            ;;
        "test")
            run_tests
            ;;
        "health")
            check_service_health "$option"
            ;;
        "plan")
            show_startup_plan
            ;;
        "validate")
            validate_services
            ;;
        "db-shell")
            db_shell
            ;;
        "redis-shell")
            redis_shell
            ;;
        "logs")
            show_logs "$option"
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Execute main function with all arguments
main "$@"
