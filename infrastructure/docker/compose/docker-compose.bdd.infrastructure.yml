# Docker Compose file for BDD Test Infrastructure

services:
  # =============================================================================
  # BDD TEST INFRASTRUCTURE SERVICES
  # =============================================================================

  postgres:
    image: postgres:15-alpine
    container_name: qeep-postgres
    environment:
      POSTGRES_DB: qeep_bdd
      POSTGRES_USER: qeep_bdd
      POSTGRES_PASSWORD: qeep_bdd_password
      POSTGRES_INITDB_ARGS: '--encoding=UTF8 --locale=C'
    ports:
      - '5434:5432'
    volumes:
      - postgres_data_bdd:/var/lib/postgresql/data
      - ./bdd/database/init:/docker-entrypoint-initdb.d
      - ./bdd/database/scripts:/opt/scripts
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U qeep_bdd -d qeep_bdd']
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - qeep-bdd-network
    restart: unless-stopped
    command: >
      postgres
      -c log_statement=none
      -c log_min_duration_statement=-1
      -c log_checkpoints=off
      -c log_connections=off
      -c log_disconnections=off
      -c log_lock_waits=off
      -c shared_preload_libraries=''

  redis:
    image: redis:7-alpine
    container_name: qeep-redis
    command: >
      redis-server
      --requirepass qeep_bdd_redis_password
      --appendonly no
      --save ""
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --databases 16
    ports:
      - '6381:6379'
    networks:
      - qeep-bdd-network
    healthcheck:
      test: ['CMD', 'redis-cli', '-a', 'qeep_bdd_redis_password', 'ping']
      interval: 10s
      timeout: 5s
      retries: 3
    restart: unless-stopped
    tmpfs:
      - /data

  # =============================================================================
  # BDD TEST UTILITIES
  # =============================================================================

  db-manager:
    image: postgres:15-alpine
    container_name: qeep-db-manager
    environment:
      PGHOST: postgres
      PGPORT: 5432
      PGUSER: qeep_bdd
      PGPASSWORD: qeep_bdd_password
      PGDATABASE: qeep_bdd
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - qeep-bdd-network
    volumes:
      - ./bdd/database/scripts:/scripts
      - ../../../:/workspace
    working_dir: /scripts
    profiles:
      - tools
    command: tail -f /dev/null

  redis-manager:
    image: redis:7-alpine
    container_name: qeep-redis-manager
    environment:
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: redis_dev_password
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - qeep-bdd-network
    volumes:
      - ./bdd/redis/scripts:/scripts
    working_dir: /scripts
    profiles:
      - tools
    command: tail -f /dev/null

  # =============================================================================
  # MOCK SERVICES FOR BDD TESTS
  # =============================================================================

  mailhog:
    image: mailhog/mailhog:latest
    container_name: qeep-mailhog
    ports:
      - '1025:1025' # SMTP
      - '8025:8025' # Web UI
    networks:
      - qeep-bdd-network
    restart: unless-stopped
    profiles:
      - mocks

  # =============================================================================
  # NGINX PROXY
  # =============================================================================

  nginx:
    image: nginx:alpine
    container_name: qeep-nginx
    ports:
      - '8090:80'
      - '8453:443'
    volumes:
      - ./bdd/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./bdd/nginx/conf.d:/etc/nginx/conf.d:ro
    depends_on:
      - postgres
      - redis
    networks:
      - qeep-bdd-network
    restart: unless-stopped
    profiles:
      - proxy

  # =============================================================================
  # BDD TEST RUNNER
  # =============================================================================

  test-runner:
    image: node:18-alpine
    container_name: qeep-test-runner
    working_dir: /app
    environment:
      NODE_ENV: bdd
      # Load from .env.bdd file
    env_file:
      - ../../../.env.bdd
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - qeep-bdd-network
    volumes:
      - ../../../:/app
      - /app/node_modules
      - bdd_test_reports:/app/reports
    profiles:
      - test
    command: >
      sh -c "
        apk add --no-cache bash curl &&
        corepack enable pnpm &&
        echo 'Installing dependencies...' &&
        pnpm install --frozen-lockfile &&
        echo 'Waiting for services to be ready...' &&
        sleep 10 &&
        echo 'Running BDD tests...' &&
        pnpm run test:bdd
      "

# =============================================================================
# VOLUMES
# =============================================================================

volumes:
  postgres_data_bdd:
    driver: local
    driver_opts:
      type: tmpfs
      device: tmpfs
      o: size=1g,uid=999,gid=999

  bdd_test_reports:
    driver: local

# =============================================================================
# NETWORKS
# =============================================================================

networks:
  qeep-bdd-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
