# BDD Test Infrastructure

This directory contains the complete infrastructure setup for running BDD (Behavior Driven Development) tests in an isolated environment.

## 🏗️ Architecture

The BDD test infrastructure consists of:

- **PostgreSQL Database** (`qeep_bdd`) - Isolated test database with cleanup utilities
- **Redis Cache** - Separate Redis instance for test data
- **Mock Services** - MailHog for email testing
- **Management Scripts** - Automated cleanup and maintenance tools

## 🚀 Quick Start

### 1. Start Infrastructure

```bash
# Start core infrastructure (PostgreSQL + Redis)
./bdd-infrastructure.sh start

# Or start with mock services (includes MailHog)
./bdd-infrastructure.sh start-mocks
```

### 2. Run BDD Tests

```bash
# Run the complete BDD test suite
./bdd-infrastructure.sh test
```

### 3. Cleanup After Tests

```bash
# Cleanup test data
./bdd-infrastructure.sh db-cleanup scenarios
./bdd-infrastructure.sh redis-cleanup bdd
```

## 📋 Available Commands

### Infrastructure Management

```bash
./bdd-infrastructure.sh start        # Start core infrastructure
./bdd-infrastructure.sh start-mocks  # Start with mock services
./bdd-infrastructure.sh stop         # Stop all services
./bdd-infrastructure.sh restart      # Restart infrastructure
./bdd-infrastructure.sh cleanup      # Remove everything
./bdd-infrastructure.sh status       # Show status
```

### Data Management

```bash
# Database cleanup options
./bdd-infrastructure.sh db-cleanup scenarios   # Clean test scenarios
./bdd-infrastructure.sh db-cleanup truncate    # Truncate all tables
./bdd-infrastructure.sh db-cleanup sequences   # Reset sequences
./bdd-infrastructure.sh db-cleanup vacuum      # Vacuum database
./bdd-infrastructure.sh db-cleanup full        # Full cleanup

# Redis cleanup options
./bdd-infrastructure.sh redis-cleanup bdd         # Clean BDD keys
./bdd-infrastructure.sh redis-cleanup tokens      # Clean verification tokens
./bdd-infrastructure.sh redis-cleanup sessions    # Clean sessions
./bdd-infrastructure.sh redis-cleanup ratelimits  # Clean rate limits
./bdd-infrastructure.sh redis-cleanup full        # Full cleanup
```

### Utilities

```bash
./bdd-infrastructure.sh db-shell      # Open PostgreSQL shell
./bdd-infrastructure.sh redis-shell   # Open Redis shell
./bdd-infrastructure.sh logs          # Show all logs
./bdd-infrastructure.sh logs postgres-bdd  # Show specific service logs
```

## 🗄️ Database Features

### Test Scenario Tracking

The BDD database includes utilities to track and cleanup test scenarios:

```sql
-- Start a new test scenario
SELECT bdd_test.start_test_scenario('User Registration', 'Authentication');

-- Register test data for cleanup
SELECT bdd_test.register_test_data(
    scenario_id,
    'users',
    'public',
    'user-id-123',
    'user'
);

-- Cleanup scenario data
SELECT bdd_test.cleanup_test_scenario(scenario_id);

-- Cleanup all test data
SELECT bdd_test.cleanup_all_test_data();
```

### Database Schemas

- `public` - Default schema for shared data
- `auth_service` - Authentication service data
- `user_service` - User service data
- `tenant_service` - Tenant service data
- `notification_service` - Notification service data
- `audit_service` - Audit service data
- `bdd_test` - Test utilities and tracking

## 🔧 Configuration

### Environment Variables

The infrastructure uses the `.env.bdd` file for configuration:

```bash
# Database
POSTGRES_DB=qeep_bdd
POSTGRES_USER=qeep_dev
POSTGRES_PASSWORD=qeep_password_dev

# Redis
REDIS_DB=2
REDIS_PASSWORD=redis_dev_password

# Test settings
BDD_TEST_MODE=true
BDD_CLEANUP_AFTER_TESTS=true
```

### Service Ports

- **PostgreSQL**: `localhost:5434` (user: `qeep_bdd`, password: `qeep_bdd_password`, database: `qeep_bdd`)
- **Redis**: `localhost:6381` (password: `qeep_bdd_redis_password`)
- **MailHog SMTP**: `localhost:1025`
- **MailHog Web UI**: `http://localhost:8025`

## 🧪 Test Data Management

### Cleanup Strategies

1. **Scenario-based Cleanup** (Recommended)

   - Tracks data created during each test scenario
   - Automatically cleans up related data
   - Maintains referential integrity

2. **Table Truncation**

   - Fast but destructive
   - Removes all data from tables
   - Resets sequences

3. **Selective Cleanup**
   - Clean specific data types
   - Verification tokens, sessions, etc.

### Data Isolation

- Each test scenario gets a unique identifier
- Test data is tagged with scenario ID
- Cleanup removes only scenario-specific data
- Parallel test execution supported

## 🔍 Monitoring & Debugging

### Health Checks

```bash
# Check infrastructure status
./bdd-infrastructure.sh status

# Check database statistics
./bdd-infrastructure.sh db-cleanup stats

# Check Redis statistics
./bdd-infrastructure.sh redis-cleanup stats
```

### Logs

```bash
# View all logs
./bdd-infrastructure.sh logs

# View specific service logs
./bdd-infrastructure.sh logs postgres-bdd
./bdd-infrastructure.sh logs redis-bdd
./bdd-infrastructure.sh logs mailhog-bdd
```

### Direct Access

```bash
# PostgreSQL shell
./bdd-infrastructure.sh db-shell

# Redis shell
./bdd-infrastructure.sh redis-shell
```

## 🚨 Troubleshooting

### Common Issues

1. **Services won't start**

   ```bash
   # Check Docker is running
   docker info

   # Check port conflicts
   netstat -tulpn | grep -E ':(5434|6381|1025|8025)'
   ```

2. **Database connection issues**

   ```bash
   # Check PostgreSQL health
   docker-compose -f docker-compose.bdd.infrastructure.yml exec postgres-bdd pg_isready -U qeep_bdd -d qeep_bdd
   ```

3. **Redis connection issues**

   ```bash
   # Check Redis health
   docker-compose -f docker-compose.bdd.infrastructure.yml exec redis-bdd redis-cli -a qeep_bdd_redis_password ping
   ```

### Reset Everything

```bash
# Complete reset (removes all data)
./bdd-infrastructure.sh cleanup
./bdd-infrastructure.sh start
```

## 📁 Directory Structure

```
bdd/
├── README.md                    # This file
├── database/
│   ├── init/
│   │   └── 01-init-bdd-database.sql  # Database initialization
│   └── scripts/
│       └── cleanup-database.sh       # Database cleanup script
└── redis/
    └── scripts/
        └── cleanup-redis.sh          # Redis cleanup script
```

## 🔗 Integration

### With BDD Tests

The infrastructure is designed to work seamlessly with Cucumber/Jest BDD tests:

1. Tests use `.env.bdd` configuration
2. Database utilities track test scenarios
3. Cleanup happens automatically after tests
4. Mock services capture external interactions

### With CI/CD

```bash
# In CI pipeline
./bdd-infrastructure.sh start
./bdd-infrastructure.sh test
./bdd-infrastructure.sh cleanup
```

## 📝 Best Practices

1. **Always cleanup after tests**
2. **Use scenario-based cleanup for reliability**
3. **Monitor resource usage in CI**
4. **Keep test data minimal**
5. **Use mock services for external dependencies**
6. **Run tests in isolated environments**

## 🤝 Contributing

When adding new BDD tests:

1. Use the test scenario tracking utilities
2. Register created data for cleanup
3. Use appropriate cleanup strategies
4. Test cleanup procedures
5. Update documentation as needed
