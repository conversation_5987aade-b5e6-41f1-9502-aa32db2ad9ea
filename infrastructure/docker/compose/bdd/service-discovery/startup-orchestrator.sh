#!/bin/bash

# =============================================================================
# BDD Service Startup Orchestrator
# =============================================================================
# This script orchestrates the startup of services in the correct order
# based on dependencies defined in the service registry

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVICE_REGISTRY="$SCRIPT_DIR/service-registry.json"
HEALTH_CHECKER="$SCRIPT_DIR/health-checker.sh"
MAX_STARTUP_TIME=300  # 5 minutes
HEALTH_CHECK_INTERVAL=10  # 10 seconds

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "\n${PURPLE}=== $1 ===${NC}\n"
}

# Function to check dependencies
check_dependencies() {
    if ! command -v jq > /dev/null 2>&1; then
        log_error "jq is required but not installed. Please install jq and try again."
        exit 1
    fi
    
    if [[ ! -f "$SERVICE_REGISTRY" ]]; then
        log_error "Service registry not found: $SERVICE_REGISTRY"
        exit 1
    fi
    
    if [[ ! -x "$HEALTH_CHECKER" ]]; then
        log_error "Health checker script not found or not executable: $HEALTH_CHECKER"
        exit 1
    fi
}

# Function to get services by startup order
get_services_by_order() {
    local order="$1"
    local service_type="${2:-all}"  # all, infrastructure, services
    
    case "$service_type" in
        "infrastructure")
            jq -r ".infrastructure | to_entries[] | select(.value.startup.order == $order) | .key" "$SERVICE_REGISTRY"
            ;;
        "services")
            jq -r ".services | to_entries[] | select(.value.startup.order == $order) | .key" "$SERVICE_REGISTRY"
            ;;
        "all")
            {
                jq -r ".infrastructure | to_entries[] | select(.value.startup.order == $order) | .key" "$SERVICE_REGISTRY"
                jq -r ".services | to_entries[] | select(.value.startup.order == $order) | .key" "$SERVICE_REGISTRY"
            } | sort
            ;;
    esac
}

# Function to get maximum startup order
get_max_startup_order() {
    local max_infra max_services
    max_infra=$(jq -r '.infrastructure | [.[].startup.order] | max' "$SERVICE_REGISTRY")
    max_services=$(jq -r '.services | [.[].startup.order] | max' "$SERVICE_REGISTRY")
    
    if [[ "$max_infra" -gt "$max_services" ]]; then
        echo "$max_infra"
    else
        echo "$max_services"
    fi
}

# Function to check if service dependencies are ready
check_service_dependencies() {
    local service_name="$1"
    local service_type="$2"  # infrastructure or services
    
    local dependencies
    if [[ "$service_type" == "infrastructure" ]]; then
        dependencies=$(jq -r ".infrastructure[\"$service_name\"].startup.dependsOn[]?" "$SERVICE_REGISTRY" 2>/dev/null || echo "")
    else
        dependencies=$(jq -r ".services[\"$service_name\"].startup.dependsOn[]?" "$SERVICE_REGISTRY" 2>/dev/null || echo "")
    fi
    
    if [[ -z "$dependencies" ]]; then
        return 0  # No dependencies
    fi
    
    log_info "Checking dependencies for $service_name: $dependencies"
    
    while IFS= read -r dep; do
        if [[ -n "$dep" ]]; then
            if ! "$HEALTH_CHECKER" check "$dep" > /dev/null 2>&1; then
                log_warning "Dependency $dep is not ready for $service_name"
                return 1
            fi
        fi
    done <<< "$dependencies"
    
    log_success "All dependencies ready for $service_name"
    return 0
}

# Function to wait for service to be healthy
wait_for_service_health() {
    local service_name="$1"
    local max_wait="${2:-$MAX_STARTUP_TIME}"
    
    log_info "Waiting for $service_name to be healthy (max ${max_wait}s)"
    
    local elapsed=0
    while [[ $elapsed -lt $max_wait ]]; do
        if "$HEALTH_CHECKER" check "$service_name" > /dev/null 2>&1; then
            log_success "$service_name is healthy after ${elapsed}s"
            return 0
        fi
        
        sleep "$HEALTH_CHECK_INTERVAL"
        elapsed=$((elapsed + HEALTH_CHECK_INTERVAL))
        
        if [[ $((elapsed % 30)) -eq 0 ]]; then
            log_info "$service_name still starting... (${elapsed}s elapsed)"
        fi
    done
    
    log_error "$service_name did not become healthy within ${max_wait}s"
    return 1
}

# Function to start services in order
start_services_in_order() {
    local dry_run="${1:-false}"
    
    log_header "Service Startup Orchestration"
    
    if [[ "$dry_run" == "true" ]]; then
        log_info "DRY RUN MODE - No services will actually be started"
    fi
    
    local max_order
    max_order=$(get_max_startup_order)
    
    log_info "Maximum startup order: $max_order"
    
    # Process each startup order level
    for order in $(seq 1 "$max_order"); do
        log_header "Startup Order $order"
        
        # Get services for this order
        local services
        services=$(get_services_by_order "$order")
        
        if [[ -z "$services" ]]; then
            log_info "No services found for order $order"
            continue
        fi
        
        log_info "Services to start in order $order: $(echo "$services" | tr '\n' ' ')"
        
        # Start each service in this order level
        while IFS= read -r service_name; do
            if [[ -n "$service_name" ]]; then
                log_info "Processing service: $service_name"
                
                # Determine service type
                local service_type
                if jq -e ".infrastructure[\"$service_name\"]" "$SERVICE_REGISTRY" > /dev/null 2>&1; then
                    service_type="infrastructure"
                else
                    service_type="services"
                fi
                
                # Check dependencies
                if ! check_service_dependencies "$service_name" "$service_type"; then
                    log_error "Dependencies not ready for $service_name, skipping"
                    continue
                fi
                
                # Start the service (if not dry run)
                if [[ "$dry_run" != "true" ]]; then
                    log_info "Service $service_name dependencies are ready, it should start automatically"
                    
                    # Wait for the service to become healthy
                    if ! wait_for_service_health "$service_name"; then
                        log_error "Failed to start $service_name"
                        return 1
                    fi
                else
                    log_info "DRY RUN: Would start $service_name"
                fi
            fi
        done <<< "$services"
        
        log_success "Completed startup order $order"
    done
    
    log_header "Startup Orchestration Complete"
    
    # Final health check
    if [[ "$dry_run" != "true" ]]; then
        log_info "Performing final health check on all services"
        if "$HEALTH_CHECKER" check; then
            log_success "All services are healthy and ready"
        else
            log_warning "Some services may not be fully ready"
            return 1
        fi
    fi
    
    return 0
}

# Function to show startup plan
show_startup_plan() {
    log_header "Service Startup Plan"
    
    local max_order
    max_order=$(get_max_startup_order)
    
    for order in $(seq 1 "$max_order"); do
        echo -e "${BLUE}Order $order:${NC}"
        
        local services
        services=$(get_services_by_order "$order")
        
        while IFS= read -r service_name; do
            if [[ -n "$service_name" ]]; then
                # Get dependencies
                local dependencies service_type
                if jq -e ".infrastructure[\"$service_name\"]" "$SERVICE_REGISTRY" > /dev/null 2>&1; then
                    service_type="infrastructure"
                    dependencies=$(jq -r ".infrastructure[\"$service_name\"].startup.dependsOn[]?" "$SERVICE_REGISTRY" 2>/dev/null | tr '\n' ',' | sed 's/,$//')
                else
                    service_type="services"
                    dependencies=$(jq -r ".services[\"$service_name\"].startup.dependsOn[]?" "$SERVICE_REGISTRY" 2>/dev/null | tr '\n' ',' | sed 's/,$//')
                fi
                
                if [[ -n "$dependencies" ]]; then
                    echo "  - $service_name ($service_type) [depends on: $dependencies]"
                else
                    echo "  - $service_name ($service_type) [no dependencies]"
                fi
            fi
        done <<< "$services"
        
        echo ""
    done
}

# Function to validate startup configuration
validate_startup_config() {
    log_header "Validating Startup Configuration"
    
    local validation_errors=0
    
    # Check for circular dependencies
    log_info "Checking for circular dependencies..."
    
    # Check if all dependencies exist
    log_info "Checking if all dependencies exist..."
    
    local all_services
    all_services=$(jq -r '.infrastructure | keys[]' "$SERVICE_REGISTRY"; jq -r '.services | keys[]' "$SERVICE_REGISTRY")
    
    while IFS= read -r service_name; do
        local dependencies service_type
        
        if jq -e ".infrastructure[\"$service_name\"]" "$SERVICE_REGISTRY" > /dev/null 2>&1; then
            service_type="infrastructure"
            dependencies=$(jq -r ".infrastructure[\"$service_name\"].startup.dependsOn[]?" "$SERVICE_REGISTRY" 2>/dev/null || echo "")
        else
            service_type="services"
            dependencies=$(jq -r ".services[\"$service_name\"].startup.dependsOn[]?" "$SERVICE_REGISTRY" 2>/dev/null || echo "")
        fi
        
        while IFS= read -r dep; do
            if [[ -n "$dep" ]]; then
                if ! echo "$all_services" | grep -q "^$dep$"; then
                    log_error "Service $service_name depends on non-existent service: $dep"
                    validation_errors=$((validation_errors + 1))
                fi
            fi
        done <<< "$dependencies"
    done <<< "$all_services"
    
    if [[ $validation_errors -eq 0 ]]; then
        log_success "Startup configuration is valid"
        return 0
    else
        log_error "Found $validation_errors validation errors"
        return 1
    fi
}

# Function to show help
show_help() {
    echo "BDD Service Startup Orchestrator"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  start [--dry-run]  - Start services in dependency order"
    echo "  plan               - Show startup plan without starting services"
    echo "  validate           - Validate startup configuration"
    echo "  help               - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start                    # Start all services in order"
    echo "  $0 start --dry-run          # Show what would be started"
    echo "  $0 plan                     # Show startup plan"
    echo "  $0 validate                 # Validate configuration"
}

# Main execution
main() {
    local command="${1:-help}"
    local option="${2:-}"
    
    # Check dependencies
    check_dependencies
    
    case "$command" in
        "start")
            if [[ "$option" == "--dry-run" ]]; then
                start_services_in_order "true"
            else
                start_services_in_order "false"
            fi
            ;;
        "plan")
            show_startup_plan
            ;;
        "validate")
            validate_startup_config
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Execute main function with all arguments
main "$@"
