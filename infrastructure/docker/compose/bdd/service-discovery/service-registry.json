{"services": {"auth-service": {"name": "auth-service", "version": "1.0.0", "environment": "bdd", "http": {"host": "host.docker.internal", "port": 3001, "baseUrl": "http://host.docker.internal:3001", "healthEndpoint": "/health", "readinessEndpoint": "/health/ready", "livenessEndpoint": "/health/live"}, "grpc": {"host": "host.docker.internal", "port": 3012, "url": "host.docker.internal:3012", "package": ["auth", "common"], "protoFiles": ["auth.proto", "health.proto"]}, "dependencies": ["postgres", "redis", "auth0"], "healthCheck": {"enabled": true, "interval": 10000, "timeout": 5000, "retries": 3, "startPeriod": 30000}, "startup": {"order": 2, "dependsOn": ["postgres", "redis"]}}, "user-service": {"name": "user-service", "version": "1.0.0", "environment": "bdd", "http": {"host": "host.docker.internal", "port": 3002, "baseUrl": "http://host.docker.internal:3002", "healthEndpoint": "/health", "readinessEndpoint": "/health/ready", "livenessEndpoint": "/health/live"}, "grpc": {"host": "host.docker.internal", "port": 3013, "url": "host.docker.internal:3013", "package": ["user", "common"], "protoFiles": ["user.proto", "health.proto"]}, "dependencies": ["postgres", "redis", "auth-service"], "healthCheck": {"enabled": true, "interval": 10000, "timeout": 5000, "retries": 3, "startPeriod": 30000}, "startup": {"order": 3, "dependsOn": ["postgres", "redis", "auth-service"]}}, "tenant-service": {"name": "tenant-service", "version": "1.0.0", "environment": "bdd", "http": {"host": "host.docker.internal", "port": 3003, "baseUrl": "http://host.docker.internal:3003", "healthEndpoint": "/health", "readinessEndpoint": "/health/ready", "livenessEndpoint": "/health/live"}, "grpc": {"host": "host.docker.internal", "port": 3014, "url": "host.docker.internal:3014", "package": ["tenant", "common"], "protoFiles": ["tenant.proto", "health.proto"]}, "dependencies": ["postgres", "redis", "auth-service"], "healthCheck": {"enabled": true, "interval": 10000, "timeout": 5000, "retries": 3, "startPeriod": 30000}, "startup": {"order": 3, "dependsOn": ["postgres", "redis", "auth-service"]}}, "notification-service": {"name": "notification-service", "version": "1.0.0", "environment": "bdd", "http": {"host": "host.docker.internal", "port": 3004, "baseUrl": "http://host.docker.internal:3004", "healthEndpoint": "/health", "readinessEndpoint": "/health/ready", "livenessEndpoint": "/health/live"}, "grpc": {"host": "host.docker.internal", "port": 3015, "url": "host.docker.internal:3015", "package": ["notification", "common"], "protoFiles": ["notification.proto", "health.proto"]}, "dependencies": ["postgres", "redis", "resend", "twi<PERSON>"], "healthCheck": {"enabled": true, "interval": 10000, "timeout": 5000, "retries": 3, "startPeriod": 30000}, "startup": {"order": 2, "dependsOn": ["postgres", "redis"]}}, "audit-service": {"name": "audit-service", "version": "1.0.0", "environment": "bdd", "http": {"host": "host.docker.internal", "port": 3005, "baseUrl": "http://host.docker.internal:3005", "healthEndpoint": "/health", "readinessEndpoint": "/health/ready", "livenessEndpoint": "/health/live"}, "grpc": {"host": "host.docker.internal", "port": 3016, "url": "host.docker.internal:3016", "package": ["audit", "common"], "protoFiles": ["audit.proto", "health.proto"]}, "dependencies": ["postgres", "redis"], "healthCheck": {"enabled": true, "interval": 10000, "timeout": 5000, "retries": 3, "startPeriod": 30000}, "startup": {"order": 2, "dependsOn": ["postgres", "redis"]}}}, "infrastructure": {"postgres": {"name": "postgres", "type": "database", "host": "postgres", "port": 5432, "healthCheck": {"enabled": true, "command": "pg_isready -U qeep_bdd -d qeep_bdd", "interval": 10000, "timeout": 5000, "retries": 5, "startPeriod": 30000}, "startup": {"order": 1, "dependsOn": []}}, "redis": {"name": "redis", "type": "cache", "host": "redis", "port": 6379, "healthCheck": {"enabled": true, "command": "redis-cli -a qeep_bdd_redis_password ping", "interval": 10000, "timeout": 5000, "retries": 3, "startPeriod": 10000}, "startup": {"order": 1, "dependsOn": []}}, "nginx": {"name": "nginx", "type": "proxy", "host": "nginx", "port": 80, "healthCheck": {"enabled": true, "command": "curl -f http://localhost/health", "interval": 10000, "timeout": 5000, "retries": 3, "startPeriod": 10000}, "startup": {"order": 4, "dependsOn": ["postgres", "redis"]}}, "mailhog": {"name": "mailhog", "type": "mock", "host": "mailhog", "port": 1025, "webPort": 8025, "healthCheck": {"enabled": false}, "startup": {"order": 1, "dependsOn": []}}}, "external": {"auth0": {"name": "auth0", "type": "identity-provider", "baseUrl": "https://bdd-test.us.auth0.com", "mocked": true, "healthCheck": {"enabled": false}}, "resend": {"name": "resend", "type": "email-provider", "baseUrl": "https://api.resend.com", "mocked": true, "healthCheck": {"enabled": false}}, "twilio": {"name": "twi<PERSON>", "type": "sms-provider", "baseUrl": "https://api.twilio.com", "mocked": true, "healthCheck": {"enabled": false}}}, "metadata": {"environment": "bdd", "version": "1.0.0", "lastUpdated": "2025-01-11T00:00:00Z", "description": "BDD Test Environment Service Registry"}}