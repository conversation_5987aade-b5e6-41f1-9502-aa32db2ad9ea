#!/bin/bash

# =============================================================================
# BDD Service Health Checker
# =============================================================================
# This script checks the health of all services in the BDD test environment
# and provides detailed status information for debugging

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVICE_REGISTRY="$SCRIPT_DIR/service-registry.json"
TIMEOUT=30
RETRY_ATTEMPTS=3
RETRY_DELAY=2

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "\n${PURPLE}=== $1 ===${NC}\n"
}

# Function to check if jq is available
check_dependencies() {
    if ! command -v jq > /dev/null 2>&1; then
        log_error "jq is required but not installed. Please install jq and try again."
        exit 1
    fi
    
    if ! command -v curl > /dev/null 2>&1; then
        log_error "curl is required but not installed. Please install curl and try again."
        exit 1
    fi
}

# Function to check HTTP endpoint health
check_http_health() {
    local service_name="$1"
    local url="$2"
    local timeout="${3:-$TIMEOUT}"
    
    log_info "Checking HTTP health for $service_name at $url"
    
    local response_code
    if response_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time "$timeout" "$url" 2>/dev/null); then
        if [[ "$response_code" -ge 200 && "$response_code" -lt 300 ]]; then
            log_success "$service_name HTTP health check passed (HTTP $response_code)"
            return 0
        else
            log_warning "$service_name HTTP health check returned HTTP $response_code"
            return 1
        fi
    else
        log_error "$service_name HTTP health check failed (connection error)"
        return 1
    fi
}

# Function to check gRPC health
check_grpc_health() {
    local service_name="$1"
    local grpc_url="$2"
    
    log_info "Checking gRPC health for $service_name at $grpc_url"
    
    # For now, we'll just check if the port is open
    local host port
    host=$(echo "$grpc_url" | cut -d':' -f1)
    port=$(echo "$grpc_url" | cut -d':' -f2)
    
    if timeout "$TIMEOUT" bash -c "</dev/tcp/$host/$port" 2>/dev/null; then
        log_success "$service_name gRPC port is accessible"
        return 0
    else
        log_error "$service_name gRPC port is not accessible"
        return 1
    fi
}

# Function to check infrastructure service health
check_infrastructure_health() {
    local service_name="$1"
    local service_data="$2"
    
    local host port command
    host=$(echo "$service_data" | jq -r '.host')
    port=$(echo "$service_data" | jq -r '.port')
    command=$(echo "$service_data" | jq -r '.healthCheck.command // empty')
    
    log_info "Checking infrastructure service: $service_name"
    
    if [[ -n "$command" ]]; then
        # Use the specific health check command
        if eval "$command" > /dev/null 2>&1; then
            log_success "$service_name infrastructure health check passed"
            return 0
        else
            log_error "$service_name infrastructure health check failed"
            return 1
        fi
    else
        # Fallback to port check
        if timeout "$TIMEOUT" bash -c "</dev/tcp/$host/$port" 2>/dev/null; then
            log_success "$service_name port is accessible"
            return 0
        else
            log_error "$service_name port is not accessible"
            return 1
        fi
    fi
}

# Function to check all services
check_all_services() {
    local overall_status=0
    local service_results=()
    
    log_header "BDD Service Health Check"
    
    # Check infrastructure services first
    log_header "Infrastructure Services"
    
    local infrastructure_services
    infrastructure_services=$(jq -r '.infrastructure | keys[]' "$SERVICE_REGISTRY")
    
    while IFS= read -r service_name; do
        local service_data
        service_data=$(jq ".infrastructure[\"$service_name\"]" "$SERVICE_REGISTRY")
        
        if check_infrastructure_health "$service_name" "$service_data"; then
            service_results+=("$service_name:✓")
        else
            service_results+=("$service_name:✗")
            overall_status=1
        fi
    done <<< "$infrastructure_services"
    
    # Check application services
    log_header "Application Services"
    
    local app_services
    app_services=$(jq -r '.services | keys[]' "$SERVICE_REGISTRY")
    
    while IFS= read -r service_name; do
        local service_data
        service_data=$(jq ".services[\"$service_name\"]" "$SERVICE_REGISTRY")
        
        local http_url grpc_url
        http_url=$(echo "$service_data" | jq -r '.http.baseUrl + .http.healthEndpoint')
        grpc_url=$(echo "$service_data" | jq -r '.grpc.url')
        
        local service_status=0
        
        # Check HTTP health
        if ! check_http_health "$service_name" "$http_url"; then
            service_status=1
        fi
        
        # Check gRPC health
        if ! check_grpc_health "$service_name" "$grpc_url"; then
            service_status=1
        fi
        
        if [[ $service_status -eq 0 ]]; then
            service_results+=("$service_name:✓")
        else
            service_results+=("$service_name:✗")
            overall_status=1
        fi
    done <<< "$app_services"
    
    # Display summary
    log_header "Health Check Summary"
    
    for result in "${service_results[@]}"; do
        local service status
        service=$(echo "$result" | cut -d':' -f1)
        status=$(echo "$result" | cut -d':' -f2)
        
        if [[ "$status" == "✓" ]]; then
            echo -e "${GREEN}✓${NC} $service"
        else
            echo -e "${RED}✗${NC} $service"
        fi
    done
    
    echo ""
    if [[ $overall_status -eq 0 ]]; then
        log_success "All services are healthy"
    else
        log_error "Some services are unhealthy"
    fi
    
    return $overall_status
}

# Function to check specific service
check_service() {
    local target_service="$1"
    
    log_header "Checking Service: $target_service"
    
    # Check if it's an infrastructure service
    if jq -e ".infrastructure[\"$target_service\"]" "$SERVICE_REGISTRY" > /dev/null 2>&1; then
        local service_data
        service_data=$(jq ".infrastructure[\"$target_service\"]" "$SERVICE_REGISTRY")
        check_infrastructure_health "$target_service" "$service_data"
        return $?
    fi
    
    # Check if it's an application service
    if jq -e ".services[\"$target_service\"]" "$SERVICE_REGISTRY" > /dev/null 2>&1; then
        local service_data
        service_data=$(jq ".services[\"$target_service\"]" "$SERVICE_REGISTRY")
        
        local http_url grpc_url
        http_url=$(echo "$service_data" | jq -r '.http.baseUrl + .http.healthEndpoint')
        grpc_url=$(echo "$service_data" | jq -r '.grpc.url')
        
        local service_status=0
        
        # Check HTTP health
        if ! check_http_health "$target_service" "$http_url"; then
            service_status=1
        fi
        
        # Check gRPC health
        if ! check_grpc_health "$target_service" "$grpc_url"; then
            service_status=1
        fi
        
        return $service_status
    fi
    
    log_error "Service '$target_service' not found in registry"
    return 1
}

# Function to wait for services to be ready
wait_for_services() {
    local max_wait="${1:-300}"  # Default 5 minutes
    local check_interval="${2:-10}"  # Default 10 seconds
    
    log_header "Waiting for Services to be Ready"
    log_info "Maximum wait time: ${max_wait}s, Check interval: ${check_interval}s"
    
    local elapsed=0
    
    while [[ $elapsed -lt $max_wait ]]; do
        log_info "Checking services... (${elapsed}s elapsed)"
        
        if check_all_services > /dev/null 2>&1; then
            log_success "All services are ready after ${elapsed}s"
            return 0
        fi
        
        sleep "$check_interval"
        elapsed=$((elapsed + check_interval))
    done
    
    log_error "Services did not become ready within ${max_wait}s"
    return 1
}

# Function to show service registry
show_registry() {
    log_header "Service Registry"
    
    if [[ -f "$SERVICE_REGISTRY" ]]; then
        jq '.' "$SERVICE_REGISTRY"
    else
        log_error "Service registry file not found: $SERVICE_REGISTRY"
        return 1
    fi
}

# Function to show help
show_help() {
    echo "BDD Service Health Checker"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  check [SERVICE]  - Check health of all services or specific service"
    echo "  wait [TIMEOUT]   - Wait for all services to be ready (default: 300s)"
    echo "  registry         - Show service registry"
    echo "  help             - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 check                    # Check all services"
    echo "  $0 check auth-service       # Check specific service"
    echo "  $0 wait 600                 # Wait up to 10 minutes for services"
    echo "  $0 registry                 # Show service registry"
}

# Main execution
main() {
    local command="${1:-check}"
    local option="${2:-}"
    
    # Check dependencies
    check_dependencies
    
    case "$command" in
        "check")
            if [[ -n "$option" ]]; then
                check_service "$option"
            else
                check_all_services
            fi
            ;;
        "wait")
            wait_for_services "$option"
            ;;
        "registry")
            show_registry
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Execute main function with all arguments
main "$@"
