# =============================================================================
# NGINX Configuration for BDD Test Environment
# =============================================================================

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Rate limiting (disabled for BDD tests)
    # limit_req_zone $binary_remote_addr zone=api:10m rate=1000r/s;

    # Upstream definitions
    upstream api_gateway {
        server host.docker.internal:3000;
        keepalive 32;
    }

    upstream auth_service {
        server host.docker.internal:3001;
        keepalive 32;
    }

    upstream user_service {
        server host.docker.internal:3002;
        keepalive 32;
    }

    upstream tenant_service {
        server host.docker.internal:3003;
        keepalive 32;
    }

    upstream notification_service {
        server host.docker.internal:3004;
        keepalive 32;
    }

    upstream audit_service {
        server host.docker.internal:3005;
        keepalive 32;
    }

    # Include additional configurations
    include /etc/nginx/conf.d/*.conf;
}
