#!/bin/bash

# =============================================================================
# BDD Test Redis Cleanup Script
# =============================================================================
# This script provides various cleanup operations for the BDD test Redis instance

set -euo pipefail

# Configuration
REDIS_HOST="${REDIS_HOST:-localhost}"
REDIS_PORT="${REDIS_PORT:-6381}"
REDIS_PASSWORD="${REDIS_PASSWORD:-qeep_bdd_redis_password}"
REDIS_DB_PREFIX="bdd:test:"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Redis connection function
execute_redis() {
    local command="$1"
    local description="${2:-Redis command}"
    
    log_info "Executing: $description"
    
    if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --no-auth-warning $command > /dev/null 2>&1; then
        log_success "$description completed"
        return 0
    else
        log_error "$description failed"
        return 1
    fi
}

# Function to check Redis connection
check_connection() {
    log_info "Checking Redis connection..."
    
    if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --no-auth-warning ping > /dev/null 2>&1; then
        log_success "Redis connection established"
        return 0
    else
        log_error "Cannot connect to Redis"
        return 1
    fi
}

# Function to cleanup BDD test keys
cleanup_bdd_keys() {
    log_info "Cleaning up BDD test keys..."
    
    # Get all keys with BDD test prefix
    local keys=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --no-auth-warning KEYS "${REDIS_DB_PREFIX}*" 2>/dev/null)
    
    if [ -n "$keys" ] && [ "$keys" != "(empty array)" ]; then
        local key_count=$(echo "$keys" | wc -l)
        log_info "Found $key_count BDD test keys to cleanup"
        
        # Delete keys in batches
        echo "$keys" | while IFS= read -r key; do
            if [ -n "$key" ]; then
                redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --no-auth-warning DEL "$key" > /dev/null 2>&1
            fi
        done
        
        log_success "Cleaned up $key_count BDD test keys"
    else
        log_info "No BDD test keys found to cleanup"
    fi
}

# Function to cleanup verification tokens
cleanup_verification_tokens() {
    log_info "Cleaning up verification tokens..."
    
    # Get all verification token keys
    local keys=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --no-auth-warning KEYS "verification:*" 2>/dev/null)
    
    if [ -n "$keys" ] && [ "$keys" != "(empty array)" ]; then
        local key_count=$(echo "$keys" | wc -l)
        log_info "Found $key_count verification tokens to cleanup"
        
        # Delete keys
        echo "$keys" | while IFS= read -r key; do
            if [ -n "$key" ]; then
                redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --no-auth-warning DEL "$key" > /dev/null 2>&1
            fi
        done
        
        log_success "Cleaned up $key_count verification tokens"
    else
        log_info "No verification tokens found to cleanup"
    fi
}

# Function to cleanup session data
cleanup_sessions() {
    log_info "Cleaning up session data..."
    
    # Get all session keys
    local keys=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --no-auth-warning KEYS "session:*" 2>/dev/null)
    
    if [ -n "$keys" ] && [ "$keys" != "(empty array)" ]; then
        local key_count=$(echo "$keys" | wc -l)
        log_info "Found $key_count sessions to cleanup"
        
        # Delete keys
        echo "$keys" | while IFS= read -r key; do
            if [ -n "$key" ]; then
                redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --no-auth-warning DEL "$key" > /dev/null 2>&1
            fi
        done
        
        log_success "Cleaned up $key_count sessions"
    else
        log_info "No sessions found to cleanup"
    fi
}

# Function to cleanup rate limiting data
cleanup_rate_limits() {
    log_info "Cleaning up rate limiting data..."
    
    # Get all rate limit keys
    local keys=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --no-auth-warning KEYS "rate-limit:*" 2>/dev/null)
    
    if [ -n "$keys" ] && [ "$keys" != "(empty array)" ]; then
        local key_count=$(echo "$keys" | wc -l)
        log_info "Found $key_count rate limit entries to cleanup"
        
        # Delete keys
        echo "$keys" | while IFS= read -r key; do
            if [ -n "$key" ]; then
                redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --no-auth-warning DEL "$key" > /dev/null 2>&1
            fi
        done
        
        log_success "Cleaned up $key_count rate limit entries"
    else
        log_info "No rate limit entries found to cleanup"
    fi
}

# Function to flush specific database
flush_database() {
    local db_number="${1:-2}"
    
    log_warning "This will flush database $db_number completely!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Operation cancelled"
        return 0
    fi
    
    log_info "Flushing Redis database $db_number..."
    
    if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --no-auth-warning -n "$db_number" FLUSHDB > /dev/null 2>&1; then
        log_success "Database $db_number flushed"
    else
        log_error "Failed to flush database $db_number"
    fi
}

# Function to show Redis statistics
show_stats() {
    log_info "Redis Statistics:"
    
    # Basic info
    echo -e "\n${BLUE}Redis Info:${NC}"
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --no-auth-warning INFO memory | grep -E "(used_memory_human|used_memory_peak_human|maxmemory_human)"
    
    # Key counts by pattern
    echo -e "\n${BLUE}Key Counts:${NC}"
    
    local bdd_keys=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --no-auth-warning KEYS "${REDIS_DB_PREFIX}*" | wc -l)
    echo "BDD Test Keys: $bdd_keys"
    
    local verification_keys=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --no-auth-warning KEYS "verification:*" | wc -l)
    echo "Verification Tokens: $verification_keys"
    
    local session_keys=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --no-auth-warning KEYS "session:*" | wc -l)
    echo "Sessions: $session_keys"
    
    local rate_limit_keys=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --no-auth-warning KEYS "rate-limit:*" | wc -l)
    echo "Rate Limits: $rate_limit_keys"
    
    local total_keys=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --no-auth-warning DBSIZE)
    echo "Total Keys: $total_keys"
    
    # Database info
    echo -e "\n${BLUE}Database Info:${NC}"
    for db in {0..15}; do
        local db_size=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --no-auth-warning -n "$db" DBSIZE 2>/dev/null || echo "0")
        if [ "$db_size" -gt 0 ]; then
            echo "Database $db: $db_size keys"
        fi
    done
}

# Function to show help
show_help() {
    echo "BDD Test Redis Cleanup Script"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  bdd          - Cleanup BDD test keys (${REDIS_DB_PREFIX}*)"
    echo "  tokens       - Cleanup verification tokens"
    echo "  sessions     - Cleanup session data"
    echo "  ratelimits   - Cleanup rate limiting data"
    echo "  flush [DB]   - Flush specific database (default: 2)"
    echo "  full         - Run full cleanup (bdd + tokens + sessions + ratelimits)"
    echo "  stats        - Show Redis statistics"
    echo "  help         - Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  REDIS_HOST     - Redis host (default: localhost)"
    echo "  REDIS_PORT     - Redis port (default: 6381)"
    echo "  REDIS_PASSWORD - Redis password (default: qeep_bdd_redis_password)"
}

# Main execution
main() {
    local command="${1:-help}"
    local option="${2:-}"
    
    case "$command" in
        "bdd")
            check_connection && cleanup_bdd_keys
            ;;
        "tokens")
            check_connection && cleanup_verification_tokens
            ;;
        "sessions")
            check_connection && cleanup_sessions
            ;;
        "ratelimits")
            check_connection && cleanup_rate_limits
            ;;
        "flush")
            check_connection && flush_database "$option"
            ;;
        "full")
            check_connection && cleanup_bdd_keys && cleanup_verification_tokens && cleanup_sessions && cleanup_rate_limits
            ;;
        "stats")
            check_connection && show_stats
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Execute main function with all arguments
main "$@"
