#!/bin/bash

# =============================================================================
# BDD Test Database Cleanup Script
# =============================================================================
# This script provides various cleanup operations for the BDD test database

set -euo pipefail

# Configuration
DB_HOST="${PGHOST:-localhost}"
DB_PORT="${PGPORT:-5434}"
DB_USER="${PGUSER:-qeep_bdd}"
DB_PASSWORD="${PGPASSWORD:-qeep_bdd_password}"
DB_NAME="${PGDATABASE:-qeep_bdd}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Database connection function
execute_sql() {
    local sql="$1"
    local description="${2:-SQL command}"
    
    log_info "Executing: $description"
    
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "$sql" > /dev/null 2>&1; then
        log_success "$description completed"
        return 0
    else
        log_error "$description failed"
        return 1
    fi
}

# Function to check database connection
check_connection() {
    log_info "Checking database connection..."
    
    if PGPASSWORD="$DB_PASSWORD" pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" > /dev/null 2>&1; then
        log_success "Database connection established"
        return 0
    else
        log_error "Cannot connect to database"
        return 1
    fi
}

# Function to cleanup test scenarios
cleanup_test_scenarios() {
    log_info "Cleaning up test scenarios..."
    
    local sql="SELECT bdd_test.cleanup_all_test_data();"
    
    if execute_sql "$sql" "Test scenarios cleanup"; then
        # Get cleanup count
        local count_sql="SELECT COUNT(*) FROM bdd_test.test_scenarios WHERE status = 'cleaned_up';"
        local count=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "$count_sql" | xargs)
        log_success "Cleaned up $count test scenarios"
    fi
}

# Function to truncate all tables
truncate_all_tables() {
    log_warning "This will truncate ALL tables in the database!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Operation cancelled"
        return 0
    fi
    
    log_info "Truncating all tables..."
    
    # Get all tables except system tables
    local tables_sql="
        SELECT schemaname||'.'||tablename 
        FROM pg_tables 
        WHERE schemaname NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
        AND tablename NOT LIKE 'pg_%'
        ORDER BY schemaname, tablename;
    "
    
    local tables=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "$tables_sql")
    
    if [ -n "$tables" ]; then
        # Disable foreign key checks temporarily
        execute_sql "SET session_replication_role = replica;" "Disable foreign key checks"
        
        # Truncate each table
        while IFS= read -r table; do
            if [ -n "$table" ]; then
                table=$(echo "$table" | xargs) # trim whitespace
                execute_sql "TRUNCATE TABLE $table CASCADE;" "Truncate $table"
            fi
        done <<< "$tables"
        
        # Re-enable foreign key checks
        execute_sql "SET session_replication_role = DEFAULT;" "Re-enable foreign key checks"
        
        log_success "All tables truncated"
    else
        log_info "No tables found to truncate"
    fi
}

# Function to reset sequences
reset_sequences() {
    log_info "Resetting sequences..."
    
    local sequences_sql="
        SELECT schemaname||'.'||sequencename 
        FROM pg_sequences 
        WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
        ORDER BY schemaname, sequencename;
    "
    
    local sequences=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "$sequences_sql")
    
    if [ -n "$sequences" ]; then
        while IFS= read -r sequence; do
            if [ -n "$sequence" ]; then
                sequence=$(echo "$sequence" | xargs) # trim whitespace
                execute_sql "ALTER SEQUENCE $sequence RESTART WITH 1;" "Reset $sequence"
            fi
        done <<< "$sequences"
        
        log_success "All sequences reset"
    else
        log_info "No sequences found to reset"
    fi
}

# Function to vacuum and analyze
vacuum_analyze() {
    log_info "Running VACUUM ANALYZE..."
    
    execute_sql "VACUUM ANALYZE;" "VACUUM ANALYZE"
}

# Function to show database statistics
show_stats() {
    log_info "Database Statistics:"
    
    # Table counts
    local stats_sql="
        SELECT 
            schemaname,
            tablename,
            n_tup_ins as inserts,
            n_tup_upd as updates,
            n_tup_del as deletes,
            n_live_tup as live_rows
        FROM pg_stat_user_tables 
        WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
        ORDER BY schemaname, tablename;
    "
    
    echo -e "\n${BLUE}Table Statistics:${NC}"
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "$stats_sql"
    
    # Test scenarios
    local scenarios_sql="
        SELECT 
            status,
            COUNT(*) as count
        FROM bdd_test.test_scenarios 
        GROUP BY status
        ORDER BY status;
    "
    
    echo -e "\n${BLUE}Test Scenarios:${NC}"
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "$scenarios_sql" 2>/dev/null || echo "No test scenarios found"
}

# Function to show help
show_help() {
    echo "BDD Test Database Cleanup Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  scenarios    - Cleanup test scenarios using BDD test utilities"
    echo "  truncate     - Truncate all tables (WARNING: destructive)"
    echo "  sequences    - Reset all sequences to 1"
    echo "  vacuum       - Run VACUUM ANALYZE"
    echo "  full         - Run full cleanup (scenarios + vacuum)"
    echo "  stats        - Show database statistics"
    echo "  help         - Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  PGHOST       - Database host (default: localhost)"
    echo "  PGPORT       - Database port (default: 5434)"
    echo "  PGUSER       - Database user (default: qeep_bdd)"
    echo "  PGPASSWORD   - Database password (default: qeep_bdd_password)"
    echo "  PGDATABASE   - Database name (default: qeep_bdd)"
}

# Main execution
main() {
    local command="${1:-help}"
    
    case "$command" in
        "scenarios")
            check_connection && cleanup_test_scenarios
            ;;
        "truncate")
            check_connection && truncate_all_tables
            ;;
        "sequences")
            check_connection && reset_sequences
            ;;
        "vacuum")
            check_connection && vacuum_analyze
            ;;
        "full")
            check_connection && cleanup_test_scenarios && vacuum_analyze
            ;;
        "stats")
            check_connection && show_stats
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Execute main function with all arguments
main "$@"
