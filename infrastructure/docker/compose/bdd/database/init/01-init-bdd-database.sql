-- =============================================================================
-- BDD Test Database Initialization Script
-- =============================================================================
-- This script initializes the BDD test database with necessary schemas,
-- extensions, and configurations optimized for testing

-- Create database if it doesn't exist (handled by POSTGRES_DB env var)
-- CREATE DATABASE IF NOT EXISTS qeep_bdd;

-- Connect to the BDD database
\c qeep_bdd;

-- =============================================================================
-- EXTENSIONS
-- =============================================================================

-- Enable UUID generation in public schema
CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;

-- Enable cryptographic functions in public schema
CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA public;

-- Enable full-text search (if needed) in public schema
CREATE EXTENSION IF NOT EXISTS "pg_trgm" WITH SCHEMA public;

-- =============================================================================
-- SCHEMAS
-- =============================================================================

-- Create schemas for different services
CREATE SCHEMA IF NOT EXISTS auth_service;
CREATE SCHEMA IF NOT EXISTS user_service;
CREATE SCHEMA IF NOT EXISTS tenant_service;
CREATE SCHEMA IF NOT EXISTS notification_service;
CREATE SCHEMA IF NOT EXISTS audit_service;

-- Create test-specific schema
CREATE SCHEMA IF NOT EXISTS bdd_test;

-- =============================================================================
-- BDD TEST UTILITIES SCHEMA
-- =============================================================================

-- Create test utilities in the bdd_test schema
SET search_path TO bdd_test;

-- Table to track test scenarios and cleanup
CREATE TABLE IF NOT EXISTS test_scenarios (
    id UUID PRIMARY KEY DEFAULT public.uuid_generate_v4(),
    scenario_name VARCHAR(255) NOT NULL,
    feature_name VARCHAR(255) NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) DEFAULT 'running',
    cleanup_required BOOLEAN DEFAULT true,
    created_data JSONB DEFAULT '{}',
    UNIQUE(scenario_name, feature_name, started_at)
);

-- Table to track test data for cleanup
CREATE TABLE IF NOT EXISTS test_data_registry (
    id UUID PRIMARY KEY DEFAULT public.uuid_generate_v4(),
    scenario_id UUID REFERENCES test_scenarios(id) ON DELETE CASCADE,
    table_name VARCHAR(255) NOT NULL,
    schema_name VARCHAR(255) NOT NULL DEFAULT 'public',
    record_id VARCHAR(255) NOT NULL,
    record_type VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    cleanup_order INTEGER DEFAULT 0
);

-- Table to store test fixtures
CREATE TABLE IF NOT EXISTS test_fixtures (
    id UUID PRIMARY KEY DEFAULT public.uuid_generate_v4(),
    fixture_name VARCHAR(255) NOT NULL UNIQUE,
    fixture_type VARCHAR(100) NOT NULL,
    fixture_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- BDD TEST FUNCTIONS
-- =============================================================================

-- Function to start a new test scenario
CREATE OR REPLACE FUNCTION start_test_scenario(
    p_scenario_name VARCHAR(255),
    p_feature_name VARCHAR(255)
) RETURNS UUID AS $$
DECLARE
    scenario_id UUID;
BEGIN
    INSERT INTO bdd_test.test_scenarios (scenario_name, feature_name)
    VALUES (p_scenario_name, p_feature_name)
    RETURNING id INTO scenario_id;

    RETURN scenario_id;
END;
$$ LANGUAGE plpgsql;

-- Function to register test data for cleanup
CREATE OR REPLACE FUNCTION register_test_data(
    p_scenario_id UUID,
    p_table_name VARCHAR(255),
    p_schema_name VARCHAR(255),
    p_record_id VARCHAR(255),
    p_record_type VARCHAR(100),
    p_cleanup_order INTEGER DEFAULT 0
) RETURNS VOID AS $$
BEGIN
    INSERT INTO bdd_test.test_data_registry (
        scenario_id, table_name, schema_name, record_id, record_type, cleanup_order
    ) VALUES (
        p_scenario_id, p_table_name, p_schema_name, p_record_id, p_record_type, p_cleanup_order
    );
END;
$$ LANGUAGE plpgsql;

-- Function to complete a test scenario
CREATE OR REPLACE FUNCTION complete_test_scenario(
    p_scenario_id UUID,
    p_status VARCHAR(50) DEFAULT 'completed'
) RETURNS VOID AS $$
BEGIN
    UPDATE bdd_test.test_scenarios
    SET completed_at = NOW(), status = p_status
    WHERE id = p_scenario_id;
END;
$$ LANGUAGE plpgsql;

-- Function to cleanup test data for a scenario
CREATE OR REPLACE FUNCTION cleanup_test_scenario(p_scenario_id UUID) RETURNS INTEGER AS $$
DECLARE
    cleanup_record RECORD;
    cleanup_count INTEGER := 0;
    sql_statement TEXT;
BEGIN
    -- Delete test data in reverse order of creation (highest cleanup_order first)
    FOR cleanup_record IN
        SELECT table_name, schema_name, record_id, record_type
        FROM bdd_test.test_data_registry
        WHERE scenario_id = p_scenario_id
        ORDER BY cleanup_order DESC, created_at DESC
    LOOP
        -- Build dynamic SQL for cleanup
        sql_statement := format(
            'DELETE FROM %I.%I WHERE id = %L',
            cleanup_record.schema_name,
            cleanup_record.table_name,
            cleanup_record.record_id
        );

        -- Execute cleanup
        BEGIN
            EXECUTE sql_statement;
            cleanup_count := cleanup_count + 1;
        EXCEPTION WHEN OTHERS THEN
            -- Log error but continue cleanup
            RAISE NOTICE 'Failed to cleanup %: %', sql_statement, SQLERRM;
        END;
    END LOOP;

    -- Remove test data registry entries
    DELETE FROM bdd_test.test_data_registry WHERE scenario_id = p_scenario_id;

    -- Mark scenario as cleaned up
    UPDATE bdd_test.test_scenarios
    SET status = 'cleaned_up'
    WHERE id = p_scenario_id;

    RETURN cleanup_count;
END;
$$ LANGUAGE plpgsql;

-- Function to cleanup all test data
CREATE OR REPLACE FUNCTION cleanup_all_test_data() RETURNS INTEGER AS $$
DECLARE
    total_cleaned INTEGER := 0;
    scenario_record RECORD;
BEGIN
    FOR scenario_record IN
        SELECT id FROM bdd_test.test_scenarios WHERE status != 'cleaned_up'
    LOOP
        total_cleaned := total_cleaned + bdd_test.cleanup_test_scenario(scenario_record.id);
    END LOOP;

    RETURN total_cleaned;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- PERMISSIONS
-- =============================================================================

-- Grant permissions to the qeep_bdd user
GRANT ALL PRIVILEGES ON SCHEMA bdd_test TO qeep_bdd;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA bdd_test TO qeep_bdd;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA bdd_test TO qeep_bdd;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA bdd_test TO qeep_bdd;

-- Grant permissions on other schemas
GRANT ALL PRIVILEGES ON SCHEMA auth_service TO qeep_bdd;
GRANT ALL PRIVILEGES ON SCHEMA user_service TO qeep_bdd;
GRANT ALL PRIVILEGES ON SCHEMA tenant_service TO qeep_bdd;
GRANT ALL PRIVILEGES ON SCHEMA notification_service TO qeep_bdd;
GRANT ALL PRIVILEGES ON SCHEMA audit_service TO qeep_bdd;

-- Reset search path
SET search_path TO public;

-- =============================================================================
-- CONFIGURATION
-- =============================================================================

-- Optimize for testing performance
ALTER SYSTEM SET synchronous_commit = off;
ALTER SYSTEM SET fsync = off;
ALTER SYSTEM SET full_page_writes = off;
ALTER SYSTEM SET max_wal_size = '1GB';
ALTER SYSTEM SET min_wal_size = '80MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET shared_buffers = '256MB';

-- Reload configuration
SELECT pg_reload_conf();

-- =============================================================================
-- COMPLETION
-- =============================================================================

-- Log successful initialization
DO $$
BEGIN
    RAISE NOTICE 'BDD Test Database initialized successfully at %', NOW();
    RAISE NOTICE 'Database: qeep_bdd';
    RAISE NOTICE 'Schemas created: auth_service, user_service, tenant_service, notification_service, audit_service, bdd_test';
    RAISE NOTICE 'Extensions enabled: uuid-ossp, pgcrypto, pg_trgm';
    RAISE NOTICE 'Test utilities available in bdd_test schema';
END $$;
