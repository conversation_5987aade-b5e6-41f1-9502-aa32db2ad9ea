
#!/bin/bash

# =============================================================================
# Qeep Development Environment Reset Script
# =============================================================================
# This script completely resets the development environment and sets up
# fresh databases with seed data for customer data ingestion testing
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
DOCKER_COMPOSE_FILE="$PROJECT_ROOT/infrastructure/docker/compose/docker-compose.dev.infrastructure.yml"

echo -e "${BLUE}==============================================================================${NC}"
echo -e "${BLUE}Qeep Development Environment Reset${NC}"
echo -e "${BLUE}==============================================================================${NC}"

# Function to print status messages
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    print_status "Checking Docker status..."
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_status "Docker is running ✓"
}

# Function to stop and remove existing containers
cleanup_containers() {
    print_status "Stopping and removing existing containers..."
    
    # Stop all containers defined in docker-compose
    if [ -f "$DOCKER_COMPOSE_FILE" ]; then
        docker-compose -f "$DOCKER_COMPOSE_FILE" down --volumes --remove-orphans 2>/dev/null || true
    fi
    
    # Remove any orphaned containers
    docker container prune -f 2>/dev/null || true
    
    print_status "Container cleanup completed ✓"
}

# Function to clean up Docker volumes
cleanup_volumes() {
    print_status "Cleaning up Docker volumes..."
    
    # Remove specific volumes if they exist
    docker volume rm qeep_postgres_data 2>/dev/null || true
    docker volume rm qeep_redis_data 2>/dev/null || true
    docker volume rm qeep_kafka_data 2>/dev/null || true
    
    # Remove unused volumes
    docker volume prune -f 2>/dev/null || true
    
    print_status "Volume cleanup completed ✓"
}

# Function to clean up Docker networks
cleanup_networks() {
    print_status "Cleaning up Docker networks..."
    
    # Remove unused networks
    docker network prune -f 2>/dev/null || true
    
    print_status "Network cleanup completed ✓"
}

# Function to start infrastructure services
start_infrastructure() {
    print_status "Starting infrastructure services..."
    
    if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
        print_error "Docker compose file not found: $DOCKER_COMPOSE_FILE"
        exit 1
    fi
    
    # Start infrastructure services
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    print_status "Infrastructure services started ✓"
}

# Function to wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for PostgreSQL
    print_status "Waiting for PostgreSQL..."
    timeout=60
    while ! docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T postgres pg_isready -U qeep_dev > /dev/null 2>&1; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -le 0 ]; then
            print_error "PostgreSQL failed to start within 60 seconds"
            exit 1
        fi
    done
    print_status "PostgreSQL is ready ✓"
    
    # Wait for Redis
    print_status "Waiting for Redis..."
    timeout=30
    while ! docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T redis redis-cli ping > /dev/null 2>&1; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -le 0 ]; then
            print_error "Redis failed to start within 30 seconds"
            exit 1
        fi
    done
    print_status "Redis is ready ✓"
    
    # Wait for Kafka (if enabled)
    if docker-compose -f "$DOCKER_COMPOSE_FILE" ps kafka > /dev/null 2>&1; then
        print_status "Waiting for Kafka..."
        sleep 10  # Kafka takes longer to start
        print_status "Kafka is ready ✓"
    fi
}

# Function to run database migrations and seeding
setup_databases() {
    print_status "Setting up databases with migrations and seed data..."

    cd "$PROJECT_ROOT"

    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        print_status "Installing dependencies..."
        npm install
    fi

    # Use the comprehensive seeding script
    print_status "Running comprehensive database setup..."
    if bash scripts/seed-all-services.sh; then
        print_status "Database setup completed successfully ✓"
    else
        print_error "Database setup failed"
        exit 1
    fi
}

# Function to verify setup
verify_setup() {
    print_status "Verifying setup..."
    
    # Check if databases were created
    databases=("qeep_tenant" "qeep_user" "qeep_auth" "qeep_customer" "qeep_transaction" "qeep_aml" "qeep_surveillance" "qeep_integration" "qeep_monitoring")
    
    for db in "${databases[@]}"; do
        if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T postgres psql -U qeep_dev -lqt | cut -d \| -f 1 | grep -qw "$db"; then
            print_status "Database $db exists ✓"
        else
            print_warning "Database $db not found"
        fi
    done
    
    # Check if seed data was inserted
    tenant_count=$(docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T postgres psql -U qeep_tenant_user -d qeep_tenant -tAc "SELECT COUNT(*) FROM tenants;" 2>/dev/null || echo "0")
    if [ "$tenant_count" -gt 0 ]; then
        print_status "Seed data found: $tenant_count tenant(s) ✓"
    else
        print_warning "No seed data found in tenants table"
    fi
    
    user_count=$(docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T postgres psql -U qeep_user_user -d qeep_user -tAc "SELECT COUNT(*) FROM users;" 2>/dev/null || echo "0")
    if [ "$user_count" -gt 0 ]; then
        print_status "Seed data found: $user_count user(s) ✓"
    else
        print_warning "No seed data found in users table"
    fi
    
    customer_count=$(docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T postgres psql -U qeep_customer_user -d qeep_customer -tAc "SELECT COUNT(*) FROM customers;" 2>/dev/null || echo "0")
    if [ "$customer_count" -gt 0 ]; then
        print_status "Seed data found: $customer_count customer(s) ✓"
    else
        print_warning "No seed data found in customers table"
    fi
}

# Function to display connection information
display_connection_info() {
    echo -e "${BLUE}==============================================================================${NC}"
    echo -e "${BLUE}Development Environment Ready!${NC}"
    echo -e "${BLUE}==============================================================================${NC}"
    echo -e "${GREEN}Database Connection Information:${NC}"
    echo -e "  PostgreSQL: localhost:5432"
    echo -e "  Username: qeep_dev"
    echo -e "  Password: qeep_dev_password"
    echo ""
    echo -e "${GREEN}Service Databases:${NC}"
    echo -e "  - qeep_tenant (tenant-service)"
    echo -e "  - qeep_user (user-service)"
    echo -e "  - qeep_auth (auth-service)"
    echo -e "  - qeep_customer (customer-service)"
    echo -e "  - qeep_transaction (transaction-service)"
    echo -e "  - qeep_aml (aml-service)"
    echo -e "  - qeep_surveillance (surveillance-service)"
    echo -e "  - qeep_integration (integration-service)"
    echo -e "  - qeep_monitoring (monitoring-service)"
    echo ""
    echo -e "${GREEN}Seed Data:${NC}"
    echo -e "  - Tenant: ACME Inc. (ID: 550e8400-e29b-41d4-a716-446655440000)"
    echo -e "  - Admin User: <EMAIL>"
    echo -e "  - Compliance User: <EMAIL>"
    echo -e "  - Sample Customers: John Doe, Tech Solutions LLC"
    echo ""
    echo -e "${GREEN}Next Steps:${NC}"
    echo -e "  1. Start the customer-service: npm run start:dev customer-service"
    echo -e "  2. Test customer data ingestion APIs"
    echo -e "  3. Run customer data workflow tests"
    echo -e "${BLUE}==============================================================================${NC}"
}

# Main execution
main() {
    print_status "Starting development environment reset..."
    
    # Check prerequisites
    check_docker
    
    # Cleanup existing environment
    cleanup_containers
    cleanup_volumes
    cleanup_networks
    
    # Start fresh infrastructure
    start_infrastructure
    wait_for_services
    
    # Setup databases and seed data
    setup_databases
    
    # Verify everything is working
    verify_setup
    
    # Display connection information
    display_connection_info
    
    print_status "Development environment reset completed successfully! 🎉"
}

# Run main function
main "$@"
