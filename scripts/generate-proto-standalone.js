#!/usr/bin/env node

/**
 * Standalone proto generation script that doesn't depend on NX
 * This script can be run independently to generate TypeScript types from proto files
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const WORKSPACE_ROOT = path.join(__dirname, '..');
const PROTO_DIR = path.join(WORKSPACE_ROOT, 'libs', 'proto', 'src', 'proto');
const GENERATED_DIR = path.join(WORKSPACE_ROOT, 'libs', 'proto', 'src', 'generated');
const PROTOC_PLUGIN = path.join(WORKSPACE_ROOT, 'node_modules', '.bin', 'protoc-gen-ts_proto');

/**
 * Check if protoc is available
 */
function checkProtoc() {
  try {
    execSync('protoc --version', { stdio: 'pipe' });
    return true;
  } catch (error) {
    console.error('❌ protoc is not installed or not in PATH');
    console.error('Please install Protocol Buffer Compiler (protoc)');
    return false;
  }
}

/**
 * Check if ts-proto plugin is available
 */
function checkTsProtoPlugin() {
  if (!fs.existsSync(PROTOC_PLUGIN)) {
    console.error('❌ ts-proto plugin not found');
    console.error('Please run: pnpm install');
    return false;
  }
  return true;
}

/**
 * Find all proto files
 */
function findProtoFiles() {
  const protoFiles = [];

  function scanDirectory(dir, relativePath = '') {
    if (!fs.existsSync(dir)) return;

    const items = fs.readdirSync(dir);
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const itemRelativePath = relativePath ? path.join(relativePath, item) : item;
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        scanDirectory(fullPath, itemRelativePath);
      } else if (item.endsWith('.proto')) {
        protoFiles.push(itemRelativePath);
      }
    }
  }

  scanDirectory(PROTO_DIR);
  return protoFiles;
}

/**
 * Generate TypeScript types from proto files
 */
function generateTypes() {
  console.log('🔄 Generating TypeScript types from proto files...');

  // Ensure generated directory exists
  if (!fs.existsSync(GENERATED_DIR)) {
    fs.mkdirSync(GENERATED_DIR, { recursive: true });
  }

  // Find all proto files
  const protoFiles = findProtoFiles();
  if (protoFiles.length === 0) {
    console.log('⚠️  No proto files found');
    return false;
  }

  console.log(`📋 Found ${protoFiles.length} proto files:`);
  protoFiles.forEach((file) => console.log(`   - ${file}`));

  // Build protoc command
  const protocCmd = [
    'protoc',
    `--plugin=${PROTOC_PLUGIN}`,
    `--ts_proto_out=${GENERATED_DIR}`,
    '--ts_proto_opt=nestJs=true,outputServices=grpc-js,env=node,esModuleInterop=true,stringEnums=true,useOptionals=messages,exportCommonSymbols=false,outputIndex=true,useDate=true,forceLong=string,useExactTypes=false',
    `--proto_path=${PROTO_DIR}`,
    ...protoFiles,
  ].join(' ');

  try {
    console.log('🔧 Running protoc...');
    execSync(protocCmd, {
      stdio: 'inherit',
      cwd: WORKSPACE_ROOT,
    });

    console.log('✅ Proto types generated successfully');
    return true;
  } catch (error) {
    console.error('❌ Failed to generate proto types:', error.message);
    return false;
  }
}

/**
 * Update exports index file
 */
function updateExports() {
  console.log('🔄 Updating exports...');

  try {
    const updateScript = path.join(__dirname, 'update-proto-exports.js');
    execSync(`node ${updateScript}`, {
      stdio: 'inherit',
      cwd: WORKSPACE_ROOT,
    });

    console.log('✅ Exports updated successfully');
    return true;
  } catch (error) {
    console.error('❌ Failed to update exports:', error.message);
    return false;
  }
}

/**
 * Main execution
 */
function main() {
  console.log('🚀 Standalone Proto Type Generation');
  console.log('=====================================');

  // Check prerequisites
  if (!checkProtoc()) {
    process.exit(1);
  }

  if (!checkTsProtoPlugin()) {
    process.exit(1);
  }

  // Generate types
  const generateSuccess = generateTypes();
  if (!generateSuccess) {
    process.exit(1);
  }

  // Update exports
  const exportSuccess = updateExports();
  if (!exportSuccess) {
    process.exit(1);
  }

  console.log('');
  console.log('🎉 Proto type generation completed successfully!');
  console.log(`📁 Generated files are in: ${GENERATED_DIR}`);
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  checkProtoc,
  checkTsProtoPlugin,
  findProtoFiles,
  generateTypes,
  updateExports,
};
