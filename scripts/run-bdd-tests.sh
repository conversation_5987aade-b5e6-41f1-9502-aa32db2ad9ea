#!/bin/bash

# Qeep BDD Tests Runner
# This script runs BDD tests using Cucumber.js

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BASE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
REPORTS_DIR="$BASE_DIR/reports"

echo -e "${BLUE}🥒 Qeep BDD Test Suite${NC}"
echo "=================================="

# Create reports directory if it doesn't exist
mkdir -p "$REPORTS_DIR"

# Check if Cucumber is available
if ! command -v npx &> /dev/null; then
    echo -e "${RED}❌ npx is not available. Please install Node.js and npm.${NC}"
    exit 1
fi

# Function to check if API is running
check_api_health() {
    echo -e "${YELLOW}🔍 Checking API health...${NC}"
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s http://localhost:8080/api/v1/health > /dev/null 2>&1; then
            echo -e "${GREEN}✅ API is running and healthy${NC}"
            return 0
        fi
        
        echo "Attempt $attempt/$max_attempts: API not ready, waiting..."
        sleep 2
        ((attempt++))
    done
    
    echo -e "${RED}❌ API is not running or not healthy after $max_attempts attempts${NC}"
    echo -e "${YELLOW}💡 Make sure to start the API with: pnpm run dev:start${NC}"
    return 1
}

# Function to run BDD tests
run_bdd_tests() {
    local profile=${1:-"default"}
    local tags=${2:-""}

    echo -e "${YELLOW}🧪 Running BDD tests with profile: $profile${NC}"

    # Set environment variables
    export API_BASE_URL="http://localhost:8080/api/v1"
    export NODE_ENV="test"
    export CUCUMBER_PROFILE="$profile"
    export NODE_OPTIONS="--loader=ts-node/esm --experimental-specifier-resolution=node"
    export TS_NODE_PROJECT="tests/bdd/tsconfig.json"

    # Build the cucumber command with proper tag filtering
    local cucumber_cmd="npx @cucumber/cucumber --config cucumber.config.js"

    if [ "$profile" != "default" ]; then
        cucumber_cmd="$cucumber_cmd --profile $profile"
    fi

    if [ -n "$tags" ]; then
        echo -e "${BLUE}🏷️  Filtering tests with tags: $tags${NC}"
        cucumber_cmd="$cucumber_cmd --tags '$tags'"
        export CUCUMBER_TAGS="$tags"
    fi

    echo -e "${BLUE}🔧 Running command: $cucumber_cmd${NC}"

    # Run Cucumber tests
    eval "$cucumber_cmd"
}

# Parse command line arguments
PROFILE="default"
TAGS=""
CHECK_API=true
HELP=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--profile)
            PROFILE="$2"
            shift 2
            ;;
        -t|--tags)
            TAGS="$2"
            shift 2
            ;;
        --tags=*)
            TAGS="${1#*=}"
            shift
            ;;
        --no-health-check)
            CHECK_API=false
            shift
            ;;
        -h|--help)
            HELP=true
            shift
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            HELP=true
            shift
            ;;
    esac
done

# Show help
if [ "$HELP" = true ]; then
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -p, --profile PROFILE    Cucumber profile to use (default, auth, ci)"
    echo "  -t, --tags TAGS         Tags to filter scenarios"
    echo "  --no-health-check       Skip API health check"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Run all tests"
    echo "  $0 --profile auth                     # Run only auth tests"
    echo "  $0 --tags '@smoke'                    # Run only smoke tests"
    echo "  $0 --tags '@auth and @happy-path'     # Run auth happy path tests"
    echo "  $0 --tags '@me-endpoint'              # Run only /auth/me endpoint tests"
    echo "  $0 --tags '@me-endpoint and @security'# Run /auth/me security tests"
    echo "  $0 --tags '@me-endpoint and @happy-path' # Run /auth/me happy path tests"
    echo "  $0 --tags '@verification and @core'   # Run core verification tests"
    echo "  $0 --no-health-check                 # Skip health check"
    echo ""
    exit 0
fi

# Check API health unless skipped
if [ "$CHECK_API" = true ]; then
    if ! check_api_health; then
        exit 1
    fi
fi

# Run the tests
echo -e "${YELLOW}🚀 Starting BDD test execution...${NC}"

if run_bdd_tests "$PROFILE" "$TAGS"; then
    echo -e "${GREEN}✅ BDD tests completed successfully!${NC}"
    echo -e "${BLUE}📊 Reports available in: $REPORTS_DIR${NC}"
    
    # List generated reports
    if [ -d "$REPORTS_DIR" ]; then
        echo -e "${BLUE}Generated reports:${NC}"
        find "$REPORTS_DIR" -name "bdd-*.html" -o -name "bdd-*.json" | while read -r file; do
            echo "  - $(basename "$file")"
        done
    fi
    
    exit 0
else
    echo -e "${RED}❌ BDD tests failed!${NC}"
    echo -e "${YELLOW}💡 Check the reports for details: $REPORTS_DIR${NC}"
    exit 1
fi
