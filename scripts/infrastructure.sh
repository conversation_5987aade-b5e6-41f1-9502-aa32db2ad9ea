#!/bin/bash

# Qeep Infrastructure Management Script
# This script manages Docker infrastructure services (PostgreSQL, Redis, Kafka, etc.)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="infrastructure/docker/compose/docker-compose.dev.infrastructure.yml"
ENV_FILE=".env.development"
PROJECT_NAME="qeep"

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required files exist
check_prerequisites() {
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        log_error "Docker Compose file not found: $COMPOSE_FILE"
        exit 1
    fi

    if [[ ! -f "$ENV_FILE" ]]; then
        log_error "Environment file not found: $ENV_FILE"
        exit 1
    fi
}

# Function to run database setup for all services
run_database_setup() {
    log_info "Setting up databases and running migrations for all services..."

    # Function to get database URL for a service
    get_db_url() {
        case "$1" in
            "auth-service")
                echo "postgresql://qeep_auth_user:qeep_auth_password@localhost:5433/qeep_auth?schema=public"
                ;;
            "user-service")
                echo "postgresql://qeep_user_user:qeep_user_password@localhost:5433/qeep_user?schema=public"
                ;;
            "tenant-service")
                echo "postgresql://qeep_tenant_user:qeep_tenant_password@localhost:5433/qeep_tenant?schema=public"
                ;;
            "notification-service")
                echo "postgresql://qeep_notification_user:qeep_notification_password@localhost:5433/qeep_notification?schema=public"
                ;;
            "audit-service")
                echo "postgresql://qeep_audit_user:qeep_audit_password@localhost:5433/qeep_audit?schema=public"
                ;;
            "customer-service")
                echo "postgresql://qeep_customer_user:qeep_customer_password@localhost:5433/qeep_customer?schema=public"
                ;;
        esac
    }

    # Function to get environment variable name for a service
    get_env_var() {
        case "$1" in
            "auth-service")
                echo "AUTH_DATABASE_URL"
                ;;
            "user-service")
                echo "USER_DATABASE_URL"
                ;;
            "tenant-service")
                echo "TENANT_DATABASE_URL"
                ;;
            "notification-service")
                echo "NOTIFICATION_DATABASE_URL"
                ;;
            "audit-service")
                echo "AUDIT_DATABASE_URL"
                ;;
            "customer-service")
                echo "CUSTOMER_DATABASE_URL"
                ;;
        esac
    }

    # Services with Prisma configurations
    local services=("auth-service" "user-service" "tenant-service" "notification-service" "audit-service" "customer-service")

    for service in "${services[@]}"; do
        local service_path="apps/$service"
        local prisma_path="$service_path/prisma"
        local db_url=$(get_db_url "$service")
        local env_var=$(get_env_var "$service")

        if [[ -d "$prisma_path" ]]; then
            log_info "Setting up database for $service..."

            # Generate Prisma client
            if (cd "$service_path" && export "$env_var=$db_url" && npx prisma generate >/dev/null 2>&1); then
                log_success "Generated Prisma client for $service"
            else
                log_warning "Failed to generate Prisma client for $service"
                continue
            fi

            # Push schema to database (creates tables)
            if (cd "$service_path" && export "$env_var=$db_url" && npx prisma db push >/dev/null 2>&1); then
                log_success "Database schema pushed for $service"
            else
                log_warning "Failed to push database schema for $service"
                continue
            fi

            # Run seed if seed file exists
            if [[ -f "$prisma_path/seed.ts" ]]; then
                if (export "$env_var=$db_url" && npx ts-node --project tsconfig.base.json "$service_path/prisma/seed.ts" >/dev/null 2>&1); then
                    log_success "Database seeded for $service"
                else
                    log_warning "Failed to seed database for $service"
                fi
            fi
        else
            log_warning "No Prisma configuration found for $service"
        fi
    done

    log_success "Database setup completed for all services!"
}

# Function to start infrastructure services
start_infrastructure() {
    log_info "Starting Qeep infrastructure services..."
    check_prerequisites

    # Start core infrastructure (postgres, redis, mailpit, nginx)
    docker compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" --env-file "$ENV_FILE" up -d postgres redis mailpit nginx kafka
    
    # Wait for services to be healthy
    log_info "Waiting for infrastructure services to be ready..."
    
    # Wait for PostgreSQL
    local postgres_ready=false
    local attempts=0
    local max_attempts=30
    
    while [[ $postgres_ready == false && $attempts -lt $max_attempts ]]; do
        if docker exec qeep-postgres-dev pg_isready -U qeep_dev -d qeep_dev >/dev/null 2>&1; then
            postgres_ready=true
            log_success "PostgreSQL is ready"
        else
            ((attempts++))
            echo -n "."
            sleep 2
        fi
    done
    
    if [[ $postgres_ready == false ]]; then
        log_error "PostgreSQL failed to start within expected time"
        exit 1
    fi
    
    # Wait for Redis
    local redis_ready=false
    attempts=0
    
    while [[ $redis_ready == false && $attempts -lt $max_attempts ]]; do
        if docker exec qeep-redis-dev redis-cli -a redis_dev_password ping >/dev/null 2>&1; then
            redis_ready=true
            log_success "Redis is ready"
        else
            ((attempts++))
            echo -n "."
            sleep 2
        fi
    done
    
    if [[ $redis_ready == false ]]; then
        log_error "Redis failed to start within expected time"
        exit 1
    fi

    # Wait for Mailpit
    log_info "Waiting for Mailpit to be ready..."
    local mailpit_ready=false
    attempts=0

    while [[ $mailpit_ready == false && $attempts -lt $max_attempts ]]; do
        if curl -s http://localhost:8026/api/v1/info >/dev/null 2>&1; then
            mailpit_ready=true
            log_success "Mailpit is ready"
        else
            ((attempts++))
            echo -n "."
            sleep 2
        fi
    done

    if [[ $mailpit_ready == false ]]; then
        log_warning "Mailpit failed to start within expected time (non-critical)"
    fi

    # Wait for Nginx
    local nginx_ready=false
    attempts=0

    while [[ $nginx_ready == false && $attempts -lt $max_attempts ]]; do
        if docker exec qeep-nginx-dev nginx -t >/dev/null 2>&1; then
            nginx_ready=true
            log_success "Nginx is ready"
        else
            ((attempts++))
            echo -n "."
            sleep 2
        fi
    done

    if [[ $nginx_ready == false ]]; then
        log_warning "Nginx may not be fully ready, but continuing..."
    fi

    log_success "Infrastructure services started successfully!"

    # Automatically run database setup
    echo ""
    log_info "Setting up databases for all services..."
    run_database_setup
    echo ""

    # Display service information
    echo ""
    log_info "Infrastructure Services Status:"
    echo "  🐘 PostgreSQL: localhost:${POSTGRES_PORT:-5433}"
    echo "  🔴 Redis: localhost:6379"
    echo "  📧 Mailpit: localhost:8026 (Web UI) | localhost:1026 (SMTP)"
    echo "  🌐 Nginx Proxy: localhost:${NGINX_PORT:-8080}"
    echo ""
    log_info "Database Status:"
    echo "  📊 Auth Database: qeep_auth (user: qeep_auth_user)"
    echo "  👤 User Database: qeep_user (user: qeep_user_user)"
    echo "  🏢 Tenant Database: qeep_tenant (user: qeep_tenant_user)"
    echo "  📧 Notification Database: qeep_notification (user: qeep_notification_user)"
    echo "  📋 Audit Database: qeep_audit (user: qeep_audit_user)"
    echo ""
    log_info "Next steps:"
    echo "  1. Run 'pnpm run dev:services:start' to start application services"
    echo "  2. Or run individual services with 'nx serve <service-name>'"
    echo "  3. Access API Gateway at: http://localhost:${NGINX_PORT:-8080}"
    echo "  4. View emails at: http://localhost:8026 (Mailpit Web UI)"
    echo ""
}

# Function to start infrastructure with monitoring
start_with_monitoring() {
    log_info "Starting Qeep infrastructure with monitoring services..."
    check_prerequisites
    
    docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" --profile monitoring up -d
    
    log_success "Infrastructure and monitoring services started!"
    log_info "Nginx Proxy: localhost:${NGINX_PORT:-8080}"
    log_info "PostgreSQL: localhost:5432"
    log_info "Redis: localhost:6379"
    log_info "Prometheus: localhost:9090"
    log_info "Grafana: localhost:3001 (admin/grafana_admin_password)"
    log_info "Jaeger: localhost:16686"
}

# Function to stop infrastructure services
stop_infrastructure() {
    log_info "Stopping Qeep infrastructure services..."
    docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down
    log_success "Infrastructure services stopped successfully!"
}

# Function to restart infrastructure services
restart_infrastructure() {
    log_info "Restarting Qeep infrastructure services..."
    stop_infrastructure
    start_infrastructure
}

# Function to show infrastructure status
show_status() {
    log_info "Qeep infrastructure status:"
    docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps
}

# Function to show infrastructure logs
show_logs() {
    if [[ -n "$1" ]]; then
        docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs -f "$1"
    else
        docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs -f
    fi
}

# Function to clean up infrastructure
cleanup() {
    log_warning "This will remove all infrastructure containers and volumes. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "Cleaning up Qeep infrastructure..."
        docker compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down -v --remove-orphans
        log_success "Infrastructure cleanup completed!"
    else
        log_info "Cleanup cancelled."
    fi
}

# Function to show help
show_help() {
    echo "Qeep Infrastructure Management"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start                  Start core infrastructure (PostgreSQL, Redis)"
    echo "  start-monitoring       Start infrastructure with monitoring services"
    echo "  stop                   Stop all infrastructure services"
    echo "  restart                Restart all infrastructure services"
    echo "  status                 Show status of infrastructure services"
    echo "  logs [service]         Show logs for all services or specific service"
    echo "  cleanup                Remove all containers and volumes"
    echo "  help                   Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start               # Start PostgreSQL and Redis"
    echo "  $0 logs postgres       # Show PostgreSQL logs"
    echo "  $0 start-monitoring    # Start with Prometheus, Grafana, Jaeger"
    echo ""
}

# Main script logic
case "${1:-help}" in
    start)
        start_infrastructure
        ;;
    start-monitoring)
        start_with_monitoring
        ;;
    stop)
        stop_infrastructure
        ;;
    restart)
        restart_infrastructure
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "$2"
        ;;
    cleanup)
        cleanup
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        log_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
