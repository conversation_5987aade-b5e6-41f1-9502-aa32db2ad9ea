#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to automatically update proto exports after code generation
 * This script scans the generated proto files and updates the index.ts exports
 * with automatic conflict resolution for duplicate export names
 */

const fs = require('fs');
const path = require('path');

const GENERATED_DIR = path.join(__dirname, '..', 'libs', 'proto', 'src', 'generated');
const INDEX_FILE = path.join(GENERATED_DIR, 'index.ts');

// Module name mapping for creating meaningful aliases
const MODULE_ALIASES = {
  auth: 'Auth',
  common: 'Common',
  health: 'Health',
  user: 'User',
  tenant: 'Tenant',
  notification: 'Notification',
  audit: 'Audit',
};

/**
 * Recursively find all .ts files in a directory
 */
function findTsFiles(dir, basePath = '') {
  const files = [];

  if (!fs.existsSync(dir)) {
    return files;
  }

  const items = fs.readdirSync(dir);

  for (const item of items) {
    const fullPath = path.join(dir, item);
    const relativePath = basePath ? path.join(basePath, item) : item;
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      files.push(...findTsFiles(fullPath, relativePath));
    } else if (item.endsWith('.ts') && item !== 'index.ts') {
      // Remove .ts extension and normalize path separators
      const exportPath = relativePath.replace(/\.ts$/, '').replace(/\\/g, '/');
      files.push(exportPath);
    }
  }

  return files;
}

/**
 * Extract export names from a TypeScript file
 */
function extractExports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const exports = new Set();

    // Match export declarations
    const exportPatterns = [
      /export\s+(?:interface|enum|class|type|const|function)\s+([A-Za-z_$][A-Za-z0-9_$]*)/g,
      /export\s*\{\s*([^}]+)\s*\}/g,
      /export\s+default\s+(?:interface|enum|class|type|const|function)\s+([A-Za-z_$][A-Za-z0-9_$]*)/g,
    ];

    for (const pattern of exportPatterns) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        if (pattern.source.includes('\\{')) {
          // Handle export { ... } syntax
          const exportList = match[1];
          const names = exportList
            .split(',')
            .map((name) => {
              const trimmed = name.trim();
              // Handle "name as alias" syntax - we want the original name
              const asIndex = trimmed.indexOf(' as ');
              return asIndex > -1 ? trimmed.substring(0, asIndex).trim() : trimmed;
            })
            .filter((name) => name && !name.includes('*'));

          names.forEach((name) => exports.add(name));
        } else {
          // Handle direct export declarations
          exports.add(match[1]);
        }
      }
    }

    return Array.from(exports);
  } catch (error) {
    console.warn(`Warning: Could not analyze exports from ${filePath}:`, error.message);
    return [];
  }
}

/**
 * Analyze all modules and detect export conflicts
 */
function analyzeModules() {
  const tsFiles = findTsFiles(GENERATED_DIR);
  const modules = [];
  const exportCounts = new Map();

  for (const file of tsFiles) {
    const fullPath = path.join(GENERATED_DIR, `${file}.ts`);
    const exports = extractExports(fullPath);

    // Extract module name from path (e.g., 'auth/auth' -> 'auth')
    const pathParts = file.split('/');
    const moduleName = pathParts[0];

    const moduleInfo = {
      path: file,
      moduleName,
      exports,
      fullPath,
    };

    modules.push(moduleInfo);

    // Count export occurrences
    for (const exportName of exports) {
      if (!exportCounts.has(exportName)) {
        exportCounts.set(exportName, []);
      }
      exportCounts.get(exportName).push(moduleInfo);
    }
  }

  // Find conflicts
  const conflicts = new Map();
  for (const [exportName, moduleList] of exportCounts) {
    if (moduleList.length > 1) {
      conflicts.set(exportName, moduleList);
    }
  }

  return { modules, conflicts };
}

/**
 * Generate alias name for a conflicting export
 */
function generateAlias(exportName, moduleName) {
  const moduleAlias = MODULE_ALIASES[moduleName] || moduleName.charAt(0).toUpperCase() + moduleName.slice(1);
  return `${exportName} as ${moduleAlias}${exportName}`;
}

/**
 * Generate exports for a module, handling conflicts
 */
function generateModuleExports(moduleInfo, conflicts) {
  const { path: modulePath, exports, moduleName } = moduleInfo;
  const conflictingExports = [];
  const regularExports = [];

  for (const exportName of exports) {
    if (conflicts.has(exportName)) {
      conflictingExports.push(exportName);
    } else {
      regularExports.push(exportName);
    }
  }

  const lines = [];

  if (conflictingExports.length > 0 || regularExports.length > 0) {
    lines.push(`// Export ${moduleName} module${conflictingExports.length > 0 ? ' - with conflict resolution' : ''}`);
    lines.push('export {');

    // Add regular exports
    if (regularExports.length > 0) {
      const sortedRegular = regularExports.sort();
      lines.push(`  ${sortedRegular.join(',\n  ')},`);
    }

    // Add conflicting exports with aliases
    if (conflictingExports.length > 0) {
      lines.push('  // Aliased exports to resolve conflicts');
      const aliasedExports = conflictingExports.sort().map((exportName) => `  ${generateAlias(exportName, moduleName)}`);
      lines.push(aliasedExports.join(',\n'));
    }

    lines.push(`} from './${modulePath}';`);
  }

  return lines;
}

/**
 * Generate the index.ts content with conflict resolution
 */
function generateIndexContent() {
  const { modules, conflicts } = analyzeModules();

  if (modules.length === 0) {
    return `// Generated TypeScript interfaces from proto files
// This file will be populated when running: nx run proto:generate-types

// No generated files found yet
export {};
`;
  }

  const lines = [
    '// Generated TypeScript interfaces from proto files',
    '// This file is automatically updated by scripts/update-proto-exports.js',
    '// Do not edit manually - changes will be overwritten',
    '',
  ];

  // Log conflicts for debugging
  if (conflicts.size > 0) {
    lines.push('// Export conflicts detected and resolved with aliases:');
    for (const [exportName, moduleList] of conflicts) {
      const moduleNames = moduleList.map((m) => m.moduleName).join(', ');
      lines.push(`// - ${exportName} (found in: ${moduleNames})`);
    }
    lines.push('');
  }

  // Generate exports for each module
  const sortedModules = modules.sort((a, b) => a.moduleName.localeCompare(b.moduleName));

  for (const moduleInfo of sortedModules) {
    const moduleExports = generateModuleExports(moduleInfo, conflicts);
    if (moduleExports.length > 0) {
      lines.push(...moduleExports);
      lines.push('');
    }
  }

  // Add note about excluded files if any exist
  const excludedFiles = findTsFiles(GENERATED_DIR).filter((file) => file.startsWith('index.') && file !== 'index');

  if (excludedFiles.length > 0) {
    lines.push('// Note: The following generated index files are excluded to prevent conflicts:');
    excludedFiles.forEach((file) => {
      lines.push(`// - ${file}.ts`);
    });
    lines.push('// All necessary exports are handled explicitly above');
  }

  return lines.join('\n') + '\n';
}

/**
 * Main execution
 */
function main() {
  try {
    console.log('🔄 Updating proto exports with conflict resolution...');

    // Ensure the generated directory exists
    if (!fs.existsSync(GENERATED_DIR)) {
      fs.mkdirSync(GENERATED_DIR, { recursive: true });
    }

    // Analyze modules and conflicts
    const { modules, conflicts } = analyzeModules();

    // Generate and write the index content
    const content = generateIndexContent();
    fs.writeFileSync(INDEX_FILE, content, 'utf8');

    console.log('✅ Proto exports updated successfully');
    console.log(`📁 Generated index file: ${INDEX_FILE}`);

    // Log found files and modules
    if (modules.length > 0) {
      console.log('📋 Processed modules:');
      modules.forEach((module) => {
        console.log(`   - ${module.moduleName}: ${module.exports.length} exports`);
      });
    } else {
      console.log('⚠️  No TypeScript files found in generated directory');
    }

    // Log conflicts and resolutions
    if (conflicts.size > 0) {
      console.log('⚠️  Export conflicts detected and resolved:');
      for (const [exportName, moduleList] of conflicts) {
        const moduleNames = moduleList.map((m) => m.moduleName);
        console.log(`   - ${exportName} (conflicts in: ${moduleNames.join(', ')})`);
        moduleList.forEach((module) => {
          const alias = generateAlias(exportName, module.moduleName);
          console.log(`     → ${module.moduleName}: ${alias}`);
        });
      }
    } else {
      console.log('✅ No export conflicts detected');
    }
  } catch (error) {
    console.error('❌ Error updating proto exports:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  findTsFiles,
  generateIndexContent,
  extractExports,
  analyzeModules,
  generateModuleExports,
  generateAlias,
};
