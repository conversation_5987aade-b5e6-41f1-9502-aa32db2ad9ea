#!/bin/bash

# =============================================================================
# Qeep All Services Seeding Script
# =============================================================================
# This script seeds all services in the correct order to ensure proper
# data relationships and dependencies are maintained
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

echo -e "${BLUE}==============================================================================${NC}"
echo -e "${BLUE}Qeep All Services Database Seeding${NC}"
echo -e "${BLUE}==============================================================================${NC}"

# Function to print status messages
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to seed a specific service
seed_service() {
    local service=$1
    local service_path="$PROJECT_ROOT/apps/$service"
    local seed_file="$service_path/prisma/seed.ts"
    
    if [ ! -f "$seed_file" ]; then
        print_warning "No seed file found for $service, skipping..."
        return 0
    fi
    
    print_status "Seeding $service..."
    
    cd "$service_path"
    
    if npx ts-node prisma/seed.ts; then
        print_status "$service seeded successfully ✓"
        return 0
    else
        print_error "Failed to seed $service"
        return 1
    fi
}

# Function to check if databases are ready
check_databases() {
    print_status "Checking database connectivity..."
    
    # Use the db-manager to check status
    cd "$PROJECT_ROOT"
    
    if node scripts/db-manager.js status > /dev/null 2>&1; then
        print_status "All databases are accessible ✓"
        return 0
    else
        print_error "Some databases are not accessible"
        print_status "Please ensure infrastructure is running: docker-compose up -d"
        return 1
    fi
}

# Function to run migrations first
run_migrations() {
    print_status "Running database migrations for all services..."
    
    cd "$PROJECT_ROOT"
    
    if node scripts/db-manager.js migrate; then
        print_status "All migrations completed successfully ✓"
        return 0
    else
        print_error "Migration failed"
        return 1
    fi
}

# Function to generate Prisma clients
generate_clients() {
    print_status "Generating Prisma clients for all services..."
    
    cd "$PROJECT_ROOT"
    
    if node scripts/db-manager.js generate; then
        print_status "All Prisma clients generated successfully ✓"
        return 0
    else
        print_error "Client generation failed"
        return 1
    fi
}

# Main seeding function
main() {
    print_status "Starting comprehensive database seeding..."
    
    # Check prerequisites
    if ! check_databases; then
        exit 1
    fi
    
    # Run migrations first
    if ! run_migrations; then
        exit 1
    fi
    
    # Generate Prisma clients
    if ! generate_clients; then
        exit 1
    fi
    
    # Seed services in dependency order
    print_status "Seeding services in dependency order..."
    
    # 1. Tenant service (foundational data)
    if ! seed_service "tenant-service"; then
        print_error "Failed to seed tenant-service - this is required for other services"
        exit 1
    fi
    
    # 2. User service (creates users and RBAC)
    if ! seed_service "user-service"; then
        print_error "Failed to seed user-service - this is required for auth service"
        exit 1
    fi
    
    # 3. Auth service (depends on users existing)
    if ! seed_service "auth-service"; then
        print_warning "Auth service seeding failed, but continuing..."
    fi
    
    # 4. Notification service (independent)
    if ! seed_service "notification-service"; then
        print_warning "Notification service seeding failed, but continuing..."
    fi
    
    # 5. Customer service (depends on tenant)
    if ! seed_service "customer-service"; then
        print_warning "Customer service seeding failed, but continuing..."
    fi
    
    # 6. Audit service (independent, mainly for structure)
    if ! seed_service "audit-service"; then
        print_warning "Audit service seeding failed, but continuing..."
    fi
    
    print_status "Database seeding completed!"
    
    # Display summary
    echo -e "${BLUE}==============================================================================${NC}"
    echo -e "${BLUE}Seeding Summary${NC}"
    echo -e "${BLUE}==============================================================================${NC}"
    echo -e "${GREEN}✅ Tenant Service:${NC}"
    echo -e "   - Ronna Bank tenant created with full AML configuration"
    echo -e "   - Website: ronna.com.gh"
    echo -e "   - Additional Ghanaian financial institutions available"
    echo ""
    echo -e "${GREEN}✅ User Service:${NC}"
    echo -e "   - Complete RBAC system with 5 roles and 40+ permissions"
    echo -e "   - 5 comprehensive test users for all roles:"
    echo -e "     • <EMAIL> (super_admin role)"
    echo -e "     • <EMAIL> (admin role)"
    echo -e "     • <EMAIL> (manager role)"
    echo -e "     • <EMAIL> (support role)"
    echo -e "     • <EMAIL> (user role)"
    echo ""
    echo -e "${GREEN}✅ Auth Service:${NC}"
    echo -e "   - Role-based security configurations for all users"
    echo -e "   - MFA enabled for super_admin and admin roles"
    echo -e "   - Progressive security policies by role level"
    echo -e "   - Ghana-specific security questions and settings"
    echo ""
    echo -e "${GREEN}✅ Customer Service:${NC}"
    echo -e "   - Ghanaian customers with various risk levels"
    echo -e "   - Individual customers: Kwame Nkrumah, Ama Serwaa, Kofi Mensah"
    echo -e "   - Business customers: Ashanti Gold Mining Company"
    echo -e "   - KYC verification statuses and Ghana Card integration"
    echo ""
    echo -e "${GREEN}✅ Notification Service:${NC}"
    echo -e "   - Email templates for user workflows"
    echo -e "   - Welcome, verification, and password reset templates"
    echo ""
    echo -e "${GREEN}✅ Audit Service:${NC}"
    echo -e "   - Ready to collect audit events from all services"
    echo ""
    echo -e "${GREEN}🚀 Ready for Development:${NC}"
    echo -e "   - Customer data ingestion testing"
    echo -e "   - AML workflow development"
    echo -e "   - User authentication flows"
    echo -e "   - Multi-tenant operations"
    echo ""
    echo -e "${GREEN}🔑 Test Credentials (all follow pattern {role}123):${NC}"
    echo -e "   - <EMAIL> / superadmin123"
    echo -e "   - <EMAIL> / admin123"
    echo -e "   - <EMAIL> / manager123"
    echo -e "   - <EMAIL> / support123"
    echo -e "   - <EMAIL> / user123"
    echo ""
    echo -e "${GREEN}🔐 MFA Test Secrets:${NC}"
    echo -e "   - <EMAIL>: JBSWY3DPEHPK3PXP"
    echo -e "   - <EMAIL>: KBSWY3DPEHPK3PXQ"
    echo -e "${BLUE}==============================================================================${NC}"
}

# Run main function
main "$@"
