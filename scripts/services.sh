#!/bin/bash

# Qeep Services Management Script
# This script manages individual NestJS services running locally

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVICES=("audit-service" "auth-service" "customer-service" "integration-service" "monitoring-service" "notification-service" "surveillance-service" "tenant-service" "aml-service" "transaction-service" "user-service")
PID_DIR="tmp/pids"
LOG_DIR="tmp/logs"

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create necessary directories
setup_directories() {
    mkdir -p "$PID_DIR"
    mkdir -p "$LOG_DIR"
}

# Function to check if a service is running
is_service_running() {
    local service=$1
    local pid_file="$PID_DIR/$service.pid"
    
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            # PID file exists but process is dead, clean up
            rm -f "$pid_file"
            return 1
        fi
    fi
    return 1
}

# Function to start a single service
start_service() {
    local service=$1
    local pid_file="$PID_DIR/$service.pid"
    local log_file="$LOG_DIR/$service.log"
    
    if is_service_running "$service"; then
        log_warning "$service is already running"
        return 0
    fi
    
    log_info "Starting $service..."
    
    # Clear the log file and start the service in background
    > "$log_file"  # Clear/create the log file
    nohup npx nx serve "$service" --output-style=stream > "$log_file" 2>&1 &
    local pid=$!
    
    # Save PID to file
    echo "$pid" > "$pid_file"
    
    # Wait a moment and check if the service started successfully
    sleep 3
    if is_service_running "$service"; then
        log_success "$service started successfully (PID: $pid)"
        log_info "Logs: tail -f $log_file"
    else
        log_error "Failed to start $service"
        rm -f "$pid_file"
        return 1
    fi
}

# Function to stop a single service
stop_service() {
    local service=$1
    local pid_file="$PID_DIR/$service.pid"
    
    if ! is_service_running "$service"; then
        log_warning "$service is not running"
        return 0
    fi
    
    local pid=$(cat "$pid_file")
    log_info "Stopping $service (PID: $pid)..."
    
    # Try graceful shutdown first
    kill "$pid" 2>/dev/null || true
    
    # Wait for graceful shutdown
    local attempts=0
    local max_attempts=10
    while [[ $attempts -lt $max_attempts ]] && ps -p "$pid" > /dev/null 2>&1; do
        sleep 1
        ((attempts++))
    done
    
    # Force kill if still running
    if ps -p "$pid" > /dev/null 2>&1; then
        log_warning "Force killing $service..."
        kill -9 "$pid" 2>/dev/null || true
    fi
    
    rm -f "$pid_file"
    log_success "$service stopped successfully"
}

# Function to restart a single service
restart_service() {
    local service=$1
    log_info "Restarting $service..."
    stop_service "$service"
    sleep 2
    start_service "$service"
}

# Function to start all services
start_all_services() {
    log_info "Starting all Qeep services..."
    
    # Always kill existing nx processes before starting
    kill_nx_processes
    
    setup_directories
    
    for service in "${SERVICES[@]}"; do
        start_service "$service"
        sleep 2  # Stagger service starts
    done
    
    log_success "All services started!"
    show_status
}

# Function to stop all services
stop_all_services() {
    log_info "Stopping all Qeep services..."
    
    for service in "${SERVICES[@]}"; do
        stop_service "$service"
    done
    
    # Also kill any remaining nx serve processes
    pkill -f "nx serve" 2>/dev/null || true
    
    log_success "All services stopped!"
}

# Function to restart all services
restart_all_services() {
    log_info "Restarting all Qeep services..."
    stop_all_services
    sleep 3
    start_all_services
}

# Function to show service status
show_status() {
    log_info "Qeep services status:"
    echo ""
    printf "%-20s %-10s %-10s %-50s\n" "SERVICE" "STATUS" "PID" "URL"
    printf "%-20s %-10s %-10s %-50s\n" "-------" "------" "---" "---"
    
    for service in "${SERVICES[@]}"; do
        local status="STOPPED"
        local pid="N/A"
        local url="N/A"
        
        if is_service_running "$service"; then
            status="RUNNING"
            pid=$(cat "$PID_DIR/$service.pid")
            
            case "$service" in
                "auth-service")
                    url="http://localhost:3001/api"
                    ;;
                "user-service")
                    url="http://localhost:3002/api"
                    ;;
                "tenant-service")
                    url="http://localhost:3003/api"
                    ;;
                "notification-service")
                    url="http://localhost:3004/api"
                    ;;
                "audit-service")
                    url="http://localhost:3005/api"
                    ;;
                "transaction-service")
                    url="http://localhost:3006/api"
                    ;;
                "aml-service")
                    url="http://localhost:3007/api"
                    ;;
                "surveillance-service")
                    url="http://localhost:3009/api"
                    ;;
                "monitoring-service")
                    url="http://localhost:3012/api"
                    ;;
                "integration-service")
                    url="http://localhost:3010/api"
                    ;;
                "customer-service")
                    url="http://localhost:3008/api"
                    ;;
            esac
        fi
        
        printf "%-20s %-10s %-10s %-50s\n" "$service" "$status" "$pid" "$url"
    done
    echo ""
}

# Function to show logs for a service
show_logs() {
    local service=$1
    local log_file="$LOG_DIR/$service.log"
    
    if [[ -z "$service" ]]; then
        log_error "Please specify a service name"
        log_info "Available services: ${SERVICES[*]}"
        return 1
    fi
    
    if [[ ! -f "$log_file" ]]; then
        log_error "Log file not found: $log_file"
        return 1
    fi
    
    log_info "Showing logs for $service (Press Ctrl+C to exit):"
    # Show entire log file first, then follow new entries
    cat "$log_file"
    echo "--- Following new log entries ---"
    tail -f "$log_file"
}

# Function to kill all nx processes
kill_nx_processes() {
    log_info "Killing all nx processes..."
    
    # Find and kill all nx serve processes
    local nx_pids=$(pgrep -f "nx serve" 2>/dev/null || true)
    
    if [[ -n "$nx_pids" ]]; then
        echo "$nx_pids" | while read -r pid; do
            if ps -p "$pid" > /dev/null 2>&1; then
                log_info "Killing nx process: $pid"
                kill "$pid" 2>/dev/null || true
            fi
        done
        
        # Wait a moment for graceful shutdown
        sleep 2
        
        # Force kill any remaining nx processes
        local remaining_pids=$(pgrep -f "nx serve" 2>/dev/null || true)
        if [[ -n "$remaining_pids" ]]; then
            log_warning "Force killing remaining nx processes..."
            echo "$remaining_pids" | while read -r pid; do
                kill -9 "$pid" 2>/dev/null || true
            done
        fi
        
        log_success "All nx processes killed"
    else
        log_info "No nx processes found"
    fi
    
    # Also kill any node processes that might be related to nx
    local node_nx_pids=$(pgrep -f "node.*nx" 2>/dev/null || true)
    if [[ -n "$node_nx_pids" ]]; then
        log_info "Killing related node processes..."
        echo "$node_nx_pids" | while read -r pid; do
            kill "$pid" 2>/dev/null || true
        done
    fi
}

# Function to clean up all service files
cleanup() {
    log_warning "This will stop all services and remove PID/log files. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        stop_all_services
        rm -rf "$PID_DIR" "$LOG_DIR"
        log_success "Cleanup completed!"
    else
        log_info "Cleanup cancelled."
    fi
}

# Function to show help
show_help() {
    echo "Qeep Services Management"
    echo ""
    echo "Usage: $0 [COMMAND] [SERVICE]"
    echo ""
    echo "Commands:"
    echo "  start [service]        Start all services or specific service"
    echo "  stop [service]         Stop all services or specific service"
    echo "  restart [service]      Restart all services or specific service"
    echo "  status                 Show status of all services"
    echo "  logs <service>         Show logs for specific service"
    echo "  kill-nx                Kill all nx processes (emergency stop)"
    echo "  cleanup                Stop all services and remove files"
    echo "  help                   Show this help message"
    echo ""
    echo "Available services: ${SERVICES[*]}"
    echo ""
    echo "Examples:"
    echo "  $0 start               # Start all services"
    echo "  $0 start auth-service  # Start only auth service"
    echo "  $0 logs auth-service   # Show auth service logs"
    echo "  $0 restart user-service # Restart user service"
    echo ""
}

# Validate service name if provided
validate_service() {
    local service=$1
    if [[ -n "$service" ]]; then
        for valid_service in "${SERVICES[@]}"; do
            if [[ "$service" == "$valid_service" ]]; then
                return 0
            fi
        done
        log_error "Invalid service: $service"
        log_info "Available services: ${SERVICES[*]}"
        exit 1
    fi
}

# Main script logic
case "${1:-help}" in
    start)
        if [[ -n "$2" ]]; then
            validate_service "$2"
            setup_directories
            start_service "$2"
        else
            start_all_services
        fi
        ;;
    stop)
        if [[ -n "$2" ]]; then
            validate_service "$2"
            stop_service "$2"
        else
            stop_all_services
        fi
        ;;
    restart)
        if [[ -n "$2" ]]; then
            validate_service "$2"
            restart_service "$2"
        else
            restart_all_services
        fi
        ;;
    status)
        show_status
        ;;
    logs)
        validate_service "$2"
        show_logs "$2"
        ;;
    kill-nx)
        kill_nx_processes
        ;;
    cleanup)
        cleanup
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        log_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac