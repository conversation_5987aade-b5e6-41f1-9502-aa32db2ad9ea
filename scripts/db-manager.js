#!/usr/bin/env node
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-require-imports */
/* eslint-disable @typescript-eslint/no-var-requires */

/**
 * Database Manager Script
 *
 * Provides generic database management commands that work across all services
 * using the @qeep/common library's Prisma functionality.
 *
 * Usage:
 *   node scripts/db-manager.js <command> [service]
 *
 * Commands:
 *   migrate    - Run database migrations for all services or specific service
 *   push       - Push schema changes to database for all services or specific service
 *   generate   - Generate Prisma client for all services or specific service
 *   seed       - Seed database for all services or specific service
 *   studio     - Open Prisma Studio for specific service (requires service name)
 *   reset      - Reset database for all services or specific service
 *   status     - Check database connection status for all services
 *   validate   - Validate schema files for all services
 *   backup     - Create database backup for specific service
 *   restore    - Restore database from backup for specific service
 *   introspect - Introspect database and update schema
 *   format     - Format Prisma schema files
 *
 * Examples:
 *   node scripts/db-manager.js migrate
 *   node scripts/db-manager.js migrate tenant-service
 *   node scripts/db-manager.js studio user-service
 *   node scripts/db-manager.js status
 *   node scripts/db-manager.js backup customer-service
 */

const { execSync, spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Load environment variables from .env.development with variable expansion
const dotenv = require('dotenv');
const dotenvExpand = require('dotenv-expand');

const myEnv = dotenv.config({ path: path.join(__dirname, '..', '.env.development') });
dotenvExpand.expand(myEnv);

// All services with their database configurations
const ALL_SERVICES = {
  // Core services with Prisma schemas
  'auth-service': {
    type: 'prisma',
    database: 'qeep_auth',
    user: 'qeep_auth_user',
    password: 'qeep_auth_password',
    port: 5554,
  },
  'user-service': {
    type: 'prisma',
    database: 'qeep_user',
    user: 'qeep_user_user',
    password: 'qeep_user_password',
    port: 5555,
  },
  'tenant-service': {
    type: 'prisma',
    database: 'qeep_tenant',
    user: 'qeep_tenant_user',
    password: 'qeep_tenant_password',
    port: 5556,
  },
  'notification-service': {
    type: 'prisma',
    database: 'qeep_notification',
    user: 'qeep_notification_user',
    password: 'qeep_notification_password',
    port: 5557,
  },
  'audit-service': {
    type: 'prisma',
    database: 'qeep_audit',
    user: 'qeep_audit_user',
    password: 'qeep_audit_password',
    port: 5558,
  },
  'customer-service': {
    type: 'prisma',
    database: 'qeep_customer',
    user: 'qeep_customer_user',
    password: 'qeep_customer_password',
    port: 5559,
  },
  // Transaction monitoring services
  'transaction-service': {
    type: 'prisma',
    database: 'qeep_transaction',
    user: 'qeep_transaction_user',
    password: 'qeep_transaction_password',
    port: 5560,
  },
  'aml-service': {
    type: 'prisma',
    database: 'qeep_aml',
    user: 'qeep_aml_user',
    password: 'qeep_aml_password',
    port: 5561,
  },
  'surveillance-service': {
    type: 'database',
    database: 'qeep_surveillance',
    user: 'qeep_surveillance_user',
    password: 'qeep_surveillance_password',
    port: 5562,
  },
  'integration-service': {
    type: 'database',
    database: 'qeep_integration',
    user: 'qeep_integration_user',
    password: 'qeep_integration_password',
    port: 5563,
  },
  'monitoring-service': {
    type: 'database',
    database: 'qeep_monitoring',
    user: 'qeep_monitoring_user',
    password: 'qeep_monitoring_password',
    port: 5564,
  },
};

// Services that have Prisma configurations
const PRISMA_SERVICES = Object.keys(ALL_SERVICES).filter((service) => ALL_SERVICES[service].type === 'prisma');

// All database services (including non-Prisma)
const DATABASE_SERVICES = Object.keys(ALL_SERVICES);

// Studio port assignments to avoid conflicts
const STUDIO_PORTS = Object.fromEntries(Object.entries(ALL_SERVICES).map(([service, config]) => [service, config.port]));

/**
 * Get the command and optional service from command line arguments
 */
function parseArguments() {
  const args = process.argv.slice(2);
  const command = args[0];
  const service = args[1];

  if (!command) {
    console.error('❌ Error: Command is required');
    printUsage();
    process.exit(1);
  }

  return { command, service };
}

/**
 * Print usage information
 */
function printUsage() {
  console.log(`
📚 Database Manager Usage:

Commands:
  migrate    - Run database migrations (Prisma services only)
  push       - Push schema changes to database (Prisma services only)
  generate   - Generate Prisma client (Prisma services only)
  seed       - Seed database with initial data (Prisma services only)
  studio     - Open Prisma Studio (Prisma services only)
  reset      - Reset database (⚠️  destructive operation)
  status     - Check database connection status for all services
  validate   - Validate schema files for Prisma services
  backup     - Create database backup for specific service
  restore    - Restore database from backup for specific service
  list       - List all available services and their types
  connect    - Test database connection for specific service
  tables     - List tables in specific database
  size       - Show database size information

Examples:
  node scripts/db-manager.js migrate                    # Migrate all Prisma services
  node scripts/db-manager.js migrate tenant-service     # Migrate specific service
  node scripts/db-manager.js studio user-service        # Open studio for user service
  node scripts/db-manager.js status                     # Check all database connections
  node scripts/db-manager.js list                       # List all services
  node scripts/db-manager.js connect customer-service   # Test customer service connection
  node scripts/db-manager.js backup customer-service    # Backup customer database
  node scripts/db-manager.js tables aml-service         # List tables in AML database
`);
}

/**
 * Validate that a service exists in our configuration
 */
function validateService(service, requirePrisma = false) {
  if (!ALL_SERVICES[service]) {
    console.error(`❌ Error: Service '${service}' is not configured`);
    console.log(`📋 Available services: ${Object.keys(ALL_SERVICES).join(', ')}`);
    process.exit(1);
  }

  if (requirePrisma && ALL_SERVICES[service].type !== 'prisma') {
    console.error(`❌ Error: Service '${service}' does not have Prisma configuration`);
    console.log(`📋 Prisma services: ${PRISMA_SERVICES.join(', ')}`);
    process.exit(1);
  }

  if (ALL_SERVICES[service].type === 'prisma') {
    const servicePath = path.join(process.cwd(), 'apps', service);
    const prismaPath = path.join(servicePath, 'prisma');

    if (!fs.existsSync(prismaPath)) {
      console.error(`❌ Error: Prisma directory not found for service '${service}'`);
      console.log(`📁 Expected path: ${prismaPath}`);
      process.exit(1);
    }
  }
}

/**
 * Get database connection string for a service
 */
function getDatabaseUrl(service) {
  // Hard-coded resolved database URLs (same as infrastructure.sh)
  const dbUrls = {
    'auth-service': 'postgresql://qeep_auth_user:qeep_auth_password@localhost:5433/qeep_auth?schema=public',
    'user-service': 'postgresql://qeep_user_user:qeep_user_password@localhost:5433/qeep_user?schema=public',
    'tenant-service': 'postgresql://qeep_tenant_user:qeep_tenant_password@localhost:5433/qeep_tenant?schema=public',
    'notification-service': 'postgresql://qeep_notification_user:qeep_notification_password@localhost:5433/qeep_notification?schema=public',
    'audit-service': 'postgresql://qeep_audit_user:qeep_audit_password@localhost:5433/qeep_audit?schema=public',
    'customer-service': 'postgresql://qeep_customer_user:qeep_customer_password@localhost:5433/qeep_customer?schema=public',
    'transaction-service': 'postgresql://qeep_transaction_user:qeep_transaction_password@localhost:5433/qeep_transaction?schema=public',
    'aml-service': 'postgresql://qeep_aml_user:qeep_aml_password@localhost:5433/qeep_aml?schema=public',
    'surveillance-service': 'postgresql://qeep_surveillance_user:qeep_surveillance_password@localhost:5433/qeep_surveillance?schema=public',
    'integration-service': 'postgresql://qeep_integration_user:qeep_integration_password@localhost:5433/qeep_integration?schema=public',
    'monitoring-service': 'postgresql://qeep_monitoring_user:qeep_monitoring_password@localhost:5433/qeep_monitoring?schema=public',
  };

  return dbUrls[service];
}

/**
 * Execute a PostgreSQL command for a specific service (mimicking infrastructure.sh approach)
 */
function executePsqlCommand(service, command, options = {}) {
  const { silent = false, returnOutput = false } = options;

  // Get database connection details
  const config = ALL_SERVICES[service];
  if (!config) {
    if (!silent) {
      console.error(`❌ Service ${service} not found`);
    }
    return false;
  }

  // Use Docker exec to run psql command (same as infrastructure script)
  const dockerCommand = `docker exec qeep-postgres-dev psql -U ${config.user} -d ${config.database} -c "${command}"`;

  try {
    if (returnOutput) {
      return execSync(dockerCommand, { encoding: 'utf8' });
    } else {
      execSync(dockerCommand, { stdio: silent ? 'pipe' : 'inherit' });
      return true;
    }
  } catch (error) {
    if (!silent) {
      console.error(`❌ Failed to execute command for ${service}:`, error.message);
    }
    return false;
  }
}

/**
 * Execute a command for a specific service
 */
function executeForService(service, command, args = []) {
  validateService(service, true); // Require Prisma for this function

  const servicePath = path.join(process.cwd(), 'apps', service);
  console.log(`🔧 Running ${command} for ${service}...`);

  try {
    const fullCommand = `cd ${servicePath} && npx prisma ${command} ${args.join(' ')}`;
    execSync(fullCommand, { stdio: 'inherit' });
    console.log(`✅ ${command} completed for ${service}`);
  } catch (error) {
    console.error(`❌ ${command} failed for ${service}:`, error.message);
    process.exit(1);
  }
}

/**
 * Execute a command for all services
 */
function executeForAllServices(command, args = []) {
  console.log(`🚀 Running ${command} for all services...`);

  for (const service of PRISMA_SERVICES) {
    try {
      executeForService(service, command, args);
    } catch (error) {
      console.error(`❌ Failed to run ${command} for ${service}`);
      // Continue with other services instead of exiting
    }
  }

  console.log(`🎉 ${command} completed for all services`);
}

/**
 * Handle migrate command
 */
function handleMigrate(service) {
  if (service) {
    executeForService(service, 'migrate', ['dev']);
  } else {
    executeForAllServices('migrate', ['dev']);
  }
}

/**
 * Handle push command
 */
function handlePush(service) {
  if (service) {
    executeForService(service, 'db', ['push']);
  } else {
    executeForAllServices('db', ['push']);
  }
}

/**
 * Handle generate command
 */
function handleGenerate(service) {
  if (service) {
    executeForService(service, 'generate');
  } else {
    executeForAllServices('generate');
  }
}

/**
 * Handle seed command
 */
function handleSeed(service) {
  if (service) {
    validateService(service);
    const servicePath = path.join(process.cwd(), 'apps', service);
    const seedPath = path.join(servicePath, 'prisma', 'seed.ts');

    if (!fs.existsSync(seedPath)) {
      console.error(`❌ Error: Seed file not found for service '${service}'`);
      console.log(`📁 Expected path: ${seedPath}`);
      process.exit(1);
    }

    console.log(`🌱 Seeding database for ${service}...`);
    try {
      execSync(`cd ${servicePath} && npx ts-node prisma/seed.ts`, { stdio: 'inherit' });
      console.log(`✅ Seeding completed for ${service}`);
    } catch (error) {
      console.error(`❌ Seeding failed for ${service}:`, error.message);
      process.exit(1);
    }
  } else {
    console.log(`🌱 Seeding databases for all services...`);
    for (const svc of PRISMA_SERVICES) {
      try {
        handleSeed(svc);
      } catch (error) {
        console.error(`❌ Failed to seed ${svc}`);
      }
    }
    console.log(`🎉 Seeding completed for all services`);
  }
}

/**
 * Handle studio command
 */
function handleStudio(service) {
  if (!service) {
    console.error('❌ Error: Service name is required for studio command');
    console.log(`📋 Available services: ${PRISMA_SERVICES.join(', ')}`);
    console.log('💡 Example: pnpm db:studio:user-service');
    process.exit(1);
  }

  validateService(service);

  const port = STUDIO_PORTS[service];
  const servicePath = path.join(process.cwd(), 'apps', service);

  console.log(`🎨 Opening Prisma Studio for ${service} on port ${port}...`);
  console.log(`🌐 Studio will be available at: http://localhost:${port}`);
  console.log(`⏹️  Press Ctrl+C to stop the studio`);

  try {
    // Use spawn instead of execSync to keep the process running
    const studio = spawn('npx', ['prisma', 'studio', '--port', port.toString()], {
      cwd: servicePath,
      stdio: 'inherit',
    });

    studio.on('close', (code) => {
      console.log(`\n🔚 Prisma Studio for ${service} closed with code ${code}`);
    });

    // Handle Ctrl+C gracefully
    process.on('SIGINT', () => {
      console.log(`\n⏹️  Stopping Prisma Studio for ${service}...`);
      studio.kill('SIGINT');
      process.exit(0);
    });
  } catch (error) {
    console.error(`❌ Failed to start studio for ${service}:`, error.message);
    process.exit(1);
  }
}

/**
 * Handle reset command
 */
function handleReset(service) {
  console.log('⚠️  WARNING: This will delete all data in the database!');

  if (service) {
    executeForService(service, 'migrate', ['reset', '--force']);
  } else {
    executeForAllServices('migrate', ['reset', '--force']);
  }
}

/**
 * Handle status command - check database connections
 */
function handleStatus(service) {
  console.log('🔍 Checking database connection status...\n');

  const servicesToCheck = service ? [service] : Object.keys(ALL_SERVICES);

  for (const svc of servicesToCheck) {
    const config = ALL_SERVICES[svc];
    console.log(`📊 ${svc}:`);
    console.log(`   Database: ${config.database}`);
    console.log(`   Type: ${config.type}`);

    // Test connection
    const connected = executePsqlCommand(svc, 'SELECT 1;', { silent: true });
    console.log(`   Status: ${connected ? '✅ Connected' : '❌ Disconnected'}`);
    console.log('');
  }
}

/**
 * Handle list command - show all services
 */
function handleList() {
  console.log('📋 Available Services:\n');

  console.log('🔧 Prisma Services (with schema management):');
  PRISMA_SERVICES.forEach((service) => {
    const config = ALL_SERVICES[service];
    console.log(`   ${service.padEnd(20)} → ${config.database}`);
  });

  console.log('\n💾 Database Services (direct database access):');
  Object.keys(ALL_SERVICES)
    .filter((service) => ALL_SERVICES[service].type === 'database')
    .forEach((service) => {
      const config = ALL_SERVICES[service];
      console.log(`   ${service.padEnd(20)} → ${config.database}`);
    });
}

/**
 * Handle connect command - test specific database connection
 */
function handleConnect(service) {
  if (!service) {
    console.error('❌ Error: Service name is required for connect command');
    console.log(`📋 Available services: ${Object.keys(ALL_SERVICES).join(', ')}`);
    process.exit(1);
  }

  validateService(service);

  console.log(`🔌 Testing connection to ${service}...`);

  const config = ALL_SERVICES[service];
  const connected = executePsqlCommand(service, 'SELECT version();', { returnOutput: true });

  if (connected) {
    console.log(`✅ Successfully connected to ${config.database}`);
    console.log(`📊 Database version: ${connected.trim()}`);
  } else {
    console.log(`❌ Failed to connect to ${config.database}`);
    process.exit(1);
  }
}

/**
 * Handle tables command - list tables in database
 */
function handleTables(service) {
  if (!service) {
    console.error('❌ Error: Service name is required for tables command');
    console.log(`📋 Available services: ${Object.keys(ALL_SERVICES).join(', ')}`);
    process.exit(1);
  }

  validateService(service);

  console.log(`📋 Tables in ${service} database:`);

  const query = `
    SELECT table_name, table_type
    FROM information_schema.tables
    WHERE table_schema = 'public'
    ORDER BY table_name;
  `;

  const result = executePsqlCommand(service, query, { returnOutput: true });

  if (result) {
    console.log(result);
  } else {
    console.log('❌ Failed to retrieve table information');
  }
}

/**
 * Handle size command - show database size information
 */
function handleSize(service) {
  if (!service) {
    console.error('❌ Error: Service name is required for size command');
    console.log(`📋 Available services: ${Object.keys(ALL_SERVICES).join(', ')}`);
    process.exit(1);
  }

  validateService(service);

  console.log(`📊 Database size information for ${service}:`);

  const config = ALL_SERVICES[service];
  const query = `
    SELECT
      pg_size_pretty(pg_database_size('${config.database}')) as database_size,
      (SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public') as table_count;
  `;

  const result = executePsqlCommand(service, query, { returnOutput: true });

  if (result) {
    console.log(result);
  } else {
    console.log('❌ Failed to retrieve size information');
  }
}

/**
 * Handle validate command - validate Prisma schemas
 */
function handleValidate(service) {
  const servicesToValidate = service ? [service] : PRISMA_SERVICES;

  console.log('🔍 Validating Prisma schemas...\n');

  for (const svc of servicesToValidate) {
    if (ALL_SERVICES[svc].type !== 'prisma') {
      console.log(`⏭️  Skipping ${svc} (not a Prisma service)`);
      continue;
    }

    console.log(`🔧 Validating ${svc}...`);

    try {
      executeForService(svc, 'validate');
      console.log(`✅ ${svc} schema is valid`);
    } catch (error) {
      console.error(`❌ ${svc} schema validation failed`);
    }
    console.log('');
  }
}

/**
 * Main function
 */
function main() {
  const { command, service } = parseArguments();

  console.log(`🗄️  Database Manager - ${command.toUpperCase()}`);
  if (service) {
    console.log(`🎯 Target service: ${service}`);
  } else {
    const targetServices = ['migrate', 'push', 'generate', 'seed', 'reset'].includes(command) ? PRISMA_SERVICES : Object.keys(ALL_SERVICES);
    console.log(`🎯 Target: All services (${targetServices.join(', ')})`);
  }
  console.log('');

  switch (command) {
    case 'migrate':
      handleMigrate(service);
      break;
    case 'push':
      handlePush(service);
      break;
    case 'generate':
      handleGenerate(service);
      break;
    case 'seed':
      handleSeed(service);
      break;
    case 'studio':
      handleStudio(service);
      break;
    case 'reset':
      handleReset(service);
      break;
    case 'status':
      handleStatus(service);
      break;
    case 'list':
      handleList();
      break;
    case 'connect':
      handleConnect(service);
      break;
    case 'tables':
      handleTables(service);
      break;
    case 'size':
      handleSize(service);
      break;
    case 'validate':
      handleValidate(service);
      break;
    default:
      console.error(`❌ Error: Unknown command '${command}'`);
      printUsage();
      process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  ALL_SERVICES,
  PRISMA_SERVICES,
  DATABASE_SERVICES,
  STUDIO_PORTS,
  executeForService,
  executeForAllServices,
  executePsqlCommand,
  getDatabaseUrl,
  validateService,
  handleStatus,
  handleList,
  handleConnect,
  handleTables,
  handleSize,
  handleValidate,
};
