#!/usr/bin/env node

/**
 * Qeep Process Manager
 * Utility for managing multiple terminal processes with proper cleanup
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

class ProcessManager {
  constructor() {
    this.processes = new Map();
    this.pidDir = path.join(process.cwd(), 'tmp', 'pids');
    this.logDir = path.join(process.cwd(), 'tmp', 'logs');
    this.setupDirectories();
    this.setupSignalHandlers();
  }

  setupDirectories() {
    if (!fs.existsSync(this.pidDir)) {
      fs.mkdirSync(this.pidDir, { recursive: true });
    }
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  setupSignalHandlers() {
    // Handle graceful shutdown
    process.on('SIGINT', () => this.shutdown('SIGINT'));
    process.on('SIGTERM', () => this.shutdown('SIGTERM'));
    process.on('exit', () => this.cleanup());
  }

  /**
   * Start a new process
   * @param {string} name - Process name
   * @param {string} command - Command to execute
   * @param {string[]} args - Command arguments
   * @param {Object} options - Spawn options
   */
  startProcess(name, command, args = [], options = {}) {
    if (this.processes.has(name)) {
      console.log(`⚠️  Process ${name} is already running`);
      return false;
    }

    const logFile = path.join(this.logDir, `${name}.log`);
    const pidFile = path.join(this.pidDir, `${name}.pid`);

    // Create log stream
    const logStream = fs.createWriteStream(logFile, { flags: 'a' });

    // Spawn process
    const child = spawn(command, args, {
      stdio: ['ignore', 'pipe', 'pipe'],
      detached: false,
      ...options
    });

    // Handle process output
    child.stdout.on('data', (data) => {
      logStream.write(`[STDOUT] ${data}`);
      if (options.verbose) {
        process.stdout.write(`[${name}] ${data}`);
      }
    });

    child.stderr.on('data', (data) => {
      logStream.write(`[STDERR] ${data}`);
      if (options.verbose) {
        process.stderr.write(`[${name}] ${data}`);
      }
    });

    // Handle process events
    child.on('spawn', () => {
      console.log(`✅ Started ${name} (PID: ${child.pid})`);
      fs.writeFileSync(pidFile, child.pid.toString());
    });

    child.on('error', (error) => {
      console.error(`❌ Failed to start ${name}: ${error.message}`);
      this.processes.delete(name);
      this.cleanupProcessFiles(name);
    });

    child.on('exit', (code, signal) => {
      const exitReason = signal ? `signal ${signal}` : `code ${code}`;
      console.log(`🔴 Process ${name} exited with ${exitReason}`);
      this.processes.delete(name);
      this.cleanupProcessFiles(name);
      logStream.end();
    });

    // Store process info
    this.processes.set(name, {
      process: child,
      logFile,
      pidFile,
      logStream,
      startTime: new Date()
    });

    return true;
  }

  /**
   * Stop a process
   * @param {string} name - Process name
   * @param {string} signal - Signal to send (default: SIGTERM)
   */
  stopProcess(name, signal = 'SIGTERM') {
    const processInfo = this.processes.get(name);
    if (!processInfo) {
      console.log(`⚠️  Process ${name} is not running`);
      return false;
    }

    console.log(`🛑 Stopping ${name}...`);
    
    try {
      processInfo.process.kill(signal);
      
      // Wait for graceful shutdown, then force kill if needed
      setTimeout(() => {
        if (this.processes.has(name)) {
          console.log(`⚠️  Force killing ${name}...`);
          processInfo.process.kill('SIGKILL');
        }
      }, 5000);
      
      return true;
    } catch (error) {
      console.error(`❌ Failed to stop ${name}: ${error.message}`);
      return false;
    }
  }

  /**
   * Restart a process
   * @param {string} name - Process name
   * @param {string} command - Command to execute
   * @param {string[]} args - Command arguments
   * @param {Object} options - Spawn options
   */
  restartProcess(name, command, args = [], options = {}) {
    console.log(`🔄 Restarting ${name}...`);
    
    if (this.processes.has(name)) {
      this.stopProcess(name);
      
      // Wait for process to stop before restarting
      const checkStopped = () => {
        if (!this.processes.has(name)) {
          setTimeout(() => this.startProcess(name, command, args, options), 1000);
        } else {
          setTimeout(checkStopped, 500);
        }
      };
      checkStopped();
    } else {
      this.startProcess(name, command, args, options);
    }
  }

  /**
   * Get process status
   * @param {string} name - Process name (optional)
   */
  getStatus(name = null) {
    if (name) {
      const processInfo = this.processes.get(name);
      if (!processInfo) {
        return { name, status: 'stopped', pid: null, uptime: null };
      }
      
      const uptime = Date.now() - processInfo.startTime.getTime();
      return {
        name,
        status: 'running',
        pid: processInfo.process.pid,
        uptime: Math.floor(uptime / 1000),
        logFile: processInfo.logFile
      };
    }

    // Return status for all processes
    const statuses = [];
    for (const [processName] of this.processes) {
      statuses.push(this.getStatus(processName));
    }
    return statuses;
  }

  /**
   * Show logs for a process
   * @param {string} name - Process name
   * @param {boolean} follow - Follow logs (tail -f)
   */
  showLogs(name, follow = false) {
    const processInfo = this.processes.get(name);
    if (!processInfo) {
      console.error(`❌ Process ${name} is not running`);
      return false;
    }

    if (follow) {
      console.log(`📋 Following logs for ${name} (Press Ctrl+C to exit):`);
      const tail = spawn('tail', ['-f', processInfo.logFile], { stdio: 'inherit' });
      
      process.on('SIGINT', () => {
        tail.kill();
        process.exit(0);
      });
    } else {
      console.log(`📋 Logs for ${name}:`);
      const cat = spawn('cat', [processInfo.logFile], { stdio: 'inherit' });
    }

    return true;
  }

  /**
   * Clean up process files
   * @param {string} name - Process name
   */
  cleanupProcessFiles(name) {
    const pidFile = path.join(this.pidDir, `${name}.pid`);
    if (fs.existsSync(pidFile)) {
      fs.unlinkSync(pidFile);
    }
  }

  /**
   * Stop all processes
   * @param {string} signal - Signal to send
   */
  stopAll(signal = 'SIGTERM') {
    console.log('🛑 Stopping all processes...');
    
    const promises = [];
    for (const [name] of this.processes) {
      promises.push(new Promise((resolve) => {
        this.stopProcess(name, signal);
        
        // Wait for process to exit
        const checkStopped = () => {
          if (!this.processes.has(name)) {
            resolve();
          } else {
            setTimeout(checkStopped, 100);
          }
        };
        checkStopped();
      }));
    }

    return Promise.all(promises);
  }

  /**
   * Shutdown handler
   * @param {string} signal - Signal received
   */
  async shutdown(signal) {
    console.log(`\n🔴 Received ${signal}, shutting down...`);
    await this.stopAll();
    this.cleanup();
    process.exit(0);
  }

  /**
   * Clean up resources
   */
  cleanup() {
    // Close all log streams
    for (const [, processInfo] of this.processes) {
      if (processInfo.logStream) {
        processInfo.logStream.end();
      }
    }
  }
}

// CLI interface
if (require.main === module) {
  const manager = new ProcessManager();
  const [,, command, ...args] = process.argv;

  switch (command) {
    case 'start':
      const [name, cmd, ...cmdArgs] = args;
      if (!name || !cmd) {
        console.error('Usage: process-manager.js start <name> <command> [args...]');
        process.exit(1);
      }
      manager.startProcess(name, cmd, cmdArgs, { verbose: true });
      break;

    case 'stop':
      const [stopName] = args;
      if (!stopName) {
        console.error('Usage: process-manager.js stop <name>');
        process.exit(1);
      }
      manager.stopProcess(stopName);
      break;

    case 'restart':
      const [restartName, restartCmd, ...restartArgs] = args;
      if (!restartName || !restartCmd) {
        console.error('Usage: process-manager.js restart <name> <command> [args...]');
        process.exit(1);
      }
      manager.restartProcess(restartName, restartCmd, restartArgs, { verbose: true });
      break;

    case 'status':
      const statuses = manager.getStatus();
      console.table(statuses);
      break;

    case 'logs':
      const [logName, follow] = args;
      if (!logName) {
        console.error('Usage: process-manager.js logs <name> [follow]');
        process.exit(1);
      }
      manager.showLogs(logName, follow === 'follow');
      break;

    case 'stop-all':
      manager.stopAll().then(() => {
        console.log('✅ All processes stopped');
        process.exit(0);
      });
      break;

    default:
      console.log('Qeep Process Manager');
      console.log('');
      console.log('Usage: process-manager.js <command> [args...]');
      console.log('');
      console.log('Commands:');
      console.log('  start <name> <command> [args...]  Start a new process');
      console.log('  stop <name>                       Stop a process');
      console.log('  restart <name> <command> [args...] Restart a process');
      console.log('  status                            Show all process status');
      console.log('  logs <name> [follow]              Show process logs');
      console.log('  stop-all                          Stop all processes');
      break;
  }
}

module.exports = ProcessManager;
