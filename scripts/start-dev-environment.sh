#!/bin/bash

# Qeep Development Environment Startup Script
# This script starts all services in the correct order for development

set -e

echo "🚀 Starting Qeep Development Environment"
echo "============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if .env.development exists
if [ ! -f ".env.development" ]; then
    print_error ".env.development file not found. Please create it first."
    exit 1
fi

print_status "Stopping any existing containers..."
docker-compose -f infrastructure/docker/compose/docker-compose.dev.yml down

print_status "Starting infrastructure services (PostgreSQL, Redis)..."
docker-compose -f infrastructure/docker/compose/docker-compose.dev.yml up -d postgres redis

print_status "Waiting for infrastructure services to be healthy..."
sleep 10

# Wait for PostgreSQL to be ready
print_status "Waiting for PostgreSQL to be ready..."
until docker-compose -f infrastructure/docker/compose/docker-compose.dev.yml exec postgres pg_isready -U qeep_dev > /dev/null 2>&1; do
    echo -n "."
    sleep 2
done
print_success "PostgreSQL is ready!"

# Wait for Redis to be ready
print_status "Waiting for Redis to be ready..."
until docker-compose -f infrastructure/docker/compose/docker-compose.dev.yml exec redis redis-cli ping > /dev/null 2>&1; do
    echo -n "."
    sleep 2
done
print_success "Redis is ready!"

print_status "Starting core services (User, Tenant, Auth)..."
docker-compose -f infrastructure/docker/compose/docker-compose.dev.yml up -d tenant-service user-service

print_status "Waiting for core services to start..."
sleep 15

print_status "Starting auth service..."
docker-compose -f infrastructure/docker/compose/docker-compose.dev.yml up -d auth-service

print_status "Waiting for auth service to start..."
sleep 10

print_status "Starting Nginx reverse proxy..."
docker-compose -f docker-compose.nginx.yml up -d

print_success "All services started!"

echo ""
echo "🎉 Development Environment Ready!"
echo "================================="
echo ""
echo "📋 Service Status:"
echo "  • PostgreSQL:    http://localhost:5432"
echo "  • Redis:         http://localhost:6379"
echo "  • User Service:  http://localhost:3002"
echo "  • Tenant Service: http://localhost:3003"
echo "  • Auth Service:  http://localhost:3001"
echo "  • API Gateway:   http://localhost:3000"
echo ""
echo "🔍 Useful Commands:"
echo "  • View logs:     docker-compose -f infrastructure/docker/compose/docker-compose.dev.yml logs -f [service-name]"
echo "  • Stop all:      docker-compose -f infrastructure/docker/compose/docker-compose.dev.yml down"
echo "  • Restart:       docker-compose -f infrastructure/docker/compose/docker-compose.dev.yml restart [service-name]"
echo ""
echo "🧪 Test Authentication:"
echo "  • Run tests:     node scripts/test-auth-endpoints.js"
echo ""
echo "📊 Monitor services:"
echo "  • All logs:      docker-compose -f infrastructure/docker/compose/docker-compose.dev.yml logs -f"
echo ""

# Optional: Show service status
print_status "Checking service health..."
sleep 5

services=("postgres" "redis" "tenant-service" "user-service" "auth-service")
for service in "${services[@]}"; do
    if docker-compose -f infrastructure/docker/compose/docker-compose.dev.yml ps "$service" | grep -q "Up"; then
        print_success "$service is running"
    else
        print_warning "$service may not be running properly"
    fi
done

echo ""
print_success "Development environment is ready! 🎉"
