#!/bin/bash

# Qeep Hybrid Development Environment Script
# This script manages the hybrid development setup:
# - Infrastructure services in Docker containers
# - NestJS application services running locally for hot reload

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INFRASTRUCTURE_SCRIPT="$SCRIPT_DIR/infrastructure.sh"
SERVICES_SCRIPT="$SCRIPT_DIR/services.sh"

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${CYAN}$1${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    
    # Check if pnpm is available
    if ! command -v pnpm >/dev/null 2>&1; then
        log_error "pnpm is not installed. Please install pnpm and try again."
        exit 1
    fi
    
    # Check if required scripts exist
    if [[ ! -f "$INFRASTRUCTURE_SCRIPT" ]]; then
        log_error "Infrastructure script not found: $INFRASTRUCTURE_SCRIPT"
        exit 1
    fi
    
    if [[ ! -f "$SERVICES_SCRIPT" ]]; then
        log_error "Services script not found: $SERVICES_SCRIPT"
        exit 1
    fi
    
    # Check if .env.development exists
    if [[ ! -f ".env.development" ]]; then
        log_error ".env.development file not found"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Function to start the hybrid development environment
start_hybrid() {
    log_header "🚀 Starting Qeep Hybrid Development Environment"
    echo ""
    
    check_prerequisites
    
    # Step 1: Start infrastructure services
    log_header "📦 Step 1: Starting Infrastructure Services"
    bash "$INFRASTRUCTURE_SCRIPT" start
    echo ""
    
    # Step 2: Wait a moment for infrastructure to stabilize
    log_info "Waiting for infrastructure to stabilize..."
    sleep 5
    
    # Step 3: Generate proto files if needed
    log_header "🔧 Step 2: Checking Protocol Buffer Files"
    if [[ -f "scripts/dev-proto-check.js" ]]; then
        node scripts/dev-proto-check.js
    fi
    echo ""
    
    # Step 4: Start application services
    log_header "🎯 Step 3: Starting Application Services"
    bash "$SERVICES_SCRIPT" start
    echo ""
    
    # Step 5: Show final status
    log_header "✅ Development Environment Ready!"
    echo ""
    show_status
    
    echo ""
    log_header "📋 Quick Commands:"
    echo "  • View service status:    $0 status"
    echo "  • View service logs:      $0 logs <service-name>"
    echo "  • Restart a service:      $0 restart <service-name>"
    echo "  • Stop everything:        $0 stop"
    echo ""
    log_header "🔍 Service URLs:"
    echo "  • API Gateway:            http://localhost:3000/api"
    echo "  • Auth Service:           http://localhost:3001/api"
    echo "  • User Service:           http://localhost:3002/api"
    echo "  • Tenant Service:         http://localhost:3003/api"
    echo "  • Notification Service:   http://localhost:3004/api"
    echo "  • Audit Service:          http://localhost:3005/api"
    echo ""
    log_header "💾 Infrastructure:"
    echo "  • PostgreSQL:             localhost:5432"
    echo "  • Redis:                  localhost:6379"
    echo ""
}

# Function to stop the hybrid development environment
stop_hybrid() {
    log_header "🛑 Stopping Qeep Hybrid Development Environment"
    echo ""
    
    # Step 1: Stop application services
    log_info "Stopping application services..."
    bash "$SERVICES_SCRIPT" stop
    echo ""
    
    # Step 2: Stop infrastructure services
    log_info "Stopping infrastructure services..."
    bash "$INFRASTRUCTURE_SCRIPT" stop
    echo ""
    
    log_success "Development environment stopped successfully!"
}

# Function to restart the hybrid development environment
restart_hybrid() {
    log_header "🔄 Restarting Qeep Hybrid Development Environment"
    echo ""
    
    stop_hybrid
    sleep 3
    start_hybrid
}

# Function to show status
show_status() {
    log_header "📊 Development Environment Status"
    echo ""
    
    # Infrastructure status
    log_info "Infrastructure Services:"
    bash "$INFRASTRUCTURE_SCRIPT" status
    echo ""
    
    # Application services status
    log_info "Application Services:"
    bash "$SERVICES_SCRIPT" status
}

# Function to show logs
show_logs() {
    local service=$1
    
    if [[ -z "$service" ]]; then
        log_error "Please specify a service name"
        log_info "Available services: auth-service, user-service, tenant-service, notification-service, audit-service"
        log_info "Infrastructure services: postgres, redis"
        return 1
    fi
    
    # Check if it's an infrastructure service
    case "$service" in
        postgres|redis|kafka|zookeeper)
            bash "$INFRASTRUCTURE_SCRIPT" logs "$service"
            ;;
        auth-service|user-service|tenant-service|notification-service|audit-service)
            bash "$SERVICES_SCRIPT" logs "$service"
            ;;
        *)
            log_error "Unknown service: $service"
            log_info "Available services: auth-service, user-service, tenant-service, notification-service, audit-service"
            log_info "Infrastructure services: postgres, redis"
            return 1
            ;;
    esac
}

# Function to restart a specific service
restart_service() {
    local service=$1
    
    if [[ -z "$service" ]]; then
        log_error "Please specify a service name"
        return 1
    fi
    
    # Check if it's an application service
    case "$service" in
        auth-service|user-service|tenant-service|notification-service|audit-service)
            bash "$SERVICES_SCRIPT" restart "$service"
            ;;
        *)
            log_error "Service restart only supported for application services"
            log_info "Available services: auth-service, user-service, tenant-service, notification-service, audit-service"
            return 1
            ;;
    esac
}

# Function to clean up everything
cleanup() {
    log_warning "This will stop all services and clean up containers/volumes. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "Cleaning up development environment..."
        bash "$SERVICES_SCRIPT" cleanup
        bash "$INFRASTRUCTURE_SCRIPT" cleanup
        log_success "Cleanup completed!"
    else
        log_info "Cleanup cancelled."
    fi
}

# Function to show help
show_help() {
    echo "Qeep Hybrid Development Environment"
    echo ""
    echo "This script manages a hybrid development setup where:"
    echo "• Infrastructure services (PostgreSQL, Redis) run in Docker containers"
    echo "• NestJS application services run locally for hot reload support"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  start                  Start the complete development environment"
    echo "  stop                   Stop the complete development environment"
    echo "  restart                Restart the complete development environment"
    echo "  status                 Show status of all services"
    echo "  logs <service>         Show logs for specific service"
    echo "  restart <service>      Restart specific application service"
    echo "  cleanup                Clean up all containers and volumes"
    echo "  help                   Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start               # Start everything"
    echo "  $0 logs auth-service   # Show auth service logs"
    echo "  $0 restart auth-service # Restart auth service"
    echo "  $0 status              # Show all service status"
    echo ""
}

# Main script logic
case "${1:-help}" in
    start)
        start_hybrid
        ;;
    stop)
        stop_hybrid
        ;;
    restart)
        if [[ -n "$2" ]]; then
            restart_service "$2"
        else
            restart_hybrid
        fi
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "$2"
        ;;
    cleanup)
        cleanup
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        log_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac