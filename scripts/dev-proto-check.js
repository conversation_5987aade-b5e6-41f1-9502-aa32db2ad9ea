#!/usr/bin/env node

/**
 * Development script to check if proto types need regeneration
 * This script only regenerates types if proto files are newer than generated files
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const PROTO_DIR = path.join(__dirname, '..', 'proto');
const GENERATED_DIR = path.join(__dirname, '..', 'libs', 'proto', 'src', 'generated');
const GENERATED_INDEX = path.join(GENERATED_DIR, 'index.ts');

/**
 * Get the latest modification time of all proto files
 */
function getLatestProtoTime() {
  let latestTime = 0;
  
  function scanDirectory(dir) {
    if (!fs.existsSync(dir)) return;
    
    const items = fs.readdirSync(dir);
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath);
      } else if (item.endsWith('.proto')) {
        latestTime = Math.max(latestTime, stat.mtime.getTime());
      }
    }
  }
  
  scanDirectory(PROTO_DIR);
  return latestTime;
}

/**
 * Get the modification time of the generated index file
 */
function getGeneratedTime() {
  if (!fs.existsSync(GENERATED_INDEX)) {
    return 0;
  }
  
  const stat = fs.statSync(GENERATED_INDEX);
  return stat.mtime.getTime();
}

/**
 * Check if generated files exist and are not empty
 */
function hasValidGeneratedFiles() {
  if (!fs.existsSync(GENERATED_INDEX)) {
    return false;
  }

  const content = fs.readFileSync(GENERATED_INDEX, 'utf8');
  // Check if the file has actual exports (either export * from or export { ... })
  const hasExports = content.includes('export * from') || content.includes('export {');
  const isNotPlaceholder = !content.includes('export {};');

  return hasExports && isNotPlaceholder;
}

/**
 * Main execution
 */
function main() {
  try {
    console.log('🔍 Checking if proto types need regeneration...');
    
    const protoTime = getLatestProtoTime();
    const generatedTime = getGeneratedTime();
    const hasValidFiles = hasValidGeneratedFiles();
    
    console.log(`📅 Latest proto file: ${new Date(protoTime).toISOString()}`);
    console.log(`📅 Generated files: ${new Date(generatedTime).toISOString()}`);
    console.log(`✅ Valid generated files: ${hasValidFiles}`);
    
    // Regenerate if:
    // 1. No generated files exist
    // 2. Generated files are invalid/empty
    // 3. Proto files are newer than generated files
    const shouldRegenerate = !hasValidFiles || protoTime > generatedTime;
    
    if (shouldRegenerate) {
      console.log('🔄 Proto types need regeneration...');
      
      // Use standalone script to run the generation
      execSync('node scripts/generate-proto-standalone.js', {
        stdio: 'inherit',
        cwd: path.join(__dirname, '..')
      });
      
      console.log('✅ Proto types regenerated successfully');
    } else {
      console.log('✅ Proto types are up to date');
    }
    
  } catch (error) {
    console.error('❌ Error checking proto types:', error.message);
    
    // If there's an error and no valid generated files exist, try to generate them
    if (!hasValidGeneratedFiles()) {
      console.log('🔄 Attempting to generate proto types due to error...');
      try {
        execSync('node scripts/generate-proto-standalone.js', {
          stdio: 'inherit',
          cwd: path.join(__dirname, '..')
        });
        console.log('✅ Proto types generated successfully after error recovery');
      } catch (genError) {
        console.error('❌ Failed to generate proto types:', genError.message);
        process.exit(1);
      }
    } else {
      // If we have valid files, continue despite the error
      console.log('⚠️  Continuing with existing proto types');
    }
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { getLatestProtoTime, getGeneratedTime, hasValidGeneratedFiles };
