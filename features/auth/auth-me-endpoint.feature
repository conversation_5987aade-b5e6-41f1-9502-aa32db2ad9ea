@auth @me-endpoint @security
Feature: /auth/me Endpoint - Authenticated User Profile Retrieval
  As an authenticated user
  I want to securely access my profile information via the /auth/me endpoint
  So that I can view my account details while ensuring sensitive data is protected

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"
    And the auth service is healthy and running
    And rate limiting is properly configured

  # =============================================================================
  # HAPPY PATH SCENARIOS - SUCCESSFUL AUTHENTICATED ACCESS
  # =============================================================================

  @me-endpoint @happy-path @core @security-compliant
  Scenario: Successful Profile Retrieval with Valid JWT Token
    Given I have registered and verified my account with email "test.me.success.{{random}}@businesscorp.com"
    And I have logged in and received a valid access token
    When I request my profile information via GET /auth/me
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the response should contain my complete user profile data
    And the response should include field "id" as a valid UUID string
    And the response should include field "email" matching my registered email
    And the response should include field "first_name" as string
    And the response should include field "last_name" as string
    And the response should include field "tenant_code" as string or null
    And the response should include field "status" as string
    And the response should include field "is_email_verified" as boolean true
    And the response should include field "last_login_at" as ISO 8601 timestamp
    And the response should include field "last_login_ip" as string
    And the response should include field "last_login_user_agent" as string
    And the response should include field "login_attempts" as number
    And the response should include field "locked_until" as timestamp or null
    And the response should include field "password_changed_at" as timestamp or null
    And the response should include field "created_at" as ISO 8601 timestamp
    And the response should include field "updated_at" as ISO 8601 timestamp
    And the response should NOT contain any password-related fields
    And the response should NOT contain any verification tokens
    And the response should NOT contain any sensitive security data
    And the response time should be under 200 milliseconds
    And the access should be logged for security monitoring

  @me-endpoint @happy-path @snake-case-validation
  Scenario: Response Fields Follow snake_case Convention via Common Module Transformation
    Given I have a valid access token for user "test.snake.case.{{random}}@businesscorp.com"
    When I request my profile information via GET /auth/me
    Then I should receive a 200 OK response
    And all response data fields should use snake_case naming convention
    And the field "first_name" should be present (transformed from "firstName")
    And the field "last_name" should be present (transformed from "lastName")
    And the field "is_email_verified" should be present (transformed from "isEmailVerified")
    And the field "last_login_at" should be present (transformed from "lastLoginAt")
    And the field "created_at" should be present (transformed from "createdAt")
    And the field "updated_at" should be present (transformed from "updatedAt")

  # =============================================================================
  # AUTHENTICATION ERROR SCENARIOS - 401 UNAUTHORIZED
  # =============================================================================

  @me-endpoint @authentication @negative @missing-token @security
  Scenario: Profile Access without Authorization Header
    Given I am not authenticated
    When I attempt to access GET /auth/me without any authorization header
    Then I should receive a 401 Unauthorized response
    And the response should follow StandardApiResponse error format
    And the error message should mention "missing authorization" or "authentication required"
    And the response should not contain any user data
    And the failed access attempt should be logged for security monitoring

  @me-endpoint @authentication @negative @invalid-token-format @security
  Scenario: Profile Access with Malformed JWT Token
    Given I have a malformed JWT token "invalid.jwt.token.format"
    When I attempt to access GET /auth/me with the malformed token
    Then I should receive a 401 Unauthorized response
    And the error message should mention "invalid token format" or "malformed token"
    And the response should not contain any user data
    And the security violation should be logged

  @me-endpoint @authentication @negative @expired-token @security
  Scenario: Profile Access with Expired JWT Token
    Given I have an expired JWT token
    When I attempt to access GET /auth/me with the expired token
    Then I should receive a 401 Unauthorized response
    And the error message should mention "token expired" or "expired"
    And the response should not contain any user data
    And the expired token usage should be logged for security monitoring

  @me-endpoint @authentication @negative @invalid-signature @security
  Scenario: Profile Access with Invalid Token Signature
    Given I have a JWT token with an invalid signature
    When I attempt to access GET /auth/me with the tampered token
    Then I should receive a 401 Unauthorized response
    And the error message should mention "invalid token signature" or "invalid token"
    And the response should not contain any user data
    And the security violation should be handled safely
    And the tampered token attempt should be logged as a security event

  @me-endpoint @authentication @negative @revoked-token @security
  Scenario: Profile Access with Revoked/Blacklisted Token
    Given I have a valid JWT token that has been revoked
    When I attempt to access GET /auth/me with the revoked token
    Then I should receive a 401 Unauthorized response
    And the error message should mention "token revoked" or "invalid token"
    And the response should not contain any user data
    And the revoked token usage should be logged

  # =============================================================================
  # AUTHORIZATION ERROR SCENARIOS - 403 FORBIDDEN
  # =============================================================================

  @me-endpoint @authorization @negative @suspended-account @security
  Scenario: Profile Access with Suspended User Account
    Given I have a valid JWT token for a suspended user account
    When I attempt to access GET /auth/me with the valid token
    Then I should receive a 403 Forbidden response
    And the error message should mention "account suspended" or "access forbidden"
    And the response should not contain any user data
    And the suspended account access attempt should be logged

  @me-endpoint @authorization @negative @disabled-account @security
  Scenario: Profile Access with Disabled User Account
    Given I have a valid JWT token for a disabled user account
    When I attempt to access GET /auth/me with the valid token
    Then I should receive a 403 Forbidden response
    And the error message should mention "account disabled" or "access forbidden"
    And the response should not contain any user data

  # =============================================================================
  # NOT FOUND ERROR SCENARIOS - 404 NOT FOUND
  # =============================================================================

  @me-endpoint @not-found @negative @deleted-user @security
  Scenario: Profile Access for Deleted User Account
    Given I have a valid JWT token for a user that has been deleted
    When I attempt to access GET /auth/me with the valid token
    Then I should receive a 404 Not Found response
    And the error message should mention "user not found" or "account not found"
    And the response should not contain any user data
    And the deleted user access attempt should be logged

  # =============================================================================
  # SECURITY VALIDATION SCENARIOS
  # =============================================================================

  @me-endpoint @security @data-exposure @validation
  Scenario: Verify No Sensitive Data Exposure
    Given I have a valid access token for user "test.security.{{random}}@businesscorp.com"
    When I request my profile information via GET /auth/me
    Then I should receive a 200 OK response
    And the response should NOT contain field "password"
    And the response should NOT contain field "passwordHash"
    And the response should NOT contain field "password_hash"
    And the response should NOT contain field "verificationToken"
    And the response should NOT contain field "verification_token"
    And the response should NOT contain field "resetToken"
    And the response should NOT contain field "reset_token"
    And the response should NOT contain field "mfaSecret"
    And the response should NOT contain field "mfa_secret"
    And the response should NOT contain any field containing "secret"
    And the response should NOT contain any field containing "token" except in metadata

  @me-endpoint @security @jwt-validation @comprehensive
  Scenario: Comprehensive JWT Token Validation
    Given I have a properly signed JWT token with valid claims
    And the token contains subject claim with my user ID
    And the token contains issued-at timestamp
    And the token contains expiration timestamp in the future
    And the token type is "access_token"
    When I request my profile information via GET /auth/me
    Then I should receive a 200 OK response
    And the JWT validation should verify all required claims
    And the token signature should be validated against the service secret
    And the token should not be in the blacklist/revocation list

  @me-endpoint @security @rate-limiting @protection
  Scenario: Rate Limiting Protection
    Given I have a valid access token
    When I make multiple rapid requests to GET /auth/me
    Then the first requests should succeed with 200 OK
    And subsequent requests should be rate limited appropriately
    And rate limiting should not affect legitimate usage patterns
    And excessive requests should be logged for monitoring

  # =============================================================================
  # PERFORMANCE AND RELIABILITY SCENARIOS
  # =============================================================================

  @me-endpoint @performance @response-time
  Scenario: Response Time Performance Validation
    Given I have a valid access token
    When I request my profile information via GET /auth/me
    Then I should receive a 200 OK response
    And the response time should be under 200 milliseconds
    And the response should be properly cached for subsequent requests
    And database queries should be optimized

  @me-endpoint @reliability @service-degradation
  Scenario: Graceful Handling of Service Dependencies
    Given I have a valid access token
    And the user service is experiencing temporary issues
    When I request my profile information via GET /auth/me
    Then the system should handle the service degradation gracefully
    And appropriate error responses should be returned
    And the system should not crash or expose internal errors
    And retry mechanisms should be employed where appropriate

  # =============================================================================
  # EDGE CASES AND ERROR HANDLING
  # =============================================================================

  @me-endpoint @edge-cases @locked-account
  Scenario: Profile Access for Temporarily Locked Account
    Given I have a valid JWT token for a temporarily locked account
    When I request my profile information via GET /auth/me
    Then I should receive a 200 OK response
    And the response should include the lock status information
    And the "locked_until" field should contain the lock expiration timestamp
    And the "login_attempts" field should reflect the failed attempts

  @me-endpoint @edge-cases @unverified-email
  Scenario: Profile Access for Unverified Email Account
    Given I have a valid JWT token for an account with unverified email
    When I request my profile information via GET /auth/me
    Then I should receive a 200 OK response
    And the "is_email_verified" field should be false
    And all other profile information should be accessible
    And the account status should reflect the unverified state

  @me-endpoint @edge-cases @no-tenant
  Scenario: Profile Access for User with No Tenant Assignment
    Given I have a valid JWT token for a user with no tenant assignment
    When I request my profile information via GET /auth/me
    Then I should receive a 200 OK response
    And the "tenant_code" field should be null
    And all other profile information should be accessible
    And the response should handle null tenant gracefully

  # =============================================================================
  # DATA VALIDATION AND FORMAT SCENARIOS
  # =============================================================================

  @me-endpoint @validation @data-types @format
  Scenario: Response Data Type and Format Validation
    Given I have a valid access token
    When I request my profile information via GET /auth/me
    Then I should receive a 200 OK response
    And the "id" field should be a valid UUID format
    And the "email" field should be a valid email format
    And the "first_name" field should be a non-empty string
    And the "last_name" field should be a non-empty string
    And the "status" field should be a valid status enum value
    And the "is_email_verified" field should be a boolean
    And the "login_attempts" field should be a non-negative integer
    And timestamp fields should be in ISO 8601 format
    And nullable fields should be either valid values or null

  @me-endpoint @validation @required-fields
  Scenario: All Required Fields Present in Response
    Given I have a valid access token
    When I request my profile information via GET /auth/me
    Then I should receive a 200 OK response
    And the response data should contain all required fields:
      | field_name           | required |
      | id                   | true     |
      | email                | true     |
      | first_name           | true     |
      | last_name            | true     |
      | status               | true     |
      | is_email_verified    | true     |
      | login_attempts       | true     |
      | created_at           | true     |
      | updated_at           | true     |
      | tenant_code          | false    |
      | last_login_at        | false    |
      | last_login_ip        | false    |
      | last_login_user_agent| false    |
      | locked_until         | false    |
      | password_changed_at  | false    |

  # =============================================================================
  # SECURITY MONITORING AND LOGGING SCENARIOS
  # =============================================================================

  @me-endpoint @security @logging @monitoring
  Scenario: Security Event Logging for Profile Access
    Given I have a valid access token
    When I request my profile information via GET /auth/me
    Then I should receive a 200 OK response
    And the successful profile access should be logged with:
      | log_field    | description                    |
      | user_id      | The authenticated user ID      |
      | timestamp    | Request timestamp              |
      | ip_address   | Client IP address              |
      | user_agent   | Client user agent              |
      | endpoint     | /auth/me                       |
      | action       | profile_access                 |
      | status       | success                        |
    And the log entry should not contain sensitive information

  @me-endpoint @security @monitoring @failed-attempts
  Scenario: Security Monitoring for Failed Access Attempts
    Given I have an invalid JWT token
    When I attempt to access GET /auth/me with the invalid token
    Then I should receive a 401 Unauthorized response
    And the failed access attempt should be logged with:
      | log_field    | description                    |
      | timestamp    | Request timestamp              |
      | ip_address   | Client IP address              |
      | user_agent   | Client user agent              |
      | endpoint     | /auth/me                       |
      | action       | profile_access_failed          |
      | reason       | invalid_token                  |
      | status       | unauthorized                   |
    And the log should help identify potential security threats

  # =============================================================================
  # HTTPS AND TRANSPORT SECURITY SCENARIOS
  # =============================================================================

  @me-endpoint @security @https @transport
  Scenario: HTTPS-Only Access in Production Environment
    Given the application is running in production mode
    And I have a valid access token
    When I attempt to access GET /auth/me over HTTP (non-secure)
    Then the request should be redirected to HTTPS or rejected with appropriate security headers
    And sensitive data should never be transmitted over unencrypted connections

  @me-endpoint @security @headers @protection
  Scenario: Security Headers in Response
    Given I have a valid access token
    When I request my profile information via GET /auth/me
    Then I should receive a 200 OK response
    And the response should include appropriate security headers:
      | header_name           | purpose                        |
      | X-Content-Type-Options| Prevent MIME type sniffing     |
      | X-Frame-Options       | Prevent clickjacking           |
      | X-XSS-Protection      | XSS protection                 |
      | Strict-Transport-Security | Force HTTPS               |
    And the response should not expose sensitive server information

  # =============================================================================
  # INTEGRATION AND DEPENDENCY SCENARIOS
  # =============================================================================

  @me-endpoint @integration @user-service @dependency
  Scenario: Integration with User Service
    Given I have a valid access token
    And the user service is available and responding
    When I request my profile information via GET /auth/me
    Then the auth service should query the user service for my profile data
    And the response should contain the most up-to-date user information
    And the integration should handle user service responses correctly

  @me-endpoint @integration @caching @performance
  Scenario: User Data Caching for Performance
    Given I have a valid access token
    When I request my profile information via GET /auth/me multiple times
    Then the first request should fetch data from the user service
    And subsequent requests should use cached data when appropriate
    And the cache should respect the configured TTL (5 minutes)
    And cache invalidation should work when user data changes

  # =============================================================================
  # CONCURRENT ACCESS AND RACE CONDITIONS
  # =============================================================================

  @me-endpoint @concurrency @race-conditions
  Scenario: Concurrent Profile Access Requests
    Given I have a valid access token
    When I make multiple concurrent requests to GET /auth/me
    Then all requests should be handled correctly
    And there should be no race conditions in data retrieval
    And each request should receive consistent user data
    And the system should handle concurrent load appropriately

  # =============================================================================
  # COMPLIANCE AND AUDIT SCENARIOS
  # =============================================================================

  @me-endpoint @compliance @audit @data-protection
  Scenario: Data Protection and Privacy Compliance
    Given I have a valid access token
    When I request my profile information via GET /auth/me
    Then only my own profile data should be returned
    And no other user's data should be accessible
    And the data returned should comply with privacy regulations
    And audit trails should be maintained for compliance
