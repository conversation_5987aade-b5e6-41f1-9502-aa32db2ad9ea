@auth
Feature: 08 - WebAuthn/Passkeys
  As a user
  I want to use WebAuthn/Passkeys for authentication
  So that I can have a more secure and convenient login experience

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"

  # =============================================================================
  # WEBAUTHN REGISTRATION SCENARIOS
  # =============================================================================

  @webauthn @start @happy-path @core
  Scenario: Successful WebAuthn Registration Start
    Given I am authenticated with a valid token
    When I start WebAuthn registration with:
      | authenticatorName | My Security Key |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And I should receive WebAuthn creation options
    And I should receive a challenge for verification

    
    And the options should include user information
    And the options should include relying party information
    And the response time should be reasonable

  @webauthn @start @authorization @negative @missing-auth
  Scenario: WebAuthn Registration Start without Authentication
    Given I am not authenticated
    When I attempt to start WebAuthn registration
    Then I should receive a 401 Unauthorized response
    And the error should mention missing authorization

  @webauthn @start @authorization @negative @invalid-auth
  Scenario: WebAuthn Registration Start with Invalid Authentication
    Given I have an invalid JWT token
    When I attempt to start WebAuthn registration with the invalid token
    Then I should receive a 401 Unauthorized response
    And the error should mention invalid token

  @webauthn @complete @happy-path @core
  Scenario: Successful WebAuthn Registration Completion
    Given I have started WebAuthn registration
    And I have completed the browser WebAuthn ceremony
    When I complete WebAuthn registration with:
      | challenge         | {{webauthnChallenge}}    |
      | credential        | {{webauthnCredential}}   |
      | authenticatorName | My Security Key          |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the WebAuthn credential should be registered
    And I should receive a credential ID
    And I should receive backup codes
    And the credential should be verified
    And the response should confirm successful registration

  @webauthn @complete @validation @negative @invalid-challenge
  Scenario: WebAuthn Registration Completion with Invalid Challenge
    Given I have started WebAuthn registration
    When I attempt to complete registration with invalid challenge "invalid-challenge-123"
    Then I should receive a 400 Bad Request response
    And the error should mention invalid challenge
    And the credential should not be registered

  @webauthn @complete @validation @negative @invalid-credential
  Scenario: WebAuthn Registration Completion with Invalid Credential
    Given I have started WebAuthn registration
    When I attempt to complete registration with malformed credential data
    Then I should receive a 400 Bad Request response
    And the error should mention invalid credential
    And the credential should not be registered

  @webauthn @complete @validation @negative @expired-challenge
  Scenario: WebAuthn Registration Completion with Expired Challenge
    Given I have started WebAuthn registration
    And the registration challenge has expired
    When I attempt to complete registration with the expired challenge
    Then I should receive a 400 Bad Request response
    And the error should mention expired challenge
    And I should be required to restart registration

  # =============================================================================
  # WEBAUTHN AUTHENTICATION SCENARIOS
  # =============================================================================

  @webauthn @authentication @start @happy-path @core
  Scenario: Successful WebAuthn Authentication Start
    Given I have registered WebAuthn credentials
    And I have completed initial login
    When I start WebAuthn authentication with:
      | loginToken | {{temporaryLoginToken}} |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And I should receive WebAuthn request options
    And I should receive a challenge for authentication
    And the options should include allowed credentials
    And the response time should be reasonable

  @webauthn @authentication @start @validation @negative @invalid-login-token
  Scenario: WebAuthn Authentication Start with Invalid Login Token
    Given I have registered WebAuthn credentials
    When I attempt to start WebAuthn authentication with invalid login token "invalid-token-123"
    Then I should receive a 400 Bad Request response
    And the error should mention invalid login token

  @webauthn @authentication @start @validation @negative @missing-login-token
  Scenario: WebAuthn Authentication Start without Login Token
    Given I have registered WebAuthn credentials
    When I attempt to start WebAuthn authentication without login token
    Then I should receive a 400 Bad Request response
    And the error should mention login token is required

  @webauthn @authentication @start @business-rules @negative @no-credentials
  Scenario: WebAuthn Authentication Start with No Registered Credentials
    Given I have no registered WebAuthn credentials
    When I attempt to start WebAuthn authentication
    Then I should receive a 400 Bad Request response
    And the error should mention no WebAuthn credentials found

  @webauthn @authentication @complete @happy-path @core
  Scenario: Successful WebAuthn Authentication Completion
    Given I have started WebAuthn authentication
    And I have completed the browser WebAuthn ceremony
    When I complete WebAuthn authentication with:
      | challenge  | {{webauthnChallenge}}    |
      | credential | {{webauthnCredential}}   |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And I should receive final access and refresh tokens
    And I should be fully authenticated
    And the authentication should be verified
    And the credential usage should be recorded
    And the response time should be reasonable

  @webauthn @authentication @complete @validation @negative @invalid-challenge
  Scenario: WebAuthn Authentication Completion with Invalid Challenge
    Given I have started WebAuthn authentication
    When I attempt to complete authentication with invalid challenge "invalid-challenge-123"
    Then I should receive a 400 Bad Request response
    And the error should mention invalid challenge
    And authentication should fail

  @webauthn @authentication @complete @validation @negative @invalid-credential
  Scenario: WebAuthn Authentication Completion with Invalid Credential
    Given I have started WebAuthn authentication
    When I attempt to complete authentication with malformed credential data
    Then I should receive a 400 Bad Request response
    And the error should mention invalid credential
    And authentication should fail

  @webauthn @authentication @complete @security @negative @replay-attack
  Scenario: WebAuthn Authentication with Replay Attack
    Given I have completed a WebAuthn authentication
    When I attempt to reuse the same authentication response
    Then I should receive a 400 Bad Request response
    And the error should mention authentication failed
    And the replay attack should be detected and blocked

  # =============================================================================
  # PASSKEY MANAGEMENT SCENARIOS
  # =============================================================================

  @passkey @list @happy-path @core
  Scenario: List User Passkeys
    Given I am authenticated with a valid token
    And I have registered multiple passkeys
    When I request my passkey list
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And I should receive a list of my passkeys
    And each passkey should include credential ID
    And each passkey should include friendly name
    And each passkey should include creation date
    And each passkey should include last used date
    And the response time should be reasonable

  @passkey @list @authorization @negative @missing-auth
  Scenario: List Passkeys without Authentication
    Given I am not authenticated
    When I attempt to list my passkeys
    Then I should receive a 401 Unauthorized response
    And the error should mention missing authorization

  @passkey @list @happy-path @empty
  Scenario: List Passkeys when None Registered
    Given I am authenticated with a valid token
    And I have no registered passkeys
    When I request my passkey list
    Then I should receive a 200 OK response
    And I should receive an empty passkey list
    And the response should indicate no passkeys found

  @passkey @remove @happy-path @core
  Scenario: Successful Passkey Removal
    Given I am authenticated with a valid token
    And I have a registered passkey with ID "test-credential-123"
    When I remove the passkey "test-credential-123"
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the response should confirm passkey removal
    And the passkey should be deleted from my account
    And the passkey should no longer appear in my list
    And the response time should be reasonable

  @passkey @remove @authorization @negative @missing-auth
  Scenario: Remove Passkey without Authentication
    Given I am not authenticated
    When I attempt to remove a passkey
    Then I should receive a 401 Unauthorized response
    And the error should mention missing authorization

  @passkey @remove @validation @negative @non-existent
  Scenario: Remove Non-existent Passkey
    Given I am authenticated with a valid token
    When I attempt to remove non-existent passkey "non-existent-credential"
    Then I should receive a 404 Not Found response
    And the error should mention passkey not found

  @passkey @remove @security @negative @other-user-passkey
  Scenario: Remove Another User's Passkey
    Given I am authenticated as user A
    And user B has a passkey with ID "user-b-credential"
    When I attempt to remove user B's passkey
    Then I should receive a 404 Not Found response
    And the error should mention passkey not found
    And user B's passkey should remain intact

  @passkey @rename @happy-path @core
  Scenario: Successful Passkey Rename
    Given I am authenticated with a valid token
    And I have a registered passkey with ID "test-credential-123"
    When I rename the passkey "test-credential-123" to "My Updated Security Key"
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the response should confirm passkey renamed
    And the passkey name should be updated
    And the updated name should appear in my passkey list

  @passkey @rename @authorization @negative @missing-auth
  Scenario: Rename Passkey without Authentication
    Given I am not authenticated
    When I attempt to rename a passkey
    Then I should receive a 401 Unauthorized response
    And the error should mention missing authorization

  @passkey @rename @validation @negative @invalid-name
  Scenario: Rename Passkey with Invalid Name
    Given I am authenticated with a valid token
    And I have a registered passkey
    When I attempt to rename the passkey with empty name ""
    Then I should receive a 400 Bad Request response
    And the error should mention invalid name
    And the passkey name should remain unchanged

  # =============================================================================
  # SECURITY AND EDGE CASES
  # =============================================================================

  @webauthn @security @negative @cross-origin
  Scenario: WebAuthn Registration from Different Origin
    Given I am authenticated with a valid token
    When I attempt WebAuthn registration from unauthorized origin
    Then I should receive a 400 Bad Request response
    And the error should mention origin validation failed
    And the registration should be blocked

  @webauthn @security @negative @tampered-response
  Scenario: WebAuthn Authentication with Tampered Response
    Given I have started WebAuthn authentication
    When I attempt to complete authentication with tampered credential response
    Then I should receive a 400 Bad Request response
    And the error should mention authentication failed
    And the tampering should be detected

  @webauthn @edge-case @concurrent-registration
  Scenario: Concurrent WebAuthn Registration Attempts
    Given I am authenticated with a valid token
    When I start multiple WebAuthn registration sessions simultaneously
    Then only one registration should be allowed to proceed
    And other attempts should be handled gracefully
    And no duplicate credentials should be created

  @webauthn @business-rules @credential-limit
  Scenario: WebAuthn Registration with Maximum Credentials
    Given I am authenticated with a valid token
    And I have reached the maximum number of registered credentials
    When I attempt to register another WebAuthn credential
    Then I should receive a 400 Bad Request response
    And the error should mention credential limit reached
    And I should be prompted to remove existing credentials

  @webauthn @performance @large-credential-set
  Scenario: WebAuthn Authentication with Many Registered Credentials
    Given I am authenticated with a valid token
    And I have many registered WebAuthn credentials
    When I perform WebAuthn authentication
    Then the authentication should complete within reasonable time
    And all credentials should be properly evaluated
    And the system should handle the large credential set efficiently
