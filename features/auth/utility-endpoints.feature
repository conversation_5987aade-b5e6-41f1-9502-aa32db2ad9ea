@auth
Feature: Utility Endpoints
  As a user or system
  I want to use utility authentication endpoints
  So that I can check email availability, resend verification emails, and monitor system health

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"

  # =============================================================================
  # CHECK EMAIL AVAILABILITY SCENARIOS
  # =============================================================================

  @utility @check-email @happy-path @core
  Scenario: Check Email Availability for Available Email
    Given no user exists with email "<EMAIL>"
    When I check email availability for "<EMAIL>"
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the response should indicate email is available
    And the response should confirm the email can be used for registration
    And the response time should be reasonable

  @utility @check-email @happy-path @unavailable
  Scenario: Check Email Availability for Taken Email
    Given a user already exists with email "<EMAIL>"
    When I check email availability for "<EMAIL>"
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the response should indicate email is not available
    And the response should suggest using a different email

  @utility @check-email @validation @negative @invalid-email
  Scenario Outline: Check Email Availability with Invalid Email Formats
    Given I want to check email availability
    When I check availability for invalid email "<email>"
    Then I should receive a 400 Bad Request response
    And the error should mention email validation

    Examples:
      | email                    |
      | invalid-email-format     |
      | user@                    |
      | @domain.com              |
      | <EMAIL>    |
      | .<EMAIL>         |
      | <EMAIL>         |
      | user@domain.            |
      | user <EMAIL>     |

  @utility @check-email @validation @negative @missing-email
  Scenario: Check Email Availability without Email Parameter
    Given I want to check email availability
    When I check availability without providing an email
    Then I should receive a 400 Bad Request response
    And the error should mention email is required

  @utility @check-email @business-rules @personal-email
  Scenario Outline: Check Email Availability for Personal Email Domains
    Given I want to check email availability
    When I check availability for personal email "<email>"
    Then I should receive a 200 OK response
    And the response should indicate email is not available for business use
    And the response should suggest using a business email

    Examples:
      | email                    |
      | <EMAIL>           |
      | <EMAIL>           |
      | <EMAIL>         |
      | <EMAIL>         |
      | <EMAIL>             |
      | <EMAIL>          |

  @utility @check-email @security @rate-limiting
  Scenario: Check Email Availability Rate Limiting
    Given I want to check email availability
    When I make multiple rapid email availability checks
    Then I should be rate limited after several requests
    And I should receive a 429 Too Many Requests response
    And the error should mention rate limiting

  # =============================================================================
  # RESEND VERIFICATION EMAIL SCENARIOS
  # =============================================================================

  @utility @resend-verification @happy-path @core
  Scenario: Successful Resend Verification Email
    Given I have registered but not verified my email "<EMAIL>"
    When I request to resend verification email for "<EMAIL>"
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the response should confirm verification email sent
    And a new verification email should be sent
    And the old verification token should be invalidated
    And a new verification token should be generated
    And the response time should be reasonable

  @utility @resend-verification @negative @already-verified
  Scenario: Resend Verification Email for Already Verified Account
    Given I have a verified account with email "<EMAIL>"
    When I request to resend verification email for "<EMAIL>"
    Then I should receive a 400 Bad Request response
    And the error should mention account already verified
    And no email should be sent

  @utility @resend-verification @negative @non-existent-email
  Scenario: Resend Verification Email for Non-existent Email
    Given no user exists with email "<EMAIL>"
    When I request to resend verification email for "<EMAIL>"
    Then I should receive a 200 OK response
    And the response should confirm verification email sent
    And no actual email should be sent for security reasons
    And the response should not reveal user existence

  @utility @resend-verification @validation @negative @invalid-email
  Scenario Outline: Resend Verification Email with Invalid Email Formats
    Given I want to resend verification email
    When I request resend for invalid email "<email>"
    Then I should receive a 400 Bad Request response
    And the error should mention email validation

    Examples:
      | email                    |
      | invalid-email-format     |
      | user@                    |
      | @domain.com              |
      | <EMAIL>    |

  @utility @resend-verification @validation @negative @missing-email
  Scenario: Resend Verification Email without Email Parameter
    Given I want to resend verification email
    When I request resend without providing an email
    Then I should receive a 400 Bad Request response
    And the error should mention email is required

  @utility @resend-verification @rate-limiting @negative
  Scenario: Resend Verification Email Rate Limiting
    Given I have an unverified account
    And I have recently requested a verification email
    When I immediately request another verification email
    Then I should receive a 429 Too Many Requests response
    And the error should mention rate limiting
    And the error should indicate how long to wait

  # =============================================================================
  # HEALTH CHECK SCENARIOS
  # =============================================================================

  @utility @health @happy-path @core
  Scenario: Successful Auth Service Health Check
    Given the auth service is running
    When I check the auth service health
    Then I should receive a 200 OK response
    And the response should indicate the service is healthy
    And the response should include service status
    And the response should include timestamp
    And the response should include version information
    And the response time should be under 2 seconds

  @utility @health @monitoring @detailed
  Scenario: Health Check Includes Detailed Service Information
    Given the auth service is running with all dependencies
    When I check the auth service health
    Then I should receive comprehensive health information
    And the response should include database connectivity status
    And the response should include Redis connectivity status
    And the response should include external service dependencies
    And the response should include memory usage information
    And the response should include uptime information

  @utility @health @edge-case @degraded-service
  Scenario: Health Check with Degraded Service Performance
    Given the auth service is running but with degraded performance
    When I check the auth service health
    Then I should receive a 200 OK response
    And the response should indicate degraded performance
    And the response should include performance metrics
    And the response should include warnings about degraded services

  @utility @health @edge-case @partial-outage
  Scenario: Health Check During Partial Service Outage
    Given the auth service is running but some dependencies are down
    When I check the auth service health
    Then I should receive a 503 Service Unavailable response
    And the response should indicate which services are affected
    And the response should include estimated recovery time
    And the response should include alternative service options

  # =============================================================================
  # SECURITY AND PERFORMANCE SCENARIOS
  # =============================================================================

  @utility @security @negative @sql-injection
  Scenario: Utility Endpoints with SQL Injection Attempts
    Given I want to test utility endpoint security
    When I attempt to check email availability with malicious input "'; DROP TABLE users; --"
    Then I should receive a 400 Bad Request response
    And the system should handle malicious input safely
    And no database operations should be compromised

  @utility @security @negative @xss-attempts
  Scenario: Utility Endpoints with XSS Attempts
    Given I want to test utility endpoint security
    When I attempt to use utility endpoints with XSS payloads
    Then the system should sanitize all inputs
    And no script execution should occur
    And the responses should be safe

  @utility @performance @high-load
  Scenario: Utility Endpoints Under High Load
    Given the auth service is under high load
    When I use utility endpoints
    Then all endpoints should respond within acceptable time limits
    And the system should handle concurrent requests appropriately
    And rate limiting should protect against abuse

  @utility @performance @caching
  Scenario: Utility Endpoints Use Appropriate Caching
    Given I make repeated requests to utility endpoints
    When I check the same email availability multiple times
    Then the system should use caching to improve performance
    And cache invalidation should work correctly
    And fresh data should be returned when needed

  # =============================================================================
  # INTEGRATION AND EDGE CASES
  # =============================================================================

  @utility @integration @email-service
  Scenario: Utility Endpoints Integration with Email Service
    Given the email service is available
    When I request verification email resend
    Then the integration with email service should work correctly
    And email delivery should be tracked
    And delivery failures should be handled gracefully

  @utility @integration @email-service-down
  Scenario: Utility Endpoints When Email Service is Down
    Given the email service is unavailable
    When I request verification email resend
    Then I should receive an appropriate error response
    And the error should indicate email service issues
    And the request should be queued for retry if possible

  @utility @edge-case @concurrent-requests
  Scenario: Concurrent Utility Endpoint Requests
    Given I want to test concurrent access
    When I make multiple concurrent requests to utility endpoints
    Then all requests should be handled correctly
    And no race conditions should occur
    And responses should be consistent

  @utility @business-rules @tenant-isolation
  Scenario: Utility Endpoints Respect Tenant Isolation
    Given I am checking email availability across different tenants
    When I check the same email for different tenant contexts
    Then the availability should be checked per tenant
    And tenant isolation should be maintained
    And cross-tenant data should not be exposed

  @utility @monitoring @metrics
  Scenario: Utility Endpoints Provide Monitoring Metrics
    Given I use utility endpoints
    When monitoring systems check endpoint metrics
    Then usage statistics should be available
    And performance metrics should be tracked
    And error rates should be monitored
    And alerts should be triggered for anomalies

  @utility @compliance @data-privacy
  Scenario: Utility Endpoints Comply with Data Privacy
    Given I use utility endpoints with personal data
    When the endpoints process the data
    Then data privacy regulations should be followed
    And minimal data should be logged
    And data retention policies should be applied
    And user consent should be respected
