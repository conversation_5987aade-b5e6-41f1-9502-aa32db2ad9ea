@auth @skip
Feature: Multi-Factor Authentication
  As a user
  I want to set up and use multi-factor authentication
  So that I can enhance the security of my account

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"
    And I am authenticated with a valid token

  # =============================================================================
  # MFA SETUP SCENARIOS
  # =============================================================================

  @mfa @setup @happy-path @core
  Scenario: Successful MFA Setup with TOTP Method
    Given I do not have MFA enabled
    When I setup MFA with TOTP method:
      | method | TOTP |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And I should receive a QR code for TOTP setup
    And I should receive a secret key for manual entry
    And I should receive backup codes
    And the MFA setup should be in pending state
    And the response time should be reasonable

  @mfa @setup @happy-path @sms
  Scenario: Successful MFA Setup with SMS Method
    Given I do not have MFA enabled
    And I have a verified phone number
    When I setup MFA with SMS method:
      | method      | SMS           |
      | phoneNumber | +1234567890   |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And a verification SMS should be sent to my phone
    And I should receive backup codes
    And the MFA setup should be in pending state

  @mfa @setup @happy-path @email
  Scenario: Successful MFA Setup with Email Method
    Given I do not have MFA enabled
    When I setup MFA with Email method:
      | method | EMAIL |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And a verification email should be sent
    And I should receive backup codes
    And the MFA setup should be in pending state

  @mfa @setup @authorization @negative @missing-auth
  Scenario: MFA Setup without Authentication
    Given I am not authenticated
    When I attempt to setup MFA
    Then I should receive a 401 Unauthorized response
    And the error should mention missing authorization

  @mfa @setup @validation @negative @invalid-method
  Scenario: MFA Setup with Invalid Method
    Given I do not have MFA enabled
    When I attempt to setup MFA with invalid method "INVALID_METHOD"
    Then I should receive a 400 Bad Request response
    And the error should mention invalid MFA method

  @mfa @setup @business-rules @negative @already-enabled
  Scenario: MFA Setup when Already Enabled
    Given I already have MFA enabled
    When I attempt to setup MFA again
    Then I should receive a 400 Bad Request response
    And the error should mention MFA already enabled

  # =============================================================================
  # MFA SETUP VERIFICATION SCENARIOS
  # =============================================================================

  @mfa @verify-setup @happy-path @core
  Scenario: Successful MFA Setup Verification with TOTP
    Given I have initiated MFA setup with TOTP method
    And I have configured my authenticator app
    When I verify MFA setup with:
      | method | TOTP           |
      | code   | {{totpCode}}   |
      | secret | {{totpSecret}} |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And MFA should be enabled for my account
    And I should receive final backup codes
    And the response should confirm MFA activation

  @mfa @verify-setup @happy-path @sms
  Scenario: Successful MFA Setup Verification with SMS
    Given I have initiated MFA setup with SMS method
    And I have received an SMS verification code
    When I verify MFA setup with:
      | method | SMS         |
      | code   | {{smsCode}} |
    Then I should receive a 200 OK response
    And MFA should be enabled for my account
    And I should receive final backup codes

  @mfa @verify-setup @validation @negative @invalid-code
  Scenario: MFA Setup Verification with Invalid Code
    Given I have initiated MFA setup with TOTP method
    When I verify MFA setup with invalid code "123456"
    Then I should receive a 400 Bad Request response
    And the error should mention invalid verification code
    And MFA should remain disabled

  @mfa @verify-setup @validation @negative @expired-setup
  Scenario: MFA Setup Verification with Expired Setup Session
    Given I have initiated MFA setup but the session has expired
    When I attempt to verify MFA setup
    Then I should receive a 400 Bad Request response
    And the error should mention setup session expired
    And I should be required to restart MFA setup

  # =============================================================================
  # MFA CHALLENGE SCENARIOS (During Login)
  # =============================================================================

  @mfa @challenge @happy-path @core
  Scenario: Successful MFA Challenge Creation During Login
    Given I have MFA enabled with TOTP method
    And I have completed initial login
    When I request an MFA challenge with:
      | loginToken | {{temporaryLoginToken}} |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And I should receive a challenge ID
    And I should receive the MFA method type
    And the challenge should have appropriate expiration
    And the response time should be reasonable

  @mfa @challenge @validation @negative @invalid-login-token
  Scenario: MFA Challenge with Invalid Login Token
    Given I have MFA enabled
    When I request an MFA challenge with invalid login token "invalid-token-123"
    Then I should receive a 400 Bad Request response
    And the error should mention invalid login token

  @mfa @challenge @validation @negative @missing-login-token
  Scenario: MFA Challenge without Login Token
    Given I have MFA enabled
    When I request an MFA challenge without login token
    Then I should receive a 400 Bad Request response
    And the error should mention login token is required

  # =============================================================================
  # MFA CHALLENGE VERIFICATION SCENARIOS
  # =============================================================================

  @mfa @verify-challenge @happy-path @core
  Scenario: Successful MFA Challenge Verification with TOTP
    Given I have an active MFA challenge
    When I verify the MFA challenge with:
      | challengeId | {{mfaChallengeId}} |
      | code        | {{totpCode}}       |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And I should receive final access and refresh tokens
    And I should be fully authenticated
    And the challenge should be invalidated
    And the response time should be reasonable

  @mfa @verify-challenge @happy-path @backup-code
  Scenario: Successful MFA Challenge Verification with Backup Code
    Given I have an active MFA challenge
    When I verify the MFA challenge with backup code:
      | challengeId | {{mfaChallengeId}} |
      | backupCode  | {{backupCode}}     |
    Then I should receive a 200 OK response
    And I should receive final access and refresh tokens
    And the backup code should be marked as used
    And I should be warned about remaining backup codes

  @mfa @verify-challenge @validation @negative @invalid-code
  Scenario: MFA Challenge Verification with Invalid Code
    Given I have an active MFA challenge
    When I verify the MFA challenge with invalid code "000000"
    Then I should receive a 400 Bad Request response
    And the error should mention invalid MFA code
    And the challenge should remain active
    And failed attempt should be recorded

  @mfa @verify-challenge @validation @negative @expired-challenge
  Scenario: MFA Challenge Verification with Expired Challenge
    Given I have an expired MFA challenge
    When I attempt to verify the expired challenge
    Then I should receive a 400 Bad Request response
    And the error should mention challenge expired
    And I should be required to restart the login process

  @mfa @verify-challenge @security @negative @used-backup-code
  Scenario: MFA Challenge Verification with Used Backup Code
    Given I have an active MFA challenge
    And I have previously used backup code "BACKUP123"
    When I attempt to verify with the used backup code
    Then I should receive a 400 Bad Request response
    And the error should mention backup code already used

  # =============================================================================
  # MFA STATUS AND MANAGEMENT SCENARIOS
  # =============================================================================

  @mfa @status @happy-path @core
  Scenario: Get MFA Status for Enabled Account
    Given I have MFA enabled with TOTP method
    When I request my MFA status
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the response should show MFA is enabled
    And the response should show the MFA method
    And the response should show backup codes count
    And the response should include setup date

  @mfa @status @happy-path @disabled
  Scenario: Get MFA Status for Disabled Account
    Given I do not have MFA enabled
    When I request my MFA status
    Then I should receive a 200 OK response
    And the response should show MFA is disabled
    And the response should show available MFA methods

  @mfa @disable @happy-path @core
  Scenario: Successful MFA Disable with Password Verification
    Given I have MFA enabled
    When I disable MFA with:
      | password | SecurePass123! |
      | reason   | No longer needed |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And MFA should be disabled for my account
    And all backup codes should be invalidated
    And the response should confirm MFA disabled

  @mfa @disable @validation @negative @wrong-password
  Scenario: MFA Disable with Wrong Password
    Given I have MFA enabled
    When I attempt to disable MFA with wrong password "WrongPassword123!"
    Then I should receive a 400 Bad Request response
    And the error should mention incorrect password
    And MFA should remain enabled

  @mfa @regenerate-backup-codes @happy-path @core
  Scenario: Successful Backup Codes Regeneration
    Given I have MFA enabled
    When I regenerate backup codes with:
      | password | SecurePass123! |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And I should receive new backup codes
    And old backup codes should be invalidated
    And the response should confirm codes regenerated

  # =============================================================================
  # SECURITY AND EDGE CASES
  # =============================================================================

  @mfa @security @rate-limiting @negative
  Scenario: MFA Challenge Rate Limiting
    Given I have an active MFA challenge
    When I make multiple failed verification attempts
    Then I should be rate limited after several attempts
    And I should receive a 429 Too Many Requests response
    And the challenge should be temporarily locked

  @mfa @security @negative @brute-force
  Scenario: MFA Brute Force Protection
    Given I have MFA enabled
    When I make multiple failed MFA attempts across different sessions
    Then the system should detect brute force attempts
    And my account should be temporarily protected
    And security alerts should be triggered

  @mfa @edge-case @device-change
  Scenario: MFA After Device Change
    Given I have MFA enabled with device-specific settings
    When I login from a new device
    Then I should be required to complete MFA
    And the new device should be registered after successful MFA
    And I should receive device change notifications
