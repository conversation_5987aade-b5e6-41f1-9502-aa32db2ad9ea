@auth
Feature: Email Verification
  As a registered user
  I want to verify my email address
  So that I can activate my account and access the platform

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"

  # =============================================================================
  # HAPPY PATH SCENARIOS
  # =============================================================================

  @verification @happy-path @core
  Scenario: Successful Email Verification with Complete Flow
    Given I have registered for an account with email "test.verification.{{random}}@businesscorp.com"
    And I have received a verification email with valid token
    When I verify my email with the provided token
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And my email should be marked as verified
    And my account status should be updated to active
    And the verification message should confirm success
    And the response should contain user information
    And I should receive access and refresh tokens for auto-login
    And the response time should be reasonable

  @verification @welcome-email @happy-path @core
  Scenario: Welcome Email Sent After Successful Email Verification
    Given I have registered for an account with email "test.welcome.{{random}}@businesscorp.com"
    And I have received a verification email with valid token
    When I verify my email with the provided token
    Then I should receive a 200 OK response
    And my email should be marked as verified
    And a welcome email should be sent to my email address
    And the welcome email should contain organization setup instructions
    And the welcome email should include profile completion guidance
    And the welcome email should have links to documentation and onboarding resources

  # =============================================================================
  # NEGATIVE SCENARIOS - Invalid Tokens
  # =============================================================================

  @verification @validation @negative @invalid-token
  Scenario Outline: Email Verification with Invalid Token Formats
    Given I have registered for an account
    When I attempt to verify with invalid token "<token>"
    Then I should receive a 400 Bad Request response
    And the error should mention invalid or malformed token

    Examples:
      | token                    |
      | invalid-token-12345      |
      | malformed-token          |
      | 123                      |
      | token-with-special-@#$   |
      | empty-string             |
      | null                     |

  @verification @security @negative @expired-token
  Scenario: Email Verification with Expired Token
    Given I have registered for an account
    And I have an expired verification token
    When I attempt to verify with the expired token
    Then I should receive a 400 Bad Request response
    And the error should mention expired token
    And I should be prompted to request a new verification email

  @verification @validation @negative @missing-token
  Scenario: Email Verification without Token Parameter
    Given I have registered for an account
    When I attempt to verify without providing a token
    Then I should receive a 400 Bad Request response
    And the error should mention missing token parameter

  @verification @business-rules @negative @already-verified
  Scenario: Email Verification for Already Verified Account
    Given I have registered and verified my account
    When I attempt to verify again with the same token
    Then I should receive a 400 Bad Request response
    And the error should mention invalid or expired token

  @verification @security @negative @used-token
  Scenario: Email Verification with Previously Used Token
    Given I have registered for an account
    And I have successfully verified my email
    When I attempt to verify again with the same token
    Then I should receive a 400 Bad Request response
    And the error should mention token already used or invalid

  @verification @welcome-email @negative @already-verified
  Scenario: No Welcome Email for Already Verified Account
    Given I have registered and verified my account
    When I attempt to verify again with the same token
    Then I should receive a 400 Bad Request response
    And no welcome email should be sent

  @verification @welcome-email @idempotent @positive
  Scenario: Welcome Email Sent Only Once Per User
    Given I have registered for an account with email "test.idempotent.{{random}}@businesscorp.com"
    And I have received a verification email with valid token
    When I verify my email with the provided token
    Then I should receive a 200 OK response
    And a welcome email should be sent to my email address
    When I check the welcome email sending status for my user
    Then the welcome email should be marked as already sent
    And subsequent verification attempts should not trigger additional welcome emails

  # =============================================================================
  # RESEND VERIFICATION EMAIL SCENARIOS
  # =============================================================================

  @verification @resend @happy-path
  Scenario: Resend Verification Email for Unverified Account
    Given I have registered for an account
    And I have not verified my email
    When I request a new verification email
    Then I should receive a 200 OK response
    And a new verification email should be sent
    And the response should confirm email sent
    And the old token should be invalidated

  @verification @resend @negative @already-verified
  Scenario: Resend Verification Email for Already Verified Account
    Given I have registered and verified my account
    When I request a new verification email
    Then I should receive a 200 OK response
    And the response should contain generic verification message

  @verification @resend @rate-limiting @positive
  Scenario: Resend Verification Email Multiple Times (Rate Limiting Disabled)
    Given I have registered for an account
    And I have recently requested a verification email
    When I immediately request another verification email
    Then I should receive a 200 OK response
    And the response should confirm email sent

  # =============================================================================
  # EDGE CASES AND SECURITY SCENARIOS
  # =============================================================================

  @verification @security @negative @sql-injection
  Scenario: Email Verification with SQL Injection Attempt
    Given I have registered for an account
    When I attempt to verify with malicious token "'; DROP TABLE users; --"
    Then I should receive a 400 Bad Request response
    And the system should handle malicious input safely
    And no database operations should be compromised

  @verification @validation @negative @non-existent-user
  Scenario: Email Verification for Non-existent User
    Given I have a valid-looking verification token
    But the token belongs to a non-existent user
    When I attempt to verify with the token
    Then I should receive a 400 Bad Request response
    And the error should mention invalid token or user not found

  @verification @welcome-email @resilience @positive
  Scenario: Email Verification Succeeds Even if Welcome Email Fails
    Given I have registered for an account with email "test.resilience.{{random}}@businesscorp.com"
    And I have received a verification email with valid token
    And the notification service is temporarily unavailable
    When I verify my email with the provided token
    Then I should receive a 200 OK response
    And my email should be marked as verified
    And my account status should be updated to active
    And I should receive access and refresh tokens for auto-login
    But the welcome email sending should fail gracefully without affecting verification
