@auth
Feature: User Registration
  As a new user
  I want to register for Qeep
  So that I can access the platform and manage alerts

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"

  # =============================================================================
  # HEALTH CHECK SCENARIOS
  # =============================================================================

  @smoke @health
  Scenario: API Gateway Health Check
    When I check the health endpoint
    Then the system should be available
    And response time should be under 2 seconds

  # =============================================================================
  # SUCCESSFUL REGISTRATION SCENARIOS
  # =============================================================================

  @registration @happy-path @comprehensive @core
  Scenario: Successful User Registration with Complete Validation
    Given I am a new user with valid business email
    When I register with:
      | email                     | test.user.{{random}}@businesscorp.com |
      | password                  | SecurePass123!                         |
      | first_name                | John                                   |
      | last_name                 | Doe                                    |
      | accept_terms              | true                                   |
      | accept_privacy_policy     | true                                   |
      | receive_marketing_emails  | true                                   |
    Then I should receive a 201 Created response
    And the response should follow StandardApiResponse format
    And the response should contain success indicator
    And the response should contain status code 201
    And the response should contain descriptive message
    And the response should contain metadata with timestamp
    And the response should contain my user data
    And the user ID should be a valid UUID
    And the email should match what I provided
    And the first name should match what I provided
    And the last name should match what I provided
    And the verification email status should be indicated
    And no verification token should be exposed in the response
    And my password should not be returned
    And the user should be created with pending status
    And the user should be unverified initially
    And the response time should be reasonable

  @registration @email-verification @security @core
  Scenario: Registration Email Verification Security
    Given I am a new user with valid business email
    And the notification service is running
    When I register with valid credentials
    Then I should receive a 201 Created response
    And the response should contain verification_email_sent as true
    And the response should NOT contain verification_token
    And the response should NOT contain any sensitive tokens
    And a verification email should be sent to my email address
    And the verification token should only exist in the email content
    And the verification token should not be exposed via API

  @registration @email-failure @resilience @skip
  Scenario: Registration with Email Service Unavailable
    Given I am a new user with valid business email
    And the notification service is unavailable
    When I register with valid credentials
    Then I should receive a 201 Created response
    And the response should contain verification_email_sent as false
    And the user account should still be created successfully
    And the user should be able to request email resend later

  @registration @response-format @api-contract @skip
  Scenario: Registration Response Format Validation
    Given I am a new user with valid business email
    When I register with complete valid data
    Then I should receive a 201 Created response
    And the response should have the correct structure:
      | field                    | type    | required |
      | success                  | boolean | true     |
      | status_code              | number  | true     |
      | message                  | string  | true     |
      | data                     | object  | true     |
      | meta                     | object  | true     |
    And the data object should contain:
      | field                    | type    | required |
      | user_id                  | string  | true     |
      | email                    | string  | true     |
      | first_name               | string  | true     |
      | last_name                | string  | true     |
      | verification_email_sent  | boolean | true     |
    And the data object should NOT contain:
      | field                    |
      | verification_token       |
      | password                 |
      | password_hash            |
    And all field names should follow snake_case convention

  # =============================================================================
  # VALIDATION ERROR SCENARIOS
  # =============================================================================

  @registration @validation @negative @required-fields @skip
  Scenario: Registration with Missing Required Fields
    Given I am a new user
    When I register with incomplete data:
      | email | <EMAIL> |
    Then I should receive a 400 Bad Request response
    And the error should mention required fields

  @registration @validation @negative @email-format @skip
  Scenario Outline: Registration with Various Invalid Email Formats
    Given I am a new user
    When I register with invalid email "<email>"
    Then I should receive a 400 Bad Request response
    And the error should mention email validation

    Examples:
      | email                    |
      | invalid-email-format     |
      | user@                    |
      | @domain.com              |
      | <EMAIL>    |
      | .<EMAIL>         |
      | <EMAIL>         |
      | user@domain.            |
      | user <EMAIL>     |
      | <EMAIL>         |
      | user@domain             |

  @registration @validation @negative @password @skip
  Scenario: Registration with Weak Password
    Given I am a new user
    When I register with weak password "123456"
    Then I should receive a 400 Bad Request response
    And the error should mention password requirements

  @registration @validation @negative @terms @skip
  Scenario: Registration without Accepting Terms
    Given I am a new user
    When I register without accepting terms and conditions
    Then I should receive a 400 Bad Request response
    And the error should mention terms acceptance

  # =============================================================================
  # BUSINESS EMAIL VALIDATION SCENARIOS
  # =============================================================================

  @registration @business-rules @negative @personal-email @skip
  Scenario Outline: Registration with Personal Email Domains
    Given I am a new user
    When I register with personal email domain "<domain>"
    Then I should receive a 400 Bad Request response
    And the error should mention business email requirement

    Examples:
      | domain        |
      | gmail.com     |
      | yahoo.com     |
      | hotmail.com   |
      | outlook.com   |
      | aol.com       |
      | icloud.com    |
      | protonmail.com|
      | mail.com      |
      | yandex.com    |

  @registration @business-rules @positive @business-email @skip
  Scenario Outline: Registration with Valid Business Email Formats
    Given I am a new user
    When I register with business email "<email>"
    Then I should receive a 201 Created response
    And the response should contain my user data

    Examples:
      | email                           |
      | <EMAIL>                |
      | <EMAIL>       |
      | <EMAIL>                |
      | <EMAIL>         |
      | <EMAIL>          |
      | <EMAIL>            |
      | <EMAIL>           |
      | <EMAIL>           |
      | <EMAIL>           |
      | <EMAIL>             |

  # =============================================================================
  # SECURITY AND EDGE CASE SCENARIOS
  # =============================================================================

  @registration @security @negative @sql-injection @skip
  Scenario: Registration with SQL Injection Attempt
    Given I am a malicious user
    When I attempt SQL injection in registration fields
    Then I should receive a 400 Bad Request response
    And the system should handle malicious input safely

  @registration @duplicate @negative @existing-user @skip
  Scenario: Registration with Duplicate Email
    Given a user already exists with email "<EMAIL>"
    When I try to register with the same email
    Then I should receive a 400 Bad Request response
    And the error should mention email already exists