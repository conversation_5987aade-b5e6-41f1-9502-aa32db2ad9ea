@auth
Feature: Session Management
  As a user
  I want to manage my active sessions
  So that I can monitor and control access to my account

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"
    And I am authenticated with a valid token

  # =============================================================================
  # LIST SESSIONS SCENARIOS
  # =============================================================================

  @session @list @happy-path @core
  Scenario: Successful List of Active Sessions
    Given I have multiple active sessions across different devices
    When I request my active sessions list
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And I should receive a list of my active sessions
    And each session should include session ID
    And each session should include device information
    And each session should include IP address
    And each session should include user agent
    And each session should include login timestamp
    And each session should include last activity timestamp
    And each session should include location information
    And the current session should be marked as current
    And the response time should be reasonable

  @session @list @authorization @negative @missing-auth
  Scenario: List Sessions without Authentication
    Given I am not authenticated
    When I attempt to list my sessions
    Then I should receive a 401 Unauthorized response
    And the error should mention missing authorization

  @session @list @authorization @negative @invalid-auth
  Scenario: List Sessions with Invalid Authentication
    Given I have an invalid JWT token
    When I attempt to list my sessions with the invalid token
    Then I should receive a 401 Unauthorized response
    And the error should mention invalid token

  @session @list @happy-path @single-session
  Scenario: List Sessions with Only Current Session
    Given I have only one active session (current)
    When I request my active sessions list
    Then I should receive a 200 OK response
    And I should receive a list with one session
    And the session should be marked as current
    And the session should include all required information

  @session @list @happy-path @empty
  Scenario: List Sessions when No Other Sessions Exist
    Given I have only the current session active
    When I request my active sessions list
    Then I should receive a 200 OK response
    And I should receive a list with only the current session
    And the response should indicate this is the only active session

  # =============================================================================
  # TERMINATE SESSION SCENARIOS
  # =============================================================================

  @session @terminate @happy-path @core
  Scenario: Successful Termination of Specific Session
    Given I have multiple active sessions
    And I have a session with ID "session-123" on another device
    When I terminate the session "session-123"
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the response should confirm session termination
    And the specified session should be terminated
    And the session should no longer appear in my sessions list
    And other sessions should remain active
    And the current session should remain active
    And the response time should be reasonable

  @session @terminate @authorization @negative @missing-auth
  Scenario: Terminate Session without Authentication
    Given I am not authenticated
    When I attempt to terminate a session
    Then I should receive a 401 Unauthorized response
    And the error should mention missing authorization

  @session @terminate @authorization @negative @invalid-auth
  Scenario: Terminate Session with Invalid Authentication
    Given I have an invalid JWT token
    When I attempt to terminate a session with the invalid token
    Then I should receive a 401 Unauthorized response
    And the error should mention invalid token

  @session @terminate @validation @negative @non-existent-session
  Scenario: Terminate Non-existent Session
    Given I have active sessions
    When I attempt to terminate non-existent session "non-existent-session-123"
    Then I should receive a 404 Not Found response
    And the error should mention session not found

  @session @terminate @security @negative @other-user-session
  Scenario: Terminate Another User's Session
    Given I am authenticated as user A
    And user B has an active session with ID "user-b-session-123"
    When I attempt to terminate user B's session
    Then I should receive a 404 Not Found response
    And the error should mention session not found
    And user B's session should remain active

  @session @terminate @edge-case @current-session
  Scenario: Attempt to Terminate Current Session
    Given I have multiple active sessions
    When I attempt to terminate my current session
    Then I should receive a 400 Bad Request response
    And the error should mention cannot terminate current session
    And the current session should remain active
    And I should be advised to use logout instead

  # =============================================================================
  # FORCE LOGOUT ALL SCENARIOS
  # =============================================================================

  @session @force-logout-all @happy-path @core
  Scenario: Successful Force Logout from All Sessions
    Given I have multiple active sessions across different devices
    When I force logout from all sessions with reason "Security concern"
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the response should confirm all sessions terminated
    And the response should include count of terminated sessions
    And all my sessions should be terminated
    And all my refresh tokens should be invalidated
    And all my access tokens should be blacklisted
    And I should be logged out from all devices
    And the response time should be reasonable

  @session @force-logout-all @authorization @negative @missing-auth
  Scenario: Force Logout All without Authentication
    Given I am not authenticated
    When I attempt to force logout from all sessions
    Then I should receive a 401 Unauthorized response
    And the error should mention missing authorization

  @session @force-logout-all @authorization @negative @invalid-auth
  Scenario: Force Logout All with Invalid Authentication
    Given I have an invalid JWT token
    When I attempt to force logout from all sessions with the invalid token
    Then I should receive a 401 Unauthorized response
    And the error should mention invalid token

  @session @force-logout-all @happy-path @single-session
  Scenario: Force Logout All with Only Current Session
    Given I have only one active session (current)
    When I force logout from all sessions
    Then I should receive a 200 OK response
    And the response should confirm all sessions terminated
    And the terminated sessions count should be 1
    And I should be logged out

  @session @force-logout-all @validation @optional-reason
  Scenario: Force Logout All without Reason
    Given I have multiple active sessions
    When I force logout from all sessions without providing a reason
    Then I should receive a 200 OK response
    And all sessions should be terminated
    And the operation should complete successfully

  # =============================================================================
  # SESSION INFORMATION AND SECURITY
  # =============================================================================

  @session @information @device-tracking
  Scenario: Session Information Includes Device Details
    Given I have sessions from different devices
    When I request my active sessions list
    Then I should receive detailed device information
    And each session should include device type
    And each session should include operating system
    And each session should include browser information
    And each session should include screen resolution
    And each session should include timezone information

  @session @information @location-tracking
  Scenario: Session Information Includes Location Details
    Given I have sessions from different locations
    When I request my active sessions list
    Then I should receive location information for each session
    And each session should include country
    And each session should include city
    And each session should include ISP information
    And location should be based on IP address

  @session @security @suspicious-activity
  Scenario: Session List Shows Suspicious Activity Indicators
    Given I have sessions with suspicious activity patterns
    When I request my active sessions list
    Then suspicious sessions should be flagged
    And I should receive security warnings
    And I should be advised to terminate suspicious sessions
    And I should be prompted to change password if needed

  # =============================================================================
  # EDGE CASES AND ERROR HANDLING
  # =============================================================================

  @session @edge-case @concurrent-termination
  Scenario: Concurrent Session Termination Requests
    Given I have multiple active sessions
    When I make concurrent requests to terminate the same session
    Then only one termination should succeed
    And other requests should handle the already-terminated session gracefully
    And I should receive appropriate responses for all requests

  @session @edge-case @session-expiry
  Scenario: Session Management with Expired Sessions
    Given I have sessions that have naturally expired
    When I request my active sessions list
    Then expired sessions should not appear in the list
    And only truly active sessions should be shown
    And the system should clean up expired sessions automatically

  @session @performance @large-session-count
  Scenario: Session Management with Many Active Sessions
    Given I have a large number of active sessions
    When I perform session management operations
    Then all operations should complete within reasonable time
    And the system should handle the large session count efficiently
    And pagination should be used if necessary

  @session @business-rules @session-limits
  Scenario: Session Management with Session Limits
    Given I have reached the maximum number of allowed sessions
    When I attempt to create a new session
    Then the oldest session should be automatically terminated
    And the new session should be created successfully
    And I should be notified about the session limit

  @session @audit @session-tracking
  Scenario: Session Activities are Properly Audited
    Given I perform various session management operations
    When I check the audit logs
    Then all session activities should be logged
    And each log entry should include timestamp
    And each log entry should include user identification
    And each log entry should include action performed
    And each log entry should include IP address and user agent

  @session @security @anomaly-detection
  Scenario: Session Anomaly Detection
    Given I have established normal session patterns
    When I create sessions from unusual locations or devices
    Then the system should detect anomalies
    And I should receive security notifications
    And additional verification may be required
    And suspicious sessions should be flagged for review
