@auth
Feature: Password Management
  As a user
  I want to manage my password securely
  So that I can maintain account security and recover access when needed

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"

  # =============================================================================
  # FORGOT PASSWORD SCENARIOS
  # =============================================================================

  @password @forgot-password @happy-path @core
  Scenario: Successful Forgot Password Request
    Given I have a registered and verified account with email "test.forgot.{{random}}@businesscorp.com"
    When I request a password reset for my email
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the response should confirm password reset email sent
    And a password reset email should be sent to my email
    And a password reset token should be generated
    And the token should have appropriate expiration time
    And the response time should be reasonable

  @password @forgot-password @negative @non-existent-email
  Scenario: Forgot Password for Non-existent Email
    Given no user exists with email "<EMAIL>"
    When I request a password reset for "<EMAIL>"
    Then I should receive a 200 OK response
    And the response should confirm password reset email sent
    And no actual email should be sent for security reasons
    And the response should not reveal user existence

  @password @forgot-password @validation @negative @invalid-email
  Scenario Outline: Forgot Password with Invalid Email Formats
    Given I have a registered account
    When I request a password reset with invalid email "<email>"
    Then I should receive a 400 Bad Request response
    And the error should mention email validation

    Examples:
      | email                    |
      | invalid-email-format     |
      | user@                    |
      | @domain.com              |
      | <EMAIL>    |
      | .<EMAIL>         |
      | <EMAIL>         |

  @password @forgot-password @validation @negative @missing-email
  Scenario: Forgot Password without Email
    Given I have a registered account
    When I request a password reset without providing an email
    Then I should receive a 400 Bad Request response
    And the error should mention email is required

  @password @forgot-password @rate-limiting @negative
  Scenario: Multiple Forgot Password Requests Rate Limiting
    Given I have a registered account
    And I have recently requested a password reset
    When I immediately request another password reset
    Then I should receive a 429 Too Many Requests response
    And the error should mention rate limiting

  # =============================================================================
  # RESET PASSWORD SCENARIOS
  # =============================================================================

  @password @reset-password @happy-path @core
  Scenario: Successful Password Reset with Valid Token
    Given I have requested a password reset
    And I have received a valid reset token
    When I reset my password with:
      | token       | {{validResetToken}} |
      | newPassword | NewSecurePass123!   |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the response should confirm password reset success
    And my password should be updated
    And the reset token should be invalidated
    And I should be able to login with the new password
    And the response time should be reasonable

  @password @reset-password @negative @invalid-token
  Scenario Outline: Password Reset with Invalid Tokens
    Given I have requested a password reset
    When I attempt to reset password with invalid token "<token>"
    Then I should receive a 400 Bad Request response
    And the error should mention invalid or expired token

    Examples:
      | token                    |
      | invalid-token-12345      |
      | malformed-token          |
      | expired-token            |
      | used-token               |

  @password @reset-password @negative @expired-token
  Scenario: Password Reset with Expired Token
    Given I have requested a password reset
    And the reset token has expired
    When I attempt to reset password with the expired token
    Then I should receive a 400 Bad Request response
    And the error should mention token expired
    And I should be prompted to request a new reset email

  @password @reset-password @validation @negative @weak-password
  Scenario: Password Reset with Weak Password
    Given I have a valid reset token
    When I attempt to reset password with weak password "123456"
    Then I should receive a 400 Bad Request response
    And the error should mention password requirements
    And the error should specify password complexity rules

  @password @reset-password @validation @negative @missing-fields
  Scenario: Password Reset with Missing Required Fields
    Given I have a valid reset token
    When I attempt to reset password without providing new password
    Then I should receive a 400 Bad Request response
    And the error should mention required fields

  @password @reset-password @security @negative @used-token
  Scenario: Password Reset with Previously Used Token
    Given I have successfully reset my password
    When I attempt to use the same reset token again
    Then I should receive a 400 Bad Request response
    And the error should mention token already used or invalid

  # =============================================================================
  # CHANGE PASSWORD SCENARIOS
  # =============================================================================

  @password @change-password @happy-path @core
  Scenario: Successful Password Change for Authenticated User
    Given I am authenticated with a valid token
    When I change my password with:
      | currentPassword | SecurePass123!    |
      | newPassword     | NewSecurePass123! |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the response should confirm password change success
    And my password should be updated
    And I should be able to login with the new password
    And my existing sessions should remain valid
    And the response time should be reasonable

  @password @change-password @authorization @negative @missing-token
  Scenario: Change Password without Authentication
    Given I am not authenticated
    When I attempt to change my password
    Then I should receive a 401 Unauthorized response
    And the error should mention missing authorization

  @password @change-password @authorization @negative @invalid-token
  Scenario: Change Password with Invalid Token
    Given I have an invalid JWT token
    When I attempt to change my password with the invalid token
    Then I should receive a 401 Unauthorized response
    And the error should mention invalid token

  @password @change-password @validation @negative @wrong-current-password
  Scenario: Change Password with Wrong Current Password
    Given I am authenticated with a valid token
    When I attempt to change password with wrong current password:
      | currentPassword | WrongPassword123! |
      | newPassword     | NewSecurePass123! |
    Then I should receive a 400 Bad Request response
    And the error should mention current password is incorrect

  @password @change-password @validation @negative @weak-new-password
  Scenario: Change Password with Weak New Password
    Given I am authenticated with a valid token
    When I attempt to change password with weak new password:
      | currentPassword | SecurePass123! |
      | newPassword     | 123456         |
    Then I should receive a 400 Bad Request response
    And the error should mention password requirements
    And the error should specify password complexity rules

  @password @change-password @validation @negative @same-password
  Scenario: Change Password to Same Password
    Given I am authenticated with a valid token
    When I attempt to change password to the same password:
      | currentPassword | SecurePass123! |
      | newPassword     | SecurePass123! |
    Then I should receive a 400 Bad Request response
    And the error should mention new password must be different

  @password @change-password @validation @negative @missing-fields
  Scenario: Change Password with Missing Required Fields
    Given I am authenticated with a valid token
    When I attempt to change password without current password:
      | newPassword | NewSecurePass123! |
    Then I should receive a 400 Bad Request response
    And the error should mention current password is required

  # =============================================================================
  # SECURITY AND EDGE CASES
  # =============================================================================

  @password @security @negative @sql-injection
  Scenario: Password Operations with SQL Injection Attempts
    Given I am authenticated with a valid token
    When I attempt to change password with malicious input:
      | currentPassword | SecurePass123!                    |
      | newPassword     | '; DROP TABLE users; --           |
    Then I should receive a 400 Bad Request response
    And the system should handle malicious input safely
    And no database operations should be compromised

  @password @business-rules @password-history
  Scenario: Password Change with Recently Used Password
    Given I am authenticated with a valid token
    And I have recently used password "RecentPassword123!"
    When I attempt to change password to the recently used password:
      | currentPassword | SecurePass123!    |
      | newPassword     | RecentPassword123! |
    Then I should receive a 400 Bad Request response
    And the error should mention password was recently used
    And the error should suggest using a different password

  @password @edge-case @concurrent-requests
  Scenario: Concurrent Password Reset Requests
    Given I have a registered account
    When I make multiple concurrent password reset requests
    Then only one reset token should be valid
    And previous tokens should be invalidated
    And I should receive appropriate responses for all requests
