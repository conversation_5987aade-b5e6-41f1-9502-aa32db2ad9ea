@auth @skip
Feature: Security Settings
  As a user or administrator
  I want to manage security settings and account security
  So that I can maintain proper account security and administrative control

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"

  # =============================================================================
  # GET SECURITY SETTINGS SCENARIOS
  # =============================================================================

  @security @settings @get @happy-path @core
  Scenario: Successful Retrieval of Security Settings
    Given I am authenticated with a valid token
    When I request my security settings
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And I should receive my security configuration
    And the settings should include MFA status
    And the settings should include WebAuthn credentials count
    And the settings should include password last changed date
    And the settings should include login attempt settings
    And the settings should include session timeout settings
    And the settings should include security notifications preferences
    And the settings should include account lockout settings
    And the response time should be reasonable

  @security @settings @get @authorization @negative @missing-auth
  Scenario: Get Security Settings without Authentication
    Given I am not authenticated
    When I attempt to get my security settings
    Then I should receive a 401 Unauthorized response
    And the error should mention missing authorization

  @security @settings @get @authorization @negative @invalid-auth
  Scenario: Get Security Settings with Invalid Authentication
    Given I have an invalid JWT token
    When I attempt to get my security settings with the invalid token
    Then I should receive a 401 Unauthorized response
    And the error should mention invalid token

  @security @settings @get @authorization @negative @expired-auth
  Scenario: Get Security Settings with Expired Authentication
    Given I have an expired JWT token
    When I attempt to get my security settings with the expired token
    Then I should receive a 401 Unauthorized response
    And the error should mention token expired

  @security @settings @get @happy-path @detailed-info
  Scenario: Security Settings Include Detailed Security Information
    Given I am authenticated with a valid token
    And I have various security features configured
    When I request my security settings
    Then I should receive comprehensive security information
    And the settings should include recent login history
    And the settings should include failed login attempts count
    And the settings should include active sessions count
    And the settings should include security events summary
    And the settings should include password strength indicators
    And the settings should include account verification status

  # =============================================================================
  # ACCOUNT UNLOCK SCENARIOS (ADMIN ONLY)
  # =============================================================================

  @security @unlock-account @admin @happy-path @core
  Scenario: Successful Account Unlock by Administrator
    Given I am authenticated as an administrator
    And there is a locked user account with ID "locked-user-123"
    When I unlock the account with:
      | userId | locked-user-123           |
      | reason | Customer support request  |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the response should confirm account unlocked
    And the user account should be unlocked
    And the unlock action should be logged
    And the user should be notified of the unlock
    And the response time should be reasonable

  @security @unlock-account @authorization @negative @missing-auth
  Scenario: Account Unlock without Authentication
    Given I am not authenticated
    When I attempt to unlock a user account
    Then I should receive a 401 Unauthorized response
    And the error should mention missing authorization

  @security @unlock-account @authorization @negative @insufficient-permissions
  Scenario: Account Unlock without Admin Permissions
    Given I am authenticated as a regular user
    When I attempt to unlock a user account
    Then I should receive a 403 Forbidden response
    And the error should mention insufficient permissions
    And the error should indicate admin role required

  @security @unlock-account @authorization @negative @wrong-role
  Scenario: Account Unlock with Wrong Role
    Given I am authenticated with role "customer"
    When I attempt to unlock a user account
    Then I should receive a 403 Forbidden response
    And the error should mention insufficient permissions
    And the account should remain locked

  @security @unlock-account @validation @negative @non-existent-user
  Scenario: Unlock Non-existent User Account
    Given I am authenticated as an administrator
    When I attempt to unlock non-existent user "non-existent-user-123"
    Then I should receive a 404 Not Found response
    And the error should mention user not found

  @security @unlock-account @validation @negative @already-unlocked
  Scenario: Unlock Already Unlocked Account
    Given I am authenticated as an administrator
    And there is an active user account with ID "active-user-123"
    When I attempt to unlock the already active account
    Then I should receive a 400 Bad Request response
    And the error should mention account is not locked

  @security @unlock-account @validation @negative @missing-user-id
  Scenario: Account Unlock without User ID
    Given I am authenticated as an administrator
    When I attempt to unlock an account without providing user ID
    Then I should receive a 400 Bad Request response
    And the error should mention user ID is required

  # =============================================================================
  # SECURITY NOTIFICATIONS AND ALERTS
  # =============================================================================

  @security @notifications @preferences @happy-path
  Scenario: Security Settings Include Notification Preferences
    Given I am authenticated with a valid token
    When I request my security settings
    Then I should receive notification preferences
    And the preferences should include login alerts
    And the preferences should include password change alerts
    And the preferences should include MFA setup alerts
    And the preferences should include suspicious activity alerts
    And the preferences should include new device alerts
    And the preferences should include account lockout alerts

  @security @alerts @recent-activity
  Scenario: Security Settings Show Recent Security Events
    Given I am authenticated with a valid token
    And I have recent security events
    When I request my security settings
    Then I should receive recent security events
    And events should include login attempts
    And events should include password changes
    And events should include MFA activities
    And events should include session activities
    And events should include failed authentication attempts
    And each event should include timestamp and details

  # =============================================================================
  # SECURITY RECOMMENDATIONS
  # =============================================================================

  @security @recommendations @weak-security
  Scenario: Security Settings Provide Recommendations for Weak Security
    Given I am authenticated with a valid token
    And I have weak security configuration
    When I request my security settings
    Then I should receive security recommendations
    And recommendations should suggest enabling MFA
    And recommendations should suggest stronger password
    And recommendations should suggest WebAuthn setup
    And recommendations should suggest reviewing active sessions
    And recommendations should be prioritized by importance

  @security @recommendations @strong-security
  Scenario: Security Settings for Well-Secured Account
    Given I am authenticated with a valid token
    And I have strong security configuration
    When I request my security settings
    Then I should receive positive security status
    And the response should confirm good security posture
    And minimal recommendations should be provided
    And the account should be marked as well-secured

  # =============================================================================
  # SECURITY METRICS AND ANALYTICS
  # =============================================================================

  @security @metrics @login-patterns
  Scenario: Security Settings Include Login Pattern Analysis
    Given I am authenticated with a valid token
    And I have established login patterns
    When I request my security settings
    Then I should receive login pattern analysis
    And the analysis should include common login times
    And the analysis should include common login locations
    And the analysis should include common devices
    And the analysis should highlight unusual patterns

  @security @metrics @risk-assessment
  Scenario: Security Settings Include Risk Assessment
    Given I am authenticated with a valid token
    When I request my security settings
    Then I should receive a security risk assessment
    And the assessment should include overall risk score
    And the assessment should include risk factors
    And the assessment should include mitigation suggestions
    And the assessment should be updated regularly

  # =============================================================================
  # EDGE CASES AND ERROR HANDLING
  # =============================================================================

  @security @edge-case @concurrent-unlock
  Scenario: Concurrent Account Unlock Attempts
    Given I am authenticated as an administrator
    And there is a locked user account
    When multiple administrators attempt to unlock the same account simultaneously
    Then only one unlock should succeed
    And other attempts should handle the already-unlocked state gracefully
    And all actions should be properly logged

  @security @edge-case @self-unlock
  Scenario: Administrator Attempting to Unlock Own Account
    Given I am authenticated as an administrator
    And my own account is locked
    When I attempt to unlock my own account
    Then I should receive a 400 Bad Request response
    And the error should mention cannot unlock own account
    And the account should remain locked

  @security @performance @large-security-data
  Scenario: Security Settings with Large Amount of Security Data
    Given I am authenticated with a valid token
    And I have extensive security history
    When I request my security settings
    Then the response should be delivered within reasonable time
    And the data should be properly paginated if necessary
    And the most relevant information should be prioritized

  @security @audit @admin-actions
  Scenario: Administrative Security Actions are Properly Audited
    Given I am authenticated as an administrator
    When I perform account unlock operations
    Then all actions should be logged in audit trail
    And each log entry should include administrator ID
    And each log entry should include target user ID
    And each log entry should include action performed
    And each log entry should include reason provided
    And each log entry should include timestamp and IP address

  @security @compliance @data-protection
  Scenario: Security Settings Respect Data Protection Requirements
    Given I am authenticated with a valid token
    When I request my security settings
    Then sensitive information should be properly masked
    And personal data should be handled according to privacy laws
    And data retention policies should be respected
    And user consent should be considered for data processing

  @security @integration @external-security-services
  Scenario: Security Settings Integration with External Services
    Given I am authenticated with a valid token
    And external security services are configured
    When I request my security settings
    Then integration status should be included
    And external service health should be reported
    And any external security alerts should be included
    And service dependencies should be clearly indicated
