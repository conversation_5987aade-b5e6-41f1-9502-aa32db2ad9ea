@auth
Feature: Token Management
  As a user
  I want to manage my authentication tokens securely
  So that I can maintain secure sessions and control access to my account

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"

  # =============================================================================
  # REFRESH TOKEN SCENARIOS
  # =============================================================================

  @token @refresh-token @happy-path @core
  Scenario: Successful Token Refresh with Valid Refresh Token
    Given I am authenticated with valid access and refresh tokens
    When I refresh my tokens with:
      | refreshToken | {{validRefreshToken}} |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And I should receive a new access token
    And I should receive a new refresh token
    And the new tokens should be valid
    And the old refresh token should be invalidated
    And the token expiration should be appropriate
    And the response time should be reasonable

  @token @refresh-token @negative @invalid-token
  Scenario Outline: Token Refresh with Invalid Refresh Tokens
    Given I have an authentication session
    When I attempt to refresh tokens with invalid refresh token "<token>"
    Then I should receive a 401 Unauthorized response
    And the error should mention invalid refresh token

    Examples:
      | token                    |
      | invalid-token-12345      |
      | malformed-token          |
      | expired-token            |
      | revoked-token            |

  @token @refresh-token @negative @expired-token
  Scenario: Token Refresh with Expired Refresh Token
    Given I have an expired refresh token
    When I attempt to refresh tokens with the expired token
    Then I should receive a 401 Unauthorized response
    And the error should mention invalid refresh token
    And I should be required to login again

  @token @refresh-token @validation @negative @missing-token
  Scenario: Token Refresh without Refresh Token
    Given I have an authentication session
    When I attempt to refresh tokens without providing a refresh token
    Then I should receive a 400 Bad Request response
    And the error should mention refresh token is required

  @token @refresh-token @security @negative @blacklisted-token
  Scenario: Token Refresh with Blacklisted Refresh Token
    Given I have a blacklisted refresh token
    When I attempt to refresh tokens with the blacklisted token
    Then I should receive a 401 Unauthorized response
    And the error should mention invalid refresh token

  # =============================================================================
  # LOGOUT SCENARIOS
  # =============================================================================

  @token @logout @happy-path @core
  Scenario: Successful Logout with Valid Tokens
    Given I am authenticated with valid access and refresh tokens
    When I logout with:
      | refreshToken | {{validRefreshToken}} |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the response should confirm logout success
    And my refresh token should be invalidated
    And my access token should be blacklisted
    And subsequent requests with the tokens should fail
    And the response time should be reasonable

  @token @logout @negative @invalid-refresh-token
  Scenario: Logout with Invalid Refresh Token
    Given I am authenticated
    When I attempt to logout with invalid refresh token "invalid-token-123"
    Then I should receive a 400 Bad Request response
    And the error should mention logout failed

  @token @logout @validation @negative @missing-refresh-token
  Scenario: Logout without Refresh Token
    Given I am authenticated
    When I attempt to logout without providing a refresh token
    Then I should receive a 400 Bad Request response
    And the error should mention refresh token is required

  @token @logout @edge-case @already-logged-out
  Scenario: Logout with Already Invalidated Token
    Given I have previously logged out
    When I attempt to logout again with the same refresh token
    Then I should receive a 400 Bad Request response
    And the error should mention logout failed

  # =============================================================================
  # REVOKE TOKEN SCENARIOS
  # =============================================================================

  @token @revoke-token @happy-path @core
  Scenario: Successful Single Token Revocation
    Given I am authenticated with multiple active sessions
    When I revoke a specific refresh token:
      | refreshToken | {{specificRefreshToken}} |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the response should confirm token revocation
    And the specified token should be invalidated
    And other tokens should remain valid
    And the response time should be reasonable

  @token @revoke-token @authorization @negative @missing-auth
  Scenario: Revoke Token without Authentication
    Given I am not authenticated
    When I attempt to revoke a token
    Then I should receive a 401 Unauthorized response
    And the error should mention missing authorization

  @token @revoke-token @authorization @negative @invalid-auth
  Scenario: Revoke Token with Invalid Authentication
    Given I have an invalid JWT token
    When I attempt to revoke a token with the invalid auth token
    Then I should receive a 401 Unauthorized response
    And the error should mention invalid token

  @token @revoke-token @validation @negative @invalid-refresh-token
  Scenario: Revoke Invalid Refresh Token
    Given I am authenticated with a valid token
    When I attempt to revoke an invalid refresh token "invalid-token-123"
    Then I should receive a 400 Bad Request response
    And the error should mention token revocation failed

  @token @revoke-token @validation @negative @missing-refresh-token
  Scenario: Revoke Token without Refresh Token
    Given I am authenticated with a valid token
    When I attempt to revoke a token without providing refresh token
    Then I should receive a 400 Bad Request response
    And the error should mention refresh token is required

  @token @revoke-token @security @negative @other-user-token
  Scenario: Revoke Another User's Token
    Given I am authenticated as user A
    And user B has a valid refresh token
    When I attempt to revoke user B's refresh token
    Then I should receive a 400 Bad Request response
    And the error should mention token revocation failed
    And user B's token should remain valid

  # =============================================================================
  # REVOKE ALL TOKENS SCENARIOS
  # =============================================================================

  @token @revoke-all-tokens @happy-path @core
  Scenario: Successful Revocation of All User Tokens
    Given I am authenticated with multiple active sessions
    When I revoke all my tokens
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the response should confirm all tokens revoked
    And the response should include count of revoked tokens
    And all my refresh tokens should be invalidated
    And all my access tokens should be blacklisted
    And I should be logged out of all sessions
    And the response time should be reasonable

  @token @revoke-all-tokens @authorization @negative @missing-auth
  Scenario: Revoke All Tokens without Authentication
    Given I am not authenticated
    When I attempt to revoke all tokens
    Then I should receive a 401 Unauthorized response
    And the error should mention missing authorization

  @token @revoke-all-tokens @authorization @negative @invalid-auth
  Scenario: Revoke All Tokens with Invalid Authentication
    Given I have an invalid JWT token
    When I attempt to revoke all tokens with the invalid auth token
    Then I should receive a 401 Unauthorized response
    And the error should mention invalid token

  @token @revoke-all-tokens @edge-case @no-active-tokens
  Scenario: Revoke All Tokens when No Active Tokens Exist
    Given I am authenticated but have no other active sessions
    When I revoke all my tokens
    Then I should receive a 200 OK response
    And the response should confirm all tokens revoked
    And the revoked tokens count should be appropriate

  # =============================================================================
  # SECURITY AND EDGE CASES
  # =============================================================================

  @token @security @negative @token-reuse
  Scenario: Attempt to Reuse Invalidated Refresh Token
    Given I have refreshed my tokens
    And the old refresh token has been invalidated
    When I attempt to use the old refresh token again
    Then I should receive a 401 Unauthorized response
    And the error should mention invalid refresh token
    And no new tokens should be issued

  @token @security @negative @concurrent-refresh
  Scenario: Concurrent Token Refresh Requests
    Given I am authenticated with a valid refresh token
    When I make multiple concurrent token refresh requests
    Then only one request should succeed
    And other requests should fail with appropriate errors
    And only one set of new tokens should be issued

  @token @security @negative @sql-injection
  Scenario: Token Operations with SQL Injection Attempts
    Given I am authenticated
    When I attempt to refresh tokens with malicious input "'; DROP TABLE tokens; --"
    Then I should receive a 400 Bad Request response
    And the system should handle malicious input safely
    And no database operations should be compromised

  @token @business-rules @token-lifetime
  Scenario: Token Refresh Near Expiration
    Given I have a refresh token that expires soon
    When I refresh my tokens before expiration
    Then I should receive new valid tokens
    And the new tokens should have full lifetime
    And the old tokens should be properly invalidated

  @token @edge-case @forced-logout
  Scenario: Token Operations After Forced Logout
    Given I have been forcefully logged out by an administrator
    When I attempt to refresh my tokens
    Then I should receive a 401 Unauthorized response
    And the error should mention user session has been terminated
    And I should be required to login again

  @token @performance @high-volume
  Scenario: Token Management Under High Load
    Given I have multiple active sessions
    When I perform multiple token operations simultaneously
    Then all operations should complete within reasonable time
    And the system should handle the load appropriately
    And no tokens should be left in inconsistent state
