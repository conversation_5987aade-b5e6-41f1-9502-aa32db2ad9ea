@auth
Feature: User Profile Management
  As an authenticated user
  I want to access and manage my profile
  So that I can view and update my account information

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"
    And I am authenticated with a valid token

  # =============================================================================
  # HAPPY PATH SCENARIOS
  # =============================================================================

  @profile @happy-path @core
  Scenario: Successful Profile Access with Complete Information
    Given I have a valid access token
    When I request my profile information
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the response should contain my user data
    And the response should include my user ID
    And the response should include my email
    And the response should include my first and last name
    And the response should include my tenant code
    And the response should include my account status
    And the response should include my email verification status
    And the response should include my last login information
    And the response should include my account creation date
    And the response should include my account update date
    And my password should not be included
    And the response time should be reasonable

  # =============================================================================
  # AUTHORIZATION ERROR SCENARIOS
  # =============================================================================

  @profile @authorization @negative @missing-token
  Scenario: Profile Access without Authorization Token
    Given I am not authenticated
    When I attempt to access my profile without a token
    Then I should receive a 401 Unauthorized response
    And the error should mention missing authorization
    And the error should indicate authentication is required

  @profile @authorization @negative @invalid-token
  Scenario: Profile Access with Invalid Token
    Given I have an invalid JWT token
    When I attempt to access my profile with token "invalid.jwt.token"
    Then I should receive a 401 Unauthorized response
    And the error should mention invalid token
    And the error should indicate token verification failed

  @profile @authorization @negative @malformed-token
  Scenario: Profile Access with Malformed Token
    Given I have a malformed JWT token
    When I attempt to access my profile with token "malformed.token"
    Then I should receive a 401 Unauthorized response
    And the error should mention invalid token format
    And the error should indicate token is not properly formatted

  @profile @authorization @negative @expired-token
  Scenario: Profile Access with Expired Token
    Given I have an expired JWT token
    When I attempt to access my profile with the expired token
    Then I should receive a 401 Unauthorized response
    And the error should mention token expired
    And the error should indicate token has expired

  @profile @authorization @negative @revoked-token
  Scenario: Profile Access with Revoked Token
    Given I have a revoked JWT token
    When I attempt to access my profile with the revoked token
    Then I should receive a 401 Unauthorized response
    And the error should mention token has been revoked
    And the error should indicate token is no longer valid

  # =============================================================================
  # SECURITY SCENARIOS
  # =============================================================================

  @profile @security @negative @blacklisted-token
  Scenario: Profile Access with Blacklisted Token
    Given I have a blacklisted JWT token
    When I attempt to access my profile with the blacklisted token
    Then I should receive a 401 Unauthorized response
    And the error should mention token has been revoked
    And the system should log the security event

  @profile @security @negative @forced-logout
  Scenario: Profile Access After Forced Logout
    Given I have been forcefully logged out by an administrator
    When I attempt to access my profile with my previous token
    Then I should receive a 401 Unauthorized response
    And the error should mention user session has been terminated
    And the error should indicate forced logout

  @profile @security @negative @tampered-token
  Scenario: Profile Access with Tampered Token
    Given I have a JWT token with modified payload
    When I attempt to access my profile with the tampered token
    Then I should receive a 401 Unauthorized response
    And the error should mention invalid token signature
    And the system should handle the security violation safely

  # =============================================================================
  # EDGE CASES AND ERROR HANDLING
  # =============================================================================

  @profile @edge-case @negative @deleted-user
  Scenario: Profile Access for Deleted User
    Given my user account has been deleted
    But I still have a valid token
    When I attempt to access my profile
    Then I should receive a 401 Unauthorized response
    And the error should mention user not found or inactive

  @profile @edge-case @negative @suspended-user
  Scenario: Profile Access for Suspended User
    Given my user account has been suspended
    When I attempt to access my profile with a valid token
    Then I should receive a 401 Unauthorized response
    And the error should mention account is suspended

  # =============================================================================
  # RESPONSE FORMAT VALIDATION
  # =============================================================================

  @profile @validation @response-format
  Scenario: Profile Response Contains All Required Fields
    Given I have a valid access token
    When I request my profile information
    Then I should receive a 200 OK response
    And the response should contain field "id" as string
    And the response should contain field "email" as string
    And the response should contain field "firstName" as string
    And the response should contain field "lastName" as string
    And the response should contain field "tenantCode" as string or null
    And the response should contain field "status" as string
    And the response should contain field "isEmailVerified" as boolean
    And the response should contain field "createdAt" as timestamp
    And the response should contain field "updatedAt" as timestamp
    And the response should not contain field "passwordHash"
    And the response should not contain field "password"

  @profile @validation @data-types
  Scenario: Profile Response Data Type Validation
    Given I have a valid access token
    When I request my profile information
    Then I should receive a 200 OK response
    And the user ID should be a valid UUID format
    And the email should be a valid email format
    And the timestamps should be in ISO 8601 format
    And the boolean fields should be true or false
    And numeric fields should be valid numbers

  @profile @happy-path
  Scenario: Successful Profile Access
    Given I have a valid access token
    When I request my profile information
    Then I should receive a 200 OK response
    And the response should contain my user data
    And the response should include my email
    And the response should include my first and last name
    And my password should not be included

  @profile @authorization @negative
  Scenario: Profile Access without Authorization Token
    Given I am not authenticated
    When I attempt to access my profile without a token
    Then I should receive a 401 Unauthorized response
    And the error should mention missing authorization

  @profile @authorization @negative
  Scenario: Profile Access with Invalid Token
    Given I have an invalid JWT token
    When I attempt to access my profile with token "invalid.jwt.token"
    Then I should receive a 401 Unauthorized response
    And the error should mention invalid token

  @profile @authorization @negative
  Scenario: Profile Access with Expired Token
    Given I have an expired JWT token
    When I attempt to access my profile with the expired token
    Then I should receive a 401 Unauthorized response
    And the error should mention token expired

  @profile @authorization @negative
  Scenario: Profile Access with Malformed Token
    Given I have a malformed JWT token
    When I attempt to access my profile with token "malformed.token"
    Then I should receive a 401 Unauthorized response
    And the error should mention invalid token format

  @profile @update
  Scenario: Update Profile Information
    Given I have a valid access token
    When I update my profile with:
      | firstName | UpdatedFirst |
      | lastName  | UpdatedLast  |
    Then I should receive a 200 OK response
    And my profile should reflect the changes
    And the response should contain updated information
