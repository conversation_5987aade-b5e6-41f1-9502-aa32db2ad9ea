@auth
Feature: User Login
  As a registered user
  I want to login to Qeep
  So that I can access my account and manage alerts

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"
    And I have a registered account

  # =============================================================================
  # HAPPY PATH SCENARIOS
  # =============================================================================

  @login @happy-path @core
  Scenario: Successful Login with Verified Account (Auto-Login from Verification)
    Given I have verified my email address
    # Note: Verification already provides auto-login with tokens
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And I should receive an access token
    And I should receive a refresh token
    And the tokens should be valid
    And the response should contain user information
    And the user information should include my email
    And the user information should include verification status
    And my password should not be included in the response
    And the response time should be reasonable

  @login @optional-fields @happy-path
  Scenario: Successful Login with Remember Me Option
    Given I have verified my email address
    When I login with remember me enabled:
      | email      | {{testUserEmail}} |
      | password   | SecurePass123!    |
      | rememberMe | true              |
    Then I should receive a 200 OK response
    And I should receive tokens with extended expiration
    And the refresh token should have longer validity

  @login @optional-fields @happy-path
  Scenario: Successful Login with Device ID
    Given I have verified my email address
    When I login with device tracking:
      | email    | {{testUserEmail}} |
      | password | SecurePass123!    |
      | deviceId | mobile-app-123    |
    Then I should receive a 200 OK response
    And the login should be tracked for the device
    And the response should contain session information

  # =============================================================================
  # NEGATIVE SCENARIOS - Authentication Failures
  # =============================================================================

  @login @unverified @negative
  Scenario: Login Attempt with Unverified Email
    Given I have not verified my email address
    When I attempt to login with correct credentials
    Then I should receive a 401 Unauthorized response
    And the error should mention email verification required
    And the error code should be "EMAIL_NOT_VERIFIED"

  @login @invalid-credentials @negative
  Scenario: Login with Wrong Password
    Given I have a verified account
    When I attempt to login with wrong password:
      | email    | {{testUserEmail}} |
      | password | WrongPassword123! |
    Then I should receive a 401 Unauthorized response
    And the error should mention invalid credentials
    And the error code should be "INVALID_CREDENTIALS"
    And the failed login attempt should be recorded

  @login @nonexistent @negative
  Scenario: Login with Non-existent User
    Given no user exists with email "<EMAIL>"
    When I attempt to login:
      | email    | <EMAIL> |
      | password | SecurePass123!               |
    Then I should receive a 401 Unauthorized response
    And the error should mention invalid credentials
    And the error code should be "INVALID_CREDENTIALS"

  # =============================================================================
  # VALIDATION ERROR SCENARIOS
  # =============================================================================

  @login @validation @negative @required-fields
  Scenario: Login with Missing Email
    Given I have a verified account
    When I attempt to login without email:
      | password | SecurePass123! |
    Then I should receive a 400 Bad Request response
    And the error should mention email is required

  @login @validation @negative @required-fields
  Scenario: Login with Missing Password
    Given I have a verified account
    When I attempt to login without password:
      | email | {{testUserEmail}} |
    Then I should receive a 400 Bad Request response
    And the error should mention password is required

  @login @validation @negative @email-format
  Scenario Outline: Login with Invalid Email Formats
    Given I have a verified account
    When I attempt to login with invalid email "<email>":
      | email    | <email>        |
      | password | SecurePass123! |
    Then I should receive a 400 Bad Request response
    And the error should mention email validation

    Examples:
      | email                    |
      | invalid-email-format     |
      | user@                    |
      | @domain.com              |
      | <EMAIL>    |
      | .<EMAIL>         |
      | <EMAIL>         |
      | user@domain.            |
      | user <EMAIL>     |

  # =============================================================================
  # SECURITY SCENARIOS
  # =============================================================================

  @login @security @negative @tenant-header
  Scenario: Login with Missing Tenant Header
    Given I have a verified account
    When I attempt to login without tenant header
    Then I should receive a 400 Bad Request response
    And the error should mention missing tenant information

  @login @rate-limiting @negative @account-lockout
  Scenario: Multiple Failed Login Attempts Leading to Account Lockout
    Given I have a verified account
    When I make multiple failed login attempts with wrong password
    Then my account should be temporarily locked
    And I should receive a 401 Unauthorized response
    And the error should mention account lockout
    And the error code should be "ACCOUNT_LOCKED"
    And subsequent login attempts should be blocked until lockout expires

  @login @security @negative @sql-injection
  Scenario: Login with SQL Injection Attempt
    Given I have a verified account
    When I attempt to login with malicious input:
      | email    | admin'; DROP TABLE users; -- |
      | password | SecurePass123!               |
    Then I should receive a 400 Bad Request response
    And the system should handle malicious input safely
    And no database operations should be compromised

  # =============================================================================
  # EDGE CASES AND BUSINESS RULES
  # =============================================================================

  @login @business-rules @negative @inactive-account
  Scenario: Login with Inactive Account
    Given I have an inactive account
    When I attempt to login with correct credentials
    Then I should receive a 401 Unauthorized response
    And the error should mention account is not active

  @login @validation @negative @password-length
  Scenario: Login with Extremely Long Password
    Given I have a verified account
    When I attempt to login with password exceeding 128 characters
    Then I should receive a 400 Bad Request response
    And the error should mention password length limit

  # Note: Duplicate scenarios removed - keeping only the comprehensive versions above
