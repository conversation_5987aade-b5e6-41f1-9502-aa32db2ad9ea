@auth @email-check
Feature: Email Existence Check
  As a system
  I want to check if an email address is already registered
  So that I can provide appropriate feedback during registration and password reset flows

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"

  @email-check @happy-path @positive
  Scenario: Check existing email address
    Given I have registered an account with email "<EMAIL>"
    When I check if the registered email exists
    Then I should receive a 200 OK response
    And the response should indicate email exists
    And the response should follow StandardApiResponse format

  @email-check @happy-path @negative
  Scenario: Check non-existent email address
    When I check if email "<EMAIL>" exists
    Then I should receive a 200 OK response
    And the response should indicate email does not exist
    And the response should follow StandardApiResponse format

  @email-check @validation @negative @invalid-email
  Scenario Outline: Check email with invalid formats
    When I check if email "<invalid_email>" exists
    Then I should receive a 400 Bad Request response
    And the error should mention invalid email format

    Examples:
      | invalid_email           |
      | invalid-email           |
      | @businesscorp.com       |
      | user@                   |
      | user@.com               |
      | user <EMAIL>  |
      | <EMAIL>|

  @email-check @validation @negative @missing-email
  Scenario: Check email without providing email parameter
    When I check email existence without providing email parameter
    Then I should receive a 400 Bad Request response
    And the error should mention missing email parameter

  @email-check @validation @negative @empty-email
  Scenario: Check email with empty email parameter
    When I check if email "" exists
    Then I should receive a 400 Bad Request response
    And the error should mention invalid email format

  @email-check @security @negative @sql-injection
  Scenario: Check email with SQL injection attempt
    When I check if email "<EMAIL>'; DROP TABLE users; --" exists
    Then I should receive a 400 Bad Request response
    And the error should mention invalid email format

  @email-check @security @negative @xss-attempt
  Scenario: Check email with XSS attempt
    When I check if email "<script>alert('xss')</script>@example.com" exists
    Then I should receive a 400 Bad Request response
    And the error should mention invalid email format

  @email-check @edge-cases @positive
  Scenario: Check email with maximum length
    When I check if email "<EMAIL>" exists
    Then I should receive a 200 OK response
    And the response should indicate email does not exist

  @email-check @edge-cases @negative @too-long
  Scenario: Check email that exceeds maximum length
    When I check if email with 255+ characters exists
    Then I should receive a 400 Bad Request response
    And the error should mention email too long

  @email-check @case-sensitivity @positive
  Scenario: Check email case sensitivity
    Given I have registered an account with email "<EMAIL>"
    When I check if the registered email exists in lowercase
    Then I should receive a 200 OK response
    And the response should indicate email does not exist

  @email-check @performance @positive
  Scenario: Check email response time
    When I check if email "<EMAIL>" exists
    Then I should receive a 200 OK response
    And the response time should be reasonable

  @email-check @rate-limiting @positive
  Scenario: Multiple email checks (Rate Limiting Disabled)
    When I check multiple emails rapidly
    Then all requests should receive 200 OK responses
    And no rate limiting should be applied
