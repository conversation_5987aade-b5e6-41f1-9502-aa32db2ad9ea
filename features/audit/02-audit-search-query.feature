@audit
Feature: 02 - Audit Search and Query
  As a compliance officer or investigator
  I want to search and query audit logs in the Qeep financial monitoring system
  So that I can investigate incidents and ensure regulatory compliance

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"
    And the audit service is available
    And I have tenant header "x-tenant-code" set to "financial-corp"
    And audit events exist in the system

  # =============================================================================
  # BASIC SEARCH SCENARIOS
  # =============================================================================

  @audit @search @basic @happy-path @core
  Scenario: Search Audit Events by Basic Criteria
    Given I am authenticated as a compliance officer
    When I search audit events with:
      | search_term     | user login                  |
      | time_range      | last_24_hours               |
      | event_category  | AUTHENTICATION              |
      | severity        | INFO                        |
      | page            | 1                           |
      | limit           | 50                          |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the results should contain matching audit events
    And the results should be properly paginated
    And the results should include event summaries
    And the results should be sorted by timestamp descending
    And the response time should be reasonable

  @audit @search @basic @user-specific
  Scenario: Search Audit Events for Specific User
    Given I am authenticated as a compliance officer
    When I search audit events with:
      | user_id         | user-123                    |
      | time_range      | last_week                   |
      | include_details | true                        |
    Then I should receive a 200 OK response
    And the results should contain events for the specified user
    And the results should include detailed event information
    And the results should show user activity timeline
    And the results should include session correlation

  @audit @search @basic @resource-specific
  Scenario: Search Audit Events for Specific Resource
    Given I am authenticated as a compliance officer
    When I search audit events with:
      | resource_type   | TRANSACTION                 |
      | resource_id     | txn-456                     |
      | include_changes | true                        |
    Then I should receive a 200 OK response
    And the results should contain events for the specified resource
    And the results should include before/after states
    And the results should show resource modification history
    And the results should include related events

  # =============================================================================
  # ADVANCED SEARCH SCENARIOS
  # =============================================================================

  @audit @search @advanced @happy-path @core
  Scenario: Advanced Search with Multiple Filters
    Given I am authenticated as an investigator
    When I perform advanced search with:
      | event_types     | ["USER_LOGIN", "DATA_ACCESS", "CONFIGURATION_CHANGE"] |
      | severity_levels | ["HIGH", "CRITICAL"]            |
      | source_services | ["auth-service", "user-service"] |
      | outcome         | FAILURE                         |
      | ip_address_range | ***********/24                |
      | time_range      | {"start": "2024-06-01", "end": "2024-06-30"} |
    Then I should receive a 200 OK response
    And the results should match all specified criteria
    And the results should be filtered correctly
    And the results should include relevance scoring
    And the results should support drill-down analysis

  @audit @search @advanced @correlation
  Scenario: Search Related Events by Correlation ID
    Given I am authenticated as an investigator
    When I search events by correlation with:
      | correlation_id  | corr-789                    |
      | include_related | true                        |
      | expand_timeline | true                        |
    Then I should receive a 200 OK response
    And the results should include all correlated events
    And the results should show event relationships
    And the results should include timeline visualization
    And the results should support causal analysis

  @audit @search @advanced @pattern-matching
  Scenario: Search Events Using Pattern Matching
    Given I am authenticated as a security analyst
    When I search events with pattern matching:
      | pattern_type    | suspicious_login_sequence   |
      | pattern_params  | {"failed_attempts": 5, "time_window": 300} |
      | user_behavior   | anomalous                   |
    Then I should receive a 200 OK response
    And the results should identify matching patterns
    And the results should include pattern confidence scores
    And the results should highlight anomalous behavior
    And the results should suggest investigation priorities

  # =============================================================================
  # FULL-TEXT SEARCH SCENARIOS
  # =============================================================================

  @audit @search @full-text @happy-path @core
  Scenario: Full-Text Search Across Audit Event Content
    Given I am authenticated as a compliance officer
    When I perform full-text search with:
      | search_query    | "suspicious transaction wire transfer" |
      | search_fields   | ["details", "changes", "notes"] |
      | highlight       | true                        |
      | fuzzy_matching  | true                        |
    Then I should receive a 200 OK response
    And the results should include text matches
    And matching text should be highlighted
    And fuzzy matches should be included
    And relevance scores should be provided

  @audit @search @full-text @complex-query
  Scenario: Complex Full-Text Search with Boolean Operators
    Given I am authenticated as an investigator
    When I perform complex full-text search with:
      | search_query    | "(money laundering OR suspicious) AND (wire transfer OR cash)" |
      | boolean_mode    | true                        |
      | case_sensitive  | false                       |
      | word_boundaries | true                        |
    Then I should receive a 200 OK response
    And the results should respect boolean operators
    And the results should include phrase matching
    And the results should support complex queries
    And the results should provide search explanations

  # =============================================================================
  # TIME-BASED SEARCH SCENARIOS
  # =============================================================================

  @audit @search @time-based @happy-path @core
  Scenario: Search Events Within Specific Time Range
    Given I am authenticated as a compliance officer
    When I search events within time range:
      | start_time      | 2024-06-01T00:00:00Z        |
      | end_time        | 2024-06-30T23:59:59Z        |
      | timezone        | America/New_York            |
      | granularity     | hour                        |
    Then I should receive a 200 OK response
    And the results should be within the specified time range
    And timestamps should be in the requested timezone
    And results should be grouped by time granularity
    And time-based analytics should be included

  @audit @search @time-based @relative-time
  Scenario: Search Events Using Relative Time Expressions
    Given I am authenticated as a compliance officer
    When I search events with relative time:
      | relative_time   | last_7_days                 |
      | business_hours_only | true                    |
      | exclude_weekends | true                       |
    Then I should receive a 200 OK response
    And the results should cover the relative time period
    And business hours filtering should be applied
    And weekend events should be excluded
    And time calculations should be accurate

  # =============================================================================
  # AGGREGATION AND ANALYTICS SCENARIOS
  # =============================================================================

  @audit @search @aggregation @happy-path @core
  Scenario: Aggregate Audit Events for Analytics
    Given I am authenticated as a compliance officer
    When I request event aggregations with:
      | group_by        | ["event_type", "severity", "outcome"] |
      | time_bucket     | daily                       |
      | metrics         | ["count", "unique_users", "unique_resources"] |
      | time_range      | last_month                  |
    Then I should receive a 200 OK response
    And the results should include aggregated data
    And the results should be grouped as specified
    And metrics should be calculated correctly
    And time-based bucketing should be applied

  @audit @search @aggregation @trend-analysis
  Scenario: Analyze Event Trends Over Time
    Given I am authenticated as a security analyst
    When I request trend analysis with:
      | event_category  | SECURITY                    |
      | trend_period    | last_quarter                |
      | trend_metrics   | ["frequency", "severity_distribution", "outcome_ratio"] |
      | comparison_period | previous_quarter          |
    Then I should receive a 200 OK response
    And the results should include trend data
    And the results should show period comparisons
    And trend indicators should be calculated
    And anomaly detection should be applied

  # =============================================================================
  # EXPORT AND REPORTING SCENARIOS
  # =============================================================================

  @audit @search @export @happy-path @core
  Scenario: Export Search Results for External Analysis
    Given I am authenticated as a compliance officer
    When I export search results with:
      | search_criteria | {"event_type": "TRANSACTION_PROCESSED", "time_range": "last_month"} |
      | export_format   | CSV                         |
      | include_headers | true                        |
      | max_records     | 10000                       |
      | anonymize_pii   | true                        |
    Then I should receive a 200 OK response
    And the export should be generated successfully
    And the export should be in the requested format
    And PII should be properly anonymized
    And export limits should be respected

  @audit @search @export @compliance-report
  Scenario: Generate Compliance Report from Search Results
    Given I am authenticated as a compliance officer
    When I generate compliance report with:
      | report_type     | regulatory_audit            |
      | search_criteria | {"event_category": "FINANCIAL_OPERATION", "severity": ["HIGH", "CRITICAL"]} |
      | report_format   | PDF                         |
      | include_summary | true                        |
      | certification   | true                        |
    Then I should receive a 200 OK response
    And the report should be generated
    And the report should include executive summary
    And the report should be certified for compliance
    And the report should meet regulatory standards

  # =============================================================================
  # TIMELINE RECONSTRUCTION SCENARIOS
  # =============================================================================

  @audit @search @timeline @happy-path @core
  Scenario: Reconstruct Event Timeline for Investigation
    Given I am authenticated as an investigator
    When I request timeline reconstruction with:
      | focus_entity    | user-123                    |
      | time_range      | {"start": "2024-06-01T08:00:00Z", "end": "2024-06-01T18:00:00Z"} |
      | include_context | true                        |
      | correlation_depth | 2                         |
    Then I should receive a 200 OK response
    And the timeline should be chronologically ordered
    And the timeline should include contextual events
    And the timeline should show event relationships
    And the timeline should support investigation workflow

  @audit @search @timeline @multi-entity
  Scenario: Reconstruct Timeline for Multiple Related Entities
    Given I am authenticated as an investigator
    When I request multi-entity timeline with:
      | entities        | [{"type": "USER", "id": "user-123"}, {"type": "TRANSACTION", "id": "txn-456"}] |
      | relationship_types | ["direct", "indirect", "temporal"] |
      | visualization   | true                        |
    Then I should receive a 200 OK response
    And the timeline should include all related entities
    And relationships should be clearly indicated
    And the timeline should support visualization
    And interaction patterns should be highlighted

  # =============================================================================
  # SEARCH PERFORMANCE SCENARIOS
  # =============================================================================

  @audit @search @performance @optimization
  Scenario: Optimize Search Performance for Large Datasets
    Given I am authenticated as a compliance officer
    And the system contains millions of audit events
    When I perform a complex search query
    Then the search should complete within acceptable time limits
    And the search should use appropriate indexes
    And the search should support pagination efficiently
    And the search should provide progress indicators

  @audit @search @performance @caching
  Scenario: Utilize Search Result Caching for Repeated Queries
    Given I am authenticated as a compliance officer
    When I perform the same search query multiple times
    Then subsequent searches should be faster
    And cached results should be used when appropriate
    And cache invalidation should work correctly
    And cache hit rates should be monitored

  # =============================================================================
  # SEARCH SECURITY SCENARIOS
  # =============================================================================

  @audit @search @security @access-control
  Scenario: Enforce Access Control in Search Results
    Given I am authenticated as a regular user
    When I search audit events
    Then I should only see events I have permission to view
    And sensitive information should be filtered
    And tenant isolation should be enforced
    And role-based filtering should be applied

  @audit @search @security @data-masking
  Scenario: Apply Data Masking to Sensitive Search Results
    Given I am authenticated as a compliance officer
    When I search events containing sensitive data
    Then PII should be masked appropriately
    And financial data should be protected
    And masking should be role-based
    And original data should remain intact

  # =============================================================================
  # SEARCH VALIDATION SCENARIOS
  # =============================================================================

  @audit @search @validation @negative @invalid-parameters
  Scenario: Handle Invalid Search Parameters
    Given I am authenticated as a compliance officer
    When I search with invalid parameters:
      | time_range      | invalid_date_format         |
      | page            | -1                          |
      | limit           | 10000                       |
    Then I should receive a 400 Bad Request response
    And the error should specify invalid parameters
    And the error should provide correction suggestions
    And no search should be performed

  @audit @search @validation @negative @unauthorized-access
  Scenario: Prevent Unauthorized Search Access
    Given I am authenticated as an unauthorized user
    When I attempt to search audit events
    Then I should receive a 403 Forbidden response
    And the error should mention insufficient permissions
    And no search results should be returned
    And the unauthorized attempt should be logged

  # =============================================================================
  # SEARCH MONITORING SCENARIOS
  # =============================================================================

  @audit @search @monitoring @usage-tracking
  Scenario: Track Search Usage and Performance Metrics
    Given I am authenticated as a system administrator
    When I monitor search operations
    Then search frequency should be tracked
    And search performance should be measured
    And popular search patterns should be identified
    And optimization opportunities should be highlighted

  @audit @search @monitoring @audit-search-activities
  Scenario: Audit Search Activities for Compliance
    Given I am authenticated as a compliance officer
    When search operations are performed
    Then all search activities should be audited
    And search criteria should be logged
    And result access should be tracked
    And search audit trail should be maintained
