@audit
Feature: 03 - Compliance Reporting
  As a compliance officer or regulatory auditor
  I want to generate compliance reports from audit data in the Qeep financial monitoring system
  So that I can meet regulatory requirements and demonstrate compliance

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"
    And the audit service is available
    And I have tenant header "x-tenant-code" set to "financial-corp"
    And audit events exist for compliance reporting

  # =============================================================================
  # REGULATORY COMPLIANCE REPORTS
  # =============================================================================

  @audit @compliance @reporting @regulatory @happy-path @core
  Scenario: Generate BSA/AML Compliance Report
    Given I am authenticated as a compliance officer
    When I generate BSA/AML compliance report with:
      | report_period   | 2024-Q2                     |
      | report_type     | bsa_aml_compliance          |
      | include_sections | ["transaction_monitoring", "sar_filing", "kyc_compliance", "training_records"] |
      | format          | PDF                         |
      | certification   | true                        |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the report should be generated successfully
    And the report should include all required sections
    And the report should be certified for regulatory submission
    And the report should meet BSA/AML standards
    And the response time should be reasonable

  @audit @compliance @reporting @regulatory @ofac-sanctions
  Scenario: Generate OFAC Sanctions Compliance Report
    Given I am authenticated as a compliance officer
    When I generate OFAC sanctions compliance report with:
      | report_period   | last_quarter                |
      | report_type     | ofac_sanctions_compliance   |
      | screening_data  | {"transactions": true, "customers": true, "counterparties": true} |
      | violation_summary | true                      |
      | remediation_actions | true                    |
    Then I should receive a 200 OK response
    And the report should include sanctions screening statistics
    And the report should detail any violations found
    And the report should include remediation actions taken
    And the report should be suitable for OFAC submission

  @audit @compliance @reporting @regulatory @gdpr-compliance
  Scenario: Generate GDPR Data Protection Compliance Report
    Given I am authenticated as a data protection officer
    When I generate GDPR compliance report with:
      | report_period   | last_year                   |
      | report_type     | gdpr_compliance             |
      | data_categories | ["personal_data", "sensitive_data", "financial_data"] |
      | processing_activities | true                  |
      | data_subject_requests | true                  |
      | breach_incidents | true                       |
    Then I should receive a 200 OK response
    And the report should include data processing activities
    And the report should detail data subject requests
    And the report should include breach incident summaries
    And the report should demonstrate GDPR compliance

  # =============================================================================
  # AUTOMATED REPORT GENERATION
  # =============================================================================

  @audit @compliance @reporting @automated @happy-path @core
  Scenario: Schedule Automated Monthly Compliance Reports
    Given I am authenticated as a compliance administrator
    When I schedule automated compliance reports with:
      | report_schedule | monthly                     |
      | report_types    | ["user_activity", "data_access", "security_events"] |
      | delivery_method | email                       |
      | recipients      | ["<EMAIL>", "<EMAIL>"] |
      | format          | PDF                         |
      | auto_certification | true                    |
    Then I should receive a 200 OK response
    And the automated schedule should be created
    And reports should be generated monthly
    And reports should be delivered to specified recipients
    And auto-certification should be applied

  @audit @compliance @reporting @automated @quarterly
  Scenario: Generate Quarterly Regulatory Summary Report
    Given I am authenticated as a compliance officer
    When the quarterly report generation is triggered
    Then a comprehensive quarterly report should be generated
    And the report should include compliance metrics
    And the report should include trend analysis
    And the report should include executive summary
    And the report should be automatically distributed

  @audit @compliance @reporting @automated @real-time
  Scenario: Generate Real-time Compliance Alerts Report
    Given I am authenticated as a compliance officer
    When compliance violations are detected in real-time
    Then immediate compliance alert reports should be generated
    And alerts should be categorized by severity
    And alerts should include remediation recommendations
    And alerts should be delivered to appropriate stakeholders
    And escalation procedures should be triggered

  # =============================================================================
  # CUSTOM COMPLIANCE REPORTS
  # =============================================================================

  @audit @compliance @reporting @custom @happy-path @core
  Scenario: Create Custom Compliance Report Template
    Given I am authenticated as a compliance administrator
    When I create a custom report template with:
      | template_name   | monthly_transaction_review  |
      | data_sources    | ["audit_events", "transaction_data", "user_activities"] |
      | filters         | {"event_category": "FINANCIAL_OPERATION", "severity": ["HIGH", "CRITICAL"]} |
      | sections        | ["executive_summary", "detailed_analysis", "recommendations"] |
      | visualizations  | ["charts", "tables", "heatmaps"] |
    Then I should receive a 200 OK response
    And the custom template should be created
    And the template should be reusable
    And the template should support parameterization
    And the template should be validated

  @audit @compliance @reporting @custom @parameterized
  Scenario: Generate Report Using Custom Template with Parameters
    Given I am authenticated as a compliance officer
    And a custom report template exists
    When I generate report using template with parameters:
      | template_id     | template-123                |
      | parameters      | {"start_date": "2024-06-01", "end_date": "2024-06-30", "department": "compliance"} |
      | output_format   | Excel                       |
      | include_raw_data | true                       |
    Then I should receive a 200 OK response
    And the report should be generated using the template
    And parameters should be applied correctly
    And the report should include raw data appendix
    And the report should be in Excel format

  # =============================================================================
  # AUDIT TRAIL REPORTS
  # =============================================================================

  @audit @compliance @reporting @audit-trail @happy-path @core
  Scenario: Generate Comprehensive Audit Trail Report
    Given I am authenticated as an auditor
    When I generate audit trail report with:
      | scope           | system_wide                 |
      | time_period     | last_quarter                |
      | event_categories | ["AUTHENTICATION", "DATA_MODIFICATION", "SECURITY", "FINANCIAL_OPERATION"] |
      | detail_level    | comprehensive               |
      | include_metadata | true                       |
    Then I should receive a 200 OK response
    And the report should include complete audit trail
    And the report should cover all specified categories
    And the report should include event metadata
    And the report should be suitable for external audit

  @audit @compliance @reporting @audit-trail @user-specific
  Scenario: Generate User-Specific Audit Trail Report
    Given I am authenticated as a compliance officer
    When I generate user audit trail report with:
      | user_id         | user-123                    |
      | time_period     | last_six_months             |
      | include_sessions | true                       |
      | include_permissions | true                    |
      | include_data_access | true                    |
    Then I should receive a 200 OK response
    And the report should include user's complete activity history
    And the report should include session information
    And the report should include permission changes
    And the report should include data access patterns

  # =============================================================================
  # COMPLIANCE METRICS REPORTS
  # =============================================================================

  @audit @compliance @reporting @metrics @happy-path @core
  Scenario: Generate Compliance Performance Metrics Report
    Given I am authenticated as a compliance manager
    When I generate compliance metrics report with:
      | metrics         | ["compliance_score", "violation_rate", "remediation_time", "training_completion"] |
      | time_period     | last_year                   |
      | benchmark_comparison | industry_standards       |
      | trend_analysis  | true                        |
      | improvement_recommendations | true            |
    Then I should receive a 200 OK response
    And the report should include compliance performance metrics
    And the report should include industry benchmarks
    And the report should include trend analysis
    And the report should provide improvement recommendations

  @audit @compliance @reporting @metrics @kpi-dashboard
  Scenario: Generate Compliance KPI Dashboard Report
    Given I am authenticated as an executive
    When I generate compliance KPI dashboard with:
      | kpi_categories  | ["regulatory_compliance", "data_protection", "security_posture"] |
      | visualization_type | executive_dashboard       |
      | update_frequency | real_time                 |
      | alert_thresholds | {"compliance_score": 85, "violation_rate": 5} |
    Then I should receive a 200 OK response
    And the dashboard should display key compliance KPIs
    And the dashboard should be visually appealing
    And the dashboard should update in real-time
    And alert thresholds should be monitored

  # =============================================================================
  # REGULATORY EXAMINATION REPORTS
  # =============================================================================

  @audit @compliance @reporting @examination @preparation
  Scenario: Generate Regulatory Examination Preparation Report
    Given I am authenticated as a compliance officer
    When I generate examination preparation report with:
      | examination_type | bsa_aml_examination         |
      | preparation_scope | comprehensive              |
      | documentation_checklist | true                 |
      | sample_selections | {"transactions": 100, "cases": 50, "alerts": 200} |
      | deficiency_analysis | true                   |
    Then I should receive a 200 OK response
    And the report should include examination readiness assessment
    And the report should include documentation checklist
    And the report should include sample data selections
    And the report should identify potential deficiencies

  @audit @compliance @reporting @examination @response
  Scenario: Generate Examination Response Documentation
    Given I am authenticated as a compliance officer
    When I generate examination response documentation with:
      | examination_id  | exam-2024-001               |
      | response_type   | formal_response             |
      | findings_addressed | true                     |
      | corrective_actions | true                     |
      | supporting_evidence | true                    |
    Then I should receive a 200 OK response
    And the documentation should address examination findings
    And the documentation should include corrective actions
    And the documentation should include supporting evidence
    And the documentation should be examination-ready

  # =============================================================================
  # DATA RETENTION COMPLIANCE REPORTS
  # =============================================================================

  @audit @compliance @reporting @retention @policy-compliance
  Scenario: Generate Data Retention Policy Compliance Report
    Given I am authenticated as a compliance officer
    When I generate data retention compliance report with:
      | retention_policies | ["audit_logs", "transaction_data", "customer_data"] |
      | compliance_status | true                        |
      | retention_schedules | true                      |
      | disposal_activities | true                      |
      | policy_violations | true                       |
    Then I should receive a 200 OK response
    And the report should show retention policy compliance
    And the report should include retention schedules
    And the report should detail disposal activities
    And the report should identify policy violations

  @audit @compliance @reporting @retention @archival-report
  Scenario: Generate Data Archival and Disposal Report
    Given I am authenticated as a records manager
    When I generate archival and disposal report with:
      | time_period     | last_quarter                |
      | data_categories | ["expired_audit_logs", "closed_cases", "inactive_users"] |
      | archival_actions | true                       |
      | disposal_actions | true                       |
      | certification   | true                        |
    Then I should receive a 200 OK response
    And the report should detail archival activities
    And the report should detail disposal activities
    And the report should include certification of destruction
    And the report should maintain chain of custody

  # =============================================================================
  # REPORT VALIDATION AND CERTIFICATION
  # =============================================================================

  @audit @compliance @reporting @validation @integrity-check
  Scenario: Validate Report Data Integrity and Completeness
    Given I am authenticated as a compliance officer
    When I validate a compliance report
    Then the report data should be verified for integrity
    And the report should be checked for completeness
    And data sources should be validated
    And calculation accuracy should be verified
    And validation results should be documented

  @audit @compliance @reporting @certification @digital-signature
  Scenario: Digitally Sign and Certify Compliance Reports
    Given I am authenticated as a compliance officer
    When I certify a compliance report
    Then the report should be digitally signed
    And the signature should be cryptographically secure
    And the certification should include timestamp
    And the certification should include certifier identity
    And the certification should be verifiable

  # =============================================================================
  # REPORT DISTRIBUTION AND ACCESS
  # =============================================================================

  @audit @compliance @reporting @distribution @secure-delivery
  Scenario: Securely Distribute Compliance Reports
    Given I am authenticated as a compliance administrator
    When I distribute compliance reports with:
      | distribution_method | secure_email              |
      | recipients         | ["<EMAIL>", "<EMAIL>"] |
      | encryption         | true                      |
      | access_controls    | time_limited              |
      | delivery_confirmation | true                   |
    Then I should receive a 200 OK response
    And reports should be encrypted for transmission
    And access controls should be applied
    And delivery confirmation should be tracked
    And distribution should be audited

  @audit @compliance @reporting @access @controlled-access
  Scenario: Control Access to Sensitive Compliance Reports
    Given I am authenticated as a compliance administrator
    When I configure report access controls with:
      | report_classification | confidential            |
      | access_roles         | ["compliance_officer", "auditor", "executive"] |
      | time_restrictions    | business_hours_only     |
      | location_restrictions | office_network_only    |
      | audit_access         | true                    |
    Then I should receive a 200 OK response
    And access controls should be enforced
    And unauthorized access should be prevented
    And access attempts should be audited
    And access logs should be maintained

  # =============================================================================
  # REPORT PERFORMANCE AND OPTIMIZATION
  # =============================================================================

  @audit @compliance @reporting @performance @optimization
  Scenario: Optimize Large Compliance Report Generation
    Given I am authenticated as a compliance officer
    And the system contains extensive audit data
    When I generate a comprehensive annual compliance report
    Then the report generation should be optimized
    And progress indicators should be provided
    And the report should be generated within acceptable time
    And system resources should be managed efficiently

  @audit @compliance @reporting @performance @caching
  Scenario: Cache Frequently Generated Compliance Reports
    Given I am authenticated as a compliance officer
    When I generate the same compliance report multiple times
    Then report caching should be utilized
    And cached reports should be used when appropriate
    And cache invalidation should work correctly
    And cache performance should be monitored
