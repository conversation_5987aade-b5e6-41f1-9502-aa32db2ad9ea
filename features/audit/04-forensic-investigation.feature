@audit
Feature: 04 - Forensic Investigation
  As a forensic investigator or security analyst
  I want to conduct forensic analysis using audit data in the Qeep financial monitoring system
  So that I can investigate incidents and support legal proceedings

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"
    And the audit service is available
    And I have tenant header "x-tenant-code" set to "financial-corp"
    And audit events exist for forensic analysis

  # =============================================================================
  # INVESTIGATION INITIATION SCENARIOS
  # =============================================================================

  @audit @forensic @investigation @initiate @happy-path @core
  Scenario: Initiate Forensic Investigation Case
    Given I am authenticated as a forensic investigator
    When I initiate a forensic investigation with:
      | case_id         | case-2024-001               |
      | case_type       | financial_fraud             |
      | incident_date   | 2024-06-15T14:30:00Z        |
      | scope           | {"users": ["user-123"], "transactions": ["txn-456"], "time_range": "2024-06-15"} |
      | priority        | high                        |
      | legal_hold      | true                        |
      | chain_of_custody | required                   |
    Then I should receive a 201 Created response
    And the response should follow StandardApiResponse format
    And the investigation case should be created
    And legal hold should be applied to relevant data
    And chain of custody should be initiated
    And investigation workspace should be prepared
    And the response time should be reasonable

  @audit @forensic @investigation @initiate @security-incident
  Scenario: Initiate Security Incident Investigation
    Given I am authenticated as a security analyst
    When I initiate security incident investigation with:
      | incident_id     | incident-2024-sec-001       |
      | incident_type   | data_breach                 |
      | detection_time  | 2024-06-15T09:15:00Z        |
      | affected_systems | ["user-service", "transaction-service"] |
      | severity        | critical                    |
      | containment_status | in_progress               |
    Then I should receive a 201 Created response
    And the security investigation should be created
    And affected systems should be identified
    And containment measures should be documented
    And incident response team should be notified

  # =============================================================================
  # EVIDENCE COLLECTION SCENARIOS
  # =============================================================================

  @audit @forensic @evidence @collection @happy-path @core
  Scenario: Collect Digital Evidence from Audit Logs
    Given I am authenticated as a forensic investigator
    And an investigation case exists
    When I collect evidence with:
      | case_id         | case-2024-001               |
      | evidence_type   | audit_logs                  |
      | collection_scope | {"event_types": ["USER_LOGIN", "DATA_ACCESS", "TRANSACTION_PROCESSED"], "time_range": "2024-06-15"} |
      | preservation_method | forensic_copy            |
      | hash_verification | true                      |
    Then I should receive a 200 OK response
    And evidence should be collected and preserved
    And forensic copies should be created
    And hash verification should be performed
    And evidence integrity should be maintained
    And collection should be documented

  @audit @forensic @evidence @collection @transaction-data
  Scenario: Collect Transaction-Related Evidence
    Given I am authenticated as a forensic investigator
    And a financial fraud investigation is active
    When I collect transaction evidence with:
      | case_id         | case-2024-001               |
      | transaction_ids | ["txn-456", "txn-789"]      |
      | include_related | true                        |
      | evidence_depth  | comprehensive               |
      | metadata_collection | true                    |
    Then I should receive a 200 OK response
    And transaction evidence should be collected
    And related transactions should be included
    And comprehensive metadata should be gathered
    And transaction flow should be documented

  @audit @forensic @evidence @collection @user-activity
  Scenario: Collect User Activity Evidence
    Given I am authenticated as a forensic investigator
    And an insider threat investigation is active
    When I collect user activity evidence with:
      | case_id         | case-2024-002               |
      | user_id         | user-123                    |
      | activity_scope  | {"sessions": true, "data_access": true, "system_changes": true} |
      | behavioral_analysis | true                     |
      | timeline_reconstruction | true                 |
    Then I should receive a 200 OK response
    And user activity evidence should be collected
    And behavioral patterns should be analyzed
    And activity timeline should be reconstructed
    And anomalous behavior should be identified

  # =============================================================================
  # CHAIN OF CUSTODY SCENARIOS
  # =============================================================================

  @audit @forensic @custody @chain @happy-path @core
  Scenario: Maintain Chain of Custody for Digital Evidence
    Given I am authenticated as a forensic investigator
    And digital evidence has been collected
    When I document chain of custody with:
      | evidence_id     | evidence-001                |
      | custodian       | investigator-123            |
      | custody_action  | transfer                    |
      | recipient       | forensic-lab-456            |
      | transfer_reason | expert_analysis             |
      | integrity_check | true                        |
    Then I should receive a 200 OK response
    And chain of custody should be documented
    And custody transfer should be recorded
    And integrity verification should be performed
    And custody log should be updated
    And legal admissibility should be maintained

  @audit @forensic @custody @verification
  Scenario: Verify Evidence Integrity Throughout Chain of Custody
    Given I am authenticated as a forensic investigator
    And evidence is in custody chain
    When I verify evidence integrity with:
      | evidence_id     | evidence-001                |
      | verification_method | cryptographic_hash       |
      | baseline_hash   | sha256:abc123...            |
      | verification_timestamp | current                |
    Then I should receive a 200 OK response
    And evidence integrity should be verified
    And hash comparison should be performed
    And verification results should be documented
    And any tampering should be detected

  # =============================================================================
  # TIMELINE RECONSTRUCTION SCENARIOS
  # =============================================================================

  @audit @forensic @timeline @reconstruction @happy-path @core
  Scenario: Reconstruct Detailed Event Timeline for Investigation
    Given I am authenticated as a forensic investigator
    And an investigation case is active
    When I reconstruct event timeline with:
      | case_id         | case-2024-001               |
      | focus_entities  | [{"type": "USER", "id": "user-123"}, {"type": "TRANSACTION", "id": "txn-456"}] |
      | time_window     | {"start": "2024-06-15T08:00:00Z", "end": "2024-06-15T18:00:00Z"} |
      | correlation_depth | 3                         |
      | include_context | true                        |
    Then I should receive a 200 OK response
    And detailed timeline should be reconstructed
    And events should be chronologically ordered
    And correlations should be identified
    And contextual information should be included
    And timeline should support investigation analysis

  @audit @forensic @timeline @multi-source
  Scenario: Reconstruct Timeline from Multiple Data Sources
    Given I am authenticated as a forensic investigator
    When I reconstruct multi-source timeline with:
      | case_id         | case-2024-001               |
      | data_sources    | ["audit_logs", "transaction_data", "system_logs", "network_logs"] |
      | synchronization | time_based                  |
      | correlation_rules | ["user_session", "transaction_flow", "system_interaction"] |
      | visualization   | true                        |
    Then I should receive a 200 OK response
    And multi-source timeline should be created
    And data sources should be synchronized
    And correlations should be applied
    And timeline visualization should be generated

  # =============================================================================
  # PATTERN ANALYSIS SCENARIOS
  # =============================================================================

  @audit @forensic @analysis @pattern @happy-path @core
  Scenario: Analyze Suspicious Activity Patterns
    Given I am authenticated as a forensic investigator
    When I analyze suspicious patterns with:
      | case_id         | case-2024-001               |
      | pattern_types   | ["unusual_login_times", "abnormal_transaction_amounts", "suspicious_data_access"] |
      | analysis_period | last_30_days                |
      | baseline_comparison | true                     |
      | anomaly_detection | machine_learning          |
    Then I should receive a 200 OK response
    And suspicious patterns should be identified
    And baseline comparisons should be performed
    And anomalies should be detected
    And pattern analysis should be documented

  @audit @forensic @analysis @behavioral
  Scenario: Conduct Behavioral Analysis for Insider Threat Detection
    Given I am authenticated as a forensic investigator
    When I conduct behavioral analysis with:
      | case_id         | case-2024-002               |
      | subject_user    | user-123                    |
      | behavioral_indicators | ["access_patterns", "working_hours", "data_volume", "system_usage"] |
      | risk_scoring    | true                        |
      | peer_comparison | true                        |
    Then I should receive a 200 OK response
    And behavioral analysis should be performed
    And risk scores should be calculated
    And peer comparisons should be included
    And insider threat indicators should be identified

  # =============================================================================
  # CORRELATION ANALYSIS SCENARIOS
  # =============================================================================

  @audit @forensic @analysis @correlation @happy-path @core
  Scenario: Perform Cross-Entity Correlation Analysis
    Given I am authenticated as a forensic investigator
    When I perform correlation analysis with:
      | case_id         | case-2024-001               |
      | entities        | [{"type": "USER", "ids": ["user-123", "user-456"]}, {"type": "TRANSACTION", "ids": ["txn-789"]}] |
      | correlation_types | ["temporal", "causal", "associative"] |
      | analysis_depth  | comprehensive               |
      | visualization   | network_graph               |
    Then I should receive a 200 OK response
    And correlation analysis should be performed
    And relationships should be identified
    And correlation strength should be measured
    And network visualization should be generated

  @audit @forensic @analysis @link-analysis
  Scenario: Conduct Link Analysis for Complex Investigations
    Given I am authenticated as a forensic investigator
    When I conduct link analysis with:
      | case_id         | case-2024-001               |
      | starting_entities | [{"type": "USER", "id": "user-123"}] |
      | link_depth      | 4                           |
      | relationship_types | ["direct", "indirect", "temporal", "transactional"] |
      | strength_threshold | 0.7                       |
    Then I should receive a 200 OK response
    And link analysis should be performed
    And relationship networks should be mapped
    And link strengths should be calculated
    And investigation leads should be identified

  # =============================================================================
  # EVIDENCE ANALYSIS SCENARIOS
  # =============================================================================

  @audit @forensic @analysis @evidence @digital-forensics
  Scenario: Perform Digital Forensics Analysis on Collected Evidence
    Given I am authenticated as a forensic investigator
    And digital evidence has been collected
    When I perform digital forensics analysis with:
      | evidence_id     | evidence-001                |
      | analysis_type   | comprehensive               |
      | tools           | ["log_analysis", "data_recovery", "metadata_extraction"] |
      | expert_review   | true                        |
      | documentation   | detailed                    |
    Then I should receive a 200 OK response
    And digital forensics analysis should be performed
    And analysis tools should be applied
    And expert review should be conducted
    And detailed documentation should be generated

  @audit @forensic @analysis @evidence @data-recovery
  Scenario: Recover Deleted or Modified Audit Data
    Given I am authenticated as a forensic investigator
    When I attempt data recovery with:
      | case_id         | case-2024-001               |
      | recovery_scope  | {"deleted_events": true, "modified_events": true} |
      | recovery_methods | ["backup_analysis", "log_reconstruction", "database_forensics"] |
      | integrity_verification | true                 |
    Then I should receive a 200 OK response
    And data recovery should be attempted
    And recovery methods should be applied
    And recovered data should be verified
    And recovery results should be documented

  # =============================================================================
  # INVESTIGATION REPORTING SCENARIOS
  # =============================================================================

  @audit @forensic @reporting @investigation @happy-path @core
  Scenario: Generate Comprehensive Investigation Report
    Given I am authenticated as a forensic investigator
    And an investigation has been completed
    When I generate investigation report with:
      | case_id         | case-2024-001               |
      | report_type     | comprehensive               |
      | sections        | ["executive_summary", "methodology", "findings", "evidence", "conclusions", "recommendations"] |
      | evidence_appendix | true                      |
      | legal_format    | true                        |
    Then I should receive a 200 OK response
    And comprehensive report should be generated
    And all sections should be included
    And evidence should be properly referenced
    And report should be legally formatted

  @audit @forensic @reporting @expert-testimony
  Scenario: Prepare Expert Testimony Documentation
    Given I am authenticated as a forensic investigator
    When I prepare expert testimony with:
      | case_id         | case-2024-001               |
      | testimony_type  | expert_witness              |
      | technical_details | simplified                |
      | visual_aids     | true                        |
      | supporting_evidence | referenced               |
    Then I should receive a 200 OK response
    And expert testimony should be prepared
    And technical details should be simplified
    And visual aids should be included
    And evidence should be properly referenced

  # =============================================================================
  # LEGAL SUPPORT SCENARIOS
  # =============================================================================

  @audit @forensic @legal @discovery @electronic-discovery
  Scenario: Support Electronic Discovery (eDiscovery) Process
    Given I am authenticated as a forensic investigator
    When I support eDiscovery with:
      | case_id         | case-2024-001               |
      | discovery_scope | {"custodians": ["user-123"], "date_range": "2024-01-01 to 2024-06-30"} |
      | search_terms    | ["suspicious", "fraud", "unauthorized"] |
      | production_format | native_with_metadata       |
      | privilege_review | required                   |
    Then I should receive a 200 OK response
    And eDiscovery should be supported
    And search terms should be applied
    And production format should be prepared
    And privilege review should be facilitated

  @audit @forensic @legal @preservation
  Scenario: Implement Legal Hold for Evidence Preservation
    Given I am authenticated as a legal counsel
    When I implement legal hold with:
      | case_id         | case-2024-001               |
      | hold_scope      | {"data_types": ["audit_logs", "transaction_data"], "custodians": ["user-123", "user-456"]} |
      | hold_duration   | indefinite                  |
      | notification    | automatic                   |
      | compliance_monitoring | true                   |
    Then I should receive a 200 OK response
    And legal hold should be implemented
    And data preservation should be ensured
    And custodians should be notified
    And compliance should be monitored

  # =============================================================================
  # INVESTIGATION COLLABORATION SCENARIOS
  # =============================================================================

  @audit @forensic @collaboration @team
  Scenario: Collaborate with Investigation Team Members
    Given I am authenticated as a forensic investigator
    When I collaborate with team members on:
      | case_id         | case-2024-001               |
      | team_members    | ["investigator-456", "analyst-789", "legal-counsel-123"] |
      | collaboration_tools | ["shared_workspace", "secure_messaging", "evidence_sharing"] |
      | access_controls | role_based                  |
    Then I should receive a 200 OK response
    And collaboration should be enabled
    And shared workspace should be created
    And secure communication should be established
    And role-based access should be enforced

  @audit @forensic @collaboration @external
  Scenario: Collaborate with External Law Enforcement
    Given I am authenticated as a compliance officer
    When I collaborate with law enforcement on:
      | case_id         | case-2024-001               |
      | agency          | fbi_financial_crimes        |
      | collaboration_type | information_sharing       |
      | data_sharing_agreement | executed               |
      | security_clearance | verified                 |
    Then I should receive a 200 OK response
    And external collaboration should be established
    And information sharing should be secured
    And data sharing agreements should be enforced
    And security clearances should be verified

  # =============================================================================
  # INVESTIGATION QUALITY ASSURANCE
  # =============================================================================

  @audit @forensic @quality @peer-review
  Scenario: Conduct Peer Review of Investigation Findings
    Given I am authenticated as a senior investigator
    When I conduct peer review of:
      | case_id         | case-2024-001               |
      | review_scope    | comprehensive               |
      | review_criteria | ["methodology", "evidence_handling", "conclusions", "documentation"] |
      | reviewer_qualifications | certified_forensic_examiner |
    Then I should receive a 200 OK response
    And peer review should be conducted
    And review criteria should be applied
    And reviewer qualifications should be verified
    And review results should be documented

  @audit @forensic @quality @validation
  Scenario: Validate Investigation Methodology and Results
    Given I am authenticated as a quality assurance officer
    When I validate investigation with:
      | case_id         | case-2024-001               |
      | validation_scope | ["evidence_integrity", "analysis_accuracy", "procedural_compliance"] |
      | standards_compliance | ["iso_27037", "nist_guidelines"] |
      | independent_verification | true                |
    Then I should receive a 200 OK response
    And investigation should be validated
    And standards compliance should be verified
    And independent verification should be performed
    And validation results should be documented
