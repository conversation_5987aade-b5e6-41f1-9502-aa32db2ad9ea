@user
Feature: 03 - Team and Organization Management
  As a system administrator or team manager
  I want to manage teams and organizational structure in the Qeep financial monitoring system
  So that I can organize users effectively and maintain proper departmental access control

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"
    And the user service is available
    And I have tenant header "x-tenant-code" set to "financial-corp"

  # =============================================================================
  # TEAM CREATION SCENARIOS
  # =============================================================================

  @user @team @create @happy-path @core
  Scenario: Successful Creation of Financial Crimes Investigation Team
    Given I am authenticated as a system administrator
    When I create a new team with:
      | team_name    | Financial Crimes Investigation |
      | description  | Investigates suspicious financial activities |
      | department   | compliance                     |
      | team_type    | investigation                  |
      | manager_id   | user-manager-123               |
    Then I should receive a 201 Created response
    And the response should follow StandardApiResponse format
    And the team should be created with the specified details
    And the manager should be assigned to the team
    And the team should be associated with the compliance department
    And a team creation event should be published
    And the response time should be reasonable

  @user @team @create @happy-path @transaction-monitoring
  Scenario: Successful Creation of Transaction Monitoring Team
    Given I am authenticated as a system administrator
    When I create a new team with:
      | team_name    | Transaction Monitoring         |
      | description  | Real-time transaction analysis |
      | department   | operations                     |
      | team_type    | monitoring                     |
      | manager_id   | user-manager-456               |
    Then I should receive a 201 Created response
    And the team should be created for transaction monitoring
    And the team should have monitoring-specific permissions
    And the team should be able to access real-time transaction feeds

  @user @team @create @validation @negative @duplicate-name
  Scenario: Team Creation with Duplicate Name in Same Department
    Given I am authenticated as a system administrator
    And a team named "Fraud Detection" already exists in department "compliance"
    When I attempt to create a team with:
      | team_name    | Fraud Detection |
      | department   | compliance      |
    Then I should receive a 409 Conflict response
    And the error should mention team name already exists
    And no duplicate team should be created

  @user @team @create @validation @negative @invalid-manager
  Scenario: Team Creation with Non-existent Manager
    Given I am authenticated as a system administrator
    When I attempt to create a team with:
      | team_name    | New Investigation Team |
      | manager_id   | non-existent-manager   |
    Then I should receive a 404 Not Found response
    And the error should mention manager not found
    And no team should be created

  @user @team @create @authorization @negative @insufficient-permissions
  Scenario: Team Creation without Admin Permissions
    Given I am authenticated as a financial analyst
    When I attempt to create a new team
    Then I should receive a 403 Forbidden response
    And the error should mention insufficient permissions
    And no team should be created

  # =============================================================================
  # TEAM MEMBER MANAGEMENT SCENARIOS
  # =============================================================================

  @user @team @member @add @happy-path @core
  Scenario: Successful Addition of User to Investigation Team
    Given I am authenticated as a system administrator
    And a team exists with ID "team-123"
    And a user exists with ID "user-456"
    When I add user to team with:
      | team_id   | team-123    |
      | user_id   | user-456    |
      | role      | investigator |
      | start_date | 2024-01-01  |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the user should be added to the team
    And the user should have the investigator role within the team
    And the user should inherit team-specific permissions
    And a team membership event should be published

  @user @team @member @add @happy-path @multiple-users
  Scenario: Add Multiple Users to Compliance Team
    Given I am authenticated as a system administrator
    And a team exists with ID "compliance-team-123"
    When I add multiple users to team with:
      | team_id  | compliance-team-123                    |
      | user_ids | user-111,user-222,user-333             |
      | role     | compliance_analyst                     |
    Then I should receive a 200 OK response
    And all specified users should be added to the team
    And each user should have the compliance_analyst role
    And team capacity should be validated

  @user @team @member @remove @happy-path @core
  Scenario: Successful Removal of User from Team
    Given I am authenticated as a system administrator
    And a user "user-456" is a member of team "team-123"
    When I remove user from team with:
      | team_id   | team-123 |
      | user_id   | user-456 |
      | end_date  | 2024-12-31 |
      | reason    | Team restructuring |
    Then I should receive a 200 OK response
    And the user should be removed from the team
    And the user should lose team-specific permissions
    And the removal should be logged with reason
    And a team membership removal event should be published

  @user @team @member @transfer @happy-path @core
  Scenario: Transfer User Between Teams
    Given I am authenticated as a system administrator
    And a user "user-456" is a member of team "old-team-123"
    And a team exists with ID "new-team-456"
    When I transfer user between teams with:
      | user_id      | user-456      |
      | from_team_id | old-team-123  |
      | to_team_id   | new-team-456  |
      | transfer_date | 2024-06-01   |
      | reason       | Promotion     |
    Then I should receive a 200 OK response
    And the user should be removed from the old team
    And the user should be added to the new team
    And the transfer should maintain user history
    And appropriate permissions should be updated

  # =============================================================================
  # DEPARTMENT MANAGEMENT SCENARIOS
  # =============================================================================

  @user @department @create @happy-path @core
  Scenario: Successful Creation of Compliance Department
    Given I am authenticated as a system administrator
    When I create a new department with:
      | department_name | Compliance and Risk Management |
      | description     | Ensures regulatory compliance  |
      | department_code | COMPLIANCE                     |
      | head_user_id    | user-head-123                  |
    Then I should receive a 201 Created response
    And the response should follow StandardApiResponse format
    And the department should be created with specified details
    And the department head should be assigned
    And the department should have appropriate default permissions
    And a department creation event should be published

  @user @department @update @happy-path @core
  Scenario: Update Department Information
    Given I am authenticated as a system administrator
    And a department exists with ID "dept-123"
    When I update the department with:
      | department_id   | dept-123                        |
      | description     | Updated compliance department   |
      | head_user_id    | user-new-head-456              |
    Then I should receive a 200 OK response
    And the department information should be updated
    And the new department head should be assigned
    And the old head should be notified of the change

  @user @department @list @happy-path @core
  Scenario: List All Departments with Team Structure
    Given I am authenticated as a system administrator
    And multiple departments exist with teams
    When I request the department list
    Then I should receive a 200 OK response
    And the response should include all departments
    And each department should include team count
    And each department should include member count
    And the organizational hierarchy should be clear

  # =============================================================================
  # ORGANIZATIONAL HIERARCHY SCENARIOS
  # =============================================================================

  @user @hierarchy @reporting @happy-path @core
  Scenario: Establish Reporting Relationships
    Given I am authenticated as a system administrator
    And users exist with IDs "manager-123" and "analyst-456"
    When I establish reporting relationship with:
      | subordinate_id | analyst-456  |
      | supervisor_id  | manager-123  |
      | relationship_type | direct_report |
      | effective_date | 2024-01-01   |
    Then I should receive a 200 OK response
    And the reporting relationship should be established
    And the supervisor should be able to view subordinate's work
    And the hierarchy should be reflected in permissions

  @user @hierarchy @delegation @happy-path @core
  Scenario: Delegate Authority During Manager Absence
    Given I am authenticated as a team manager
    And I have a team member "user-delegate-123"
    When I delegate authority with:
      | delegate_user_id | user-delegate-123 |
      | permissions      | team_management,case_approval |
      | start_date       | 2024-06-01        |
      | end_date         | 2024-06-15        |
      | reason           | Vacation absence  |
    Then I should receive a 200 OK response
    And the delegate should receive temporary permissions
    And the delegation should automatically expire
    And all delegated actions should be logged

  # =============================================================================
  # TEAM PERFORMANCE AND ANALYTICS
  # =============================================================================

  @user @team @analytics @performance @happy-path
  Scenario: Generate Team Performance Analytics
    Given I am authenticated as a team manager
    And I manage team "investigation-team-123"
    When I request team performance analytics with:
      | team_id    | investigation-team-123 |
      | start_date | 2024-01-01             |
      | end_date   | 2024-03-31             |
    Then I should receive a 200 OK response
    And the response should include case completion metrics
    And the response should include team productivity statistics
    And the response should include individual member performance
    And the response should include workload distribution

  @user @team @workload @distribution @happy-path
  Scenario: Analyze Team Workload Distribution
    Given I am authenticated as a team manager
    And my team has multiple active cases
    When I request workload distribution analysis
    Then I should receive current workload for each team member
    And I should see case assignment recommendations
    And I should see capacity utilization metrics
    And I should be able to identify overloaded team members

  # =============================================================================
  # CROSS-FUNCTIONAL TEAM SCENARIOS
  # =============================================================================

  @user @team @cross-functional @create @happy-path
  Scenario: Create Cross-Functional Investigation Task Force
    Given I am authenticated as a system administrator
    And users exist from different departments
    When I create a cross-functional team with:
      | team_name     | Money Laundering Task Force    |
      | team_type     | cross_functional               |
      | duration      | temporary                      |
      | end_date      | 2024-12-31                     |
      | members       | user-123,user-456,user-789     |
      | departments   | compliance,operations,legal    |
    Then I should receive a 201 Created response
    And the cross-functional team should be created
    And members should retain their primary team assignments
    And the team should have combined permissions from all departments
    And the team should automatically dissolve on end date

  @user @team @collaboration @permissions @happy-path
  Scenario: Manage Cross-Team Collaboration Permissions
    Given I am authenticated as a system administrator
    And teams need to collaborate on a complex case
    When I grant cross-team collaboration permissions with:
      | primary_team_id   | fraud-team-123      |
      | collaborating_teams | aml-team-456,kyc-team-789 |
      | shared_permissions | view_cases,add_notes |
      | duration          | 30_days             |
    Then I should receive a 200 OK response
    And teams should be able to collaborate on shared cases
    And permissions should be time-limited
    And all cross-team activities should be logged

  # =============================================================================
  # SECURITY AND COMPLIANCE SCENARIOS
  # =============================================================================

  @user @team @security @access-control
  Scenario: Enforce Team-Based Access Control
    Given I am authenticated as a team member
    And I belong to the "fraud-detection" team
    When I attempt to access resources
    Then I should only access my team's cases and data
    And I should not access other teams' sensitive information
    And cross-team access should require explicit permission
    And all access attempts should be logged

  @user @team @compliance @audit-trail
  Scenario: Maintain Complete Team Management Audit Trail
    Given I am authenticated as a system administrator
    When I perform team management operations
    Then all team changes should be logged with timestamps
    And the audit log should include user who made changes
    And the audit log should include affected team members
    And the audit log should include business justification
    And the audit trail should support regulatory compliance

  @user @team @security @tenant-isolation
  Scenario: Team Management Tenant Isolation
    Given I am authenticated as admin for tenant "BANK_A"
    And teams exist across multiple tenants
    When I manage teams and organizational structure
    Then I should only affect teams within my tenant
    And cross-tenant team assignments should be prevented
    And organizational data should be completely isolated
    And no data leakage should occur between financial institutions
