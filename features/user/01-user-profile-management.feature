@user
Feature: 01 - User Profile Management
  As a system administrator or user
  I want to manage user profiles in the Qeep financial monitoring system
  So that I can maintain accurate user information and ensure proper access control

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"
    And the user service is available

  # =============================================================================
  # CREATE USER SCENARIOS
  # =============================================================================

  @user @create @happy-path @core
  Scenario: Successful User Creation for Financial Analyst
    Given I am authenticated as a system administrator
    And I have tenant header "x-tenant-code" set to "financial-corp"
    When I create a new user with:
      | email      | <EMAIL> |
      | first_name | John                           |
      | last_name  | Smith                          |
      | role       | analyst                        |
      | status     | PENDING                        |
    Then I should receive a 201 Created response
    And the response should follow StandardApiResponse format
    And the user should be created with status PENDING
    And the user should be assigned to tenant financial-corp
    And the user should have analyst role
    And the user should not be email verified initially
    And a user creation event should be published
    And the response time should be reasonable

  @user @create @happy-path @compliance-officer
  Scenario: Successful User Creation for Compliance Officer
    Given I am authenticated as a system administrator
    And I have tenant header "x-tenant-code" set to "financial-corp"
    When I create a new user with:
      | email      | <EMAIL> |
      | first_name | Jane                               |
      | last_name  | Doe                                |
      | role       | compliance_officer                 |
      | status     | PENDING                            |
    Then I should receive a 201 Created response
    And the user should be created with compliance officer role
    And the user should have appropriate compliance permissions
    And the user should be assigned to the compliance team

  @user @create @validation @negative @duplicate-email
  Scenario: User Creation with Duplicate Email in Same Tenant
    Given I am authenticated as a system administrator
    And I have tenant header "x-tenant-code" set to "financial-corp"
    And a user already exists with email "<EMAIL>" in the current tenant
    When I attempt to create a user with the same email
    Then I should receive a 409 Conflict response
    And the error should mention user already exists
    And the error code should be "USER_ALREADY_EXISTS"
    And no duplicate user should be created

  @user @create @validation @negative @invalid-email
  Scenario Outline: User Creation with Invalid Email Formats
    Given I am authenticated as a system administrator
    When I attempt to create a user with invalid email "<email>"
    Then I should receive a 400 Bad Request response
    And the error should mention email validation

    Examples:
      | email                    |
      | invalid-email-format     |
      | user@                    |
      | @domain.com              |
      | <EMAIL>    |
      | .<EMAIL>         |

  @user @create @validation @negative @missing-required-fields
  Scenario: User Creation with Missing Required Fields
    Given I am authenticated as a system administrator
    When I attempt to create a user without required fields:
      | email      | <EMAIL> |
      | first_name |                              |
    Then I should receive a 400 Bad Request response
    And the error should mention required fields
    And the error should specify which fields are missing

  @user @create @authorization @negative @insufficient-permissions
  Scenario: User Creation without Admin Permissions
    Given I am authenticated as a regular analyst
    When I attempt to create a new user
    Then I should receive a 403 Forbidden response
    And the error should mention insufficient permissions
    And no user should be created

  # =============================================================================
  # GET USER SCENARIOS
  # =============================================================================

  @user @get @happy-path @core
  Scenario: Successful User Retrieval by ID
    Given I am authenticated as a system administrator
    And I have tenant header "x-tenant-code" set to "financial-corp"
    And a user exists with ID "user-123" in the current tenant
    When I request user information for ID "user-123"
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the response should contain complete user information
    And the response should include user ID
    And the response should include email address
    And the response should include first and last name
    And the response should include tenant information
    And the response should include user status
    And the response should include role information
    And the response should include creation and update timestamps
    And the password hash should not be included
    And the response time should be reasonable

  @user @get @happy-path @by-email
  Scenario: Successful User Retrieval by Email and Tenant
    Given I am authenticated as a system administrator
    And I have tenant header "x-tenant-code" set to "financial-corp"
    And a user exists with email "<EMAIL>" in the current tenant
    When I request user information by email and tenant
    Then I should receive a 200 OK response
    And the response should contain the correct user information
    And the user should belong to the specified tenant

  @user @get @authorization @self-access
  Scenario: User Retrieving Own Profile Information
    Given I am authenticated as user "<EMAIL>"
    When I request my own profile information
    Then I should receive a 200 OK response
    And the response should contain my user information
    And sensitive information should be appropriately filtered
    And I should see my roles and permissions

  @user @get @validation @negative @non-existent-user
  Scenario: Get Non-existent User
    Given I am authenticated as a system administrator
    When I request user information for non-existent ID "non-existent-123"
    Then I should receive a 404 Not Found response
    And the error should mention user not found

  @user @get @authorization @negative @cross-tenant-access
  Scenario: Attempt to Access User from Different Tenant
    Given I am authenticated as admin for tenant "TENANT_A"
    And a user exists in tenant "TENANT_B"
    When I attempt to access the user from tenant B
    Then I should receive a 403 Forbidden response
    And the error should mention insufficient permissions
    And tenant isolation should be maintained

  # =============================================================================
  # UPDATE USER SCENARIOS
  # =============================================================================

  @user @update @happy-path @core
  Scenario: Successful User Profile Update
    Given I am authenticated as a system administrator
    And a user exists with ID "user-123"
    When I update the user with:
      | first_name | UpdatedJohn    |
      | last_name  | UpdatedSmith   |
      | status     | ACTIVE         |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the user information should be updated
    And the updated timestamp should be current
    And a user update event should be published
    And the response time should be reasonable

  @user @update @happy-path @self-update
  Scenario: User Updating Own Profile Information
    Given I am authenticated as user "<EMAIL>"
    When I update my own profile with:
      | firstName | MyUpdatedName |
      | lastName  | MyUpdatedLast |
    Then I should receive a 200 OK response
    And my profile should be updated
    And I should not be able to change my role or status
    And I should not be able to change my tenant assignment

  @user @update @validation @negative @invalid-status
  Scenario: User Update with Invalid Status
    Given I am authenticated as a system administrator
    And a user exists with ID "user-123"
    When I attempt to update the user with invalid status "INVALID_STATUS"
    Then I should receive a 400 Bad Request response
    And the error should mention invalid status value
    And the user should remain unchanged

  @user @update @authorization @negative @insufficient-permissions
  Scenario: User Update without Admin Permissions
    Given I am authenticated as a regular analyst
    And another user exists with ID "other-user-123"
    When I attempt to update the other user's information
    Then I should receive a 403 Forbidden response
    And the error should mention insufficient permissions
    And the user should remain unchanged

  @user @update @business-rules @status-transition
  Scenario: User Status Transition Validation
    Given I am authenticated as a system administrator
    And a user exists with status "SUSPENDED"
    When I attempt to update the user status to "ACTIVE"
    Then I should receive a 200 OK response
    And the status transition should be logged
    And appropriate notifications should be sent
    And the user should be able to access the system again

  # =============================================================================
  # DELETE USER SCENARIOS
  # =============================================================================

  @user @delete @soft-delete @happy-path @core
  Scenario: Successful Soft Delete of User
    Given I am authenticated as a system administrator
    And a user exists with ID "user-123"
    When I soft delete the user
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the user should be marked as deleted
    And the user should have a deletion timestamp
    And the user should not appear in active user lists
    And a user deletion event should be published
    And the user data should be retained for audit purposes

  @user @delete @hard-delete @admin-only
  Scenario: Hard Delete of User (Admin Only)
    Given I am authenticated as a super administrator
    And a user exists with ID "user-123" that was soft deleted
    When I perform a hard delete of the user
    Then I should receive a 200 OK response
    And the user should be permanently removed from the database
    And all associated data should be cleaned up
    And a hard deletion audit log should be created
    And the action should be irreversible

  @user @delete @authorization @negative @insufficient-permissions
  Scenario: User Deletion without Admin Permissions
    Given I am authenticated as a regular analyst
    And a user exists with ID "other-user-123"
    When I attempt to delete the other user
    Then I should receive a 403 Forbidden response
    And the error should mention insufficient permissions
    And the user should remain active

  @user @delete @business-rules @self-deletion-prevention
  Scenario: Prevent User from Deleting Own Account
    Given I am authenticated as user "<EMAIL>"
    When I attempt to delete my own account
    Then I should receive a 400 Bad Request response
    And the error should mention cannot delete own account
    And my account should remain active

  # =============================================================================
  # USER RESTORATION SCENARIOS
  # =============================================================================

  @user @restore @happy-path @core
  Scenario: Successful User Restoration from Soft Delete
    Given I am authenticated as a system administrator
    And a user exists with ID "user-123" that was soft deleted
    When I restore the deleted user
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the user should be marked as active
    And the deletion timestamp should be cleared
    And the user should appear in active user lists again
    And a user restoration event should be published

  @user @restore @validation @negative @non-deleted-user
  Scenario: Attempt to Restore Non-deleted User
    Given I am authenticated as a system administrator
    And a user exists with ID "user-123" that is active
    When I attempt to restore the user
    Then I should receive a 400 Bad Request response
    And the error should mention user is not deleted
    And the user status should remain unchanged

  # =============================================================================
  # SECURITY AND COMPLIANCE SCENARIOS
  # =============================================================================

  @user @security @data-protection
  Scenario: User Data Protection and Privacy Compliance
    Given I am authenticated as a system administrator
    When I retrieve user information
    Then sensitive data should be properly protected
    And password hashes should never be exposed
    And personal data should be handled according to privacy laws
    And audit logs should track all data access

  @user @security @tenant-isolation
  Scenario: Multi-tenant Data Isolation Verification
    Given I am authenticated as admin for tenant "TENANT_A"
    And users exist in multiple tenants
    When I list users or search for users
    Then I should only see users from my tenant
    And cross-tenant data should be completely isolated
    And no data leakage should occur between tenants
