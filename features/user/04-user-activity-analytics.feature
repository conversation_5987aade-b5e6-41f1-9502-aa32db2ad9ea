@user
Feature: 04 - User Activity and Analytics
  As a system administrator or compliance officer
  I want to track and analyze user activity in the Qeep financial monitoring system
  So that I can ensure compliance, detect anomalies, and optimize system usage

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"
    And the user service is available
    And I have tenant header "x-tenant-code" set to "financial-corp"

  # =============================================================================
  # USER ACTIVITY TRACKING SCENARIOS
  # =============================================================================

  @user @activity @tracking @happy-path @core
  Scenario: Track User Login Activity
    Given I am authenticated as a system administrator
    And a user exists with ID "user-123"
    When I request user activity for the past 30 days with:
      | user_id     | user-123   |
      | start_date  | 2024-01-01 |
      | end_date    | 2024-01-31 |
      | activity_type | login     |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the response should include login timestamps
    And the response should include IP addresses
    And the response should include device information
    And the response should include session durations
    And the response should include geographic locations
    And the response time should be reasonable

  @user @activity @tracking @system-access
  Scenario: Track User System Access Patterns
    Given I am authenticated as a compliance officer
    And a user exists with ID "analyst-456"
    When I request system access activity with:
      | user_id        | analyst-456           |
      | activity_types | page_view,api_call,data_access |
      | start_date     | 2024-01-01            |
      | end_date       | 2024-01-31            |
    Then I should receive a 200 OK response
    And the response should include page access patterns
    And the response should include API endpoint usage
    And the response should include data access logs
    And the response should include time-based usage patterns

  @user @activity @tracking @case-management
  Scenario: Track Case Management Activities
    Given I am authenticated as a team manager
    And an investigator exists with ID "investigator-789"
    When I request case management activity with:
      | user_id        | investigator-789      |
      | activity_types | case_created,case_updated,case_closed |
      | start_date     | 2024-01-01            |
      | end_date       | 2024-01-31            |
    Then I should receive a 200 OK response
    And the response should include case creation activities
    And the response should include case update frequencies
    And the response should include case closure rates
    And the response should include case complexity metrics

  @user @activity @tracking @data-access
  Scenario: Track Sensitive Data Access
    Given I am authenticated as a compliance officer
    And a user exists with ID "analyst-123"
    When I request sensitive data access logs with:
      | user_id     | analyst-123           |
      | data_types  | pii,financial_records,investigation_notes |
      | start_date  | 2024-01-01            |
      | end_date    | 2024-01-31            |
    Then I should receive a 200 OK response
    And the response should include PII access events
    And the response should include financial record queries
    And the response should include investigation note access
    And each access should include business justification
    And all access should be timestamped and auditable

  # =============================================================================
  # LOGIN PATTERN ANALYSIS SCENARIOS
  # =============================================================================

  @user @analytics @login-patterns @happy-path @core
  Scenario: Analyze User Login Patterns for Anomaly Detection
    Given I am authenticated as a security administrator
    And a user has established login patterns over 90 days
    When I request login pattern analysis with:
      | user_id           | user-123    |
      | analysis_period   | 90_days     |
      | pattern_types     | time,location,device |
    Then I should receive a 200 OK response
    And the response should include typical login hours
    And the response should include common login locations
    And the response should include usual devices
    And the response should highlight anomalous logins
    And the response should include risk scores for unusual activity

  @user @analytics @login-patterns @anomaly-detection
  Scenario: Detect Suspicious Login Activity
    Given I am authenticated as a security administrator
    And a user has unusual login activity
    When I request anomaly detection analysis with:
      | user_id        | user-456              |
      | anomaly_types  | unusual_time,new_location,new_device |
      | sensitivity    | high                  |
    Then I should receive a 200 OK response
    And the response should flag unusual login times
    And the response should flag new geographic locations
    And the response should flag unrecognized devices
    And the response should include risk assessment
    And security alerts should be generated for high-risk activities

  @user @analytics @login-patterns @geographic-analysis
  Scenario: Analyze Geographic Login Distribution
    Given I am authenticated as a compliance officer
    And users have logged in from various locations
    When I request geographic login analysis with:
      | time_period    | last_quarter          |
      | user_groups    | all_analysts,investigators |
      | detail_level   | country,city,ip_range |
    Then I should receive a 200 OK response
    And the response should include login distribution by country
    And the response should include city-level analysis
    And the response should include IP range patterns
    And the response should flag logins from restricted regions

  # =============================================================================
  # USAGE ANALYTICS SCENARIOS
  # =============================================================================

  @user @analytics @usage @feature-adoption
  Scenario: Analyze Feature Adoption and Usage Patterns
    Given I am authenticated as a system administrator
    When I request feature usage analytics with:
      | time_period    | last_month            |
      | user_segments  | by_role,by_department |
      | features       | case_management,reporting,alerts |
    Then I should receive a 200 OK response
    And the response should include feature adoption rates
    And the response should include usage frequency by role
    And the response should include department-wise usage patterns
    And the response should identify underutilized features
    And the response should suggest training opportunities

  @user @analytics @usage @productivity-metrics
  Scenario: Generate User Productivity Metrics
    Given I am authenticated as a team manager
    And I manage a team of financial analysts
    When I request productivity analytics with:
      | team_id        | analyst-team-123      |
      | metrics        | cases_per_day,alerts_reviewed,reports_generated |
      | time_period    | last_quarter          |
    Then I should receive a 200 OK response
    And the response should include individual productivity scores
    And the response should include team average metrics
    And the response should include trend analysis
    And the response should identify top performers
    And the response should highlight improvement opportunities

  @user @analytics @usage @system-performance
  Scenario: Analyze System Performance Impact by User Activity
    Given I am authenticated as a system administrator
    When I request system performance analytics with:
      | metrics        | response_time,error_rate,resource_usage |
      | user_segments  | heavy_users,normal_users,light_users |
      | time_period    | last_week             |
    Then I should receive a 200 OK response
    And the response should correlate user activity with system performance
    And the response should identify performance bottlenecks
    And the response should suggest optimization opportunities
    And the response should include capacity planning insights

  # =============================================================================
  # BEHAVIORAL MONITORING SCENARIOS
  # =============================================================================

  @user @monitoring @behavior @compliance @happy-path
  Scenario: Monitor User Behavior for Compliance Violations
    Given I am authenticated as a compliance officer
    When I request behavioral compliance monitoring with:
      | monitoring_rules | data_access_frequency,off_hours_activity,bulk_downloads |
      | time_period      | last_month                |
      | alert_threshold  | medium                    |
    Then I should receive a 200 OK response
    And the response should include compliance violation alerts
    And the response should include behavior pattern analysis
    And the response should flag unusual data access patterns
    And the response should identify potential insider threats
    And appropriate escalation procedures should be triggered

  @user @monitoring @behavior @insider-threat
  Scenario: Detect Potential Insider Threat Indicators
    Given I am authenticated as a security administrator
    When I request insider threat analysis with:
      | risk_indicators | unusual_data_access,privilege_escalation,off_hours_activity |
      | user_scope      | all_users                 |
      | analysis_depth  | comprehensive             |
    Then I should receive a 200 OK response
    And the response should include risk scores for each user
    And the response should flag high-risk behaviors
    And the response should include behavioral change detection
    And the response should suggest investigation priorities
    And security incident workflows should be initiated for high-risk cases

  @user @monitoring @behavior @data-exfiltration
  Scenario: Monitor for Data Exfiltration Patterns
    Given I am authenticated as a security administrator
    When I request data exfiltration monitoring with:
      | monitoring_scope | bulk_downloads,email_attachments,external_transfers |
      | time_period      | real_time                 |
      | sensitivity      | high                      |
    Then I should receive a 200 OK response
    And the response should include data movement patterns
    And the response should flag unusual download volumes
    And the response should monitor external data transfers
    And the response should include real-time alerts
    And automatic blocking should be triggered for suspicious activity

  # =============================================================================
  # REPORTING AND DASHBOARDS SCENARIOS
  # =============================================================================

  @user @reporting @executive-dashboard
  Scenario: Generate Executive Dashboard for User Activity
    Given I am authenticated as an executive
    When I request executive activity dashboard with:
      | dashboard_type | executive_summary     |
      | time_period    | last_quarter          |
      | metrics        | user_engagement,productivity,compliance |
    Then I should receive a 200 OK response
    And the response should include high-level activity metrics
    And the response should include user engagement trends
    And the response should include compliance status overview
    And the response should include key performance indicators
    And the dashboard should be suitable for executive presentation

  @user @reporting @compliance-report
  Scenario: Generate Regulatory Compliance Activity Report
    Given I am authenticated as a compliance officer
    When I request compliance activity report with:
      | report_type    | regulatory_compliance |
      | time_period    | last_year             |
      | regulations    | aml,kyc,gdpr          |
      | detail_level   | comprehensive         |
    Then I should receive a 200 OK response
    And the report should include AML compliance activities
    And the report should include KYC verification activities
    And the report should include GDPR compliance measures
    And the report should be suitable for regulatory submission
    And the report should include audit trail references

  # =============================================================================
  # REAL-TIME MONITORING SCENARIOS
  # =============================================================================

  @user @monitoring @real-time @active-sessions
  Scenario: Monitor Active User Sessions in Real-Time
    Given I am authenticated as a system administrator
    When I request real-time session monitoring
    Then I should receive current active sessions
    And the response should include session details
    And the response should include user activity status
    And the response should include resource utilization
    And the response should update in real-time
    And I should be able to terminate suspicious sessions

  @user @monitoring @real-time @security-events
  Scenario: Real-Time Security Event Monitoring
    Given I am authenticated as a security administrator
    When I monitor real-time security events
    Then I should receive immediate alerts for security violations
    And the alerts should include failed login attempts
    And the alerts should include privilege escalation attempts
    And the alerts should include unusual access patterns
    And the alerts should trigger automated response procedures
    And incident response workflows should be initiated automatically

  # =============================================================================
  # PRIVACY AND COMPLIANCE SCENARIOS
  # =============================================================================

  @user @privacy @data-protection
  Scenario: Ensure User Activity Data Protection Compliance
    Given I am authenticated as a privacy officer
    When I review user activity data collection and storage
    Then personal data should be properly anonymized where possible
    And data retention policies should be enforced
    And user consent should be documented and respected
    And data access should be logged and auditable
    And privacy regulations should be fully complied with

  @user @compliance @audit-readiness
  Scenario: Maintain Audit-Ready Activity Logs
    Given I am authenticated as a compliance officer
    When I prepare for regulatory audit
    Then all user activity logs should be complete and accurate
    And audit trails should be immutable and tamper-evident
    And log retention should meet regulatory requirements
    And data should be easily retrievable for audit purposes
    And compliance documentation should be comprehensive and current
