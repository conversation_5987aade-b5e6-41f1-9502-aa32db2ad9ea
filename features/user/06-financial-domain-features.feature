@user
Feature: 06 - Financial Domain-Specific Features
  As a financial institution administrator
  I want to manage users with financial domain-specific requirements
  So that I can ensure compliance with financial regulations and optimize financial crime detection

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"
    And the user service is available
    And I have tenant header "x-tenant-code" set to "FINANCIALCORP"

  # =============================================================================
  # FINANCIAL ANALYST ROLE MANAGEMENT
  # =============================================================================

  @user @financial @analyst @certification @happy-path @core
  Scenario: Manage Financial Analyst Certifications and Qualifications
    Given I am authenticated as an HR administrator
    And a financial analyst exists with ID "analyst-123"
    When I update analyst certifications with:
      | user_id         | analyst-123                   |
      | certifications  | cams,cfcs,acams               |
      | license_numbers | CAMS-12345,CFCS-67890         |
      | expiry_dates    | 2025-06-01,2025-12-31         |
      | specializations | aml,fraud_detection,kyc       |
    Then I should receive a 200 OK response
    And the analyst should have updated certifications
    And certification expiry monitoring should be enabled
    And specialized access permissions should be granted
    And compliance team should be notified of updates

  @user @financial @analyst @case-assignment
  Scenario: Assign Cases Based on Analyst Expertise and Workload
    Given I am authenticated as a case manager
    And financial analysts exist with different specializations
    When I request case assignment recommendations with:
      | case_type       | money_laundering              |
      | complexity      | high                          |
      | urgency         | medium                        |
      | required_skills | aml,transaction_analysis      |
    Then I should receive a 200 OK response
    And the response should include qualified analysts
    And the response should consider current workload
    And the response should consider expertise match
    And the response should include availability status
    And automatic assignment should be possible

  @user @financial @analyst @performance-tracking
  Scenario: Track Financial Analyst Performance Metrics
    Given I am authenticated as a team manager
    And financial analysts have completed cases
    When I request analyst performance metrics with:
      | analyst_ids     | analyst-123,analyst-456       |
      | metrics         | case_closure_rate,accuracy,efficiency |
      | time_period     | last_quarter                  |
    Then I should receive a 200 OK response
    And the response should include case closure rates
    And the response should include accuracy scores
    And the response should include efficiency metrics
    And the response should include peer comparisons
    And performance improvement recommendations should be provided

  # =============================================================================
  # COMPLIANCE OFFICER WORKFLOWS
  # =============================================================================

  @user @financial @compliance @regulatory-access
  Scenario: Manage Compliance Officer Regulatory Access
    Given I am authenticated as a compliance administrator
    And a compliance officer exists with ID "compliance-123"
    When I configure regulatory access with:
      | user_id         | compliance-123                |
      | regulations     | bsa,aml,ofac,kyc              |
      | access_levels   | read_write,read_only,admin    |
      | jurisdictions   | us,eu,uk                      |
      | reporting_rights | true                         |
    Then I should receive a 200 OK response
    And the officer should have appropriate regulatory access
    And jurisdiction-specific permissions should be applied
    And reporting capabilities should be enabled
    And regulatory change notifications should be configured

  @user @financial @compliance @sar-filing
  Scenario: Manage SAR Filing Permissions and Workflow
    Given I am authenticated as a compliance administrator
    And a compliance officer needs SAR filing access
    When I grant SAR filing permissions with:
      | user_id         | compliance-456                |
      | filing_types    | sar,ctr,fbar                  |
      | approval_level  | senior_officer                |
      | review_required | true                          |
      | deadline_alerts | enabled                       |
    Then I should receive a 200 OK response
    And the officer should have SAR filing permissions
    And approval workflows should be configured
    And deadline monitoring should be enabled
    And regulatory submission tracking should be activated

  @user @financial @compliance @audit-preparation
  Scenario: Prepare Compliance Officers for Regulatory Audits
    Given I am authenticated as a compliance administrator
    And a regulatory audit is scheduled
    When I prepare compliance team for audit with:
      | audit_type      | aml_examination               |
      | audit_date      | 2024-09-15                    |
      | team_members    | compliance-123,compliance-456 |
      | preparation_tasks | document_review,system_demo   |
    Then I should receive a 200 OK response
    And audit preparation tasks should be assigned
    And document access should be configured
    And system demonstration permissions should be granted
    And audit timeline should be communicated

  # =============================================================================
  # INVESTIGATOR CASE MANAGEMENT
  # =============================================================================

  @user @financial @investigator @case-access
  Scenario: Configure Investigator Case Access Based on Clearance Level
    Given I am authenticated as a case administrator
    And an investigator exists with security clearance
    When I configure case access with:
      | user_id         | investigator-789              |
      | clearance_level | level_3                       |
      | case_types      | money_laundering,terrorism_financing |
      | data_access     | full_transaction_history      |
      | geographic_scope | global                       |
    Then I should receive a 200 OK response
    And the investigator should have appropriate case access
    And clearance-based restrictions should be applied
    And data access should match clearance level
    And geographic limitations should be enforced

  @user @financial @investigator @evidence-handling
  Scenario: Manage Investigator Evidence Handling Permissions
    Given I am authenticated as a case administrator
    And an investigator is working on a complex case
    When I configure evidence handling with:
      | user_id         | investigator-123              |
      | evidence_types  | financial_records,communications |
      | handling_level  | chain_of_custody              |
      | export_permissions | restricted                 |
      | sharing_rules   | supervisor_approval_required  |
    Then I should receive a 200 OK response
    And evidence handling permissions should be configured
    And chain of custody should be enforced
    And export restrictions should be applied
    And sharing approval workflows should be enabled

  @user @financial @investigator @collaboration
  Scenario: Enable Cross-Jurisdictional Investigator Collaboration
    Given I am authenticated as a case administrator
    And investigators need to collaborate across jurisdictions
    When I enable collaboration with:
      | primary_investigator | investigator-us-123          |
      | collaborating_investigators | investigator-eu-456,investigator-uk-789 |
      | case_id         | case-ml-2024-001              |
      | data_sharing_level | limited                    |
      | communication_channels | secure_messaging,video_conference |
    Then I should receive a 200 OK response
    And cross-jurisdictional collaboration should be enabled
    And data sharing restrictions should be applied
    And secure communication channels should be configured
    And collaboration audit trail should be maintained

  # =============================================================================
  # RISK MANAGER OVERSIGHT
  # =============================================================================

  @user @financial @risk-manager @portfolio-oversight
  Scenario: Configure Risk Manager Portfolio Oversight
    Given I am authenticated as a risk administrator
    And a risk manager needs portfolio oversight
    When I configure portfolio oversight with:
      | user_id         | risk-manager-123              |
      | portfolio_scope | institutional_clients         |
      | risk_metrics    | transaction_volume,geographic_exposure |
      | alert_thresholds | high_risk,unusual_patterns   |
      | reporting_frequency | daily                      |
    Then I should receive a 200 OK response
    And portfolio oversight should be configured
    And risk metrics monitoring should be enabled
    And alert thresholds should be set
    And automated reporting should be scheduled

  @user @financial @risk-manager @model-validation
  Scenario: Grant Risk Manager Model Validation Access
    Given I am authenticated as a risk administrator
    And a risk manager needs model validation access
    When I grant model validation permissions with:
      | user_id         | risk-manager-456              |
      | model_types     | aml_scoring,fraud_detection   |
      | validation_level | full_access                  |
      | testing_environments | sandbox,staging            |
      | approval_rights | model_deployment              |
    Then I should receive a 200 OK response
    And model validation access should be granted
    And testing environment access should be configured
    And model deployment approval rights should be enabled
    And validation audit trail should be maintained

  # =============================================================================
  # SPECIALIZED FINANCIAL ROLES
  # =============================================================================

  @user @financial @kyc-specialist @customer-verification
  Scenario: Configure KYC Specialist Customer Verification Access
    Given I am authenticated as a compliance administrator
    And a KYC specialist needs customer verification access
    When I configure KYC access with:
      | user_id         | kyc-specialist-123            |
      | verification_types | individual,corporate,trust  |
      | data_sources    | credit_bureaus,sanctions_lists |
      | approval_limits | up_to_1_million               |
      | escalation_rules | above_limit_to_senior        |
    Then I should receive a 200 OK response
    And KYC verification access should be configured
    And data source integrations should be enabled
    And approval limits should be enforced
    And escalation workflows should be activated

  @user @financial @sanctions-analyst @screening
  Scenario: Configure Sanctions Analyst Screening Capabilities
    Given I am authenticated as a compliance administrator
    And a sanctions analyst needs screening access
    When I configure sanctions screening with:
      | user_id         | sanctions-analyst-456         |
      | screening_lists | ofac,eu_sanctions,un_sanctions |
      | screening_types | real_time,batch,manual        |
      | override_permissions | false_positive_resolution |
      | reporting_access | sanctions_violations          |
    Then I should receive a 200 OK response
    And sanctions screening access should be configured
    And multiple screening lists should be available
    And override permissions should be granted
    And violations reporting should be enabled

  # =============================================================================
  # FINANCIAL INSTITUTION SPECIFIC FEATURES
  # =============================================================================

  @user @financial @institution @branch-management
  Scenario: Manage Users Across Financial Institution Branches
    Given I am authenticated as a regional administrator
    And users exist across multiple branches
    When I manage branch user assignments with:
      | user_id         | analyst-branch-123            |
      | branch_codes    | NYC001,NYC002,BOS001          |
      | access_scope    | local_transactions            |
      | transfer_permissions | inter_branch_limited      |
      | reporting_hierarchy | branch_manager             |
    Then I should receive a 200 OK response
    And branch assignments should be configured
    And access scope should be limited to assigned branches
    And transfer permissions should be restricted
    And reporting hierarchy should be established

  @user @financial @institution @regulatory-reporting
  Scenario: Configure Users for Regulatory Reporting Requirements
    Given I am authenticated as a compliance administrator
    And users need regulatory reporting access
    When I configure regulatory reporting with:
      | user_roles      | compliance_officer,risk_manager |
      | reporting_types | sar,ctr,8300,fbar             |
      | submission_rights | draft,review,submit           |
      | deadline_monitoring | enabled                    |
      | quality_controls | mandatory_review              |
    Then I should receive a 200 OK response
    And regulatory reporting access should be configured
    And submission workflows should be established
    And deadline monitoring should be enabled
    And quality control processes should be enforced

  # =============================================================================
  # FINANCIAL CRIME DETECTION WORKFLOWS
  # =============================================================================

  @user @financial @crime-detection @alert-management
  Scenario: Configure Users for Financial Crime Alert Management
    Given I am authenticated as an operations administrator
    And users need alert management capabilities
    When I configure alert management with:
      | user_types      | analyst,investigator,supervisor |
      | alert_categories | aml,fraud,sanctions,kyc        |
      | priority_levels | low,medium,high,critical        |
      | assignment_rules | round_robin,expertise_based     |
      | escalation_timers | 24h,48h,72h                   |
    Then I should receive a 200 OK response
    And alert management capabilities should be configured
    And category-based access should be established
    And priority-based workflows should be enabled
    And automatic escalation should be configured

  @user @financial @crime-detection @investigation-workflow
  Scenario: Configure Investigation Workflow for Financial Crimes
    Given I am authenticated as a case administrator
    And complex financial crime investigations are required
    When I configure investigation workflows with:
      | investigation_types | money_laundering,fraud,sanctions |
      | workflow_stages | initial,analysis,evidence,conclusion |
      | approval_gates | supervisor,compliance,legal       |
      | documentation_requirements | mandatory_at_each_stage    |
      | collaboration_tools | case_notes,evidence_sharing   |
    Then I should receive a 200 OK response
    And investigation workflows should be configured
    And approval gates should be established
    And documentation requirements should be enforced
    And collaboration tools should be enabled

  # =============================================================================
  # COMPLIANCE AND AUDIT SCENARIOS
  # =============================================================================

  @user @financial @compliance @regulatory-compliance
  Scenario: Ensure User Management Meets Financial Regulatory Requirements
    Given I am authenticated as a compliance officer
    When I review user management for regulatory compliance
    Then user access should be properly documented
    And role assignments should be justified and approved
    And access reviews should be conducted regularly
    And segregation of duties should be enforced
    And audit trails should be complete and immutable

  @user @financial @audit @examination-readiness
  Scenario: Prepare User Management for Regulatory Examination
    Given I am authenticated as a compliance administrator
    And a regulatory examination is scheduled
    When I prepare user management documentation
    Then user access matrices should be current and accurate
    And role definitions should be clearly documented
    And access approval processes should be well-defined
    And exception handling should be properly documented
    And remediation plans should be available for any deficiencies
