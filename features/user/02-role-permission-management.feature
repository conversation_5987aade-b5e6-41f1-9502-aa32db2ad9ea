@user
Feature: 02 - Role and Permission Management
  As a system administrator
  I want to manage user roles and permissions in the Qeep financial monitoring system
  So that I can ensure proper access control and compliance with financial regulations

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"
    And the user service is available
    And I have tenant header "x-tenant-code" set to "financial-corp"

  # =============================================================================
  # ROLE ASSIGNMENT SCENARIOS
  # =============================================================================

  @user @role @assign @happy-path @core
  Scenario: Successful Role Assignment to Financial Analyst
    Given I am authenticated as a system administrator
    And a user exists with ID "user-123"
    And the role "financial_analyst" exists in the system
    When I assign role to user with:
      | user_id   | user-123          |
      | role_name | financial_analyst |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the user should have the financial_analyst role
    And the user should inherit permissions from the role
    And a role assignment event should be published
    And the role assignment should be logged for audit
    And the response time should be reasonable

  @user @role @assign @happy-path @compliance-officer
  Scenario: Successful Role Assignment to Compliance Officer
    Given I am authenticated as a system administrator
    And a user exists with ID "user-456"
    And the role "compliance_officer" exists in the system
    When I assign role to user with:
      | user_id   | user-456          |
      | role_name | compliance_officer |
    Then I should receive a 200 OK response
    And the user should have compliance officer permissions
    And the user should be able to access compliance reports
    And the user should be able to review investigation cases
    And the user should be able to generate regulatory reports

  @user @role @assign @happy-path @multiple-roles
  Scenario: Assign Multiple Roles to Senior Investigator
    Given I am authenticated as a system administrator
    And a user exists with ID "user-789"
    And the roles "investigator" and "case_manager" exist in the system
    When I assign multiple roles to user with:
      | user_id | user-789                      |
      | roles   | investigator,case_manager     |
    Then I should receive a 200 OK response
    And the user should have both investigator and case_manager roles
    And the user should have combined permissions from both roles
    And no permission conflicts should exist

  @user @role @assign @validation @negative @non-existent-user
  Scenario: Role Assignment to Non-existent User
    Given I am authenticated as a system administrator
    And the role "financial_analyst" exists in the system
    When I attempt to assign role to non-existent user with:
      | user_id   | non-existent-123  |
      | role_name | financial_analyst |
    Then I should receive a 404 Not Found response
    And the error should mention user not found
    And no role assignment should be created

  @user @role @assign @validation @negative @non-existent-role
  Scenario: Assignment of Non-existent Role
    Given I am authenticated as a system administrator
    And a user exists with ID "user-123"
    When I attempt to assign non-existent role with:
      | user_id   | user-123           |
      | role_name | non_existent_role  |
    Then I should receive a 404 Not Found response
    And the error should mention role not found
    And no role assignment should be created

  @user @role @assign @authorization @negative @insufficient-permissions
  Scenario: Role Assignment without Admin Permissions
    Given I am authenticated as a financial analyst
    And a user exists with ID "user-123"
    When I attempt to assign a role to the user
    Then I should receive a 403 Forbidden response
    And the error should mention insufficient permissions
    And no role assignment should be created

  @user @role @assign @business-rules @duplicate-assignment
  Scenario: Duplicate Role Assignment Prevention
    Given I am authenticated as a system administrator
    And a user exists with ID "user-123"
    And the user already has role "financial_analyst"
    When I attempt to assign the same role again
    Then I should receive a 400 Bad Request response
    And the error should mention role already assigned
    And no duplicate assignment should be created

  # =============================================================================
  # ROLE REMOVAL SCENARIOS
  # =============================================================================

  @user @role @remove @happy-path @core
  Scenario: Successful Role Removal from User
    Given I am authenticated as a system administrator
    And a user exists with ID "user-123"
    And the user has role "financial_analyst"
    When I remove role from user with:
      | user_id   | user-123          |
      | role_name | financial_analyst |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the user should no longer have the financial_analyst role
    And the user should lose permissions associated with the role
    And a role removal event should be published
    And the role removal should be logged for audit

  @user @role @remove @validation @negative @non-assigned-role
  Scenario: Remove Role Not Assigned to User
    Given I am authenticated as a system administrator
    And a user exists with ID "user-123"
    And the user does not have role "compliance_officer"
    When I attempt to remove role from user with:
      | user_id   | user-123          |
      | role_name | compliance_officer |
    Then I should receive a 400 Bad Request response
    And the error should mention role not assigned to user
    And no changes should be made to user roles

  @user @role @remove @business-rules @last-admin-protection
  Scenario: Prevent Removal of Last Admin Role
    Given I am authenticated as a system administrator
    And only one user has the "system_admin" role
    When I attempt to remove the admin role from the last admin user
    Then I should receive a 400 Bad Request response
    And the error should mention cannot remove last admin
    And the admin role should remain assigned
    And system administration capabilities should be preserved

  # =============================================================================
  # PERMISSION MANAGEMENT SCENARIOS
  # =============================================================================

  @user @permission @grant @happy-path @core
  Scenario: Grant Direct Permission to User
    Given I am authenticated as a system administrator
    And a user exists with ID "user-123"
    And the permission "view_sensitive_transactions" exists
    When I grant permission to user with:
      | user_id         | user-123                     |
      | permission_name | view_sensitive_transactions  |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the user should have the granted permission
    And the permission grant should be logged for audit
    And the user should be able to access sensitive transaction data

  @user @permission @grant @happy-path @temporary-permission
  Scenario: Grant Temporary Permission with Expiration
    Given I am authenticated as a system administrator
    And a user exists with ID "user-123"
    And the permission "emergency_case_access" exists
    When I grant temporary permission to user with:
      | user_id         | user-123              |
      | permission_name | emergency_case_access |
      | expires_at      | 2024-12-31T23:59:59Z  |
      | reason          | Emergency investigation |
    Then I should receive a 200 OK response
    And the user should have the permission until expiration
    And the permission should automatically expire
    And the temporary grant should be logged with reason

  @user @permission @revoke @happy-path @core
  Scenario: Revoke Direct Permission from User
    Given I am authenticated as a system administrator
    And a user exists with ID "user-123"
    And the user has direct permission "view_sensitive_transactions"
    When I revoke permission from user with:
      | user_id         | user-123                     |
      | permission_name | view_sensitive_transactions  |
    Then I should receive a 200 OK response
    And the user should no longer have the permission
    And the permission revocation should be logged for audit
    And the user should lose access to sensitive transaction data

  @user @permission @check @happy-path @core
  Scenario: Check User Permission Status
    Given I am authenticated as a system administrator
    And a user exists with ID "user-123"
    And the user has permission "create_investigation_case"
    When I check user permission with:
      | user_id         | user-123                  |
      | permission_name | create_investigation_case |
    Then I should receive a 200 OK response
    And the response should confirm the user has the permission
    And the response should include permission source (role or direct)
    And the response should include permission details

  @user @permission @list @happy-path @core
  Scenario: List All User Permissions
    Given I am authenticated as a system administrator
    And a user exists with ID "user-123"
    And the user has multiple roles and direct permissions
    When I request all permissions for user "user-123"
    Then I should receive a 200 OK response
    And the response should include all effective permissions
    And the response should group permissions by source
    And the response should include role-based permissions
    And the response should include direct permissions
    And the response should show permission inheritance

  # =============================================================================
  # ROLE-BASED ACCESS CONTROL (RBAC) SCENARIOS
  # =============================================================================

  @user @rbac @validation @happy-path @core
  Scenario: Validate User Access to Financial Data
    Given I am authenticated as a financial analyst
    And I have role "financial_analyst"
    When I attempt to access transaction monitoring data
    Then I should receive a 200 OK response
    And I should be able to view transaction details
    And I should be able to create alerts
    And I should not be able to access compliance reports
    And I should not be able to modify system settings

  @user @rbac @validation @compliance-officer
  Scenario: Validate Compliance Officer Access Rights
    Given I am authenticated as a compliance officer
    And I have role "compliance_officer"
    When I attempt to access compliance features
    Then I should be able to view all investigation cases
    And I should be able to generate regulatory reports
    And I should be able to review alert dispositions
    And I should be able to access audit trails
    And I should not be able to modify user roles

  @user @rbac @validation @investigator
  Scenario: Validate Investigator Access Rights
    Given I am authenticated as an investigator
    And I have role "investigator"
    When I attempt to access investigation features
    Then I should be able to create investigation cases
    And I should be able to update case status
    And I should be able to add case notes
    And I should be able to request additional data
    And I should not be able to close cases without approval

  @user @rbac @hierarchy @supervisor-access
  Scenario: Validate Supervisor Role Hierarchy
    Given I am authenticated as a supervisor
    And I have role "supervisor"
    When I attempt to access team management features
    Then I should be able to view my team's cases
    And I should be able to reassign cases within my team
    And I should be able to approve case closures
    And I should be able to view team performance metrics
    And I should not be able to access other teams' data

  # =============================================================================
  # PERMISSION INHERITANCE AND CONFLICTS
  # =============================================================================

  @user @permission @inheritance @role-based
  Scenario: Permission Inheritance from Multiple Roles
    Given I am authenticated as a system administrator
    And a user has roles "financial_analyst" and "case_reviewer"
    When I check the user's effective permissions
    Then the user should have permissions from both roles
    And there should be no permission conflicts
    And the most permissive access should be granted
    And permission inheritance should be clearly documented

  @user @permission @override @direct-permission
  Scenario: Direct Permission Override of Role Permission
    Given I am authenticated as a system administrator
    And a user has role "junior_analyst" with limited permissions
    And I grant direct permission "view_high_risk_cases" to the user
    When I check the user's effective permissions
    Then the user should have the direct permission
    And the direct permission should override role limitations
    And the override should be logged for audit
    And the permission source should be clearly identified

  # =============================================================================
  # SECURITY AND COMPLIANCE SCENARIOS
  # =============================================================================

  @user @security @permission-escalation
  Scenario: Prevent Unauthorized Permission Escalation
    Given I am authenticated as a junior analyst
    When I attempt to grant myself admin permissions
    Then I should receive a 403 Forbidden response
    And the attempt should be logged as a security event
    And no permission changes should be made
    And security alerts should be triggered

  @user @compliance @audit-trail
  Scenario: Complete Audit Trail for Role Changes
    Given I am authenticated as a system administrator
    When I perform role and permission changes
    Then all changes should be logged with timestamps
    And the audit log should include user who made changes
    And the audit log should include reason for changes
    And the audit log should include before and after states
    And the audit trail should be immutable

  @user @security @tenant-isolation
  Scenario: Role and Permission Tenant Isolation
    Given I am authenticated as admin for tenant "TENANT_A"
    And users exist in multiple tenants
    When I manage roles and permissions
    Then I should only affect users in my tenant
    And cross-tenant role assignments should be prevented
    And tenant-specific roles should be isolated
    And no data leakage should occur between tenants
