@user
Feature: 05 - User Lifecycle Management
  As a system administrator or HR manager
  I want to manage the complete user lifecycle in the Qeep financial monitoring system
  So that I can ensure proper onboarding, activation, and offboarding processes

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"
    And the user service is available
    And I have tenant header "x-tenant-code" set to "financial-corp"

  # =============================================================================
  # USER ONBOARDING SCENARIOS
  # =============================================================================

  @user @lifecycle @onboarding @happy-path @core
  Scenario: Complete User Onboarding for New Financial Analyst
    Given I am authenticated as an HR administrator
    When I initiate user onboarding with:
      | email           | <EMAIL> |
      | first_name      | Sarah                         |
      | last_name       | Johnson                       |
      | role            | financial_analyst             |
      | department      | compliance                    |
      | team_id         | fraud-detection-team          |
      | manager_id      | manager-123                   |
      | start_date      | 2024-06-01                    |
      | security_clearance | level_2                    |
    Then I should receive a 201 Created response
    And the response should follow StandardApiResponse format
    And the user should be created with PENDING status
    And an onboarding workflow should be initiated
    And welcome email should be sent to the user
    And manager should be notified of new team member
    And IT provisioning tasks should be created
    And training assignments should be scheduled

  @user @lifecycle @onboarding @compliance-officer
  Scenario: Onboard New Compliance Officer with Enhanced Security
    Given I am authenticated as an HR administrator
    When I initiate compliance officer onboarding with:
      | email           | <EMAIL> |
      | first_name      | Michael                              |
      | last_name       | Chen                                 |
      | role            | compliance_officer                   |
      | security_clearance | level_3                           |
      | background_check_required | true                        |
      | mfa_required    | true                                 |
    Then I should receive a 201 Created response
    And enhanced security onboarding should be initiated
    And background check workflow should be triggered
    And MFA setup should be mandatory
    And compliance training should be assigned
    And regulatory access permissions should be prepared

  @user @lifecycle @onboarding @bulk-import
  Scenario: Bulk User Onboarding from HR System
    Given I am authenticated as an HR administrator
    When I initiate bulk user onboarding with:
      | import_source   | hr_system_export              |
      | user_count      | 25                            |
      | default_role    | junior_analyst                |
      | department      | operations                    |
      | start_date      | 2024-07-01                    |
    Then I should receive a 202 Accepted response
    And bulk onboarding job should be queued
    And progress tracking should be available
    And individual onboarding workflows should be created
    And batch notifications should be sent
    And error handling should manage failed imports

  # =============================================================================
  # USER ACTIVATION SCENARIOS
  # =============================================================================

  @user @lifecycle @activation @happy-path @core
  Scenario: Successful User Activation After Email Verification
    Given I am authenticated as a system administrator
    And a user exists with status "PENDING" and ID "user-123"
    And the user has completed email verification
    When I activate the user with:
      | user_id         | user-123                      |
      | activation_date | 2024-06-01                    |
      | activated_by    | admin-456                     |
      | notes           | Completed onboarding process  |
    Then I should receive a 200 OK response
    And the user status should change to "ACTIVE"
    And the user should gain access to assigned systems
    And activation notification should be sent to user
    And manager should be notified of activation
    And audit log should record the activation

  @user @lifecycle @activation @conditional
  Scenario: Conditional User Activation Based on Training Completion
    Given I am authenticated as an HR administrator
    And a user exists with status "PENDING"
    And the user has completed required training modules
    When I check activation eligibility for user "user-456"
    Then the system should verify training completion
    And the system should verify background check status
    And the system should verify manager approval
    And automatic activation should proceed if all conditions are met
    And activation should be delayed if conditions are not met

  @user @lifecycle @activation @security-clearance
  Scenario: Activate User with Security Clearance Requirements
    Given I am authenticated as a security administrator
    And a user exists with pending security clearance
    When I activate user with security clearance with:
      | user_id              | user-789              |
      | clearance_level      | level_3               |
      | clearance_expiry     | 2025-06-01            |
      | approved_by          | security-admin-123    |
      | access_restrictions  | no_external_access    |
    Then I should receive a 200 OK response
    And the user should be activated with clearance level
    And appropriate access restrictions should be applied
    And clearance expiry monitoring should be set up
    And security team should be notified

  # =============================================================================
  # USER SUSPENSION SCENARIOS
  # =============================================================================

  @user @lifecycle @suspension @happy-path @core
  Scenario: Temporary User Suspension for Policy Violation
    Given I am authenticated as an HR administrator
    And an active user exists with ID "user-123"
    When I suspend the user with:
      | user_id         | user-123                      |
      | suspension_type | temporary                     |
      | duration_days   | 7                             |
      | reason          | Policy violation investigation |
      | suspended_by    | hr-admin-456                  |
    Then I should receive a 200 OK response
    And the user status should change to "SUSPENDED"
    And the user should lose system access immediately
    And active sessions should be terminated
    And suspension notification should be sent
    And automatic reactivation should be scheduled
    And manager should be notified of suspension

  @user @lifecycle @suspension @security-incident
  Scenario: Emergency User Suspension for Security Incident
    Given I am authenticated as a security administrator
    And a security incident involves user "user-456"
    When I perform emergency suspension with:
      | user_id         | user-456                      |
      | suspension_type | emergency                     |
      | reason          | Security incident investigation |
      | immediate_effect | true                         |
      | revoke_tokens   | true                          |
    Then I should receive a 200 OK response
    And the user should be immediately suspended
    And all user tokens should be revoked
    And all active sessions should be terminated
    And security team should be alerted
    And incident response workflow should be triggered

  # =============================================================================
  # USER DEACTIVATION SCENARIOS
  # =============================================================================

  @user @lifecycle @deactivation @happy-path @core
  Scenario: User Deactivation for Employee Departure
    Given I am authenticated as an HR administrator
    And an active user exists with ID "user-123"
    When I deactivate the user with:
      | user_id         | user-123                      |
      | deactivation_date | 2024-06-30                  |
      | reason          | Employee departure            |
      | last_working_day | 2024-06-28                   |
      | data_retention  | 90_days                       |
    Then I should receive a 200 OK response
    And the user status should change to "INACTIVE"
    And system access should be revoked
    And data retention policy should be applied
    And manager should be notified
    And IT asset recovery should be initiated
    And knowledge transfer reminders should be sent

  @user @lifecycle @deactivation @immediate
  Scenario: Immediate User Deactivation for Termination
    Given I am authenticated as an HR administrator
    And an active user exists with ID "user-456"
    When I perform immediate deactivation with:
      | user_id         | user-456                      |
      | deactivation_type | immediate                   |
      | reason          | Termination for cause        |
      | revoke_access   | true                          |
      | secure_data     | true                          |
    Then I should receive a 200 OK response
    And the user should be immediately deactivated
    And all system access should be revoked instantly
    And user data should be secured
    And security team should be notified
    And audit trail should be preserved

  # =============================================================================
  # USER REACTIVATION SCENARIOS
  # =============================================================================

  @user @lifecycle @reactivation @happy-path @core
  Scenario: User Reactivation After Suspension Period
    Given I am authenticated as an HR administrator
    And a user exists with status "SUSPENDED"
    And the suspension period has ended
    When I reactivate the user with:
      | user_id         | user-123                      |
      | reactivation_date | 2024-06-15                  |
      | reactivated_by  | hr-admin-456                  |
      | conditions      | mandatory_training_refresh    |
    Then I should receive a 200 OK response
    And the user status should change to "ACTIVE"
    And system access should be restored
    And mandatory training should be assigned
    And manager should be notified of reactivation
    And monitoring period should be established

  @user @lifecycle @reactivation @returning-employee
  Scenario: Reactivate Returning Employee Account
    Given I am authenticated as an HR administrator
    And a user exists with status "INACTIVE" from previous employment
    When I reactivate returning employee with:
      | user_id         | user-789                      |
      | return_date     | 2024-07-01                    |
      | new_role        | senior_analyst                |
      | new_department  | risk_management               |
      | security_review | required                      |
    Then I should receive a 200 OK response
    And the user should be reactivated with new role
    And security review should be initiated
    And updated training should be assigned
    And new access permissions should be configured
    And welcome back notification should be sent

  # =============================================================================
  # LIFECYCLE AUTOMATION SCENARIOS
  # =============================================================================

  @user @lifecycle @automation @scheduled-actions
  Scenario: Automated Lifecycle Actions Based on Scheduled Events
    Given I am authenticated as a system administrator
    And users have scheduled lifecycle events
    When the system processes scheduled lifecycle actions
    Then pending activations should be processed automatically
    And suspension periods should end automatically
    And security clearance renewals should be triggered
    And training deadline reminders should be sent
    And compliance status should be updated
    And managers should receive relevant notifications

  @user @lifecycle @automation @policy-enforcement
  Scenario: Automated Policy Enforcement for Lifecycle Management
    Given I am authenticated as a compliance administrator
    When the system enforces lifecycle policies
    Then inactive users should be automatically deactivated
    And expired security clearances should trigger access restrictions
    And overdue training should result in access limitations
    And policy violations should trigger automatic reviews
    And compliance reports should be generated automatically

  # =============================================================================
  # LIFECYCLE REPORTING SCENARIOS
  # =============================================================================

  @user @lifecycle @reporting @status-overview
  Scenario: Generate User Lifecycle Status Report
    Given I am authenticated as an HR administrator
    When I request lifecycle status report with:
      | report_period   | current_quarter               |
      | include_metrics | onboarding,activation,deactivation |
      | department_breakdown | true                      |
    Then I should receive a 200 OK response
    And the report should include user status distribution
    And the report should include onboarding pipeline metrics
    And the report should include activation success rates
    And the report should include deactivation reasons analysis
    And the report should include department-wise breakdowns

  @user @lifecycle @reporting @compliance-audit
  Scenario: Generate Lifecycle Compliance Audit Report
    Given I am authenticated as a compliance officer
    When I request lifecycle compliance audit with:
      | audit_period    | last_year                     |
      | compliance_areas | data_retention,access_control,training |
      | detail_level    | comprehensive                 |
    Then I should receive a 200 OK response
    And the report should include data retention compliance
    And the report should include access control adherence
    And the report should include training completion rates
    And the report should include policy violation incidents
    And the report should be suitable for regulatory submission

  # =============================================================================
  # SECURITY AND COMPLIANCE SCENARIOS
  # =============================================================================

  @user @lifecycle @security @data-protection
  Scenario: Ensure Data Protection Throughout User Lifecycle
    Given I am authenticated as a privacy officer
    When I review user lifecycle data handling
    Then personal data should be protected at all lifecycle stages
    And data retention policies should be enforced consistently
    And data deletion should occur according to policy
    And access to lifecycle data should be properly controlled
    And privacy regulations should be fully complied with

  @user @lifecycle @compliance @audit-trail
  Scenario: Maintain Complete Lifecycle Audit Trail
    Given I am authenticated as a compliance officer
    When I review user lifecycle audit trails
    Then all lifecycle events should be logged with timestamps
    And the audit log should include responsible administrators
    And the audit log should include business justifications
    And the audit log should include system-generated events
    And the audit trail should be immutable and tamper-evident
