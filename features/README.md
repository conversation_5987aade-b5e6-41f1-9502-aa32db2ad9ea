# Qeep Features Documentation

This directory contains Gherkin feature files that document the user stories and acceptance criteria for Qeep's functionality.

## 📁 Folder Structure

```
features/
├── auth/                    # Authentication & Authorization features
│   ├── registration.feature # User registration scenarios
│   ├── login.feature       # User login scenarios  
│   ├── verification.feature # Email verification scenarios
│   └── profile.feature     # Profile management scenarios
├── alerts/                 # Alert management features (future)
├── tenants/               # Multi-tenancy features (future)
└── README.md              # This file
```

## 🎯 Feature Categories

### Authentication (`auth/`)

- **Registration**: User signup, validation, business rules
- **Login**: Authentication, credential validation, security
- **Verification**: Email verification, token management
- **Profile**: User profile access and management

## 🏷️ Tag Categories

### Test Types

- `@smoke` - Critical functionality tests
- `@happy-path` - Main user flows
- `@negative` - Error scenarios and edge cases
- `@security` - Security-related tests

### Functional Areas

- `@registration` - User registration flows
- `@login` - Authentication flows
- `@verification` - Email verification flows
- `@profile` - Profile management
- `@validation` - Input validation scenarios
- `@authorization` - Access control scenarios

### Business Rules

- `@business-rules` - Business logic validation
- `@rate-limiting` - Rate limiting scenarios
- `@duplicate` - Duplicate data handling

## 🔗 Mapping to Tests

Each Gherkin scenario maps to corresponding Postman/Newman tests:

| Feature File | Postman Collection |
|-------------|-------------------|
| `registration.feature` | `registration-flow.json` |
| `login.feature` | `registration-flow.json` (login scenarios) |
| `verification.feature` | `registration-flow.json` (verification scenarios) |
| `profile.feature` | `registration-flow.json` (profile scenarios) |

## 📊 Current Coverage

### Registration Flow ✅

- [x] Happy path registration
- [x] Validation scenarios (missing fields, invalid email, weak password)
- [x] Business rules (personal email rejection, terms acceptance)
- [x] Security (SQL injection protection)
- [x] Duplicate email handling

### Login Flow ✅

- [x] Successful login
- [x] Invalid credentials
- [x] Unverified user login
- [x] Non-existent user

### Verification Flow ✅

- [x] Successful verification
- [x] Invalid/expired tokens
- [x] Missing token scenarios

### Profile Management ✅

- [x] Successful profile access
- [x] Authorization scenarios (no token, invalid token)
- [ ] Profile updates (future)

## 🚀 Usage

These feature files serve as:

1. **Living Documentation** - Always up-to-date requirements
2. **Test Planning** - Clear scenarios for test implementation
3. **Business Communication** - Readable by non-technical stakeholders
4. **Acceptance Criteria** - Definition of done for features

## 🔄 Maintenance

When adding new features:

1. Create new `.feature` files in appropriate folders
2. Use consistent tag naming conventions
3. Map scenarios to actual test implementations
4. Update this README with new coverage information
