@tenant
Feature: Tenant Analytics and Reporting
  As a platform administrator or tenant administrator
  I want to analyze tenant usage and performance in the Qeep financial monitoring system
  So that I can optimize operations and make data-driven decisions

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"
    And the tenant service is available
    And I have tenant header "x-tenant-code" set to "financial-corp"

  # =============================================================================
  # TENANT STATISTICS SCENARIOS
  # =============================================================================

  @tenant @analytics @statistics @happy-path @core
  Scenario: Retrieve Comprehensive Tenant Statistics
    Given I am authenticated as a platform administrator
    And a tenant exists with active usage data
    When I request tenant statistics with:
      | tenant_id     | tenant-123                  |
      | time_period   | last_month                  |
      | metrics       | ["users", "storage", "transactions", "cases", "alerts"] |
      | breakdown     | daily                       |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the statistics should include user activity metrics
    And the statistics should include storage utilization
    And the statistics should include transaction processing volumes
    And the statistics should include case management metrics
    And the statistics should include alert generation statistics
    And the response time should be reasonable

  @tenant @analytics @statistics @overview
  Scenario: Generate Tenant Overview Dashboard
    Given I am authenticated as a tenant administrator
    When I request tenant overview dashboard
    Then I should receive a 200 OK response
    And the dashboard should include key performance indicators
    And the dashboard should include current usage vs limits
    And the dashboard should include recent activity trends
    And the dashboard should include system health status
    And the dashboard should include upcoming renewals or limits

  @tenant @analytics @statistics @comparison
  Scenario: Compare Tenant Performance Metrics
    Given I am authenticated as a platform administrator
    And multiple tenants exist with usage data
    When I request tenant comparison with:
      | tenant_ids    | ["tenant-123", "tenant-456", "tenant-789"] |
      | metrics       | ["user_engagement", "case_resolution_time", "alert_accuracy"] |
      | time_period   | last_quarter                |
      | normalization | per_user                    |
    Then I should receive a 200 OK response
    And the comparison should include normalized metrics
    And the comparison should highlight performance leaders
    And the comparison should identify improvement opportunities
    And the comparison should include industry benchmarks

  # =============================================================================
  # USAGE ANALYTICS SCENARIOS
  # =============================================================================

  @tenant @analytics @usage @happy-path @core
  Scenario: Analyze Tenant Feature Usage Patterns
    Given I am authenticated as a tenant administrator
    When I request feature usage analytics with:
      | time_period   | last_six_months             |
      | features      | ["case_management", "alert_monitoring", "reporting", "api_access"] |
      | user_segments | ["analysts", "compliance", "administrators"] |
      | detail_level  | comprehensive               |
    Then I should receive a 200 OK response
    And the analytics should include feature adoption rates
    And the analytics should include usage frequency patterns
    And the analytics should include user segment analysis
    And the analytics should include feature correlation analysis
    And the analytics should include optimization recommendations

  @tenant @analytics @usage @resource-utilization
  Scenario: Monitor Resource Utilization Trends
    Given I am authenticated as a platform administrator
    When I request resource utilization analytics with:
      | tenant_id     | tenant-123                  |
      | resources     | ["cpu", "memory", "storage", "network", "database"] |
      | time_period   | last_month                  |
      | granularity   | hourly                      |
    Then I should receive a 200 OK response
    And the analytics should include resource consumption trends
    And the analytics should include peak usage periods
    And the analytics should include capacity planning insights
    And the analytics should include cost optimization opportunities
    And the analytics should include performance bottlenecks

  @tenant @analytics @usage @api-analytics
  Scenario: Analyze API Usage and Performance
    Given I am authenticated as a tenant administrator
    When I request API analytics with:
      | time_period   | last_month                  |
      | endpoints     | ["alerts", "cases", "users", "reports"] |
      | metrics       | ["call_volume", "response_time", "error_rate", "throughput"] |
      | breakdown     | by_endpoint                 |
    Then I should receive a 200 OK response
    And the analytics should include API call volumes
    And the analytics should include response time distributions
    And the analytics should include error rate analysis
    And the analytics should include throughput metrics
    And the analytics should include performance recommendations

  # =============================================================================
  # PERFORMANCE METRICS SCENARIOS
  # =============================================================================

  @tenant @analytics @performance @happy-path @core
  Scenario: Monitor Tenant System Performance
    Given I am authenticated as a platform administrator
    When I request performance metrics with:
      | tenant_id     | tenant-123                  |
      | metrics       | ["response_time", "availability", "throughput", "error_rate"] |
      | time_period   | last_week                   |
      | sla_comparison | true                       |
    Then I should receive a 200 OK response
    And the metrics should include system response times
    And the metrics should include availability percentages
    And the metrics should include throughput measurements
    And the metrics should include error rate analysis
    And the metrics should include SLA compliance status

  @tenant @analytics @performance @user-experience
  Scenario: Analyze User Experience Metrics
    Given I am authenticated as a tenant administrator
    When I request user experience analytics with:
      | metrics       | ["page_load_time", "session_duration", "bounce_rate", "task_completion"] |
      | user_segments | ["power_users", "regular_users", "new_users"] |
      | time_period   | last_month                  |
    Then I should receive a 200 OK response
    And the analytics should include page performance metrics
    And the analytics should include user engagement patterns
    And the analytics should include task completion rates
    And the analytics should include user satisfaction indicators
    And the analytics should include UX improvement recommendations

  @tenant @analytics @performance @business-metrics
  Scenario: Track Business Performance Indicators
    Given I am authenticated as a tenant administrator
    When I request business performance metrics with:
      | metrics       | ["case_resolution_time", "alert_accuracy", "false_positive_rate", "compliance_score"] |
      | time_period   | last_quarter                |
      | benchmarking  | industry_standards          |
    Then I should receive a 200 OK response
    And the metrics should include operational efficiency indicators
    And the metrics should include quality measurements
    And the metrics should include compliance performance
    And the metrics should include industry benchmark comparisons
    And the metrics should include improvement targets

  # =============================================================================
  # FINANCIAL ANALYTICS SCENARIOS
  # =============================================================================

  @tenant @analytics @financial @cost-analysis
  Scenario: Analyze Tenant Cost and Revenue Metrics
    Given I am authenticated as a platform administrator
    When I request financial analytics with:
      | tenant_id     | tenant-123                  |
      | metrics       | ["revenue", "costs", "margin", "usage_costs"] |
      | time_period   | last_year                   |
      | breakdown     | monthly                     |
    Then I should receive a 200 OK response
    And the analytics should include revenue trends
    And the analytics should include cost breakdowns
    And the analytics should include profit margin analysis
    And the analytics should include usage-based cost allocation
    And the analytics should include financial forecasting

  @tenant @analytics @financial @roi-analysis
  Scenario: Calculate Return on Investment Metrics
    Given I am authenticated as a tenant administrator
    When I request ROI analysis with:
      | investment_areas | ["platform_subscription", "training", "integration"] |
      | benefits         | ["efficiency_gains", "cost_savings", "risk_reduction"] |
      | time_period      | since_implementation        |
    Then I should receive a 200 OK response
    And the analysis should include investment costs
    And the analysis should include quantified benefits
    And the analysis should include ROI calculations
    And the analysis should include payback period
    And the analysis should include future value projections

  # =============================================================================
  # COMPLIANCE AND AUDIT ANALYTICS
  # =============================================================================

  @tenant @analytics @compliance @audit-metrics
  Scenario: Generate Compliance and Audit Analytics
    Given I am authenticated as a compliance officer
    When I request compliance analytics with:
      | compliance_areas | ["aml", "kyc", "sanctions", "data_protection"] |
      | metrics          | ["compliance_rate", "violation_count", "remediation_time"] |
      | time_period      | last_quarter                |
      | regulatory_focus | true                        |
    Then I should receive a 200 OK response
    And the analytics should include compliance performance
    And the analytics should include violation tracking
    And the analytics should include remediation effectiveness
    And the analytics should include regulatory readiness
    And the analytics should include audit trail completeness

  @tenant @analytics @compliance @risk-assessment
  Scenario: Perform Risk Assessment Analytics
    Given I am authenticated as a risk manager
    When I request risk assessment analytics with:
      | risk_categories  | ["operational", "compliance", "financial", "reputational"] |
      | assessment_period | last_six_months            |
      | risk_tolerance   | medium                     |
      | mitigation_tracking | true                    |
    Then I should receive a 200 OK response
    And the analytics should include risk exposure levels
    And the analytics should include risk trend analysis
    And the analytics should include mitigation effectiveness
    And the analytics should include risk appetite alignment
    And the analytics should include action recommendations

  # =============================================================================
  # PREDICTIVE ANALYTICS SCENARIOS
  # =============================================================================

  @tenant @analytics @predictive @usage-forecasting
  Scenario: Generate Usage Forecasting Analytics
    Given I am authenticated as a platform administrator
    When I request usage forecasting with:
      | forecast_period  | next_six_months             |
      | metrics          | ["user_growth", "storage_needs", "transaction_volume"] |
      | confidence_level | 95_percent                  |
      | seasonality      | true                        |
    Then I should receive a 200 OK response
    And the forecast should include predicted usage trends
    And the forecast should include confidence intervals
    And the forecast should include seasonal adjustments
    And the forecast should include capacity planning recommendations
    And the forecast should include cost projections

  @tenant @analytics @predictive @anomaly-detection
  Scenario: Detect Usage and Performance Anomalies
    Given I am authenticated as a platform administrator
    When I request anomaly detection analysis with:
      | detection_scope  | ["usage_patterns", "performance_metrics", "error_rates"] |
      | sensitivity      | medium                      |
      | time_window      | last_30_days                |
      | alert_threshold  | high                        |
    Then I should receive a 200 OK response
    And the analysis should identify usage anomalies
    And the analysis should detect performance deviations
    And the analysis should flag unusual patterns
    And the analysis should provide anomaly explanations
    And the analysis should suggest investigation priorities

  # =============================================================================
  # REPORTING AND DASHBOARDS SCENARIOS
  # =============================================================================

  @tenant @analytics @reporting @executive-reports
  Scenario: Generate Executive Summary Reports
    Given I am authenticated as an executive
    When I request executive reports with:
      | report_type      | monthly_summary             |
      | focus_areas      | ["performance", "growth", "compliance", "costs"] |
      | visualization    | charts_and_graphs           |
      | distribution     | ["email", "dashboard"]      |
    Then I should receive a 200 OK response
    And the report should include high-level KPIs
    And the report should include trend analysis
    And the report should include strategic insights
    And the report should include action recommendations
    And the report should be suitable for executive presentation

  @tenant @analytics @reporting @operational-dashboards
  Scenario: Create Operational Monitoring Dashboards
    Given I am authenticated as an operations manager
    When I configure operational dashboards with:
      | dashboard_type   | real_time_monitoring        |
      | metrics          | ["system_health", "user_activity", "alert_status"] |
      | refresh_interval | 30_seconds                  |
      | alert_integration | true                       |
    Then I should receive a 200 OK response
    And real-time dashboards should be configured
    And metrics should update automatically
    And alert integration should be enabled
    And drill-down capabilities should be available
    And mobile responsiveness should be ensured

  @tenant @analytics @reporting @custom-reports
  Scenario: Create Custom Analytics Reports
    Given I am authenticated as a business analyst
    When I create custom reports with:
      | report_name      | monthly_compliance_review   |
      | data_sources     | ["cases", "alerts", "users", "audit_logs"] |
      | filters          | {"date_range": "last_month", "status": "closed"} |
      | visualizations   | ["charts", "tables", "heatmaps"] |
      | schedule         | monthly                     |
    Then I should receive a 200 OK response
    And custom reports should be created
    And data sources should be connected
    And visualizations should be configured
    And automated scheduling should be enabled
    And report sharing should be available

  # =============================================================================
  # DATA EXPORT AND INTEGRATION SCENARIOS
  # =============================================================================

  @tenant @analytics @export @data-export
  Scenario: Export Analytics Data for External Analysis
    Given I am authenticated as a data analyst
    When I export analytics data with:
      | export_format    | ["csv", "json", "excel"]    |
      | data_sets        | ["usage_metrics", "performance_data", "compliance_reports"] |
      | time_range       | last_quarter                |
      | anonymization    | true                        |
    Then I should receive a 200 OK response
    And data should be exported in requested formats
    And sensitive data should be anonymized
    And export should include metadata
    And data integrity should be maintained
    And export audit trail should be created

  @tenant @analytics @integration @bi-tools
  Scenario: Integrate with Business Intelligence Tools
    Given I am authenticated as a BI administrator
    When I configure BI integration with:
      | bi_tool          | tableau                     |
      | connection_type  | api                         |
      | data_refresh     | daily                       |
      | security_model   | role_based                  |
    Then I should receive a 200 OK response
    And BI integration should be configured
    And data connections should be established
    And security permissions should be applied
    And automated refresh should be scheduled
    And data lineage should be documented

  # =============================================================================
  # ANALYTICS SECURITY AND COMPLIANCE
  # =============================================================================

  @tenant @analytics @security @data-privacy
  Scenario: Ensure Analytics Data Privacy and Security
    Given I am authenticated as a privacy officer
    When I review analytics data handling
    Then personal data should be properly anonymized
    And data access should be role-based
    And analytics queries should be logged
    And data retention policies should be enforced
    And privacy regulations should be complied with

  @tenant @analytics @compliance @audit-trail
  Scenario: Maintain Analytics Audit Trail
    Given I am authenticated as a compliance officer
    When I review analytics audit trails
    Then all analytics access should be logged
    And data modifications should be tracked
    And user activities should be recorded
    And audit logs should be tamper-evident
    And compliance reports should be generated automatically
