@tenant
Feature: 03 - Tenant Subscription Management
  As a platform administrator or tenant administrator
  I want to manage tenant subscriptions and billing in the Qeep financial monitoring system
  So that I can control access to features and track usage for financial institutions

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"
    And the tenant service is available
    And I have tenant header "x-tenant-code" set to "financial-corp"

  # =============================================================================
  # SUBSCRIPTION PLAN MANAGEMENT SCENARIOS
  # =============================================================================

  @tenant @subscription @plan @happy-path @core
  Scenario: Update Tenant Subscription Plan
    Given I am authenticated as a platform administrator
    And a tenant exists with current plan "BASIC"
    When I update the tenant subscription with:
      | tenant_id     | tenant-123                |
      | plan          | PREMIUM                   |
      | billing_cycle | YEARLY                    |
      | effective_date | 2024-07-01               |
      | reason        | Upgrade for advanced features |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the tenant plan should be updated to "PREMIUM"
    And the billing cycle should be set to "YEARLY"
    And premium features should be enabled
    And a subscription update event should be published
    And the tenant should be notified of the upgrade

  @tenant @subscription @plan @upgrade @enterprise
  Scenario: Upgrade Tenant to Enterprise Plan
    Given I am authenticated as a platform administrator
    And a tenant exists with current plan "PREMIUM"
    When I upgrade the tenant to enterprise with:
      | tenant_id     | tenant-456                |
      | plan          | ENTERPRISE                |
      | custom_limits | {"max_users": 1000, "max_storage_gb": 5000} |
      | dedicated_support | true                  |
      | sla_tier      | platinum                  |
    Then I should receive a 200 OK response
    And the tenant should be upgraded to "ENTERPRISE"
    And custom limits should be applied
    And dedicated support should be enabled
    And platinum SLA should be activated
    And enterprise features should be unlocked

  @tenant @subscription @plan @downgrade
  Scenario: Downgrade Tenant Subscription Plan
    Given I am authenticated as a platform administrator
    And a tenant exists with current plan "PREMIUM"
    When I downgrade the tenant subscription with:
      | tenant_id     | tenant-789                |
      | plan          | BASIC                     |
      | effective_date | 2024-08-01               |
      | grace_period_days | 30                    |
      | reason        | Cost optimization         |
    Then I should receive a 200 OK response
    And the tenant should be scheduled for downgrade
    And a grace period should be applied
    And premium features should be disabled after grace period
    And the tenant should be notified of the downgrade
    And data migration should be scheduled if needed

  @tenant @subscription @plan @validation @negative @invalid-plan
  Scenario: Update Subscription with Invalid Plan
    Given I am authenticated as a platform administrator
    And a tenant exists with current plan "BASIC"
    When I attempt to update subscription with invalid plan "INVALID_PLAN"
    Then I should receive a 400 Bad Request response
    And the error should mention invalid subscription plan
    And the current plan should remain unchanged

  # =============================================================================
  # BILLING CYCLE MANAGEMENT SCENARIOS
  # =============================================================================

  @tenant @subscription @billing @happy-path @core
  Scenario: Update Tenant Billing Cycle
    Given I am authenticated as a platform administrator
    And a tenant exists with billing cycle "MONTHLY"
    When I update the billing cycle with:
      | tenant_id     | tenant-123                |
      | billing_cycle | YEARLY                    |
      | prorate       | true                      |
      | next_billing_date | 2024-12-01           |
    Then I should receive a 200 OK response
    And the billing cycle should be updated to "YEARLY"
    And prorated billing should be calculated
    And the next billing date should be set
    And billing notifications should be updated

  @tenant @subscription @billing @proration
  Scenario: Handle Billing Proration for Mid-cycle Changes
    Given I am authenticated as a platform administrator
    And a tenant has an active monthly subscription
    And the tenant is 15 days into the billing cycle
    When I upgrade the tenant plan mid-cycle
    Then the billing should be prorated correctly
    And the tenant should be charged the difference
    And the next billing cycle should reflect the new plan
    And proration details should be documented

  @tenant @subscription @billing @payment-method
  Scenario: Update Tenant Payment Method
    Given I am authenticated as a tenant administrator
    When I update the payment method with:
      | payment_type  | credit_card               |
      | card_token    | encrypted_card_token      |
      | billing_address | {"country": "US", "zip": "10001"} |
      | auto_pay      | true                      |
    Then I should receive a 200 OK response
    And the payment method should be updated
    And auto-pay should be enabled
    And payment validation should be performed
    And billing notifications should be sent to new method

  # =============================================================================
  # FEATURE LIMITS MANAGEMENT SCENARIOS
  # =============================================================================

  @tenant @subscription @limits @happy-path @core
  Scenario: Configure Subscription-based Feature Limits
    Given I am authenticated as a platform administrator
    And a tenant has "PREMIUM" subscription
    When I configure feature limits with:
      | max_users             | 250                     |
      | max_storage_gb        | 500                     |
      | max_api_calls_monthly | 50000                   |
      | max_cases_monthly     | 5000                    |
      | advanced_analytics    | true                    |
    Then I should receive a 200 OK response
    And the feature limits should be applied
    And usage monitoring should be enabled
    And limit enforcement should be activated
    And overage alerts should be configured

  @tenant @subscription @limits @enforcement
  Scenario: Enforce Subscription Limits
    Given I am authenticated as a tenant administrator
    And my tenant has reached the user limit
    When I attempt to create a new user
    Then I should receive a 400 Bad Request response
    And the error should mention user limit exceeded
    And the error should suggest upgrading subscription
    And no new user should be created

  @tenant @subscription @limits @overage-handling
  Scenario: Handle Subscription Overage
    Given I am authenticated as a platform administrator
    And a tenant has exceeded storage limits
    When I check the tenant overage status
    Then I should receive overage details
    And overage charges should be calculated
    And the tenant should be notified of overage
    And options for limit increase should be provided

  # =============================================================================
  # USAGE TRACKING SCENARIOS
  # =============================================================================

  @tenant @subscription @usage @tracking @happy-path @core
  Scenario: Track Tenant Usage Metrics
    Given I am authenticated as a platform administrator
    And a tenant has active usage
    When I request usage metrics for the current billing period with:
      | tenant_id     | tenant-123                |
      | metrics       | ["users", "storage", "api_calls", "cases"] |
      | period        | current_month             |
    Then I should receive a 200 OK response
    And the response should include current usage
    And the response should include usage trends
    And the response should include limit comparisons
    And the response should include projected usage

  @tenant @subscription @usage @analytics
  Scenario: Generate Usage Analytics Report
    Given I am authenticated as a tenant administrator
    When I request usage analytics with:
      | time_period   | last_quarter              |
      | breakdown     | monthly                   |
      | include_costs | true                      |
    Then I should receive a 200 OK response
    And the report should include detailed usage breakdown
    And the report should include cost analysis
    And the report should include usage trends
    And the report should include optimization recommendations

  @tenant @subscription @usage @alerts
  Scenario: Configure Usage Alert Thresholds
    Given I am authenticated as a tenant administrator
    When I configure usage alerts with:
      | user_threshold_percent    | 80                      |
      | storage_threshold_percent | 85                      |
      | api_threshold_percent     | 90                      |
      | alert_frequency          | daily                   |
    Then I should receive a 200 OK response
    And usage alert thresholds should be configured
    And monitoring should be enabled
    And alert notifications should be set up
    And escalation rules should be applied

  # =============================================================================
  # SUBSCRIPTION LIFECYCLE SCENARIOS
  # =============================================================================

  @tenant @subscription @lifecycle @trial @happy-path
  Scenario: Start Tenant Trial Subscription
    Given I am authenticated as a platform administrator
    And a new tenant has been created
    When I start a trial subscription with:
      | tenant_id     | tenant-new-123            |
      | trial_plan    | PREMIUM                   |
      | trial_days    | 30                        |
      | auto_convert  | false                     |
    Then I should receive a 200 OK response
    And the trial subscription should be activated
    And premium features should be enabled
    And trial expiration should be scheduled
    And trial notifications should be configured

  @tenant @subscription @lifecycle @renewal
  Scenario: Handle Subscription Renewal
    Given I am authenticated as a platform administrator
    And a tenant subscription is approaching expiration
    When the subscription renewal process runs
    Then the subscription should be automatically renewed
    And payment should be processed
    And the new billing period should start
    And renewal confirmation should be sent
    And service continuity should be maintained

  @tenant @subscription @lifecycle @expiration
  Scenario: Handle Subscription Expiration
    Given I am authenticated as a platform administrator
    And a tenant subscription has expired
    When the subscription expiration process runs
    Then the tenant should be downgraded to free tier
    And premium features should be disabled
    And data access should be restricted
    And expiration notifications should be sent
    And grace period should be offered

  @tenant @subscription @lifecycle @cancellation
  Scenario: Process Subscription Cancellation
    Given I am authenticated as a platform administrator
    And a tenant requests subscription cancellation
    When I process the cancellation with:
      | tenant_id     | tenant-cancel-123         |
      | effective_date | 2024-12-31               |
      | reason        | Budget constraints        |
      | data_retention | 90_days                  |
    Then I should receive a 200 OK response
    And the subscription should be scheduled for cancellation
    And services should continue until effective date
    And data retention policy should be applied
    And cancellation confirmation should be sent

  # =============================================================================
  # BILLING AND INVOICING SCENARIOS
  # =============================================================================

  @tenant @subscription @billing @invoice-generation
  Scenario: Generate Subscription Invoice
    Given I am authenticated as a platform administrator
    And a tenant billing period has ended
    When I generate an invoice with:
      | tenant_id     | tenant-123                |
      | billing_period | 2024-06                  |
      | include_usage | true                      |
      | include_overages | true                   |
    Then I should receive a 200 OK response
    And the invoice should be generated
    And usage charges should be included
    And overage charges should be calculated
    And the invoice should be sent to the tenant

  @tenant @subscription @billing @payment-processing
  Scenario: Process Subscription Payment
    Given I am authenticated as a platform administrator
    And a tenant has an outstanding invoice
    When I process the payment with:
      | tenant_id     | tenant-123                |
      | invoice_id    | inv-2024-06-123           |
      | payment_method | auto_pay                 |
    Then I should receive a 200 OK response
    And the payment should be processed
    And the invoice should be marked as paid
    And payment confirmation should be sent
    And services should remain active

  @tenant @subscription @billing @failed-payment
  Scenario: Handle Failed Payment
    Given I am authenticated as a platform administrator
    And a tenant payment has failed
    When the failed payment process runs
    Then retry attempts should be scheduled
    And the tenant should be notified
    And grace period should be applied
    And service restrictions should be implemented if needed
    And dunning process should be initiated

  # =============================================================================
  # SUBSCRIPTION REPORTING SCENARIOS
  # =============================================================================

  @tenant @subscription @reporting @revenue-analytics
  Scenario: Generate Subscription Revenue Analytics
    Given I am authenticated as a platform administrator
    When I request revenue analytics with:
      | time_period   | last_year                 |
      | breakdown     | monthly                   |
      | include_forecasts | true                  |
    Then I should receive a 200 OK response
    And the report should include revenue trends
    And the report should include subscription distribution
    And the report should include churn analysis
    And the report should include growth forecasts

  @tenant @subscription @reporting @tenant-overview
  Scenario: Generate Tenant Subscription Overview
    Given I am authenticated as a platform administrator
    When I request subscription overview
    Then I should receive a 200 OK response
    And the overview should include active subscriptions
    And the overview should include trial conversions
    And the overview should include upgrade/downgrade trends
    And the overview should include revenue metrics

  # =============================================================================
  # SECURITY AND COMPLIANCE SCENARIOS
  # =============================================================================

  @tenant @subscription @security @payment-security
  Scenario: Ensure Payment Data Security
    Given I am authenticated as a tenant administrator
    When I handle payment information
    Then payment data should be encrypted
    And PCI compliance should be maintained
    And access should be properly logged
    And sensitive data should never be exposed in logs

  @tenant @subscription @compliance @audit-trail
  Scenario: Maintain Subscription Audit Trail
    Given I am authenticated as a platform administrator
    When I perform subscription operations
    Then all changes should be logged with timestamps
    And the audit log should include administrator who made changes
    And the audit log should include financial impact
    And the audit log should include business justification
    And the audit trail should support financial audits
