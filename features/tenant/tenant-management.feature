@tenant
Feature: Tenant Management
  As a platform administrator
  I want to manage tenant organizations in the Qeep financial monitoring system
  So that I can provision and maintain financial institutions using the platform

  Background:
    Given the API gateway is running at "http://localhost:8080/api/v1"
    And the tenant service is available

  # =============================================================================
  # TENANT CREATION SCENARIOS
  # =============================================================================

  @tenant @create @happy-path @core
  Scenario: Successful Creation of Financial Institution Tenant
    Given I am authenticated as a platform administrator
    When I create a new tenant with:
      | name     | First National Bank Corp        |
      | code     | first-national-bank             |
      | settings | {"theme": "light", "timezone": "America/New_York"} |
    Then I should receive a 201 Created response
    And the response should follow StandardApiResponse format
    And the tenant should be created with status "ACTIVE"
    And the tenant should have the specified name and code
    And the tenant should have default settings applied
    And a tenant creation event should be published
    And the response time should be reasonable

  @tenant @create @happy-path @with-settings
  Scenario: Create Tenant with Custom Financial Institution Settings
    Given I am authenticated as a platform administrator
    When I create a new tenant with:
      | name     | Global Investment Bank          |
      | code     | global-investment-bank          |
      | settings | {"currency": "USD", "language": "en", "features": {"advanced_analytics": true, "custom_branding": true}} |
    Then I should receive a 201 Created response
    And the tenant should be created with custom settings
    And the tenant should have advanced analytics enabled
    And the tenant should have custom branding enabled
    And the settings should be properly validated and stored

  @tenant @create @validation @negative @duplicate-code
  Scenario: Tenant Creation with Duplicate Code
    Given I am authenticated as a platform administrator
    And a tenant already exists with code "existing-bank"
    When I attempt to create a tenant with:
      | name | Another Bank Corp |
      | code | existing-bank     |
    Then I should receive a 409 Conflict response
    And the error should mention tenant code already exists
    And the error code should be "TENANT_CODE_EXISTS"
    And no duplicate tenant should be created

  @tenant @create @validation @negative @invalid-code
  Scenario Outline: Tenant Creation with Invalid Code Formats
    Given I am authenticated as a platform administrator
    When I attempt to create a tenant with invalid code "<code>"
    Then I should receive a 400 Bad Request response
    And the error should mention code validation

    Examples:
      | code                    |
      | invalid code with spaces |
      | invalid@code#symbols    |
      | 123-starting-with-number |
      | code.with.dots          |
      | code/with/slashes       |

  @tenant @create @validation @negative @missing-required-fields
  Scenario: Tenant Creation with Missing Required Fields
    Given I am authenticated as a platform administrator
    When I attempt to create a tenant without required fields:
      | name |                    |
      | code | missing-name-bank  |
    Then I should receive a 400 Bad Request response
    And the error should mention required fields
    And the error should specify which fields are missing

  @tenant @create @authorization @negative @insufficient-permissions
  Scenario: Tenant Creation without Platform Admin Permissions
    Given I am authenticated as a regular user
    When I attempt to create a new tenant
    Then I should receive a 403 Forbidden response
    And the error should mention insufficient permissions
    And no tenant should be created

  # =============================================================================
  # TENANT RETRIEVAL SCENARIOS
  # =============================================================================

  @tenant @get @happy-path @core
  Scenario: Successful Tenant Retrieval by ID
    Given I am authenticated as a platform administrator
    And a tenant exists with ID "tenant-123"
    When I request tenant information for ID "tenant-123"
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the response should contain complete tenant information
    And the response should include tenant ID
    And the response should include tenant name and code
    And the response should include tenant status
    And the response should include tenant settings
    And the response should include creation and update timestamps
    And the response time should be reasonable

  @tenant @get @happy-path @by-code
  Scenario: Successful Tenant Retrieval by Code
    Given I am authenticated as a platform administrator
    And a tenant exists with code "financial-corp"
    When I request tenant information by code "financial-corp"
    Then I should receive a 200 OK response
    And the response should contain the correct tenant information
    And the tenant code should match the requested code

  @tenant @get @validation @negative @non-existent-tenant
  Scenario: Get Non-existent Tenant
    Given I am authenticated as a platform administrator
    When I request tenant information for non-existent ID "non-existent-123"
    Then I should receive a 404 Not Found response
    And the error should mention tenant not found

  @tenant @get @authorization @tenant-admin
  Scenario: Tenant Admin Retrieving Own Tenant Information
    Given I am authenticated as a tenant administrator for "financial-corp"
    When I request my tenant information
    Then I should receive a 200 OK response
    And the response should contain my tenant information
    And I should not see other tenants' information

  # =============================================================================
  # TENANT UPDATE SCENARIOS
  # =============================================================================

  @tenant @update @happy-path @core
  Scenario: Successful Tenant Information Update
    Given I am authenticated as a platform administrator
    And a tenant exists with ID "tenant-123"
    When I update the tenant with:
      | name     | Updated Financial Institution |
      | settings | {"theme": "dark", "currency": "EUR"} |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the tenant information should be updated
    And the updated timestamp should be current
    And a tenant update event should be published
    And the response time should be reasonable

  @tenant @update @happy-path @settings-only
  Scenario: Update Tenant Settings Only
    Given I am authenticated as a platform administrator
    And a tenant exists with ID "tenant-456"
    When I update only the tenant settings with:
      | settings | {"notifications": {"email": true, "sms": false}, "features": {"api_access": true}} |
    Then I should receive a 200 OK response
    And only the settings should be updated
    And the tenant name should remain unchanged
    And the settings should be properly merged with existing settings

  @tenant @update @authorization @negative @insufficient-permissions
  Scenario: Tenant Update without Admin Permissions
    Given I am authenticated as a regular user
    And a tenant exists with ID "tenant-123"
    When I attempt to update the tenant information
    Then I should receive a 403 Forbidden response
    And the error should mention insufficient permissions
    And the tenant should remain unchanged

  @tenant @update @validation @negative @invalid-settings
  Scenario: Tenant Update with Invalid Settings Format
    Given I am authenticated as a platform administrator
    And a tenant exists with ID "tenant-123"
    When I attempt to update the tenant with invalid settings "invalid-json-string"
    Then I should receive a 400 Bad Request response
    And the error should mention invalid settings format
    And the tenant should remain unchanged

  # =============================================================================
  # TENANT STATUS MANAGEMENT SCENARIOS
  # =============================================================================

  @tenant @status @update @happy-path @core
  Scenario: Successful Tenant Status Update to Suspended
    Given I am authenticated as a platform administrator
    And a tenant exists with ID "tenant-123" and status "ACTIVE"
    When I update the tenant status with:
      | tenant_id | tenant-123                    |
      | status    | SUSPENDED                     |
      | reason    | Compliance investigation      |
    Then I should receive a 200 OK response
    And the tenant status should change to "SUSPENDED"
    And the status change should be logged with reason
    And tenant users should be notified of suspension
    And tenant access should be restricted
    And a tenant status change event should be published

  @tenant @status @update @happy-path @reactivation
  Scenario: Reactivate Suspended Tenant
    Given I am authenticated as a platform administrator
    And a tenant exists with ID "tenant-456" and status "SUSPENDED"
    When I update the tenant status with:
      | tenant_id | tenant-456                    |
      | status    | ACTIVE                        |
      | reason    | Investigation completed       |
    Then I should receive a 200 OK response
    And the tenant status should change to "ACTIVE"
    And tenant access should be restored
    And tenant users should be notified of reactivation
    And all tenant services should be available again

  @tenant @status @validation @negative @invalid-status
  Scenario: Tenant Status Update with Invalid Status
    Given I am authenticated as a platform administrator
    And a tenant exists with ID "tenant-123"
    When I attempt to update the tenant status to "INVALID_STATUS"
    Then I should receive a 400 Bad Request response
    And the error should mention invalid status value
    And the tenant status should remain unchanged

  @tenant @status @business-rules @status-transition
  Scenario: Validate Tenant Status Transition Rules
    Given I am authenticated as a platform administrator
    And a tenant exists with status "DELETED"
    When I attempt to update the tenant status to "ACTIVE"
    Then I should receive a 400 Bad Request response
    And the error should mention invalid status transition
    And the tenant status should remain "DELETED"

  # =============================================================================
  # TENANT DELETION SCENARIOS
  # =============================================================================

  @tenant @delete @soft-delete @happy-path @core
  Scenario: Successful Soft Delete of Tenant
    Given I am authenticated as a platform administrator
    And a tenant exists with ID "tenant-123"
    When I soft delete the tenant with:
      | tenant_id | tenant-123                    |
      | reason    | Client contract terminated    |
    Then I should receive a 200 OK response
    And the response should follow StandardApiResponse format
    And the tenant should be marked as deleted
    And the tenant should have a deletion timestamp
    And the tenant should not appear in active tenant lists
    And tenant data should be retained for audit purposes
    And a tenant deletion event should be published

  @tenant @delete @authorization @negative @insufficient-permissions
  Scenario: Tenant Deletion without Platform Admin Permissions
    Given I am authenticated as a tenant administrator
    And a tenant exists with ID "tenant-123"
    When I attempt to delete the tenant
    Then I should receive a 403 Forbidden response
    And the error should mention insufficient permissions
    And the tenant should remain active

  @tenant @delete @business-rules @active-users
  Scenario: Prevent Deletion of Tenant with Active Users
    Given I am authenticated as a platform administrator
    And a tenant exists with ID "tenant-123"
    And the tenant has active users
    When I attempt to delete the tenant
    Then I should receive a 400 Bad Request response
    And the error should mention active users exist
    And the error should suggest deactivating users first
    And the tenant should remain active

  # =============================================================================
  # TENANT LISTING AND SEARCH SCENARIOS
  # =============================================================================

  @tenant @list @happy-path @core
  Scenario: List All Tenants with Pagination
    Given I am authenticated as a platform administrator
    And multiple tenants exist in the system
    When I request the tenant list with:
      | page  | 1  |
      | limit | 10 |
    Then I should receive a 200 OK response
    And the response should include a list of tenants
    And the response should include pagination information
    And each tenant should include basic information
    And the response should be properly paginated

  @tenant @list @search @happy-path
  Scenario: Search Tenants by Name and Code
    Given I am authenticated as a platform administrator
    And tenants exist with various names and codes
    When I search for tenants with:
      | search | "financial" |
    Then I should receive a 200 OK response
    And the response should include matching tenants
    And the search should match both names and codes
    And the results should be relevant to the search term

  @tenant @list @filter @status
  Scenario: Filter Tenants by Status
    Given I am authenticated as a platform administrator
    And tenants exist with different statuses
    When I filter tenants with:
      | status | ACTIVE |
    Then I should receive a 200 OK response
    And the response should include only active tenants
    And suspended or deleted tenants should not be included

  # =============================================================================
  # TENANT VALIDATION SCENARIOS
  # =============================================================================

  @tenant @validation @code-availability
  Scenario: Check Tenant Code Availability
    Given I am authenticated as a platform administrator
    When I check if tenant code "new-bank-code" is available
    Then I should receive a 200 OK response
    And the response should indicate if the code is available
    And the response should include validation status

  @tenant @validation @code-format
  Scenario: Validate Tenant Code Format
    Given I am authenticated as a platform administrator
    When I validate tenant code format for "valid-bank-123"
    Then I should receive a 200 OK response
    And the response should confirm the code format is valid
    And the response should include format validation details

  # =============================================================================
  # SECURITY AND COMPLIANCE SCENARIOS
  # =============================================================================

  @tenant @security @data-isolation
  Scenario: Ensure Tenant Data Isolation
    Given I am authenticated as a platform administrator
    And multiple tenants exist with data
    When I retrieve tenant information
    Then each tenant's data should be completely isolated
    And no cross-tenant data should be accessible
    And tenant boundaries should be strictly enforced

  @tenant @compliance @audit-trail
  Scenario: Maintain Complete Tenant Management Audit Trail
    Given I am authenticated as a platform administrator
    When I perform tenant management operations
    Then all tenant changes should be logged with timestamps
    And the audit log should include administrator who made changes
    And the audit log should include business justification
    And the audit log should include before and after states
    And the audit trail should be immutable and tamper-evident
