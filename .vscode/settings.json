{
  "files.exclude": {
    "**/*-e2e": true
  },
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.expand": false,
  "explorer.fileNesting.patterns": {
    // "*.ts": "${capture}.js",
    // "*.js": "${capture}.js.map, ${capture}.min.js, ${capture}.d.ts",
    // "*.jsx": "${capture}.js",
    // "*.tsx": "${capture}.ts",
    // "tsconfig.json": "tsconfig.*.json",
    // "package.json": "package-lock.json, yarn.lock, pnpm-lock.yaml",
    // "*.component.ts": "${capture}.component.html, ${capture}.component.scss, ${capture}.component.css, ${capture}.component.spec.ts",
    // "*.service.ts": "${capture}.service.spec.ts",
    // "*.controller.ts": "${capture}.controller.spec.ts",
    // "*.module.ts": "${capture}.module.spec.ts"
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  // "typescript.suggest.autoImports": true,
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit"
  },
  "eslint.workingDirectories": ["apps", "libs"],
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.nx": true,
    "**/*-e2e": true
  },
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/dist/**": true,
    "**/.nx/**": true,
    "**/*-e2e/**": true
  },
  "cSpell.words": [
    "absa",
    "AMLDTOs",
    "approvable",
    "axllent",
    "bufbuild",
    "bullmq",
    "businesscorp",
    "Commentable",
    "cooldown",
    "cuid",
    "dbgenerated",
    "DTOs",
    "ecobank",
    "Expirable",
    "FINTECH",
    "Geolocated",
    "gitbook",
    "healthcheck",
    "honkit",
    "IAML",
    "kafkajs",
    "keepalive",
    "lookback",
    "MAILPIT",
    "mobi",
    "momo",
    "msword",
    "nanos",
    "NCUA",
    "nodelay",
    "noindex",
    "nosniff",
    "OFAC",
    "officedocument",
    "oneofs",
    "openxmlformats",
    "otpauth",
    "paralleldrive",
    "protonmail",
    "qeep",
    "qrcode",
    "ronna",
    "SAMEORIGIN",
    "setex",
    "signups",
    "simplewebauthn",
    "spreadsheetml",
    "superadmin",
    "superadmins",
    "Timestamptz",
    "TOTP",
    "uuidv",
    "validatable",
    "Watchlist",
    "WEBAUTHN",
    "whsec",
    "wordprocessingml"
  ]
}
