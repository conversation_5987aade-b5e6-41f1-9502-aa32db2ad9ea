# =============================================================================
# BDD TEST ENVIRONMENT CONFIGURATION
# =============================================================================
# This file contains environment variables specifically for BDD (Behavior Driven Development) tests
# It ensures isolated, reliable, and fast test execution

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
NODE_ENV=bdd
LOG_LEVEL=warn
BDD_TEST_MODE=true

# =============================================================================
# BDD TEST CONFIGURATION
# =============================================================================
# Test execution settings
BDD_TEST_TIMEOUT=30000
BDD_MAX_RETRIES=2
BDD_PARALLEL_EXECUTION=false
BDD_CLEANUP_AFTER_TESTS=true
BDD_VERBOSE_LOGGING=false

# Test data management
BDD_USE_TRANSACTIONS=true
BDD_SEED_TEST_DATA=true
BDD_CLEANUP_STRATEGY=truncate
BDD_UNIQUE_EMAIL_SUFFIX=bdd

# Service behavior
BDD_MOCK_EXTERNAL_SERVICES=true
BDD_DISABLE_RATE_LIMITING=true
BDD_SYNC_PROCESSING=true
BDD_EXTENDED_TIMEOUTS=true

# Debugging and monitoring
BDD_CAPTURE_REQUESTS=true
BDD_SAVE_FAILED_SCENARIOS=true
BDD_ENABLE_PERFORMANCE_MONITORING=false

# =============================================================================
# DATABASE CONFIGURATION (BDD Test Database)
# =============================================================================
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=qeep_bdd
POSTGRES_PASSWORD=qeep_bdd_password
POSTGRES_DB=qeep_bdd

# Database URLs for all services (BDD Test Environment)
DATABASE_URL=postgresql://qeep_bdd:qeep_bdd_password@localhost:5432/qeep_bdd?schema=public
AUTH_DATABASE_URL=postgresql://qeep_bdd:qeep_bdd_password@localhost:5432/qeep_bdd?schema=auth_service
USER_DATABASE_URL=postgresql://qeep_bdd:qeep_bdd_password@localhost:5432/qeep_bdd?schema=user_service
TENANT_DATABASE_URL=postgresql://qeep_bdd:qeep_bdd_password@localhost:5432/qeep_bdd?schema=tenant_service
NOTIFICATION_DATABASE_URL=postgresql://qeep_bdd:qeep_bdd_password@localhost:5432/qeep_bdd?schema=notification_service
AUDIT_DATABASE_URL=postgresql://qeep_bdd:qeep_bdd_password@localhost:5432/qeep_bdd?schema=audit_service

# Database behavior for BDD tests
DB_LOGGING=false
DB_SYNCHRONIZE=false
DB_DROP_SCHEMA=false
DB_MIGRATIONS_RUN=true

# =============================================================================
# REDIS CONFIGURATION (BDD Test Redis)
# =============================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=qeep_bdd_redis_password
REDIS_DB=2
REDIS_URL=redis://:qeep_bdd_redis_password@localhost:6379/2

# Redis behavior for BDD tests
REDIS_KEY_PREFIX=bdd:test:
REDIS_DEFAULT_TTL=300
REDIS_CLEANUP_ON_START=true

# =============================================================================
# JWT CONFIGURATION (BDD Test Settings)
# =============================================================================
JWT_SECRET=bdd_test_jwt_secret_key_for_testing_only
JWT_REFRESH_SECRET=bdd_test_jwt_refresh_secret_key_for_testing_only
JWT_EXPIRES_IN=2h
JWT_REFRESH_EXPIRES_IN=24h

# JWT validation settings for BDD tests
JWT_CLOCK_TOLERANCE=300
JWT_IGNORE_EXPIRATION=false
JWT_IGNORE_NOT_BEFORE=true
JWT_REQUIRE_AUDIENCE=false
JWT_REQUIRE_ISSUER=false

# =============================================================================
# APPLICATION PORTS (BDD Test Environment)
# =============================================================================
# API Gateway (Main entry point)
API_GATEWAY_PORT=3000
API_GATEWAY_HOST=0.0.0.0

# Core Platform Services (HTTP)
AUTH_SERVICE_PORT=3001
AUTH_SERVICE_HOST=0.0.0.0

USER_SERVICE_PORT=3002
USER_SERVICE_HOST=0.0.0.0

TENANT_SERVICE_PORT=3003
TENANT_SERVICE_HOST=0.0.0.0

NOTIFICATION_SERVICE_PORT=3004
NOTIFICATION_SERVICE_HOST=0.0.0.0

AUDIT_SERVICE_PORT=3005
AUDIT_SERVICE_HOST=0.0.0.0

# gRPC Service Ports (BDD Test Environment)
AUTH_SERVICE_GRPC_PORT=3012
USER_SERVICE_GRPC_PORT=3013
TENANT_SERVICE_GRPC_PORT=3014
NOTIFICATION_SERVICE_GRPC_PORT=3015
AUDIT_SERVICE_GRPC_PORT=3016

# =============================================================================
# SERVICE URLS (BDD Test Environment)
# =============================================================================
# HTTP Service URLs
AUTH_SERVICE_URL=http://localhost:3001
USER_SERVICE_URL=http://localhost:3002
TENANT_SERVICE_URL=http://localhost:3003
NOTIFICATION_SERVICE_URL=http://localhost:3004
AUDIT_SERVICE_URL=http://localhost:3005

# gRPC Service URLs (BDD Test Environment)
AUTH_SERVICE_GRPC_URL=localhost:3012
USER_SERVICE_GRPC_URL=localhost:3013
TENANT_SERVICE_GRPC_URL=localhost:3014
NOTIFICATION_SERVICE_GRPC_URL=localhost:3015
AUDIT_SERVICE_GRPC_URL=localhost:3016

# =============================================================================
# EXTERNAL SERVICE MOCKING (BDD Tests)
# =============================================================================
# Email Provider Configuration (Mocked for BDD)
DEFAULT_EMAIL_PROVIDER=resend
EMAIL_TIMEOUT=30000
EMAIL_RETRY_ATTEMPTS=3
EMAIL_RATE_LIMIT=100000
EMAIL_DAILY_LIMIT=100000

# Email services (mocked)
DISABLE_EMAIL_SENDING=true
MOCK_EMAIL_PROVIDER=true
EMAIL_CAPTURE_MODE=true

# Resend Configuration (Mocked for BDD)
RESEND_API_KEY=mock_resend_api_key_for_bdd_tests
RESEND_FROM_EMAIL=<EMAIL>
RESEND_FROM_NAME=Qeep BDD Test
RESEND_WEBHOOK_SECRET=mock_resend_webhook_secret_for_bdd_tests

# SendGrid Configuration (Mocked for BDD)
SENDGRID_API_KEY=mock_sendgrid_api_key_for_bdd_tests
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=Qeep BDD Test
SENDGRID_WEBHOOK_SECRET=mock_sendgrid_webhook_secret_for_bdd_tests

# Mailgun Configuration (Mocked for BDD)
MAILGUN_API_KEY=mock_mailgun_api_key_for_bdd_tests
MAILGUN_DOMAIN=bdd-test.qeep.com
MAILGUN_FROM_EMAIL=<EMAIL>
MAILGUN_FROM_NAME=Qeep BDD Test
MAILGUN_BASE_URL=https://api.mailgun.net
MAILGUN_WEBHOOK_SECRET=mock_mailgun_webhook_secret_for_bdd_tests

# SMS Provider Configuration (Mocked for BDD)
DEFAULT_SMS_PROVIDER=twilio
SMS_TIMEOUT=30000
SMS_RETRY_ATTEMPTS=3
SMS_RATE_LIMIT=1000
SMS_DAILY_LIMIT=10000

# SMS services (mocked)
DISABLE_SMS_SENDING=true
MOCK_SMS_PROVIDER=true
SMS_CAPTURE_MODE=true

# Twilio Configuration (Mocked for BDD)
TWILIO_ACCOUNT_SID=mock_twilio_account_sid_for_bdd_tests
TWILIO_AUTH_TOKEN=mock_twilio_auth_token_for_bdd_tests
TWILIO_FROM_NUMBER=+**********
TWILIO_WEBHOOK_SECRET=mock_twilio_webhook_secret_for_bdd_tests

# Auth0 mocking
MOCK_AUTH0=true
AUTH0_DOMAIN=bdd-test.us.auth0.com
AUTH0_CLIENT_ID=mock_auth0_client_id_for_bdd_tests
AUTH0_CLIENT_SECRET=mock_auth0_client_secret_for_bdd_tests
AUTH0_AUDIENCE=https://api.bdd-test.qeep.com

# Other external services
DISABLE_KAFKA_EVENTS=true
DISABLE_TELEMETRY=true
DISABLE_WEBHOOKS=true
MOCK_FILE_STORAGE=true

# =============================================================================
# NGINX PROXY CONFIGURATION (BDD Test Environment)
# =============================================================================
# Nginx Proxy Settings (for BDD tests)
NGINX_ENABLED=true
NGINX_PORT=8080
NGINX_HTTPS_PORT=8443
NGINX_LOG_LEVEL=error
NGINX_PROXY_URL=http://localhost:8080

# Nginx Upstream Configuration (BDD Test Environment)
NGINX_UPSTREAM_API_GATEWAY=host.orb.internal:3000
NGINX_UPSTREAM_AUTH_SERVICE=host.orb.internal:3001
NGINX_UPSTREAM_USER_SERVICE=host.orb.internal:3002
NGINX_UPSTREAM_TENANT_SERVICE=host.orb.internal:3003
NGINX_UPSTREAM_NOTIFICATION_SERVICE=host.orb.internal:3004
NGINX_UPSTREAM_AUDIT_SERVICE=host.orb.internal:3005

# =============================================================================
# KAFKA CONFIGURATION (Mocked for BDD Tests)
# =============================================================================
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=qeep-bdd-test
KAFKA_GROUP_ID=qeep-bdd-test-group

# Kafka Topics (BDD Test Environment)
KAFKA_TOPIC_USER_EVENTS=bdd.user.events
KAFKA_TOPIC_AUTH_EVENTS=bdd.auth.events
KAFKA_TOPIC_TENANT_EVENTS=bdd.tenant.events
KAFKA_TOPIC_NOTIFICATION_EVENTS=bdd.notification.events
KAFKA_TOPIC_AUDIT_EVENTS=bdd.audit.events

# =============================================================================
# SMTP CONFIGURATION (Mocked for BDD Tests)
# =============================================================================
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=<EMAIL>

# =============================================================================
# TEMPLATE CONFIGURATION (BDD Test Environment)
# =============================================================================
TEMPLATE_CACHE_SIZE=100
TEMPLATE_CACHE_TTL=300

# =============================================================================
# SECURITY CONFIGURATION (BDD Test Overrides)
# =============================================================================
# Rate limiting (disabled for BDD tests)
RATE_LIMITING_ENABLED=false
API_RATE_LIMIT=100000
API_TIMEOUT=30000

# =============================================================================
# RATE LIMITING CONFIGURATION (Disabled for BDD Tests)
# =============================================================================
# Rate Limiting Feature Toggle
RATE_LIMITING_ENABLED=false

# Redis Configuration for Rate Limiting (BDD Test Environment)
RATE_LIMITING_REDIS_HOST=localhost
RATE_LIMITING_REDIS_PORT=6379
RATE_LIMITING_REDIS_PASSWORD=qeep_bdd_redis_password
RATE_LIMITING_REDIS_DB=3
RATE_LIMITING_REDIS_KEY_PREFIX=bdd:rate-limit

# Rate Limiting Rules (very high limits for BDD tests)
RATE_LIMIT_PER_USER=100000
RATE_LIMIT_PER_TENANT=100000
RATE_LIMIT_PER_IP=100000
RATE_LIMIT_PER_ENDPOINT=100000

# Rate Limiting Time Windows (seconds)
RATE_LIMIT_TTL_USER=60
RATE_LIMIT_TTL_TENANT=60
RATE_LIMIT_TTL_IP=60
RATE_LIMIT_TTL_ENDPOINT=60

# Block Duration (seconds) - minimal for BDD tests
RATE_LIMIT_BLOCK_DURATION_USER=1
RATE_LIMIT_BLOCK_DURATION_TENANT=1
RATE_LIMIT_BLOCK_DURATION_IP=1
RATE_LIMIT_BLOCK_DURATION_ENDPOINT=1

# Rate Limiting Features (disabled for BDD tests)
RATE_LIMITING_ENABLE_METRICS=false
RATE_LIMITING_ENABLE_LOGGING=false

# Custom Rate Limits for BDD Testing (very high limits)
RATE_LIMIT_HEALTH_CHECK=10000
RATE_LIMIT_HEALTH_CHECK_TTL=60
RATE_LIMIT_HEALTH_CHECK_BLOCK=1

RATE_LIMIT_K8S_READINESS=10000
RATE_LIMIT_K8S_READINESS_TTL=60
RATE_LIMIT_K8S_READINESS_BLOCK=1

RATE_LIMIT_K8S_LIVENESS=10000
RATE_LIMIT_K8S_LIVENESS_TTL=60
RATE_LIMIT_K8S_LIVENESS_BLOCK=1

RATE_LIMIT_DETAILED_HEALTH=10000
RATE_LIMIT_DETAILED_HEALTH_TTL=60
RATE_LIMIT_DETAILED_HEALTH_BLOCK=1

# =============================================================================
# SECURITY HEADERS CONFIGURATION (Disabled for BDD Tests)
# =============================================================================
# HSTS (HTTP Strict Transport Security) - disabled for BDD tests
SECURITY_HEADERS_HSTS_ENABLED=false
SECURITY_HEADERS_HSTS_MAX_AGE=0
SECURITY_HEADERS_HSTS_INCLUDE_SUBDOMAINS=false
SECURITY_HEADERS_HSTS_PRELOAD=false

# Content Security Policy - disabled for BDD tests
SECURITY_HEADERS_CSP_ENABLED=false
SECURITY_HEADERS_CSP_REPORT_ONLY=false
SECURITY_HEADERS_CSP_DEFAULT_SRC='self'
SECURITY_HEADERS_CSP_SCRIPT_SRC="'self','unsafe-inline','unsafe-eval'"
SECURITY_HEADERS_CSP_STYLE_SRC="'self','unsafe-inline'"
SECURITY_HEADERS_CSP_IMG_SRC="'self',data:,https:,http:"
SECURITY_HEADERS_CSP_CONNECT_SRC="'self',localhost:*,127.0.0.1:*"
SECURITY_HEADERS_CSP_FONT_SRC="'self',data:"
SECURITY_HEADERS_CSP_OBJECT_SRC='none'
SECURITY_HEADERS_CSP_UPGRADE_INSECURE_REQUESTS=false
SECURITY_HEADERS_CSP_BLOCK_MIXED_CONTENT=false

# Frame Options - disabled for BDD tests
SECURITY_HEADERS_FRAME_OPTIONS_ENABLED=false
SECURITY_HEADERS_FRAME_OPTIONS_POLICY=DENY

# Content Type Options - disabled for BDD tests
SECURITY_HEADERS_CONTENT_TYPE_OPTIONS_ENABLED=false
SECURITY_HEADERS_CONTENT_TYPE_OPTIONS_NOSNIFF=false

# XSS Protection - disabled for BDD tests
SECURITY_HEADERS_XSS_PROTECTION_ENABLED=false
SECURITY_HEADERS_XSS_PROTECTION_MODE=0

# Referrer Policy - disabled for BDD tests
SECURITY_HEADERS_REFERRER_POLICY_ENABLED=false
SECURITY_HEADERS_REFERRER_POLICY=no-referrer

# Permissions Policy - disabled for BDD tests
SECURITY_HEADERS_PERMISSIONS_POLICY_ENABLED=false

# Cross-Origin Policies - disabled for BDD tests
SECURITY_HEADERS_COEP_ENABLED=false
SECURITY_HEADERS_COEP_POLICY=unsafe-none
SECURITY_HEADERS_COOP_ENABLED=false
SECURITY_HEADERS_COOP_POLICY=unsafe-none
SECURITY_HEADERS_CORP_ENABLED=false
SECURITY_HEADERS_CORP_POLICY=cross-origin

# Legacy security headers (for backward compatibility)
SECURITY_HEADERS_ENABLED=false
CSP_ENABLED=false
HSTS_ENABLED=false

# CORS Configuration (permissive for BDD tests)
CORS_ORIGIN=*
CORS_CREDENTIALS=true
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH
CORS_HEADERS=*

# =============================================================================
# FEATURE FLAGS (BDD Test Configuration)
# =============================================================================
# Core features
ENABLE_USER_REGISTRATION=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_PASSWORD_RESET=true
ENABLE_MFA=false

# Development/Testing features
ENABLE_SWAGGER=true
ENABLE_METRICS=false
ENABLE_TRACING=false
ENABLE_RATE_LIMITING=false
ENABLE_CORS=true

# Advanced features (simplified for BDD tests)
ENABLE_AUDIT_LOGGING=false
ENABLE_DETAILED_LOGGING=false
ENABLE_PERFORMANCE_MONITORING=false
ENABLE_CIRCUIT_BREAKER=false

# =============================================================================
# TENANT INTERCEPTOR CONFIGURATION (BDD Test Environment)
# =============================================================================
# Enable/disable tenant interceptor globally
TENANT_INTERCEPTOR_ENABLED=false

# Whether to validate tenant against tenant service
TENANT_INTERCEPTOR_VALIDATE=false

# Whether to require tenant for all requests (can be overridden per endpoint)
TENANT_INTERCEPTOR_REQUIRE=false

# Enable tenant information caching
TENANT_INTERCEPTOR_CACHE=false

# Cache TTL in seconds (default: 300 = 5 minutes)
TENANT_INTERCEPTOR_CACHE_TTL=60

# Header names for tenant extraction
TENANT_CODE_HEADER=x-tenant-code
TENANT_ID_HEADER=x-tenant-id

# Query parameter names for tenant extraction
TENANT_CODE_QUERY_PARAM=tenant_code
TENANT_ID_QUERY_PARAM=tenant_id

# JWT claim names for tenant extraction
JWT_TENANT_ID_CLAIM=tenant_id
JWT_TENANT_CODE_CLAIM=tenant_code

# Enable debug logging for tenant interceptor
TENANT_INTERCEPTOR_DEBUG=false

# =============================================================================
# CIRCUIT BREAKER CONFIGURATION (Disabled for BDD Tests)
# =============================================================================
# Circuit Breaker Configuration (disabled for BDD tests)
CIRCUIT_BREAKER_ENABLED=false
CIRCUIT_BREAKER_FAILURE_THRESHOLD=100
CIRCUIT_BREAKER_TIMEOUT=60000
CIRCUIT_BREAKER_REQUEST_VOLUME_THRESHOLD=50
CIRCUIT_BREAKER_SLEEP_WINDOW=60000
CIRCUIT_BREAKER_MAX_RETRIES=1
CIRCUIT_BREAKER_ENABLE_METRICS=false
CIRCUIT_BREAKER_ENABLE_LOGGING=false

# Service-specific Circuit Breaker Settings (all disabled for BDD)
CIRCUIT_BREAKER_AUTH_ENABLED=false
CIRCUIT_BREAKER_AUTH_FAILURE_THRESHOLD=100
CIRCUIT_BREAKER_AUTH_TIMEOUT=60000

CIRCUIT_BREAKER_USER_ENABLED=false
CIRCUIT_BREAKER_USER_FAILURE_THRESHOLD=100
CIRCUIT_BREAKER_USER_TIMEOUT=60000

CIRCUIT_BREAKER_TENANT_ENABLED=false
CIRCUIT_BREAKER_TENANT_FAILURE_THRESHOLD=100
CIRCUIT_BREAKER_TENANT_TIMEOUT=60000

CIRCUIT_BREAKER_NOTIFICATION_ENABLED=false
CIRCUIT_BREAKER_NOTIFICATION_FAILURE_THRESHOLD=100
CIRCUIT_BREAKER_NOTIFICATION_TIMEOUT=60000

# =============================================================================
# APPLICATION URLS (BDD Test Environment)
# =============================================================================
FRONTEND_URL=http://localhost:3000
API_BASE_URL=http://localhost:8080/api/v1
APP_URL=https://bdd-test.qeep.com
SUPPORT_EMAIL=<EMAIL>

# =============================================================================
# MONITORING & OBSERVABILITY (BDD Test Settings)
# =============================================================================
# Telemetry (disabled for BDD tests)
TELEMETRY_ENABLED=false
TRACING_ENABLED=false
METRICS_ENABLED=false

# Prometheus (disabled for BDD tests)
PROMETHEUS_ENABLED=false
PROMETHEUS_PORT=9090
PROMETHEUS_HOST=localhost

# Jaeger Tracing (disabled for BDD tests)
JAEGER_ENABLED=false
JAEGER_ENDPOINT=http://localhost:14268/api/traces
JAEGER_SERVICE_NAME=qeep-bdd-test

# =============================================================================
# DEVELOPMENT TOOLS (BDD Test Environment)
# =============================================================================
# Hot reload (disabled for BDD tests)
WATCH_MODE=false
HOT_RELOAD=false

# Debug configuration (minimal for BDD tests)
DEBUG_ENABLED=false
DEBUG_PORT=9229
DEBUG_HOST=localhost

# =============================================================================
# TEST UTILITIES
# =============================================================================
# Test data generation
TEST_DATA_SEED=12345
TEST_USER_PASSWORD=TestPassword123!
TEST_ADMIN_EMAIL=<EMAIL>

# Test execution
TEST_PARALLEL_WORKERS=1
TEST_RETRY_DELAY=1000
TEST_CLEANUP_TIMEOUT=5000

# Test reporting
TEST_REPORT_FORMAT=json
TEST_REPORT_PATH=./reports/bdd
TEST_SCREENSHOT_ON_FAILURE=false

# =============================================================================
# ENVIRONMENT VALIDATION
# =============================================================================
# These variables help validate that BDD test environment is properly configured
BDD_ENV_VALIDATION=true
BDD_CONFIG_VERSION=1.0.0
BDD_LAST_UPDATED=2025-01-11
