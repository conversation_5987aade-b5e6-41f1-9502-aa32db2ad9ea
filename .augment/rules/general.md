---
type: 'always_apply'
---

# Guidelines

## Preferences

- User prefers that all database seeding and infrastructure setup should be completed with one command for streamlined workflow.

## Code Organization

- User prefers organizing contract code by separating DTOs, interfaces, and schemas into dedicated files with a main namespace file serving as a barrel export to maintain API compatibility.
- User prefers organizing shared contracts with individual files per interface/DTO/schema group rather than consolidated files, using barrel exports and maintaining separate subdirectories for better organization and maintainability.
