// Export the common module
export * from './lib/common.module';

// Export pipes
export * from './lib/pipes';

// Export tenant functionality
export * from './lib/tenant/decorators/tenant.decorator';
export * from './lib/tenant/interceptors/tenant.interceptor';
export * from './lib/tenant/interfaces/tenant.interface';
// export * from './lib/tenant/services/tenant.service';

// Export user functionality
export * from './lib/user/interfaces/user.interface';
// export * from './lib/user/services/user.service';

// Export circuit breaker functionality
export * from './lib/circuit-breaker/circuit-breaker-config.service';
export * from './lib/circuit-breaker/circuit-breaker-health.service';
export * from './lib/circuit-breaker/circuit-breaker-init.service';
export * from './lib/circuit-breaker/circuit-breaker.constants';
export * from './lib/circuit-breaker/circuit-breaker.module';
export * from './lib/circuit-breaker/circuit-breaker.service';
export * from './lib/circuit-breaker/decorators/circuit-breaker.decorator';
export * from './lib/circuit-breaker/interceptors/circuit-breaker.interceptor';
export * from './lib/circuit-breaker/interfaces/circuit-breaker-options.interface';

// Export config functionality
export * from './lib/config/config.module';
export * from './lib/config/config.service';
export * from './lib/config/environment.config';

// Export rate limiting functionality
export {
  RateLimitConfig,
  RateLimitContext,
  RateLimitingModuleOptions,
  RedisConfig as RateLimitingRedisConfig,
  RateLimitMetrics,
  RateLimitResult,
  ThrottlerStorageRecord,
} from './lib/rate-limiting/interfaces/rate-limiting-options.interface';
export * from './lib/rate-limiting/rate-limiting-config.service';
export * from './lib/rate-limiting/rate-limiting.constants';
export * from './lib/rate-limiting/rate-limiting.guard';
export * from './lib/rate-limiting/rate-limiting.module';
export * from './lib/rate-limiting/rate-limiting.service';
export * from './lib/rate-limiting/rate-limiting.storage';

// Export rate limiting decorators
export { RateLimit, SkipRateLimit } from './lib/rate-limiting/rate-limiting.guard';

// Export security headers functionality
export * from './lib/security-headers/config/security-headers.config';
export * from './lib/security-headers/interfaces/security-headers-health.interface';
export {
  SecurityHeadersMetrics,
  SecurityHeadersConfig as SecurityHeadersModuleConfig,
  SecurityViolationReport,
} from './lib/security-headers/interfaces/security-headers.interface';
export * from './lib/security-headers/middleware/security-headers.middleware';
export * from './lib/security-headers/security-headers.module';
export * from './lib/security-headers/services/security-headers-health.service';
export * from './lib/security-headers/services/security-headers.service';

// Export service proxy functionality
export * from './lib/service-proxy/interfaces/service-proxy.interface';
export * from './lib/service-proxy/service-proxy.module';
export * from './lib/service-proxy/services/base-service-proxy.service';
export * from './lib/service-proxy/services/service-proxy-factory.service';
export * from './lib/service-proxy/services/service-registry.service';

// Export request routing functionality
export * from './lib/request-routing/interfaces/route-config.interface';
export * from './lib/request-routing/interfaces/routing.interface';
export * from './lib/request-routing/request-routing.module';
export * from './lib/request-routing/services/request-transformer.service';
export * from './lib/request-routing/services/route-matcher.service';
export * from './lib/request-routing/services/routing.service';

// Export response transformation functionality
export * from './lib/response-transformation/interfaces/transformation.interface';

// Export JWT validation functionality
export * from './lib/jwt-validation/guards/jwt-auth.guard';
export * from './lib/jwt-validation/guards/rbac.guard';
export * from './lib/jwt-validation/interfaces/authenticated-request.interface';
export * from './lib/jwt-validation/interfaces/jwt-validation-health.interface';
export * from './lib/jwt-validation/jwt-validation.module';

// Export RBAC functionality (type-safe roles and permissions)
export * from './lib/rbac';

// Export JWT validation decorators with explicit naming to avoid conflicts
export {
  HasPermission,
  HasRole,
  JwtOptions,
  JwtPayloadDecorator,
  RequireTenant as JwtRequireTenant,
  Tenant as JwtTenant,
  TenantId as JwtTenantId,
  UserId as JwtUserId,
  Public,
  RequirePermissions,
  RequireRoles,
  SkipTenantValidation,
  User,
  UserEmail,
  UserPermissions,
  UserRoles,
} from './lib/jwt-validation/decorators/jwt-decorators';

// Export JWT validation interfaces with explicit naming to avoid conflicts
export {
  JwtPayload,
  JwtValidationConfig as JwtValidationModuleConfig,
  JwtValidationOptions,
  TenantContext,
  UserContext,
} from './lib/jwt-validation/interfaces/jwt-validation.interface';

export * from './lib/jwt-validation';

export * from './lib/response-transformation';

// Export Prisma functionality
export * from './lib/prisma/prisma';

// Export case transformation functionality
export * from './lib/case-transform';

// Export response format functionality
export * from './lib/response-format';

// Export query functionality
export * from './lib/query';

// Export telemetry functionality
export * from './lib/telemetry';

// Export utility functions
export * from './lib/utils';

// Export decorators
export * from './lib/decorators';

// Export proto
export * from './lib/proto';

// Export repositories
export * from './repositories';
