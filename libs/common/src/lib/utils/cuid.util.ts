import { createId } from '@paralleldrive/cuid2';

/**
 * CUID utility functions for generating prefixed CUIDs
 * Used across the application for consistent ID generation
 */

/**
 * CUID prefixes for different entity types
 */
export const CUID_PREFIXES = {
  // User Service
  USER: 'usr_',
  ROLE: 'rol_',
  PERMISSION: 'per_',
  USER_ROLE: 'uro_',
  ROLE_PERMISSION: 'rpe_',
  USER_PERMISSION: 'upe_',
  ROLE_AUDIT: 'rau_',

  // Auth Service
  AUTH_SESSION: 'ses_',
  USER_SESSION: 'use_',
  AUTH_EVENT: 'aev_',
  AUTH_TOKEN: 'tok_',
  SECURITY_SETTINGS: 'sec_',
  MFA_CONFIGURATION: 'mfa_',
  MFA_CHALLENGE: 'mfc_',
  TRUSTED_DEVICE: 'trd_',

  // Tenant Service
  TENANT: 'ten_',
  ONBOARDING_WORKFLOW: 'onw_',
  ONBOARDING_SESSION: 'ons_',
  ONBOARDING_STEP: 'ost_',
  DOCUMENT_UPLOAD: 'doc_',
  TENANT_CONFIGURATION: 'tcf_',
  TENANT_API_KEY: 'tak_',
  TENANT_WEBHOOK_KEY: 'twk_',

  // Customer Service
  CUSTOMER: 'cus_',
  ACCOUNT: 'acc_',
  CUSTOMER_DOCUMENT: 'cdo_',
  CUSTOMER_RISK_SCORE: 'crs_',
  CUSTOMER_RELATIONSHIP: 'cre_',
  CUSTOMER_ALERT: 'cal_',
  CUSTOMER_NOTE: 'cno_',

  // Transaction Service
  TRANSACTION: 'txn_',
  TRANSACTION_ALERT: 'tal_',

  // AML Service
  RISK_RULE: 'rru_',
  RISK_EVALUATION: 'rev_',
  RISK_ALERT: 'ral_',
  CUSTOMER_RISK_PROFILE: 'crp_',
  AML_AUDIT_LOG: 'aal_',
  AML_CONFIGURATION: 'acf_',

  // Generic
  GENERIC: 'gen_',

  // Fingerprinting
  DEVICE_FINGERPRINT: 'fp_',
} as const;

/**
 * Generate a CUID with the specified prefix
 * @param prefix - The prefix to use (from CUID_PREFIXES)
 * @returns A prefixed CUID string
 */
export function generateCuid(prefix: string): string {
  return `${prefix}${createId()}`;
}

/**
 * Generate a user ID
 */
export function generateUserId(): string {
  return generateCuid(CUID_PREFIXES.USER);
}

/**
 * Generate a role ID
 */
export function generateRoleId(): string {
  return generateCuid(CUID_PREFIXES.ROLE);
}

/**
 * Generate a permission ID
 */
export function generatePermissionId(): string {
  return generateCuid(CUID_PREFIXES.PERMISSION);
}

/**
 * Generate a user role ID
 */
export function generateUserRoleId(): string {
  return generateCuid(CUID_PREFIXES.USER_ROLE);
}

/**
 * Generate a role permission ID
 */
export function generateRolePermissionId(): string {
  return generateCuid(CUID_PREFIXES.ROLE_PERMISSION);
}

/**
 * Generate a user permission ID
 */
export function generateUserPermissionId(): string {
  return generateCuid(CUID_PREFIXES.USER_PERMISSION);
}

/**
 * Generate a role audit ID
 */
export function generateRoleAuditId(): string {
  return generateCuid(CUID_PREFIXES.ROLE_AUDIT);
}

/**
 * Generate an auth session ID
 */
export function generateAuthSessionId(): string {
  return generateCuid(CUID_PREFIXES.AUTH_SESSION);
}

/**
 * Generate a user session ID
 */
export function generateUserSessionId(): string {
  return generateCuid(CUID_PREFIXES.USER_SESSION);
}

/**
 * Generate an auth event ID
 */
export function generateAuthEventId(): string {
  return generateCuid(CUID_PREFIXES.AUTH_EVENT);
}

/**
 * Generate an auth token ID
 */
export function generateAuthTokenId(): string {
  return generateCuid(CUID_PREFIXES.AUTH_TOKEN);
}

/**
 * Generate a security settings ID
 */
export function generateSecuritySettingsId(): string {
  return generateCuid(CUID_PREFIXES.SECURITY_SETTINGS);
}

/**
 * Generate an MFA configuration ID
 */
export function generateMfaConfigurationId(): string {
  return generateCuid(CUID_PREFIXES.MFA_CONFIGURATION);
}

/**
 * Generate an MFA challenge ID
 */
export function generateMfaChallengeId(): string {
  return generateCuid(CUID_PREFIXES.MFA_CHALLENGE);
}

/**
 * Generate a tenant ID
 */
export function generateTenantId(): string {
  return generateCuid(CUID_PREFIXES.TENANT);
}

/**
 * Generate a customer ID
 */
export function generateCustomerId(): string {
  return generateCuid(CUID_PREFIXES.CUSTOMER);
}

/**
 * Generate a trusted device ID
 */
export function generateTrustedDeviceId(): string {
  return generateCuid(CUID_PREFIXES.TRUSTED_DEVICE);
}

/**
 * Generate a transaction ID
 */
export function generateTransactionId(): string {
  return generateCuid(CUID_PREFIXES.TRANSACTION);
}

/**
 * Generate a transaction alert ID
 */
export function generateTransactionAlertId(): string {
  return generateCuid(CUID_PREFIXES.TRANSACTION_ALERT);
}

/**
 * Generate a risk rule ID
 */
export function generateRiskRuleId(): string {
  return generateCuid(CUID_PREFIXES.RISK_RULE);
}

/**
 * Generate a risk evaluation ID
 */
export function generateRiskEvaluationId(): string {
  return generateCuid(CUID_PREFIXES.RISK_EVALUATION);
}

/**
 * Generate a risk alert ID
 */
export function generateRiskAlertId(): string {
  return generateCuid(CUID_PREFIXES.RISK_ALERT);
}

/**
 * Generate a customer risk profile ID
 */
export function generateCustomerRiskProfileId(): string {
  return generateCuid(CUID_PREFIXES.CUSTOMER_RISK_PROFILE);
}

/**
 * Generate an AML audit log ID
 */
export function generateAmlAuditLogId(): string {
  return generateCuid(CUID_PREFIXES.AML_AUDIT_LOG);
}

/**
 * Generate an AML configuration ID
 */
export function generateAmlConfigurationId(): string {
  return generateCuid(CUID_PREFIXES.AML_CONFIGURATION);
}

/**
 * Generate a device fingerprint ID
 * @param fingerprint Optional fingerprint data to include
 */
export function generateDeviceFingerprintId(fingerprint?: string): string {
  const fingerprintPart = fingerprint && fingerprint.length > 0 ? `${fingerprint.substring(0, 10)}_` : '';
  return `${CUID_PREFIXES.DEVICE_FINGERPRINT}${fingerprintPart}${createId()}`;
}

/**
 * Validate if a string is a valid CUID with prefix
 * @param id - The ID to validate
 * @param expectedPrefix - Optional expected prefix to validate against
 * @returns boolean indicating if the ID is valid
 */
export function isValidCuid(id: string, expectedPrefix?: string): boolean {
  if (!id || typeof id !== 'string') {
    return false;
  }

  // Check if it has a prefix (contains underscore)
  const underscoreIndex = id.indexOf('_');
  if (underscoreIndex === -1) {
    return false;
  }

  const prefix = id.substring(0, underscoreIndex + 1);
  const cuidPart = id.substring(underscoreIndex + 1);

  // If expected prefix is provided, validate it matches
  if (expectedPrefix && prefix !== expectedPrefix) {
    return false;
  }

  // Validate the CUID part (should be 24 characters long and contain only valid characters)
  if (cuidPart.length !== 24) {
    return false;
  }

  // CUID2 uses base32 encoding with specific character set
  const validCuidPattern = /^[0-9a-z]{24}$/;
  return validCuidPattern.test(cuidPart);
}

/**
 * Extract the prefix from a CUID
 * @param id - The CUID to extract prefix from
 * @returns The prefix or null if invalid
 */
export function extractCuidPrefix(id: string): string | null {
  if (!id || typeof id !== 'string') {
    return null;
  }

  const underscoreIndex = id.indexOf('_');
  if (underscoreIndex === -1) {
    return null;
  }

  return id.substring(0, underscoreIndex + 1);
}

/**
 * Extract the CUID part (without prefix) from a prefixed CUID
 * @param id - The prefixed CUID
 * @returns The CUID part or null if invalid
 */
export function extractCuidPart(id: string): string | null {
  if (!id || typeof id !== 'string') {
    return null;
  }

  const underscoreIndex = id.indexOf('_');
  if (underscoreIndex === -1) {
    return null;
  }

  return id.substring(underscoreIndex + 1);
}
