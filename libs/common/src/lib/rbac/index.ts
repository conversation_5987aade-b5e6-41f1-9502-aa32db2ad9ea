/**
 * RBAC (Role-Based Access Control) module exports
 *
 * This module provides type-safe role and permission management
 * for the Qeep platform with compile-time validation.
 */

import { AMLPermission, PlatformPermission, UserPermission } from './enums/permissions.enum';
import { PlatformRole, Role, RoleUtils, TenantRole } from './enums/roles.enum';
import { RolePermissionUtils } from './mappings/role-permission.mappings';

// Role enums and utilities
export { PlatformRole, Role, ROLE_HIERARCHY, ROLE_METADATA, RoleUtils, TenantRole } from './enums/roles.enum';

// Permission enums and utilities
export {
  AMLPermission,
  APIPermission,
  AuditPermission,
  Permission,
  PERMISSION_CATEGORIES,
  PERMISSION_METADATA,
  PermissionUtils,
  PlatformPermission,
  RolePermission,
  SessionPermission,
  TenantPermission,
  UserPermission,
} from './enums/permissions.enum';

// Role-permission mappings
export { PLATFORM_ROLE_PERMISSIONS, ROLE_PERMISSIONS, RolePermissionUtils, TENANT_ROLE_PERMISSIONS } from './mappings/role-permission.mappings';

// Type-safe decorators
export {
  getUserEffectivePermissions,
  HasAnyPermission,
  HasAnyRole,
  HasPermission,
  HasRole,
  // Type guards and utilities
  isPlatformRole,
  isTenantRole,
  RequireAdminAccess,
  RequireAlertManagementPermissions,
  RequireCaseManagementPermissions,
  REQUIRED_PERMISSIONS_KEY,
  REQUIRED_PLATFORM_ROLES_KEY,
  // Metadata keys
  REQUIRED_ROLES_KEY,
  REQUIRED_TENANT_ROLES_KEY,
  // Permission-based decorators
  RequireOnboardingPermissions,
  RequirePermissions,
  // Method decorators - Specialized
  RequirePlatformAdmin,
  RequirePlatformRoles,
  // Method decorators - General
  RequireRoles,
  RequireSuperAdmin,
  RequireTenantAdmin,
  RequireTenantRoles,
  RequireUserManagementPermissions,
  userHasEffectivePermission,
  UserPermissions,
  // Parameter decorators
  UserRoles,
} from './decorators/rbac.decorators';

/**
 * Common role combinations for convenience
 */
export const ADMIN_ROLES = [PlatformRole.SUPER_ADMIN, PlatformRole.PLATFORM_ADMINISTRATOR, TenantRole.TENANT_ADMIN] as const;

// Only keep the basic admin roles since other specialized roles are removed

/**
 * Common permission combinations for convenience
 */
export const ONBOARDING_PERMISSIONS = [
  PlatformPermission.TENANT_ONBOARDING_INITIATE,
  PlatformPermission.TENANT_ONBOARDING_MANAGE,
  PlatformPermission.TENANT_ONBOARDING_APPROVE,
] as const;

export const USER_MANAGEMENT_PERMISSIONS = [
  UserPermission.USER_CREATE,
  UserPermission.USER_READ,
  UserPermission.USER_UPDATE,
  UserPermission.USER_DELETE,
  UserPermission.USER_LIST,
] as const;

export const CASE_MANAGEMENT_PERMISSIONS = [
  AMLPermission.CASE_READ,
  AMLPermission.CASE_CREATE,
  AMLPermission.CASE_UPDATE,
  AMLPermission.CASE_ASSIGN,
  AMLPermission.CASE_CLOSE,
] as const;

export const ALERT_MANAGEMENT_PERMISSIONS = [
  AMLPermission.ALERT_READ,
  AMLPermission.ALERT_CREATE,
  AMLPermission.ALERT_UPDATE,
  AMLPermission.ALERT_ASSIGN,
  AMLPermission.ALERT_RESOLVE,
] as const;

/**
 * Type definitions for common use cases
 */
export type AdminRole = (typeof ADMIN_ROLES)[number];

export type OnboardingPermission = (typeof ONBOARDING_PERMISSIONS)[number];
export type UserManagementPermission = (typeof USER_MANAGEMENT_PERMISSIONS)[number];
export type CaseManagementPermission = (typeof CASE_MANAGEMENT_PERMISSIONS)[number];
export type AlertManagementPermission = (typeof ALERT_MANAGEMENT_PERMISSIONS)[number];

/**
 * Utility functions for common operations
 */
export class RBACUtils {
  /**
   * Check if user has admin privileges
   */
  static isAdmin(userRoles: Role[]): boolean {
    return ADMIN_ROLES.some((role) => userRoles.includes(role));
  }

  /**
   * Check if user has tenant admin privileges
   */
  static isTenantAdmin(userRoles: Role[]): boolean {
    return userRoles.includes(TenantRole.TENANT_ADMIN);
  }

  /**
   * Get the highest privilege level role for a user
   */
  static getHighestRole(userRoles: Role[]): Role | null {
    if (userRoles.length === 0) return null;

    return userRoles.reduce((highest, current) => {
      const currentLevel = RoleUtils.getRoleLevel(current);
      const highestLevel = RoleUtils.getRoleLevel(highest);
      return currentLevel > highestLevel ? current : highest;
    });
  }

  /**
   * Check if user can perform onboarding operations
   */
  static canPerformOnboarding(userRoles: Role[]): boolean {
    return userRoles.some((role) => RolePermissionUtils.roleHasPermission(role, PlatformPermission.TENANT_ONBOARDING_INITIATE));
  }

  /**
   * Check if user can manage users
   */
  static canManageUsers(userRoles: Role[]): boolean {
    return userRoles.some((role) => RolePermissionUtils.roleHasPermission(role, UserPermission.USER_CREATE));
  }

  /**
   * Check if user can manage cases
   */
  static canManageCases(userRoles: Role[]): boolean {
    return userRoles.some((role) => RolePermissionUtils.roleHasPermission(role, AMLPermission.CASE_CREATE));
  }

  /**
   * Check if user can manage alerts
   */
  static canManageAlerts(userRoles: Role[]): boolean {
    return userRoles.some((role) => RolePermissionUtils.roleHasPermission(role, AMLPermission.ALERT_ASSIGN));
  }
}
