/**
 * Type-safe RBAC decorators for Qeep platform
 *
 * These decorators provide compile-time type safety for roles and permissions,
 * preventing typos and ensuring consistency across the application.
 */

import { createParamDecorator, ExecutionContext, SetMetadata } from '@nestjs/common';
import { AuthenticatedRequest } from '../../jwt-validation/interfaces/authenticated-request.interface';
import { AMLPermission, Permission, PlatformPermission, UserPermission } from '../enums/permissions.enum';
import { PlatformRole, Role, RoleUtils, TenantRole } from '../enums/roles.enum';
import { RolePermissionUtils } from '../mappings/role-permission.mappings';

// Metadata keys for RBAC decorators
export const REQUIRED_ROLES_KEY = 'requiredRoles';
export const REQUIRED_PERMISSIONS_KEY = 'requiredPermissions';
export const REQUIRED_PLATFORM_ROLES_KEY = 'requiredPlatformRoles';
export const REQUIRED_TENANT_ROLES_KEY = 'requiredTenantRoles';

/**
 * Parameter decorators for extracting user context
 */

/**
 * Extract user roles from request (type-safe)
 */
export const UserRoles = createParamDecorator((_data: unknown, ctx: ExecutionContext): Role[] => {
  const request = ctx.switchToHttp().getRequest<AuthenticatedRequest>();
  const user = request.user;
  return (user?.roles as Role[]) || [];
});

/**
 * Extract user permissions from request (type-safe)
 */
export const UserPermissions = createParamDecorator((_data: unknown, ctx: ExecutionContext): Permission[] => {
  const request = ctx.switchToHttp().getRequest<AuthenticatedRequest>();
  const user = request.user;
  return (user?.permissions as Permission[]) || [];
});

/**
 * Check if user has specific role (type-safe)
 */
export const HasRole = (role: Role) =>
  createParamDecorator((_data: unknown, ctx: ExecutionContext): boolean => {
    const request = ctx.switchToHttp().getRequest<AuthenticatedRequest>();
    const user = request.user;
    return user?.roles?.includes(role) || false;
  });

/**
 * Check if user has specific permission (type-safe)
 */
export const HasPermission = (permission: Permission) =>
  createParamDecorator((_data: unknown, ctx: ExecutionContext): boolean => {
    const request = ctx.switchToHttp().getRequest<AuthenticatedRequest>();
    const user = request.user;
    return user?.permissions?.includes(permission) || false;
  });

/**
 * Check if user has any of the specified roles
 */
export const HasAnyRole = (...roles: Role[]) =>
  createParamDecorator((_data: unknown, ctx: ExecutionContext): boolean => {
    const request = ctx.switchToHttp().getRequest<AuthenticatedRequest>();
    const user = request.user;
    const userRoles = (user?.roles as Role[]) || [];
    return roles.some((role) => userRoles.includes(role));
  });

/**
 * Check if user has any of the specified permissions
 */
export const HasAnyPermission = (...permissions: Permission[]) =>
  createParamDecorator((_data: unknown, ctx: ExecutionContext): boolean => {
    const request = ctx.switchToHttp().getRequest<AuthenticatedRequest>();
    const user = request.user;
    const userPermissions = (user?.permissions as Permission[]) || [];
    return permissions.some((permission) => userPermissions.includes(permission));
  });

/**
 * Method decorators for route protection
 */

/**
 * Require specific roles for a route (type-safe)
 * @param roles - Array of roles required
 * @example @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR, TenantRole.TENANT_ADMIN)
 */
export const RequireRoles = (...roles: Role[]) => SetMetadata(REQUIRED_ROLES_KEY, roles);

/**
 * Require specific permissions for a route (type-safe)
 * @param permissions - Array of permissions required
 * @example @RequirePermissions(PlatformPermission.TENANT_ONBOARDING_INITIATE)
 */
export const RequirePermissions = (...permissions: Permission[]) => SetMetadata(REQUIRED_PERMISSIONS_KEY, permissions);

/**
 * Require platform-level roles (cross-tenant access)
 * @param roles - Array of platform roles required
 * @example @RequirePlatformRoles(PlatformRole.PLATFORM_ADMINISTRATOR)
 */
export const RequirePlatformRoles = (...roles: PlatformRole[]) => SetMetadata(REQUIRED_PLATFORM_ROLES_KEY, roles);

/**
 * Require tenant-level roles (within tenant access)
 * @param roles - Array of tenant roles required
 * @example @RequireTenantRoles(TenantRole.TENANT_ADMIN, TenantRole.AML_MANAGER)
 */
export const RequireTenantRoles = (...roles: TenantRole[]) => SetMetadata(REQUIRED_TENANT_ROLES_KEY, roles);

/**
 * Specialized decorators for common use cases
 */

/**
 * Require platform administrator access
 * Shorthand for @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR)
 */
export const RequirePlatformAdmin = () => RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR);

/**
 * Require super admin access
 * Shorthand for @RequireRoles(PlatformRole.SUPER_ADMIN)
 */
export const RequireSuperAdmin = () => RequireRoles(PlatformRole.SUPER_ADMIN);

/**
 * Require tenant admin access
 * Shorthand for @RequireRoles(TenantRole.TENANT_ADMIN)
 */
export const RequireTenantAdmin = () => RequireRoles(TenantRole.TENANT_ADMIN);

/**
 * Permission-based decorators for specific operations
 */

/**
 * Require tenant onboarding permissions
 */
export const RequireOnboardingPermissions = () => RequirePermissions(PlatformPermission.TENANT_ONBOARDING_INITIATE, PlatformPermission.TENANT_ONBOARDING_MANAGE);

/**
 * Require user management permissions
 */
export const RequireUserManagementPermissions = () => RequirePermissions(UserPermission.USER_CREATE, UserPermission.USER_READ, UserPermission.USER_UPDATE);

/**
 * Require case management permissions
 */
export const RequireCaseManagementPermissions = () => RequirePermissions(AMLPermission.CASE_READ, AMLPermission.CASE_CREATE, AMLPermission.CASE_UPDATE);

/**
 * Require alert management permissions
 */
export const RequireAlertManagementPermissions = () => RequirePermissions(AMLPermission.ALERT_READ, AMLPermission.ALERT_UPDATE, AMLPermission.ALERT_ASSIGN);

/**
 * Utility decorators for role hierarchy
 */

/**
 * Require minimum role level
 * @param minimumRole - The minimum role required
 * @example @RequireMinimumRole(TenantRole.INVESTIGATOR) // Allows investigator and above
 */
export const RequireMinimumRole = (minimumRole: Role) => {
  const minimumLevel = RoleUtils.getRoleLevel(minimumRole);
  const allowedRoles = Object.entries(RoleUtils.getRoleLevel)
    .filter(([, level]) => level >= minimumLevel)
    .map(([role]) => role as Role);

  return RequireRoles(...allowedRoles);
};

/**
 * Require admin-level access (platform or tenant)
 */
export const RequireAdminAccess = () => RequireRoles(PlatformRole.SUPER_ADMIN, PlatformRole.PLATFORM_ADMINISTRATOR, TenantRole.TENANT_ADMIN);

/**
 * Type guards and utility functions
 */

/**
 * Type guard to check if a role is a platform role
 */
export const isPlatformRole = (role: Role): role is PlatformRole => {
  return RoleUtils.isPlatformRole(role);
};

/**
 * Type guard to check if a role is a tenant role
 */
export const isTenantRole = (role: Role): role is TenantRole => {
  return RoleUtils.isTenantRole(role);
};

/**
 * Helper function to get user's effective permissions
 * Combines role-based permissions with direct permissions
 */
export const getUserEffectivePermissions = (userRoles: Role[], directPermissions: Permission[] = []): Permission[] => {
  const rolePermissions = userRoles.flatMap((role) => RolePermissionUtils.getPermissionsForRole(role));
  const allPermissions = [...new Set([...rolePermissions, ...directPermissions])];
  return allPermissions;
};

/**
 * Helper function to check if user has effective permission
 */
export const userHasEffectivePermission = (userRoles: Role[], directPermissions: Permission[], requiredPermission: Permission): boolean => {
  const effectivePermissions = getUserEffectivePermissions(userRoles, directPermissions);
  return effectivePermissions.includes(requiredPermission);
};
