/**
 * System-wide role definitions for Qeep platform
 *
 * Roles are hierarchical and define broad access levels.
 * Each role has a specific set of permissions associated with it.
 */

/**
 * Platform-level roles (cross-tenant)
 * These roles operate at the platform level and can access multiple tenants
 */
export enum PlatformRole {
  /** Super administrator with full platform access */
  SUPER_ADMIN = 'super_admin',

  /** Platform administrator with cross-tenant management capabilities */
  PLATFORM_ADMINISTRATOR = 'platform_administrator',
}

/**
 * Tenant-level roles (within a specific tenant)
 * These roles operate within a single tenant context
 */
export enum TenantRole {
  /** Tenant administrator with full access within their tenant */
  TENANT_ADMIN = 'tenant_admin',

  /** Regular user with basic access to their assigned tasks */
  USER = 'user',
}

/**
 * Combined role type for type safety
 */
export type Role = PlatformRole | TenantRole;

/**
 * Role hierarchy definitions
 * Higher roles inherit permissions from lower roles
 */
export const ROLE_HIERARCHY = {
  // Platform roles (highest to lowest)
  [PlatformRole.SUPER_ADMIN]: 100,
  [PlatformRole.PLATFORM_ADMINISTRATOR]: 90,

  // Tenant roles (highest to lowest within tenant)
  [TenantRole.TENANT_ADMIN]: 50,
  [TenantRole.USER]: 15,
} as const;

/**
 * Role metadata for display and validation
 */
export const ROLE_METADATA = {
  // Platform roles
  [PlatformRole.SUPER_ADMIN]: {
    name: 'Super Administrator',
    description: 'Full platform access with all permissions across all tenants',
    category: 'platform',
    isSystemRole: true,
  },
  [PlatformRole.PLATFORM_ADMINISTRATOR]: {
    name: 'Platform Administrator',
    description: 'Platform-level administration with cross-tenant management capabilities',
    category: 'platform',
    isSystemRole: true,
  },

  // Tenant roles
  [TenantRole.TENANT_ADMIN]: {
    name: 'Tenant Administrator',
    description: 'Full administrative access within the tenant',
    category: 'tenant',
    isSystemRole: false,
  },
  [TenantRole.USER]: {
    name: 'User',
    description: 'Regular user with access to assigned tasks and basic features',
    category: 'tenant',
    isSystemRole: false,
  },
} as const;

/**
 * Utility functions for role management
 */
export class RoleUtils {
  /**
   * Check if a role is a platform role
   */
  static isPlatformRole(role: Role): role is PlatformRole {
    return Object.values(PlatformRole).includes(role as PlatformRole);
  }

  /**
   * Check if a role is a tenant role
   */
  static isTenantRole(role: Role): role is TenantRole {
    return Object.values(TenantRole).includes(role as TenantRole);
  }

  /**
   * Get role hierarchy level
   */
  static getRoleLevel(role: Role): number {
    return ROLE_HIERARCHY[role] || 0;
  }

  /**
   * Check if role A has higher or equal privileges than role B
   */
  static hasHigherOrEqualPrivileges(roleA: Role, roleB: Role): boolean {
    return this.getRoleLevel(roleA) >= this.getRoleLevel(roleB);
  }

  /**
   * Get role metadata
   */
  static getRoleMetadata(role: Role) {
    return ROLE_METADATA[role];
  }

  /**
   * Get all roles by category
   */
  static getRolesByCategory(category: 'platform' | 'tenant'): Role[] {
    return Object.entries(ROLE_METADATA)
      .filter(([, metadata]) => metadata.category === category)
      .map(([role]) => role as Role);
  }
}
