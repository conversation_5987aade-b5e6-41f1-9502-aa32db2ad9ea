/**
 * System-wide permission definitions for Qeep platform
 *
 * Permissions follow the format: resource:action
 * This provides fine-grained access control for specific operations
 */

/**
 * Platform-level permissions (cross-tenant operations)
 */
export enum PlatformPermission {
  // Platform administration
  PLATFORM_ADMIN_ACCESS = 'platform:admin:access',
  PLATFORM_CONFIG_READ = 'platform:config:read',
  PLATFORM_CONFIG_WRITE = 'platform:config:write',

  // Tenant management (platform level)
  TENANT_CREATE = 'tenant:create',
  TENANT_READ_ALL = 'tenant:read:all',
  TENANT_UPDATE_ALL = 'tenant:update:all',
  TENANT_DELETE = 'tenant:delete',
  TENANT_SUSPEND = 'tenant:suspend',
  TENANT_ACTIVATE = 'tenant:activate',

  // Tenant onboarding
  TENANT_ONBOARDING_INITIATE = 'tenant:onboarding:initiate',
  TENANT_ONBOARDING_MANAGE = 'tenant:onboarding:manage',
  TENANT_ONBOARDING_APPROVE = 'tenant:onboarding:approve',
  TENANT_ONBOARDING_VIEW_ALL = 'tenant:onboarding:view:all',

  // Cross-tenant analytics
  PLATFORM_ANALYTICS_READ = 'platform:analytics:read',
  PLATFORM_REPORTS_GENERATE = 'platform:reports:generate',

  // System monitoring
  SYSTEM_HEALTH_READ = 'system:health:read',
  SYSTEM_METRICS_READ = 'system:metrics:read',
  SYSTEM_LOGS_READ = 'system:logs:read',
}

/**
 * User management permissions
 */
export enum UserPermission {
  // User CRUD operations
  USER_CREATE = 'user:create',
  USER_READ = 'user:read',
  USER_UPDATE = 'user:update',
  USER_DELETE = 'user:delete',
  USER_LIST = 'user:list',

  // User account management
  USER_ACTIVATE = 'user:activate',
  USER_DEACTIVATE = 'user:deactivate',
  USER_SUSPEND = 'user:suspend',
  USER_UNLOCK = 'user:unlock',
  USER_RESET_PASSWORD = 'user:reset:password',
  USER_FORCE_PASSWORD_CHANGE = 'user:force:password:change',

  // Profile management
  PROFILE_READ_OWN = 'profile:read:own',
  PROFILE_UPDATE_OWN = 'profile:update:own',
  PROFILE_READ_ALL = 'profile:read:all',
  PROFILE_UPDATE_ALL = 'profile:update:all',
}

/**
 * Role and permission management
 */
export enum RolePermission {
  // Role management
  ROLE_CREATE = 'role:create',
  ROLE_READ = 'role:read',
  ROLE_UPDATE = 'role:update',
  ROLE_DELETE = 'role:delete',
  ROLE_LIST = 'role:list',

  // Role assignment
  ROLE_ASSIGN = 'role:assign',
  ROLE_REVOKE = 'role:revoke',

  // Permission management
  PERMISSION_CREATE = 'permission:create',
  PERMISSION_READ = 'permission:read',
  PERMISSION_UPDATE = 'permission:update',
  PERMISSION_DELETE = 'permission:delete',
  PERMISSION_LIST = 'permission:list',

  // Permission assignment
  PERMISSION_ASSIGN = 'permission:assign',
  PERMISSION_REVOKE = 'permission:revoke',
}

/**
 * Tenant-specific permissions
 */
export enum TenantPermission {
  // Tenant configuration
  TENANT_CONFIG_READ = 'tenant:config:read',
  TENANT_CONFIG_UPDATE = 'tenant:config:update',

  // Tenant features
  TENANT_FEATURES_READ = 'tenant:features:read',
  TENANT_FEATURES_UPDATE = 'tenant:features:update',

  // Tenant analytics
  TENANT_ANALYTICS_READ = 'tenant:analytics:read',
  TENANT_REPORTS_GENERATE = 'tenant:reports:generate',

  // Tenant branding
  TENANT_BRANDING_READ = 'tenant:branding:read',
  TENANT_BRANDING_UPDATE = 'tenant:branding:update',
}

/**
 * AML and compliance permissions
 */
export enum AMLPermission {
  // Transaction monitoring
  TRANSACTION_READ = 'transaction:read',
  TRANSACTION_UPDATE = 'transaction:update',
  TRANSACTION_EXPORT = 'transaction:export',

  // Alert management
  ALERT_READ = 'alert:read',
  ALERT_CREATE = 'alert:create',
  ALERT_UPDATE = 'alert:update',
  ALERT_ASSIGN = 'alert:assign',
  ALERT_RESOLVE = 'alert:resolve',
  ALERT_ESCALATE = 'alert:escalate',
  ALERT_EXPORT = 'alert:export',

  // Case management
  CASE_READ = 'case:read',
  CASE_CREATE = 'case:create',
  CASE_UPDATE = 'case:update',
  CASE_ASSIGN = 'case:assign',
  CASE_CLOSE = 'case:close',
  CASE_REOPEN = 'case:reopen',
  CASE_EXPORT = 'case:export',

  // Investigation
  INVESTIGATION_READ = 'investigation:read',
  INVESTIGATION_CREATE = 'investigation:create',
  INVESTIGATION_UPDATE = 'investigation:update',
  INVESTIGATION_ASSIGN = 'investigation:assign',
  INVESTIGATION_COMPLETE = 'investigation:complete',

  // Rules and scenarios
  RULE_READ = 'rule:read',
  RULE_CREATE = 'rule:create',
  RULE_UPDATE = 'rule:update',
  RULE_DELETE = 'rule:delete',
  RULE_ACTIVATE = 'rule:activate',
  RULE_DEACTIVATE = 'rule:deactivate',

  // Compliance reporting
  COMPLIANCE_REPORT_READ = 'compliance:report:read',
  COMPLIANCE_REPORT_GENERATE = 'compliance:report:generate',
  COMPLIANCE_REPORT_SUBMIT = 'compliance:report:submit',

  // SAR (Suspicious Activity Report)
  SAR_READ = 'sar:read',
  SAR_CREATE = 'sar:create',
  SAR_UPDATE = 'sar:update',
  SAR_SUBMIT = 'sar:submit',
  SAR_APPROVE = 'sar:approve',
}

/**
 * Audit and monitoring permissions
 */
export enum AuditPermission {
  // Audit logs
  AUDIT_READ = 'audit:read',
  AUDIT_EXPORT = 'audit:export',
  AUDIT_SEARCH = 'audit:search',

  // System monitoring
  MONITORING_READ = 'monitoring:read',
  MONITORING_CONFIGURE = 'monitoring:configure',

  // Security events
  SECURITY_EVENT_READ = 'security:event:read',
  SECURITY_EVENT_INVESTIGATE = 'security:event:investigate',
}

/**
 * API and integration permissions
 */
export enum APIPermission {
  // API access
  API_READ = 'api:read',
  API_WRITE = 'api:write',
  API_DELETE = 'api:delete',

  // Webhook management
  WEBHOOK_CREATE = 'webhook:create',
  WEBHOOK_READ = 'webhook:read',
  WEBHOOK_UPDATE = 'webhook:update',
  WEBHOOK_DELETE = 'webhook:delete',
  WEBHOOK_TEST = 'webhook:test',

  // Integration management
  INTEGRATION_CREATE = 'integration:create',
  INTEGRATION_READ = 'integration:read',
  INTEGRATION_UPDATE = 'integration:update',
  INTEGRATION_DELETE = 'integration:delete',
}

/**
 * Session management permissions
 */
export enum SessionPermission {
  // Session management
  SESSION_READ_OWN = 'session:read:own',
  SESSION_READ_ALL = 'session:read:all',
  SESSION_TERMINATE_OWN = 'session:terminate:own',
  SESSION_TERMINATE_ALL = 'session:terminate:all',
  SESSION_MANAGE = 'session:manage',
}

/**
 * Combined permission type for type safety
 */
export type Permission = PlatformPermission | UserPermission | RolePermission | TenantPermission | AMLPermission | AuditPermission | APIPermission | SessionPermission;

/**
 * Permission categories for organization
 */
export const PERMISSION_CATEGORIES = {
  PLATFORM: 'Platform',
  USER: 'User Management',
  ROLE: 'Role & Permission Management',
  TENANT: 'Tenant Management',
  AML: 'AML & Compliance',
  AUDIT: 'Audit & Monitoring',
  API: 'API & Integrations',
  SESSION: 'Session Management',
} as const;

/**
 * Permission metadata for display and validation
 */
export const PERMISSION_METADATA = {
  // Platform permissions
  [PlatformPermission.PLATFORM_ADMIN_ACCESS]: {
    name: 'Platform Admin Access',
    description: 'Access to platform administration features',
    category: PERMISSION_CATEGORIES.PLATFORM,
    resource: 'platform',
    action: 'admin:access',
  },
  [PlatformPermission.TENANT_ONBOARDING_INITIATE]: {
    name: 'Initiate Tenant Onboarding',
    description: 'Ability to initiate onboarding for new tenants',
    category: PERMISSION_CATEGORIES.PLATFORM,
    resource: 'tenant',
    action: 'onboarding:initiate',
  },

  // User permissions
  [UserPermission.USER_CREATE]: {
    name: 'Create User',
    description: 'Create new user accounts',
    category: PERMISSION_CATEGORIES.USER,
    resource: 'user',
    action: 'create',
  },
  [UserPermission.USER_UNLOCK]: {
    name: 'Unlock User Account',
    description: 'Unlock locked user accounts',
    category: PERMISSION_CATEGORIES.USER,
    resource: 'user',
    action: 'unlock',
  },

  // Add more metadata as needed...
} as const;

/**
 * Utility functions for permission management
 */
export class PermissionUtils {
  /**
   * Parse permission string into resource and action
   */
  static parsePermission(permission: Permission): { resource: string; action: string } {
    const parts = permission.split(':');
    if (parts.length < 2) {
      throw new Error(`Invalid permission format: ${permission}`);
    }

    const resource = parts[0];
    const action = parts.slice(1).join(':');

    return { resource, action };
  }

  /**
   * Check if permission matches a pattern (supports wildcards)
   */
  static matchesPattern(permission: Permission, pattern: string): boolean {
    if (pattern === '*') return true;
    if (pattern.endsWith('*')) {
      const prefix = pattern.slice(0, -1);
      return permission.startsWith(prefix);
    }
    return permission === pattern;
  }

  /**
   * Get all permissions by category
   */
  static getPermissionsByCategory(category: keyof typeof PERMISSION_CATEGORIES): Permission[] {
    return Object.entries(PERMISSION_METADATA)
      .filter(([, metadata]) => metadata.category === PERMISSION_CATEGORIES[category])
      .map(([permission]) => permission as Permission);
  }

  /**
   * Get permission metadata
   */
  static getPermissionMetadata(permission: Permission) {
    return PERMISSION_METADATA[permission as keyof typeof PERMISSION_METADATA];
  }
}
