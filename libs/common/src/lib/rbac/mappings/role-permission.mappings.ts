/**
 * Role-Permission mappings for Qeep platform
 *
 * This file defines which permissions are granted to each role.
 * Roles inherit permissions hierarchically where applicable.
 */

import {
  AMLPermission,
  APIPermission,
  AuditPermission,
  Permission,
  PlatformPermission,
  RolePermission,
  SessionPermission,
  TenantPermission,
  UserPermission,
} from '../enums/permissions.enum';
import { PlatformRole, Role, TenantRole } from '../enums/roles.enum';

/**
 * Platform role permissions
 */
export const PLATFORM_ROLE_PERMISSIONS: Record<PlatformRole, Permission[]> = {
  [PlatformRole.SUPER_ADMIN]: [
    // Platform permissions (all)
    ...Object.values(PlatformPermission),
    // User permissions (all)
    ...Object.values(UserPermission),
    // Role permissions (all)
    ...Object.values(RolePermission),
    // Tenant permissions (all)
    ...Object.values(TenantPermission),
    // AML permissions (all)
    ...Object.values(AMLPermission),
    // Audit permissions (all)
    ...Object.values(AuditPermission),
    // API permissions (all)
    ...Object.values(APIPermission),
    // Session permissions (all)
    ...Object.values(SessionPermission),
  ],

  [PlatformRole.PLATFORM_ADMINISTRATOR]: [
    // Platform administration
    PlatformPermission.PLATFORM_ADMIN_ACCESS,
    PlatformPermission.PLATFORM_CONFIG_READ,
    PlatformPermission.PLATFORM_CONFIG_WRITE,

    // Tenant management
    PlatformPermission.TENANT_CREATE,
    PlatformPermission.TENANT_READ_ALL,
    PlatformPermission.TENANT_UPDATE_ALL,
    PlatformPermission.TENANT_SUSPEND,
    PlatformPermission.TENANT_ACTIVATE,

    // Tenant onboarding
    PlatformPermission.TENANT_ONBOARDING_INITIATE,
    PlatformPermission.TENANT_ONBOARDING_MANAGE,
    PlatformPermission.TENANT_ONBOARDING_APPROVE,
    PlatformPermission.TENANT_ONBOARDING_VIEW_ALL,

    // Cross-tenant analytics
    PlatformPermission.PLATFORM_ANALYTICS_READ,
    PlatformPermission.PLATFORM_REPORTS_GENERATE,

    // System monitoring
    PlatformPermission.SYSTEM_HEALTH_READ,
    PlatformPermission.SYSTEM_METRICS_READ,
    PlatformPermission.SYSTEM_LOGS_READ,

    // User management (cross-tenant)
    UserPermission.USER_CREATE,
    UserPermission.USER_READ,
    UserPermission.USER_UPDATE,
    UserPermission.USER_DELETE,
    UserPermission.USER_LIST,
    UserPermission.USER_ACTIVATE,
    UserPermission.USER_DEACTIVATE,
    UserPermission.USER_SUSPEND,
    UserPermission.USER_UNLOCK,
    UserPermission.USER_RESET_PASSWORD,

    // Role management
    RolePermission.ROLE_CREATE,
    RolePermission.ROLE_READ,
    RolePermission.ROLE_UPDATE,
    RolePermission.ROLE_DELETE,
    RolePermission.ROLE_LIST,
    RolePermission.ROLE_ASSIGN,
    RolePermission.ROLE_REVOKE,

    // Audit access
    AuditPermission.AUDIT_READ,
    AuditPermission.AUDIT_EXPORT,
    AuditPermission.AUDIT_SEARCH,

    // Session management
    SessionPermission.SESSION_READ_ALL,
    SessionPermission.SESSION_TERMINATE_ALL,
    SessionPermission.SESSION_MANAGE,
  ],
};

/**
 * Tenant role permissions
 */
export const TENANT_ROLE_PERMISSIONS: Record<TenantRole, Permission[]> = {
  [TenantRole.TENANT_ADMIN]: [
    // Tenant configuration
    TenantPermission.TENANT_CONFIG_READ,
    TenantPermission.TENANT_CONFIG_UPDATE,
    TenantPermission.TENANT_FEATURES_READ,
    TenantPermission.TENANT_FEATURES_UPDATE,
    TenantPermission.TENANT_BRANDING_READ,
    TenantPermission.TENANT_BRANDING_UPDATE,

    // User management (within tenant)
    UserPermission.USER_CREATE,
    UserPermission.USER_READ,
    UserPermission.USER_UPDATE,
    UserPermission.USER_DELETE,
    UserPermission.USER_LIST,
    UserPermission.USER_ACTIVATE,
    UserPermission.USER_DEACTIVATE,
    UserPermission.USER_SUSPEND,
    UserPermission.USER_UNLOCK,
    UserPermission.USER_RESET_PASSWORD,

    // Role management (within tenant)
    RolePermission.ROLE_READ,
    RolePermission.ROLE_LIST,
    RolePermission.ROLE_ASSIGN,
    RolePermission.ROLE_REVOKE,

    // AML permissions (all within tenant)
    ...Object.values(AMLPermission),

    // Audit access
    AuditPermission.AUDIT_READ,
    AuditPermission.AUDIT_EXPORT,
    AuditPermission.AUDIT_SEARCH,

    // API management
    APIPermission.API_READ,
    APIPermission.API_WRITE,
    APIPermission.WEBHOOK_CREATE,
    APIPermission.WEBHOOK_READ,
    APIPermission.WEBHOOK_UPDATE,
    APIPermission.WEBHOOK_DELETE,

    // Session management
    SessionPermission.SESSION_READ_ALL,
    SessionPermission.SESSION_TERMINATE_ALL,
    SessionPermission.SESSION_MANAGE,
  ],

  [TenantRole.USER]: [
    // Basic profile access
    UserPermission.PROFILE_READ_OWN,
    UserPermission.PROFILE_UPDATE_OWN,

    // Session management (own)
    SessionPermission.SESSION_READ_OWN,
    SessionPermission.SESSION_TERMINATE_OWN,
  ],
};

/**
 * Combined role permissions mapping
 */
export const ROLE_PERMISSIONS: Record<Role, Permission[]> = {
  ...PLATFORM_ROLE_PERMISSIONS,
  ...TENANT_ROLE_PERMISSIONS,
};

/**
 * Utility functions for role-permission management
 */
export class RolePermissionUtils {
  /**
   * Get permissions for a role
   */
  static getPermissionsForRole(role: Role): Permission[] {
    return ROLE_PERMISSIONS[role] || [];
  }

  /**
   * Check if a role has a specific permission
   */
  static roleHasPermission(role: Role, permission: Permission): boolean {
    const rolePermissions = this.getPermissionsForRole(role);
    return rolePermissions.includes(permission);
  }

  /**
   * Get all roles that have a specific permission
   */
  static getRolesWithPermission(permission: Permission): Role[] {
    return Object.entries(ROLE_PERMISSIONS)
      .filter(([, permissions]) => permissions.includes(permission))
      .map(([role]) => role as Role);
  }

  /**
   * Check if user with roles has permission
   */
  static userHasPermission(userRoles: Role[], permission: Permission): boolean {
    return userRoles.some((role) => this.roleHasPermission(role, permission));
  }
}
