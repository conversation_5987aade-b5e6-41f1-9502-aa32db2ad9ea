import { Request } from 'express';

/**
 * User information extracted from user service
 * This interface represents the user data available to services
 */
export interface UserInfo {
  /** Unique user identifier */
  id: string;
  /** User email address */
  email: string;
  /** User first name */
  firstName: string;
  /** User last name */
  lastName: string;
  /** Tenant code the user belongs to */
  tenantCode?: string;
  /** Tenant ID the user belongs to */
  tenantId?: string;
  /** User roles */
  roles: string[];
  /** User permissions */
  permissions: string[];
  /** User account status */
  status: UserStatus;
  /** Whether user's email is verified */
  isEmailVerified: boolean;
  /** Last login timestamp */
  lastLoginAt?: Date;
  /** Last login IP address */
  lastLoginIp?: string;
  /** Last login user agent */
  lastLoginUserAgent?: string;
  /** Number of failed login attempts */
  loginAttempts: number;
  /** Account locked until timestamp */
  lockedUntil?: Date;
  /** User creation timestamp */
  createdAt: Date;
  /** User last update timestamp */
  updatedAt: Date;
}

/**
 * User status enumeration
 */
export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
  PENDING = 'PENDING',
}

/**
 * Configuration options for user service client
 */
export interface UserServiceConfig {
  /** Enable/disable the user service client */
  enabled: boolean;
  /** Cache user information for performance */
  enableCache: boolean;
  /** Cache TTL in seconds */
  cacheTtlSeconds: number;
  /** Default timeout for user service requests in milliseconds */
  defaultTimeoutMs: number;
  /** Enable debug logging */
  enableDebugLogging: boolean;
  /** Maximum number of retries for failed requests */
  maxRetries: number;
  /** Retry delay in milliseconds */
  retryDelayMs: number;
}

/**
 * Options for user validation
 */
export interface UserValidationOptions {
  /** Whether to check user status */
  checkStatus?: boolean;
  /** Allowed statuses for validation */
  allowedStatuses?: UserStatus[];
  /** Whether to fetch fresh data from service */
  forceFresh?: boolean;
  /** Timeout for validation request in milliseconds */
  timeoutMs?: number;
  /** Whether to include roles and permissions */
  includeRoles?: boolean;
  /** Whether to include permissions */
  includePermissions?: boolean;
}

/**
 * User cache entry
 */
export interface UserCacheEntry {
  /** Cached user information */
  user: UserInfo;
  /** Cache entry creation timestamp */
  cachedAt: Date;
  /** Cache entry expiration timestamp */
  expiresAt: Date;
  /** Number of times this entry has been accessed */
  accessCount: number;
}

/**
 * Result of user lookup operations
 */
export interface UserLookupResult {
  /** Whether user was successfully found */
  success: boolean;
  /** User information if found */
  user?: UserInfo;
  /** Error message if lookup failed */
  error?: string;
  /** Error code for programmatic handling */
  errorCode?: UserLookupErrorCode;
}

/**
 * Error codes for user lookup failures
 */
export enum UserLookupErrorCode {
  /** User not found */
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  /** User ID is invalid format */
  INVALID_USER_ID = 'INVALID_USER_ID',
  /** User service is unavailable */
  USER_SERVICE_UNAVAILABLE = 'USER_SERVICE_UNAVAILABLE',
  /** User is inactive or suspended */
  USER_INACTIVE = 'USER_INACTIVE',
  /** Internal error during lookup */
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  /** Request timeout */
  TIMEOUT = 'TIMEOUT',
  /** Authentication failed */
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
}

/**
 * Role assignment request
 */
export interface RoleAssignmentRequest {
  /** User ID to assign role to */
  userId: string;
  /** Role name to assign */
  roleName: string;
  /** User ID of who is assigning the role */
  assignedBy?: string;
  /** Optional expiration date for the role */
  expiresAt?: Date;
}

/**
 * Role removal request
 */
export interface RoleRemovalRequest {
  /** User ID to remove role from */
  userId: string;
  /** Role name to remove */
  roleName: string;
}

/**
 * Permission check request
 */
export interface PermissionCheckRequest {
  /** User ID to check permissions for */
  userId: string;
  /** Permission name to check */
  permissionName: string;
}

/**
 * Role check request
 */
export interface RoleCheckRequest {
  /** User ID to check roles for */
  userId: string;
  /** Role name to check */
  roleName: string;
}

/**
 * User roles and permissions response
 */
export interface UserRolesPermissionsResponse {
  /** User roles */
  roles: string[];
  /** User permissions */
  permissions: string[];
}

/**
 * Extended request interface with user information
 */
export interface UserAwareRequest extends Request {
  /** User information from authentication */
  user?: {
    userId: string;
    email: string;
    tenantCode?: string;
    roles?: string[];
    permissions?: string[];
  };
  /** Full user information if fetched */
  userInfo?: UserInfo;
}

/**
 * User service client interface
 * Defines the contract for interacting with the user service
 */
export interface IUserService {
  /**
   * Get user by ID
   */
  getUserById(userId: string, options?: UserValidationOptions): Promise<UserInfo | null>;

  /**
   * Get user by email and tenant
   */
  getUserByEmail(email: string, tenantCode?: string, options?: UserValidationOptions): Promise<UserInfo | null>;

  /**
   * Get user roles
   */
  getUserRoles(userId: string): Promise<string[]>;

  /**
   * Get user permissions
   */
  getUserPermissions(userId: string): Promise<string[]>;

  /**
   * Check if user has specific role
   */
  hasRole(userId: string, roleName: string): Promise<boolean>;

  /**
   * Check if user has specific permission
   */
  hasPermission(userId: string, permissionName: string): Promise<boolean>;

  /**
   * Assign role to user
   */
  assignRole(request: RoleAssignmentRequest): Promise<void>;

  /**
   * Remove role from user
   */
  removeRole(request: RoleRemovalRequest): Promise<void>;

  /**
   * Validate user exists and is active
   */
  validateUser(userId: string, options?: UserValidationOptions): Promise<boolean>;

  /**
   * Clear user cache
   */
  clearCache(): void;

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; entries: Array<{ key: string; accessCount: number; expiresAt: Date }> };
}

/**
 * Default user service configuration
 */
export const DEFAULT_USER_SERVICE_CONFIG: UserServiceConfig = {
  enabled: true,
  enableCache: true,
  cacheTtlSeconds: 300, // 5 minutes
  defaultTimeoutMs: 5000, // 5 seconds
  enableDebugLogging: false,
  maxRetries: 3,
  retryDelayMs: 1000, // 1 second
};

/**
 * Convert user service response to UserInfo
 */
export function protoUserToUserInfo(protoUser: any): UserInfo {
  return {
    id: protoUser.id,
    email: protoUser.email,
    firstName: protoUser.firstName,
    lastName: protoUser.lastName,
    tenantCode: protoUser.tenantCode,
    tenantId: protoUser.tenantId,
    roles: protoUser.roles || [],
    permissions: protoUser.permissions || [],
    status: protoUser.status as UserStatus,
    isEmailVerified: protoUser.isEmailVerified || false,
    lastLoginAt: protoUser.lastLoginAt,
    lastLoginIp: protoUser.lastLoginIp,
    lastLoginUserAgent: protoUser.lastLoginUserAgent,
    loginAttempts: protoUser.loginAttempts || 0,
    lockedUntil: protoUser.lockedUntil,
    createdAt: protoUser.createdAt,
    updatedAt: protoUser.updatedAt,
  };
}
