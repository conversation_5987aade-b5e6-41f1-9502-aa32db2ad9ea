/* eslint-disable @typescript-eslint/no-unused-vars */
import { Inject, Injectable, Logger } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { catchError, firstValueFrom, of, timeout } from 'rxjs';
import {
  DEFAULT_USER_SERVICE_CONFIG,
  IUserService,
  RoleAssignmentRequest,
  RoleRemovalRequest,
  UserCacheEntry,
  UserInfo,
  UserServiceConfig,
  UserValidationOptions,
  protoUserToUserInfo,
} from '../interfaces/user.interface';

/**
 * Service for interacting with the user service via microservices
 * Provides user validation, caching, and RBAC operations
 */
@Injectable()
export class UserService implements IUserService {
  private readonly logger = new Logger(UserService.name);
  private userCache = new Map<string, UserCacheEntry>();
  private readonly config: UserServiceConfig;

  constructor(@Inject('USER_SERVICE') private readonly userServiceClient: ClientProxy, @Inject('USER_SERVICE_CONFIG') config?: Partial<UserServiceConfig>) {
    this.config = { ...DEFAULT_USER_SERVICE_CONFIG, ...config };
  }

  /**
   * Get user information by user ID
   */
  async getUserById(userId: string, options: Partial<UserValidationOptions> = {}): Promise<UserInfo | null> {
    if (!userId || typeof userId !== 'string') {
      this.logger.warn('Invalid user ID provided');
      return null;
    }

    const cacheKey = `id:${userId}`;

    // Check cache first (unless force fresh is requested)
    if (!options.forceFresh) {
      const cached = this.getCachedUser(cacheKey);
      if (cached) {
        this.logger.debug(`User found in cache: ${userId}`);
        return cached;
      }
    }

    try {
      const request = {
        userId,
        metadata: {
          requestId: `get-user-${Date.now()}`,
          sourceIp: '127.0.0.1',
          userAgent: 'UserService/1.0',
          timestamp: new Date(),
        },
      };

      const response = await firstValueFrom(
        this.userServiceClient.send('getUser', request).pipe(
          timeout(options.timeoutMs || this.config.defaultTimeoutMs),
          catchError((error) => {
            this.logger.error(`Failed to get user by ID: ${userId}`, error);
            return of(null);
          }),
        ),
      );

      if (!response || !response.success || !response.user) {
        this.logger.warn(`User not found or invalid response for ID: ${userId}`);
        return null;
      }

      const userInfo = protoUserToUserInfo(response.user);

      // Validate user status if required
      if (options.checkStatus && options.allowedStatuses) {
        if (!options.allowedStatuses.includes(userInfo.status)) {
          this.logger.warn(`User ${userId} has invalid status: ${userInfo.status}`);
          return null;
        }
      }

      // Cache the result
      this.cacheUser(cacheKey, userInfo);

      this.logger.debug(`Successfully retrieved user: ${userId}`);
      return userInfo;
    } catch (error) {
      this.logger.error(`Error getting user by ID: ${userId}`, error);
      return null;
    }
  }

  /**
   * Get user information by email and tenant
   */
  async getUserByEmail(email: string, tenantCode?: string, options: Partial<UserValidationOptions> = {}): Promise<UserInfo | null> {
    if (!email) {
      this.logger.warn('Invalid email provided');
      return null;
    }

    const cacheKey = `email:${tenantCode || 'global'}:${email}`;

    // Check cache first (unless force fresh is requested)
    if (!options.forceFresh) {
      const cached = this.getCachedUser(cacheKey);
      if (cached) {
        this.logger.debug(`User found in cache: ${email}@${tenantCode}`);
        return cached;
      }
    }

    try {
      const request = {
        email,
        tenantCode: tenantCode || '',
        metadata: {
          requestId: `get-user-email-${Date.now()}`,
          sourceIp: '127.0.0.1',
          userAgent: 'UserService/1.0',
          timestamp: new Date(),
        },
      };

      const response = await firstValueFrom(
        this.userServiceClient.send('getUserByEmail', request).pipe(
          timeout(options.timeoutMs || this.config.defaultTimeoutMs),
          catchError((error) => {
            this.logger.error(`Failed to get user by email: ${email}@${tenantCode}`, error);
            return of(null);
          }),
        ),
      );

      if (!response || !response.success || !response.user) {
        this.logger.warn(`User not found or invalid response for email: ${email}@${tenantCode}`);
        return null;
      }

      const userInfo = protoUserToUserInfo(response.user);

      // Validate user status if required
      if (options.checkStatus && options.allowedStatuses) {
        if (!options.allowedStatuses.includes(userInfo.status)) {
          this.logger.warn(`User ${email}@${tenantCode} has invalid status: ${userInfo.status}`);
          return null;
        }
      }

      // Cache the result
      this.cacheUser(cacheKey, userInfo);
      // Also cache by ID for future lookups
      this.cacheUser(`id:${userInfo.id}`, userInfo);

      this.logger.debug(`Successfully retrieved user: ${email}@${tenantCode}`);
      return userInfo;
    } catch (error) {
      this.logger.error(`Error getting user by email: ${email}@${tenantCode}`, error);
      return null;
    }
  }

  /**
   * Get user roles
   */
  async getUserRoles(userId: string): Promise<string[]> {
    try {
      const response = await firstValueFrom(
        this.userServiceClient.send('getUserRoles', { userId }).pipe(
          timeout(this.config.defaultTimeoutMs),
          catchError((error) => {
            this.logger.error(`Failed to get user roles: ${userId}`, error);
            return of({ roles: [] });
          }),
        ),
      );

      return response?.roles || [];
    } catch (error) {
      this.logger.error(`Error getting user roles: ${userId}`, error);
      return [];
    }
  }

  /**
   * Get user permissions
   */
  async getUserPermissions(userId: string): Promise<string[]> {
    try {
      const response = await firstValueFrom(
        this.userServiceClient.send('getUserPermissions', { userId }).pipe(
          timeout(this.config.defaultTimeoutMs),
          catchError((error) => {
            this.logger.error(`Failed to get user permissions: ${userId}`, error);
            return of({ permissions: [] });
          }),
        ),
      );

      return response?.permissions || [];
    } catch (error) {
      this.logger.error(`Error getting user permissions: ${userId}`, error);
      return [];
    }
  }

  /**
   * Check if user has specific role
   */
  async hasRole(userId: string, roleName: string): Promise<boolean> {
    try {
      const roles = await this.getUserRoles(userId);
      return roles.includes(roleName);
    } catch (error) {
      this.logger.error(`Error checking user role: ${userId}, ${roleName}`, error);
      return false;
    }
  }

  /**
   * Check if user has specific permission
   */
  async hasPermission(userId: string, permissionName: string): Promise<boolean> {
    try {
      const permissions = await this.getUserPermissions(userId);
      return permissions.includes(permissionName);
    } catch (error) {
      this.logger.error(`Error checking user permission: ${userId}, ${permissionName}`, error);
      return false;
    }
  }

  /**
   * Assign role to user
   */
  async assignRole(request: RoleAssignmentRequest): Promise<void> {
    try {
      await firstValueFrom(
        this.userServiceClient.send('assignRole', request).pipe(
          timeout(this.config.defaultTimeoutMs),
          catchError((error) => {
            this.logger.error(`Failed to assign role: ${request.roleName} to user: ${request.userId}`, error);
            throw error;
          }),
        ),
      );

      // Invalidate cache for this user
      this.invalidateUserCache(request.userId);
      this.logger.debug(`Role ${request.roleName} assigned to user ${request.userId}`);
    } catch (error) {
      this.logger.error(`Error assigning role: ${request.roleName} to user: ${request.userId}`, error);
      throw error;
    }
  }

  /**
   * Remove role from user
   */
  async removeRole(request: RoleRemovalRequest): Promise<void> {
    try {
      await firstValueFrom(
        this.userServiceClient.send('removeRole', request).pipe(
          timeout(this.config.defaultTimeoutMs),
          catchError((error) => {
            this.logger.error(`Failed to remove role: ${request.roleName} from user: ${request.userId}`, error);
            throw error;
          }),
        ),
      );

      // Invalidate cache for this user
      this.invalidateUserCache(request.userId);
      this.logger.debug(`Role ${request.roleName} removed from user ${request.userId}`);
    } catch (error) {
      this.logger.error(`Error removing role: ${request.roleName} from user: ${request.userId}`, error);
      throw error;
    }
  }

  /**
   * Validate if a user exists and is active
   */
  async validateUser(userId: string, options: Partial<UserValidationOptions> = {}): Promise<boolean> {
    const user = await this.getUserById(userId, options);
    return user !== null;
  }

  /**
   * Get cached user information
   */
  private getCachedUser(cacheKey: string): UserInfo | null {
    const entry = this.userCache.get(cacheKey);

    if (!entry) {
      return null;
    }

    // Check if cache entry has expired
    if (entry.expiresAt < new Date()) {
      this.userCache.delete(cacheKey);
      return null;
    }

    // Update access count
    entry.accessCount++;

    return entry.user;
  }

  /**
   * Cache user information
   */
  private cacheUser(cacheKey: string, user: UserInfo, ttlSeconds = this.config.cacheTtlSeconds): void {
    if (!this.config.enableCache) {
      return;
    }

    const now = new Date();
    const expiresAt = new Date(now.getTime() + ttlSeconds * 1000);

    const entry: UserCacheEntry = {
      user,
      cachedAt: now,
      expiresAt,
      accessCount: 0,
    };

    this.userCache.set(cacheKey, entry);

    // Clean up expired entries periodically
    this.cleanupExpiredCache();
  }

  /**
   * Invalidate cache for a specific user
   */
  private invalidateUserCache(userId: string): void {
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.userCache.entries()) {
      if (key.includes(userId) || entry.user.id === userId) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach((key) => this.userCache.delete(key));

    if (keysToDelete.length > 0) {
      this.logger.debug(`Invalidated ${keysToDelete.length} cache entries for user ${userId}`);
    }
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupExpiredCache(): void {
    const now = new Date();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.userCache.entries()) {
      if (entry.expiresAt < now) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach((key) => this.userCache.delete(key));

    if (expiredKeys.length > 0) {
      this.logger.debug(`Cleaned up ${expiredKeys.length} expired cache entries`);
    }
  }

  /**
   * Clear all cached user information
   */
  clearCache(): void {
    this.userCache.clear();
    this.logger.debug('User cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; entries: Array<{ key: string; accessCount: number; expiresAt: Date }> } {
    const entries = Array.from(this.userCache.entries()).map(([key, entry]) => ({
      key,
      accessCount: entry.accessCount,
      expiresAt: entry.expiresAt,
    }));

    return {
      size: this.userCache.size,
      entries,
    };
  }
}
