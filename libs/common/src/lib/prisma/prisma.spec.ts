import { Test, TestingModule } from '@nestjs/testing';
import { BasePrismaService } from './prisma';

// Mock implementation for testing
class TestPrismaService extends BasePrismaService {
  protected prismaClient = {
    $connect: jest.fn(),
    $disconnect: jest.fn(),
    $queryRaw: jest.fn(),
  };
}

describe('BasePrismaService', () => {
  let service: TestPrismaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [TestPrismaService],
    }).compile();

    service = module.get<TestPrismaService>(TestPrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('onModuleInit', () => {
    it('should connect to database successfully', async () => {
      service['prismaClient'].$connect.mockResolvedValue(undefined);

      await expect(service.onModuleInit()).resolves.not.toThrow();
      expect(service['prismaClient'].$connect).toHaveBeenCalled();
    });

    it('should throw error if connection fails', async () => {
      const error = new Error('Connection failed');
      service['prismaClient'].$connect.mockRejectedValue(error);

      await expect(service.onModuleInit()).rejects.toThrow(error);
    });
  });

  describe('onModuleDestroy', () => {
    it('should disconnect from database', async () => {
      service['prismaClient'].$disconnect.mockResolvedValue(undefined);

      await service.onModuleDestroy();
      expect(service['prismaClient'].$disconnect).toHaveBeenCalled();
    });

    it('should handle disconnect errors gracefully', async () => {
      const error = new Error('Disconnect failed');
      service['prismaClient'].$disconnect.mockRejectedValue(error);

      await expect(service.onModuleDestroy()).resolves.not.toThrow();
    });
  });

  describe('isHealthy', () => {
    it('should return true when database is healthy', async () => {
      service['prismaClient'].$queryRaw.mockResolvedValue([{ result: 1 }]);

      const result = await service.isHealthy();
      expect(result).toBe(true);
      expect(service['prismaClient'].$queryRaw).toHaveBeenCalled();
    });

    it('should return false when database is unhealthy', async () => {
      service['prismaClient'].$queryRaw.mockRejectedValue(new Error('Query failed'));

      const result = await service.isHealthy();
      expect(result).toBe(false);
    });
  });

  describe('getDatabaseInfo', () => {
    it('should return database info', async () => {
      const mockInfo = {
        version: 'PostgreSQL 14.0',
        current_database: 'test_db',
        current_user: 'test_user',
      };
      service['prismaClient'].$queryRaw.mockResolvedValue([mockInfo]);

      const result = await service.getDatabaseInfo();
      expect(result).toEqual(mockInfo);
    });

    it('should throw error if query fails', async () => {
      const error = new Error('Query failed');
      service['prismaClient'].$queryRaw.mockRejectedValue(error);

      await expect(service.getDatabaseInfo()).rejects.toThrow(error);
    });
  });
});
