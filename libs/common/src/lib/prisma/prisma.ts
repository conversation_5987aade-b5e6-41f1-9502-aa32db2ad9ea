/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable, OnModuleInit, OnModuleDestroy, Logger } from '@nestjs/common';

/**
 * Base Prisma Service
 * This is an abstract base class that services can extend to create their own Prisma services
 */
@Injectable()
export abstract class BasePrismaService implements OnModuleInit, OnModuleDestroy {
  protected readonly logger = new Logger(this.constructor.name);
  protected abstract prismaClient: any;

  async onModuleInit() {
    try {
      await this.prismaClient.$connect();
      this.logger.log('Successfully connected to database');
    } catch (error) {
      this.logger.error('Failed to connect to database', error);
      throw error;
    }
  }

  async onModuleDestroy() {
    try {
      await this.prismaClient.$disconnect();
      this.logger.log('Disconnected from database');
    } catch (error) {
      this.logger.error('Error disconnecting from database', error);
    }
  }

  /**
   * Health check method
   */
  async isHealthy(): Promise<boolean> {
    try {
      await this.prismaClient.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      this.logger.error('Database health check failed', error);
      return false;
    }
  }

  /**
   * Get database info for health checks
   */
  async getDatabaseInfo() {
    try {
      const result = await this.prismaClient.$queryRaw<Array<{ version: string; current_database: string; current_user: string }>>`
        SELECT version(), current_database(), current_user
      `;
      return result[0];
    } catch (error) {
      this.logger.error('Failed to get database info', error);
      throw error;
    }
  }
}
