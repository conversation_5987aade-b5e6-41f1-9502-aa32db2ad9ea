/* eslint-disable @typescript-eslint/no-explicit-any */
import { DynamicModule, Module } from '@nestjs/common';
import { APP_INTERCEPTOR, Reflector } from '@nestjs/core';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { CaseTransformModule } from './case-transform/case-transform.module';
import { CaseTransformInterceptor } from './case-transform/interceptors/case-transform.interceptor';
import { CaseTransformValidationPipe } from './case-transform/pipes/case-transform-validation.pipe';
import { CircuitBreakerModule } from './circuit-breaker/circuit-breaker.module';
import { CircuitBreakerModuleOptions } from './circuit-breaker/interfaces/circuit-breaker-options.interface';
import { ConfigModule } from './config/config.module';
import { ConfigService } from './config/config.service';
import { JwtValidationModule } from './jwt-validation/jwt-validation.module';
import { ProtoConfigService, ProtoModule } from './proto';
import { RateLimitingModuleOptions } from './rate-limiting/interfaces/rate-limiting-options.interface';
import { RateLimitingModule } from './rate-limiting/rate-limiting.module';
import { RequestRoutingModule } from './request-routing/request-routing.module';
import { ResponseFormatExceptionFilter } from './response-format/filters/response-format-exception.filter';
import { ResponseFormatInterceptor } from './response-format/interceptors/response-format.interceptor';
import { ResponseFormatModule } from './response-format/response-format.module';
import { ResponseTransformationModule } from './response-transformation/response-transformation.module';
import { SecurityHeadersConfig } from './security-headers/interfaces/security-headers.interface';
import { SecurityHeadersModule } from './security-headers/security-headers.module';
import { ServiceProxyModule } from './service-proxy/service-proxy.module';
import { TelemetryConfig } from './telemetry/interfaces/telemetry.interface';
import { TelemetryModule } from './telemetry/telemetry.module';
import { TenantInterceptor } from './tenant/interceptors/tenant.interceptor';
import { TenantInterceptorConfig } from './tenant/interfaces/tenant.interface';
import { TenantService } from './tenant/services/tenant.service';

/**
 * Common module that provides shared functionality across all services
 * including tenant management, validation, and common utilities
 *
 * Note: Telemetry is automatically disabled in development mode for better performance.
 * Set TELEMETRY_ENABLED=true to override this behavior.
 */
@Module({})
export class CommonModule {
  /**
   * Configure the common module with optional tenant and circuit breaker settings
   */
  static forRoot(options?: {
    // Tenant interceptor configuration
    tenantConfig?: Partial<TenantInterceptorConfig>;
    enableGlobalTenantInterceptor?: boolean;

    // Circuit breaker configurations
    circuitBreakerConfig?: CircuitBreakerModuleOptions;
    enableCircuitBreaker?: boolean;

    // Rate limiting configuration
    rateLimitingConfig?: RateLimitingModuleOptions;
    enableRateLimiting?: boolean;

    // Security headers configuration
    securityHeadersConfig?: Partial<SecurityHeadersConfig>;
    enableSecurityHeaders?: boolean;

    // Cate transform and response format configuration
    caseTransformConfig?: any;
    enableCaseTransform?: boolean;
    responseFormatConfig?: any;
    enableResponseFormat?: boolean;

    // Telemetry configuration
    telemetryConfig?: Partial<TelemetryConfig>;
    enableTelemetry?: boolean;

    // Request routing configuration
    requestRoutingConfig?: any;
    enableRequestRouting?: boolean;
  }): DynamicModule {
    const enableGlobalInterceptor = options?.enableGlobalTenantInterceptor ?? true;
    const enableCircuitBreaker = options?.enableCircuitBreaker ?? false;
    const enableRateLimiting = options?.enableRateLimiting ?? false;
    const enableSecurityHeaders = options?.enableSecurityHeaders ?? false;
    const enableCaseTransform = options?.enableCaseTransform ?? true;
    const enableResponseFormat = options?.enableResponseFormat ?? true;
    const enableTelemetry = options?.enableTelemetry ?? false;
    const enableRequestRouting = options?.enableRequestRouting ?? false;

    const providers: any[] = [
      TenantService,
      ConfigService,
      Reflector,
      CaseTransformInterceptor,
      CaseTransformValidationPipe,
      ResponseFormatInterceptor,
      ResponseFormatExceptionFilter,
      {
        provide: 'TENANT_INTERCEPTOR_CONFIG',
        useValue: options?.tenantConfig || {},
      },
      {
        provide: 'CASE_TRANSFORM_CONFIG',
        useValue: options?.caseTransformConfig || {},
      },
      {
        provide: 'RESPONSE_FORMAT_CONFIG',
        useValue: options?.responseFormatConfig || {},
      },
      {
        provide: TenantInterceptor,
        useFactory: (tenantService: TenantService, reflector: Reflector, config: Partial<TenantInterceptorConfig>) => {
          return new TenantInterceptor(tenantService, reflector, config);
        },
        inject: [TenantService, Reflector, 'TENANT_INTERCEPTOR_CONFIG'],
      },
    ];

    // Add global interceptor if enabled
    if (enableGlobalInterceptor) {
      providers.push({
        provide: APP_INTERCEPTOR,
        useFactory: (tenantService: TenantService, reflector: Reflector, config: Partial<TenantInterceptorConfig>) => {
          return new TenantInterceptor(tenantService, reflector, config);
        },
        inject: [TenantService, Reflector, 'TENANT_INTERCEPTOR_CONFIG'],
      });
    }

    // Add global case transform interceptor if enabled
    if (enableCaseTransform) {
      providers.push({
        provide: APP_INTERCEPTOR,
        useFactory: (config: any) => {
          return new CaseTransformInterceptor(config);
        },
        inject: ['CASE_TRANSFORM_CONFIG'],
      });
    }

    // Add global response format interceptor if enabled
    if (enableResponseFormat) {
      providers.push({
        provide: APP_INTERCEPTOR,
        useFactory: (config: any) => {
          return new ResponseFormatInterceptor(config);
        },
        inject: ['RESPONSE_FORMAT_CONFIG'],
      });

      // Add global exception filter for response formatting
      providers.push({
        provide: 'APP_FILTER',
        useFactory: (config: any) => {
          return new ResponseFormatExceptionFilter(config);
        },
        inject: ['RESPONSE_FORMAT_CONFIG'],
      });
    }

    // Prepare imports array
    const imports = [
      ProtoModule,
      ConfigModule.forRoot(),
      CaseTransformModule,
      ResponseFormatModule,
      ServiceProxyModule,
      ResponseTransformationModule,
      JwtValidationModule.forRoot(),
      ClientsModule.registerAsync([
        {
          name: 'TENANT_GRPC_CLIENT',
          imports: [ProtoModule],
          useFactory: (protoConfigService: ProtoConfigService, configService: ConfigService) => ({
            transport: Transport.GRPC,
            options: {
              package: 'tenant',
              url: configService.getServiceUrl('tenant-service-grpc'),
              protoPath: protoConfigService.getProtoPath('tenant', 'tenant.proto'),
              loader: protoConfigService.getLoaderOptions(),
              channelOptions: protoConfigService.getChannelOptions(),
            },
          }),
          inject: [ProtoConfigService, ConfigService],
        },
      ]),
    ];

    // Add request routing module if enabled
    if (enableRequestRouting) {
      imports.push(RequestRoutingModule);
    }

    // Add circuit breaker module if enabled
    if (enableCircuitBreaker) {
      const circuitBreakerModule = options?.circuitBreakerConfig ? CircuitBreakerModule.forRoot(options.circuitBreakerConfig) : CircuitBreakerModule.forRootAsync();

      imports.push(circuitBreakerModule);
    }

    // Add rate limiting module if enabled
    if (enableRateLimiting) {
      const rateLimitingModule = options?.rateLimitingConfig
        ? RateLimitingModule.forRoot(options.rateLimitingConfig)
        : RateLimitingModule.forRootAsync({
            useFactory: (configService: ConfigService) => {
              return configService.getRateLimitingConfig();
            },
            inject: [ConfigService],
          });

      imports.push(rateLimitingModule);
    }

    // Add security headers module if enabled
    if (enableSecurityHeaders) {
      const securityHeadersModule = options?.securityHeadersConfig
        ? SecurityHeadersModule.forRoot(options.securityHeadersConfig)
        : SecurityHeadersModule.forRootAsync({
            useFactory: (configService: ConfigService) => {
              return configService.getSecurityHeadersConfig();
            },
            inject: [ConfigService],
          });

      imports.push(securityHeadersModule);
    }

    // Always add telemetry module, but configure it based on enableTelemetry flag
    const telemetryModule = options?.telemetryConfig
      ? TelemetryModule.forRoot(options.telemetryConfig)
      : TelemetryModule.forRootAsync({
          useFactory: (configService: ConfigService) => {
            const environment = configService.get('NODE_ENV') || 'development';
            const isDev = environment === 'development';

            // Default to disabled in development, enabled in production
            const defaultEnabled = isDev ? 'false' : 'true';

            // Respect the enableTelemetry flag from module configuration
            const moduleEnabled = enableTelemetry;
            const envEnabled = (configService.get('TELEMETRY_ENABLED') || defaultEnabled) === 'true';

            return {
              enabled: moduleEnabled && envEnabled,
              serviceName: configService.get('SERVICE_NAME') || 'qeep-service',
              serviceVersion: configService.get('SERVICE_VERSION') || '1.0.0',
              environment,
              tracing: {
                enabled: moduleEnabled && envEnabled,
                sampleRate: isDev ? 0.1 : 1.0,
                jaegerEndpoint: configService.get('JAEGER_ENDPOINT') || 'http://localhost:14268/api/traces',
                resourceAttributes: {},
              },
              metrics: {
                enabled: moduleEnabled && envEnabled,
                prometheusEndpoint: '/metrics',
                collectionInterval: isDev ? 10000 : 5000,
                customMetrics: [],
              },
              logging: {
                level: 'info',
                includeTraceContext: true,
                format: 'json',
              },
            };
          },
          inject: [ConfigService],
        });

    imports.push(telemetryModule);

    return {
      module: CommonModule,
      imports,
      providers,
      exports: [
        TenantService,
        TenantInterceptor,
        ConfigService,
        ClientsModule,
        CaseTransformModule,
        CaseTransformInterceptor,
        CaseTransformValidationPipe,
        ResponseFormatModule,
        ResponseFormatInterceptor,
        ResponseFormatExceptionFilter,
        TelemetryModule,
      ],
      global: true,
    };
  }

  /**
   * Configure the common module without global tenant interceptor
   * Useful when you want to apply the interceptor selectively
   */
  static forFeature(options?: {
    tenantConfig?: Partial<TenantInterceptorConfig>;
    circuitBreakerConfig?: CircuitBreakerModuleOptions;
    enableCircuitBreaker?: boolean;
    rateLimitingConfig?: RateLimitingModuleOptions;
    enableRateLimiting?: boolean;
    securityHeadersConfig?: Partial<SecurityHeadersConfig>;
    enableSecurityHeaders?: boolean;
    telemetryConfig?: Partial<TelemetryConfig>;
    enableTelemetry?: boolean;
  }): DynamicModule {
    return this.forRoot({
      ...options,
      enableGlobalTenantInterceptor: false,
    });
  }
}
