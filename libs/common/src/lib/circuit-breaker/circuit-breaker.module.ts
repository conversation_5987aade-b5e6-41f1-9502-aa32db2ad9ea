import { DynamicModule, Module, Provider } from '@nestjs/common';
import { CircuitBreakerConfigService } from './circuit-breaker-config.service';
import { CircuitBreakerHealthService } from './circuit-breaker-health.service';
import { CircuitBreakerInitService } from './circuit-breaker-init.service';
import { CIRCUIT_BREAKER_OPTIONS } from './circuit-breaker.constants';
import { CircuitBreakerService } from './circuit-breaker.service';
import { CircuitBreakerInterceptor } from './interceptors/circuit-breaker.interceptor';
import {
    CircuitBreakerModuleOptions,
} from './interfaces/circuit-breaker-options.interface';

@Module({})
export class CircuitBreakerModule {
  /**
   * Create a circuit breaker module with configuration
   */
  static forRoot(options: CircuitBreakerModuleOptions): DynamicModule {
    const providers: Provider[] = [
      {
        provide: CIRCUIT_BREAKER_OPTIONS,
        useValue: options,
      },
      CircuitBreakerConfigService,
      CircuitBreakerService,
      CircuitBreakerHealthService,
      CircuitBreakerInitService,
      CircuitBreakerInterceptor,
    ];

    return {
      module: CircuitBreakerModule,
      providers,
      exports: [
        CircuitBreakerService,
        CircuitBreakerConfigService,
        CircuitBreakerHealthService,
        CircuitBreakerInterceptor,
      ],
      global: true,
    };
  }

  /**
   * Create a circuit breaker module with environment-based configuration
   */
  static forRootAsync(): DynamicModule {
    const providers: Provider[] = [
      {
        provide: CIRCUIT_BREAKER_OPTIONS,
        useFactory: () => CircuitBreakerConfigService.createFromEnvironment(),
      },
      CircuitBreakerConfigService,
      CircuitBreakerService,
      CircuitBreakerHealthService,
      CircuitBreakerInitService,
      CircuitBreakerInterceptor,
    ];

    return {
      module: CircuitBreakerModule,
      providers,
      exports: [
        CircuitBreakerService,
        CircuitBreakerConfigService,
        CircuitBreakerHealthService,
        CircuitBreakerInterceptor,
      ],
      global: true,
    };
  }
}
