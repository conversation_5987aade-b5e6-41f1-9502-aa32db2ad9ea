export const CIRCUIT_BREAKER_OPTIONS = 'CIRCUIT_BREAKER_OPTIONS';

export const DEFAULT_CIRCUIT_BREAKER_CONFIG = {
  failureThreshold: 50, // 50% failure rate
  timeout: 30000, // 30 seconds
  requestVolumeThreshold: 10, // Minimum 10 requests before calculating failure rate
  sleepWindowInMilliseconds: 30000, // 30 seconds before trying HALF_OPEN
  maxRetries: 3, // Maximum retries in HALF_OPEN state
  enabled: true,
} as const;

export const CIRCUIT_BREAKER_EVENTS = {
  STATE_CHANGE: 'circuit-breaker.state-change',
  FAILURE: 'circuit-breaker.failure',
  SUCCESS: 'circuit-breaker.success',
  TIMEOUT: 'circuit-breaker.timeout',
  FALLBACK_EXECUTED: 'circuit-breaker.fallback-executed',
} as const;

export const CIRCUIT_BREAKER_METRICS = {
  TOTAL_REQUESTS: 'circuit_breaker_total_requests',
  SUCCESSFUL_REQUESTS: 'circuit_breaker_successful_requests',
  FAILED_REQUESTS: 'circuit_breaker_failed_requests',
  STATE_DURATION: 'circuit_breaker_state_duration_seconds',
  RESPONSE_TIME: 'circuit_breaker_response_time_seconds',
} as const;

export const CIRCUIT_BREAKER_ERRORS = {
  CIRCUIT_OPEN: 'Circuit breaker is OPEN',
  EXECUTION_TIMEOUT: 'Execution timeout exceeded',
  FALLBACK_FAILED: 'Fallback execution failed',
  INVALID_CONFIGURATION: 'Invalid circuit breaker configuration',
  NOT_FOUND: 'Circuit breaker not found',
} as const;

export const DEFAULT_CIRCUIT_BREAKERS = {
  AUTH_SERVICE: 'auth-service',
  USER_SERVICE: 'user-service',
  TENANT_SERVICE: 'tenant-service',
  NOTIFICATION_SERVICE: 'notification-service',
  DEFAULT: 'default',
} as const;
