import { SetMetadata } from '@nestjs/common';

export const CIRCUIT_BREAKER_KEY = 'circuit-breaker';

export interface CircuitBreakerDecoratorOptions {
  name: string;
  fallbackMethod?: string;
  enabled?: boolean;
}

/**
 * Decorator to apply circuit breaker protection to a method
 * @param options Circuit breaker configuration options
 */
export const CircuitBreaker = (options: CircuitBreakerDecoratorOptions) =>
  SetMetadata(CIRCUIT_BREAKER_KEY, options);

/**
 * Decorator to apply circuit breaker protection with a specific name
 * @param name Circuit breaker name
 * @param fallbackMethod Optional fallback method name
 */
export const UseCircuitBreaker = (name: string, fallbackMethod?: string) =>
  CircuitBreaker({ name, fallbackMethod });

/**
 * Decorator to mark a method as a fallback for circuit breaker
 * @param circuitBreakerName Name of the circuit breaker this is a fallback for
 */
export const CircuitBreakerFallback = (circuitBreakerName: string) =>
  SetMetadata(`${CIRCUIT_BREAKER_KEY}-fallback`, circuitBreakerName);
