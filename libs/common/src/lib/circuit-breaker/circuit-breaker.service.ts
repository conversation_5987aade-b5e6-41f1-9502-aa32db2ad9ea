import { Injectable, Logger } from '@nestjs/common';
import { CIRCUIT_BREAKER_ERRORS } from './circuit-breaker.constants';
import {
    CircuitBreakerConfig,
    CircuitBreakerExecution,
    CircuitBreakerHealthCheck,
    CircuitBreakerResult,
    CircuitBreakerState,
    CircuitBreakerStats
} from './interfaces/circuit-breaker-options.interface';

interface CircuitBreakerInstance {
  config: CircuitBreakerConfig;
  state: CircuitBreakerState;
  failureCount: number;
  successCount: number;
  totalRequests: number;
  lastFailureTime?: Date;
  lastSuccessTime?: Date;
  nextAttemptTime?: Date;
  halfOpenRetries: number;
  responseTimes: number[];
}

@Injectable()
export class CircuitBreakerService {
  private readonly logger = new Logger(CircuitBreakerService.name);
  private readonly circuitBreakers = new Map<string, CircuitBreakerInstance>();

  /**
   * Register a circuit breaker with the given configuration
   */
  registerCircuitBreaker(name: string, config: CircuitBreakerConfig): void {
    this.logger.log(`Registering circuit breaker: ${name}`);
    
    const instance: CircuitBreakerInstance = {
      config,
      state: CircuitBreakerState.CLOSED,
      failureCount: 0,
      successCount: 0,
      totalRequests: 0,
      halfOpenRetries: 0,
      responseTimes: [],
    };

    this.circuitBreakers.set(name, instance);
  }

  /**
   * Execute a function with circuit breaker protection
   */
  async execute<T>(
    circuitBreakerName: string,
    execution: CircuitBreakerExecution<T>
  ): Promise<CircuitBreakerResult<T>> {
    const startTime = Date.now();
    const instance = this.circuitBreakers.get(circuitBreakerName);

    if (!instance) {
      throw new Error(`${CIRCUIT_BREAKER_ERRORS.NOT_FOUND}: ${circuitBreakerName}`);
    }

    if (!instance.config.enabled) {
      // Circuit breaker disabled, execute directly
      try {
        const data = await execution.execute();
        return {
          success: true,
          data,
          executedFallback: false,
          circuitBreakerState: CircuitBreakerState.CLOSED,
          executionTime: Date.now() - startTime,
        };
      } catch (error) {
        return {
          success: false,
          error: error as Error,
          executedFallback: false,
          circuitBreakerState: CircuitBreakerState.CLOSED,
          executionTime: Date.now() - startTime,
        };
      }
    }

    // Check if circuit breaker should transition states
    this.updateCircuitBreakerState(instance);

    // If circuit is OPEN, reject immediately or execute fallback
    if (instance.state === CircuitBreakerState.OPEN) {
      this.logger.warn(`Circuit breaker ${circuitBreakerName} is OPEN, executing fallback`);
      
      if (execution.fallback) {
        try {
          const data = await execution.fallback();
          return {
            success: true,
            data,
            executedFallback: true,
            circuitBreakerState: instance.state,
            executionTime: Date.now() - startTime,
          };
        } catch (error) {
          return {
            success: false,
            error: error as Error,
            executedFallback: true,
            circuitBreakerState: instance.state,
            executionTime: Date.now() - startTime,
          };
        }
      }

      return {
        success: false,
        error: new Error(CIRCUIT_BREAKER_ERRORS.CIRCUIT_OPEN),
        executedFallback: false,
        circuitBreakerState: instance.state,
        executionTime: Date.now() - startTime,
      };
    }

    // Execute the function
    try {
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(CIRCUIT_BREAKER_ERRORS.EXECUTION_TIMEOUT));
        }, instance.config.timeout);
      });

      const data = await Promise.race([
        execution.execute(),
        timeoutPromise,
      ]);

      this.recordSuccess(instance, Date.now() - startTime);
      
      return {
        success: true,
        data,
        executedFallback: false,
        circuitBreakerState: instance.state,
        executionTime: Date.now() - startTime,
      };
    } catch (error) {
      this.recordFailure(instance, error as Error);
      
      // Try fallback if available
      if (execution.fallback) {
        try {
          const data = await execution.fallback();
          return {
            success: true,
            data,
            executedFallback: true,
            circuitBreakerState: instance.state,
            executionTime: Date.now() - startTime,
          };
        } catch (fallbackError) {
          return {
            success: false,
            error: fallbackError as Error,
            executedFallback: true,
            circuitBreakerState: instance.state,
            executionTime: Date.now() - startTime,
          };
        }
      }

      return {
        success: false,
        error: error as Error,
        executedFallback: false,
        circuitBreakerState: instance.state,
        executionTime: Date.now() - startTime,
      };
    }
  }

  /**
   * Get statistics for a specific circuit breaker
   */
  getStats(name: string): CircuitBreakerStats | undefined {
    const instance = this.circuitBreakers.get(name);
    if (!instance) return undefined;

    const failureRate = instance.totalRequests > 0 
      ? (instance.failureCount / instance.totalRequests) * 100 
      : 0;

    return {
      name,
      state: instance.state,
      failureCount: instance.failureCount,
      successCount: instance.successCount,
      totalRequests: instance.totalRequests,
      failureRate,
      lastFailureTime: instance.lastFailureTime,
      lastSuccessTime: instance.lastSuccessTime,
      nextAttemptTime: instance.nextAttemptTime,
      isEnabled: instance.config.enabled,
    };
  }

  /**
   * Get statistics for all circuit breakers
   */
  getAllStats(): CircuitBreakerStats[] {
    return Array.from(this.circuitBreakers.keys())
      .map(name => this.getStats(name))
      .filter((stats): stats is CircuitBreakerStats => stats !== undefined);
  }

  /**
   * Reset a circuit breaker to CLOSED state
   */
  reset(name: string): boolean {
    const instance = this.circuitBreakers.get(name);
    if (!instance) return false;

    instance.state = CircuitBreakerState.CLOSED;
    instance.failureCount = 0;
    instance.successCount = 0;
    instance.totalRequests = 0;
    instance.halfOpenRetries = 0;
    instance.lastFailureTime = undefined;
    instance.lastSuccessTime = undefined;
    instance.nextAttemptTime = undefined;
    instance.responseTimes = [];

    this.logger.log(`Circuit breaker ${name} has been reset`);
    return true;
  }

  /**
   * Reset all circuit breakers
   */
  resetAll(): void {
    for (const name of this.circuitBreakers.keys()) {
      this.reset(name);
    }
    this.logger.log('All circuit breakers have been reset');
  }

  /**
   * Get health check information for all circuit breakers
   */
  getHealthChecks(): CircuitBreakerHealthCheck[] {
    return Array.from(this.circuitBreakers.entries()).map(([name, instance]) => {
      const failureRate = instance.totalRequests > 0
        ? (instance.failureCount / instance.totalRequests) * 100
        : 0;

      return {
        name,
        state: instance.state,
        healthy: instance.state !== CircuitBreakerState.OPEN,
        failureRate,
        lastFailure: instance.lastFailureTime?.toISOString(),
        nextRetryTime: instance.nextAttemptTime?.toISOString(),
        configuration: {
          failureThreshold: instance.config.failureThreshold,
          timeout: instance.config.timeout,
          requestVolumeThreshold: instance.config.requestVolumeThreshold,
        },
      };
    });
  }

  private updateCircuitBreakerState(instance: CircuitBreakerInstance): void {
    const now = new Date();

    switch (instance.state) {
      case CircuitBreakerState.CLOSED:
        if (this.shouldOpenCircuit(instance)) {
          this.openCircuit(instance);
        }
        break;

      case CircuitBreakerState.OPEN:
        if (this.shouldAttemptReset(instance, now)) {
          this.halfOpenCircuit(instance);
        }
        break;

      case CircuitBreakerState.HALF_OPEN:
        if (instance.halfOpenRetries >= instance.config.maxRetries) {
          if (instance.successCount > 0) {
            this.closeCircuit(instance);
          } else {
            this.openCircuit(instance);
          }
        }
        break;
    }
  }

  private shouldOpenCircuit(instance: CircuitBreakerInstance): boolean {
    if (instance.totalRequests < instance.config.requestVolumeThreshold) {
      return false;
    }

    const failureRate = (instance.failureCount / instance.totalRequests) * 100;
    return failureRate >= instance.config.failureThreshold;
  }

  private shouldAttemptReset(instance: CircuitBreakerInstance, now: Date): boolean {
    return instance.nextAttemptTime ? now >= instance.nextAttemptTime : false;
  }

  private openCircuit(instance: CircuitBreakerInstance): void {
    instance.state = CircuitBreakerState.OPEN;
    instance.nextAttemptTime = new Date(Date.now() + instance.config.sleepWindowInMilliseconds);

    this.logger.warn(`Circuit breaker ${instance.config.name} opened due to failure threshold`);

    if (instance.config.onStateChange) {
      instance.config.onStateChange(instance.state, instance.config.name);
    }
  }

  private halfOpenCircuit(instance: CircuitBreakerInstance): void {
    instance.state = CircuitBreakerState.HALF_OPEN;
    instance.halfOpenRetries = 0;
    instance.successCount = 0;
    instance.failureCount = 0;

    this.logger.log(`Circuit breaker ${instance.config.name} transitioned to HALF_OPEN`);

    if (instance.config.onStateChange) {
      instance.config.onStateChange(instance.state, instance.config.name);
    }
  }

  private closeCircuit(instance: CircuitBreakerInstance): void {
    instance.state = CircuitBreakerState.CLOSED;
    instance.failureCount = 0;
    instance.successCount = 0;
    instance.totalRequests = 0;
    instance.nextAttemptTime = undefined;

    this.logger.log(`Circuit breaker ${instance.config.name} closed successfully`);

    if (instance.config.onStateChange) {
      instance.config.onStateChange(instance.state, instance.config.name);
    }
  }

  private recordSuccess(instance: CircuitBreakerInstance, responseTime: number): void {
    instance.successCount++;
    instance.totalRequests++;
    instance.lastSuccessTime = new Date();
    instance.responseTimes.push(responseTime);

    // Keep only last 100 response times for memory efficiency
    if (instance.responseTimes.length > 100) {
      instance.responseTimes = instance.responseTimes.slice(-100);
    }

    if (instance.state === CircuitBreakerState.HALF_OPEN) {
      instance.halfOpenRetries++;
    }

    if (instance.config.onSuccess) {
      instance.config.onSuccess(instance.config.name);
    }
  }

  private recordFailure(instance: CircuitBreakerInstance, error: Error): void {
    instance.failureCount++;
    instance.totalRequests++;
    instance.lastFailureTime = new Date();

    if (instance.state === CircuitBreakerState.HALF_OPEN) {
      instance.halfOpenRetries++;
    }

    this.logger.error(`Circuit breaker ${instance.config.name} recorded failure: ${error.message}`);

    if (instance.config.onFailure) {
      instance.config.onFailure(error, instance.config.name);
    }
  }
}
