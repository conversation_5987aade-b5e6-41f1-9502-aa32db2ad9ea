/* eslint-disable @typescript-eslint/no-explicit-any */

export enum CircuitBreakerState {
  CLOSED = 'CLOSED',
  OPEN = 'OPEN',
  HALF_OPEN = 'HALF_OPEN',
}

export interface CircuitBreakerConfig {
  name: string;
  failureThreshold: number; // Percentage of failures to trigger open state (e.g., 50 for 50%)
  timeout: number; // Timeout in milliseconds before attempting to close (e.g., 30000 for 30s)
  requestVolumeThreshold: number; // Minimum number of requests before calculating failure rate
  sleepWindowInMilliseconds: number; // Time to wait before transitioning from OPEN to HALF_OPEN
  maxRetries: number; // Maximum number of retries in HALF_OPEN state
  enabled: boolean; // Whether circuit breaker is enabled
  onStateChange?: (state: CircuitBreakerState, name: string) => void;
  onFailure?: (error: Error, name: string) => void;
  onSuccess?: (name: string) => void;
}

export interface CircuitBreakerModuleOptions {
  defaultConfig: CircuitBreakerConfig;
  circuitBreakers: Record<string, Partial<CircuitBreakerConfig>>;
  enableMetrics?: boolean;
  enableLogging?: boolean;
  globalTimeout?: number;
}

export interface CircuitBreakerStats {
  name: string;
  state: CircuitBreakerState;
  failureCount: number;
  successCount: number;
  totalRequests: number;
  failureRate: number;
  lastFailureTime?: Date;
  lastSuccessTime?: Date;
  nextAttemptTime?: Date;
  isEnabled: boolean;
}

export interface CircuitBreakerMetrics {
  circuitBreakerName: string;
  state: CircuitBreakerState;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  failureRate: number;
  averageResponseTime: number;
  lastStateChange: Date;
  timeInCurrentState: number;
}

export interface CircuitBreakerExecution<T> {
  execute(): Promise<T>;
  fallback?(): Promise<T>;
}

export interface CircuitBreakerResult<T> {
  success: boolean;
  data?: T;
  error?: Error;
  executedFallback: boolean;
  circuitBreakerState: CircuitBreakerState;
  executionTime: number;
}

export interface CircuitBreakerHealthCheck {
  name: string;
  state: CircuitBreakerState;
  healthy: boolean;
  failureRate: number;
  lastFailure?: string;
  nextRetryTime?: string;
  configuration: {
    failureThreshold: number;
    timeout: number;
    requestVolumeThreshold: number;
  };
}
