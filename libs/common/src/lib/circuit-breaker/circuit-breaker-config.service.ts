import { Inject, Injectable } from '@nestjs/common';
import {
    CIRCUIT_BREAKER_OPTIONS,
    DEFAULT_CIRCUIT_BREAKER_CONFIG,
    DEFAULT_CIRCUIT_BREAKERS,
} from './circuit-breaker.constants';
import {
    CircuitBreakerConfig,
    CircuitBreakerModuleOptions,
} from './interfaces/circuit-breaker-options.interface';

@Injectable()
export class CircuitBreakerConfigService {
  constructor(
    @Inject(CIRCUIT_BREAKER_OPTIONS)
    private readonly options: CircuitBreakerModuleOptions
  ) {}

  /**
   * Get configuration for a specific circuit breaker
   */
  getConfig(name: string): CircuitBreakerConfig {
    const customConfig = this.options.circuitBreakers[name] || {};

    return {
      ...DEFAULT_CIRCUIT_BREAKER_CONFIG,
      ...this.options.defaultConfig,
      ...customConfig,
      name, // Override name last to ensure it's not overwritten
    };
  }

  /**
   * Get all configured circuit breaker names
   */
  getCircuitBreakerNames(): string[] {
    const configuredNames = Object.keys(this.options.circuitBreakers);
    const defaultNames = Object.values(DEFAULT_CIRCUIT_BREAKERS);
    
    // Combine and deduplicate
    return [...new Set([...configuredNames, ...defaultNames])];
  }

  /**
   * Check if metrics collection is enabled
   */
  isMetricsEnabled(): boolean {
    return this.options.enableMetrics ?? true;
  }

  /**
   * Check if logging is enabled
   */
  isLoggingEnabled(): boolean {
    return this.options.enableLogging ?? true;
  }

  /**
   * Get global timeout setting
   */
  getGlobalTimeout(): number {
    return this.options.globalTimeout ?? DEFAULT_CIRCUIT_BREAKER_CONFIG.timeout;
  }

  /**
   * Get default configuration
   */
  getDefaultConfig(): CircuitBreakerConfig {
    return {
      ...DEFAULT_CIRCUIT_BREAKER_CONFIG,
      ...this.options.defaultConfig,
      name: 'default', // Override name last to ensure it's not overwritten
    };
  }

  /**
   * Validate circuit breaker configuration
   */
  validateConfig(config: Partial<CircuitBreakerConfig>): boolean {
    if (config.failureThreshold !== undefined) {
      if (config.failureThreshold < 0 || config.failureThreshold > 100) {
        throw new Error('Failure threshold must be between 0 and 100');
      }
    }

    if (config.timeout !== undefined) {
      if (config.timeout <= 0) {
        throw new Error('Timeout must be greater than 0');
      }
    }

    if (config.requestVolumeThreshold !== undefined) {
      if (config.requestVolumeThreshold <= 0) {
        throw new Error('Request volume threshold must be greater than 0');
      }
    }

    if (config.sleepWindowInMilliseconds !== undefined) {
      if (config.sleepWindowInMilliseconds <= 0) {
        throw new Error('Sleep window must be greater than 0');
      }
    }

    if (config.maxRetries !== undefined) {
      if (config.maxRetries < 0) {
        throw new Error('Max retries must be greater than or equal to 0');
      }
    }

    return true;
  }

  /**
   * Create configuration from environment variables
   */
  static createFromEnvironment(): CircuitBreakerModuleOptions {
    const enabled = process.env['CIRCUIT_BREAKER_ENABLED'] !== 'false';
    const failureThreshold = parseInt(process.env['CIRCUIT_BREAKER_FAILURE_THRESHOLD'] || '50', 10);
    const timeout = parseInt(process.env['CIRCUIT_BREAKER_TIMEOUT'] || '30000', 10);
    const requestVolumeThreshold = parseInt(process.env['CIRCUIT_BREAKER_REQUEST_VOLUME_THRESHOLD'] || '10', 10);
    const sleepWindow = parseInt(process.env['CIRCUIT_BREAKER_SLEEP_WINDOW'] || '30000', 10);
    const maxRetries = parseInt(process.env['CIRCUIT_BREAKER_MAX_RETRIES'] || '3', 10);
    const enableMetrics = process.env['CIRCUIT_BREAKER_ENABLE_METRICS'] !== 'false';
    const enableLogging = process.env['CIRCUIT_BREAKER_ENABLE_LOGGING'] !== 'false';

    return {
      defaultConfig: {
        name: 'default',
        enabled,
        failureThreshold,
        timeout,
        requestVolumeThreshold,
        sleepWindowInMilliseconds: sleepWindow,
        maxRetries,
      },
      circuitBreakers: {
        [DEFAULT_CIRCUIT_BREAKERS.AUTH_SERVICE]: {
          enabled: process.env['CIRCUIT_BREAKER_AUTH_ENABLED'] !== 'false',
          failureThreshold: parseInt(process.env['CIRCUIT_BREAKER_AUTH_FAILURE_THRESHOLD'] || '50', 10),
          timeout: parseInt(process.env['CIRCUIT_BREAKER_AUTH_TIMEOUT'] || '30000', 10),
        },
        [DEFAULT_CIRCUIT_BREAKERS.USER_SERVICE]: {
          enabled: process.env['CIRCUIT_BREAKER_USER_ENABLED'] !== 'false',
          failureThreshold: parseInt(process.env['CIRCUIT_BREAKER_USER_FAILURE_THRESHOLD'] || '50', 10),
          timeout: parseInt(process.env['CIRCUIT_BREAKER_USER_TIMEOUT'] || '30000', 10),
        },
        [DEFAULT_CIRCUIT_BREAKERS.TENANT_SERVICE]: {
          enabled: process.env['CIRCUIT_BREAKER_TENANT_ENABLED'] !== 'false',
          failureThreshold: parseInt(process.env['CIRCUIT_BREAKER_TENANT_FAILURE_THRESHOLD'] || '50', 10),
          timeout: parseInt(process.env['CIRCUIT_BREAKER_TENANT_TIMEOUT'] || '30000', 10),
        },
        [DEFAULT_CIRCUIT_BREAKERS.NOTIFICATION_SERVICE]: {
          enabled: process.env['CIRCUIT_BREAKER_NOTIFICATION_ENABLED'] !== 'false',
          failureThreshold: parseInt(process.env['CIRCUIT_BREAKER_NOTIFICATION_FAILURE_THRESHOLD'] || '50', 10),
          timeout: parseInt(process.env['CIRCUIT_BREAKER_NOTIFICATION_TIMEOUT'] || '30000', 10),
        },
      },
      enableMetrics,
      enableLogging,
      globalTimeout: timeout,
    };
  }
}
