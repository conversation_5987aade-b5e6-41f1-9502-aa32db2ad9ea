import {
    CallH<PERSON><PERSON>,
    ExecutionContext,
    Injectable,
    Logger,
    NestInterceptor,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, from } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { CircuitBreakerService } from '../circuit-breaker.service';
import {
    CIRCUIT_BREAKER_KEY,
    CircuitBreakerDecoratorOptions,
} from '../decorators/circuit-breaker.decorator';

@Injectable()
export class CircuitBreakerInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CircuitBreakerInterceptor.name);

  constructor(
    private readonly circuitBreakerService: CircuitBreakerService,
    private readonly reflector: Reflector
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const circuitBreakerOptions = this.reflector.get<CircuitBreakerDecoratorOptions>(
      CIRCUIT_BREAKER_KEY,
      context.getHandler()
    );

    if (!circuitBreakerOptions) {
      return next.handle();
    }

    const { name, fallbackMethod, enabled = true } = circuitBreakerOptions;

    if (!enabled) {
      return next.handle();
    }

    const instance = context.getClass();
    const methodName = context.getHandler().name;

    return from(
      this.circuitBreakerService.execute(name, {
        execute: async () => {
          const result = await next.handle().toPromise();
          return result;
        },
        fallback: fallbackMethod && instance.prototype[fallbackMethod]
          ? async () => {
              this.logger.log(`Executing fallback method: ${fallbackMethod}`);
              return instance.prototype[fallbackMethod]();
            }
          : undefined,
      })
    ).pipe(
      switchMap(async (result) => {
        if (!result.success) {
          if (result.executedFallback) {
            this.logger.warn(
              `Circuit breaker ${name} executed fallback for ${methodName}`
            );
            return result.data;
          } else {
            throw result.error;
          }
        }
        return result.data;
      })
    );
  }
}
