import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { CircuitBreakerService } from './circuit-breaker.service';
import { CircuitBreakerConfigService } from './circuit-breaker-config.service';

@Injectable()
export class CircuitBreakerInitService implements OnModuleInit {
  private readonly logger = new Logger(CircuitBreakerInitService.name);

  constructor(
    private readonly circuitBreakerService: CircuitBreakerService,
    private readonly configService: CircuitBreakerConfigService
  ) {}

  async onModuleInit(): Promise<void> {
    this.logger.log('Initializing circuit breakers...');
    
    try {
      // Get all configured circuit breaker names
      const circuitBreakerNames = this.configService.getCircuitBreakerNames();
      
      // Register each circuit breaker with its configuration
      for (const name of circuitBreakerNames) {
        const config = this.configService.getConfig(name);
        this.circuitBreakerService.registerCircuitBreaker(name, config);
        
        this.logger.log(`Registered circuit breaker: ${name} (enabled: ${config.enabled})`);
      }
      
      this.logger.log(`Successfully initialized ${circuitBreakerNames.length} circuit breakers`);
    } catch (error) {
      this.logger.error('Failed to initialize circuit breakers', error);
      throw error;
    }
  }
}
