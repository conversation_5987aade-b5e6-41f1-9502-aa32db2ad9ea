import { Injectable, Logger } from '@nestjs/common';
import { CircuitBreakerService } from './circuit-breaker.service';
import {
  CircuitBreakerHealthCheck,
  CircuitBreakerState,
  CircuitBreakerStats,
} from './interfaces/circuit-breaker-options.interface';

export interface CircuitBreakerHealthSummary {
  overall: {
    healthy: boolean;
    totalCircuitBreakers: number;
    openCircuitBreakers: number;
    halfOpenCircuitBreakers: number;
    closedCircuitBreakers: number;
  };
  circuitBreakers: CircuitBreakerHealthCheck[];
  timestamp: string;
}

@Injectable()
export class CircuitBreakerHealthService {
  private readonly logger = new Logger(CircuitBreakerHealthService.name);

  constructor(private readonly circuitBreakerService: CircuitBreakerService) {}

  /**
   * Get comprehensive health check for all circuit breakers
   */
  getHealthSummary(): CircuitBreakerHealthSummary {
    const circuitBreakers = this.circuitBreakerService.getHealthChecks();
    const stats = this.circuitBreakerService.getAllStats();

    const openCount = stats.filter(s => s.state === CircuitBreakerState.OPEN).length;
    const halfOpenCount = stats.filter(s => s.state === CircuitBreakerState.HALF_OPEN).length;
    const closedCount = stats.filter(s => s.state === CircuitBreakerState.CLOSED).length;

    // Consider system healthy if no circuit breakers are open
    const overall = {
      healthy: openCount === 0,
      totalCircuitBreakers: stats.length,
      openCircuitBreakers: openCount,
      halfOpenCircuitBreakers: halfOpenCount,
      closedCircuitBreakers: closedCount,
    };

    return {
      overall,
      circuitBreakers,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Get health status for a specific circuit breaker
   */
  getCircuitBreakerHealth(name: string): CircuitBreakerHealthCheck | null {
    const healthChecks = this.circuitBreakerService.getHealthChecks();
    return healthChecks.find(hc => hc.name === name) || null;
  }

  /**
   * Check if any circuit breakers are in unhealthy state
   */
  hasUnhealthyCircuitBreakers(): boolean {
    const stats = this.circuitBreakerService.getAllStats();
    return stats.some(stat => stat.state === CircuitBreakerState.OPEN);
  }

  /**
   * Get circuit breakers that are currently open
   */
  getOpenCircuitBreakers(): CircuitBreakerStats[] {
    return this.circuitBreakerService
      .getAllStats()
      .filter(stat => stat.state === CircuitBreakerState.OPEN);
  }

  /**
   * Get circuit breakers with high failure rates (above threshold)
   */
  getHighFailureRateCircuitBreakers(threshold = 25): CircuitBreakerStats[] {
    return this.circuitBreakerService
      .getAllStats()
      .filter(stat => stat.failureRate >= threshold && stat.totalRequests > 0);
  }

  /**
   * Reset all circuit breakers (admin operation)
   */
  resetAllCircuitBreakers(): { success: boolean; message: string; timestamp: string } {
    try {
      this.circuitBreakerService.resetAll();
      this.logger.log('All circuit breakers have been reset via health service');
      
      return {
        success: true,
        message: 'All circuit breakers have been reset successfully',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to reset circuit breakers', error);
      
      return {
        success: false,
        message: `Failed to reset circuit breakers: ${(error as Error).message}`,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Reset a specific circuit breaker
   */
  resetCircuitBreaker(name: string): { success: boolean; message: string; timestamp: string } {
    try {
      const success = this.circuitBreakerService.reset(name);
      
      if (success) {
        this.logger.log(`Circuit breaker ${name} has been reset via health service`);
        return {
          success: true,
          message: `Circuit breaker ${name} has been reset successfully`,
          timestamp: new Date().toISOString(),
        };
      } else {
        return {
          success: false,
          message: `Circuit breaker ${name} not found`,
          timestamp: new Date().toISOString(),
        };
      }
    } catch (error) {
      this.logger.error(`Failed to reset circuit breaker ${name}`, error);
      
      return {
        success: false,
        message: `Failed to reset circuit breaker ${name}: ${(error as Error).message}`,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Get detailed metrics for monitoring systems
   */
  getMetricsForMonitoring(): Record<string, any> {
    const stats = this.circuitBreakerService.getAllStats();
    const summary = this.getHealthSummary();

    return {
      circuit_breaker_total_count: summary.overall.totalCircuitBreakers,
      circuit_breaker_open_count: summary.overall.openCircuitBreakers,
      circuit_breaker_half_open_count: summary.overall.halfOpenCircuitBreakers,
      circuit_breaker_closed_count: summary.overall.closedCircuitBreakers,
      circuit_breaker_overall_healthy: summary.overall.healthy,
      circuit_breakers: stats.reduce((acc, stat) => {
        acc[stat.name] = {
          state: stat.state,
          failure_rate: stat.failureRate,
          total_requests: stat.totalRequests,
          success_count: stat.successCount,
          failure_count: stat.failureCount,
          enabled: stat.isEnabled,
        };
        return acc;
      }, {} as Record<string, any>),
      timestamp: new Date().toISOString(),
    };
  }
}
