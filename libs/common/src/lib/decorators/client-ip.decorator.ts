import { createParamDecorator, ExecutionContext } from '@nestjs/common';

export const ClientIp = createParamDecorator((_data: unknown, ctx: ExecutionContext): string => {
  const req = ctx.switchToHttp().getRequest();

  const forwarded = req.headers['x-forwarded-for'];
  if (typeof forwarded === 'string') {
    return forwarded.split(',')[0].trim();
  }

  const realIp = req.headers['x-real-ip'];
  if (typeof realIp === 'string') {
    return realIp;
  }

  const clientIp = req.headers['x-client-ip'];
  if (typeof clientIp === 'string') {
    return clientIp;
  }

  return req.connection?.remoteAddress || req.socket?.remoteAddress || req.ip || 'unknown';
});
