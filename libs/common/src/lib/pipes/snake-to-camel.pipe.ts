/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable, PipeTransform } from '@nestjs/common';
import { camelCase, mapKeys } from 'lodash';

@Injectable()
export class SnakeToCamelPipe implements PipeTransform {
  private toCamel(obj: any): any {
    if (Array.isArray(obj)) {
      return obj.map((v) => this.toCamel(v));
    } else if (typeof obj === 'object' && obj !== null && obj.constructor === Object) {
      return mapKeys(obj, (_: any, key: any) => camelCase(key));
    }
    return obj;
  }

  transform(value: any) {
    return this.toCamel(value);
  }
}
