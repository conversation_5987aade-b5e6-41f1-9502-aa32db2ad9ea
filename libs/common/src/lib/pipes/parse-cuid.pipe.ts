/* eslint-disable @typescript-eslint/no-explicit-any */
import { ArgumentMetadata, BadRequestException, Injectable, PipeTransform } from '@nestjs/common';
import { isValidCuid } from '../utils/cuid.util';

/**
 * Parse CUID Pipe
 *
 * Validates that a string parameter is a valid CUID with optional prefix validation.
 * Used as a replacement for ParseUUIDPipe when working with CUIDs.
 *
 * Usage:
 * - @Param('id', ParseCuidPipe) id: string
 * - @Param('customerId', new ParseCuidPipe('cus_')) customerId: string
 */
@Injectable()
export class ParseCuidPipe implements PipeTransform<string, string> {
  constructor(private readonly expectedPrefix?: string) {}

  /**
   * Transform and validate CUID parameter
   *
   * @param value The parameter value to validate
   * @param metadata Argument metadata (not used)
   * @returns The validated CUID string
   * @throws BadRequestException if validation fails
   */
  transform(data: any, metadata: ArgumentMetadata): string {
    console.log({ data });
    const { id: value } = data;

    if (!value || typeof value !== 'string') {
      throw new BadRequestException(`Validation failed (${metadata.data || 'parameter'} must be a valid CUID)`);
    }

    // Validate CUID format with optional prefix check
    if (!isValidCuid(value, this.expectedPrefix)) {
      const expectedFormat = this.expectedPrefix ? `CUID with prefix "${this.expectedPrefix}"` : 'valid CUID';

      throw new BadRequestException(`Validation failed (${metadata.data || 'parameter'} must be a ${expectedFormat})`);
    }

    return value;
  }
}
