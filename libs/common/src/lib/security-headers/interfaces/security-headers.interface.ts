export interface SecurityHeadersConfig {
  // Strict Transport Security
  hsts?: {
    enabled: boolean;
    maxAge: number; // seconds
    includeSubDomains: boolean;
    preload: boolean;
  };

  // Content Security Policy
  csp?: {
    enabled: boolean;
    directives: {
      defaultSrc?: string[];
      scriptSrc?: string[];
      styleSrc?: string[];
      imgSrc?: string[];
      connectSrc?: string[];
      fontSrc?: string[];
      objectSrc?: string[];
      mediaSrc?: string[];
      frameSrc?: string[];
      childSrc?: string[];
      workerSrc?: string[];
      manifestSrc?: string[];
      baseUri?: string[];
      formAction?: string[];
      frameAncestors?: string[];
      upgradeInsecureRequests?: boolean;
      blockAllMixedContent?: boolean;
    };
    reportUri?: string;
    reportOnly?: boolean;
  };

  // X-Frame-Options
  frameOptions?: {
    enabled: boolean;
    policy: 'DENY' | 'SAMEORIGIN' | 'ALLOW-FROM';
    allowFrom?: string;
  };

  // X-Content-Type-Options
  contentTypeOptions?: {
    enabled: boolean;
    nosniff: boolean;
  };

  // X-XSS-Protection
  xssProtection?: {
    enabled: boolean;
    mode: '0' | '1' | '1; mode=block';
    reportUri?: string;
  };

  // Referrer Policy
  referrerPolicy?: {
    enabled: boolean;
    policy: 'no-referrer' | 'no-referrer-when-downgrade' | 'origin' | 'origin-when-cross-origin' | 
            'same-origin' | 'strict-origin' | 'strict-origin-when-cross-origin' | 'unsafe-url';
  };

  // Permissions Policy (formerly Feature Policy)
  permissionsPolicy?: {
    enabled: boolean;
    directives: {
      camera?: string[];
      microphone?: string[];
      geolocation?: string[];
      payment?: string[];
      usb?: string[];
      accelerometer?: string[];
      gyroscope?: string[];
      magnetometer?: string[];
      fullscreen?: string[];
      displayCapture?: string[];
    };
  };

  // Cross-Origin Embedder Policy
  coep?: {
    enabled: boolean;
    policy: 'unsafe-none' | 'require-corp' | 'credentialless';
  };

  // Cross-Origin Opener Policy
  coop?: {
    enabled: boolean;
    policy: 'unsafe-none' | 'same-origin-allow-popups' | 'same-origin';
  };

  // Cross-Origin Resource Policy
  corp?: {
    enabled: boolean;
    policy: 'same-site' | 'same-origin' | 'cross-origin';
  };

  // Additional custom headers
  customHeaders?: Record<string, string>;

  // Environment-specific overrides
  development?: Partial<SecurityHeadersConfig>;
  production?: Partial<SecurityHeadersConfig>;
  test?: Partial<SecurityHeadersConfig>;
}

export interface SecurityHeadersMetrics {
  headersApplied: number;
  violationsReported: number;
  lastViolationTime?: Date;
  headersByType: Record<string, number>;
}

export interface SecurityViolationReport {
  type: 'csp' | 'hsts' | 'xss' | 'frame' | 'content-type';
  message: string;
  sourceFile?: string;
  lineNumber?: number;
  columnNumber?: number;
  blockedUri?: string;
  violatedDirective?: string;
  originalPolicy?: string;
  userAgent: string;
  timestamp: Date;
  requestId?: string;
}
