import { SecurityHeadersMetrics } from './security-headers.interface';

export interface SecurityHeadersHealthStatus {
  healthy: boolean;
  status: 'healthy' | 'warning' | 'error';
  message: string;
  details: {
    configurationValid: boolean;
    configurationErrors: string[];
    enabledHeaders: string[];
    disabledHeaders: string[];
    metrics: SecurityHeadersMetrics;
    environment: string;
    lastCheck: Date;
  };
}

export interface SecurityHeadersHealthSummary {
  totalHeaders: number;
  enabledHeaders: number;
  disabledHeaders: number;
  configurationValid: boolean;
  environment: string;
}

export interface SecurityHeadersTestResult {
  success: boolean;
  headers: Record<string, string>;
  errors: string[];
}
