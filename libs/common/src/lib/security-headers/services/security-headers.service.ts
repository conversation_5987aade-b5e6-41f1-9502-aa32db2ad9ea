import { Inject, Injectable, Logger } from '@nestjs/common';
import { Request, Response } from 'express';
import { SECURITY_HEADERS_CONFIG_TOKEN } from '../config/security-headers.config';
import { SecurityHeadersConfig, SecurityHeadersMetrics } from '../interfaces/security-headers.interface';

@Injectable()
export class SecurityHeadersService {
  private readonly logger = new Logger(SecurityHeadersService.name);
  private metrics: SecurityHeadersMetrics = {
    headersApplied: 0,
    violationsReported: 0,
    headersByType: {},
  };

  constructor(
    @Inject(SECURITY_HEADERS_CONFIG_TOKEN)
    private readonly config: SecurityHeadersConfig
  ) {}

  /**
   * Apply security headers to response
   */
  applyHeaders(req: Request, res: Response): void {
    try {
      const environment = process.env['NODE_ENV'] || 'development';
      const effectiveConfig = this.mergeEnvironmentConfig(environment);

      this.applyHSTS(res, effectiveConfig);
      this.applyCSP(res, effectiveConfig);
      this.applyFrameOptions(res, effectiveConfig);
      this.applyContentTypeOptions(res, effectiveConfig);
      this.applyXSSProtection(res, effectiveConfig);
      this.applyReferrerPolicy(res, effectiveConfig);
      this.applyPermissionsPolicy(res, effectiveConfig);
      this.applyCOEP(res, effectiveConfig);
      this.applyCOOP(res, effectiveConfig);
      this.applyCORP(res, effectiveConfig);
      this.applyCustomHeaders(res, effectiveConfig);

      this.updateMetrics();
      
      this.logger.debug(`Applied security headers for ${req.method} ${req.path}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Failed to apply security headers: ${errorMessage}`, errorStack);
    }
  }

  /**
   * Apply Strict Transport Security header
   */
  private applyHSTS(res: Response, config: SecurityHeadersConfig): void {
    if (!config.hsts?.enabled) return;

    const { maxAge, includeSubDomains, preload } = config.hsts;
    let hstsValue = `max-age=${maxAge}`;
    
    if (includeSubDomains) {
      hstsValue += '; includeSubDomains';
    }
    
    if (preload) {
      hstsValue += '; preload';
    }

    res.setHeader('Strict-Transport-Security', hstsValue);
    this.incrementHeaderMetric('hsts');
  }

  /**
   * Apply Content Security Policy header
   */
  private applyCSP(res: Response, config: SecurityHeadersConfig): void {
    if (!config.csp?.enabled) return;

    const { directives, reportUri, reportOnly } = config.csp;
    const cspParts: string[] = [];

    // Build CSP directive string
    Object.entries(directives).forEach(([directive, values]) => {
      if (directive === 'upgradeInsecureRequests' && values === true) {
        cspParts.push('upgrade-insecure-requests');
      } else if (directive === 'blockAllMixedContent' && values === true) {
        cspParts.push('block-all-mixed-content');
      } else if (Array.isArray(values) && values.length > 0) {
        const kebabDirective = this.camelToKebab(directive);
        cspParts.push(`${kebabDirective} ${values.join(' ')}`);
      }
    });

    if (reportUri) {
      cspParts.push(`report-uri ${reportUri}`);
    }

    const cspValue = cspParts.join('; ');
    const headerName = reportOnly ? 'Content-Security-Policy-Report-Only' : 'Content-Security-Policy';
    
    res.setHeader(headerName, cspValue);
    this.incrementHeaderMetric('csp');
  }

  /**
   * Apply X-Frame-Options header
   */
  private applyFrameOptions(res: Response, config: SecurityHeadersConfig): void {
    if (!config.frameOptions?.enabled) return;

    const { policy, allowFrom } = config.frameOptions;
    let frameValue: string = policy;

    if (policy === 'ALLOW-FROM' && allowFrom) {
      frameValue = `ALLOW-FROM ${allowFrom}`;
    }

    res.setHeader('X-Frame-Options', frameValue);
    this.incrementHeaderMetric('frame-options');
  }

  /**
   * Apply X-Content-Type-Options header
   */
  private applyContentTypeOptions(res: Response, config: SecurityHeadersConfig): void {
    if (!config.contentTypeOptions?.enabled) return;

    if (config.contentTypeOptions.nosniff) {
      res.setHeader('X-Content-Type-Options', 'nosniff');
      this.incrementHeaderMetric('content-type-options');
    }
  }

  /**
   * Apply X-XSS-Protection header
   */
  private applyXSSProtection(res: Response, config: SecurityHeadersConfig): void {
    if (!config.xssProtection?.enabled) return;

    const { mode, reportUri } = config.xssProtection;
    let xssValue = mode;

    if (reportUri && mode.includes('1')) {
      xssValue += `; report=${reportUri}`;
    }

    res.setHeader('X-XSS-Protection', xssValue);
    this.incrementHeaderMetric('xss-protection');
  }

  /**
   * Apply Referrer-Policy header
   */
  private applyReferrerPolicy(res: Response, config: SecurityHeadersConfig): void {
    if (!config.referrerPolicy?.enabled) return;

    res.setHeader('Referrer-Policy', config.referrerPolicy.policy);
    this.incrementHeaderMetric('referrer-policy');
  }

  /**
   * Apply Permissions-Policy header
   */
  private applyPermissionsPolicy(res: Response, config: SecurityHeadersConfig): void {
    if (!config.permissionsPolicy?.enabled) return;

    const { directives } = config.permissionsPolicy;
    const policyParts: string[] = [];

    Object.entries(directives).forEach(([feature, allowlist]) => {
      if (Array.isArray(allowlist)) {
        const kebabFeature = this.camelToKebab(feature);
        if (allowlist.length === 0) {
          policyParts.push(`${kebabFeature}=()`);
        } else {
          policyParts.push(`${kebabFeature}=(${allowlist.join(' ')})`);
        }
      }
    });

    if (policyParts.length > 0) {
      res.setHeader('Permissions-Policy', policyParts.join(', '));
      this.incrementHeaderMetric('permissions-policy');
    }
  }

  /**
   * Apply Cross-Origin-Embedder-Policy header
   */
  private applyCOEP(res: Response, config: SecurityHeadersConfig): void {
    if (!config.coep?.enabled) return;

    res.setHeader('Cross-Origin-Embedder-Policy', config.coep.policy);
    this.incrementHeaderMetric('coep');
  }

  /**
   * Apply Cross-Origin-Opener-Policy header
   */
  private applyCOOP(res: Response, config: SecurityHeadersConfig): void {
    if (!config.coop?.enabled) return;

    res.setHeader('Cross-Origin-Opener-Policy', config.coop.policy);
    this.incrementHeaderMetric('coop');
  }

  /**
   * Apply Cross-Origin-Resource-Policy header
   */
  private applyCORP(res: Response, config: SecurityHeadersConfig): void {
    if (!config.corp?.enabled) return;

    res.setHeader('Cross-Origin-Resource-Policy', config.corp.policy);
    this.incrementHeaderMetric('corp');
  }

  /**
   * Apply custom headers
   */
  private applyCustomHeaders(res: Response, config: SecurityHeadersConfig): void {
    if (!config.customHeaders) return;

    Object.entries(config.customHeaders).forEach(([name, value]) => {
      res.setHeader(name, value);
      this.incrementHeaderMetric('custom');
    });
  }

  /**
   * Merge environment-specific configuration
   */
  private mergeEnvironmentConfig(environment: string): SecurityHeadersConfig {
    const envConfig = this.config[environment as keyof SecurityHeadersConfig] as Partial<SecurityHeadersConfig>;
    if (!envConfig) return this.config;

    return this.deepMerge(this.config, envConfig);
  }

  /**
   * Deep merge configuration objects
   */
  private deepMerge(target: any, source: any): any {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(target[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }

  /**
   * Convert camelCase to kebab-case
   */
  private camelToKebab(str: string): string {
    return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
  }

  /**
   * Update metrics
   */
  private updateMetrics(): void {
    this.metrics.headersApplied++;
  }

  /**
   * Increment header-specific metrics
   */
  private incrementHeaderMetric(headerType: string): void {
    this.metrics.headersByType[headerType] = (this.metrics.headersByType[headerType] || 0) + 1;
  }

  /**
   * Get current metrics
   */
  getMetrics(): SecurityHeadersMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset metrics
   */
  resetMetrics(): void {
    this.metrics = {
      headersApplied: 0,
      violationsReported: 0,
      headersByType: {},
    };
  }

  /**
   * Get effective configuration for current environment
   */
  getEffectiveConfig(): SecurityHeadersConfig {
    const environment = process.env['NODE_ENV'] || 'development';
    return this.mergeEnvironmentConfig(environment);
  }

  /**
   * Validate configuration
   */
  validateConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate HSTS
    if (this.config.hsts?.enabled && this.config.hsts.maxAge < 0) {
      errors.push('HSTS maxAge must be non-negative');
    }

    // Validate CSP
    if (this.config.csp?.enabled && !this.config.csp.directives.defaultSrc) {
      errors.push('CSP must include default-src directive');
    }

    // Validate Frame Options
    if (this.config.frameOptions?.enabled &&
        this.config.frameOptions.policy === 'ALLOW-FROM' &&
        !this.config.frameOptions.allowFrom) {
      errors.push('Frame Options ALLOW-FROM policy requires allowFrom URL');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}
