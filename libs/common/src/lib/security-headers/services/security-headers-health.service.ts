import { Injectable, Logger } from '@nestjs/common';
import { Request, Response } from 'express';
import {
    SecurityHeadersHealthStatus,
    SecurityHeadersHealthSummary,
    SecurityHeadersTestResult
} from '../interfaces/security-headers-health.interface';
import { SecurityHeadersConfig } from '../interfaces/security-headers.interface';
import { SecurityHeadersService } from './security-headers.service';

@Injectable()
export class SecurityHeadersHealthService {
  private readonly logger = new Logger(SecurityHeadersHealthService.name);

  constructor(private readonly securityHeadersService: SecurityHeadersService) {}

  /**
   * Get comprehensive health status of security headers
   */
  getHealthStatus(): SecurityHeadersHealthStatus {
    try {
      const config = this.securityHeadersService.getEffectiveConfig();
      const validation = this.securityHeadersService.validateConfig();
      const metrics = this.securityHeadersService.getMetrics();
      const environment = process.env['NODE_ENV'] || 'development';

      const enabledHeaders = this.getEnabledHeaders(config);
      const disabledHeaders = this.getDisabledHeaders(config);

      // Determine overall health status
      const { healthy, status, message } = this.determineHealthStatus(
        validation,
        enabledHeaders,
        environment
      );

      return {
        healthy,
        status,
        message,
        details: {
          configurationValid: validation.valid,
          configurationErrors: validation.errors,
          enabledHeaders,
          disabledHeaders,
          metrics,
          environment,
          lastCheck: new Date(),
        },
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Failed to get security headers health status: ${errorMessage}`, errorStack);
      
      return {
        healthy: false,
        status: 'error',
        message: `Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: {
          configurationValid: false,
          configurationErrors: [error instanceof Error ? error.message : 'Unknown error'],
          enabledHeaders: [],
          disabledHeaders: [],
          metrics: {
            headersApplied: 0,
            violationsReported: 0,
            headersByType: {},
          },
          environment: process.env['NODE_ENV'] || 'development',
          lastCheck: new Date(),
        },
      };
    }
  }

  /**
   * Get list of enabled security headers
   */
  private getEnabledHeaders(config: SecurityHeadersConfig): string[] {
    const enabled: string[] = [];

    if (config.hsts?.enabled) enabled.push('Strict-Transport-Security');
    if (config.csp?.enabled) enabled.push('Content-Security-Policy');
    if (config.frameOptions?.enabled) enabled.push('X-Frame-Options');
    if (config.contentTypeOptions?.enabled) enabled.push('X-Content-Type-Options');
    if (config.xssProtection?.enabled) enabled.push('X-XSS-Protection');
    if (config.referrerPolicy?.enabled) enabled.push('Referrer-Policy');
    if (config.permissionsPolicy?.enabled) enabled.push('Permissions-Policy');
    if (config.coep?.enabled) enabled.push('Cross-Origin-Embedder-Policy');
    if (config.coop?.enabled) enabled.push('Cross-Origin-Opener-Policy');
    if (config.corp?.enabled) enabled.push('Cross-Origin-Resource-Policy');
    if (config.customHeaders && Object.keys(config.customHeaders).length > 0) {
      enabled.push(...Object.keys(config.customHeaders));
    }

    return enabled;
  }

  /**
   * Get list of disabled security headers
   */
  private getDisabledHeaders(config: SecurityHeadersConfig): string[] {
    const disabled: string[] = [];

    if (!config.hsts?.enabled) disabled.push('Strict-Transport-Security');
    if (!config.csp?.enabled) disabled.push('Content-Security-Policy');
    if (!config.frameOptions?.enabled) disabled.push('X-Frame-Options');
    if (!config.contentTypeOptions?.enabled) disabled.push('X-Content-Type-Options');
    if (!config.xssProtection?.enabled) disabled.push('X-XSS-Protection');
    if (!config.referrerPolicy?.enabled) disabled.push('Referrer-Policy');
    if (!config.permissionsPolicy?.enabled) disabled.push('Permissions-Policy');
    if (!config.coep?.enabled) disabled.push('Cross-Origin-Embedder-Policy');
    if (!config.coop?.enabled) disabled.push('Cross-Origin-Opener-Policy');
    if (!config.corp?.enabled) disabled.push('Cross-Origin-Resource-Policy');

    return disabled;
  }

  /**
   * Determine overall health status based on configuration and environment
   */
  private determineHealthStatus(
    validation: { valid: boolean; errors: string[] },
    enabledHeaders: string[],
    environment: string
  ): { healthy: boolean; status: 'healthy' | 'warning' | 'error'; message: string } {
    // Configuration errors are critical
    if (!validation.valid) {
      return {
        healthy: false,
        status: 'error',
        message: `Configuration errors: ${validation.errors.join(', ')}`,
      };
    }

    // Check for essential security headers
    const essentialHeaders = [
      'X-Content-Type-Options',
      'X-Frame-Options',
      'X-XSS-Protection',
      'Referrer-Policy',
    ];

    const missingEssential = essentialHeaders.filter(header => !enabledHeaders.includes(header));

    if (missingEssential.length > 0) {
      return {
        healthy: false,
        status: 'warning',
        message: `Missing essential security headers: ${missingEssential.join(', ')}`,
      };
    }

    // Production-specific checks
    if (environment === 'production') {
      if (!enabledHeaders.includes('Strict-Transport-Security')) {
        return {
          healthy: false,
          status: 'warning',
          message: 'HSTS should be enabled in production',
        };
      }

      if (!enabledHeaders.includes('Content-Security-Policy')) {
        return {
          healthy: false,
          status: 'warning',
          message: 'CSP should be enabled in production',
        };
      }
    }

    // All checks passed
    return {
      healthy: true,
      status: 'healthy',
      message: `Security headers configured correctly (${enabledHeaders.length} enabled)`,
    };
  }

  /**
   * Get security headers summary for monitoring
   */
  getSummary(): SecurityHeadersHealthSummary {
    const config = this.securityHeadersService.getEffectiveConfig();
    const validation = this.securityHeadersService.validateConfig();
    const enabledHeaders = this.getEnabledHeaders(config);
    const disabledHeaders = this.getDisabledHeaders(config);

    return {
      totalHeaders: enabledHeaders.length + disabledHeaders.length,
      enabledHeaders: enabledHeaders.length,
      disabledHeaders: disabledHeaders.length,
      configurationValid: validation.valid,
      environment: process.env['NODE_ENV'] || 'development',
    };
  }

  /**
   * Test security headers by simulating a request
   */
  async testHeaders(): Promise<SecurityHeadersTestResult> {
    try {
      const mockReq = { method: 'GET', path: '/test' } as unknown as Request;
      const headers: Record<string, string> = {};

      const mockRes = {
        setHeader: (name: string, value: string) => {
          headers[name] = value;
        },
      } as unknown as Response;

      this.securityHeadersService.applyHeaders(mockReq, mockRes);

      return {
        success: true,
        headers,
        errors: [],
      };
    } catch (error) {
      return {
        success: false,
        headers: {},
        errors: [error instanceof Error ? error.message : 'Unknown error'],
      };
    }
  }
}
