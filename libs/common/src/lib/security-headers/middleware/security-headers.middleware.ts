import { Injectable, Logger, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';
import { SecurityHeadersService } from '../services/security-headers.service';

@Injectable()
export class SecurityHeadersMiddleware implements NestMiddleware {
  private readonly logger = new Logger(SecurityHeadersMiddleware.name);

  constructor(private readonly securityHeadersService: SecurityHeadersService) {}

  use(req: Request, res: Response, next: NextFunction): void {
    try {
      // Apply security headers to the response
      this.securityHeadersService.applyHeaders(req, res);
      
      // Log security headers application for debugging
      if (process.env['NODE_ENV'] === 'development') {
        this.logger.debug(`Security headers applied for ${req.method} ${req.path}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Failed to apply security headers: ${errorMessage}`, errorStack);
      // Continue with request even if security headers fail
    }

    next();
  }
}

/**
 * Functional middleware for applying security headers
 * Can be used as an alternative to the class-based middleware
 */
export function createSecurityHeadersMiddleware(securityHeadersService: SecurityHeadersService) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      securityHeadersService.applyHeaders(req, res);
    } catch (error) {
      console.error('Failed to apply security headers:', error);
    }
    next();
  };
}
