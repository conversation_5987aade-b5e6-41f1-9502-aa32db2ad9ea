import { SecurityHeadersConfig } from '../interfaces/security-headers.interface';

export const DEFAULT_SECURITY_HEADERS_CONFIG: SecurityHeadersConfig = {
  // Strict Transport Security - Force HTTPS
  hsts: {
    enabled: true,
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true,
  },

  // Content Security Policy - Prevent XSS and injection attacks
  csp: {
    enabled: true,
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", 'https://cdn.jsdelivr.net'],
      styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
      imgSrc: ["'self'", 'data:', 'https:'],
      connectSrc: ["'self'", 'https://api.auth0.com'],
      fontSrc: ["'self'", 'https://fonts.gstatic.com'],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
      childSrc: ["'none'"],
      workerSrc: ["'self'"],
      manifestSrc: ["'self'"],
      baseUri: ["'self'"],
      formAction: ["'self'"],
      frameAncestors: ["'none'"],
      upgradeInsecureRequests: true,
      blockAllMixedContent: true,
    },
    reportOnly: false,
  },

  // X-Frame-Options - Prevent clickjacking
  frameOptions: {
    enabled: true,
    policy: 'DENY',
  },

  // X-Content-Type-Options - Prevent MIME type sniffing
  contentTypeOptions: {
    enabled: true,
    nosniff: true,
  },

  // X-XSS-Protection - Enable XSS filtering
  xssProtection: {
    enabled: true,
    mode: '1; mode=block',
  },

  // Referrer Policy - Control referrer information
  referrerPolicy: {
    enabled: true,
    policy: 'strict-origin-when-cross-origin',
  },

  // Permissions Policy - Control browser features
  permissionsPolicy: {
    enabled: true,
    directives: {
      camera: [],
      microphone: [],
      geolocation: [],
      payment: [],
      usb: [],
      accelerometer: [],
      gyroscope: [],
      magnetometer: [],
      fullscreen: ["'self'"],
      displayCapture: [],
    },
  },

  // Cross-Origin Embedder Policy
  coep: {
    enabled: false, // Disabled by default as it can break functionality
    policy: 'unsafe-none',
  },

  // Cross-Origin Opener Policy
  coop: {
    enabled: true,
    policy: 'same-origin-allow-popups',
  },

  // Cross-Origin Resource Policy
  corp: {
    enabled: true,
    policy: 'same-site',
  },

  // Environment-specific overrides
  development: {
    csp: {
      enabled: true,
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", 'localhost:*', '127.0.0.1:*'],
        styleSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", 'data:', 'https:', 'http:'],
        connectSrc: ["'self'", 'localhost:*', '127.0.0.1:*', 'ws:', 'wss:'],
        fontSrc: ["'self'", 'data:'],
      },
      reportOnly: true, // Use report-only mode in development
    },
    hsts: {
      enabled: false, // Disable HSTS in development (HTTP)
      maxAge: 0,
      includeSubDomains: false,
      preload: false,
    },
  },

  production: {
    csp: {
      enabled: true,
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'"],
        styleSrc: ["'self'", 'https://fonts.googleapis.com'],
        imgSrc: ["'self'", 'data:', 'https:'],
        connectSrc: ["'self'", 'https://api.auth0.com'],
        fontSrc: ["'self'", 'https://fonts.gstatic.com'],
        objectSrc: ["'none'"],
        upgradeInsecureRequests: true,
        blockAllMixedContent: true,
      },
      reportOnly: false,
    },
    hsts: {
      enabled: true,
      maxAge: 63072000, // 2 years
      includeSubDomains: true,
      preload: true,
    },
  },

  test: {
    csp: {
      enabled: false, // Disable CSP in tests to avoid interference
      directives: {
        defaultSrc: ["'self'"],
      },
    },
    hsts: {
      enabled: false,
      maxAge: 0,
      includeSubDomains: false,
      preload: false,
    },
  },
};

export const SECURITY_HEADERS_CONFIG_TOKEN = 'SECURITY_HEADERS_CONFIG';
