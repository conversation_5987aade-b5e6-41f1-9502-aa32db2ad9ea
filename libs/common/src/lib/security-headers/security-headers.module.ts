import { DynamicModule, MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { DEFAULT_SECURITY_HEADERS_CONFIG, SECURITY_HEADERS_CONFIG_TOKEN } from './config/security-headers.config';
import { SecurityHeadersConfig } from './interfaces/security-headers.interface';
import { SecurityHeadersMiddleware } from './middleware/security-headers.middleware';
import { SecurityHeadersHealthService } from './services/security-headers-health.service';
import { SecurityHeadersService } from './services/security-headers.service';

@Module({})
export class SecurityHeadersModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(SecurityHeadersMiddleware)
      .forRoutes('*'); // Apply to all routes
  }

  static forRoot(config?: Partial<SecurityHeadersConfig>): DynamicModule {
    const mergedConfig = {
      ...DEFAULT_SECURITY_HEADERS_CONFIG,
      ...config,
    };

    return {
      module: SecurityHeadersModule,
      providers: [
        {
          provide: SECURITY_HEADERS_CONFIG_TOKEN,
          useValue: mergedConfig,
        },
        SecurityHeadersService,
        SecurityHeadersHealthService,
        SecurityHeadersMiddleware,
      ],
      exports: [
        SecurityHeadersService,
        SecurityHeadersHealthService,
        SecurityHeadersMiddleware,
        SECURITY_HEADERS_CONFIG_TOKEN,
      ],
      global: true,
    };
  }

  static forRootAsync(options: {
    useFactory: (...args: any[]) => Promise<SecurityHeadersConfig> | SecurityHeadersConfig;
    inject?: any[];
  }): DynamicModule {
    return {
      module: SecurityHeadersModule,
      providers: [
        {
          provide: SECURITY_HEADERS_CONFIG_TOKEN,
          useFactory: options.useFactory,
          inject: options.inject || [],
        },
        SecurityHeadersService,
        SecurityHeadersHealthService,
        SecurityHeadersMiddleware,
      ],
      exports: [
        SecurityHeadersService,
        SecurityHeadersHealthService,
        SecurityHeadersMiddleware,
        SECURITY_HEADERS_CONFIG_TOKEN,
      ],
      global: true,
    };
  }
}
