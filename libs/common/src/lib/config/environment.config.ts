/**
 * Environment Configuration for Qeep
 * Loads environment variables from .env.development file
 */

import { config } from 'dotenv';
import { join } from 'path';

// Load environment variables from .env.development
if (process.env['NODE_ENV'] === 'development') {
  config({ path: join(process.cwd(), '.env.development') });
}

export interface DatabaseConfig {
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  url: string;
}

export interface ServiceDatabaseUrls {
  // Core Platform Services
  auth: string;
  user: string;
  tenant: string;
  notification: string;
  audit: string;

  // Transaction Monitoring Services
  transaction: string;
  aml: string;
  customer: string;
  surveillance: string;
  integration: string;
  monitoring: string;
}

export interface RedisConfig {
  host: string;
  port: number;
  password: string;
  url: string;
}

export interface KafkaConfig {
  brokers: string[];
  clientId: string;
  groupId: string;
}

export interface JwtConfig {
  secret: string;
  expiresIn: string;
  refreshExpiresIn: string;
}

export interface ApiConfig {
  rateLimit: number;
  timeout: number;
}

export interface CorsConfig {
  origin: string[];
  credentials: boolean;
}

export interface FeatureFlags {
  enableSwagger: boolean;
  enableMetrics: boolean;
  enableTracing: boolean;
  enableRateLimiting: boolean;
  enableCors: boolean;
}

export interface Auth0Config {
  domain: string;
  clientId: string;
  clientSecret: string;
  audience: string;
}

export interface SmtpConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  from: string;
}

export interface RateLimitingConfig {
  enabled: boolean;
  redis: {
    host: string;
    port: number;
    password?: string;
    db: number;
    keyPrefix: string;
  };
  limits: {
    perUser: number;
    perTenant: number;
    perIp: number;
    perEndpoint: number;
  };
  ttl: {
    user: number;
    tenant: number;
    ip: number;
    endpoint: number;
  };
  blockDuration: {
    user: number;
    tenant: number;
    ip: number;
    endpoint: number;
  };
  enableMetrics: boolean;
  enableLogging: boolean;
  customLimits: Record<
    string,
    {
      limit: number;
      ttl: number;
      blockDuration: number;
    }
  >;
}

export interface SecurityHeadersConfig {
  hsts: {
    enabled: boolean;
    maxAge: number;
    includeSubDomains: boolean;
    preload: boolean;
  };
  csp: {
    enabled: boolean;
    reportOnly: boolean;
    defaultSrc: string[];
    scriptSrc: string[];
    styleSrc: string[];
    imgSrc: string[];
    connectSrc: string[];
    fontSrc: string[];
    objectSrc: string[];
    upgradeInsecureRequests: boolean;
    blockMixedContent: boolean;
  };
  frameOptions: {
    enabled: boolean;
    policy: 'DENY' | 'SAMEORIGIN' | 'ALLOW-FROM';
  };
  contentTypeOptions: {
    enabled: boolean;
    nosniff: boolean;
  };
  xssProtection: {
    enabled: boolean;
    mode: '0' | '1' | '1; mode=block';
  };
  referrerPolicy: {
    enabled: boolean;
    policy: string;
  };
  permissionsPolicy: {
    enabled: boolean;
  };
  crossOriginPolicies: {
    coep: {
      enabled: boolean;
      policy: string;
    };
    coop: {
      enabled: boolean;
      policy: string;
    };
    corp: {
      enabled: boolean;
      policy: string;
    };
  };
}

export interface JwtValidationConfig {
  auth0: {
    domain: string;
    audience: string;
    issuer: string;
    algorithms: string[];
    jwksUri: string;
    cacheMaxEntries: number;
    cacheMaxAge: number;
    rateLimit: boolean;
    jwksRequestsPerMinute: number;
  };
  validation: {
    clockTolerance: number;
    maxAge: string;
    ignoreExpiration: boolean;
    ignoreNotBefore: boolean;
    requireAudience: boolean;
    requireIssuer: boolean;
  };
  extraction: {
    userIdClaim: string;
    tenantIdClaim: string;
    emailClaim: string;
    rolesClaim: string;
    permissionsClaim: string;
    customClaims: string[];
  };
  security: {
    enableBlacklist: boolean;
    blacklistTtl: number;
    enableRateLimit: boolean;
    maxRequestsPerMinute: number;
  };
}

/**
 * Nginx Proxy Configuration
 */
export interface NginxConfig {
  enabled: boolean;
  port: number;
  httpsPort: number;
  logLevel: string;
  proxyUrl: string;
  upstreams: {
    apiGateway: string;
    authService: string;
    userService: string;
    tenantService: string;
    notificationService: string;
    auditService: string;
    transactionService: string;
    amlService: string;
    customerService: string;
    surveillanceService: string;
    integrationService: string;
    monitoringService: string;
  };
}

export interface EnvironmentConfig {
  // Application Environment
  nodeEnv: string;
  logLevel: string;

  // Nginx Proxy Configuration
  nginx: NginxConfig;

  // HTTP Service Ports
  apiGatewayPort: number;
  authServicePort: number;
  userServicePort: number;
  tenantServicePort: number;
  notificationServicePort: number;
  auditServicePort: number;
  transactionServicePort: number;
  amlServicePort: number;
  customerServicePort: number;
  surveillanceServicePort: number;
  integrationServicePort: number;
  monitoringServicePort: number;

  // gRPC Service Ports
  authServiceGrpcPort: number;
  userServiceGrpcPort: number;
  tenantServiceGrpcPort: number;
  notificationServiceGrpcPort: number;
  auditServiceGrpcPort: number;
  transactionServiceGrpcPort: number;
  amlServiceGrpcPort: number;
  customerServiceGrpcPort: number;
  surveillanceServiceGrpcPort: number;
  integrationServiceGrpcPort: number;
  monitoringServiceGrpcPort: number;

  // Service Hosts
  apiGatewayHost: string;
  authServiceHost: string;
  userServiceHost: string;
  tenantServiceHost: string;
  notificationServiceHost: string;
  auditServiceHost: string;
  transactionServiceHost: string;
  amlServiceHost: string;
  customerServiceHost: string;
  surveillanceServiceHost: string;
  integrationServiceHost: string;
  monitoringServiceHost: string;

  // HTTP Service URLs
  authServiceUrl: string;
  userServiceUrl: string;
  tenantServiceUrl: string;
  notificationServiceUrl: string;
  auditServiceUrl: string;
  transactionServiceUrl: string;
  amlServiceUrl: string;
  customerServiceUrl: string;
  surveillanceServiceUrl: string;
  integrationServiceUrl: string;
  monitoringServiceUrl: string;

  // gRPC Service URLs
  authServiceGrpcUrl: string;
  userServiceGrpcUrl: string;
  tenantServiceGrpcUrl: string;
  notificationServiceGrpcUrl: string;
  auditServiceGrpcUrl: string;
  transactionServiceGrpcUrl: string;
  amlServiceGrpcUrl: string;
  customerServiceGrpcUrl: string;
  surveillanceServiceGrpcUrl: string;
  integrationServiceGrpcUrl: string;
  monitoringServiceGrpcUrl: string;

  // Configuration Objects
  database: DatabaseConfig;
  redis: RedisConfig;
  kafka: KafkaConfig;
  jwt: JwtConfig;
  api: ApiConfig;
  cors: CorsConfig;
  features: FeatureFlags;
  auth0: Auth0Config;
  smtp: SmtpConfig;
  rateLimiting: RateLimitingConfig;
  securityHeaders: SecurityHeadersConfig;
  jwtValidation: JwtValidationConfig;
}

export const environment: EnvironmentConfig = {
  // Application Environment
  nodeEnv: process.env['NODE_ENV'] || 'development',
  logLevel: process.env['LOG_LEVEL'] || 'info',

  // Nginx Proxy Configuration
  nginx: {
    enabled: process.env['NGINX_ENABLED'] === 'true',
    port: parseInt(process.env['NGINX_PORT'] || '8080', 10),
    httpsPort: parseInt(process.env['NGINX_HTTPS_PORT'] || '8443', 10),
    logLevel: process.env['NGINX_LOG_LEVEL'] || 'warn',
    proxyUrl: process.env['NGINX_PROXY_URL'] || 'http://localhost:8080',
    upstreams: {
      apiGateway: process.env['NGINX_UPSTREAM_API_GATEWAY'] || 'host.orb.internal:3000',
      authService: process.env['NGINX_UPSTREAM_AUTH_SERVICE'] || 'host.orb.internal:3001',
      userService: process.env['NGINX_UPSTREAM_USER_SERVICE'] || 'host.orb.internal:3002',
      tenantService: process.env['NGINX_UPSTREAM_TENANT_SERVICE'] || 'host.orb.internal:3003',
      notificationService: process.env['NGINX_UPSTREAM_NOTIFICATION_SERVICE'] || 'host.orb.internal:3004',
      auditService: process.env['NGINX_UPSTREAM_AUDIT_SERVICE'] || 'host.orb.internal:3005',
      transactionService: process.env['NGINX_UPSTREAM_TRANSACTION_SERVICE'] || 'host.orb.internal:3006',
      amlService: process.env['NGINX_UPSTREAM_AML_SERVICE'] || 'host.orb.internal:3007',
      customerService: process.env['NGINX_UPSTREAM_CUSTOMER_SERVICE'] || 'host.orb.internal:3008',
      surveillanceService: process.env['NGINX_UPSTREAM_SURVEILLANCE_SERVICE'] || 'host.orb.internal:3009',
      integrationService: process.env['NGINX_UPSTREAM_INTEGRATION_SERVICE'] || 'host.orb.internal:3010',
      monitoringService: process.env['NGINX_UPSTREAM_MONITORING_SERVICE'] || 'host.orb.internal:3012',
    },
  },

  // HTTP Service Ports
  apiGatewayPort: parseInt(process.env['API_GATEWAY_PORT'] || '3000', 10),
  authServicePort: parseInt(process.env['AUTH_SERVICE_PORT'] || '3001', 10),
  userServicePort: parseInt(process.env['USER_SERVICE_PORT'] || '3002', 10),
  tenantServicePort: parseInt(process.env['TENANT_SERVICE_PORT'] || '3003', 10),
  notificationServicePort: parseInt(process.env['NOTIFICATION_SERVICE_PORT'] || '3004', 10),
  auditServicePort: parseInt(process.env['AUDIT_SERVICE_PORT'] || '3005', 10),
  transactionServicePort: parseInt(process.env['TRANSACTION_SERVICE_PORT'] || '3006', 10),
  amlServicePort: parseInt(process.env['AML_SERVICE_PORT'] || '3007', 10),
  customerServicePort: parseInt(process.env['CUSTOMER_SERVICE_PORT'] || '3008', 10),
  surveillanceServicePort: parseInt(process.env['SURVEILLANCE_SERVICE_PORT'] || '3009', 10),
  integrationServicePort: parseInt(process.env['INTEGRATION_SERVICE_PORT'] || '3010', 10),
  monitoringServicePort: parseInt(process.env['MONITORING_SERVICE_PORT'] || '3012', 10),

  // gRPC Service Ports
  authServiceGrpcPort: parseInt(process.env['AUTH_SERVICE_GRPC_PORT'] || '3021', 10),
  userServiceGrpcPort: parseInt(process.env['USER_SERVICE_GRPC_PORT'] || '3022', 10),
  tenantServiceGrpcPort: parseInt(process.env['TENANT_SERVICE_GRPC_PORT'] || '3023', 10),
  notificationServiceGrpcPort: parseInt(process.env['NOTIFICATION_SERVICE_GRPC_PORT'] || '3024', 10),
  auditServiceGrpcPort: parseInt(process.env['AUDIT_SERVICE_GRPC_PORT'] || '3025', 10),
  transactionServiceGrpcPort: parseInt(process.env['TRANSACTION_SERVICE_GRPC_PORT'] || '3026', 10),
  amlServiceGrpcPort: parseInt(process.env['AML_SERVICE_GRPC_PORT'] || '3027', 10),
  customerServiceGrpcPort: parseInt(process.env['CUSTOMER_SERVICE_GRPC_PORT'] || '3028', 10),
  surveillanceServiceGrpcPort: parseInt(process.env['SURVEILLANCE_SERVICE_GRPC_PORT'] || '3029', 10),
  integrationServiceGrpcPort: parseInt(process.env['INTEGRATION_SERVICE_GRPC_PORT'] || '3030', 10),
  monitoringServiceGrpcPort: parseInt(process.env['MONITORING_SERVICE_GRPC_PORT'] || '3031', 10),

  // Service Hosts
  apiGatewayHost: process.env['API_GATEWAY_HOST'] || 'localhost',
  authServiceHost: process.env['AUTH_SERVICE_HOST'] || 'localhost',
  userServiceHost: process.env['USER_SERVICE_HOST'] || 'localhost',
  tenantServiceHost: process.env['TENANT_SERVICE_HOST'] || 'localhost',
  notificationServiceHost: process.env['NOTIFICATION_SERVICE_HOST'] || 'localhost',
  auditServiceHost: process.env['AUDIT_SERVICE_HOST'] || 'localhost',
  transactionServiceHost: process.env['TRANSACTION_SERVICE_HOST'] || 'localhost',
  amlServiceHost: process.env['AML_SERVICE_HOST'] || 'localhost',
  customerServiceHost: process.env['CUSTOMER_SERVICE_HOST'] || 'localhost',
  surveillanceServiceHost: process.env['SURVEILLANCE_SERVICE_HOST'] || 'localhost',
  integrationServiceHost: process.env['INTEGRATION_SERVICE_HOST'] || 'localhost',
  monitoringServiceHost: process.env['MONITORING_SERVICE_HOST'] || 'localhost',

  // HTTP Service URLs
  authServiceUrl: process.env['AUTH_SERVICE_URL'] || 'http://localhost:3001',
  userServiceUrl: process.env['USER_SERVICE_URL'] || 'http://localhost:3002',
  tenantServiceUrl: process.env['TENANT_SERVICE_URL'] || 'http://localhost:3003',
  notificationServiceUrl: process.env['NOTIFICATION_SERVICE_URL'] || 'http://localhost:3004',
  auditServiceUrl: process.env['AUDIT_SERVICE_URL'] || 'http://localhost:3005',
  transactionServiceUrl: process.env['TRANSACTION_SERVICE_URL'] || 'http://localhost:3006',
  amlServiceUrl: process.env['AML_SERVICE_URL'] || 'http://localhost:3007',
  customerServiceUrl: process.env['CUSTOMER_SERVICE_URL'] || 'http://localhost:3008',
  surveillanceServiceUrl: process.env['SURVEILLANCE_SERVICE_URL'] || 'http://localhost:3009',
  integrationServiceUrl: process.env['INTEGRATION_SERVICE_URL'] || 'http://localhost:3010',
  monitoringServiceUrl: process.env['MONITORING_SERVICE_URL'] || 'http://localhost:3012',

  // gRPC Service URLs
  authServiceGrpcUrl: process.env['AUTH_SERVICE_GRPC_URL'] || 'localhost:3021',
  userServiceGrpcUrl: process.env['USER_SERVICE_GRPC_URL'] || 'localhost:3022',
  tenantServiceGrpcUrl: process.env['TENANT_SERVICE_GRPC_URL'] || 'localhost:3023',
  notificationServiceGrpcUrl: process.env['NOTIFICATION_SERVICE_GRPC_URL'] || 'localhost:3024',
  auditServiceGrpcUrl: process.env['AUDIT_SERVICE_GRPC_URL'] || 'localhost:3025',
  transactionServiceGrpcUrl: process.env['TRANSACTION_SERVICE_GRPC_URL'] || 'localhost:3026',
  amlServiceGrpcUrl: process.env['AML_SERVICE_GRPC_URL'] || 'localhost:3027',
  customerServiceGrpcUrl: process.env['CUSTOMER_SERVICE_GRPC_URL'] || 'localhost:3028',
  surveillanceServiceGrpcUrl: process.env['SURVEILLANCE_SERVICE_GRPC_URL'] || 'localhost:3029',
  integrationServiceGrpcUrl: process.env['INTEGRATION_SERVICE_GRPC_URL'] || 'localhost:3030',
  monitoringServiceGrpcUrl: process.env['MONITORING_SERVICE_GRPC_URL'] || 'localhost:3031',

  // Database Configuration
  database: {
    host: process.env['POSTGRES_HOST'] || 'localhost',
    port: parseInt(process.env['POSTGRES_PORT'] || '5432', 10),
    username: process.env['POSTGRES_USER'] || 'qeep_dev',
    password: process.env['POSTGRES_PASSWORD'] || 'qeep_password_dev',
    database: process.env['POSTGRES_DB'] || 'qeep_dev',
    url: process.env['DATABASE_URL'] || 'postgresql://qeep_dev:qeep_password_dev@localhost:5432/qeep_dev',
  },

  // Redis Configuration
  redis: {
    host: process.env['REDIS_HOST'] || 'localhost',
    port: parseInt(process.env['REDIS_PORT'] || '6379', 10),
    password: process.env['REDIS_PASSWORD'] || 'redis_dev_password',
    url: process.env['REDIS_URL'] || 'redis://:redis_dev_password@localhost:6379',
  },

  // Kafka Configuration
  kafka: {
    brokers: process.env['KAFKA_BROKERS']?.split(',') || ['localhost:9092'],
    clientId: process.env['KAFKA_CLIENT_ID'] || 'qeep-dev',
    groupId: process.env['KAFKA_GROUP_ID'] || 'qeep-dev-group',
  },

  // JWT Configuration
  jwt: {
    secret: process.env['JWT_SECRET'] || 'dev_jwt_secret_key_change_in_production',
    expiresIn: process.env['JWT_EXPIRES_IN'] || '24h',
    refreshExpiresIn: process.env['JWT_REFRESH_EXPIRES_IN'] || '7d',
  },

  // API Configuration
  api: {
    rateLimit: parseInt(process.env['API_RATE_LIMIT'] || '1000', 10),
    timeout: parseInt(process.env['API_TIMEOUT'] || '30000', 10),
  },

  // CORS Configuration
  cors: {
    origin: process.env['CORS_ORIGIN']?.split(',') || ['http://localhost:3000', 'http://localhost:4200'],
    credentials: process.env['CORS_CREDENTIALS'] === 'true',
  },

  // Feature Flags
  features: {
    enableSwagger: process.env['ENABLE_SWAGGER'] === 'true',
    enableMetrics: process.env['ENABLE_METRICS'] === 'true',
    enableTracing: process.env['ENABLE_TRACING'] === 'true',
    enableRateLimiting: process.env['ENABLE_RATE_LIMITING'] === 'true',
    enableCors: process.env['ENABLE_CORS'] === 'true',
  },

  // External Services
  auth0: {
    domain: process.env['AUTH0_DOMAIN'] || 'qeep-dev.us.auth0.com',
    clientId: process.env['AUTH0_CLIENT_ID'] || 'your_dev_client_id',
    clientSecret: process.env['AUTH0_CLIENT_SECRET'] || 'your_dev_client_secret',
    audience: process.env['AUTH0_AUDIENCE'] || 'https://api.qeep.dev',
  },

  // SMTP Configuration
  smtp: {
    host: process.env['SMTP_HOST'] || 'localhost',
    port: parseInt(process.env['SMTP_PORT'] || '1025', 10),
    user: process.env['SMTP_USER'] || '',
    password: process.env['SMTP_PASSWORD'] || '',
    from: process.env['SMTP_FROM'] || '<EMAIL>',
  },

  // Rate Limiting Configuration
  rateLimiting: {
    enabled: process.env['RATE_LIMITING_ENABLED'] === 'true',
    redis: {
      host: process.env['RATE_LIMITING_REDIS_HOST'] || 'localhost',
      port: parseInt(process.env['RATE_LIMITING_REDIS_PORT'] || '6379', 10),
      password: process.env['RATE_LIMITING_REDIS_PASSWORD'],
      db: parseInt(process.env['RATE_LIMITING_REDIS_DB'] || '2', 10),
      keyPrefix: process.env['RATE_LIMITING_REDIS_KEY_PREFIX'] || 'rate-limit',
    },
    limits: {
      perUser: parseInt(process.env['RATE_LIMIT_PER_USER'] || '100', 10),
      perTenant: parseInt(process.env['RATE_LIMIT_PER_TENANT'] || '1000', 10),
      perIp: parseInt(process.env['RATE_LIMIT_PER_IP'] || '50', 10),
      perEndpoint: parseInt(process.env['RATE_LIMIT_PER_ENDPOINT'] || '200', 10),
    },
    ttl: {
      user: parseInt(process.env['RATE_LIMIT_TTL_USER'] || '60', 10),
      tenant: parseInt(process.env['RATE_LIMIT_TTL_TENANT'] || '60', 10),
      ip: parseInt(process.env['RATE_LIMIT_TTL_IP'] || '60', 10),
      endpoint: parseInt(process.env['RATE_LIMIT_TTL_ENDPOINT'] || '60', 10),
    },
    blockDuration: {
      user: parseInt(process.env['RATE_LIMIT_BLOCK_DURATION_USER'] || '300', 10),
      tenant: parseInt(process.env['RATE_LIMIT_BLOCK_DURATION_TENANT'] || '300', 10),
      ip: parseInt(process.env['RATE_LIMIT_BLOCK_DURATION_IP'] || '600', 10),
      endpoint: parseInt(process.env['RATE_LIMIT_BLOCK_DURATION_ENDPOINT'] || '300', 10),
    },
    enableMetrics: process.env['RATE_LIMITING_ENABLE_METRICS'] === 'true',
    enableLogging: process.env['RATE_LIMITING_ENABLE_LOGGING'] === 'true',
    customLimits: {
      'health-check': {
        limit: parseInt(process.env['RATE_LIMIT_HEALTH_CHECK'] || '5', 10),
        ttl: parseInt(process.env['RATE_LIMIT_HEALTH_CHECK_TTL'] || '60', 10),
        blockDuration: parseInt(process.env['RATE_LIMIT_HEALTH_CHECK_BLOCK'] || '120', 10),
      },
      'k8s-readiness': {
        limit: parseInt(process.env['RATE_LIMIT_K8S_READINESS'] || '100', 10),
        ttl: parseInt(process.env['RATE_LIMIT_K8S_READINESS_TTL'] || '60', 10),
        blockDuration: parseInt(process.env['RATE_LIMIT_K8S_READINESS_BLOCK'] || '60', 10),
      },
      'k8s-liveness': {
        limit: parseInt(process.env['RATE_LIMIT_K8S_LIVENESS'] || '100', 10),
        ttl: parseInt(process.env['RATE_LIMIT_K8S_LIVENESS_TTL'] || '60', 10),
        blockDuration: parseInt(process.env['RATE_LIMIT_K8S_LIVENESS_BLOCK'] || '60', 10),
      },
      'detailed-health': {
        limit: parseInt(process.env['RATE_LIMIT_DETAILED_HEALTH'] || '3', 10),
        ttl: parseInt(process.env['RATE_LIMIT_DETAILED_HEALTH_TTL'] || '60', 10),
        blockDuration: parseInt(process.env['RATE_LIMIT_DETAILED_HEALTH_BLOCK'] || '300', 10),
      },
    },
  },

  // Security Headers Configuration
  securityHeaders: {
    hsts: {
      enabled: process.env['SECURITY_HEADERS_HSTS_ENABLED'] === 'true',
      maxAge: parseInt(process.env['SECURITY_HEADERS_HSTS_MAX_AGE'] || '31536000', 10),
      includeSubDomains: process.env['SECURITY_HEADERS_HSTS_INCLUDE_SUBDOMAINS'] === 'true',
      preload: process.env['SECURITY_HEADERS_HSTS_PRELOAD'] === 'true',
    },
    csp: {
      enabled: process.env['SECURITY_HEADERS_CSP_ENABLED'] === 'true',
      reportOnly: process.env['SECURITY_HEADERS_CSP_REPORT_ONLY'] === 'true',
      defaultSrc: (process.env['SECURITY_HEADERS_CSP_DEFAULT_SRC'] || "'self'").split(','),
      scriptSrc: (process.env['SECURITY_HEADERS_CSP_SCRIPT_SRC'] || "'self'").split(','),
      styleSrc: (process.env['SECURITY_HEADERS_CSP_STYLE_SRC'] || "'self'").split(','),
      imgSrc: (process.env['SECURITY_HEADERS_CSP_IMG_SRC'] || "'self'").split(','),
      connectSrc: (process.env['SECURITY_HEADERS_CSP_CONNECT_SRC'] || "'self'").split(','),
      fontSrc: (process.env['SECURITY_HEADERS_CSP_FONT_SRC'] || "'self'").split(','),
      objectSrc: (process.env['SECURITY_HEADERS_CSP_OBJECT_SRC'] || "'none'").split(','),
      upgradeInsecureRequests: process.env['SECURITY_HEADERS_CSP_UPGRADE_INSECURE_REQUESTS'] === 'true',
      blockMixedContent: process.env['SECURITY_HEADERS_CSP_BLOCK_MIXED_CONTENT'] === 'true',
    },
    frameOptions: {
      enabled: process.env['SECURITY_HEADERS_FRAME_OPTIONS_ENABLED'] === 'true',
      policy: (process.env['SECURITY_HEADERS_FRAME_OPTIONS_POLICY'] || 'DENY') as 'DENY' | 'SAMEORIGIN' | 'ALLOW-FROM',
    },
    contentTypeOptions: {
      enabled: process.env['SECURITY_HEADERS_CONTENT_TYPE_OPTIONS_ENABLED'] === 'true',
      nosniff: process.env['SECURITY_HEADERS_CONTENT_TYPE_OPTIONS_NOSNIFF'] === 'true',
    },
    xssProtection: {
      enabled: process.env['SECURITY_HEADERS_XSS_PROTECTION_ENABLED'] === 'true',
      mode: (process.env['SECURITY_HEADERS_XSS_PROTECTION_MODE'] || '1; mode=block') as '0' | '1' | '1; mode=block',
    },
    referrerPolicy: {
      enabled: process.env['SECURITY_HEADERS_REFERRER_POLICY_ENABLED'] === 'true',
      policy: process.env['SECURITY_HEADERS_REFERRER_POLICY'] || 'strict-origin-when-cross-origin',
    },
    permissionsPolicy: {
      enabled: process.env['SECURITY_HEADERS_PERMISSIONS_POLICY_ENABLED'] === 'true',
    },
    crossOriginPolicies: {
      coep: {
        enabled: process.env['SECURITY_HEADERS_COEP_ENABLED'] === 'true',
        policy: process.env['SECURITY_HEADERS_COEP_POLICY'] || 'unsafe-none',
      },
      coop: {
        enabled: process.env['SECURITY_HEADERS_COOP_ENABLED'] === 'true',
        policy: process.env['SECURITY_HEADERS_COOP_POLICY'] || 'same-origin-allow-popups',
      },
      corp: {
        enabled: process.env['SECURITY_HEADERS_CORP_ENABLED'] === 'true',
        policy: process.env['SECURITY_HEADERS_CORP_POLICY'] || 'same-site',
      },
    },
  },

  // JWT Validation Configuration
  jwtValidation: {
    auth0: {
      domain: process.env['AUTH0_DOMAIN'] || 'your-domain.auth0.com',
      audience: process.env['AUTH0_AUDIENCE'] || 'https://api.qeep.com',
      issuer: process.env['AUTH0_ISSUER'] || 'https://your-domain.auth0.com/',
      algorithms: ['RS256'],
      jwksUri: process.env['AUTH0_JWKS_URI'] || 'https://your-domain.auth0.com/.well-known/jwks.json',
      cacheMaxEntries: parseInt(process.env['JWT_JWKS_CACHE_MAX_ENTRIES'] || '5', 10),
      cacheMaxAge: parseInt(process.env['JWT_JWKS_CACHE_MAX_AGE'] || '600000', 10),
      rateLimit: process.env['JWT_JWKS_RATE_LIMIT'] === 'true',
      jwksRequestsPerMinute: parseInt(process.env['JWT_JWKS_REQUESTS_PER_MINUTE'] || '10', 10),
    },
    validation: {
      clockTolerance: parseInt(process.env['JWT_CLOCK_TOLERANCE'] || '30', 10),
      maxAge: process.env['JWT_MAX_AGE'] || '24h',
      ignoreExpiration: process.env['JWT_IGNORE_EXPIRATION'] === 'true',
      ignoreNotBefore: process.env['JWT_IGNORE_NOT_BEFORE'] === 'true',
      requireAudience: process.env['JWT_REQUIRE_AUDIENCE'] !== 'false',
      requireIssuer: process.env['JWT_REQUIRE_ISSUER'] !== 'false',
    },
    extraction: {
      userIdClaim: process.env['JWT_USER_ID_CLAIM'] || 'sub',
      tenantIdClaim: process.env['JWT_TENANT_ID_CLAIM'] || 'tenant_id',
      emailClaim: process.env['JWT_EMAIL_CLAIM'] || 'email',
      rolesClaim: process.env['JWT_ROLES_CLAIM'] || 'roles',
      permissionsClaim: process.env['JWT_PERMISSIONS_CLAIM'] || 'permissions',
      customClaims: (process.env['JWT_CUSTOM_CLAIMS'] || '').split(',').filter(Boolean),
    },
    security: {
      enableBlacklist: process.env['JWT_ENABLE_BLACKLIST'] === 'true',
      blacklistTtl: parseInt(process.env['JWT_BLACKLIST_TTL'] || '86400', 10),
      enableRateLimit: process.env['JWT_ENABLE_RATE_LIMIT'] === 'true',
      maxRequestsPerMinute: parseInt(process.env['JWT_MAX_REQUESTS_PER_MINUTE'] || '1000', 10),
    },
  },
};

export const EnvironmentConfig = environment;
export default environment;
