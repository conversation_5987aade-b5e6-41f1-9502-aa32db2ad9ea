import { Injectable } from '@nestjs/common';

@Injectable()
export class ConfigService {
  /**
   * Get a configuration value by key from environment variables
   */
  get<T = string>(key: string): T | undefined {
    return process.env[key] as T;
  }

  /**
   * Get a configuration value by key with a default value
   */
  getOrThrow<T = string>(key: string, defaultValue?: T): T {
    const value = this.get<T>(key);
    if (value === undefined) {
      if (defaultValue !== undefined) {
        return defaultValue;
      }
      throw new Error(`Configuration key "${key}" is required but not found`);
    }
    return value;
  }

  /**
   * Get database configuration
   */
  getDatabaseConfig() {
    return {
      host: this.getOrThrow('DATABASE_HOST'),
      port: parseInt(this.getOrThrow('DATABASE_PORT', '5432'), 10),
      username: this.getOrThrow('DATABASE_USERNAME'),
      password: this.getOrThrow('DATABASE_PASSWORD'),
      database: this.getOrThrow('DATABASE_NAME'),
    };
  }

  /**
   * Get Redis configuration
   */
  getRedisConfig() {
    return {
      host: this.getOrThrow('REDIS_HOST'),
      port: parseInt(this.getOrThrow('REDIS_PORT', '6379'), 10),
      password: this.get('REDIS_PASSWORD'),
    };
  }

  /**
   * Get JWT configuration
   */
  getJwtConfig() {
    return {
      secret: this.getOrThrow('JWT_SECRET'),
      expiresIn: this.getOrThrow('JWT_EXPIRES_IN', '1h'),
      refreshSecret: this.getOrThrow('JWT_REFRESH_SECRET'),
      refreshExpiresIn: this.getOrThrow('JWT_REFRESH_EXPIRES_IN', '7d'),
    };
  }

  /**
   * Get service URL by service name
   */
  getServiceUrl(serviceName: string): string {
    const key = `${serviceName.toUpperCase().replace(/-/g, '_')}_URL`;
    return this.getOrThrow(key);
  }

  /**
   * Get gRPC service URL by service name
   */
  getGrpcServiceUrl(serviceName: string): string {
    const key = `${serviceName.toUpperCase().replace(/-/g, '_')}_GRPC_URL`;
    return this.getOrThrow(key);
  }

  /**
   * Get HTTP port for a service
   */
  getServicePort(serviceName: string): number {
    const key = `${serviceName.toUpperCase().replace(/-/g, '_')}_PORT`;
    return parseInt(this.getOrThrow(key), 10);
  }

  /**
   * Get gRPC port for a service
   */
  getGrpcServicePort(serviceName: string): number {
    const key = `${serviceName.toUpperCase().replace(/-/g, '_')}_GRPC_PORT`;
    return parseInt(this.getOrThrow(key), 10);
  }

  /**
   * Get service host by service name
   */
  getServiceHost(serviceName: string): string {
    const key = `${serviceName.toUpperCase().replace(/-/g, '_')}_HOST`;
    return this.getOrThrow(key, 'localhost');
  }

  /**
   * Get node environment
   */
  getNodeEnv(): string {
    return this.getOrThrow('NODE_ENV', 'development');
  }

  /**
   * Get log level
   */
  getLogLevel(): string {
    return this.getOrThrow('LOG_LEVEL', 'info');
  }

  /**
   * Check if a feature is enabled
   */
  isFeatureEnabled(featureName: string): boolean {
    const key = `ENABLE_${featureName
      .toUpperCase()
      .replace(/([A-Z])/g, '_$1')
      .replace(/^_/, '')}`;
    return this.get(key) === 'true';
  }

  /**
   * Get CORS configuration
   */
  getCorsConfig() {
    return {
      origin: (this.get('CORS_ORIGIN') || 'http://localhost:3000,http://localhost:4200').split(','),
      credentials: this.get('CORS_CREDENTIALS') === 'true',
    };
  }

  /**
   * Get Nginx proxy configuration
   */
  getNginxConfig() {
    return {
      enabled: this.get('NGINX_ENABLED') === 'true',
      port: parseInt(this.get('NGINX_PORT') || '8080', 10),
      httpsPort: parseInt(this.get('NGINX_HTTPS_PORT') || '8443', 10),
      logLevel: this.get('NGINX_LOG_LEVEL') || 'warn',
      proxyUrl: this.get('NGINX_PROXY_URL') || 'http://localhost:8080',
      upstreams: {
        apiGateway: this.get('NGINX_UPSTREAM_API_GATEWAY') || 'host.orb.internal:3000',
        authService: this.get('NGINX_UPSTREAM_AUTH_SERVICE') || 'host.orb.internal:3001',
        userService: this.get('NGINX_UPSTREAM_USER_SERVICE') || 'host.orb.internal:3002',
        tenantService: this.get('NGINX_UPSTREAM_TENANT_SERVICE') || 'host.orb.internal:3003',
        notificationService: this.get('NGINX_UPSTREAM_NOTIFICATION_SERVICE') || 'host.orb.internal:3004',
        auditService: this.get('NGINX_UPSTREAM_AUDIT_SERVICE') || 'host.orb.internal:3005',
      },
    };
  }

  /**
   * Check if Nginx proxy is enabled
   */
  isNginxEnabled(): boolean {
    return this.get('NGINX_ENABLED') === 'true';
  }

  /**
   * Get Nginx proxy URL
   */
  getNginxProxyUrl(): string {
    return this.get('NGINX_PROXY_URL') || 'http://localhost:8080';
  }

  /**
   * Get Auth0 configuration
   */
  getAuth0Config() {
    return {
      domain: this.get('AUTH0_DOMAIN') || 'qeep-dev.us.auth0.com',
      clientId: this.get('AUTH0_CLIENT_ID') || 'your_dev_client_id',
      clientSecret: this.get('AUTH0_CLIENT_SECRET') || 'your_dev_client_secret',
      audience: this.get('AUTH0_AUDIENCE') || 'https://api.qeep.dev',
    };
  }

  /**
   * Get JWT validation configuration
   */
  getJwtValidationConfig() {
    return {
      auth0: {
        domain: this.get('AUTH0_DOMAIN') || 'your-domain.auth0.com',
        audience: this.get('AUTH0_AUDIENCE') || 'https://api.qeep.com',
        issuer: this.get('AUTH0_ISSUER') || 'https://your-domain.auth0.com/',
        algorithms: ['RS256'],
        jwksUri: this.get('AUTH0_JWKS_URI') || 'https://your-domain.auth0.com/.well-known/jwks.json',
        cacheMaxEntries: parseInt(this.get('JWT_JWKS_CACHE_MAX_ENTRIES') || '5', 10),
        cacheMaxAge: parseInt(this.get('JWT_JWKS_CACHE_MAX_AGE') || '600000', 10),
        rateLimit: this.get('JWT_JWKS_RATE_LIMIT') === 'true',
        jwksRequestsPerMinute: parseInt(this.get('JWT_JWKS_REQUESTS_PER_MINUTE') || '10', 10),
      },
      validation: {
        clockTolerance: parseInt(this.get('JWT_CLOCK_TOLERANCE') || '30', 10),
        maxAge: this.get('JWT_MAX_AGE') || '24h',
        ignoreExpiration: this.get('JWT_IGNORE_EXPIRATION') === 'true',
        ignoreNotBefore: this.get('JWT_IGNORE_NOT_BEFORE') === 'true',
        requireAudience: this.get('JWT_REQUIRE_AUDIENCE') !== 'false',
        requireIssuer: this.get('JWT_REQUIRE_ISSUER') !== 'false',
      },
      extraction: {
        userIdClaim: this.get('JWT_USER_ID_CLAIM') || 'sub',
        tenantIdClaim: this.get('JWT_TENANT_ID_CLAIM') || 'tenant_id',
        emailClaim: this.get('JWT_EMAIL_CLAIM') || 'email',
        rolesClaim: this.get('JWT_ROLES_CLAIM') || 'roles',
        permissionsClaim: this.get('JWT_PERMISSIONS_CLAIM') || 'permissions',
        customClaims: (this.get('JWT_CUSTOM_CLAIMS') || '').split(',').filter(Boolean),
      },
      security: {
        enableBlacklist: this.get('JWT_ENABLE_BLACKLIST') === 'true',
        blacklistTtl: parseInt(this.get('JWT_BLACKLIST_TTL') || '86400', 10),
        enableRateLimit: this.get('JWT_ENABLE_RATE_LIMIT') === 'true',
        maxRequestsPerMinute: parseInt(this.get('JWT_MAX_REQUESTS_PER_MINUTE') || '1000', 10),
      },
    };
  }

  /**
   * Get security headers configuration
   */
  getSecurityHeadersConfig() {
    return {
      hsts: {
        enabled: this.get('SECURITY_HEADERS_HSTS_ENABLED') === 'true',
        maxAge: parseInt(this.get('SECURITY_HEADERS_HSTS_MAX_AGE') || '31536000', 10),
        includeSubDomains: this.get('SECURITY_HEADERS_HSTS_INCLUDE_SUBDOMAINS') === 'true',
        preload: this.get('SECURITY_HEADERS_HSTS_PRELOAD') === 'true',
      },
      csp: {
        enabled: this.get('SECURITY_HEADERS_CSP_ENABLED') === 'true',
        directives: {
          defaultSrc: (this.get('SECURITY_HEADERS_CSP_DEFAULT_SRC') || "'self'").split(','),
          scriptSrc: (this.get('SECURITY_HEADERS_CSP_SCRIPT_SRC') || "'self'").split(','),
          styleSrc: (this.get('SECURITY_HEADERS_CSP_STYLE_SRC') || "'self'").split(','),
          imgSrc: (this.get('SECURITY_HEADERS_CSP_IMG_SRC') || "'self'").split(','),
          connectSrc: (this.get('SECURITY_HEADERS_CSP_CONNECT_SRC') || "'self'").split(','),
          fontSrc: (this.get('SECURITY_HEADERS_CSP_FONT_SRC') || "'self'").split(','),
          objectSrc: (this.get('SECURITY_HEADERS_CSP_OBJECT_SRC') || "'none'").split(','),
          upgradeInsecureRequests: this.get('SECURITY_HEADERS_CSP_UPGRADE_INSECURE_REQUESTS') === 'true',
          blockAllMixedContent: this.get('SECURITY_HEADERS_CSP_BLOCK_MIXED_CONTENT') === 'true',
        },
        reportOnly: this.get('SECURITY_HEADERS_CSP_REPORT_ONLY') === 'true',
        reportUri: this.get('SECURITY_HEADERS_CSP_REPORT_URI'),
      },
      frameOptions: {
        enabled: this.get('SECURITY_HEADERS_FRAME_OPTIONS_ENABLED') === 'true',
        policy: (this.get('SECURITY_HEADERS_FRAME_OPTIONS_POLICY') || 'DENY') as 'DENY' | 'SAMEORIGIN' | 'ALLOW-FROM',
        allowFrom: this.get('SECURITY_HEADERS_FRAME_OPTIONS_ALLOW_FROM'),
      },
      contentTypeOptions: {
        enabled: this.get('SECURITY_HEADERS_CONTENT_TYPE_OPTIONS_ENABLED') === 'true',
        nosniff: this.get('SECURITY_HEADERS_CONTENT_TYPE_OPTIONS_NOSNIFF') !== 'false',
      },
      xssProtection: {
        enabled: this.get('SECURITY_HEADERS_XSS_PROTECTION_ENABLED') === 'true',
        mode: (this.get('SECURITY_HEADERS_XSS_PROTECTION_MODE') || '1; mode=block') as '0' | '1' | '1; mode=block',
        reportUri: this.get('SECURITY_HEADERS_XSS_PROTECTION_REPORT_URI'),
      },
      referrerPolicy: {
        enabled: this.get('SECURITY_HEADERS_REFERRER_POLICY_ENABLED') === 'true',
        policy: (this.get('SECURITY_HEADERS_REFERRER_POLICY') || 'strict-origin-when-cross-origin') as any,
      },
      permissionsPolicy: {
        enabled: this.get('SECURITY_HEADERS_PERMISSIONS_POLICY_ENABLED') === 'true',
        directives: {
          camera: (this.get('SECURITY_HEADERS_PERMISSIONS_CAMERA') || '').split(',').filter(Boolean),
          microphone: (this.get('SECURITY_HEADERS_PERMISSIONS_MICROPHONE') || '').split(',').filter(Boolean),
          geolocation: (this.get('SECURITY_HEADERS_PERMISSIONS_GEOLOCATION') || '').split(',').filter(Boolean),
          payment: (this.get('SECURITY_HEADERS_PERMISSIONS_PAYMENT') || '').split(',').filter(Boolean),
          usb: (this.get('SECURITY_HEADERS_PERMISSIONS_USB') || '').split(',').filter(Boolean),
          accelerometer: (this.get('SECURITY_HEADERS_PERMISSIONS_ACCELEROMETER') || '').split(',').filter(Boolean),
          gyroscope: (this.get('SECURITY_HEADERS_PERMISSIONS_GYROSCOPE') || '').split(',').filter(Boolean),
          magnetometer: (this.get('SECURITY_HEADERS_PERMISSIONS_MAGNETOMETER') || '').split(',').filter(Boolean),
          fullscreen: (this.get('SECURITY_HEADERS_PERMISSIONS_FULLSCREEN') || '').split(',').filter(Boolean),
          displayCapture: (this.get('SECURITY_HEADERS_PERMISSIONS_DISPLAY_CAPTURE') || '').split(',').filter(Boolean),
        },
      },
      coep: {
        enabled: this.get('SECURITY_HEADERS_COEP_ENABLED') === 'true',
        policy: (this.get('SECURITY_HEADERS_COEP_POLICY') || 'unsafe-none') as 'unsafe-none' | 'require-corp' | 'credentialless',
      },
      coop: {
        enabled: this.get('SECURITY_HEADERS_COOP_ENABLED') === 'true',
        policy: (this.get('SECURITY_HEADERS_COOP_POLICY') || 'same-origin-allow-popups') as 'unsafe-none' | 'same-origin-allow-popups' | 'same-origin',
      },
      corp: {
        enabled: this.get('SECURITY_HEADERS_CORP_ENABLED') === 'true',
        policy: (this.get('SECURITY_HEADERS_CORP_POLICY') || 'same-site') as 'same-site' | 'same-origin' | 'cross-origin',
      },
      customHeaders: {},
    };
  }

  /**
   * Get rate limiting configuration
   */
  getRateLimitingConfig() {
    return {
      redis: {
        host: this.get('RATE_LIMITING_REDIS_HOST') || 'localhost',
        port: parseInt(this.get('RATE_LIMITING_REDIS_PORT') || '6379', 10),
        password: this.get('RATE_LIMITING_REDIS_PASSWORD'),
        db: parseInt(this.get('RATE_LIMITING_REDIS_DB') || '2', 10),
        keyPrefix: this.get('RATE_LIMITING_REDIS_KEY_PREFIX') || 'rate-limit',
      },
      defaultLimits: {
        perUser: {
          name: 'per-user',
          ttl: parseInt(this.get('RATE_LIMIT_TTL_USER') || '60', 10),
          limit: parseInt(this.get('RATE_LIMIT_PER_USER') || '100', 10),
          blockDuration: parseInt(this.get('RATE_LIMIT_BLOCK_DURATION_USER') || '300', 10),
        },
        perTenant: {
          name: 'per-tenant',
          ttl: parseInt(this.get('RATE_LIMIT_TTL_TENANT') || '60', 10),
          limit: parseInt(this.get('RATE_LIMIT_PER_TENANT') || '1000', 10),
          blockDuration: parseInt(this.get('RATE_LIMIT_BLOCK_DURATION_TENANT') || '300', 10),
        },
        perIp: {
          name: 'per-ip',
          ttl: parseInt(this.get('RATE_LIMIT_TTL_IP') || '60', 10),
          limit: parseInt(this.get('RATE_LIMIT_PER_IP') || '50', 10),
          blockDuration: parseInt(this.get('RATE_LIMIT_BLOCK_DURATION_IP') || '600', 10),
        },
        perEndpoint: {
          name: 'per-endpoint',
          ttl: parseInt(this.get('RATE_LIMIT_TTL_ENDPOINT') || '60', 10),
          limit: parseInt(this.get('RATE_LIMIT_PER_ENDPOINT') || '200', 10),
          blockDuration: parseInt(this.get('RATE_LIMIT_BLOCK_DURATION_ENDPOINT') || '300', 10),
        },
      },
      customLimits: {
        'health-check': {
          name: 'health-check',
          limit: parseInt(this.get('RATE_LIMIT_HEALTH_CHECK') || '5', 10),
          ttl: parseInt(this.get('RATE_LIMIT_HEALTH_CHECK_TTL') || '60', 10),
          blockDuration: parseInt(this.get('RATE_LIMIT_HEALTH_CHECK_BLOCK') || '120', 10),
        },
        'k8s-readiness': {
          name: 'k8s-readiness',
          limit: parseInt(this.get('RATE_LIMIT_K8S_READINESS') || '100', 10),
          ttl: parseInt(this.get('RATE_LIMIT_K8S_READINESS_TTL') || '60', 10),
          blockDuration: parseInt(this.get('RATE_LIMIT_K8S_READINESS_BLOCK') || '60', 10),
        },
        'k8s-liveness': {
          name: 'k8s-liveness',
          limit: parseInt(this.get('RATE_LIMIT_K8S_LIVENESS') || '100', 10),
          ttl: parseInt(this.get('RATE_LIMIT_K8S_LIVENESS_TTL') || '60', 10),
          blockDuration: parseInt(this.get('RATE_LIMIT_K8S_LIVENESS_BLOCK') || '60', 10),
        },
        'detailed-health': {
          name: 'detailed-health',
          limit: parseInt(this.get('RATE_LIMIT_DETAILED_HEALTH') || '3', 10),
          ttl: parseInt(this.get('RATE_LIMIT_DETAILED_HEALTH_TTL') || '60', 10),
          blockDuration: parseInt(this.get('RATE_LIMIT_DETAILED_HEALTH_BLOCK') || '300', 10),
        },
      },
      enableMetrics: this.get('RATE_LIMITING_ENABLE_METRICS') === 'true',
      enableLogging: this.get('RATE_LIMITING_ENABLE_LOGGING') === 'true',
      keyPrefix: this.get('RATE_LIMITING_REDIS_KEY_PREFIX') || 'rate-limit',
    };
  }

  /**
   * Get circuit breaker configuration
   */
  getCircuitBreakerConfig() {
    return {
      enabled: this.get('CIRCUIT_BREAKER_ENABLED') === 'true',
      failureThreshold: parseInt(this.get('CIRCUIT_BREAKER_FAILURE_THRESHOLD') || '50', 10),
      timeout: parseInt(this.get('CIRCUIT_BREAKER_TIMEOUT') || '30000', 10),
      resetTimeout: parseInt(this.get('CIRCUIT_BREAKER_RESET_TIMEOUT') || '60000', 10),
    };
  }

  /**
   * Check if we're in development mode
   */
  isDevelopment(): boolean {
    return (this.get('NODE_ENV') || 'development') === 'development';
  }

  /**
   * Check if we're in production mode
   */
  isProduction(): boolean {
    return (this.get('NODE_ENV') || 'development') === 'production';
  }

  /**
   * Check if we're in test mode
   */
  isTest(): boolean {
    return (this.get('NODE_ENV') || 'development') === 'test';
  }

  /**
   * Get Kafka configuration
   */
  getKafkaConfig() {
    return {
      brokers: (this.get('KAFKA_BROKERS') || 'localhost:9092').split(','),
      clientId: this.get('KAFKA_CLIENT_ID') || 'qeep-dev',
      groupId: this.get('KAFKA_GROUP_ID') || 'qeep-dev-group',
      topics: {
        userEvents: this.get('KAFKA_TOPIC_USER_EVENTS') || 'user.events',
        authEvents: this.get('KAFKA_TOPIC_AUTH_EVENTS') || 'auth.events',
        tenantEvents: this.get('KAFKA_TOPIC_TENANT_EVENTS') || 'tenant.events',
        notificationEvents: this.get('KAFKA_TOPIC_NOTIFICATION_EVENTS') || 'notification.events',
        auditEvents: this.get('KAFKA_TOPIC_AUDIT_EVENTS') || 'audit.events',
        transactionEvents: this.get('KAFKA_TOPIC_TRANSACTION_EVENTS') || 'transaction.events',
        amlEvents: this.get('KAFKA_TOPIC_AML_EVENTS') || 'aml.events',
        customerEvents: this.get('KAFKA_TOPIC_CUSTOMER_EVENTS') || 'customer.events',
        surveillanceEvents: this.get('KAFKA_TOPIC_SURVEILLANCE_EVENTS') || 'surveillance.events',
        integrationEvents: this.get('KAFKA_TOPIC_INTEGRATION_EVENTS') || 'integration.events',
        monitoringEvents: this.get('KAFKA_TOPIC_MONITORING_EVENTS') || 'monitoring.events',
      },
    };
  }

  /**
   * Get email configuration
   */
  getEmailConfig() {
    return {
      defaultProvider: this.get('DEFAULT_EMAIL_PROVIDER') || 'smtp',
      timeout: parseInt(this.get('EMAIL_TIMEOUT') || '30000', 10),
      retryAttempts: parseInt(this.get('EMAIL_RETRY_ATTEMPTS') || '3', 10),
      rateLimit: parseInt(this.get('EMAIL_RATE_LIMIT') || '100', 10),
      dailyLimit: parseInt(this.get('EMAIL_DAILY_LIMIT') || '10000', 10),

      // SMTP Configuration
      smtp: {
        host: this.get('SMTP_HOST') || 'localhost',
        port: parseInt(this.get('SMTP_PORT') || '1026', 10),
        user: this.get('SMTP_USER') || '',
        password: this.get('SMTP_PASSWORD') || '',
        secure: this.get('SMTP_SECURE') === 'true',
        fromEmail: this.get('SMTP_FROM_EMAIL') || '<EMAIL>',
        fromName: this.get('SMTP_FROM_NAME') || 'Qeep Development',
        ignoreTLS: this.get('SMTP_IGNORE_TLS') === 'true',
        requireTLS: this.get('SMTP_REQUIRE_TLS') === 'true',
      },

      // Resend Configuration
      resend: {
        apiKey: this.get('RESEND_API_KEY') || '',
        fromEmail: this.get('RESEND_FROM_EMAIL') || '<EMAIL>',
        fromName: this.get('RESEND_FROM_NAME') || 'Qeep',
        webhookSecret: this.get('RESEND_WEBHOOK_SECRET') || '',
      },

      // SendGrid Configuration
      sendgrid: {
        apiKey: this.get('SENDGRID_API_KEY') || '',
        fromEmail: this.get('SENDGRID_FROM_EMAIL') || '<EMAIL>',
        fromName: this.get('SENDGRID_FROM_NAME') || 'Qeep',
        webhookSecret: this.get('SENDGRID_WEBHOOK_SECRET') || '',
      },

      // Mailgun Configuration
      mailgun: {
        apiKey: this.get('MAILGUN_API_KEY') || '',
        domain: this.get('MAILGUN_DOMAIN') || '',
        fromEmail: this.get('MAILGUN_FROM_EMAIL') || '<EMAIL>',
        fromName: this.get('MAILGUN_FROM_NAME') || 'Qeep',
        baseUrl: this.get('MAILGUN_BASE_URL') || 'https://api.mailgun.net',
        webhookSecret: this.get('MAILGUN_WEBHOOK_SECRET') || '',
      },

      // AWS SES Configuration
      ses: {
        accessKeyId: this.get('AWS_ACCESS_KEY_ID') || '',
        secretAccessKey: this.get('AWS_SECRET_ACCESS_KEY') || '',
        region: this.get('AWS_REGION') || 'us-east-1',
        fromEmail: this.get('SES_FROM_EMAIL') || '<EMAIL>',
        fromName: this.get('SES_FROM_NAME') || 'Qeep',
      },

      // Application URLs
      appUrl: this.get('APP_URL') || 'https://app.qeep.dev',
      supportEmail: this.get('SUPPORT_EMAIL') || '<EMAIL>',

      // Template Configuration
      template: {
        cacheSize: parseInt(this.get('TEMPLATE_CACHE_SIZE') || '1000', 10),
        cacheTtl: parseInt(this.get('TEMPLATE_CACHE_TTL') || '3600', 10),
      },
    };
  }
}
