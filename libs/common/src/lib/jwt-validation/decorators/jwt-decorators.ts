import { createParamDecorator, ExecutionContext, SetMetadata } from '@nestjs/common';
import { IS_PUBLIC_KEY, JWT_OPTIONS_KEY } from '../guards/jwt-auth.guard';
import { AuthenticatedRequest } from '../interfaces/authenticated-request.interface';
import { JwtPayload, JwtValidationOptions, TenantContext, UserContext } from '../interfaces/jwt-validation.interface';

/**
 * Mark a route as public (skip JWT validation)
 */
export const Public = () => SetMetadata(IS_PUBLIC_KEY, true);

/**
 * Set JWT validation options for a route
 */
export const JwtOptions = (options: JwtValidationOptions) => SetMetadata(JWT_OPTIONS_KEY, options);

/**
 * Extract user context from request
 */
export const User = createParamDecorator((data: keyof UserContext | undefined, ctx: ExecutionContext): UserContext | any => {
  const request = ctx.switchToHttp().getRequest<AuthenticatedRequest>();
  const user = request.user;

  if (!user) {
    return undefined;
  }

  return data ? user[data] : user;
});

/**
 * Extract tenant context from request
 */
export const Tenant = createParamDecorator((data: keyof TenantContext | undefined, ctx: ExecutionContext): TenantContext | any => {
  const request = ctx.switchToHttp().getRequest<AuthenticatedRequest>();
  const tenant = request.tenant;

  if (!tenant) {
    return undefined;
  }

  return data ? tenant[data] : tenant;
});

/**
 * Extract JWT payload from request
 */
export const JwtPayloadDecorator = createParamDecorator((data: keyof JwtPayload | undefined, ctx: ExecutionContext): JwtPayload | any => {
  const request = ctx.switchToHttp().getRequest<AuthenticatedRequest>();
  const payload = request.jwtPayload;

  if (!payload) {
    return undefined;
  }

  return data ? payload[data] : payload;
});

/**
 * Extract user ID from request
 */
export const UserId = createParamDecorator((_data: unknown, ctx: ExecutionContext): string | undefined => {
  const request = ctx.switchToHttp().getRequest<AuthenticatedRequest>();
  const user = request.user;
  return user?.userId;
});

/**
 * Extract tenant ID from request
 */
export const TenantId = createParamDecorator((_data: unknown, ctx: ExecutionContext): string | undefined => {
  const request = ctx.switchToHttp().getRequest<AuthenticatedRequest>();
  const tenant = request.tenant;
  return tenant?.tenantId;
});

/**
 * Extract user email from request
 */
export const UserEmail = createParamDecorator((_data: unknown, ctx: ExecutionContext): string | undefined => {
  const request = ctx.switchToHttp().getRequest<AuthenticatedRequest>();
  const user = request.user;
  return user?.email;
});

/**
 * Extract user roles from request
 */
export const UserRoles = createParamDecorator((_data: unknown, ctx: ExecutionContext): string[] => {
  const request = ctx.switchToHttp().getRequest<AuthenticatedRequest>();
  const user = request.user;
  return user?.roles || [];
});

/**
 * Extract user permissions from request
 */
export const UserPermissions = createParamDecorator((_data: unknown, ctx: ExecutionContext): string[] => {
  const request = ctx.switchToHttp().getRequest<AuthenticatedRequest>();
  const user = request.user;
  return user?.permissions || [];
});

/**
 * Check if user has specific role
 * @param role - The role to check (type-safe enum value)
 */
export const HasRole = (role: string) =>
  createParamDecorator((_data: unknown, ctx: ExecutionContext): boolean => {
    const request = ctx.switchToHttp().getRequest<AuthenticatedRequest>();
    const user = request.user;
    return user?.roles?.includes(role) || false;
  });

/**
 * Check if user has specific permission
 * @param permission - The permission to check (type-safe enum value)
 */
export const HasPermission = (permission: string) =>
  createParamDecorator((_data: unknown, ctx: ExecutionContext): boolean => {
    const request = ctx.switchToHttp().getRequest<AuthenticatedRequest>();
    const user = request.user;
    return user?.permissions?.includes(permission) || false;
  });

/**
 * Require specific roles for a route
 * @param roles - Array of roles required (type-safe enum values)
 * @example @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR, TenantRole.TENANT_ADMIN)
 */
export const RequireRoles = (...roles: string[]) => SetMetadata('requiredRoles', roles);

/**
 * Require specific permissions for a route
 * @param permissions - Array of permissions required (type-safe enum values)
 * @example @RequirePermissions(PlatformPermission.TENANT_ONBOARDING_INITIATE)
 */
export const RequirePermissions = (...permissions: string[]) => SetMetadata('requiredPermissions', permissions);

/**
 * Require tenant context for a route
 */
export const RequireTenant = () => SetMetadata('requireTenant', true);

/**
 * Skip tenant validation for a route (useful for admin endpoints)
 */
export const SkipTenantValidation = () => SetMetadata('skipTenantValidation', true);
