/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';
import * as jwt from 'jsonwebtoken';
import { JwtPayload, JwtValidationErrorCode, JwtValidationOptions, JwtValidationResult, TenantContext, UserContext } from '../interfaces/jwt-validation.interface';

@Injectable()
export class JwtValidationService {
  private readonly logger = new Logger(JwtValidationService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * Extract JWT token from request
   */
  extractTokenFromRequest(request: Request): string | null {
    const authHeader = request.headers.authorization;

    if (!authHeader) {
      return null;
    }

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }

    return parts[1];
  }

  /**
   * Validate JWT token using simple secret-based validation
   */
  async validateToken(token: string, options: JwtValidationOptions = {}): Promise<JwtValidationResult> {
    try {
      // Basic token format validation
      if (!token || typeof token !== 'string') {
        return this.createErrorResult(JwtValidationErrorCode.MISSING_TOKEN, 'Token is missing or invalid format');
      }

      // Get JWT secret from config
      const jwtSecret = this.configService.get<string>('JWT_SECRET');
      if (!jwtSecret) {
        this.logger.error('JWT_SECRET not configured');
        return this.createErrorResult(JwtValidationErrorCode.INVALID_FORMAT, 'JWT configuration error');
      }

      // Verify and decode token
      const payload = jwt.verify(token, jwtSecret) as JwtPayload;

      // Extract contexts if requested
      const userContext = options.extractUserContext !== false ? this.extractUserContext(payload) : undefined;
      const tenantContext = options.extractTenantContext ? this.extractTenantContext(payload) : undefined;

      return {
        valid: true,
        payload,
        userContext,
        tenantContext,
      };
    } catch (error) {
      const errorCode = this.mapErrorToCode(error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown validation error';

      this.logger.warn(`JWT validation failed: ${errorMessage}`, {
        errorCode,
        token: token.substring(0, 20) + '...',
      });

      return this.createErrorResult(errorCode, errorMessage);
    }
  }

  /**
   * Extract user context from JWT payload
   */
  private extractUserContext(payload: JwtPayload): UserContext {
    return {
      userId: payload['userId'] || payload.sub || '',
      tenantId: payload['tenantId'],
      email: payload.email || '',
      roles: payload['roles'] || [],
      permissions: payload['permissions'] || [],
      isEmailVerified: payload.email_verified || false,
      metadata: {},
    };
  }

  /**
   * Extract tenant context from JWT payload
   */
  private extractTenantContext(payload: JwtPayload): TenantContext {
    return {
      tenantId: payload['tenantId'] || '',
      features: payload['features'] || [],
      limits: payload['limits'] || {},
      metadata: {
        tenantCode: payload['tenantCode'],
      },
    };
  }

  /**
   * Map JWT errors to validation error codes
   */
  private mapErrorToCode(error: any): JwtValidationErrorCode {
    if (error.name === 'TokenExpiredError') {
      return JwtValidationErrorCode.EXPIRED_TOKEN;
    }
    if (error.name === 'JsonWebTokenError') {
      return JwtValidationErrorCode.INVALID_SIGNATURE;
    }
    if (error.name === 'NotBeforeError') {
      return JwtValidationErrorCode.INVALID_FORMAT;
    }
    return JwtValidationErrorCode.INVALID_FORMAT;
  }

  /**
   * Create error result
   */
  private createErrorResult(errorCode: JwtValidationErrorCode, message: string): JwtValidationResult {
    return {
      valid: false,
      errorCode,
      error: message,
    };
  }
}
