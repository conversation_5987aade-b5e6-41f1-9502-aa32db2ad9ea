import { JwtValidationConfig } from '../interfaces/jwt-validation.interface';

export const DEFAULT_JWT_VALIDATION_CONFIG: JwtValidationConfig = {
  auth0: {
    domain: process.env['AUTH0_DOMAIN'] || 'your-domain.auth0.com',
    audience: process.env['AUTH0_AUDIENCE'] || 'https://api.qeep.com',
    issuer: process.env['AUTH0_ISSUER'] || 'https://your-domain.auth0.com/',
    algorithms: ['RS256'],
    jwksUri: process.env['AUTH0_JWKS_URI'] || 'https://your-domain.auth0.com/.well-known/jwks.json',
    cacheMaxEntries: parseInt(process.env['JWT_JWKS_CACHE_MAX_ENTRIES'] || '5', 10),
    cacheMaxAge: parseInt(process.env['JWT_JWKS_CACHE_MAX_AGE'] || '600000', 10), // 10 minutes
    rateLimit: process.env['JWT_JWKS_RATE_LIMIT'] === 'true',
    jwksRequestsPerMinute: parseInt(process.env['JWT_JWKS_REQUESTS_PER_MINUTE'] || '10', 10),
  },

  validation: {
    clockTolerance: parseInt(process.env['JWT_CLOCK_TOLERANCE'] || '30', 10), // 30 seconds
    maxAge: process.env['JWT_MAX_AGE'] || '24h',
    ignoreExpiration: process.env['JWT_IGNORE_EXPIRATION'] === 'true',
    ignoreNotBefore: process.env['JWT_IGNORE_NOT_BEFORE'] === 'true',
    requireAudience: process.env['JWT_REQUIRE_AUDIENCE'] !== 'false',
    requireIssuer: process.env['JWT_REQUIRE_ISSUER'] !== 'false',
  },

  extraction: {
    userIdClaim: process.env['JWT_USER_ID_CLAIM'] || 'sub',
    tenantIdClaim: process.env['JWT_TENANT_ID_CLAIM'] || 'tenant_id',
    emailClaim: process.env['JWT_EMAIL_CLAIM'] || 'email',
    rolesClaim: process.env['JWT_ROLES_CLAIM'] || 'roles',
    permissionsClaim: process.env['JWT_PERMISSIONS_CLAIM'] || 'permissions',
    customClaims: (process.env['JWT_CUSTOM_CLAIMS'] || '').split(',').filter(Boolean),
  },

  security: {
    enableBlacklist: process.env['JWT_ENABLE_BLACKLIST'] === 'true',
    blacklistTtl: parseInt(process.env['JWT_BLACKLIST_TTL'] || '86400', 10), // 24 hours
    enableRateLimit: process.env['JWT_ENABLE_RATE_LIMIT'] === 'true',
    maxRequestsPerMinute: parseInt(process.env['JWT_MAX_REQUESTS_PER_MINUTE'] || '1000', 10),
  },
};

// Environment-specific configurations
export const DEVELOPMENT_JWT_CONFIG: Partial<JwtValidationConfig> = {
  validation: {
    clockTolerance: 60, // More lenient in development
    ignoreExpiration: false, // Still validate expiration in dev
    ignoreNotBefore: true, // More lenient for testing
    requireAudience: true,
    requireIssuer: true,
    maxAge: '24h',
  },
  security: {
    enableBlacklist: false, // Disable blacklist in development
    enableRateLimit: false, // Disable rate limiting in development
    blacklistTtl: 3600,
    maxRequestsPerMinute: 10000,
  },
};

export const PRODUCTION_JWT_CONFIG: Partial<JwtValidationConfig> = {
  validation: {
    clockTolerance: 30, // Strict in production
    ignoreExpiration: false,
    ignoreNotBefore: false,
    requireAudience: true,
    requireIssuer: true,
    maxAge: '8h', // Shorter token lifetime in production
  },
  security: {
    enableBlacklist: true,
    enableRateLimit: true,
    blacklistTtl: 86400, // 24 hours
    maxRequestsPerMinute: 1000,
  },
};

export const TEST_JWT_CONFIG: Partial<JwtValidationConfig> = {
  validation: {
    clockTolerance: 300, // Very lenient for tests
    ignoreExpiration: true, // Ignore expiration in tests
    ignoreNotBefore: true,
    requireAudience: false, // More flexible for testing
    requireIssuer: false,
    maxAge: '1d',
  },
  security: {
    enableBlacklist: false,
    enableRateLimit: false,
    blacklistTtl: 60,
    maxRequestsPerMinute: 100000,
  },
};

export const JWT_VALIDATION_CONFIG_TOKEN = 'JWT_VALIDATION_CONFIG';
