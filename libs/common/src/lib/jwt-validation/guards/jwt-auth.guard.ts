import { CanActivate, ExecutionContext, Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthenticatedRequest } from '../interfaces/authenticated-request.interface';
import { JwtValidationOptions } from '../interfaces/jwt-validation.interface';
import { JwtValidationService } from '../services/jwt-validation.service';

// Metadata keys for decorators
export const IS_PUBLIC_KEY = 'isPublic';
export const JWT_OPTIONS_KEY = 'jwtOptions';

@Injectable()
export class JwtAuthGuard implements CanActivate {
  private readonly logger = new Logger(JwtAuthGuard.name);

  constructor(private readonly jwtValidationService: JwtValidationService, private readonly reflector: Reflector) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if route is marked as public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [context.getHandler(), context.getClass()]);

    if (isPublic) {
      return true;
    }

    const request = context.switchToHttp().getRequest<AuthenticatedRequest>();

    // Extract token from request
    const token = this.jwtValidationService.extractTokenFromRequest(request);

    if (!token) {
      this.logger.warn('No JWT token found in request', {
        path: request.path,
        method: request.method,
        ip: request.ip,
      });
      throw new UnauthorizedException('No authorization token provided');
    }

    // Get JWT validation options from metadata
    const jwtOptions = this.reflector.getAllAndOverride<JwtValidationOptions>(JWT_OPTIONS_KEY, [context.getHandler(), context.getClass()]) || {};

    try {
      // Validate token
      const validationResult = await this.jwtValidationService.validateToken(token, {
        extractUserContext: true,
        extractTenantContext: true,
        ...jwtOptions,
      });

      if (!validationResult.valid) {
        this.logger.warn('JWT validation failed', {
          error: validationResult.error,
          errorCode: validationResult.errorCode,
          path: request.path,
          method: request.method,
          ip: request.ip,
        });

        throw new UnauthorizedException(validationResult.error || 'Invalid authorization token');
      }

      // Attach user and tenant context to request
      request.user = validationResult.userContext;
      request.tenant = validationResult.tenantContext;
      request.jwtPayload = validationResult.payload;

      return true;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }

      const errorMessage = error instanceof Error ? error.message : 'JWT validation error';
      this.logger.error('Unexpected error during JWT validation', {
        error: errorMessage,
        path: request.path,
        method: request.method,
        ip: request.ip,
      });

      throw new UnauthorizedException('Authorization failed');
    }
  }
}
