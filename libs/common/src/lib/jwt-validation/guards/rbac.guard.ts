import { CanActivate, ExecutionContext, ForbiddenException, Inject, Injectable, Logger, Optional } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserService } from '../../user/services/user.service';
import { AuthenticatedRequest } from '../interfaces/authenticated-request.interface';

/**
 * RBAC Guard that fetches user roles/permissions from user service
 * This approach keeps roles secure (not exposed in JWT tokens)
 * while maintaining code reuse across all services.
 *
 * The guard can work in two modes:
 * 1. With UserService injection - fetches fresh roles from user service (secure)
 * 2. Without UserService injection - uses roles from JWT token (less secure but faster)
 */
@Injectable()
export class RbacGuard implements CanActivate {
  private readonly logger = new Logger(RbacGuard.name);

  constructor(private reflector: Reflector, @Optional() @Inject(UserService) private readonly userService?: UserService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Get required roles and permissions from metadata
    const requiredRoles = this.reflector.getAllAndOverride<string[]>('requiredRoles', [context.getHandler(), context.getClass()]);

    const requiredPermissions = this.reflector.getAllAndOverride<string[]>('requiredPermissions', [context.getHandler(), context.getClass()]);

    // If no roles or permissions are required, allow access
    if ((!requiredRoles || requiredRoles.length === 0) && (!requiredPermissions || requiredPermissions.length === 0)) {
      return true;
    }

    const request = context.switchToHttp().getRequest<AuthenticatedRequest>();
    const user = request.user;

    if (!user) {
      this.logger.warn('User not found in request context', {
        path: request.path,
        method: request.method,
        ip: request.ip,
      });
      throw new ForbiddenException('User not authenticated');
    }

    // Get user roles and permissions
    let userRoles: string[] = [];
    let userPermissions: string[] = [];

    if (this.userService) {
      // Secure mode: Fetch fresh roles from user service
      try {
        userRoles = await this.userService.getUserRoles(user.userId);
        userPermissions = await this.userService.getUserPermissions(user.userId);
        this.logger.debug(`Fetched roles from user service for user ${user.userId}: ${userRoles.join(', ')}`);
      } catch (error) {
        this.logger.error(`Failed to fetch roles from user service for user ${user.userId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        throw new ForbiddenException('Failed to verify user permissions');
      }
    } else {
      // Fallback mode: Use roles from JWT token (less secure)
      userRoles = user.roles || [];
      userPermissions = user.permissions || [];
      this.logger.debug(`Using roles from JWT for user ${user.userId}: ${userRoles.join(', ')}`);
    }

    // Check roles if required
    if (requiredRoles && requiredRoles.length > 0) {
      if (!userRoles || userRoles.length === 0) {
        this.logger.warn(`User ${user.userId} has no roles assigned`, {
          userId: user.userId,
          path: request.path,
          method: request.method,
          requiredRoles,
        });
        throw new ForbiddenException('User has no roles assigned');
      }

      const hasRole = requiredRoles.some((role) => userRoles.includes(role));
      if (!hasRole) {
        this.logger.warn(`User ${user.userId} with roles [${userRoles.join(', ')}] attempted to access endpoint requiring roles [${requiredRoles.join(', ')}]`, {
          userId: user.userId,
          userRoles,
          requiredRoles,
          path: request.path,
          method: request.method,
          ip: request.ip,
        });
        throw new ForbiddenException(`Insufficient permissions. Required roles: ${requiredRoles.join(', ')}`);
      }
    }

    // Check permissions if required
    if (requiredPermissions && requiredPermissions.length > 0) {
      if (!userPermissions || userPermissions.length === 0) {
        this.logger.warn(`User ${user.userId} has no permissions assigned`, {
          userId: user.userId,
          path: request.path,
          method: request.method,
          requiredPermissions,
        });
        throw new ForbiddenException('User has no permissions assigned');
      }

      const hasPermission = requiredPermissions.some((permission) => userPermissions.includes(permission));

      if (!hasPermission) {
        this.logger.warn(
          `User ${user.userId} with permissions [${userPermissions.join(', ')}] attempted to access endpoint requiring permissions [${requiredPermissions.join(', ')}]`,
          {
            userId: user.userId,
            userPermissions,
            requiredPermissions,
            path: request.path,
            method: request.method,
            ip: request.ip,
          },
        );
        throw new ForbiddenException(`Insufficient permissions. Required permissions: ${requiredPermissions.join(', ')}`);
      }
    }

    this.logger.debug(`User ${user.userId} authorized for endpoint`, {
      userId: user.userId,
      userRoles: user.roles,
      userPermissions: user.permissions,
      path: request.path,
      method: request.method,
    });
    return true;
  }
}
