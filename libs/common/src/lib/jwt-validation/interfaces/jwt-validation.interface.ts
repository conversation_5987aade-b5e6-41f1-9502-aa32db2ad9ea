/* eslint-disable @typescript-eslint/no-explicit-any */
export interface JwtValidationConfig {
  auth0: {
    domain: string;
    audience: string;
    issuer: string;
    algorithms: string[];
    jwksUri: string;
    cacheMaxEntries: number;
    cacheMaxAge: number;
    rateLimit: boolean;
    jwksRequestsPerMinute: number;
  };
  validation: {
    clockTolerance: number; // seconds
    maxAge: string; // e.g., '1d', '24h'
    ignoreExpiration: boolean;
    ignoreNotBefore: boolean;
    requireAudience: boolean;
    requireIssuer: boolean;
  };
  extraction: {
    userIdClaim: string;
    tenantIdClaim: string;
    emailClaim: string;
    rolesClaim: string;
    permissionsClaim: string;
    customClaims: string[];
  };
  security: {
    enableBlacklist: boolean;
    blacklistTtl: number; // seconds
    enableRateLimit: boolean;
    maxRequestsPerMinute: number;
  };
}

export interface JwtPayload {
  // Standard JWT claims
  iss?: string; // issuer
  sub?: string; // subject (user ID)
  aud?: string | string[]; // audience
  exp?: number; // expiration time
  nbf?: number; // not before
  iat?: number; // issued at
  jti?: string; // JWT ID

  // Auth0 specific claims
  email?: string;
  email_verified?: boolean;
  name?: string;
  nickname?: string;
  picture?: string;
  updated_at?: string;

  // Custom claims (configurable)
  tenant_id?: string;
  organization_id?: string;
  roles?: string[];
  permissions?: string[];
  scope?: string;

  // Additional custom claims
  [key: string]: any;
}

export interface UserContext {
  userId: string;
  tenantId?: string;
  email?: string;
  roles: string[];
  permissions: string[];
  isEmailVerified: boolean;
  metadata: Record<string, any>;
}

export interface TenantContext {
  tenantId: string;
  organizationId?: string;
  plan?: string;
  features: string[];
  limits: Record<string, number>;
  metadata: Record<string, any>;
}

export interface JwtValidationResult {
  valid: boolean;
  payload?: JwtPayload;
  userContext?: UserContext;
  tenantContext?: TenantContext;
  error?: string;
  errorCode?: JwtValidationErrorCode;
}

export enum JwtValidationErrorCode {
  MISSING_TOKEN = 'MISSING_TOKEN',
  INVALID_FORMAT = 'INVALID_FORMAT',
  EXPIRED_TOKEN = 'EXPIRED_TOKEN',
  INVALID_SIGNATURE = 'INVALID_SIGNATURE',
  INVALID_AUDIENCE = 'INVALID_AUDIENCE',
  INVALID_ISSUER = 'INVALID_ISSUER',
  BLACKLISTED_TOKEN = 'BLACKLISTED_TOKEN',
  RATE_LIMITED = 'RATE_LIMITED',
  JWKS_ERROR = 'JWKS_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export interface JwtValidationMetrics {
  totalValidations: number;
  successfulValidations: number;
  failedValidations: number;
  errorsByType: Record<JwtValidationErrorCode, number>;
  averageValidationTime: number;
  jwksRequests: number;
  cacheHits: number;
  cacheMisses: number;
  blacklistedTokens: number;
  rateLimitedRequests: number;
  lastValidationTime?: Date;
}

export interface JwtBlacklistEntry {
  jti: string; // JWT ID
  userId: string;
  reason: string;
  blacklistedAt: Date;
  expiresAt: Date;
}

export interface JwtValidationOptions {
  skipBlacklistCheck?: boolean;
  skipRateLimit?: boolean;
  extractUserContext?: boolean;
  extractTenantContext?: boolean;
  customClaimsExtraction?: string[];
}
