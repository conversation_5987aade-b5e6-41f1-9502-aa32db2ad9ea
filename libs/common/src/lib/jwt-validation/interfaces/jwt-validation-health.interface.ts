import { JwtValidationMetrics, JwtValidationConfig } from './jwt-validation.interface';

export interface JwtValidationHealthStatus {
  healthy: boolean;
  status: 'healthy' | 'warning' | 'error';
  message: string;
  details: {
    configurationValid: boolean;
    configurationErrors: string[];
    auth0Connection: {
      reachable: boolean;
      jwksEndpoint: string;
      lastCheck: Date;
      responseTime?: number;
    };
    metrics: JwtValidationMetrics;
    blacklistStatus: {
      enabled: boolean;
      entriesCount: number;
      lastCleanup?: Date;
    };
    rateLimitStatus: {
      enabled: boolean;
      currentRequests: number;
      limit: number;
    };
    environment: string;
    lastCheck: Date;
  };
}

export interface JwtValidationHealthSummary {
  totalValidations: number;
  successRate: number;
  averageResponseTime: number;
  auth0Connectivity: boolean;
  configurationValid: boolean;
  environment: string;
}

export interface JwtValidationTestResult {
  success: boolean;
  testToken?: string;
  validationResult?: any;
  errors: string[];
  performanceMetrics: {
    validationTime: number;
    jwksLookupTime?: number;
    totalTime: number;
  };
}

export interface Auth0ConnectivityTest {
  success: boolean;
  jwksEndpoint: string;
  responseTime: number;
  keysCount?: number;
  error?: string;
}
