/* eslint-disable @typescript-eslint/no-explicit-any */
import { DynamicModule, Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DEFAULT_JWT_VALIDATION_CONFIG, DEVELOPMENT_JWT_CONFIG, JWT_VALIDATION_CONFIG_TOKEN, PRODUCTION_JWT_CONFIG, TEST_JWT_CONFIG } from './config/jwt-validation.config';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { JwtValidationConfig } from './interfaces/jwt-validation.interface';
import { JwtValidationService } from './services/jwt-validation.service';

@Global()
@Module({})
export class JwtValidationModule {
  static forRoot(config?: Partial<JwtValidationConfig>): DynamicModule {
    const environment = process.env['NODE_ENV'] || 'development';

    // Merge environment-specific configuration
    let envConfig: Partial<JwtValidationConfig> = {};
    switch (environment) {
      case 'production':
        envConfig = PRODUCTION_JWT_CONFIG;
        break;
      case 'test':
        envConfig = TEST_JWT_CONFIG;
        break;
      case 'development':
      default:
        envConfig = DEVELOPMENT_JWT_CONFIG;
        break;
    }

    const mergedConfig = {
      ...DEFAULT_JWT_VALIDATION_CONFIG,
      ...envConfig,
      ...config,
    };

    return {
      module: JwtValidationModule,
      imports: [ConfigModule],
      providers: [
        {
          provide: JWT_VALIDATION_CONFIG_TOKEN,
          useValue: mergedConfig,
        },
        JwtValidationService,
        JwtAuthGuard,
      ],
      exports: [JwtValidationService, JwtAuthGuard, JWT_VALIDATION_CONFIG_TOKEN],
      global: true,
    };
  }

  static forRootAsync(options: { useFactory: (...args: any[]) => Promise<JwtValidationConfig> | JwtValidationConfig; inject?: any[] }): DynamicModule {
    return {
      module: JwtValidationModule,
      imports: [ConfigModule],
      providers: [
        {
          provide: JWT_VALIDATION_CONFIG_TOKEN,
          useFactory: options.useFactory,
          inject: options.inject || [],
        },
        JwtValidationService,
        JwtAuthGuard,
      ],
      exports: [JwtValidationService, JwtAuthGuard, JWT_VALIDATION_CONFIG_TOKEN],
      global: true,
    };
  }
}
