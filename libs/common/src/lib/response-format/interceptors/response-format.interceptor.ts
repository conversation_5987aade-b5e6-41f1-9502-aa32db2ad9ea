/* eslint-disable @typescript-eslint/no-explicit-any */
import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Inject, Injectable, NestInterceptor, Optional } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { v4 as uuidv4 } from 'uuid';
import { ResponseFormatConfig, StandardApiResponse } from '../interfaces/response-format.interface';

/**
 * Interceptor that formats all API responses to a standard format.
 *
 * This interceptor automatically wraps all HTTP responses in a consistent
 * StandardApiResponse structure, adding metadata and ensuring uniform
 * response formatting across all endpoints.
 *
 * Features:
 * - Automatic response wrapping with success/error indicators
 * - Configurable metadata inclusion (timestamps, request IDs, versions)
 * - Custom success messages per status code
 * - Preserves existing StandardApiResponse objects
 * - Context-aware formatting (HTTP only, not gRPC)
 *
 * @example
 * ```typescript
 * // Before interceptor (controller returns):
 * { id: "123", name: "<PERSON>" }
 *
 * // After interceptor (client receives):
 * {
 *   success: true,
 *   status_code: 200,
 *   message: "Request successful",
 *   data: { id: "123", name: "<PERSON>" },
 *   meta: {
 *     timestamp: "2025-07-05T01:06:53.083Z",
 *     request_id: "35c40696-e771-4812-b1cf-2cdb847ffe50"
 *   }
 * }
 * ```
 *
 * @see {@link StandardApiResponse} for the response format structure
 * @see {@link ResponseFormatConfig} for configuration options
 */
@Injectable()
export class ResponseFormatInterceptor implements NestInterceptor {
  /** Configuration for response formatting behavior */
  private readonly config: ResponseFormatConfig;

  /**
   * Creates a new ResponseFormatInterceptor instance.
   *
   * @param config - Optional configuration to override defaults
   */
  constructor(
    @Optional()
    @Inject('RESPONSE_FORMAT_CONFIG')
    config?: Partial<ResponseFormatConfig>,
  ) {
    // Merge provided config with sensible defaults
    this.config = {
      enabled: true,
      includeMeta: true,
      includeRequestId: true,
      includeVersion: false,
      version: '1.0',
      includeTimestamp: true,
      successMessages: {
        200: 'Request successful',
        201: 'Resource created successfully',
        202: 'Request accepted',
        204: 'Request successful',
      },
      transformErrors: true,
      includeErrorDetailsInProduction: false,
      ...config,
    };
  }

  /**
   * Intercepts HTTP responses and formats them according to the standard format.
   *
   * This method is called for every HTTP request/response cycle. It:
   * 1. Checks if formatting is enabled and context is HTTP
   * 2. Generates a request ID if needed
   * 3. Processes the response data through the formatting pipeline
   * 4. Returns the formatted response
   *
   * @param context - The execution context containing request/response information
   * @param next - The next handler in the chain
   * @returns Observable of the formatted response data
   */
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    // Only format HTTP responses, skip gRPC and other contexts
    if (context.getType() !== 'http' || !this.config.enabled) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();

    // Generate request ID if not present and tracking is enabled
    if (!request.id && this.config.includeRequestId) {
      request.id = uuidv4();
    }

    return next.handle().pipe(
      map((data) => {
        // If data is already in standard format, return as-is to avoid double-wrapping
        if (this.isStandardFormat(data)) {
          return data;
        }

        // Format the response according to our standard structure
        return this.formatResponse(data, response.statusCode, request);
      }),
    );
  }

  /**
   * Checks if the response data is already in standard format.
   *
   * This prevents double-wrapping of responses that are already formatted.
   * A response is considered "standard format" if it has the required
   * success and message properties. The status_code is optional since
   * some services may return structured responses without it.
   *
   * @param data - The response data to check
   * @returns True if the data is already in standard format
   *
   * @example
   * ```typescript
   * // Returns true - full standard format
   * isStandardFormat({
   *   success: true,
   *   status_code: 200,
   *   message: "OK",
   *   data: { id: "123" }
   * });
   *
   * // Returns true - partial standard format (missing status_code)
   * isStandardFormat({
   *   success: true,
   *   message: "Account created successfully",
   *   data: { user_id: "123" }
   * });
   *
   * // Returns false - not standard format
   * isStandardFormat({ id: "123", name: "John" });
   * ```
   */
  private isStandardFormat(data: any): boolean {
    return data && typeof data === 'object' && 'success' in data && 'message' in data && typeof data.success === 'boolean' && typeof data.message === 'string';
  }

  /**
   * Formats response data according to the standard API response structure.
   *
   * This method wraps the original response data in a StandardApiResponse
   * object, adding success indicators, status codes, messages, and metadata.
   * If the data already contains some standard fields, they are preserved.
   *
   * @param data - The original response data from the controller
   * @param statusCode - HTTP status code of the response
   * @param request - The HTTP request object
   * @returns Formatted response in StandardApiResponse structure
   *
   * @example
   * ```typescript
   * // Input: { id: "123", name: "John" }, 200, request
   * // Output: {
   * //   success: true,
   * //   status_code: 200,
   * //   message: "Request successful",
   * //   data: { id: "123", name: "John" },
   * //   meta: { timestamp: "...", request_id: "..." }
   * // }
   *
   * // Input: { success: true, message: "Custom message", data: {...} }, 201, request
   * // Output: {
   * //   success: true,
   * //   status_code: 201,
   * //   message: "Custom message", // preserved from input
   * //   data: {...}, // preserved from input
   * //   meta: { timestamp: "...", request_id: "..." }
   * // }
   * ```
   */
  private formatResponse(data: any, statusCode: number, request: any): StandardApiResponse {
    const isSuccess = statusCode >= 200 && statusCode < 300;
    const defaultMessage = this.getSuccessMessage(statusCode);

    // If data is already partially formatted, preserve existing fields
    if (data && typeof data === 'object' && ('success' in data || 'message' in data)) {
      const response: StandardApiResponse = {
        success: data.success !== undefined ? data.success : isSuccess,
        statusCode: data.statusCode !== undefined ? data.statusCode : statusCode,
        message: data.message !== undefined ? data.message : defaultMessage,
      };

      // Add data if present and not already structured
      if (data.data !== undefined) {
        response.data = data.data;
      } else if (data !== undefined && data !== null) {
        // If the input has other properties besides success/message/statusCode,
        // extract them as data
        const standardFields = ['success', 'statusCode', 'message', 'meta'];
        const otherProps = Object.keys(data)
          .filter((key) => !standardFields.includes(key))
          .reduce((obj, key) => {
            obj[key] = data[key];
            return obj;
          }, {} as any);

        if (Object.keys(otherProps).length > 0) {
          response.data = otherProps;
        }
      }

      // Add metadata if enabled in configuration
      if (this.config.includeMeta) {
        response.meta = data.meta || this.buildMetadata(request);
      }

      return response;
    }

    // Standard formatting for non-structured data
    const response: StandardApiResponse = {
      success: isSuccess,
      statusCode: statusCode,
      message: defaultMessage,
    };

    // Add data if present (exclude undefined/null to keep response clean)
    if (data !== undefined && data !== null) {
      response.data = data;
    }

    // Add metadata if enabled in configuration
    if (this.config.includeMeta) {
      response.meta = this.buildMetadata(request);
    }

    return response;
  }

  /**
   * Builds the metadata object for the response.
   *
   * Constructs a metadata object containing various pieces of information
   * about the request/response cycle, based on the current configuration.
   * Only includes fields that are enabled in the configuration.
   *
   * @param request - The HTTP request object
   * @returns Metadata object with enabled fields
   *
   * @example
   * ```typescript
   * // With all metadata enabled:
   * {
   *   timestamp: "2025-07-05T01:06:53.083Z",
   *   request_id: "35c40696-e771-4812-b1cf-2cdb847ffe50",
   *   version: "1.0"
   * }
   *
   * // With only timestamp enabled:
   * {
   *   timestamp: "2025-07-05T01:06:53.083Z"
   * }
   * ```
   */
  private buildMetadata(request: any): any {
    const meta: any = {};

    // Add timestamp if enabled (useful for caching and debugging)
    if (this.config.includeTimestamp) {
      meta.timestamp = new Date().toISOString();
    }

    // Add request ID if enabled and present (for request tracing)
    if (this.config.includeRequestId && request.id) {
      meta.request_id = request.id;
    }

    // Add API version if enabled (for version tracking)
    if (this.config.includeVersion) {
      meta.version = this.config.version;
    }

    return meta;
  }

  /**
   * Gets the appropriate success message for a given HTTP status code.
   *
   * Looks up the status code in the configured success messages,
   * falling back to a default message if no custom message is configured.
   *
   * @param statusCode - HTTP status code
   * @returns Human-readable success message
   *
   * @example
   * ```typescript
   * getSuccessMessage(200); // "Request successful"
   * getSuccessMessage(201); // "Resource created successfully"
   * getSuccessMessage(999); // "Request successful" (fallback)
   * ```
   */
  private getSuccessMessage(statusCode: number): string {
    return this.config.successMessages?.[statusCode] || 'Request successful';
  }
}
