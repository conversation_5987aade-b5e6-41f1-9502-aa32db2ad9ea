import { Module } from '@nestjs/common';
import { ResponseFormatExceptionFilter } from './filters/response-format-exception.filter';
import { ResponseFormatInterceptor } from './interceptors/response-format.interceptor';

/**
 * Response format module that provides standardized API response formatting
 */
@Module({
  providers: [
    ResponseFormatInterceptor,
    ResponseFormatExceptionFilter,
  ],
  exports: [
    ResponseFormatInterceptor,
    ResponseFormatExceptionFilter,
  ],
})
export class ResponseFormatModule {}
