/* eslint-disable @typescript-eslint/no-explicit-any */
import { ArgumentsHost, Catch, ExceptionFilter, HttpException, HttpStatus, Inject, Injectable, Optional } from '@nestjs/common';
import { Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { ConfigService } from '../../config/config.service';
import { ResponseFormatConfig, StandardApiResponse } from '../interfaces/response-format.interface';

/**
 * Exception filter that formats all error responses to a standard format.
 *
 * This filter catches all exceptions thrown by the application and formats
 * them according to the StandardApiResponse structure. It provides consistent
 * error handling across all endpoints and can be configured to include or
 * exclude sensitive information based on the environment.
 *
 * Features:
 * - Catches all types of exceptions (HTTP, validation, system errors)
 * - Formats errors in StandardApiResponse structure
 * - Environment-aware error detail inclusion
 * - Request ID correlation for debugging
 * - Configurable error transformation
 * - Proper HTTP status code mapping
 *
 * @example
 * ```typescript
 * // When a ValidationException is thrown:
 * throw new BadRequestException({
 *   message: ['Email is required', 'Password too short'],
 *   error: 'Validation failed'
 * });
 *
 * // Client receives:
 * {
 *   success: false,
 *   status_code: 400,
 *   message: "Validation failed",
 *   error: {
 *     code: "BAD_REQUEST",
 *     message: "Email is required, Password too short",
 *     timestamp: "2025-07-05T01:06:53.083Z",
 *     path: "/api/users"
 *   },
 *   meta: {
 *     timestamp: "2025-07-05T01:06:53.083Z",
 *     request_id: "35c40696-e771-4812-b1cf-2cdb847ffe50"
 *   }
 * }
 * ```
 *
 * @see {@link StandardApiResponse} for the response format structure
 * @see {@link ResponseFormatConfig} for configuration options
 */
@Injectable()
@Catch()
export class ResponseFormatExceptionFilter implements ExceptionFilter {
  /** Configuration for error response formatting behavior */
  private readonly config: ResponseFormatConfig;

  /**
   * Creates a new ResponseFormatExceptionFilter instance.
   *
   * @param config - Optional configuration to override defaults
   * @param configService - Optional config service for environment variables
   */
  constructor(
    @Optional()
    @Inject('RESPONSE_FORMAT_CONFIG')
    config?: Partial<ResponseFormatConfig>,
    @Optional()
    private readonly configService?: ConfigService,
  ) {
    // Merge provided config with sensible defaults
    this.config = {
      enabled: true,
      includeMeta: true,
      includeRequestId: true,
      includeVersion: false,
      version: '1.0',
      includeTimestamp: true,
      transformErrors: true,
      includeErrorDetailsInProduction: false,
      ...config,
    };
  }

  /**
   * Catches and formats exceptions according to the standard error response format.
   *
   * This method is called whenever an unhandled exception occurs in the application.
   * It transforms the exception into a standardized error response and sends it
   * to the client with the appropriate HTTP status code.
   *
   * @param exception - The exception that was thrown
   * @param host - The arguments host containing request/response context
   *
   * @example
   * ```typescript
   * // When this exception is thrown:
   * throw new NotFoundException('User not found');
   *
   * // This method formats it as:
   * {
   *   success: false,
   *   status_code: 404,
   *   message: "User not found",
   *   error: {
   *     code: "NOT_FOUND",
   *     message: "User not found",
   *     timestamp: "2025-07-05T01:06:53.083Z",
   *     path: "/api/users/123"
   *   }
   * }
   * ```
   */
  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<any>();

    // Only format HTTP responses, skip gRPC and other contexts
    if (host.getType() !== 'http' || !this.config.enabled || !this.config.transformErrors) {
      // Fall back to default error handling when transformation is disabled
      if (exception instanceof HttpException) {
        response.status(exception.getStatus()).json(exception.getResponse());
      } else {
        response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Internal server error',
        });
      }
      return;
    }

    // Generate request ID if not present and tracking is enabled
    if (!request.id && this.config.includeRequestId) {
      request.id = uuidv4();
    }

    // Determine appropriate HTTP status code for the exception
    const statusCode = this.getStatusCode(exception);

    // Format the exception into our standard error response structure
    const errorResponse = this.formatErrorResponse(exception, statusCode, request);

    // Send the formatted error response with appropriate status code
    response.status(statusCode).json(errorResponse);
  }

  /**
   * Get HTTP status code from exception
   */
  private getStatusCode(exception: any): number {
    if (exception instanceof HttpException) {
      return exception.getStatus();
    }
    return HttpStatus.INTERNAL_SERVER_ERROR;
  }

  /**
   * Format error response to standard format
   */
  private formatErrorResponse(exception: any, statusCode: number, request: any): StandardApiResponse {
    const response: StandardApiResponse = {
      success: false,
      statusCode: statusCode,
      message: this.getErrorMessage(exception, statusCode),
      error: this.buildErrorDetails(exception, statusCode, request),
    };

    // Add metadata if enabled
    if (this.config.includeMeta) {
      response.meta = this.buildMetadata(request);
    }

    return response;
  }

  /**
   * Get error message from exception
   */
  private getErrorMessage(exception: any, statusCode: number): string {
    if (exception instanceof HttpException) {
      const response = exception.getResponse();
      if (typeof response === 'string') {
        return response;
      }
      if (typeof response === 'object' && response && 'message' in response) {
        const message = (response as any).message;
        return Array.isArray(message) ? message.join(', ') : message;
      }
    }

    // Default messages for common status codes
    const defaultMessages: { [key: number]: string } = {
      400: 'Bad Request',
      401: 'Unauthorized',
      403: 'Forbidden',
      404: 'Not Found',
      409: 'Conflict',
      422: 'Unprocessable Entity',
      500: 'Internal Server Error',
    };

    return defaultMessages[statusCode] || 'An error occurred';
  }

  /**
   * Build error details object
   */
  private buildErrorDetails(exception: any, statusCode: number, request: any): any {
    const error: any = {
      message: this.getErrorMessage(exception, statusCode),
      timestamp: new Date().toISOString(),
      path: request.url,
    };

    // Add error code if available
    if (exception.code) {
      error.code = exception.code;
    }

    // Add detailed error information in development or if explicitly enabled
    const isDevelopment = this.configService?.get('NODE_ENV') !== 'production';
    if (isDevelopment || this.config.includeErrorDetailsInProduction) {
      if (exception instanceof HttpException) {
        const response = exception.getResponse();
        if (typeof response === 'object') {
          error.details = response;
        }
      }

      // Add stack trace in development
      // if (isDevelopment && exception.stack) {
      //   error.stack = exception.stack;
      // }
    }

    return error;
  }

  /**
   * Build metadata object
   */
  private buildMetadata(request: any): any {
    const meta: any = {};

    if (this.config.includeTimestamp) {
      meta.timestamp = new Date().toISOString();
    }

    if (this.config.includeRequestId && request.id) {
      meta.request_id = request.id;
    }

    if (this.config.includeVersion) {
      meta.version = this.config.version;
    }

    return meta;
  }
}
