import { Expose } from 'class-transformer';

/**
 * Common pagination metadata DTO
 */
export class PaginationDataDto {
  @Expose()
  page!: number;

  @Expose()
  limit!: number;

  @Expose()
  total!: number;

  @Expose({ name: 'totalPages' })
  totalPages!: number;

  @Expose({ name: 'has_next' })
  hasNext!: boolean;

  @Expose({ name: 'has_previous' })
  hasPrevious!: boolean;
}

/**
 * Common statistics DTO for various services
 */
export class StatsDataDto {
  @Expose()
  total!: number;

  @Expose()
  active!: number;

  @Expose()
  inactive!: number;

  @Expose()
  additional?: Record<string, any>;
}

/**
 * Common health check data DTO
 */
export class HealthDataDto {
  @Expose()
  status!: string;

  @Expose()
  uptime!: number;

  @Expose()
  version!: string;

  @Expose()
  details?: Record<string, any>;
}

/**
 * Common queue statistics DTO
 */
export class QueueStatsDataDto {
  @Expose()
  waiting!: number;

  @Expose()
  active!: number;

  @Expose()
  completed!: number;

  @Expose()
  failed!: number;

  @Expose()
  delayed!: number;

  @Expose({ name: 'processing_rate' })
  processingRate?: number;
}

/**
 * Common operation result DTO for actions that don't return specific data
 */
export class OperationResultDataDto {
  @Expose({ name: 'operation_id' })
  operationId!: string;

  @Expose()
  status!: string;

  @Expose({ name: 'affected_count' })
  affectedCount?: number;

  @Expose()
  details?: Record<string, any>;
}

/**
 * Common list response data DTO with pagination
 */
export class PaginatedListDataDto<T> {
  @Expose()
  items!: T[];

  @Expose()
  pagination!: PaginationDataDto;
}

/**
 * Common search result data DTO
 */
export class SearchResultDataDto<T> {
  @Expose()
  results!: T[];

  @Expose()
  query!: string;

  @Expose({ name: 'total_results' })
  totalResults!: number;

  @Expose({ name: 'execution_time_ms' })
  executionTimeMs!: number;

  @Expose()
  suggestions?: string[];
}

/**
 * Common file upload result DTO
 */
export class FileUploadDataDto {
  @Expose({ name: 'file_id' })
  fileId!: string;

  @Expose({ name: 'original_name' })
  originalName!: string;

  @Expose({ name: 'file_size' })
  fileSize!: number;

  @Expose({ name: 'mime_type' })
  mimeType!: string;

  @Expose()
  url!: string;

  @Expose()
  metadata?: Record<string, any>;
}

/**
 * Common validation error details DTO
 */
export class ValidationErrorDataDto {
  @Expose()
  field!: string;

  @Expose()
  message!: string;

  @Expose()
  rule!: string;

  @Expose()
  value?: any;
}

/**
 * Common bulk operation result DTO
 */
export class BulkOperationDataDto {
  @Expose({ name: 'total_processed' })
  totalProcessed!: number;

  @Expose({ name: 'successful_count' })
  successfulCount!: number;

  @Expose({ name: 'failed_count' })
  failedCount!: number;

  @Expose()
  errors?: Array<{
    index: number;
    error: string;
    details?: any;
  }>;

  @Expose({ name: 'processing_time_ms' })
  processingTimeMs?: number;
}
