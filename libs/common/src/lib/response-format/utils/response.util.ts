/* eslint-disable @typescript-eslint/no-explicit-any */
import { HttpStatus } from '@nestjs/common';
import { StandardApiResponse } from '../interfaces/response-format.interface';

/**
 * Utility class for creating standardized API responses.
 *
 * This class provides static methods for creating consistent API responses
 * that follow the StandardApiResponse format. It includes convenience methods
 * for common HTTP status codes and scenarios.
 *
 * Features:
 * - Type-safe response creation
 * - Automatic timestamp generation
 * - Default messages for common status codes
 * - Pagination support for list responses
 * - Consistent error response formatting
 *
 * @example
 * ```typescript
 * // Success responses
 * const user = { id: "123", name: "<PERSON>" };
 * const response = ResponseUtil.success(user, "User retrieved successfully");
 *
 * // Error responses
 * const errorResponse = ResponseUtil.notFound("User not found");
 *
 * // Paginated responses
 * const users = [{ id: "1" }, { id: "2" }];
 * const paginatedResponse = ResponseUtil.paginated(users, 1, 10, 25);
 * ```
 *
 * @see {@link StandardApiResponse} for the response format structure
 */
export class ResponseUtil {
  /**
   * Creates a success response with the provided data.
   *
   * This is the base method for creating successful responses. Other success
   * methods (created, accepted, etc.) are built on top of this method.
   *
   * @template T - The type of the response data
   * @param data - The data to include in the response
   * @param message - Custom success message (optional)
   * @param statusCode - HTTP status code (defaults to 200)
   * @returns Formatted success response
   *
   * @example
   * ```typescript
   * // Basic success response
   * ResponseUtil.success({ id: "123" });
   * // Returns: { success: true, status_code: 200, message: "Request successful", data: { id: "123" }, ... }
   *
   * // Custom message and status code
   * ResponseUtil.success(userData, "User updated successfully", 200);
   * ```
   */
  static success<T>(data?: T, message?: string, statusCode: number = HttpStatus.OK): StandardApiResponse<T> {
    return {
      success: true,
      statusCode: statusCode,
      message: message || this.getDefaultSuccessMessage(statusCode),
      data,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * Creates a "Created" response (HTTP 201).
   *
   * Use this method when a new resource has been successfully created.
   * Commonly used for POST endpoints that create new entities.
   *
   * @template T - The type of the created resource data
   * @param data - The created resource data
   * @param message - Custom success message (defaults to "Resource created successfully")
   * @returns Formatted 201 Created response
   *
   * @example
   * ```typescript
   * const newUser = { id: "123", name: "John Doe" };
   * return ResponseUtil.created(newUser, "User account created successfully");
   * ```
   */
  static created<T>(data?: T, message?: string): StandardApiResponse<T> {
    return this.success(data, message || 'Resource created successfully', HttpStatus.CREATED);
  }

  /**
   * Creates an "Accepted" response (HTTP 202).
   *
   * Use this method when a request has been accepted for processing
   * but the processing has not been completed (asynchronous operations).
   *
   * @template T - The type of the response data
   * @param data - Optional data about the accepted request
   * @param message - Custom message (defaults to "Request accepted")
   * @returns Formatted 202 Accepted response
   *
   * @example
   * ```typescript
   * const jobInfo = { jobId: "job-123", status: "queued" };
   * return ResponseUtil.accepted(jobInfo, "File processing started");
   * ```
   */
  static accepted<T>(data?: T, message?: string): StandardApiResponse<T> {
    return this.success(data, message || 'Request accepted', HttpStatus.ACCEPTED);
  }

  /**
   * Creates a "No Content" response (HTTP 204).
   *
   * Use this method for successful operations that don't return data,
   * such as DELETE operations or updates that don't return the updated resource.
   *
   * @param message - Custom message (defaults to "Request successful")
   * @returns Formatted 204 No Content response
   *
   * @example
   * ```typescript
   * // After successful deletion
   * return ResponseUtil.noContent("User deleted successfully");
   *
   * // After successful update without returning data
   * return ResponseUtil.noContent("Settings updated successfully");
   * ```
   */
  static noContent(message?: string): StandardApiResponse<null> {
    return this.success(null, message || 'Request successful', HttpStatus.NO_CONTENT);
  }

  /**
   * Create an error response
   */
  static error(message: string, statusCode: number = HttpStatus.INTERNAL_SERVER_ERROR, errorCode?: string, details?: any): StandardApiResponse<null> {
    return {
      success: false,
      statusCode: statusCode,
      message: this.getDefaultErrorMessage(statusCode),
      error: {
        code: errorCode,
        message,
        details,
        timestamp: new Date().toISOString(),
      },
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * Create a bad request response (400)
   */
  static badRequest(message?: string, details?: any): StandardApiResponse<null> {
    return this.error(message || 'Bad Request', HttpStatus.BAD_REQUEST, 'BAD_REQUEST', details);
  }

  /**
   * Create an unauthorized response (401)
   */
  static unauthorized(message?: string): StandardApiResponse<null> {
    return this.error(message || 'Unauthorized', HttpStatus.UNAUTHORIZED, 'UNAUTHORIZED');
  }

  /**
   * Create a forbidden response (403)
   */
  static forbidden(message?: string): StandardApiResponse<null> {
    return this.error(message || 'Forbidden', HttpStatus.FORBIDDEN, 'FORBIDDEN');
  }

  /**
   * Create a not found response (404)
   */
  static notFound(message?: string): StandardApiResponse<null> {
    return this.error(message || 'Resource not found', HttpStatus.NOT_FOUND, 'NOT_FOUND');
  }

  /**
   * Create a conflict response (409)
   */
  static conflict(message?: string, details?: any): StandardApiResponse<null> {
    return this.error(message || 'Conflict', HttpStatus.CONFLICT, 'CONFLICT', details);
  }

  /**
   * Create an unprocessable entity response (422)
   */
  static unprocessableEntity(message?: string, details?: any): StandardApiResponse<null> {
    return this.error(message || 'Unprocessable Entity', HttpStatus.UNPROCESSABLE_ENTITY, 'UNPROCESSABLE_ENTITY', details);
  }

  /**
   * Create an internal server error response (500)
   */
  static internalServerError(message?: string): StandardApiResponse<null> {
    return this.error(message || 'Internal Server Error', HttpStatus.INTERNAL_SERVER_ERROR, 'INTERNAL_SERVER_ERROR');
  }

  /**
   * Create a paginated response
   */
  static paginated<T>(data: T[], page: number, limit: number, total: number, message?: string): StandardApiResponse<T[]> {
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrevious = page > 1;

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: message || 'Request successful',
      data,
      meta: {
        timestamp: new Date().toISOString(),
        pagination: {
          page,
          limit,
          total,
          totalPages: totalPages,
          has_next: hasNext,
          has_previous: hasPrevious,
        },
      },
    };
  }

  /**
   * Get default success message for status code
   */
  private static getDefaultSuccessMessage(statusCode: number): string {
    const messages: { [key: number]: string } = {
      200: 'Request successful',
      201: 'Resource created successfully',
      202: 'Request accepted',
      204: 'Request successful',
    };
    return messages[statusCode] || 'Request successful';
  }

  /**
   * Get default error message for status code
   */
  private static getDefaultErrorMessage(statusCode: number): string {
    const messages: { [key: number]: string } = {
      400: 'Bad Request',
      401: 'Unauthorized',
      403: 'Forbidden',
      404: 'Not Found',
      409: 'Conflict',
      422: 'Unprocessable Entity',
      500: 'Internal Server Error',
    };
    return messages[statusCode] || 'An error occurred';
  }
}
