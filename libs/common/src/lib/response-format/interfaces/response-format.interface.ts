/**
 * Standard API response format for all endpoints.
 *
 * This interface defines the consistent structure that all API responses should follow,
 * ensuring uniformity across different services and endpoints. It includes both success
 * and error scenarios with comprehensive metadata.
 *
 * @template T - The type of the response data
 *
 * @example
 * ```typescript
 * // Success response
 * const response: StandardApiResponse<User> = {
 *   success: true,
 *   status_code: 200,
 *   message: "User retrieved successfully",
 *   data: { id: "123", name: "<PERSON>" },
 *   meta: { timestamp: "2025-07-05T01:00:00.000Z" }
 * };
 *
 * // Error response
 * const errorResponse: StandardApiResponse<null> = {
 *   success: false,
 *   status_code: 404,
 *   message: "User not found",
 *   error: {
 *     code: "USER_NOT_FOUND",
 *     message: "No user found with the provided ID",
 *     timestamp: "2025-07-05T01:00:00.000Z",
 *     path: "/api/users/123"
 *   }
 * };
 * ```
 */
export interface StandardApiResponse<T = unknown> {
  /**
   * Indicates if the request was successful.
   *
   * - `true` for successful requests (2xx status codes)
   * - `false` for failed requests (4xx, 5xx status codes)
   */
  success: boolean;

  /**
   * HTTP status code of the response.
   *
   * Common codes:
   * - 200: OK - Request successful
   * - 201: Created - Resource created successfully
   * - 400: Bad Request - Invalid request data
   * - 401: Unauthorized - Authentication required
   * - 404: Not Found - Resource not found
   * - 500: Internal Server Error - Server error
   */
  statusCode: number;

  /**
   * Human-readable message describing the result.
   *
   * This should be a clear, concise description of what happened.
   * For errors, this should explain what went wrong in user-friendly terms.
   */
  message: string;

  /**
   * The actual response data.
   *
   * This field contains the main payload of the response. It's optional
   * and may be omitted for operations that don't return data (e.g., DELETE).
   *
   * @example
   * ```typescript
   * // Single object
   * data: { id: "123", name: "John Doe" }
   *
   * // Array of objects
   * data: [{ id: "1", name: "User 1" }, { id: "2", name: "User 2" }]
   *
   * // Primitive value
   * data: "Operation completed"
   * ```
   */
  data?: T;

  /**
   * Error details (only present when success is false).
   *
   * This object provides detailed information about what went wrong,
   * including error codes for programmatic handling and additional context.
   */
  error?: {
    /**
     * Error code for programmatic handling.
     *
     * A machine-readable identifier that clients can use to handle
     * specific error scenarios programmatically.
     *
     * @example "USER_NOT_FOUND", "VALIDATION_ERROR", "INSUFFICIENT_PERMISSIONS"
     */
    code?: string;

    /**
     * Detailed error message.
     *
     * A more detailed explanation of the error, potentially including
     * technical details for debugging purposes.
     */
    message: string;

    /**
     * Additional error details (validation errors, etc.).
     *
     * This can contain structured information about the error,
     * such as field validation errors, stack traces (in development),
     * or other contextual information.
     *
     * @example
     * ```typescript
     * // Validation errors
     * details: {
     *   fields: {
     *     email: ["Email is required", "Email format is invalid"],
     *     age: ["Age must be a positive number"]
     *   }
     * }
     * ```
     */
    details?: unknown;

    /**
     * Timestamp when the error occurred.
     *
     * ISO 8601 formatted timestamp indicating when the error was generated.
     * Useful for debugging and correlation with server logs.
     */
    timestamp?: string;

    /**
     * Request path where the error occurred.
     *
     * The API endpoint path that generated the error, helpful for
     * debugging and error tracking.
     *
     * @example "/api/users/123", "/api/onboarding/start"
     */
    path?: string;
  };

  /**
   * Metadata about the response.
   *
   * This object contains additional information about the response that's not
   * part of the main data payload, such as timestamps, request tracking,
   * and pagination details.
   */
  meta?: {
    /**
     * Timestamp when the response was generated.
     *
     * ISO 8601 formatted timestamp indicating when the server generated
     * this response. Useful for caching, debugging, and audit trails.
     *
     * @example "2025-07-05T01:06:53.083Z"
     */
    timestamp: string;

    /**
     * Request ID for tracing and correlation.
     *
     * A unique identifier for this specific request, useful for:
     * - Correlating logs across services
     * - Debugging issues
     * - Request tracing in distributed systems
     *
     * @example "35c40696-e771-4812-b1cf-2cdb847ffe50"
     */
    request_id?: string;

    /**
     * API version that processed this request.
     *
     * Indicates which version of the API was used to process the request.
     * Useful for API versioning and compatibility tracking.
     *
     * @example "1.0", "2.1", "v1.2.3"
     */
    version?: string;

    /**
     * Pagination information (for list endpoints).
     *
     * When the response contains a paginated list of items, this object
     * provides information about the current page and navigation options.
     */
    pagination?: {
      /**
       * Current page number (1-based).
       *
       * @example 1, 2, 3
       */
      page: number;

      /**
       * Number of items per page.
       *
       * @example 10, 25, 50
       */
      limit: number;

      /**
       * Total number of items across all pages.
       *
       * @example 150
       */
      total: number;

      /**
       * Total number of pages available.
       *
       * Calculated as Math.ceil(total / limit)
       *
       * @example 6 (for 150 total items with 25 per page)
       */
      totalPages: number;

      /**
       * Whether there is a next page available.
       *
       * True if current page < totalPages
       */
      has_next: boolean;

      /**
       * Whether there is a previous page available.
       *
       * True if current page > 1
       */
      has_previous: boolean;
    };
  };
}

/**
 * Configuration options for the response formatting system.
 *
 * This interface defines how the response formatting interceptor and exception filter
 * should behave. It allows fine-grained control over what information is included
 * in responses and how errors are handled.
 *
 * @example
 * ```typescript
 * // Basic configuration
 * const config: ResponseFormatConfig = {
 *   enabled: true,
 *   includeMeta: true,
 *   includeRequestId: true
 * };
 *
 * // Production configuration
 * const prodConfig: ResponseFormatConfig = {
 *   enabled: true,
 *   includeMeta: true,
 *   includeRequestId: true,
 *   includeVersion: true,
 *   version: "2.1",
 *   includeErrorDetailsInProduction: false,
 *   successMessages: {
 *     200: "Request completed successfully",
 *     201: "Resource created successfully"
 *   }
 * };
 * ```
 */
export interface ResponseFormatConfig {
  /**
   * Whether to enable standardized response formatting.
   *
   * When disabled, responses will pass through without transformation.
   * Useful for debugging or gradual rollout of the formatting system.
   *
   * @default true
   */
  enabled?: boolean;

  /**
   * Whether to include metadata in responses.
   *
   * Controls whether the `meta` object is added to responses.
   * Disabling this can reduce response size but removes useful debugging information.
   *
   * @default true
   */
  includeMeta?: boolean;

  /**
   * Whether to include request ID in metadata.
   *
   * Request IDs are crucial for tracing requests across distributed systems.
   * Only disable if you have an alternative request tracking mechanism.
   *
   * @default true
   */
  includeRequestId?: boolean;

  /**
   * Whether to include API version in metadata.
   *
   * Useful for API versioning strategies and client compatibility tracking.
   * Enable this if you need to track which API version processed each request.
   *
   * @default false
   */
  includeVersion?: boolean;

  /**
   * API version to include in responses.
   *
   * The version string that will be included in the metadata when
   * `includeVersion` is enabled. Should follow semantic versioning.
   *
   * @default "1.0"
   * @example "1.0", "2.1", "v1.2.3"
   */
  version?: string;

  /**
   * Whether to include timestamp in metadata.
   *
   * Timestamps are useful for caching, debugging, and audit trails.
   * Only disable if response size is a critical concern.
   *
   * @default true
   */
  includeTimestamp?: boolean;

  /**
   * Custom success messages for different HTTP status codes.
   *
   * Allows overriding the default success messages with custom ones
   * that better fit your application's tone and terminology.
   *
   * @example
   * ```typescript
   * successMessages: {
   *   200: "Data retrieved successfully",
   *   201: "New resource created",
   *   202: "Request accepted for processing",
   *   204: "Operation completed successfully"
   * }
   * ```
   */
  successMessages?: {
    [statusCode: number]: string;
  };

  /**
   * Whether to transform error responses to standard format.
   *
   * When enabled, all exceptions will be caught and formatted according
   * to the StandardApiResponse structure. Disable only if you need
   * custom error handling for specific endpoints.
   *
   * @default true
   */
  transformErrors?: boolean;

  /**
   * Whether to include error details in production.
   *
   * In development, detailed error information (like stack traces) can be
   * helpful for debugging. In production, this information should typically
   * be hidden for security reasons.
   *
   * @default false
   */
  includeErrorDetailsInProduction?: boolean;
}
