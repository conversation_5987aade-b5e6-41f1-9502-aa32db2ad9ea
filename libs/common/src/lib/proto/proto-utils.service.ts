/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable } from '@nestjs/common';
import { BaseRequestMetadata, BaseResponseMetadata, ProtoError } from './types';

/**
 * Utility service for working with proto types and common operations
 */
@Injectable()
export class ProtoUtilsService {
  /**
   * Create request metadata with default values
   */
  createRequestMetadata(overrides: Partial<BaseRequestMetadata> = {}): BaseRequestMetadata {
    return {
      requestId: this.generateRequestId(),
      timestamp: new Date(),
      ...overrides,
    };
  }

  /**
   * Create response metadata with default values
   */
  createResponseMetadata(requestId?: string, startTime?: number, overrides: Partial<BaseResponseMetadata> = {}): BaseResponseMetadata {
    const now = Date.now();
    return {
      requestId: requestId || this.generateRequestId(),
      timestamp: new Date(),
      processingTime: startTime ? now - startTime : undefined,
      ...overrides,
    };
  }

  /**
   * Create a standardized error object
   */
  createError(code: string, message: string, details?: { [key: string]: string }, traceId?: string): ProtoError {
    return {
      code,
      message,
      details: details || {},
      traceId: traceId || this.generateTraceId(),
    };
  }

  /**
   * Generate a unique request ID
   */
  generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate a unique trace ID
   */
  generateTraceId(): string {
    return `trace_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Convert a Date object to proto timestamp (seconds since epoch)
   */
  dateToProtoTimestamp(date: Date): number {
    return Math.floor(date.getTime() / 1000);
  }

  /**
   * Convert proto timestamp (seconds since epoch) to Date object
   */
  protoTimestampToDate(timestamp: number): Date {
    return new Date(timestamp * 1000);
  }

  /**
   * Validate that required fields are present in a proto message
   */
  validateRequiredFields<T extends Record<string, any>>(obj: T, requiredFields: (keyof T)[], errorPrefix = 'Missing required field'): void {
    for (const field of requiredFields) {
      if (obj[field] === undefined || obj[field] === null) {
        throw new Error(`${errorPrefix}: ${String(field)}`);
      }
    }
  }

  /**
   * Safely extract string value from proto field
   */
  safeString(value: string | undefined | null, defaultValue = ''): string {
    return value ?? defaultValue;
  }

  /**
   * Safely extract number value from proto field
   */
  safeNumber(value: number | string | undefined | null, defaultValue = 0): number {
    if (typeof value === 'number') return value;
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? defaultValue : parsed;
    }
    return defaultValue;
  }

  /**
   * Safely extract boolean value from proto field
   */
  safeBoolean(value: boolean | undefined | null, defaultValue = false): boolean {
    return value ?? defaultValue;
  }

  /**
   * Convert proto enum value to string
   */
  enumToString<T extends Record<string, number>>(enumObj: T, value: number | undefined | null, defaultValue = 'UNKNOWN'): string {
    if (value === undefined || value === null) return defaultValue;

    const enumKey = Object.keys(enumObj).find((key) => enumObj[key] === value);
    return enumKey || defaultValue;
  }

  /**
   * Convert string to proto enum value
   */
  stringToEnum<T extends Record<string, number>>(enumObj: T, value: string | undefined | null, defaultValue = 0): number {
    if (!value) return defaultValue;

    const enumValue = enumObj[value];
    return enumValue !== undefined ? enumValue : defaultValue;
  }

  /**
   * Create a deep copy of a proto message (useful for mutations)
   */
  deepCopy<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
    if (Array.isArray(obj)) return obj.map((item) => this.deepCopy(item)) as unknown as T;

    const copy = {} as T;
    for (const key in obj) {
      // eslint-disable-next-line no-prototype-builtins
      if (obj.hasOwnProperty(key)) {
        copy[key] = this.deepCopy(obj[key]);
      }
    }
    return copy;
  }

  /**
   * Merge two proto messages, with the second taking precedence
   */
  mergeMessages<T extends Record<string, any>>(base: T, override: Partial<T>): T {
    return {
      ...this.deepCopy(base),
      ...override,
    };
  }

  /**
   * Check if a proto message is empty (all fields are undefined/null/default)
   */
  isEmpty(obj: Record<string, any>): boolean {
    if (!obj) return true;

    return Object.values(obj).every((value) => {
      if (value === undefined || value === null) return true;
      if (typeof value === 'string' && value === '') return true;
      if (typeof value === 'number' && value === 0) return true;
      if (typeof value === 'boolean' && value === false) return true;
      if (Array.isArray(value) && value.length === 0) return true;
      if (typeof value === 'object' && this.isEmpty(value)) return true;
      return false;
    });
  }

  /**
   * Convert proto message to a plain object (removing undefined fields)
   */
  toPlainObject<T extends Record<string, any>>(obj: T): Partial<T> {
    const result: Partial<T> = {};

    for (const [key, value] of Object.entries(obj)) {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
          const nested = this.toPlainObject(value);
          if (!this.isEmpty(nested)) {
            result[key as keyof T] = nested as T[keyof T];
          }
        } else {
          result[key as keyof T] = value;
        }
      }
    }

    return result;
  }
}
