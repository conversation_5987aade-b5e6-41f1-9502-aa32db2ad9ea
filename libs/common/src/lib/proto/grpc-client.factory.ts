import { Injectable } from '@nestjs/common';
import { GrpcOptions, Transport } from '@nestjs/microservices';
import { ProtoConfigService } from './proto-config.service';

export interface GrpcClientConfig {
  name: string;
  package: string;
  service: string;
  protoFile: string;
  url?: string;
}

@Injectable()
export class GrpcClientFactory {
  constructor(private readonly protoConfigService: ProtoConfigService) {}

  /**
   * Create a gRPC client configuration for NestJS ClientsModule
   */
  createClientConfig(config: GrpcClientConfig) {
    return {
      name: config.name,
      useFactory: () => ({
        transport: Transport.GRPC,
        options: {
          package: config.package,
          url: config.url || this.getDefaultUrl(config.service),
          protoPath: this.protoConfigService.getProtoPath(config.service, config.protoFile),
          loader: this.protoConfigService.getLoaderOptions(),
          channelOptions: this.protoConfigService.getChannelOptions(),
        },
      }),
    };
  }

  /**
   * Create gRPC client options (without the name and useFactory wrapper)
   */
  createClientOptions(config: GrpcClientConfig): GrpcOptions {
    return {
      transport: Transport.GRPC,
      options: {
        package: config.package,
        url: config.url || this.getDefaultUrl(config.service),
        protoPath: this.protoConfigService.getProtoPath(config.service, config.protoFile),
        loader: this.protoConfigService.getLoaderOptions(),
        channelOptions: this.protoConfigService.getChannelOptions(),
      },
    };
  }

  /**
   * Create multiple gRPC client configurations
   */
  createMultipleClientConfigs(configs: GrpcClientConfig[]) {
    return configs.map((config) => this.createClientConfig(config));
  }

  /**
   * Get default URL for a service based on environment variables
   */
  private getDefaultUrl(serviceName: string): string {
    const envKey = `${serviceName.toUpperCase().replace('-', '_')}_GRPC_URL`;
    return process.env[envKey] || `localhost:${this.getDefaultPort(serviceName)}`;
  }

  /**
   * Get default port for common services
   */
  private getDefaultPort(serviceName: string): number {
    const defaultPorts: Record<string, number> = {
      auth: 3012,
      user: 3013,
      tenant: 3014,
      notification: 3015,
      audit: 3016,
    };

    return defaultPorts[serviceName] || 3000;
  }

  /**
   * Create auth service client configuration
   */
  createAuthClientConfig(name = 'AUTH_GRPC_CLIENT') {
    return this.createClientConfig({
      name,
      package: 'auth',
      service: 'auth',
      protoFile: 'auth.proto',
      url: process.env['AUTH_SERVICE_GRPC_URL'],
    });
  }

  /**
   * Create health check client configuration
   */
  createHealthClientConfig(name = 'HEALTH_GRPC_CLIENT') {
    return this.createClientConfig({
      name,
      package: 'health',
      service: 'common',
      protoFile: 'health.proto',
    });
  }
}
