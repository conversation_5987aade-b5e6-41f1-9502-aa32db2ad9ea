import { Injectable } from '@nestjs/common';
import { join } from 'path';

@Injectable()
export class ProtoConfigService {
  private readonly protoRootPath: string;

  constructor() {
    // Determine proto root path relative to workspace root
    this.protoRootPath = join(process.cwd(), 'libs', 'proto', 'src', 'proto');
  }

  /**
   * Get the path to a specific proto file
   */
  getProtoPath(service: string, filename: string): string {
    return join(this.protoRootPath, service, filename);
  }

  /**
   * Get the proto root directory for includeDirs
   */
  getProtoRootPath(): string {
    return this.protoRootPath;
  }

  /**
   * Get standard gRPC loader options
   */
  getLoaderOptions() {
    return {
      keepCase: true,
      longs: String,
      enums: String,
      defaults: true,
      oneofs: true,
      includeDirs: [this.protoRootPath],
    };
  }

  /**
   * Get standard gRPC channel options for resilience
   */
  getChannelOptions() {
    return {
      'grpc.keepalive_time_ms': 10000,
      'grpc.keepalive_timeout_ms': 5000,
      'grpc.keepalive_permit_without_calls': 1,
      'grpc.max_receive_message_length': 4 * 1024 * 1024, // 4MB
      'grpc.max_send_message_length': 4 * 1024 * 1024, // 4MB
    };
  }

  /**
   * Get gRPC client configuration for a specific service
   */
  getGrpcClientConfig(
    serviceName: string,
    options: {
      package: string;
      protoFile: string;
      url?: string;
    },
  ) {
    return {
      transport: 'GRPC' as const,
      options: {
        package: options.package,
        protoPath: this.getProtoPath(serviceName, options.protoFile),
        url: options.url,
        loader: this.getLoaderOptions(),
        channelOptions: this.getChannelOptions(),
      },
    };
  }
}
