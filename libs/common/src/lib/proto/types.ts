import { Metadata } from '@grpc/grpc-js';
import { Observable } from 'rxjs';

/**
 * Base request metadata interface
 */
export interface BaseRequestMetadata {
  requestId?: string;
  sourceIp?: string;
  userAgent?: string;
  timestamp?: Date;
}

/**
 * Base response metadata interface
 */
export interface BaseResponseMetadata {
  requestId?: string;
  timestamp?: Date;
  processingTime?: number;
}

/**
 * Generic error interface for proto responses
 */
export interface ProtoError {
  code?: string;
  message?: string;
  details?: { [key: string]: string };
  traceId?: string;
}

/**
 * Base gRPC service interface
 */
export interface GrpcService {
  [methodName: string]: (data: any, metadata?: Metadata) => Observable<any>;
}

/**
 * gRPC client configuration
 */
export interface GrpcClientConfig {
  name: string;
  package: string;
  service: string;
  protoFile: string;
  url?: string;
}

/**
 * Service configuration for proto generation
 */
export interface ServiceConfig {
  name: string;
  package: string;
  protoFile: string;
  outputPath?: string;
}

/**
 * Proto generation options
 */
export interface ProtoGenerationOptions {
  nestJs?: boolean;
  outputServices?: string;
  env?: 'node' | 'browser' | 'both';
  esModuleInterop?: boolean;
  stringEnums?: boolean;
  useOptionals?: 'none' | 'messages' | 'all';
  exportCommonSymbols?: boolean;
  outputIndex?: boolean;
  useDate?: boolean;
  forceLong?: 'number' | 'long';
  useExactTypes?: boolean;
}

/**
 * Default proto generation options for NestJS
 */
export const DEFAULT_PROTO_OPTIONS: ProtoGenerationOptions = {
  nestJs: true,
  outputServices: 'grpc-js',
  env: 'node',
  esModuleInterop: true,
  stringEnums: true,
  useOptionals: 'messages',
  exportCommonSymbols: false,
  outputIndex: true,
  useDate: true,
  forceLong: 'number',
  useExactTypes: false,
};

/**
 * Service registry for managing proto services
 */
export interface ServiceRegistry {
  [serviceName: string]: ServiceConfig;
}

/**
 * Default service configurations
 */
export const DEFAULT_SERVICES: ServiceRegistry = {
  auth: {
    name: 'auth',
    package: 'auth',
    protoFile: 'auth.proto',
  },
  health: {
    name: 'health',
    package: 'common',
    protoFile: 'health.proto',
  },
};
