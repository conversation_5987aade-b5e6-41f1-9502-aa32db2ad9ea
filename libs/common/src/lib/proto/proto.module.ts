import { Global, Module } from '@nestjs/common';
import { GrpcClientFactory } from './grpc-client.factory';
import { ProtoConfigService } from './proto-config.service';
import { ProtoUtilsService } from './proto-utils.service';

@Global()
@Module({
  imports: [],
  controllers: [],
  providers: [ProtoConfigService, GrpcClientFactory, ProtoUtilsService],
  exports: [ProtoConfigService, GrpcClientFactory, ProtoUtilsService],
})
export class ProtoModule {}
