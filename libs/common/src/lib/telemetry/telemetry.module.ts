import { DEFAULT_TELEMETRY_CONFIG, TelemetryConfig } from './interfaces/telemetry.interface';
/* eslint-disable @typescript-eslint/no-explicit-any */
import { DynamicModule, Global, Module } from '@nestjs/common';
import { GrpcTelemetryInterceptor, TelemetryInterceptor } from './interceptors/telemetry.interceptor';

import { APP_INTERCEPTOR } from '@nestjs/core';
import { ConfigService } from '../config/config.service';
import { TelemetryController } from './controllers/telemetry.controller';
import { TelemetryService } from './services/telemetry.service';

/**
 * Telemetry module for observability
 */
@Global()
@Module({})
export class TelemetryModule {
  /**
   * Configure telemetry module with custom options
   */
  static forRoot(config?: Partial<TelemetryConfig>): DynamicModule {
    const telemetryConfig = { ...DEFAULT_TELEMETRY_CONFIG, ...config };

    const providers: any[] = [
      {
        provide: 'TELEMETRY_CONFIG',
        useValue: telemetryConfig,
      },
      {
        provide: TelemetryService,
        useFactory: (configService: ConfigService) => {
          const service = new TelemetryService();

          // Override config with environment variables if available
          const environment = configService.get('NODE_ENV') || telemetryConfig.environment;
          const isDevelopment = environment === 'development';

          // Default to disabled in development unless explicitly enabled
          const defaultEnabled = isDevelopment ? 'false' : 'true';
          const defaultTracingEnabled = isDevelopment ? 'false' : 'true';

          const finalConfig: TelemetryConfig = {
            ...telemetryConfig,
            enabled: (configService.get('TELEMETRY_ENABLED') || defaultEnabled) === 'true',
            serviceName: configService.get('SERVICE_NAME') || telemetryConfig.serviceName,
            serviceVersion: configService.get('SERVICE_VERSION') || telemetryConfig.serviceVersion,
            environment,
            tracing: {
              ...telemetryConfig.tracing,
              enabled: (configService.get('TRACING_ENABLED') || defaultTracingEnabled) === 'true',
              jaegerEndpoint: configService.get('JAEGER_ENDPOINT') || telemetryConfig.tracing?.jaegerEndpoint,
              sampleRate: parseFloat(configService.get('TRACING_SAMPLE_RATE') || (isDevelopment ? '0.1' : '1.0')),
            },
            metrics: {
              ...telemetryConfig.metrics,
              enabled: (configService.get('METRICS_ENABLED') || defaultEnabled) === 'true',
              prometheusEndpoint: configService.get('PROMETHEUS_ENDPOINT') || telemetryConfig.metrics?.prometheusEndpoint,
              collectionInterval: isDevelopment ? 10000 : telemetryConfig.metrics?.collectionInterval || 5000,
            },
          };

          service.initialize(finalConfig);
          return service;
        },
        inject: [ConfigService],
      },
    ];

    // Only add interceptors if telemetry is enabled
    if (telemetryConfig.enabled) {
      providers.push(
        {
          provide: APP_INTERCEPTOR,
          useClass: TelemetryInterceptor,
        },
        {
          provide: APP_INTERCEPTOR,
          useClass: GrpcTelemetryInterceptor,
        },
      );
    }

    return {
      module: TelemetryModule,
      controllers: [TelemetryController],
      providers,
      exports: [TelemetryService],
    };
  }

  /**
   * Configure telemetry module asynchronously
   */
  static forRootAsync(options: { useFactory: (...args: any[]) => Promise<TelemetryConfig> | TelemetryConfig; inject?: any[] }): DynamicModule {
    return {
      module: TelemetryModule,
      controllers: [TelemetryController],
      providers: [
        {
          provide: 'TELEMETRY_CONFIG',
          useFactory: options.useFactory,
          inject: options.inject || [],
        },
        {
          provide: TelemetryService,
          useFactory: (config: TelemetryConfig) => {
            const service = new TelemetryService();
            service.initialize(config);
            return service;
          },
          inject: ['TELEMETRY_CONFIG'],
        },
        {
          provide: APP_INTERCEPTOR,
          useClass: TelemetryInterceptor,
        },
        {
          provide: APP_INTERCEPTOR,
          useClass: GrpcTelemetryInterceptor,
        },
      ],
      exports: [TelemetryService],
    };
  }

  /**
   * Configure telemetry module for feature modules (without global interceptors)
   */
  static forFeature(config?: Partial<TelemetryConfig>): DynamicModule {
    const telemetryConfig = { ...DEFAULT_TELEMETRY_CONFIG, ...config };

    return {
      module: TelemetryModule,
      providers: [
        {
          provide: 'TELEMETRY_CONFIG',
          useValue: telemetryConfig,
        },
        {
          provide: TelemetryService,
          useFactory: (configService: ConfigService) => {
            const service = new TelemetryService();

            const finalConfig: TelemetryConfig = {
              ...telemetryConfig,
              enabled: (configService.get('TELEMETRY_ENABLED') || 'true') === 'true',
              serviceName: configService.get('SERVICE_NAME') || telemetryConfig.serviceName,
            };

            service.initialize(finalConfig);
            return service;
          },
          inject: [ConfigService],
        },
      ],
      exports: [TelemetryService],
    };
  }
}
