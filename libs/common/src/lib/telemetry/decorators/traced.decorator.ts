/* eslint-disable @typescript-eslint/no-explicit-any */
import { TelemetryService } from '../services/telemetry.service';

/**
 * Decorator to automatically trace method execution
 */
export function Traced(spanName?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const className = target.constructor.name;
    const methodName = propertyKey;
    const defaultSpanName = spanName || `${className}.${methodName}`;

    descriptor.value = async function (...args: any[]) {
      // Get telemetry service instance
      const telemetryService = getTelemetryService(this);

      if (!telemetryService) {
        // If no telemetry service available, just execute the method
        return originalMethod.apply(this, args);
      }

      const spanId = telemetryService.startSpan({
        name: defaultSpanName,
        kind: 'internal',
        attributes: {
          'method.class': className,
          'method.name': methodName,
          'method.args.count': args.length.toString(),
        },
      });

      try {
        const startTime = Date.now();
        const result = await originalMethod.apply(this, args);
        const duration = Date.now() - startTime;

        telemetryService.endSpan(spanId, {
          'method.success': 'true',
          'method.duration_ms': duration.toString(),
        });

        // Record success metric
        telemetryService.incrementCounter('method_calls_total', 1, {
          class: className,
          method: methodName,
          status: 'success',
        });

        telemetryService.recordHistogram('method_duration_ms', duration, {
          class: className,
          method: methodName,
        });

        return result;
      } catch (error) {
        const duration = Date.now() - Date.now();

        telemetryService.endSpan(spanId, {
          'method.success': 'false',
          'method.error': error instanceof Error ? error.message : 'Unknown error',
          'method.duration_ms': duration.toString(),
        });

        // Record error metric
        telemetryService.incrementCounter('method_calls_total', 1, {
          class: className,
          method: methodName,
          status: 'error',
        });

        telemetryService.recordEvent({
          name: 'method_error',
          timestamp: new Date(),
          severity: 'error',
          attributes: {
            class: className,
            method: methodName,
            error: error instanceof Error ? error.message : 'Unknown error',
          },
        });

        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Get telemetry service instance from the target object
 */
function getTelemetryService(target: any): TelemetryService | null {
  // Try to get from dependency injection
  if (target.telemetryService && target.telemetryService instanceof TelemetryService) {
    return target.telemetryService;
  }

  // Try to get from constructor injection
  if (target.constructor && target.constructor.telemetryService) {
    return target.constructor.telemetryService;
  }

  // Try to get from global registry (fallback)
  if (global && (global as any).telemetryService) {
    return (global as any).telemetryService;
  }

  return null;
}

/**
 * Decorator to trace HTTP requests
 */
export function TracedHttp(spanName?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const className = target.constructor.name;
    const methodName = propertyKey;
    const defaultSpanName = spanName || `HTTP ${className}.${methodName}`;

    descriptor.value = async function (...args: any[]) {
      const telemetryService = getTelemetryService(this);

      if (!telemetryService) {
        return originalMethod.apply(this, args);
      }

      // Extract request information if available
      const req = args.find((arg) => arg && arg.method && arg.url);
      const spanAttributes: Record<string, string> = {
        'http.method': req?.method || 'UNKNOWN',
        'http.url': req?.url || 'UNKNOWN',
        'http.route': req?.route?.path || 'UNKNOWN',
        'service.class': className,
        'service.method': methodName,
      };

      const spanId = telemetryService.startSpan({
        name: defaultSpanName,
        kind: 'server',
        attributes: spanAttributes,
      });

      try {
        const startTime = Date.now();
        const result = await originalMethod.apply(this, args);
        const duration = Date.now() - startTime;

        // Extract response information if available
        const statusCode = result?.statusCode || 200;

        telemetryService.endSpan(spanId, {
          'http.status_code': statusCode.toString(),
          'http.response_time_ms': duration.toString(),
        });

        // Record HTTP metrics
        telemetryService.incrementCounter('http_requests_total', 1, {
          method: req?.method || 'UNKNOWN',
          status_code: statusCode.toString(),
          route: req?.route?.path || 'UNKNOWN',
        });

        telemetryService.recordHistogram('http_request_duration_ms', duration, {
          method: req?.method || 'UNKNOWN',
          route: req?.route?.path || 'UNKNOWN',
        });

        return result;
      } catch (error) {
        const duration = Date.now() - Date.now();
        const statusCode = error instanceof Error && (error as any).status ? (error as any).status : 500;

        telemetryService.endSpan(spanId, {
          'http.status_code': statusCode.toString(),
          'http.error': error instanceof Error ? error.message : 'Unknown error',
          'http.response_time_ms': duration.toString(),
        });

        // Record HTTP error metrics
        telemetryService.incrementCounter('http_requests_total', 1, {
          method: req?.method || 'UNKNOWN',
          status_code: statusCode.toString(),
          route: req?.route?.path || 'UNKNOWN',
        });

        throw error;
      }
    };

    return descriptor;
  };
}
