/* eslint-disable @typescript-eslint/no-explicit-any */
import { <PERSON><PERSON><PERSON>ler, ExecutionContext, Injectable, Logger, NestInterceptor } from '@nestjs/common';
import { catchError, tap } from 'rxjs/operators';

import { Observable } from 'rxjs';
import { TelemetryService } from '../services/telemetry.service';

/**
 * Interceptor to automatically add telemetry to all HTTP requests
 */
@Injectable()
export class TelemetryInterceptor implements NestInterceptor {
  private readonly logger = new Logger(TelemetryInterceptor.name);

  constructor(private readonly telemetryService: TelemetryService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const startTime = Date.now();

    // Generate request ID if not present
    const requestId = request.headers['x-request-id'] || this.generateRequestId();
    request.requestId = requestId;
    response.setHeader('x-request-id', requestId);

    // Extract request information
    const method = request.method;
    const url = request.url;
    const userAgent = request.headers['user-agent'] || 'unknown';
    // Use cleaned tenant code from request context (set by tenant interceptor) or fallback to raw header
    const tenantCode = request.tenant?.code || request.headers['x-tenant-code'] || 'unknown';

    // Start telemetry span
    const spanId = this.telemetryService.startSpan({
      name: `HTTP ${method} ${url}`,
      kind: 'server',
      attributes: {
        'http.method': method,
        'http.url': url,
        'http.user_agent': userAgent,
        'http.request_id': requestId,
        'tenant.code': tenantCode,
        'service.name': 'user-service',
      },
    });

    return next.handle().pipe(
      tap((data) => {
        const duration = Date.now() - startTime;
        const statusCode = response.statusCode || 200;

        // End span with success
        this.telemetryService.endSpan(spanId, {
          'http.status_code': statusCode.toString(),
          'http.response_time_ms': duration.toString(),
          'http.response_size': this.getResponseSize(data),
        });

        // Record metrics
        this.telemetryService.incrementCounter('http_requests_total', 1, {
          method,
          status_code: statusCode.toString(),
          tenant_code: tenantCode,
        });

        this.telemetryService.recordHistogram('http_request_duration_ms', duration, {
          method,
          tenant_code: tenantCode,
        });

        // Log request completion
        // this.logger.log(`${method} ${url} - ${statusCode} - ${duration}ms`, {
        //   requestId,
        //   method,
        //   url,
        //   statusCode,
        //   duration,
        //   tenantCode,
        // });
      }),
      catchError((error) => {
        const duration = Date.now() - startTime;
        const statusCode = error.status || 500;

        // End span with error
        this.telemetryService.endSpan(spanId, {
          'http.status_code': statusCode.toString(),
          'http.error': error.message || 'Unknown error',
          'http.response_time_ms': duration.toString(),
        });

        // Record error metrics
        this.telemetryService.incrementCounter('http_requests_total', 1, {
          method,
          status_code: statusCode.toString(),
          tenant_code: tenantCode,
        });

        this.telemetryService.incrementCounter('http_errors_total', 1, {
          method,
          error_type: error.constructor.name,
          tenant_code: tenantCode,
        });

        // Record telemetry event
        this.telemetryService.recordEvent({
          name: 'http_request_error',
          timestamp: new Date(),
          severity: 'error',
          attributes: {
            requestId,
            method,
            url,
            statusCode,
            error: error.message,
            tenantCode,
          },
        });

        // Log error
        // this.logger.error(`${method} ${url} - ${statusCode} - ${duration}ms - ${error.message}`, {
        //   requestId,
        //   method,
        //   url,
        //   statusCode,
        //   duration,
        //   error: error.message,
        //   stack: error.stack,
        //   tenantCode,
        // });

        throw error;
      }),
    );
  }

  /**
   * Generate a unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  /**
   * Get response size estimate
   */
  private getResponseSize(data: any): string {
    if (!data) return '0';

    try {
      return JSON.stringify(data).length.toString();
    } catch {
      return 'unknown';
    }
  }
}

/**
 * Interceptor for gRPC requests
 */
@Injectable()
export class GrpcTelemetryInterceptor implements NestInterceptor {
  private readonly logger = new Logger(GrpcTelemetryInterceptor.name);

  constructor(private readonly telemetryService: TelemetryService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const rpcContext = context.switchToRpc();
    const data = rpcContext.getData();
    const startTime = Date.now();

    // Extract gRPC method information
    const handler = context.getHandler();
    const className = context.getClass().name;
    const methodName = handler.name;

    // Start telemetry span
    const spanId = this.telemetryService.startSpan({
      name: `gRPC ${className}.${methodName}`,
      kind: 'server',
      attributes: {
        'rpc.system': 'grpc',
        'rpc.service': className,
        'rpc.method': methodName,
        'service.name': 'user-service',
      },
    });

    return next.handle().pipe(
      tap((result) => {
        const duration = Date.now() - startTime;

        // End span with success
        this.telemetryService.endSpan(spanId, {
          'rpc.grpc.status_code': '0', // OK
          'rpc.response_time_ms': duration.toString(),
        });

        // Record metrics
        this.telemetryService.incrementCounter('grpc_requests_total', 1, {
          service: className,
          method: methodName,
          status: 'success',
        });

        this.telemetryService.recordHistogram('grpc_request_duration_ms', duration, {
          service: className,
          method: methodName,
        });

        // this.logger.log(`gRPC ${className}.${methodName} - Success - ${duration}ms`);
      }),
      catchError((error) => {
        const duration = Date.now() - startTime;

        // End span with error
        this.telemetryService.endSpan(spanId, {
          'rpc.grpc.status_code': '2', // UNKNOWN
          'rpc.error': error.message || 'Unknown error',
          'rpc.response_time_ms': duration.toString(),
        });

        // Record error metrics
        this.telemetryService.incrementCounter('grpc_requests_total', 1, {
          service: className,
          method: methodName,
          status: 'error',
        });

        this.telemetryService.incrementCounter('grpc_errors_total', 1, {
          service: className,
          method: methodName,
          error_type: error.constructor.name,
        });

        // this.logger.error(`gRPC ${className}.${methodName} - Error - ${duration}ms - ${error.message}`, error.stack);

        throw error;
      }),
    );
  }
}
