/**
 * Telemetry configuration interface
 */
export interface TelemetryConfig {
  /** Whether telemetry is enabled */
  enabled: boolean;

  /** Service name for telemetry */
  serviceName: string;

  /** Service version */
  serviceVersion?: string;

  /** Environment (development, staging, production) */
  environment?: string;

  /** Tracing configuration */
  tracing?: TracingConfig;

  /** Metrics configuration */
  metrics?: MetricsConfig;

  /** Logging configuration */
  logging?: LoggingConfig;
}

/**
 * Tracing configuration
 */
export interface TracingConfig {
  /** Whether tracing is enabled */
  enabled: boolean;

  /** Jaeger endpoint for trace export */
  jaegerEndpoint?: string;

  /** OTLP endpoint for trace export */
  otlpEndpoint?: string;

  /** Sample rate (0.0 to 1.0) */
  sampleRate?: number;

  /** Additional resource attributes */
  resourceAttributes?: Record<string, string>;
}

/**
 * Metrics configuration
 */
export interface MetricsConfig {
  /** Whether metrics are enabled */
  enabled: boolean;

  /** Prometheus endpoint */
  prometheusEndpoint?: string;

  /** Metrics collection interval in milliseconds */
  collectionInterval?: number;

  /** Custom metrics to collect */
  customMetrics?: string[];
}

/**
 * Logging configuration
 */
export interface LoggingConfig {
  /** Log level */
  level: 'error' | 'warn' | 'info' | 'debug';

  /** Whether to include trace context in logs */
  includeTraceContext: boolean;

  /** Log format */
  format?: 'json' | 'text';

  /** Additional log fields */
  additionalFields?: Record<string, any>;
}

/**
 * Default telemetry configuration
 * Disabled by default in development mode for better performance
 */
export const DEFAULT_TELEMETRY_CONFIG: TelemetryConfig = {
  enabled: process.env['NODE_ENV'] !== 'development',
  serviceName: 'qeep-service',
  serviceVersion: '1.0.0',
  environment: process.env['NODE_ENV'] || 'development',
  tracing: {
    enabled: process.env['NODE_ENV'] !== 'development',
    sampleRate: process.env['NODE_ENV'] === 'development' ? 0.1 : 1.0, // Lower sample rate in dev
    jaegerEndpoint: process.env['JAEGER_ENDPOINT'] || 'http://localhost:14268/api/traces',
    resourceAttributes: {},
  },
  metrics: {
    enabled: process.env['NODE_ENV'] !== 'development',
    prometheusEndpoint: '/metrics',
    collectionInterval: process.env['NODE_ENV'] === 'development' ? 10000 : 5000, // Less frequent in dev
    customMetrics: [],
  },
  logging: {
    level: 'info',
    includeTraceContext: true,
    format: 'json',
    additionalFields: {},
  },
};

/**
 * Telemetry span interface
 */
export interface TelemetrySpan {
  /** Span name */
  name: string;

  /** Span attributes */
  attributes?: Record<string, string | number | boolean>;

  /** Span kind */
  kind?: 'server' | 'client' | 'producer' | 'consumer' | 'internal';

  /** Parent span context */
  parentContext?: any;
}

/**
 * Telemetry metrics interface
 */
export interface TelemetryMetrics {
  /** Counter metrics */
  counters: Map<string, number>;

  /** Histogram metrics */
  histograms: Map<string, number[]>;

  /** Gauge metrics */
  gauges: Map<string, number>;
}

/**
 * Telemetry event interface
 */
export interface TelemetryEvent {
  /** Event name */
  name: string;

  /** Event timestamp */
  timestamp: Date;

  /** Event attributes */
  attributes?: Record<string, any>;

  /** Event severity */
  severity?: 'info' | 'warn' | 'error';
}
