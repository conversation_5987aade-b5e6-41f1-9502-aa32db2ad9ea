import { Controller, Get, Header } from '@nestjs/common';
import { TelemetryService } from '../services/telemetry.service';

/**
 * Controller to expose telemetry endpoints
 */
@Controller('telemetry')
export class TelemetryController {
  constructor(private readonly telemetryService: TelemetryService) {}

  /**
   * Prometheus metrics endpoint
   */
  @Get('metrics')
  @Header('Content-Type', 'text/plain')
  getMetrics(): string {
    return this.telemetryService.getPrometheusMetrics();
  }

  /**
   * Health check endpoint with telemetry info
   */
  @Get('health')
  getHealth() {
    const metrics = this.telemetryService.getMetrics();
    
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'user-service',
      telemetry: {
        counters: Object.fromEntries(metrics.counters),
        gauges: Object.fromEntries(metrics.gauges),
        histograms: Object.fromEntries(
          Array.from(metrics.histograms.entries()).map(([key, values]) => [
            key,
            {
              count: values.length,
              sum: values.reduce((a, b) => a + b, 0),
              avg: values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0,
            },
          ])
        ),
      },
    };
  }
}
