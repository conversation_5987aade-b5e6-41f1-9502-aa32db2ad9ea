import { Injectable, Logger } from '@nestjs/common';
import { TelemetryConfig, TelemetryEvent, TelemetryMetrics, TelemetrySpan } from '../interfaces/telemetry.interface';

/**
 * Telemetry service for collecting metrics, traces, and logs
 */
@Injectable()
export class TelemetryService {
  private readonly logger = new Logger(TelemetryService.name);
  private config: TelemetryConfig | undefined;
  private metrics: TelemetryMetrics;
  private activeSpans: Map<string, any> = new Map();

  constructor() {
    this.metrics = {
      counters: new Map(),
      histograms: new Map(),
      gauges: new Map(),
    };
  }

  /**
   * Initialize telemetry with configuration
   */
  initialize(config: TelemetryConfig): void {
    this.config = config;

    if (!config.enabled) {
      const reason = config.environment === 'development' ? 'Telemetry is disabled (development mode)' : 'Telemetry is disabled';
      this.logger.log(`${reason}. Set TELEMETRY_ENABLED=true to enable.`);
      return;
    }

    this.logger.log(`Initializing telemetry for service: ${config.serviceName}`);

    if (config.tracing?.enabled) {
      this.initializeTracing();
    }

    if (config.metrics?.enabled) {
      this.initializeMetrics();
    }

    this.logger.log('Telemetry initialized successfully');
  }

  /**
   * Start a new span
   */
  startSpan(spanConfig: TelemetrySpan): string {
    if (!this.config?.tracing?.enabled) {
      return '';
    }

    const spanId = this.generateSpanId();
    const span = {
      id: spanId,
      name: spanConfig.name,
      startTime: Date.now(),
      attributes: spanConfig.attributes || {},
      kind: spanConfig.kind || 'internal',
    };

    this.activeSpans.set(spanId, span);

    this.logger.debug(`Started span: ${spanConfig.name} (${spanId})`);
    return spanId;
  }

  /**
   * End a span
   */
  endSpan(spanId: string, attributes?: Record<string, any>): void {
    if (!this.config?.tracing?.enabled || !spanId) {
      return;
    }

    const span = this.activeSpans.get(spanId);
    if (!span) {
      this.logger.warn(`Span not found: ${spanId}`);
      return;
    }

    span.endTime = Date.now();
    span.duration = span.endTime - span.startTime;

    if (attributes) {
      span.attributes = { ...span.attributes, ...attributes };
    }

    this.activeSpans.delete(spanId);

    this.logger.debug(`Ended span: ${span.name} (${spanId}) - Duration: ${span.duration}ms`);

    // In a real implementation, this would export to a tracing backend
    this.exportSpan(span);
  }

  /**
   * Record a counter metric
   */
  incrementCounter(name: string, value: number = 1, labels?: Record<string, string>): void {
    if (!this.config?.metrics?.enabled) {
      return;
    }

    const key = this.buildMetricKey(name, labels);
    const current = this.metrics.counters.get(key) || 0;
    this.metrics.counters.set(key, current + value);

    this.logger.debug(`Counter incremented: ${key} = ${current + value}`);
  }

  /**
   * Record a histogram metric
   */
  recordHistogram(name: string, value: number, labels?: Record<string, string>): void {
    if (!this.config?.metrics?.enabled) {
      return;
    }

    const key = this.buildMetricKey(name, labels);
    const values = this.metrics.histograms.get(key) || [];
    values.push(value);
    this.metrics.histograms.set(key, values);

    this.logger.debug(`Histogram recorded: ${key} = ${value}`);
  }

  /**
   * Set a gauge metric
   */
  setGauge(name: string, value: number, labels?: Record<string, string>): void {
    if (!this.config?.metrics?.enabled) {
      return;
    }

    const key = this.buildMetricKey(name, labels);
    this.metrics.gauges.set(key, value);

    this.logger.debug(`Gauge set: ${key} = ${value}`);
  }

  /**
   * Record a telemetry event
   */
  recordEvent(event: TelemetryEvent): void {
    if (!this.config?.enabled) {
      return;
    }

    const logLevel = event.severity || 'info';
    const message = `Telemetry Event: ${event.name}`;
    const context = {
      timestamp: event.timestamp,
      attributes: event.attributes,
    };

    switch (logLevel) {
      case 'error':
        this.logger.error(message, context);
        break;
      case 'warn':
        this.logger.warn(message, context);
        break;
      default:
        this.logger.log(message, context);
        break;
    }
  }

  /**
   * Get current metrics
   */
  getMetrics(): TelemetryMetrics {
    return {
      counters: new Map(this.metrics.counters),
      histograms: new Map(this.metrics.histograms),
      gauges: new Map(this.metrics.gauges),
    };
  }

  /**
   * Get metrics in Prometheus format
   */
  getPrometheusMetrics(): string {
    if (!this.config?.metrics?.enabled) {
      return '';
    }

    let output = '';

    // Export counters
    for (const [key, value] of this.metrics.counters) {
      output += `# TYPE ${key} counter\n`;
      output += `${key} ${value}\n`;
    }

    // Export gauges
    for (const [key, value] of this.metrics.gauges) {
      output += `# TYPE ${key} gauge\n`;
      output += `${key} ${value}\n`;
    }

    // Export histograms (simplified)
    for (const [key, values] of this.metrics.histograms) {
      const sum = values.reduce((a, b) => a + b, 0);
      const count = values.length;
      output += `# TYPE ${key} histogram\n`;
      output += `${key}_sum ${sum}\n`;
      output += `${key}_count ${count}\n`;
    }

    return output;
  }

  /**
   * Initialize tracing
   */
  private initializeTracing(): void {
    this.logger.log('Tracing initialized');
    // In a real implementation, this would set up OpenTelemetry SDK
  }

  /**
   * Initialize metrics
   */
  private initializeMetrics(): void {
    this.logger.log('Metrics initialized');
    // In a real implementation, this would set up Prometheus client
  }

  /**
   * Export span to tracing backend
   */
  private exportSpan(span: any): void {
    // In a real implementation, this would export to Jaeger, OTLP, etc.
    this.logger.debug('Span exported', span);
  }

  /**
   * Generate a unique span ID
   */
  private generateSpanId(): string {
    return Math.random().toString(36).substring(2, 15);
  }

  /**
   * Build metric key with labels
   */
  private buildMetricKey(name: string, labels?: Record<string, string>): string {
    if (!labels || Object.keys(labels).length === 0) {
      return name;
    }

    const labelStr = Object.entries(labels)
      .map(([key, value]) => `${key}="${value}"`)
      .join(',');

    return `${name}{${labelStr}}`;
  }
}
