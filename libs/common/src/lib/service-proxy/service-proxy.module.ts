import { Module } from '@nestjs/common';
import { ProtoModule } from '../proto';
import { ServiceProxyFactoryService } from './services/service-proxy-factory.service';
import { ServiceRegistryService } from './services/service-registry.service';

@Module({
  imports: [ProtoModule],
  controllers: [],
  providers: [ServiceRegistryService, ServiceProxyFactoryService],
  exports: [ServiceRegistryService, ServiceProxyFactoryService],
})
export class ServiceProxyModule {}
