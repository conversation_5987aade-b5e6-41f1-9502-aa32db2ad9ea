import { Observable } from 'rxjs';

/**
 * Configuration for a gRPC service proxy
 */
export interface ServiceProxyConfig {
  /** Service name */
  name: string;
  
  /** gRPC client configuration */
  grpc: {
    /** gRPC service URL */
    url: string;
    
    /** Proto package name */
    package: string;
    
    /** Proto file path */
    protoPath: string;
    
    /** Service class name in proto */
    serviceName: string;
    
    /** Loader options */
    loaderOptions?: any;
    
    /** Channel options */
    channelOptions?: any;
  };
  
  /** Timeout configuration */
  timeout?: number;
  
  /** Retry configuration */
  retry?: {
    attempts: number;
    delay: number;
  };
  
  /** Circuit breaker configuration */
  circuitBreaker?: {
    enabled: boolean;
    failureThreshold: number;
    timeout: number;
  };
}

/**
 * Service proxy for handling gRPC communication
 */
export interface IServiceProxy {
  /** Service name */
  readonly serviceName: string;
  
  /** Check if service is healthy */
  isHealthy(): Promise<boolean>;
  
  /** Call a gRPC method */
  call<TRequest, TResponse>(
    method: string,
    request: TRequest,
    metadata?: any
  ): Promise<TResponse>;
  
  /** Call a gRPC method with streaming response */
  callStream<TRequest, TResponse>(
    method: string,
    request: TRequest,
    metadata?: any
  ): Observable<TResponse>;
  
  /** Get service health status */
  getHealthStatus(): Promise<any>;
  
  /** Get service metrics */
  getMetrics(): any;
}

/**
 * Factory for creating service proxies
 */
export interface IServiceProxyFactory {
  /** Create a service proxy */
  createProxy(config: ServiceProxyConfig): Promise<IServiceProxy>;

  /** Get existing proxy by name */
  getProxy(serviceName: string): IServiceProxy | null;

  /** Get all registered proxies */
  getAllProxies(): IServiceProxy[];

  /** Remove a proxy */
  removeProxy(serviceName: string): boolean;
}

/**
 * Service registry for managing service proxies
 */
export interface IServiceRegistry {
  /** Register a service proxy */
  register(proxy: IServiceProxy): void;
  
  /** Unregister a service proxy */
  unregister(serviceName: string): boolean;
  
  /** Get a service proxy by name */
  get(serviceName: string): IServiceProxy | null;
  
  /** Get all registered services */
  getAll(): IServiceProxy[];
  
  /** Check if service is registered */
  has(serviceName: string): boolean;
  
  /** Get service names */
  getServiceNames(): string[];
}

/**
 * HTTP to gRPC proxy controller interface
 */
export interface IProxyController {
  /** Handle HTTP request and proxy to gRPC service */
  handleRequest(
    serviceName: string,
    method: string,
    request: any,
    metadata?: any
  ): Promise<any>;
  
  /** Get available services */
  getServices(): string[];
  
  /** Get service health */
  getServiceHealth(serviceName: string): Promise<any>;
}
