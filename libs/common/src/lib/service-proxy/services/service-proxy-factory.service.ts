import { Injectable, Logger } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { ClientGrpc, Transport } from '@nestjs/microservices';
import { ProtoConfigService } from '../../proto';
import { IServiceProxy, IServiceProxyFactory, ServiceProxyConfig } from '../interfaces/service-proxy.interface';
import { BaseServiceProxy } from './base-service-proxy.service';
import { ServiceRegistryService } from './service-registry.service';

/**
 * Factory for creating and managing service proxies
 */
@Injectable()
export class ServiceProxyFactoryService implements IServiceProxyFactory {
  private readonly logger = new Logger(ServiceProxyFactoryService.name);

  constructor(private readonly moduleRef: ModuleRef, private readonly protoConfigService: ProtoConfigService, private readonly serviceRegistry: ServiceRegistryService) {}

  /**
   * Create a service proxy
   */
  async createProxy(config: ServiceProxyConfig): Promise<IServiceProxy> {
    try {
      this.logger.log(`Creating service proxy for ${config.name}`);

      // Validate configuration
      this.validateConfig(config);

      // Create gRPC client
      const grpcClient = await this.createGrpcClient(config);

      // Create proxy instance
      const proxy = new BaseServiceProxy(config.name, config, grpcClient);

      // Initialize the proxy
      await proxy.onModuleInit();

      // Register the proxy
      this.serviceRegistry.register(proxy);

      this.logger.log(`Service proxy created and registered: ${config.name}`);

      return proxy;
    } catch (error) {
      this.logger.error(`Failed to create service proxy for ${config.name}`, {
        error: error instanceof Error ? error.message : String(error),
        config,
      });
      throw error;
    }
  }

  /**
   * Get existing proxy by name
   */
  getProxy(serviceName: string): IServiceProxy | null {
    return this.serviceRegistry.get(serviceName);
  }

  /**
   * Get all registered proxies
   */
  getAllProxies(): IServiceProxy[] {
    return this.serviceRegistry.getAll();
  }

  /**
   * Remove a proxy
   */
  removeProxy(serviceName: string): boolean {
    return this.serviceRegistry.unregister(serviceName);
  }

  /**
   * Create multiple proxies from configurations
   */
  async createProxies(configs: ServiceProxyConfig[]): Promise<IServiceProxy[]> {
    const proxies: IServiceProxy[] = [];

    for (const config of configs) {
      try {
        const proxy = await this.createProxy(config);
        proxies.push(proxy);
      } catch (error) {
        this.logger.error(`Failed to create proxy for ${config.name}`, error);
        // Continue with other proxies
      }
    }

    this.logger.log(`Created ${proxies.length} out of ${configs.length} service proxies`);

    return proxies;
  }

  /**
   * Get default service configurations
   */
  getDefaultConfigs(): ServiceProxyConfig[] {
    return [
      {
        name: 'auth-service',
        grpc: {
          url: process.env['AUTH_SERVICE_GRPC_URL'] || 'localhost:3012',
          package: 'auth',
          protoPath: this.protoConfigService.getProtoPath('auth', 'auth.proto'),
          serviceName: 'AuthService',
          loaderOptions: this.protoConfigService.getLoaderOptions(),
          channelOptions: this.protoConfigService.getChannelOptions(),
        },
        timeout: 30000,
        retry: {
          attempts: 3,
          delay: 1000,
        },
        circuitBreaker: {
          enabled: true,
          failureThreshold: 50,
          timeout: 30000,
        },
      },
      {
        name: 'user-service',
        grpc: {
          url: process.env['USER_SERVICE_GRPC_URL'] || 'localhost:3011',
          package: 'user',
          protoPath: this.protoConfigService.getProtoPath('user', 'user.proto'),
          serviceName: 'UserService',
          loaderOptions: this.protoConfigService.getLoaderOptions(),
          channelOptions: this.protoConfigService.getChannelOptions(),
        },
        timeout: 30000,
        retry: {
          attempts: 3,
          delay: 1000,
        },
        circuitBreaker: {
          enabled: true,
          failureThreshold: 50,
          timeout: 30000,
        },
      },
      {
        name: 'tenant-service',
        grpc: {
          url: process.env['TENANT_SERVICE_GRPC_URL'] || 'localhost:3013',
          package: 'tenant',
          protoPath: this.protoConfigService.getProtoPath('tenant', 'tenant.proto'),
          serviceName: 'TenantService',
          loaderOptions: this.protoConfigService.getLoaderOptions(),
          channelOptions: this.protoConfigService.getChannelOptions(),
        },
        timeout: 30000,
        retry: {
          attempts: 3,
          delay: 1000,
        },
        circuitBreaker: {
          enabled: true,
          failureThreshold: 50,
          timeout: 30000,
        },
      },
    ];
  }

  /**
   * Initialize default service proxies
   */
  async initializeDefaultProxies(): Promise<void> {
    try {
      const configs = this.getDefaultConfigs();
      await this.createProxies(configs);
      this.logger.log('Default service proxies initialized');
    } catch (error) {
      this.logger.error('Failed to initialize default service proxies', error);
      throw error;
    }
  }

  /**
   * Create gRPC client for service
   */
  private async createGrpcClient(config: ServiceProxyConfig): Promise<ClientGrpc> {
    try {
      // This is a simplified version - in a real implementation,
      // you would use NestJS ClientsModule to create the client
      const clientConfig = {
        transport: Transport.GRPC,
        options: {
          package: config.grpc.package,
          protoPath: config.grpc.protoPath,
          url: config.grpc.url,
          loader: config.grpc.loaderOptions,
          channelOptions: config.grpc.channelOptions,
        },
      };

      // For now, return a mock client - this would be replaced with actual client creation
      return {} as ClientGrpc;
    } catch (error) {
      this.logger.error(`Failed to create gRPC client for ${config.name}`, error);
      throw error;
    }
  }

  /**
   * Validate service proxy configuration
   */
  private validateConfig(config: ServiceProxyConfig): void {
    if (!config.name) {
      throw new Error('Service name is required');
    }

    if (!config.grpc) {
      throw new Error('gRPC configuration is required');
    }

    if (!config.grpc.url) {
      throw new Error('gRPC URL is required');
    }

    if (!config.grpc.package) {
      throw new Error('gRPC package is required');
    }

    if (!config.grpc.protoPath) {
      throw new Error('gRPC proto path is required');
    }

    if (!config.grpc.serviceName) {
      throw new Error('gRPC service name is required');
    }
  }
}
