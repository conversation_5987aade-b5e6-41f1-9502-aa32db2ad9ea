import { Injectable, Logger } from '@nestjs/common';
import { IServiceProxy, IServiceRegistry } from '../interfaces/service-proxy.interface';

/**
 * Registry for managing service proxies
 */
@Injectable()
export class ServiceRegistryService implements IServiceRegistry {
  private readonly logger = new Logger(ServiceRegistryService.name);
  private readonly services = new Map<string, IServiceProxy>();

  /**
   * Register a service proxy
   */
  register(proxy: IServiceProxy): void {
    try {
      const serviceName = proxy.serviceName;

      if (this.services.has(serviceName)) {
        this.logger.warn(`Service ${serviceName} is already registered, overriding`);
      }

      this.services.set(serviceName, proxy);

      this.logger.log(`Service proxy registered: ${serviceName}`);
    } catch (error) {
      this.logger.error(`Failed to register service proxy`, {
        serviceName: proxy.serviceName,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Unregister a service proxy
   */
  unregister(serviceName: string): boolean {
    try {
      const removed = this.services.delete(serviceName);

      if (removed) {
        this.logger.log(`Service proxy unregistered: ${serviceName}`);
      } else {
        this.logger.warn(`Service proxy not found for unregistration: ${serviceName}`);
      }

      return removed;
    } catch (error) {
      this.logger.error(`Failed to unregister service proxy`, {
        serviceName,
        error: error instanceof Error ? error.message : String(error),
      });
      return false;
    }
  }

  /**
   * Get a service proxy by name
   */
  get(serviceName: string): IServiceProxy | null {
    return this.services.get(serviceName) || null;
  }

  /**
   * Get all registered services
   */
  getAll(): IServiceProxy[] {
    return Array.from(this.services.values());
  }

  /**
   * Check if service is registered
   */
  has(serviceName: string): boolean {
    return this.services.has(serviceName);
  }

  /**
   * Get service names
   */
  getServiceNames(): string[] {
    return Array.from(this.services.keys());
  }

  /**
   * Get registry statistics
   */
  getStats(): {
    totalServices: number;
    serviceNames: string[];
    healthyServices: number;
    unhealthyServices: number;
  } {
    const serviceNames = this.getServiceNames();

    return {
      totalServices: serviceNames.length,
      serviceNames,
      healthyServices: 0, // Would need to check each service
      unhealthyServices: 0, // Would need to check each service
    };
  }

  /**
   * Check health of all registered services
   */
  async checkAllHealth(): Promise<Record<string, boolean>> {
    const healthResults: Record<string, boolean> = {};

    const healthChecks = Array.from(this.services.entries()).map(async ([serviceName, proxy]) => {
      try {
        const isHealthy = await proxy.isHealthy();
        healthResults[serviceName] = isHealthy;
        return { serviceName, isHealthy };
      } catch (error) {
        this.logger.error(`Health check failed for ${serviceName}`, error);
        healthResults[serviceName] = false;
        return { serviceName, isHealthy: false };
      }
    });

    await Promise.allSettled(healthChecks);

    this.logger.debug('Health check completed for all services', healthResults);

    return healthResults;
  }

  /**
   * Get metrics for all services
   */
  getAllMetrics(): Record<string, any> {
    const metrics: Record<string, any> = {};

    for (const [serviceName, proxy] of this.services.entries()) {
      try {
        metrics[serviceName] = proxy.getMetrics();
      } catch (error) {
        this.logger.error(`Failed to get metrics for ${serviceName}`, error);
        metrics[serviceName] = { error: error instanceof Error ? error.message : String(error) };
      }
    }

    return metrics;
  }

  /**
   * Clear all registered services
   */
  clear(): void {
    const serviceCount = this.services.size;
    this.services.clear();
    this.logger.log(`Cleared ${serviceCount} service proxies from registry`);
  }
}
