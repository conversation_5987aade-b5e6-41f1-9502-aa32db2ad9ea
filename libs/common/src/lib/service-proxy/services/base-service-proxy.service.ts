import { Injectable, Logger, OnModule<PERSON><PERSON>roy } from '@nestjs/common';
import { Observable, catchError, firstValueFrom, retry, throwError, timeout } from 'rxjs';
import { IServiceProxy, ServiceProxyConfig } from '../interfaces/service-proxy.interface';

import { ClientGrpc } from '@nestjs/microservices';

/**
 * Base implementation of a service proxy
 */
@Injectable()
export class BaseServiceProxy implements IServiceProxy, OnModuleDestroy {
  protected readonly logger: Logger;
  protected grpcService: any;
  protected healthService: any;
  protected metrics = {
    totalCalls: 0,
    successfulCalls: 0,
    failedCalls: 0,
    averageResponseTime: 0,
    lastCallTime: null as Date | null,
    lastError: null as string | null,
  };

  constructor(public readonly serviceName: string, protected readonly config: ServiceProxyConfig, protected readonly grpcClient: ClientGrpc) {
    this.logger = new Logger(`${BaseServiceProxy.name}:${serviceName}`);
  }

  /**
   * Initialize the gRPC services
   */
  onModuleInit() {
    try {
      this.grpcService = this.grpcClient.getService(this.config.grpc.serviceName);
      this.healthService = this.grpcClient.getService('HealthService');
      this.logger.log(`Service proxy initialized for ${this.serviceName}`);
    } catch (error) {
      this.logger.error(`Failed to initialize service proxy for ${this.serviceName}`, error);
      throw error;
    }
  }

  /**
   * Cleanup on module destroy
   */
  onModuleDestroy() {
    this.logger.log(`Service proxy destroyed for ${this.serviceName}`);
  }

  /**
   * Check if service is healthy
   */
  async isHealthy(): Promise<boolean> {
    try {
      const healthStatus = await this.getHealthStatus();
      return healthStatus?.status === 'HEALTH_STATUS_HEALTHY';
    } catch (error) {
      this.logger.warn(`Health check failed for ${this.serviceName}`, error);
      return false;
    }
  }

  /**
   * Call a gRPC method
   */
  async call<TRequest, TResponse>(method: string, request: TRequest, metadata?: any): Promise<TResponse> {
    const startTime = Date.now();
    this.metrics.totalCalls++;
    this.metrics.lastCallTime = new Date();

    try {
      this.logger.debug(`Calling gRPC method ${method}`, {
        serviceName: this.serviceName,
        method,
        hasMetadata: !!metadata,
      });

      if (!this.grpcService || !this.grpcService[method]) {
        throw new Error(`Method ${method} not found on service ${this.serviceName}`);
      }

      // Create observable for the gRPC call
      const call$ = this.grpcService[method](request, metadata);

      // Apply timeout and retry logic
      const result = await firstValueFrom(
        call$.pipe(
          timeout(this.config.timeout || 30000),
          retry({
            count: this.config.retry?.attempts || 3,
            delay: this.config.retry?.delay || 1000,
          }),
          catchError((error) => {
            this.logger.error(`gRPC call failed for ${method}`, {
              serviceName: this.serviceName,
              method,
              error: error.message,
            });
            return throwError(() => error);
          }),
        ),
      );

      // Update metrics
      const responseTime = Date.now() - startTime;
      this.updateMetrics(true, responseTime);

      this.logger.debug(`gRPC call successful for ${method}`, {
        serviceName: this.serviceName,
        method,
        responseTime,
      });

      return result as TResponse;
    } catch (error) {
      // Update metrics
      const responseTime = Date.now() - startTime;
      this.updateMetrics(false, responseTime, error instanceof Error ? error.message : String(error));

      this.logger.error(`gRPC call failed for ${method}`, {
        serviceName: this.serviceName,
        method,
        error: error instanceof Error ? error.message : String(error),
        responseTime,
      });

      throw error;
    }
  }

  /**
   * Call a gRPC method with streaming response
   */
  callStream<TRequest, TResponse>(method: string, request: TRequest, metadata?: any): Observable<TResponse> {
    this.logger.debug(`Calling gRPC streaming method ${method}`, {
      serviceName: this.serviceName,
      method,
      hasMetadata: !!metadata,
    });

    if (!this.grpcService || !this.grpcService[method]) {
      return throwError(() => new Error(`Method ${method} not found on service ${this.serviceName}`));
    }

    return this.grpcService[method](request, metadata).pipe(
      timeout(this.config.timeout || 30000),
      catchError((error) => {
        this.logger.error(`gRPC streaming call failed for ${method}`, {
          serviceName: this.serviceName,
          method,
          error: error.message,
        });
        return throwError(() => error);
      }),
    );
  }

  /**
   * Get service health status
   */
  async getHealthStatus(): Promise<any> {
    try {
      if (!this.healthService) {
        throw new Error('Health service not available');
      }

      const request = {
        service: this.serviceName,
        metadata: {
          requestId: `health-check-${Date.now()}`,
          sourceIp: '127.0.0.1',
          userAgent: 'Nginx-Reverse-Proxy/1.0',
          timestamp: new Date(),
        },
      };

      return await firstValueFrom(this.healthService.check(request));
    } catch (error) {
      this.logger.error(`Health check failed for ${this.serviceName}`, error);
      throw error;
    }
  }

  /**
   * Get service metrics
   */
  getMetrics(): any {
    return {
      ...this.metrics,
      serviceName: this.serviceName,
      config: {
        url: this.config.grpc.url,
        timeout: this.config.timeout,
        retryAttempts: this.config.retry?.attempts,
      },
    };
  }

  /**
   * Update internal metrics
   */
  private updateMetrics(success: boolean, responseTime: number, error?: string): void {
    if (success) {
      this.metrics.successfulCalls++;
      this.metrics.lastError = null;
    } else {
      this.metrics.failedCalls++;
      this.metrics.lastError = error || 'Unknown error';
    }

    // Update average response time
    const totalSuccessfulCalls = this.metrics.successfulCalls;
    if (totalSuccessfulCalls > 0) {
      this.metrics.averageResponseTime = (this.metrics.averageResponseTime * (totalSuccessfulCalls - 1) + responseTime) / totalSuccessfulCalls;
    }
  }

  /**
   * Reset metrics
   */
  resetMetrics(): void {
    this.metrics = {
      totalCalls: 0,
      successfulCalls: 0,
      failedCalls: 0,
      averageResponseTime: 0,
      lastCallTime: null,
      lastError: null,
    };
    this.logger.debug(`Metrics reset for ${this.serviceName}`);
  }
}
