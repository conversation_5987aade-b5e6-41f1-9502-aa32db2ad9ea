import { Injectable, Logger } from '@nestjs/common';
import { IDataTransformer, TransformationRule } from '../interfaces/transformation.interface';

/**
 * Service for transforming data structures
 */
@Injectable()
export class DataTransformerService implements IDataTransformer {
  private readonly logger = new Logger(DataTransformerService.name);

  /**
   * Transform data based on transformation rules
   */
  transform<T, R>(
    data: T,
    transformationRules?: TransformationRule[]
  ): R {
    try {
      if (!data) {
        return data as any;
      }

      if (!transformationRules || transformationRules.length === 0) {
        return data as any;
      }

      const result: any = {};

      for (const rule of transformationRules) {
        try {
          const sourceValue = this.getNestedValue(data, rule.source);
          let transformedValue = sourceValue;

          // Apply transformation function if provided
          if (rule.transform && sourceValue !== undefined) {
            transformedValue = rule.transform(sourceValue);
          }

          // Use default value if source is missing and default is provided
          if (transformedValue === undefined && rule.defaultValue !== undefined) {
            transformedValue = rule.defaultValue;
          }

          // Check if required field is missing
          if (rule.required && transformedValue === undefined) {
            throw new Error(`Required field '${rule.source}' is missing`);
          }

          // Set the transformed value
          if (transformedValue !== undefined) {
            this.setNestedValue(result, rule.target, transformedValue);
          }
        } catch (error) {
          this.logger.error('Error applying transformation rule', {
            rule,
            error: error instanceof Error ? error.message : String(error),
          });

          if (rule.required) {
            throw error;
          }
        }
      }

      return result;
    } catch (error) {
      this.logger.error('Error transforming data', {
        error: error instanceof Error ? error.message : String(error),
        rulesCount: transformationRules?.length || 0,
      });
      throw error;
    }
  }

  /**
   * Apply field mapping
   */
  applyFieldMapping<T>(
    data: T,
    fieldMapping: Record<string, string>
  ): any {
    try {
      if (!data || typeof data !== 'object') {
        return data;
      }

      const transformationRules: TransformationRule[] = Object.entries(fieldMapping).map(
        ([source, target]) => ({
          source,
          target,
        })
      );

      return this.transform(data, transformationRules);
    } catch (error) {
      this.logger.error('Error applying field mapping', {
        error: error instanceof Error ? error.message : String(error),
        mappingCount: Object.keys(fieldMapping).length,
      });
      throw error;
    }
  }

  /**
   * Remove sensitive fields
   */
  sanitizeData<T>(data: T, sensitiveFields?: string[]): T {
    try {
      if (!data || typeof data !== 'object') {
        return data;
      }

      const defaultSensitiveFields = [
        'password',
        'passwordHash',
        'secret',
        'token',
        'privateKey',
        'apiKey',
        'ssn',
        'creditCard',
      ];

      const fieldsToRemove = sensitiveFields || defaultSensitiveFields;

      return this.removeFields(data, fieldsToRemove);
    } catch (error) {
      this.logger.error('Error sanitizing data', {
        error: error instanceof Error ? error.message : String(error),
        sensitiveFieldsCount: sensitiveFields?.length || 0,
      });
      return data;
    }
  }

  /**
   * Convert snake_case to camelCase
   */
  toCamelCase<T>(data: T): T {
    try {
      if (!data || typeof data !== 'object') {
        return data;
      }

      if (Array.isArray(data)) {
        return data.map(item => this.toCamelCase(item)) as T;
      }

      const result: any = {};

      for (const [key, value] of Object.entries(data)) {
        const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
        result[camelKey] = this.toCamelCase(value);
      }

      return result;
    } catch (error) {
      this.logger.error('Error converting to camelCase', error);
      return data;
    }
  }

  /**
   * Convert camelCase to snake_case
   */
  toSnakeCase<T>(data: T): T {
    try {
      if (!data || typeof data !== 'object') {
        return data;
      }

      if (Array.isArray(data)) {
        return data.map(item => this.toSnakeCase(item)) as T;
      }

      const result: any = {};

      for (const [key, value] of Object.entries(data)) {
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        result[snakeKey] = this.toSnakeCase(value);
      }

      return result;
    } catch (error) {
      this.logger.error('Error converting to snake_case', error);
      return data;
    }
  }

  /**
   * Get nested value from object using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    try {
      return path.split('.').reduce((current, key) => {
        return current && current[key] !== undefined ? current[key] : undefined;
      }, obj);
    } catch (error) {
      return undefined;
    }
  }

  /**
   * Set nested value in object using dot notation
   */
  private setNestedValue(obj: any, path: string, value: any): void {
    try {
      const keys = path.split('.');
      const lastKey = keys.pop()!;
      
      const target = keys.reduce((current, key) => {
        if (!current[key] || typeof current[key] !== 'object') {
          current[key] = {};
        }
        return current[key];
      }, obj);

      target[lastKey] = value;
    } catch (error) {
      this.logger.error('Error setting nested value', {
        path,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Remove specified fields from object
   */
  private removeFields<T>(obj: T, fieldsToRemove: string[]): T {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.removeFields(item, fieldsToRemove)) as T;
    }

    const result = { ...obj } as any;

    for (const field of fieldsToRemove) {
      if (field in result) {
        delete result[field];
      }
    }

    // Recursively remove fields from nested objects
    for (const key in result) {
      if (result[key] && typeof result[key] === 'object') {
        result[key] = this.removeFields(result[key], fieldsToRemove);
      }
    }

    return result;
  }

  /**
   * Flatten nested object
   */
  flatten(obj: any, prefix = '', separator = '.'): Record<string, any> {
    try {
      const flattened: Record<string, any> = {};

      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          const newKey = prefix ? `${prefix}${separator}${key}` : key;

          if (obj[key] && typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
            Object.assign(flattened, this.flatten(obj[key], newKey, separator));
          } else {
            flattened[newKey] = obj[key];
          }
        }
      }

      return flattened;
    } catch (error) {
      this.logger.error('Error flattening object', error);
      return obj;
    }
  }

  /**
   * Unflatten object
   */
  unflatten(obj: Record<string, any>, separator = '.'): any {
    try {
      const result: any = {};

      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          this.setNestedValue(result, key.replace(new RegExp(`\\${separator}`, 'g'), '.'), obj[key]);
        }
      }

      return result;
    } catch (error) {
      this.logger.error('Error unflattening object', error);
      return obj;
    }
  }
}
