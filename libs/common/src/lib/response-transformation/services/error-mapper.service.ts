import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { GrpcErrorMapping, IErrorMapper } from '../interfaces/transformation.interface';

/**
 * Service for mapping gRPC errors to HTTP errors
 */
@Injectable()
export class ErrorMapperService implements IErrorMapper {
  private readonly logger = new Logger(ErrorMapperService.name);
  private readonly errorMappings = new Map<number, GrpcErrorMapping>();

  constructor() {
    this.initializeDefaultMappings();
  }

  /**
   * Map gRPC error to HTTP error
   */
  mapGrpcError(grpcError: any): {
    httpStatus: HttpStatus;
    errorCode: string;
    message: string;
  } {
    try {
      // Extract gRPC status code
      const grpcCode = this.extractGrpcCode(grpcError);
      
      // Get error mapping
      const mapping = this.getErrorMapping(grpcCode);
      
      if (mapping) {
        return {
          httpStatus: mapping.httpStatus,
          errorCode: mapping.errorCode,
          message: grpcError.message || mapping.defaultMessage,
        };
      }

      // Default mapping for unknown errors
      return {
        httpStatus: HttpStatus.INTERNAL_SERVER_ERROR,
        errorCode: 'INTERNAL_ERROR',
        message: grpcError.message || 'An internal error occurred',
      };
    } catch (error) {
      this.logger.error('Error mapping gRPC error', {
        grpcError,
        error: error instanceof Error ? error.message : String(error),
      });

      return {
        httpStatus: HttpStatus.INTERNAL_SERVER_ERROR,
        errorCode: 'MAPPING_ERROR',
        message: 'Error occurred while processing the request',
      };
    }
  }

  /**
   * Get error mapping for gRPC status code
   */
  getErrorMapping(grpcCode: number): GrpcErrorMapping | null {
    return this.errorMappings.get(grpcCode) || null;
  }

  /**
   * Register custom error mapping
   */
  registerErrorMapping(mapping: GrpcErrorMapping): void {
    this.errorMappings.set(mapping.grpcCode, mapping);
    this.logger.debug('Error mapping registered', {
      grpcCode: mapping.grpcCode,
      httpStatus: mapping.httpStatus,
      errorCode: mapping.errorCode,
    });
  }

  /**
   * Initialize default gRPC to HTTP error mappings
   */
  private initializeDefaultMappings(): void {
    const defaultMappings: GrpcErrorMapping[] = [
      {
        grpcCode: 0, // OK
        httpStatus: HttpStatus.OK,
        errorCode: 'SUCCESS',
        defaultMessage: 'Request completed successfully',
      },
      {
        grpcCode: 1, // CANCELLED
        httpStatus: HttpStatus.REQUEST_TIMEOUT,
        errorCode: 'REQUEST_CANCELLED',
        defaultMessage: 'Request was cancelled',
      },
      {
        grpcCode: 2, // UNKNOWN
        httpStatus: HttpStatus.INTERNAL_SERVER_ERROR,
        errorCode: 'UNKNOWN_ERROR',
        defaultMessage: 'Unknown error occurred',
      },
      {
        grpcCode: 3, // INVALID_ARGUMENT
        httpStatus: HttpStatus.BAD_REQUEST,
        errorCode: 'INVALID_ARGUMENT',
        defaultMessage: 'Invalid argument provided',
      },
      {
        grpcCode: 4, // DEADLINE_EXCEEDED
        httpStatus: HttpStatus.REQUEST_TIMEOUT,
        errorCode: 'TIMEOUT',
        defaultMessage: 'Request timeout exceeded',
      },
      {
        grpcCode: 5, // NOT_FOUND
        httpStatus: HttpStatus.NOT_FOUND,
        errorCode: 'NOT_FOUND',
        defaultMessage: 'Resource not found',
      },
      {
        grpcCode: 6, // ALREADY_EXISTS
        httpStatus: HttpStatus.CONFLICT,
        errorCode: 'ALREADY_EXISTS',
        defaultMessage: 'Resource already exists',
      },
      {
        grpcCode: 7, // PERMISSION_DENIED
        httpStatus: HttpStatus.FORBIDDEN,
        errorCode: 'PERMISSION_DENIED',
        defaultMessage: 'Permission denied',
      },
      {
        grpcCode: 8, // RESOURCE_EXHAUSTED
        httpStatus: HttpStatus.TOO_MANY_REQUESTS,
        errorCode: 'RATE_LIMITED',
        defaultMessage: 'Rate limit exceeded',
      },
      {
        grpcCode: 9, // FAILED_PRECONDITION
        httpStatus: HttpStatus.PRECONDITION_FAILED,
        errorCode: 'PRECONDITION_FAILED',
        defaultMessage: 'Precondition failed',
      },
      {
        grpcCode: 10, // ABORTED
        httpStatus: HttpStatus.CONFLICT,
        errorCode: 'OPERATION_ABORTED',
        defaultMessage: 'Operation was aborted',
      },
      {
        grpcCode: 11, // OUT_OF_RANGE
        httpStatus: HttpStatus.BAD_REQUEST,
        errorCode: 'OUT_OF_RANGE',
        defaultMessage: 'Value out of range',
      },
      {
        grpcCode: 12, // UNIMPLEMENTED
        httpStatus: HttpStatus.NOT_IMPLEMENTED,
        errorCode: 'NOT_IMPLEMENTED',
        defaultMessage: 'Operation not implemented',
      },
      {
        grpcCode: 13, // INTERNAL
        httpStatus: HttpStatus.INTERNAL_SERVER_ERROR,
        errorCode: 'INTERNAL_ERROR',
        defaultMessage: 'Internal server error',
      },
      {
        grpcCode: 14, // UNAVAILABLE
        httpStatus: HttpStatus.SERVICE_UNAVAILABLE,
        errorCode: 'SERVICE_UNAVAILABLE',
        defaultMessage: 'Service temporarily unavailable',
      },
      {
        grpcCode: 15, // DATA_LOSS
        httpStatus: HttpStatus.INTERNAL_SERVER_ERROR,
        errorCode: 'DATA_LOSS',
        defaultMessage: 'Data loss detected',
      },
      {
        grpcCode: 16, // UNAUTHENTICATED
        httpStatus: HttpStatus.UNAUTHORIZED,
        errorCode: 'UNAUTHENTICATED',
        defaultMessage: 'Authentication required',
      },
    ];

    for (const mapping of defaultMappings) {
      this.errorMappings.set(mapping.grpcCode, mapping);
    }

    this.logger.log(`Initialized ${defaultMappings.length} default error mappings`);
  }

  /**
   * Extract gRPC status code from error
   */
  private extractGrpcCode(grpcError: any): number {
    // Try different ways to extract gRPC code
    if (typeof grpcError.code === 'number') {
      return grpcError.code;
    }

    if (grpcError.details && typeof grpcError.details.code === 'number') {
      return grpcError.details.code;
    }

    if (grpcError.status && typeof grpcError.status === 'number') {
      return grpcError.status;
    }

    // Check for string representations
    if (typeof grpcError.code === 'string') {
      const numericCode = parseInt(grpcError.code, 10);
      if (!isNaN(numericCode)) {
        return numericCode;
      }
    }

    // Default to UNKNOWN (2) if we can't extract the code
    this.logger.warn('Could not extract gRPC code from error', { grpcError });
    return 2;
  }

  /**
   * Get all registered error mappings
   */
  getAllMappings(): GrpcErrorMapping[] {
    return Array.from(this.errorMappings.values());
  }

  /**
   * Clear all custom mappings (keeps defaults)
   */
  clearCustomMappings(): void {
    this.errorMappings.clear();
    this.initializeDefaultMappings();
    this.logger.log('Custom error mappings cleared, defaults restored');
  }
}
