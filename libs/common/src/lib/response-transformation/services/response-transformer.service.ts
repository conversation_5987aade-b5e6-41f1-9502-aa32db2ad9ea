import { Injectable, Logger } from '@nestjs/common';
import {
    ApiResponse,
    ErrorResponse,
    IResponseTransformer,
    TransformationContext
} from '../interfaces/transformation.interface';
import { ErrorMapperService } from './error-mapper.service';

/**
 * Service for transforming responses between gRPC and HTTP formats
 */
@Injectable()
export class ResponseTransformerService implements IResponseTransformer {
  private readonly logger = new Logger(ResponseTransformerService.name);

  constructor(private readonly errorMapper: ErrorMapperService) {}

  /**
   * Transform successful gRPC response to HTTP response
   */
  transformSuccess<T>(
    data: T,
    context: TransformationContext
  ): ApiResponse<T> {
    try {
      const processingTime = Date.now() - context.startTime.getTime();

      const response: ApiResponse<T> = {
        success: true,
        data: this.sanitizeResponseData(data),
        metadata: {
          requestId: context.requestId,
          correlationId: context.correlationId,
          timestamp: new Date().toISOString(),
          processingTime,
          version: '1.0',
        },
      };

      this.logger.debug('Response transformed successfully', {
        requestId: context.requestId,
        correlationId: context.correlationId,
        processingTime,
        hasData: !!data,
      });

      return response;
    } catch (error) {
      this.logger.error('Error transforming success response', {
        requestId: context.requestId,
        correlationId: context.correlationId,
        error: error instanceof Error ? error.message : String(error),
      });

      // Return error response if transformation fails
      return this.transformError(error, context) as any;
    }
  }

  /**
   * Transform gRPC error to HTTP error response
   */
  transformError(
    error: any,
    context: TransformationContext
  ): ErrorResponse {
    try {
      const processingTime = Date.now() - context.startTime.getTime();
      
      // Map gRPC error to HTTP error
      const mappedError = this.errorMapper.mapGrpcError(error);

      const errorResponse: ErrorResponse = {
        success: false,
        error: {
          code: mappedError.errorCode,
          message: mappedError.message,
          details: this.extractErrorDetails(error),
        },
        metadata: {
          requestId: context.requestId,
          correlationId: context.correlationId,
          timestamp: new Date().toISOString(),
          processingTime,
        },
      };

      // Add stack trace in development
      if (process.env['NODE_ENV'] === 'development' && error.stack) {
        errorResponse.error.stack = error.stack;
      }

      this.logger.error('Error response transformed', {
        requestId: context.requestId,
        correlationId: context.correlationId,
        errorCode: mappedError.errorCode,
        httpStatus: mappedError.httpStatus,
        processingTime,
      });

      return errorResponse;
    } catch (transformError) {
      this.logger.error('Error transforming error response', {
        requestId: context.requestId,
        correlationId: context.correlationId,
        originalError: error.message,
        transformError: transformError instanceof Error ? transformError.message : String(transformError),
      });

      // Fallback error response
      return {
        success: false,
        error: {
          code: 'TRANSFORMATION_ERROR',
          message: 'An error occurred while processing the request',
        },
        metadata: {
          requestId: context.requestId,
          correlationId: context.correlationId,
          timestamp: new Date().toISOString(),
          processingTime: Date.now() - context.startTime.getTime(),
        },
      };
    }
  }

  /**
   * Transform paginated response
   */
  transformPaginated<T>(
    data: T[],
    pagination: any,
    context: TransformationContext
  ): ApiResponse<T[]> {
    try {
      const response = this.transformSuccess(data, context);

      // Add pagination metadata
      response.pagination = {
        page: pagination.page || 1,
        limit: pagination.limit || data.length,
        total: pagination.total || data.length,
        totalPages: pagination.totalPages || 1,
        hasNext: pagination.hasNext || false,
        hasPrevious: pagination.hasPrevious || false,
      };

      this.logger.debug('Paginated response transformed', {
        requestId: context.requestId,
        correlationId: context.correlationId,
        itemCount: data.length,
        pagination: response.pagination,
      });

      return response;
    } catch (error) {
      this.logger.error('Error transforming paginated response', {
        requestId: context.requestId,
        correlationId: context.correlationId,
        error: error instanceof Error ? error.message : String(error),
      });

      return this.transformError(error, context) as any;
    }
  }

  /**
   * Transform list response (non-paginated)
   */
  transformList<T>(
    data: T[],
    context: TransformationContext
  ): ApiResponse<T[]> {
    return this.transformSuccess(data, context);
  }

  /**
   * Transform single item response
   */
  transformSingle<T>(
    data: T,
    context: TransformationContext
  ): ApiResponse<T> {
    return this.transformSuccess(data, context);
  }

  /**
   * Transform empty response (for operations like delete)
   */
  transformEmpty(context: TransformationContext): ApiResponse<null> {
    return this.transformSuccess(null, context);
  }

  /**
   * Sanitize response data by removing sensitive fields
   */
  private sanitizeResponseData<T>(data: T): T {
    if (!data || typeof data !== 'object') {
      return data;
    }

    // Fields to remove from responses
    const sensitiveFields = [
      'password',
      'passwordHash',
      'secret',
      'token',
      'privateKey',
      'apiKey',
      'internalId',
      'metadata', // Remove gRPC metadata
    ];

    return this.removeSensitiveFields(data, sensitiveFields);
  }

  /**
   * Remove sensitive fields from object
   */
  private removeSensitiveFields<T>(obj: T, sensitiveFields: string[]): T {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.removeSensitiveFields(item, sensitiveFields)) as T;
    }

    const sanitized = { ...obj } as any;

    for (const field of sensitiveFields) {
      if (field in sanitized) {
        delete sanitized[field];
      }
    }

    // Recursively sanitize nested objects
    for (const key in sanitized) {
      if (sanitized[key] && typeof sanitized[key] === 'object') {
        sanitized[key] = this.removeSensitiveFields(sanitized[key], sensitiveFields);
      }
    }

    return sanitized;
  }

  /**
   * Extract error details from gRPC error
   */
  private extractErrorDetails(error: any): any {
    const details: any = {};

    // Extract common error properties
    if (error.details) {
      details.grpcDetails = error.details;
    }

    if (error.code) {
      details.grpcCode = error.code;
    }

    if (error.metadata) {
      details.metadata = error.metadata;
    }

    // Extract validation errors if present
    if (error.validationErrors) {
      details.validationErrors = error.validationErrors;
    }

    return Object.keys(details).length > 0 ? details : undefined;
  }
}
