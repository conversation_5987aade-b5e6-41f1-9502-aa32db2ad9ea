import { Module } from '@nestjs/common';
import { ErrorMapperService } from './services/error-mapper.service';
import { ResponseTransformerService } from './services/response-transformer.service';
import { DataTransformerService } from './services/data-transformer.service';

@Module({
  controllers: [],
  providers: [
    ErrorMapperService,
    ResponseTransformerService,
    DataTransformerService,
  ],
  exports: [
    ErrorMapperService,
    ResponseTransformerService,
    DataTransformerService,
  ],
})
export class ResponseTransformationModule {}
