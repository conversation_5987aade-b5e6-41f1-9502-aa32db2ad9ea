import { HttpStatus } from '@nestjs/common';

/**
 * Standard API response format
 */
export interface ApiResponse<T = any> {
  /** Success indicator */
  success: boolean;
  
  /** Response data */
  data?: T;
  
  /** Error information */
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  
  /** Response metadata */
  metadata: {
    requestId: string;
    correlationId: string;
    timestamp: string;
    processingTime: number;
    version?: string;
  };
  
  /** Pagination information (for list responses) */
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
}

/**
 * Error response format
 */
export interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    stack?: string;
  };
  metadata: {
    requestId: string;
    correlationId: string;
    timestamp: string;
    processingTime: number;
  };
}

/**
 * Transformation context
 */
export interface TransformationContext {
  /** Request ID for tracking */
  requestId: string;
  
  /** Correlation ID for tracing */
  correlationId: string;
  
  /** Request start time */
  startTime: Date;
  
  /** HTTP method */
  method: string;
  
  /** Request path */
  path: string;
  
  /** User context */
  user?: {
    id: string;
    email: string;
    tenantId: string;
  };
  
  /** Additional metadata */
  metadata?: Record<string, any>;
}

/**
 * gRPC error mapping
 */
export interface GrpcErrorMapping {
  /** gRPC status code */
  grpcCode: number;
  
  /** HTTP status code */
  httpStatus: HttpStatus;
  
  /** Error code for API response */
  errorCode: string;
  
  /** Default error message */
  defaultMessage: string;
}

/**
 * Response transformer interface
 */
export interface IResponseTransformer {
  /**
   * Transform successful gRPC response to HTTP response
   */
  transformSuccess<T>(
    data: T,
    context: TransformationContext
  ): ApiResponse<T>;
  
  /**
   * Transform gRPC error to HTTP error response
   */
  transformError(
    error: any,
    context: TransformationContext
  ): ErrorResponse;
  
  /**
   * Transform paginated response
   */
  transformPaginated<T>(
    data: T[],
    pagination: any,
    context: TransformationContext
  ): ApiResponse<T[]>;
}

/**
 * Error mapper interface
 */
export interface IErrorMapper {
  /**
   * Map gRPC error to HTTP error
   */
  mapGrpcError(grpcError: any): {
    httpStatus: HttpStatus;
    errorCode: string;
    message: string;
  };
  
  /**
   * Get error mapping for gRPC status code
   */
  getErrorMapping(grpcCode: number): GrpcErrorMapping | null;
  
  /**
   * Register custom error mapping
   */
  registerErrorMapping(mapping: GrpcErrorMapping): void;
}

/**
 * Data transformer interface
 */
export interface IDataTransformer {
  /**
   * Transform data based on transformation rules
   */
  transform<T, R>(
    data: T,
    transformationRules?: TransformationRule[]
  ): R;
  
  /**
   * Apply field mapping
   */
  applyFieldMapping<T>(
    data: T,
    fieldMapping: Record<string, string>
  ): any;
  
  /**
   * Remove sensitive fields
   */
  sanitizeData<T>(data: T, sensitiveFields?: string[]): T;
}

/**
 * Transformation rule
 */
export interface TransformationRule {
  /** Source field path */
  source: string;
  
  /** Target field path */
  target: string;
  
  /** Transformation function */
  transform?: (value: any) => any;
  
  /** Whether field is required */
  required?: boolean;
  
  /** Default value if source is missing */
  defaultValue?: any;
}
