import { FilterOperator, SortOrder } from '../interfaces/base-query.interface';
import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsInt,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
  ValidateNested
} from 'class-validator';
import { Transform, Type } from 'class-transformer';

/**
 * Base pagination DTO for query parameters
 */
export class PaginationDto {
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 25;
}

/**
 * Sort field DTO for query parameters
 */
export class SortFieldDto {
  @IsString()
  field!: string;

  @IsOptional()
  @IsEnum(SortOrder)
  order?: SortOrder = SortOrder.DESC;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  priority?: number;
}

/**
 * Date range filter DTO
 */
export class DateRangeFilterDto {
  @IsString()
  field!: string;

  @IsOptional()
  @IsDateString()
  from?: string;

  @IsOptional()
  @IsDateString()
  to?: string;
}

/**
 * Numeric range filter DTO
 */
export class NumericRangeFilterDto {
  @IsString()
  field!: string;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  min?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  max?: number;
}

/**
 * Generic filter DTO
 */
export class GenericFilterDto {
  @IsString()
  field!: string;

  @IsEnum(FilterOperator)
  operator!: FilterOperator;

  value: any;
}

/**
 * Base search DTO
 */
export class SearchDto {
  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  searchFields?: string[];

  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  caseSensitive?: boolean = false;

  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  fuzzy?: boolean = false;
}

/**
 * Base filters DTO
 */
export class BaseFiltersDto {
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DateRangeFilterDto)
  dateRanges?: DateRangeFilterDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => NumericRangeFilterDto)
  numericRanges?: NumericRangeFilterDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => GenericFilterDto)
  filters?: GenericFilterDto[];
}

/**
 * Complete base query DTO combining all query capabilities
 */
export class BaseQueryDto extends PaginationDto {
  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').map(field => field.trim());
    }
    return value;
  })
  @IsArray()
  @IsString({ each: true })
  searchFields?: string[];

  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  caseSensitive?: boolean = false;

  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  fuzzy?: boolean = false;

  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return [];
      }
    }
    return value;
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SortFieldDto)
  sort?: SortFieldDto[];

  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  includeDeleted?: boolean = false;

  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').map(field => field.trim());
    }
    return value;
  })
  @IsArray()
  @IsString({ each: true })
  include?: string[];
}
