import { Module } from '@nestjs/common';

/**
 * Query module that provides standardized query functionality.
 * 
 * This module exports DTOs, interfaces, and utilities for implementing
 * consistent search, filtering, pagination, and sorting across all
 * Qeep services.
 * 
 * Features:
 * - Standardized query DTOs with validation
 * - Type-safe interfaces for query operations
 * - Utility functions for building database queries
 * - Consistent response formatting
 * - Reusable across all services
 * 
 * @example
 * ```typescript
 * import { QueryModule } from '@qeep/common';
 * 
 * @Module({
 *   imports: [QueryModule],
 *   // ... other module configuration
 * })
 * export class MyServiceModule {}
 * ```
 */
@Module({
  providers: [],
  exports: []
})
export class QueryModule {
  /**
   * Configure the query module with optional settings
   */
  static forRoot(options?: {
    defaultPageSize?: number;
    maxPageSize?: number;
    enableQueryValidation?: boolean;
  }) {
    return {
      module: QueryModule,
      providers: [
        {
          provide: 'QUERY_CONFIG',
          useValue: {
            defaultPageSize: options?.defaultPageSize || 25,
            maxPageSize: options?.maxPageSize || 100,
            enableQueryValidation: options?.enableQueryValidation ?? true
          }
        }
      ],
      exports: ['QUERY_CONFIG']
    };
  }
}
