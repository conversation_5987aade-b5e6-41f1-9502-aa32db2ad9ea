import { 
  BaseQuery, 
  Query<PERSON><PERSON>ult, 
  QueryMetadata, 
  Sort<PERSON>ield, 
  DateRangeFilter, 
  NumericRangeFilter, 
  GenericFilter,
  FilterOperator,
  SortOrder
} from '../interfaces/base-query.interface';

/**
 * Utility class for building and processing standardized queries.
 * 
 * This class provides methods to convert query DTOs into database queries,
 * apply filters, sorting, and pagination, and format results consistently
 * across all Qeep services.
 * 
 * @example
 * ```typescript
 * // Build Prisma where clause
 * const whereClause = QueryBuilderUtil.buildPrismaWhere(query, {
 *   searchableFields: ['name', 'description'],
 *   filterableFields: ['status', 'type', 'createdAt']
 * });
 * 
 * // Apply sorting
 * const orderBy = QueryBuilderUtil.buildPrismaOrderBy(query.sort);
 * 
 * // Calculate pagination
 * const { skip, take } = QueryBuilderUtil.calculatePagination(query);
 * 
 * // Format results
 * const result = QueryBuilderUtil.formatQueryResult(data, query, totalCount);
 * ```
 */
export class QueryBuilderUtil {
  /**
   * Default pagination values
   */
  static readonly DEFAULT_PAGE = 1;
  static readonly DEFAULT_LIMIT = 25;
  static readonly MAX_LIMIT = 100;

  /**
   * Calculate pagination parameters for database queries
   */
  static calculatePagination(query: BaseQuery): { skip: number; take: number; page: number; limit: number } {
    const page = Math.max(1, query.page || this.DEFAULT_PAGE);
    const limit = Math.min(this.MAX_LIMIT, Math.max(1, query.limit || this.DEFAULT_LIMIT));
    const skip = (page - 1) * limit;
    
    return { skip, take: limit, page, limit };
  }

  /**
   * Build Prisma orderBy clause from sort configuration
   */
  static buildPrismaOrderBy(sort?: SortField[]): any[] {
    if (!sort || sort.length === 0) {
      return [{ createdAt: 'desc' }]; // Default sort
    }

    return sort
      .sort((a, b) => (a.priority || 0) - (b.priority || 0)) // Sort by priority
      .map(sortField => ({
        [sortField.field]: sortField.order.toLowerCase()
      }));
  }

  /**
   * Build Prisma where clause from query filters
   */
  static buildPrismaWhere(
    query: BaseQuery, 
    config: {
      searchableFields?: string[];
      filterableFields?: string[];
      customFilters?: Record<string, any>;
    } = {}
  ): any {
    const where: any = {};

    // Apply search
    if (query.search && config.searchableFields && config.searchableFields.length > 0) {
      const searchConditions = config.searchableFields.map(field => ({
        [field]: {
          contains: query.search,
          mode: query.caseSensitive ? 'default' : 'insensitive'
        }
      }));

      where.OR = searchConditions;
    }

    // Apply date range filters
    if (query.filters?.dateRanges) {
      query.filters.dateRanges.forEach(dateRange => {
        if (config.filterableFields?.includes(dateRange.field)) {
          where[dateRange.field] = {};
          if (dateRange.from) {
            where[dateRange.field].gte = new Date(dateRange.from);
          }
          if (dateRange.to) {
            where[dateRange.field].lte = new Date(dateRange.to);
          }
        }
      });
    }

    // Apply numeric range filters
    if (query.filters?.numericRanges) {
      query.filters.numericRanges.forEach(numericRange => {
        if (config.filterableFields?.includes(numericRange.field)) {
          where[numericRange.field] = {};
          if (numericRange.min !== undefined) {
            where[numericRange.field].gte = numericRange.min;
          }
          if (numericRange.max !== undefined) {
            where[numericRange.field].lte = numericRange.max;
          }
        }
      });
    }

    // Apply generic filters
    if (query.filters?.filters) {
      query.filters.filters.forEach(filter => {
        if (config.filterableFields?.includes(filter.field)) {
          where[filter.field] = this.buildFilterCondition(filter);
        }
      });
    }

    // Apply custom filters
    if (config.customFilters) {
      Object.assign(where, config.customFilters);
    }

    // Handle soft deletes
    if (!query.includeDeleted) {
      where.deletedAt = null;
    }

    return where;
  }

  /**
   * Build filter condition based on operator
   */
  private static buildFilterCondition(filter: GenericFilter): any {
    switch (filter.operator) {
      case FilterOperator.EQUALS:
        return filter.value;
      case FilterOperator.NOT_EQUALS:
        return { not: filter.value };
      case FilterOperator.GREATER_THAN:
        return { gt: filter.value };
      case FilterOperator.GREATER_THAN_OR_EQUAL:
        return { gte: filter.value };
      case FilterOperator.LESS_THAN:
        return { lt: filter.value };
      case FilterOperator.LESS_THAN_OR_EQUAL:
        return { lte: filter.value };
      case FilterOperator.IN:
        return { in: Array.isArray(filter.value) ? filter.value : [filter.value] };
      case FilterOperator.NOT_IN:
        return { notIn: Array.isArray(filter.value) ? filter.value : [filter.value] };
      case FilterOperator.CONTAINS:
        return { contains: filter.value, mode: 'insensitive' };
      case FilterOperator.STARTS_WITH:
        return { startsWith: filter.value, mode: 'insensitive' };
      case FilterOperator.ENDS_WITH:
        return { endsWith: filter.value, mode: 'insensitive' };
      case FilterOperator.IS_NULL:
        return null;
      case FilterOperator.IS_NOT_NULL:
        return { not: null };
      default:
        return filter.value;
    }
  }

  /**
   * Format query results with metadata
   */
  static formatQueryResult<T>(
    data: T[], 
    query: BaseQuery, 
    totalCount: number
  ): QueryResult<T> {
    const { page, limit } = this.calculatePagination(query);
    const totalPages = Math.ceil(totalCount / limit);
    
    const meta: QueryMetadata = {
      page,
      limit,
      total: totalCount,
      totalPages,
      hasNext: page < totalPages,
      hasPrevious: page > 1,
      search: query.search,
      sort: query.sort,
      appliedFilters: this.summarizeFilters(query)
    };

    return { data, meta };
  }

  /**
   * Summarize applied filters for metadata
   */
  private static summarizeFilters(query: BaseQuery): { count: number; summary: string[] } {
    const summary: string[] = [];
    let count = 0;

    if (query.search) {
      summary.push(`Search: "${query.search}"`);
      count++;
    }

    if (query.filters?.dateRanges?.length) {
      query.filters.dateRanges.forEach(dateRange => {
        const parts = [];
        if (dateRange.from) parts.push(`from ${dateRange.from}`);
        if (dateRange.to) parts.push(`to ${dateRange.to}`);
        summary.push(`${dateRange.field}: ${parts.join(' ')}`);
        count++;
      });
    }

    if (query.filters?.numericRanges?.length) {
      query.filters.numericRanges.forEach(numericRange => {
        const parts = [];
        if (numericRange.min !== undefined) parts.push(`min ${numericRange.min}`);
        if (numericRange.max !== undefined) parts.push(`max ${numericRange.max}`);
        summary.push(`${numericRange.field}: ${parts.join(' ')}`);
        count++;
      });
    }

    if (query.filters?.filters?.length) {
      query.filters.filters.forEach(filter => {
        summary.push(`${filter.field} ${filter.operator} ${JSON.stringify(filter.value)}`);
        count++;
      });
    }

    return { count, summary };
  }

  /**
   * Validate query parameters
   */
  static validateQuery(query: BaseQuery, config: {
    maxLimit?: number;
    allowedSortFields?: string[];
    allowedSearchFields?: string[];
    allowedFilterFields?: string[];
  } = {}): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate pagination
    if (query.page && query.page < 1) {
      errors.push('Page must be greater than 0');
    }

    if (query.limit && query.limit < 1) {
      errors.push('Limit must be greater than 0');
    }

    if (query.limit && config.maxLimit && query.limit > config.maxLimit) {
      errors.push(`Limit cannot exceed ${config.maxLimit}`);
    }

    // Validate sort fields
    if (query.sort && config.allowedSortFields) {
      query.sort.forEach(sortField => {
        if (!config.allowedSortFields!.includes(sortField.field)) {
          errors.push(`Sort field '${sortField.field}' is not allowed`);
        }
      });
    }

    // Validate search fields
    if (query.searchFields && config.allowedSearchFields) {
      query.searchFields.forEach(searchField => {
        if (!config.allowedSearchFields!.includes(searchField)) {
          errors.push(`Search field '${searchField}' is not allowed`);
        }
      });
    }

    // Validate filter fields
    if (query.filters && config.allowedFilterFields) {
      const filterFields = [
        ...(query.filters.dateRanges?.map(f => f.field) || []),
        ...(query.filters.numericRanges?.map(f => f.field) || []),
        ...(query.filters.filters?.map(f => f.field) || [])
      ];

      filterFields.forEach(filterField => {
        if (!config.allowedFilterFields!.includes(filterField)) {
          errors.push(`Filter field '${filterField}' is not allowed`);
        }
      });
    }

    return { isValid: errors.length === 0, errors };
  }
}
