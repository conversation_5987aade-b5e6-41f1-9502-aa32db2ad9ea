/**
 * Base interfaces for standardized query functionality across Qeep services.
 * 
 * These interfaces provide a consistent foundation for search, filtering, pagination,
 * and sorting capabilities that can be reused across all services in the platform.
 * 
 * @example
 * ```typescript
 * // Basic pagination query
 * const query: PaginationQuery = {
 *   page: 1,
 *   limit: 25
 * };
 * 
 * // Advanced query with search and filters
 * const advancedQuery: BaseQuery = {
 *   page: 1,
 *   limit: 10,
 *   search: "financial services",
 *   sort: [{ field: "createdAt", order: "DESC" }],
 *   filters: {
 *     status: ["ACTIVE", "PENDING"],
 *     dateRange: {
 *       field: "createdAt",
 *       from: "2025-01-01",
 *       to: "2025-12-31"
 *     }
 *   }
 * };
 * ```
 */

/**
 * Sort order enumeration
 */
export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC'
}

/**
 * Comparison operators for filtering
 */
export enum FilterOperator {
  EQUALS = 'eq',
  NOT_EQUALS = 'ne',
  GREATER_THAN = 'gt',
  GREATER_THAN_OR_EQUAL = 'gte',
  LESS_THAN = 'lt',
  LESS_THAN_OR_EQUAL = 'lte',
  IN = 'in',
  NOT_IN = 'nin',
  CONTAINS = 'contains',
  STARTS_WITH = 'startsWith',
  ENDS_WITH = 'endsWith',
  BETWEEN = 'between',
  IS_NULL = 'isNull',
  IS_NOT_NULL = 'isNotNull'
}

/**
 * Sort configuration for a single field
 */
export interface SortField {
  /** Field name to sort by */
  field: string;
  
  /** Sort order (ASC or DESC) */
  order: SortOrder;
  
  /** Priority for multi-field sorting (lower numbers = higher priority) */
  priority?: number;
}

/**
 * Date range filter configuration
 */
export interface DateRangeFilter {
  /** Field name to apply date range filter to */
  field: string;
  
  /** Start date (ISO string or Date) */
  from?: string | Date;
  
  /** End date (ISO string or Date) */
  to?: string | Date;
}

/**
 * Numeric range filter configuration
 */
export interface NumericRangeFilter {
  /** Field name to apply numeric range filter to */
  field: string;
  
  /** Minimum value (inclusive) */
  min?: number;
  
  /** Maximum value (inclusive) */
  max?: number;
}

/**
 * Generic filter configuration
 */
export interface GenericFilter {
  /** Field name to filter */
  field: string;
  
  /** Filter operator */
  operator: FilterOperator;
  
  /** Filter value(s) */
  value: any;
}

/**
 * Base pagination interface
 */
export interface PaginationQuery {
  /** Page number (1-based) */
  page?: number;
  
  /** Number of items per page */
  limit?: number;
}

/**
 * Search configuration
 */
export interface SearchQuery {
  /** Search term */
  search?: string;
  
  /** Fields to search in (if not specified, searches all searchable fields) */
  searchFields?: string[];
  
  /** Whether search should be case-sensitive */
  caseSensitive?: boolean;
  
  /** Whether to use fuzzy matching */
  fuzzy?: boolean;
}

/**
 * Base filter interface that can be extended by specific services
 */
export interface BaseFilters {
  /** Date range filters */
  dateRanges?: DateRangeFilter[];
  
  /** Numeric range filters */
  numericRanges?: NumericRangeFilter[];
  
  /** Generic filters */
  filters?: GenericFilter[];
  
  /** Service-specific filters (to be extended) */
  [key: string]: any;
}

/**
 * Complete base query interface combining all query capabilities
 */
export interface BaseQuery extends PaginationQuery, SearchQuery {
  /** Sorting configuration */
  sort?: SortField[];
  
  /** Filter configuration */
  filters?: BaseFilters;
  
  /** Include soft-deleted records */
  includeDeleted?: boolean;
  
  /** Include related entities */
  include?: string[];
}

/**
 * Query result metadata
 */
export interface QueryMetadata {
  /** Current page number */
  page: number;
  
  /** Items per page */
  limit: number;
  
  /** Total number of items */
  total: number;
  
  /** Total number of pages */
  totalPages: number;
  
  /** Whether there is a next page */
  hasNext: boolean;
  
  /** Whether there is a previous page */
  hasPrevious: boolean;
  
  /** Applied search term */
  search?: string;
  
  /** Applied sort configuration */
  sort?: SortField[];
  
  /** Applied filters summary */
  appliedFilters?: {
    count: number;
    summary: string[];
  };
}

/**
 * Standardized query result interface
 */
export interface QueryResult<T> {
  /** Result data */
  data: T[];
  
  /** Query metadata */
  meta: QueryMetadata;
}
