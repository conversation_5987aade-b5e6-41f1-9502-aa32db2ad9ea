/* eslint-disable @typescript-eslint/no-explicit-any */
import { Transform } from 'class-transformer';

/**
 * Utility functions for handling case transformations between snake_case and camelCase
 */

/**
 * Converts snake_case string to camelCase
 */
export function snakeToCamelCase(str: string): string {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
}

/**
 * Converts camelCase string to snake_case
 */
export function camelToSnakeCase(str: string): string {
  return str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);
}

/**
 * Recursively transforms object keys from snake_case to camelCase
 */
export function transformToCamelCase(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => transformToCamelCase(item));
  }

  if (obj instanceof Date) {
    return obj;
  }

  if (typeof obj === 'object' && obj.constructor === Object) {
    const transformed: any = {};
    
    for (const [key, value] of Object.entries(obj)) {
      const camelKey = snakeToCamelCase(key);
      transformed[camelKey] = transformToCamelCase(value);
    }
    
    return transformed;
  }

  return obj;
}

/**
 * Recursively transforms object keys from camelCase to snake_case
 */
export function transformToSnakeCase(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => transformToSnakeCase(item));
  }

  if (obj instanceof Date) {
    return obj;
  }

  if (typeof obj === 'object' && obj.constructor === Object) {
    const transformed: any = {};
    
    for (const [key, value] of Object.entries(obj)) {
      const snakeKey = camelToSnakeCase(key);
      transformed[snakeKey] = transformToSnakeCase(value);
    }
    
    return transformed;
  }

  return obj;
}

/**
 * Class-transformer decorator that accepts both snake_case and camelCase field names
 * and transforms them to camelCase for internal use
 */
export function AcceptBothCases() {
  return Transform(({ obj, key }) => {
    // First try the exact key (camelCase)
    if (obj[key] !== undefined) {
      return obj[key];
    }

    // Then try the snake_case version
    const snakeKey = camelToSnakeCase(key);
    if (obj[snakeKey] !== undefined) {
      return obj[snakeKey];
    }

    // Finally try converting snake_case input key to camelCase
    const camelKey = snakeToCamelCase(key);
    if (obj[camelKey] !== undefined) {
      return obj[camelKey];
    }

    return undefined;
  });
}

/**
 * Decorator specifically for transforming snake_case input to camelCase
 */
export function TransformSnakeCase() {
  return Transform(({ obj, key }) => {
    // Try snake_case version first (external API input)
    const snakeKey = camelToSnakeCase(key);
    if (obj[snakeKey] !== undefined) {
      return obj[snakeKey];
    }

    // Fall back to camelCase (internal usage)
    return obj[key];
  });
}

/**
 * Transform incoming request body from snake_case to camelCase
 */
export function transformRequestBody(body: any): any {
  if (!body || typeof body !== 'object') {
    return body;
  }

  const transformed: any = {};

  for (const [key, value] of Object.entries(body)) {
    // Convert snake_case keys to camelCase
    const camelKey = snakeToCamelCase(key);
    transformed[camelKey] = Array.isArray(value)
      ? value.map(item => typeof item === 'object' ? transformRequestBody(item) : item)
      : typeof value === 'object' && value !== null
        ? transformRequestBody(value)
        : value;
  }

  return transformed;
}
