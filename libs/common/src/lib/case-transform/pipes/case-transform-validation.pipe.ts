import { ArgumentMetadata, Inject, Injectable, Optional, PipeTransform, ValidationPipe } from '@nestjs/common';
import { CaseTransformConfig } from '../interfaces/case-transform-config.interface';
import { transformRequestBody } from '../utils/case-transform.util';

/**
 * Custom validation pipe that transforms snake_case input to camelCase
 * before validation and then validates using the standard ValidationPipe
 */
@Injectable()
export class CaseTransformValidationPipe implements PipeTransform {
  private readonly validationPipe: ValidationPipe;
  private readonly config: CaseTransformConfig;

  constructor(
    @Optional()
    @Inject('CASE_TRANSFORM_CONFIG')
    config?: Partial<CaseTransformConfig>,
  ) {
    this.config = {
      transformResponses: true,
      transformRequests: true,
      transformGrpc: false,
      excludeFields: [],
      preserveDates: true,
      ...config,
    };

    this.validationPipe = new ValidationPipe({
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
      whitelist: true,
      forbidNonWhitelisted: true,
    });
  }

  async transform(value: unknown, metadata: ArgumentMetadata): Promise<unknown> {
    if (this.config.transformRequests && value && typeof value === 'object' && metadata.type === 'body') {
      // Transform snake_case to camelCase for both body and query
      const transformedValue = transformRequestBody(value);
      return this.validationPipe.transform(transformedValue, metadata);
    }

    // For other types (e.g., param, custom), use default behavior
    return this.validationPipe.transform(value, metadata);
  }
}
