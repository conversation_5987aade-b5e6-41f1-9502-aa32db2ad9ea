import { Module } from '@nestjs/common';
import { CaseTransformInterceptor } from './interceptors/case-transform.interceptor';
import { CaseTransformValidationPipe } from './pipes/case-transform-validation.pipe';

/**
 * Case transformation module that provides utilities for converting between
 * camelCase (internal) and snake_case (external API) formats
 */
@Module({
  providers: [
    CaseTransformInterceptor,
    CaseTransformValidationPipe,
  ],
  exports: [
    CaseTransformInterceptor,
    CaseTransformValidationPipe,
  ],
})
export class CaseTransformModule {}
