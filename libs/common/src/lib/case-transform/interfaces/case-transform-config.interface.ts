/**
 * Configuration options for case transformation
 */
export interface CaseTransformConfig {
  /**
   * Whether to transform HTTP responses from camelCase to snake_case
   * @default true
   */
  transformResponses?: boolean;

  /**
   * Whether to transform HTTP requests from snake_case to camelCase
   * @default true
   */
  transformRequests?: boolean;

  /**
   * Whether to apply transformation to gRPC contexts
   * @default false
   */
  transformGrpc?: boolean;

  /**
   * List of field names to exclude from transformation
   * @default []
   */
  excludeFields?: string[];

  /**
   * Whether to preserve Date objects during transformation
   * @default true
   */
  preserveDates?: boolean;
}
