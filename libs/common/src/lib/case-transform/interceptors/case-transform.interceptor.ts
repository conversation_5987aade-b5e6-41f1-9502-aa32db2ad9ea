/* eslint-disable @typescript-eslint/no-explicit-any */
import {
    <PERSON><PERSON><PERSON><PERSON>,
    ExecutionContext,
    Inject,
    Injectable,
    NestInterceptor,
    Optional,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { CaseTransformConfig } from '../interfaces/case-transform-config.interface';

/**
 * Global interceptor that transforms response data from camelCase to snake_case
 * for external API consistency while maintaining camelCase internally
 */
@Injectable()
export class CaseTransformInterceptor implements NestInterceptor {
  private readonly config: CaseTransformConfig;

  constructor(
    @Optional()
    @Inject('CASE_TRANSFORM_CONFIG')
    config?: Partial<CaseTransformConfig>
  ) {
    this.config = {
      transformResponses: true,
      transformRequests: true,
      transformGrpc: false,
      excludeFields: [],
      preserveDates: true,
      ...config,
    };
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map((data) => {
        // Check if transformation is enabled for responses
        if (!this.config.transformResponses) {
          return data;
        }

        // Only transform HTTP responses by default, unless gRPC is enabled
        const contextType = context.getType();
        if (contextType === 'http' || (contextType === 'rpc' && this.config.transformGrpc)) {
          return this.transformToSnakeCase(data);
        }
        return data;
      })
    );
  }

  /**
   * Recursively transforms object keys from camelCase to snake_case
   */
  private transformToSnakeCase(obj: any): any {
    if (obj === null || obj === undefined) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map((item) => this.transformToSnakeCase(item));
    }

    if (obj instanceof Date && this.config.preserveDates) {
      return obj;
    }

    if (typeof obj === 'object' && obj.constructor === Object) {
      const transformed: any = {};

      for (const [key, value] of Object.entries(obj)) {
        // Skip transformation for excluded fields
        if (this.config.excludeFields?.includes(key)) {
          transformed[key] = value;
        } else {
          const snakeKey = this.camelToSnakeCase(key);
          transformed[snakeKey] = this.transformToSnakeCase(value);
        }
      }

      return transformed;
    }

    return obj;
  }

  /**
   * Converts camelCase string to snake_case
   */
  private camelToSnakeCase(str: string): string {
    return str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);
  }
}
