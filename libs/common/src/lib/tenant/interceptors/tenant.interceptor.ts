import { BadRequestException, <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, Logger, NestInterceptor, UnauthorizedException } from '@nestjs/common';
import {
  DEFAULT_TENANT_CONFIG,
  TenantAwareRequest,
  TenantDecoratorMetadata,
  TenantExtractionErrorCode,
  TenantExtractionResult,
  TenantExtractionSource,
  TenantInfo,
  TenantInterceptorConfig,
  TenantValidationOptions,
} from '../interfaces/tenant.interface';

import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { TenantStatus } from '../interfaces/tenant.interface';
import { TenantService } from '../services/tenant.service';

/**
 * Metadata key for tenant decorator configuration
 */
export const TENANT_METADATA_KEY = 'tenant_metadata';

/**
 * Interceptor that extracts and validates tenant information from requests
 * Makes tenant information available in the request context for controllers
 */
@Injectable()
export class TenantInterceptor implements NestInterceptor {
  private readonly logger = new Logger(TenantInterceptor.name);
  private readonly config: TenantInterceptorConfig;

  constructor(private readonly tenantService: TenantService, private readonly reflector: Reflector, config?: Partial<TenantInterceptorConfig>) {
    // Merge provided config with defaults and environment variables
    this.config = this.buildConfig(config);

    if (this.config.enableDebugLogging) {
      this.logger.debug('Tenant interceptor initialized with config:', this.config);
    }
  }

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    // Skip if interceptor is disabled
    if (!this.config.enabled) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest<TenantAwareRequest>();

    // Get tenant metadata from decorator (if any)
    const tenantMetadata = this.reflector.get<TenantDecoratorMetadata>(TENANT_METADATA_KEY, context.getHandler());

    try {
      // Extract tenant information from request
      const extractionResult = await this.extractTenantFromRequest(request);

      if (extractionResult.success && extractionResult.tenant) {
        // Add tenant information to request
        request.tenant = extractionResult.tenant;
        request.tenantExtraction = {
          source: extractionResult.source!,
          extractedAt: new Date(),
          validatedAt: this.config.validateTenant ? new Date() : undefined,
        };

        if (this.config.enableDebugLogging) {
          this.logger.debug(`Tenant extracted successfully: ${extractionResult.tenant.code} from ${extractionResult.source}`);
        }
      } else {
        // Handle tenant extraction failure
        const isRequired = this.config.requireTenant || tenantMetadata?.required;

        if (isRequired) {
          const errorMessage = tenantMetadata?.errorMessage || `Tenant information is required. ${extractionResult.error || 'No tenant found in request'}`;

          this.logger.warn(`Tenant required but not found: ${extractionResult.error}`);
          throw new UnauthorizedException(errorMessage);
        }

        if (this.config.enableDebugLogging) {
          this.logger.debug(`Tenant not found but not required: ${extractionResult.error}`);
        }
      }

      // Validate tenant status if metadata specifies requirements
      if (request.tenant && tenantMetadata?.validateStatus) {
        this.validateTenantStatus(request.tenant, tenantMetadata);
      }
    } catch (error) {
      if (error instanceof UnauthorizedException || error instanceof BadRequestException) {
        throw error;
      }

      this.logger.error('Error in tenant interceptor:', error);

      // If tenant is required, throw error; otherwise continue
      if (this.config.requireTenant || tenantMetadata?.required) {
        throw new UnauthorizedException('Failed to process tenant information');
      }
    }

    return next.handle();
  }

  /**
   * Extract tenant information from request using configured sources
   */
  private async extractTenantFromRequest(request: TenantAwareRequest): Promise<TenantExtractionResult> {
    for (const source of this.config.extractionSources) {
      try {
        const result = await this.extractFromSource(request, source);
        if (result.success) {
          return result;
        }
      } catch (error) {
        this.logger.warn(`Failed to extract tenant from ${source}:`, error);
      }
    }

    return {
      success: false,
      error: 'No tenant information found in any configured source',
      errorCode: TenantExtractionErrorCode.TENANT_NOT_FOUND,
    };
  }

  /**
   * Extract tenant from a specific source
   */
  private async extractFromSource(request: TenantAwareRequest, source: TenantExtractionSource): Promise<TenantExtractionResult> {
    let tenantCode: string | undefined;
    let tenantId: string | undefined;

    switch (source) {
      case TenantExtractionSource.HEADER:
        tenantCode = request.headers[this.config.tenantCodeHeader] as string;
        tenantId = request.headers[this.config.tenantIdHeader] as string;

        if (this.config.enableDebugLogging) {
          this.logger.debug(`Extracting from headers: ${this.config.tenantCodeHeader}=${tenantCode}, ${this.config.tenantIdHeader}=${tenantId}`);
        }

        // Handle duplicate headers (Express concatenates with commas)
        if (tenantCode && tenantCode.includes(',')) {
          const parts = tenantCode.split(',').map((part) => part.trim());
          tenantCode = parts[0]; // Take the first part
          this.logger.warn(`Duplicate tenant code header detected: "${request.headers[this.config.tenantCodeHeader]}" -> using: "${tenantCode}"`);
        }
        if (tenantId && tenantId.includes(',')) {
          const parts = tenantId.split(',').map((part) => part.trim());
          tenantId = parts[0]; // Take the first part
          this.logger.warn(`Duplicate tenant ID header detected: "${request.headers[this.config.tenantIdHeader]}" -> using: "${tenantId}"`);
        }
        break;

      case TenantExtractionSource.QUERY_PARAM:
        tenantCode = request.query[this.config.tenantCodeQueryParam] as string;
        tenantId = request.query[this.config.tenantIdQueryParam] as string;
        break;

      case TenantExtractionSource.JWT_CLAIMS: {
        // Try to extract from existing JWT context (if available)
        const jwtPayload = (request as any).jwtPayload;
        if (jwtPayload) {
          tenantCode = jwtPayload[this.config.jwtTenantCodeClaim];
          tenantId = jwtPayload[this.config.jwtTenantIdClaim];
        }
        break;
      }

      case TenantExtractionSource.PATH:
        // Extract from subdomain or path (implementation depends on routing strategy)
        tenantCode = this.extractTenantFromPath(request);
        break;

      case TenantExtractionSource.DEFAULT:
        tenantCode = this.config.defaultTenantCode;
        break;

      default:
        return {
          success: false,
          error: `Unsupported extraction source: ${source}`,
          errorCode: TenantExtractionErrorCode.INTERNAL_ERROR,
        };
    }

    // Validate extracted values
    if (!tenantCode && !tenantId) {
      return {
        success: false,
        error: `No tenant information found in ${source}`,
        errorCode: TenantExtractionErrorCode.TENANT_NOT_FOUND,
      };
    }

    // Fetch tenant information
    let tenant: TenantInfo | null = null;

    if (this.config.validateTenant) {
      const validationOptions: Partial<TenantValidationOptions> = {
        checkStatus: true,
        allowedStatuses: [TenantStatus.ACTIVE, TenantStatus.TRIAL],
        forceFresh: false,
        timeoutMs: 3000,
      };

      if (tenantCode) {
        tenant = await this.tenantService.getTenantByCode(tenantCode, validationOptions);
      } else if (tenantId) {
        tenant = await this.tenantService.getTenantById(tenantId, validationOptions);
      }

      if (!tenant) {
        return {
          success: false,
          error: `Tenant validation failed for ${tenantCode || tenantId}`,
          errorCode: TenantExtractionErrorCode.TENANT_VALIDATION_FAILED,
        };
      }
    } else {
      // Create minimal tenant info without validation
      tenant = {
        id: tenantId || '',
        code: tenantCode || '',
        name: tenantCode || tenantId || 'Unknown',
        status: TenantStatus.ACTIVE,
      };
    }

    return {
      success: true,
      tenant,
      source,
    };
  }

  /**
   * Extract tenant code from request path or subdomain
   */
  private extractTenantFromPath(request: TenantAwareRequest): string | undefined {
    // Example implementation for subdomain extraction
    const host = request.headers.host;
    if (host) {
      const parts = host.split('.');
      if (parts.length > 2) {
        // Assume first part is tenant code (e.g., tenant1.api.example.com)
        return parts[0];
      }
    }

    // Example implementation for path extraction
    const pathSegments = request.path.split('/').filter(Boolean);
    if (pathSegments.length > 0 && pathSegments[0] !== 'api') {
      // Assume first path segment is tenant code (e.g., /tenant1/api/...)
      return pathSegments[0];
    }

    return undefined;
  }

  /**
   * Validate tenant status against decorator requirements
   */
  private validateTenantStatus(tenant: TenantInfo, metadata: TenantDecoratorMetadata): void {
    if (!metadata.allowedStatuses || metadata.allowedStatuses.length === 0) {
      return;
    }

    if (!metadata.allowedStatuses.includes(tenant.status)) {
      const allowedStatusNames = metadata.allowedStatuses.map((status) => (TenantStatus as any)[status] || status).join(', ');

      throw new BadRequestException(
        `Tenant ${tenant.code} has invalid status ${(TenantStatus as any)[tenant.status] || tenant.status}. ` + `Allowed statuses: ${allowedStatusNames}`,
      );
    }
  }

  /**
   * Build configuration from defaults, environment variables, and provided config
   */
  private buildConfig(providedConfig?: Partial<TenantInterceptorConfig>): TenantInterceptorConfig {
    const envConfig: Partial<TenantInterceptorConfig> = {
      enabled: process.env['TENANT_INTERCEPTOR_ENABLED'] !== 'false',
      validateTenant: process.env['TENANT_INTERCEPTOR_VALIDATE'] !== 'false',
      requireTenant: process.env['TENANT_INTERCEPTOR_REQUIRE'] === 'true',
      enableCache: process.env['TENANT_INTERCEPTOR_CACHE'] !== 'false',
      cacheTtlSeconds: parseInt(process.env['TENANT_INTERCEPTOR_CACHE_TTL'] || '300', 10),
      tenantCodeHeader: process.env['TENANT_CODE_HEADER'] || DEFAULT_TENANT_CONFIG.tenantCodeHeader,
      tenantIdHeader: process.env['TENANT_ID_HEADER'] || DEFAULT_TENANT_CONFIG.tenantIdHeader,
      tenantCodeQueryParam: process.env['TENANT_CODE_QUERY_PARAM'] || DEFAULT_TENANT_CONFIG.tenantCodeQueryParam,
      tenantIdQueryParam: process.env['TENANT_ID_QUERY_PARAM'] || DEFAULT_TENANT_CONFIG.tenantIdQueryParam,
      defaultTenantCode: '',
      enableDebugLogging: process.env['TENANT_INTERCEPTOR_DEBUG'] === 'true',
    };

    return {
      ...DEFAULT_TENANT_CONFIG,
      ...envConfig,
      ...providedConfig,
    };
  }
}
