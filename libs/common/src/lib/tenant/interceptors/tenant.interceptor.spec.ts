import { TenantTenantStatus as TenantStatus } from '@qeep/proto';
import { BadRequestException, CallHandler, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Test, TestingModule } from '@nestjs/testing';
import { of } from 'rxjs';
import {
    TenantAwareRequest,
    TenantDecoratorMetadata,
    TenantExtractionSource,
    TenantInfo,
} from '../interfaces/tenant.interface';
import { TenantService } from '../services/tenant.service';
import { TenantInterceptor } from './tenant.interceptor';

describe('TenantInterceptor', () => {
  let interceptor: TenantInterceptor;
  let tenantService: jest.Mocked<TenantService>;
  let reflector: jest.Mocked<Reflector>;
  let executionContext: jest.Mocked<ExecutionContext>;
  let callHandler: jest.Mocked<CallHandler>;
  let mockRequest: Partial<TenantAwareRequest>;

  const mockTenantInfo: TenantInfo = {
    id: 'test-tenant-id',
    code: 'test-tenant',
    name: 'Test Tenant',
    status: TenantStatus.TENANT_STATUS_ACTIVE,
  };

  beforeEach(async () => {
    tenantService = {
      getTenantByCode: jest.fn(),
      getTenantById: jest.fn(),
      validateTenantCode: jest.fn(),
      clearCache: jest.fn(),
      getCacheStats: jest.fn(),
      onModuleInit: jest.fn(),
    } as any;

    reflector = {
      get: jest.fn(),
    } as any;

    mockRequest = {
      headers: {},
      query: {},
      path: '/api/test',
    };

    const httpContext = {
      getRequest: jest.fn().mockReturnValue(mockRequest),
      getResponse: jest.fn(),
    };

    executionContext = {
      switchToHttp: jest.fn().mockReturnValue(httpContext),
      getHandler: jest.fn(),
      getClass: jest.fn(),
      getType: jest.fn().mockReturnValue('http'),
      getArgs: jest.fn(),
      getArgByIndex: jest.fn(),
    } as any;

    callHandler = {
      handle: jest.fn().mockReturnValue(of('test response')),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: TenantService,
          useValue: tenantService,
        },
        {
          provide: Reflector,
          useValue: reflector,
        },
      ],
    }).compile();

    const tenantServiceInstance = module.get<TenantService>(TenantService);
    const reflectorInstance = module.get<Reflector>(Reflector);

    interceptor = new TenantInterceptor(tenantServiceInstance, reflectorInstance, {
      enabled: true,
      extractionSources: [TenantExtractionSource.HEADER],
      validateTenant: true,
      requireTenant: false,
      enableDebugLogging: false,
    });
  });

  describe('intercept', () => {
    it('should extract tenant from header and add to request', async () => {
      mockRequest.headers = { 'x-tenant-code': 'test-tenant' };
      tenantService.getTenantByCode.mockResolvedValue(mockTenantInfo);
      reflector.get.mockReturnValue(undefined);

      const result = await interceptor.intercept(executionContext, callHandler);

      expect(mockRequest.tenant).toEqual(mockTenantInfo);
      expect(mockRequest.tenantExtraction).toEqual({
        source: TenantExtractionSource.HEADER,
        extractedAt: expect.any(Date),
        validatedAt: expect.any(Date),
      });
      expect(tenantService.getTenantByCode).toHaveBeenCalledWith('test-tenant', expect.any(Object));
      expect(result).toBeDefined();
    });

    it('should extract tenant from query parameter', async () => {
      interceptor = new TenantInterceptor(tenantService, reflector, {
        enabled: true,
        extractionSources: [TenantExtractionSource.QUERY_PARAM],
        validateTenant: true,
        requireTenant: false,
      });

      mockRequest.query = { tenant_code: 'test-tenant' };
      tenantService.getTenantByCode.mockResolvedValue(mockTenantInfo);
      reflector.get.mockReturnValue(undefined);

      await interceptor.intercept(executionContext, callHandler);

      expect(mockRequest.tenant).toEqual(mockTenantInfo);
      expect(tenantService.getTenantByCode).toHaveBeenCalledWith('test-tenant', expect.any(Object));
    });

    it('should extract tenant from JWT claims', async () => {
      interceptor = new TenantInterceptor(tenantService, reflector, {
        enabled: true,
        extractionSources: [TenantExtractionSource.JWT_CLAIMS],
        validateTenant: true,
        requireTenant: false,
      });

      (mockRequest as any).jwtPayload = { tenant_id: 'test-tenant-id' };
      tenantService.getTenantById.mockResolvedValue(mockTenantInfo);
      reflector.get.mockReturnValue(undefined);

      await interceptor.intercept(executionContext, callHandler);

      expect(mockRequest.tenant).toEqual(mockTenantInfo);
      expect(tenantService.getTenantById).toHaveBeenCalledWith('test-tenant-id', expect.any(Object));
    });

    it('should use default tenant when configured', async () => {
      interceptor = new TenantInterceptor(tenantService, reflector, {
        enabled: true,
        extractionSources: [TenantExtractionSource.DEFAULT],
        validateTenant: true,
        requireTenant: false,
        defaultTenantCode: 'default-tenant',
      });

      tenantService.getTenantByCode.mockResolvedValue(mockTenantInfo);
      reflector.get.mockReturnValue(undefined);

      await interceptor.intercept(executionContext, callHandler);

      expect(mockRequest.tenant).toEqual(mockTenantInfo);
      expect(tenantService.getTenantByCode).toHaveBeenCalledWith('default-tenant', expect.any(Object));
    });

    it('should throw UnauthorizedException when tenant is required but not found', async () => {
      interceptor = new TenantInterceptor(tenantService, reflector, {
        enabled: true,
        extractionSources: [TenantExtractionSource.HEADER],
        validateTenant: true,
        requireTenant: true,
      });

      mockRequest.headers = {};
      reflector.get.mockReturnValue(undefined);

      await expect(interceptor.intercept(executionContext, callHandler)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException when decorator requires tenant but not found', async () => {
      const tenantMetadata: TenantDecoratorMetadata = {
        required: true,
        errorMessage: 'Custom error message',
      };

      mockRequest.headers = {};
      reflector.get.mockReturnValue(tenantMetadata);

      await expect(interceptor.intercept(executionContext, callHandler)).rejects.toThrow(
        new UnauthorizedException('Custom error message')
      );
    });

    it('should validate tenant status when decorator specifies requirements', async () => {
      const tenantMetadata: TenantDecoratorMetadata = {
        validateStatus: true,
        allowedStatuses: [TenantStatus.TENANT_STATUS_ACTIVE],
      };

      const suspendedTenant = { ...mockTenantInfo, status: TenantStatus.TENANT_STATUS_SUSPENDED };

      mockRequest.headers = { 'x-tenant-code': 'test-tenant' };
      tenantService.getTenantByCode.mockResolvedValue(suspendedTenant);
      reflector.get.mockReturnValue(tenantMetadata);

      await expect(interceptor.intercept(executionContext, callHandler)).rejects.toThrow(BadRequestException);
    });

    it('should continue without tenant when not required', async () => {
      mockRequest.headers = {};
      reflector.get.mockReturnValue(undefined);

      const result = await interceptor.intercept(executionContext, callHandler);

      expect(mockRequest.tenant).toBeUndefined();
      expect(result).toBeDefined();
      expect(callHandler.handle).toHaveBeenCalled();
    });

    it('should skip when interceptor is disabled', async () => {
      interceptor = new TenantInterceptor(tenantService, reflector, {
        enabled: false,
      });

      const result = await interceptor.intercept(executionContext, callHandler);

      expect(mockRequest.tenant).toBeUndefined();
      expect(tenantService.getTenantByCode).not.toHaveBeenCalled();
      expect(result).toBeDefined();
    });

    it('should try multiple extraction sources in order', async () => {
      interceptor = new TenantInterceptor(tenantService, reflector, {
        enabled: true,
        extractionSources: [TenantExtractionSource.HEADER, TenantExtractionSource.QUERY_PARAM],
        validateTenant: true,
        requireTenant: false,
      });

      // No header, but query param present
      mockRequest.headers = {};
      mockRequest.query = { tenant_code: 'test-tenant' };
      tenantService.getTenantByCode.mockResolvedValue(mockTenantInfo);
      reflector.get.mockReturnValue(undefined);

      await interceptor.intercept(executionContext, callHandler);

      expect(mockRequest.tenant).toEqual(mockTenantInfo);
      expect(mockRequest.tenantExtraction?.source).toBe(TenantExtractionSource.QUERY_PARAM);
    });

    it('should handle tenant service errors gracefully', async () => {
      mockRequest.headers = { 'x-tenant-code': 'test-tenant' };
      tenantService.getTenantByCode.mockRejectedValue(new Error('Service unavailable'));
      reflector.get.mockReturnValue(undefined);

      const result = await interceptor.intercept(executionContext, callHandler);

      expect(mockRequest.tenant).toBeUndefined();
      expect(result).toBeDefined();
    });

    it('should create minimal tenant info when validation is disabled', async () => {
      interceptor = new TenantInterceptor(tenantService, reflector, {
        enabled: true,
        extractionSources: [TenantExtractionSource.HEADER],
        validateTenant: false,
        requireTenant: false,
      });

      mockRequest.headers = { 'x-tenant-code': 'test-tenant' };
      reflector.get.mockReturnValue(undefined);

      await interceptor.intercept(executionContext, callHandler);

      expect(mockRequest.tenant).toEqual({
        id: '',
        code: 'test-tenant',
        name: 'test-tenant',
        status: TenantStatus.TENANT_STATUS_ACTIVE,
      });
      expect(tenantService.getTenantByCode).not.toHaveBeenCalled();
    });
  });
});
