import { Request } from 'express';
// Temporary enum definition until module resolution is fixed
export enum TenantStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
  TRIAL = 'TRIAL',
  UNKNOWN = 'UNKNOWN',
}

interface Tenant {
  id: string;
  code: string;
  name: string;
  description?: string;
  status: TenantStatus;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Tenant information extracted from request context
 * This interface represents the tenant data available to controllers
 */
export interface TenantInfo {
  /** Unique tenant identifier */
  id: string;
  /** Tenant code (short identifier) */
  code: string;
  /** Tenant display name */
  name: string;
  /** Tenant description */
  description?: string;
  /** Current tenant status */
  status: TenantStatus;
  /** Tenant creation timestamp */
  createdAt?: Date;
  /** Tenant last update timestamp */
  updatedAt?: Date;
  /** Additional tenant metadata */
  metadata?: Record<string, unknown>;
}

/**
 * Configuration options for tenant extraction and validation
 */
export interface TenantInterceptorConfig {
  /** Enable/disable the tenant interceptor */
  enabled: boolean;
  /** Sources to extract tenant information from (in order of priority) */
  extractionSources: TenantExtractionSource[];
  /** Whether to validate tenant against tenant service */
  validateTenant: boolean;
  /** Whether to require tenant for all requests */
  requireTenant: boolean;
  /** Cache tenant information for performance */
  enableCache: boolean;
  /** Cache TTL in seconds */
  cacheTtlSeconds: number;
  /** Header name for tenant code */
  tenantCodeHeader: string;
  /** Header name for tenant ID */
  tenantIdHeader: string;
  /** Query parameter name for tenant code */
  tenantCodeQueryParam: string;
  /** Query parameter name for tenant ID */
  tenantIdQueryParam: string;
  /** JWT claim name for tenant ID */
  jwtTenantIdClaim: string;
  /** JWT claim name for tenant code */
  jwtTenantCodeClaim: string;
  /** Default tenant code for development/testing */
  defaultTenantCode?: string;
  /** Enable debug logging */
  enableDebugLogging: boolean;
}

/**
 * Sources for extracting tenant information from requests
 */
export enum TenantExtractionSource {
  /** Extract from HTTP headers */
  HEADER = 'header',
  /** Extract from query parameters */
  QUERY_PARAM = 'query_param',
  /** Extract from JWT token claims */
  JWT_CLAIMS = 'jwt_claims',
  /** Extract from request path/subdomain */
  PATH = 'path',
  /** Use default tenant (fallback) */
  DEFAULT = 'default',
}

/**
 * Result of tenant extraction process
 */
export interface TenantExtractionResult {
  /** Whether tenant was successfully extracted */
  success: boolean;
  /** Extracted tenant information */
  tenant?: TenantInfo;
  /** Source that provided the tenant information */
  source?: TenantExtractionSource;
  /** Error message if extraction failed */
  error?: string;
  /** Error code for programmatic handling */
  errorCode?: TenantExtractionErrorCode;
}

/**
 * Error codes for tenant extraction failures
 */
export enum TenantExtractionErrorCode {
  /** No tenant information found in request */
  TENANT_NOT_FOUND = 'TENANT_NOT_FOUND',
  /** Tenant code/ID is invalid format */
  INVALID_TENANT_FORMAT = 'INVALID_TENANT_FORMAT',
  /** Tenant validation failed against service */
  TENANT_VALIDATION_FAILED = 'TENANT_VALIDATION_FAILED',
  /** Tenant service is unavailable */
  TENANT_SERVICE_UNAVAILABLE = 'TENANT_SERVICE_UNAVAILABLE',
  /** Tenant is inactive or suspended */
  TENANT_INACTIVE = 'TENANT_INACTIVE',
  /** Multiple conflicting tenant identifiers */
  CONFLICTING_TENANT_INFO = 'CONFLICTING_TENANT_INFO',
  /** Internal error during extraction */
  INTERNAL_ERROR = 'INTERNAL_ERROR',
}

/**
 * Extended request interface with tenant information
 */
export interface TenantAwareRequest extends Request {
  /** Extracted tenant information */
  tenant?: TenantInfo;
  /** Tenant extraction metadata */
  tenantExtraction?: {
    source: TenantExtractionSource;
    extractedAt: Date;
    validatedAt?: Date;
  };
}

/**
 * Metadata for tenant decorator
 */
export interface TenantDecoratorMetadata {
  /** Whether tenant is required for this endpoint */
  required?: boolean;
  /** Custom error message if tenant is missing */
  errorMessage?: string;
  /** Whether to validate tenant status */
  validateStatus?: boolean;
  /** Allowed tenant statuses */
  allowedStatuses?: TenantStatus[];
}

/**
 * Options for tenant validation
 */
export interface TenantValidationOptions {
  /** Whether to check tenant status */
  checkStatus: boolean;
  /** Allowed statuses for validation */
  allowedStatuses: TenantStatus[];
  /** Whether to fetch fresh data from service */
  forceFresh: boolean;
  /** Timeout for validation request in milliseconds */
  timeoutMs: number;
}

/**
 * Tenant cache entry
 */
export interface TenantCacheEntry {
  /** Cached tenant information */
  tenant: TenantInfo;
  /** Cache entry creation timestamp */
  cachedAt: Date;
  /** Cache entry expiration timestamp */
  expiresAt: Date;
  /** Number of times this entry has been accessed */
  accessCount: number;
}

/**
 * Convert proto Tenant to TenantInfo
 */
export function protoTenantToTenantInfo(protoTenant: Tenant): TenantInfo {
  return {
    id: protoTenant.id,
    code: protoTenant.code,
    name: protoTenant.name,
    description: protoTenant.description,
    status: protoTenant.status,
    createdAt: protoTenant.createdAt,
    updatedAt: protoTenant.updatedAt,
  };
}

/**
 * Default tenant interceptor configuration
 */
export const DEFAULT_TENANT_CONFIG: TenantInterceptorConfig = {
  enabled: true,
  extractionSources: [TenantExtractionSource.HEADER, TenantExtractionSource.QUERY_PARAM, TenantExtractionSource.JWT_CLAIMS, TenantExtractionSource.DEFAULT],
  validateTenant: true,
  requireTenant: false,
  enableCache: true,
  cacheTtlSeconds: 300, // 5 minutes
  tenantCodeHeader: 'x-tenant-code',
  tenantIdHeader: 'x-tenant-id',
  tenantCodeQueryParam: 'tenant_code',
  tenantIdQueryParam: 'tenant_id',
  jwtTenantIdClaim: 'tenant_id',
  jwtTenantCodeClaim: 'tenant_code',
  enableDebugLogging: false,
};
