import {
    GetTenantByCodeResponse,
    GetTenantResponse,
    TenantTenant as Tenant,
    TenantServiceClient,
    TenantTenantStatus as TenantStatus,
    ValidateTenantCodeResponse,
} from '@qeep/proto';
import { ClientGrpc } from '@nestjs/microservices';
import { Test, TestingModule } from '@nestjs/testing';
import { of, throwError } from 'rxjs';
import { TenantInfo } from '../interfaces/tenant.interface';
import { TenantService } from './tenant.service';

describe('TenantService', () => {
  let service: TenantService;
  let tenantServiceClient: jest.Mocked<TenantServiceClient>;
  let clientGrpc: jest.Mocked<ClientGrpc>;

  const mockTenant: Tenant = {
    id: 'test-tenant-id',
    code: 'test-tenant',
    name: 'Test Tenant',
    description: 'Test tenant description',
    status: TenantStatus.TENANT_STATUS_ACTIVE,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-02'),
  };

  const mockTenantInfo: TenantInfo = {
    id: 'test-tenant-id',
    code: 'test-tenant',
    name: 'Test Tenant',
    description: 'Test tenant description',
    status: TenantStatus.TENANT_STATUS_ACTIVE,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-02'),
  };

  beforeEach(async () => {
    tenantServiceClient = {
      getTenantByCode: jest.fn(),
      getTenant: jest.fn(),
      validateTenantCode: jest.fn(),
      createTenant: jest.fn(),
      updateTenant: jest.fn(),
    };

    clientGrpc = {
      getService: jest.fn().mockReturnValue(tenantServiceClient),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TenantService,
        {
          provide: 'TENANT_GRPC_CLIENT',
          useValue: clientGrpc,
        },
      ],
    }).compile();

    service = module.get<TenantService>(TenantService);
    service.onModuleInit();
  });

  afterEach(() => {
    service.clearCache();
  });

  describe('getTenantByCode', () => {
    it('should return tenant info when tenant exists', async () => {
      const mockResponse: GetTenantByCodeResponse = {
        success: true,
        tenant: mockTenant,
        error: undefined,
        metadata: undefined,
      };

      tenantServiceClient.getTenantByCode.mockReturnValue(of(mockResponse));

      const result = await service.getTenantByCode('test-tenant');

      expect(result).toEqual(mockTenantInfo);
      expect(tenantServiceClient.getTenantByCode).toHaveBeenCalledWith({
        code: 'test-tenant',
        metadata: expect.objectContaining({
          requestId: expect.stringContaining('get-tenant-'),
          sourceIp: '127.0.0.1',
          userAgent: 'TenantService/1.0',
          timestamp: expect.any(Date),
        }),
      });
    });

    it('should return null when tenant does not exist', async () => {
      const mockResponse: GetTenantByCodeResponse = {
        success: false,
        tenant: undefined,
        error: {
          code: 'TENANT_NOT_FOUND',
          message: 'Tenant not found',
          details: {},
          traceId: 'trace-123',
        },
        metadata: undefined,
      };

      tenantServiceClient.getTenantByCode.mockReturnValue(of(mockResponse));

      const result = await service.getTenantByCode('non-existent');

      expect(result).toBeNull();
    });

    it('should return null when gRPC call fails', async () => {
      tenantServiceClient.getTenantByCode.mockReturnValue(
        throwError(() => new Error('gRPC connection failed'))
      );

      const result = await service.getTenantByCode('test-tenant');

      expect(result).toBeNull();
    });

    it('should return null for invalid tenant code', async () => {
      const result = await service.getTenantByCode('');
      expect(result).toBeNull();

      const result2 = await service.getTenantByCode(null as any);
      expect(result2).toBeNull();
    });

    it('should validate tenant status when checkStatus is true', async () => {
      const inactiveTenant = { ...mockTenant, status: TenantStatus.TENANT_STATUS_INACTIVE };
      const mockResponse: GetTenantByCodeResponse = {
        success: true,
        tenant: inactiveTenant,
        error: undefined,
        metadata: undefined,
      };

      tenantServiceClient.getTenantByCode.mockReturnValue(of(mockResponse));

      const result = await service.getTenantByCode('test-tenant', {
        checkStatus: true,
        allowedStatuses: [TenantStatus.TENANT_STATUS_ACTIVE],
      });

      expect(result).toBeNull();
    });

    it('should use cache when available', async () => {
      const mockResponse: GetTenantByCodeResponse = {
        success: true,
        tenant: mockTenant,
        error: undefined,
        metadata: undefined,
      };

      tenantServiceClient.getTenantByCode.mockReturnValue(of(mockResponse));

      // First call should hit the service
      const result1 = await service.getTenantByCode('test-tenant');
      expect(result1).toEqual(mockTenantInfo);
      expect(tenantServiceClient.getTenantByCode).toHaveBeenCalledTimes(1);

      // Second call should use cache
      const result2 = await service.getTenantByCode('test-tenant');
      expect(result2).toEqual(mockTenantInfo);
      expect(tenantServiceClient.getTenantByCode).toHaveBeenCalledTimes(1);
    });

    it('should bypass cache when forceFresh is true', async () => {
      const mockResponse: GetTenantByCodeResponse = {
        success: true,
        tenant: mockTenant,
        error: undefined,
        metadata: undefined,
      };

      tenantServiceClient.getTenantByCode.mockReturnValue(of(mockResponse));

      // First call
      await service.getTenantByCode('test-tenant');
      expect(tenantServiceClient.getTenantByCode).toHaveBeenCalledTimes(1);

      // Second call with forceFresh should hit the service again
      await service.getTenantByCode('test-tenant', { forceFresh: true });
      expect(tenantServiceClient.getTenantByCode).toHaveBeenCalledTimes(2);
    });
  });

  describe('getTenantById', () => {
    it('should return tenant info when tenant exists', async () => {
      const mockResponse: GetTenantResponse = {
        success: true,
        tenant: mockTenant,
        error: undefined,
        metadata: undefined,
      };

      tenantServiceClient.getTenant.mockReturnValue(of(mockResponse));

      const result = await service.getTenantById('test-tenant-id');

      expect(result).toEqual(mockTenantInfo);
      expect(tenantServiceClient.getTenant).toHaveBeenCalledWith({
        tenantId: 'test-tenant-id',
        metadata: expect.objectContaining({
          requestId: expect.stringContaining('get-tenant-'),
          sourceIp: '127.0.0.1',
          userAgent: 'TenantService/1.0',
          timestamp: expect.any(Date),
        }),
      });
    });

    it('should return null for invalid tenant ID', async () => {
      const result = await service.getTenantById('');
      expect(result).toBeNull();
    });
  });

  describe('validateTenantCode', () => {
    it('should return true for valid tenant code', async () => {
      const mockResponse: ValidateTenantCodeResponse = {
        valid: true,
        tenant: mockTenant,
        error: undefined,
        metadata: undefined,
      };

      tenantServiceClient.validateTenantCode.mockReturnValue(of(mockResponse));

      const result = await service.validateTenantCode('test-tenant');

      expect(result).toBe(true);
      expect(tenantServiceClient.validateTenantCode).toHaveBeenCalledWith({
        code: 'test-tenant',
        metadata: expect.objectContaining({
          requestId: expect.stringContaining('validate-tenant-'),
          sourceIp: '127.0.0.1',
          userAgent: 'TenantService/1.0',
          timestamp: expect.any(Date),
        }),
      });
    });

    it('should return false for invalid tenant code', async () => {
      const mockResponse: ValidateTenantCodeResponse = {
        valid: false,
        tenant: undefined,
        error: {
          code: 'TENANT_NOT_FOUND',
          message: 'Tenant not found',
          details: {},
          traceId: 'trace-123',
        },
        metadata: undefined,
      };

      tenantServiceClient.validateTenantCode.mockReturnValue(of(mockResponse));

      const result = await service.validateTenantCode('invalid-tenant');

      expect(result).toBe(false);
    });

    it('should return false when gRPC call fails', async () => {
      tenantServiceClient.validateTenantCode.mockReturnValue(
        throwError(() => new Error('gRPC connection failed'))
      );

      const result = await service.validateTenantCode('test-tenant');

      expect(result).toBe(false);
    });

    it('should return false for empty tenant code', async () => {
      const result = await service.validateTenantCode('');
      expect(result).toBe(false);
    });
  });

  describe('cache management', () => {
    it('should clear cache', async () => {
      const mockResponse: GetTenantByCodeResponse = {
        success: true,
        tenant: mockTenant,
        error: undefined,
        metadata: undefined,
      };

      tenantServiceClient.getTenantByCode.mockReturnValue(of(mockResponse));

      // Cache a tenant
      await service.getTenantByCode('test-tenant');
      expect(tenantServiceClient.getTenantByCode).toHaveBeenCalledTimes(1);

      // Clear cache
      service.clearCache();

      // Next call should hit the service again
      await service.getTenantByCode('test-tenant');
      expect(tenantServiceClient.getTenantByCode).toHaveBeenCalledTimes(2);
    });

    it('should return cache statistics', async () => {
      const mockResponse: GetTenantByCodeResponse = {
        success: true,
        tenant: mockTenant,
        error: undefined,
        metadata: undefined,
      };

      tenantServiceClient.getTenantByCode.mockReturnValue(of(mockResponse));

      // Cache a tenant
      await service.getTenantByCode('test-tenant');

      const stats = service.getCacheStats();
      expect(stats.size).toBe(1);
      expect(stats.entries).toHaveLength(1);
      expect(stats.entries[0].key).toBe('code:test-tenant');
      expect(stats.entries[0].accessCount).toBe(0);
    });
  });
});
