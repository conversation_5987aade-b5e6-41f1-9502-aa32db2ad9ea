import { Inject, Injectable, Logger } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { catchError, firstValueFrom, of, timeout } from 'rxjs';
import { TenantCacheEntry, TenantInfo, TenantValidationOptions, protoTenantToTenantInfo } from '../interfaces/tenant.interface';
// TODO: Fix types import issue - temporarily using any types until @qeep/proto is properly built
// import { GetTenantByCodeRequest, GetTenantRequest, TenantServiceClient, ValidateTenantCodeRequest, ValidateTenantCodeResponse } from '@qeep/proto';

// Temporary type definitions to allow building
type GetTenantByCodeRequest = any;
type GetTenantRequest = any;
type TenantServiceClient = any;
type ValidateTenantCodeRequest = any;
type ValidateTenantCodeResponse = any;

/**
 * Service for interacting with the tenant service via gRPC
 * Provides tenant validation, caching, and information retrieval
 */
@Injectable()
export class TenantService {
  private readonly logger = new Logger(TenantService.name);
  private tenantServiceClient!: TenantServiceClient;
  private tenantCache = new Map<string, TenantCacheEntry>();
  private readonly defaultTimeout = 5000; // 5 seconds

  constructor(@Inject('TENANT_GRPC_CLIENT') private readonly client: ClientGrpc) {}

  /**
   * Initialize the gRPC client connection
   */
  onModuleInit() {
    this.tenantServiceClient = this.client.getService<TenantServiceClient>('TenantService');
    this.logger.log('Tenant service client initialized');
  }

  /**
   * Get tenant information by tenant code
   */
  async getTenantByCode(tenantCode: string, options: Partial<TenantValidationOptions> = {}): Promise<TenantInfo | null> {
    if (!tenantCode || typeof tenantCode !== 'string') {
      this.logger.warn('Invalid tenant code provided');
      return null;
    }

    const cacheKey = `code:${tenantCode}`;

    // Check cache first (unless force fresh is requested)
    if (!options.forceFresh) {
      const cached = this.getCachedTenant(cacheKey);
      if (cached) {
        this.logger.debug(`Tenant found in cache: ${tenantCode}`);
        return cached;
      }
    }

    try {
      const request: GetTenantByCodeRequest = {
        code: tenantCode,
        metadata: {
          requestId: `get-tenant-${Date.now()}`,
          sourceIp: '127.0.0.1',
          userAgent: 'TenantService/1.0',
          timestamp: new Date(),
        },
      };

      const response = await firstValueFrom(
        this.tenantServiceClient.getTenantByCode(request).pipe(
          timeout(options.timeoutMs || this.defaultTimeout),
          catchError((error) => {
            this.logger.error(`Failed to get tenant by code: ${tenantCode}`, error);
            return of(null);
          }),
        ),
      );

      if (!response || !(response as any).success || !(response as any).tenant) {
        this.logger.warn(`Tenant not found or invalid response for code: ${tenantCode}`);
        return null;
      }

      const tenantInfo = protoTenantToTenantInfo((response as any).tenant);

      // Validate tenant status if required
      if (options.checkStatus && options.allowedStatuses) {
        if (!options.allowedStatuses.includes(tenantInfo.status)) {
          this.logger.warn(`Tenant ${tenantCode} has invalid status: ${tenantInfo.status}`);
          return null;
        }
      }

      // Cache the result
      this.cacheTenant(cacheKey, tenantInfo);

      this.logger.debug(`Successfully retrieved tenant: ${tenantCode}`);
      return tenantInfo;
    } catch (error) {
      this.logger.error(`Error getting tenant by code: ${tenantCode}`, error);
      return null;
    }
  }

  /**
   * Get tenant information by tenant ID
   */
  async getTenantById(tenantId: string, options: Partial<TenantValidationOptions> = {}): Promise<TenantInfo | null> {
    if (!tenantId || typeof tenantId !== 'string') {
      this.logger.warn('Invalid tenant ID provided');
      return null;
    }

    const cacheKey = `id:${tenantId}`;

    // Check cache first (unless force fresh is requested)
    if (!options.forceFresh) {
      const cached = this.getCachedTenant(cacheKey);
      if (cached) {
        this.logger.debug(`Tenant found in cache: ${tenantId}`);
        return cached;
      }
    }

    try {
      const request: GetTenantRequest = {
        tenantId,
        metadata: {
          requestId: `get-tenant-${Date.now()}`,
          sourceIp: '127.0.0.1',
          userAgent: 'TenantService/1.0',
          timestamp: new Date(),
        },
      };

      const response = await firstValueFrom(
        this.tenantServiceClient.getTenant(request).pipe(
          timeout(options.timeoutMs || this.defaultTimeout),
          catchError((error) => {
            this.logger.error(`Failed to get tenant by ID: ${tenantId}`, error);
            return of(null);
          }),
        ),
      );

      if (!response || !(response as any).success || !(response as any).tenant) {
        this.logger.warn(`Tenant not found or invalid response for ID: ${tenantId}`);
        return null;
      }

      const tenantInfo = protoTenantToTenantInfo((response as any).tenant);

      // Validate tenant status if required
      if (options.checkStatus && options.allowedStatuses) {
        if (!options.allowedStatuses.includes(tenantInfo.status)) {
          this.logger.warn(`Tenant ${tenantId} has invalid status: ${tenantInfo.status}`);
          return null;
        }
      }

      // Cache the result
      this.cacheTenant(cacheKey, tenantInfo);

      this.logger.debug(`Successfully retrieved tenant: ${tenantId}`);
      return tenantInfo;
    } catch (error) {
      this.logger.error(`Error getting tenant by ID: ${tenantId}`, error);
      return null;
    }
  }

  /**
   * Validate if a tenant code exists and is active
   */
  async validateTenantCode(tenantCode: string): Promise<boolean> {
    if (!tenantCode || typeof tenantCode !== 'string') {
      return false;
    }

    try {
      const request: ValidateTenantCodeRequest = {
        code: tenantCode,
        metadata: {
          requestId: `validate-tenant-${Date.now()}`,
          sourceIp: '127.0.0.1',
          userAgent: 'TenantService/1.0',
          timestamp: new Date(),
        },
      };

      const response: ValidateTenantCodeResponse = await firstValueFrom(
        this.tenantServiceClient.validateTenantCode(request).pipe(
          timeout(this.defaultTimeout),
          catchError((error) => {
            this.logger.error(`Failed to validate tenant code: ${tenantCode}`, error);
            return of({ valid: false } as ValidateTenantCodeResponse);
          }),
        ),
      );

      return response?.valid || false;
    } catch (error) {
      this.logger.error(`Error validating tenant code: ${tenantCode}`, error);
      return false;
    }
  }

  /**
   * Get cached tenant information
   */
  private getCachedTenant(cacheKey: string): TenantInfo | null {
    const entry = this.tenantCache.get(cacheKey);

    if (!entry) {
      return null;
    }

    // Check if cache entry has expired
    if (entry.expiresAt < new Date()) {
      this.tenantCache.delete(cacheKey);
      return null;
    }

    // Update access count
    entry.accessCount++;

    return entry.tenant;
  }

  /**
   * Cache tenant information
   */
  private cacheTenant(cacheKey: string, tenant: TenantInfo, ttlSeconds = 300): void {
    const now = new Date();
    const expiresAt = new Date(now.getTime() + ttlSeconds * 1000);

    const entry: TenantCacheEntry = {
      tenant,
      cachedAt: now,
      expiresAt,
      accessCount: 0,
    };

    this.tenantCache.set(cacheKey, entry);

    // Clean up expired entries periodically
    this.cleanupExpiredCache();
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupExpiredCache(): void {
    const now = new Date();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.tenantCache.entries()) {
      if (entry.expiresAt < now) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach((key) => this.tenantCache.delete(key));

    if (expiredKeys.length > 0) {
      this.logger.debug(`Cleaned up ${expiredKeys.length} expired cache entries`);
    }
  }

  /**
   * Clear all cached tenant information
   */
  clearCache(): void {
    this.tenantCache.clear();
    this.logger.debug('Tenant cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; entries: Array<{ key: string; accessCount: number; expiresAt: Date }> } {
    const entries = Array.from(this.tenantCache.entries()).map(([key, entry]) => ({
      key,
      accessCount: entry.accessCount,
      expiresAt: entry.expiresAt,
    }));

    return {
      size: this.tenantCache.size,
      entries,
    };
  }
}
