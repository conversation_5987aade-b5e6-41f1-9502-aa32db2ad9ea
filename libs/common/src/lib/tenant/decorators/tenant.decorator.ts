import { createParamDecorator, ExecutionContext, SetMetadata } from '@nestjs/common';
import { TENANT_METADATA_KEY } from '../interceptors/tenant.interceptor';
import { TenantAwareRequest, TenantDecoratorMetadata, TenantInfo } from '../interfaces/tenant.interface';

// Import TenantStatus from interfaces to avoid conflicts
import type { TenantStatus } from '../interfaces/tenant.interface';

// Create a const object for accessing enum values
const TenantStatusValues = {
  ACTIVE: 'ACTIVE' as TenantStatus,
  INACTIVE: 'INACTIVE' as TenantStatus,
  SUSPENDED: 'SUSPENDED' as TenantStatus,
  TRIAL: 'TRIAL' as TenantStatus,
  UNKNOWN: 'UNKNOWN' as TenantStatus,
};

/**
 * Parameter decorator to extract tenant information from request context
 *
 * Usage examples:
 * - @Tenant() tenant: TenantInfo - Get full tenant object
 * - @Tenant('code') tenantCode: string - Get specific tenant property
 * - @Tenant() tenant: TenantInfo | undefined - Optional tenant (when not required)
 */
export const Tenant = createParamDecorator((data: keyof TenantInfo | undefined, ctx: ExecutionContext): TenantInfo | string | Date | Record<string, unknown> | undefined => {
  const request = ctx.switchToHttp().getRequest<TenantAwareRequest>();
  const tenant = request.tenant;

  if (!tenant) {
    return undefined;
  }

  // If specific property is requested, return that property
  if (data) {
    return tenant[data];
  }

  // Return full tenant object
  return tenant;
});

/**
 * Parameter decorator to extract tenant ID from request context
 *
 * Usage: @TenantId() tenantId: string
 */
export const TenantId = createParamDecorator((_data: unknown, ctx: ExecutionContext): string | undefined => {
  const request = ctx.switchToHttp().getRequest<TenantAwareRequest>();
  return request.tenant?.id;
});

/**
 * Parameter decorator to extract tenant code from request context
 *
 * Usage: @TenantCode() tenantCode: string
 */
export const TenantCode = createParamDecorator((_data: unknown, ctx: ExecutionContext): string | undefined => {
  const request = ctx.switchToHttp().getRequest<TenantAwareRequest>();
  return request.tenant?.code;
});

/**
 * Parameter decorator to extract tenant name from request context
 *
 * Usage: @TenantName() tenantName: string
 */
export const TenantName = createParamDecorator((_data: unknown, ctx: ExecutionContext): string | undefined => {
  const request = ctx.switchToHttp().getRequest<TenantAwareRequest>();
  return request.tenant?.name;
});

/**
 * Parameter decorator to extract tenant status from request context
 *
 * Usage: @TenantStatus() status: TenantStatus
 */
export const TenantStatusDecorator = createParamDecorator((_data: unknown, ctx: ExecutionContext): TenantStatus | undefined => {
  const request = ctx.switchToHttp().getRequest<TenantAwareRequest>();
  return request.tenant?.status;
});

/**
 * Method decorator to require tenant for a specific endpoint
 *
 * Usage: @RequireTenant()
 */
export const RequireTenant = (options?: { errorMessage?: string; validateStatus?: boolean; allowedStatuses?: TenantStatus[] }) => {
  const metadata: TenantDecoratorMetadata = {
    required: true,
    errorMessage: options?.errorMessage,
    validateStatus: options?.validateStatus || false,
    allowedStatuses: options?.allowedStatuses,
  };

  return SetMetadata(TENANT_METADATA_KEY, metadata);
};

/**
 * Method decorator to make tenant optional for a specific endpoint
 * (overrides global requireTenant setting)
 *
 * Usage: @OptionalTenant()
 */
export const OptionalTenant = () => {
  const metadata: TenantDecoratorMetadata = {
    required: false,
  };

  return SetMetadata(TENANT_METADATA_KEY, metadata);
};

/**
 * Method decorator to require specific tenant statuses
 *
 * Usage: @RequireTenantStatus(TenantStatusValues.ACTIVE)
 */
export const RequireTenantStatus = (...allowedStatuses: TenantStatus[]) => {
  const metadata: TenantDecoratorMetadata = {
    validateStatus: true,
    allowedStatuses,
  };

  return SetMetadata(TENANT_METADATA_KEY, metadata);
};

/**
 * Method decorator to require active tenant
 *
 * Usage: @RequireActiveTenant()
 */
export const RequireActiveTenant = (errorMessage?: string) => {
  const metadata: TenantDecoratorMetadata = {
    required: true,
    validateStatus: true,
    allowedStatuses: [TenantStatusValues.ACTIVE],
    errorMessage: errorMessage || 'This endpoint requires an active tenant',
  };

  return SetMetadata(TENANT_METADATA_KEY, metadata);
};

/**
 * Method decorator to allow trial tenants
 *
 * Usage: @AllowTrialTenant()
 */
export const AllowTrialTenant = () => {
  const metadata: TenantDecoratorMetadata = {
    validateStatus: true,
    allowedStatuses: [TenantStatusValues.ACTIVE, TenantStatusValues.TRIAL],
  };

  return SetMetadata(TENANT_METADATA_KEY, metadata);
};

/**
 * Utility function to check if tenant has specific status
 *
 * Usage in controller:
 * ```typescript
 * @Get('example')
 * async example(@Tenant() tenant: TenantInfo) {
 *   if (isTenantStatus(tenant, TenantStatusValues.TRIAL)) {
 *     // Handle trial tenant logic
 *   }
 * }
 * ```
 */
export function isTenantStatus(tenant: TenantInfo | undefined, status: TenantStatus): boolean {
  return tenant?.status === status;
}

/**
 * Utility function to check if tenant is active
 *
 * Usage: if (isTenantActive(tenant)) { ... }
 */
export function isTenantActive(tenant: TenantInfo | undefined): boolean {
  return tenant?.status === TenantStatusValues.ACTIVE;
}

/**
 * Utility function to check if tenant is trial
 *
 * Usage: if (isTenantTrial(tenant)) { ... }
 */
export function isTenantTrial(tenant: TenantInfo | undefined): boolean {
  return tenant?.status === TenantStatusValues.TRIAL;
}

/**
 * Utility function to check if tenant is suspended
 *
 * Usage: if (isTenantSuspended(tenant)) { ... }
 */
export function isTenantSuspended(tenant: TenantInfo | undefined): boolean {
  return tenant?.status === TenantStatusValues.SUSPENDED;
}

/**
 * Utility function to get tenant status name
 *
 * Usage: const statusName = getTenantStatusName(tenant);
 */
export function getTenantStatusName(tenant: TenantInfo | undefined): string {
  if (!tenant) {
    return 'Unknown';
  }

  switch (tenant.status) {
    case TenantStatusValues.ACTIVE:
      return 'Active';
    case TenantStatusValues.INACTIVE:
      return 'Inactive';
    case TenantStatusValues.SUSPENDED:
      return 'Suspended';
    case TenantStatusValues.TRIAL:
      return 'Trial';
    case TenantStatusValues.UNKNOWN:
    default:
      return 'Unknown';
  }
}
