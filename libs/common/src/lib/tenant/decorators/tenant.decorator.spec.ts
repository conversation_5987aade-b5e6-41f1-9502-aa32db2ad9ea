import { TenantTenantStatus as TenantStatus } from '@qeep/proto';
import { TenantInfo } from '../interfaces/tenant.interface';
import {
    getTenantStatusName,
    isTenantActive,
    isTenantStatus,
    isTenantSuspended,
    isTenantTrial,
} from './tenant.decorator';

describe('Tenant Decorator Utilities', () => {
  const mockTenantInfo: TenantInfo = {
    id: 'test-tenant-id',
    code: 'test-tenant',
    name: 'Test Tenant',
    description: 'Test tenant description',
    status: TenantStatus.TENANT_STATUS_ACTIVE,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-02'),
  };



  describe('Utility functions', () => {
    describe('isTenantStatus', () => {
      it('should return true when tenant has specified status', () => {
        expect(isTenantStatus(mockTenantInfo, TenantStatus.TENANT_STATUS_ACTIVE)).toBe(true);
        expect(isTenantStatus(mockTenantInfo, TenantStatus.TENANT_STATUS_INACTIVE)).toBe(false);
      });

      it('should return false when tenant is undefined', () => {
        expect(isTenantStatus(undefined, TenantStatus.TENANT_STATUS_ACTIVE)).toBe(false);
      });
    });

    describe('isTenantActive', () => {
      it('should return true for active tenant', () => {
        expect(isTenantActive(mockTenantInfo)).toBe(true);
      });

      it('should return false for non-active tenant', () => {
        const inactiveTenant = { ...mockTenantInfo, status: TenantStatus.TENANT_STATUS_INACTIVE };
        expect(isTenantActive(inactiveTenant)).toBe(false);
      });

      it('should return false when tenant is undefined', () => {
        expect(isTenantActive(undefined)).toBe(false);
      });
    });

    describe('isTenantTrial', () => {
      it('should return true for trial tenant', () => {
        const trialTenant = { ...mockTenantInfo, status: TenantStatus.TENANT_STATUS_TRIAL };
        expect(isTenantTrial(trialTenant)).toBe(true);
      });

      it('should return false for non-trial tenant', () => {
        expect(isTenantTrial(mockTenantInfo)).toBe(false);
      });

      it('should return false when tenant is undefined', () => {
        expect(isTenantTrial(undefined)).toBe(false);
      });
    });

    describe('isTenantSuspended', () => {
      it('should return true for suspended tenant', () => {
        const suspendedTenant = { ...mockTenantInfo, status: TenantStatus.TENANT_STATUS_SUSPENDED };
        expect(isTenantSuspended(suspendedTenant)).toBe(true);
      });

      it('should return false for non-suspended tenant', () => {
        expect(isTenantSuspended(mockTenantInfo)).toBe(false);
      });

      it('should return false when tenant is undefined', () => {
        expect(isTenantSuspended(undefined)).toBe(false);
      });
    });

    describe('getTenantStatusName', () => {
      it('should return correct status names', () => {
        expect(getTenantStatusName(mockTenantInfo)).toBe('Active');

        const inactiveTenant = { ...mockTenantInfo, status: TenantStatus.TENANT_STATUS_INACTIVE };
        expect(getTenantStatusName(inactiveTenant)).toBe('Inactive');

        const suspendedTenant = { ...mockTenantInfo, status: TenantStatus.TENANT_STATUS_SUSPENDED };
        expect(getTenantStatusName(suspendedTenant)).toBe('Suspended');

        const trialTenant = { ...mockTenantInfo, status: TenantStatus.TENANT_STATUS_TRIAL };
        expect(getTenantStatusName(trialTenant)).toBe('Trial');

        const unknownTenant = { ...mockTenantInfo, status: TenantStatus.TENANT_STATUS_UNKNOWN };
        expect(getTenantStatusName(unknownTenant)).toBe('Unknown');
      });

      it('should return "Unknown" when tenant is undefined', () => {
        expect(getTenantStatusName(undefined)).toBe('Unknown');
      });
    });
  });
});
