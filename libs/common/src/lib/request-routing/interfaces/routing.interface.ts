import { Request, Response } from 'express';
import { RouteConfig } from './route-config.interface';

/**
 * Route match result
 */
export interface RouteMatch {
  /** Matched route configuration */
  route: RouteConfig;
  
  /** Extracted parameters from URL */
  params: Record<string, string>;
  
  /** Query parameters */
  query: Record<string, any>;
  
  /** Request body */
  body: any;
  
  /** Request headers */
  headers: Record<string, string>;
}

/**
 * Routing context for request processing
 */
export interface RoutingContext {
  /** Original HTTP request */
  request: Request;
  
  /** HTTP response object */
  response: Response;
  
  /** Matched route information */
  match: RouteMatch;
  
  /** Correlation ID for tracking */
  correlationId: string;
  
  /** User context (if authenticated) */
  user?: {
    id: string;
    email: string;
    tenantId: string;
    roles: string[];
  };
  
  /** Tenant context */
  tenant?: {
    id: string;
    code: string;
    name: string;
  };
  
  /** Request metadata */
  metadata: {
    startTime: Date;
    sourceIp: string;
    userAgent: string;
    requestId: string;
  };
}

/**
 * Service for handling request routing
 */
export interface IRoutingService {
  /**
   * Find matching route for a request
   */
  findRoute(method: string, path: string): RouteMatch | null;
  
  /**
   * Process a request through the routing pipeline
   */
  processRequest(context: RoutingContext): Promise<any>;
  
  /**
   * Get all registered routes
   */
  getRoutes(): RouteConfig[];
  
  /**
   * Register a new route
   */
  registerRoute(route: RouteConfig): void;
  
  /**
   * Remove a route
   */
  unregisterRoute(method: string, path: string): boolean;
}

/**
 * Route matcher interface
 */
export interface IRouteMatcher {
  /**
   * Check if a path matches a route pattern
   */
  match(pattern: string, path: string): { matches: boolean; params: Record<string, string> };
  
  /**
   * Compile a route pattern for faster matching
   */
  compile(pattern: string): any;
}

/**
 * Request transformer interface
 */
export interface IRequestTransformer {
  /**
   * Transform HTTP request to gRPC request
   */
  transformRequest(context: RoutingContext): any;
  
  /**
   * Transform gRPC response to HTTP response
   */
  transformResponse(grpcResponse: any, context: RoutingContext): any;
}
