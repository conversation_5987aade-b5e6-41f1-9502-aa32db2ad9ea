/**
 * HTTP methods supported by the routing system
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';

/**
 * Configuration for a single route
 */
export interface RouteConfig {
  /** HTTP method for this route */
  method: HttpMethod;
  
  /** URL pattern to match (supports parameters like :id) */
  path: string;
  
  /** Target service to route to */
  service: string;
  
  /** Target gRPC method to call */
  grpcMethod: string;
  
  /** Whether authentication is required */
  requiresAuth?: boolean;
  
  /** Custom rate limiting configuration */
  rateLimit?: {
    windowMs: number;
    maxRequests: number;
  };
  
  /** Request transformation configuration */
  transformation?: {
    /** Map HTTP request parameters to gRPC request fields */
    requestMapping?: Record<string, string>;
    
    /** Map gRPC response fields to HTTP response fields */
    responseMapping?: Record<string, string>;
    
    /** Custom transformation function name */
    customTransformer?: string;
  };
  
  /** Additional metadata */
  metadata?: Record<string, any>;
}

/**
 * Service configuration for routing
 */
export interface ServiceConfig {
  /** Service name */
  name: string;
  
  /** gRPC client configuration */
  grpc: {
    /** gRPC service URL */
    url: string;
    
    /** Proto package name */
    package: string;
    
    /** Proto file path */
    protoPath: string;
    
    /** Service class name in proto */
    serviceName: string;
  };
  
  /** Health check configuration */
  health?: {
    /** Health check endpoint path */
    endpoint: string;
    
    /** Health check interval in ms */
    interval: number;
  };
  
  /** Circuit breaker configuration */
  circuitBreaker?: {
    enabled: boolean;
    failureThreshold: number;
    timeout: number;
  };
}

/**
 * Complete routing configuration
 */
export interface RoutingConfig {
  /** Available services */
  services: Record<string, ServiceConfig>;
  
  /** Route definitions */
  routes: RouteConfig[];
  
  /** Global configuration */
  global?: {
    /** Default timeout for all requests */
    timeout?: number;
    
    /** Default rate limiting */
    rateLimit?: {
      windowMs: number;
      maxRequests: number;
    };
    
    /** Enable request logging */
    enableLogging?: boolean;
    
    /** Enable metrics collection */
    enableMetrics?: boolean;
  };
}
