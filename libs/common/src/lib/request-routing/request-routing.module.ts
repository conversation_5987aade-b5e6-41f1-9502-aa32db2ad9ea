import { Module } from '@nestjs/common';
import { ConfigModule } from '../config/config.module';
import { RoutingService } from './services/routing.service';
import { RouteMatcherService } from './services/route-matcher.service';
import { RequestTransformerService } from './services/request-transformer.service';

@Module({
  imports: [ConfigModule.forRoot()],
  controllers: [],
  providers: [
    RoutingService,
    RouteMatcherService,
    RequestTransformerService,
  ],
  exports: [
    RoutingService,
    RouteMatcherService,
    RequestTransformerService,
  ],
})
export class RequestRoutingModule {}
