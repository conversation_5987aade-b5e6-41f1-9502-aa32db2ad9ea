/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable, Logger } from '@nestjs/common';
import { IRequestTransformer, RoutingContext } from '../interfaces/routing.interface';

/**
 * Service for transforming HTTP requests to gRPC requests and vice versa
 */
@Injectable()
export class RequestTransformerService implements IRequestTransformer {
  private readonly logger = new Logger(RequestTransformerService.name);

  /**
   * Transform HTTP request to gRPC request
   */
  transformRequest(context: RoutingContext): any {
    try {
      const { match, correlationId, user, tenant, metadata } = context;
      const { route, params, query, body, headers } = match;

      this.logger.debug('Transforming HTTP request to gRPC', {
        correlationId,
        route: route.path,
        grpcMethod: route.grpcMethod,
        hasBody: !!body,
        paramsCount: Object.keys(params).length,
        queryCount: Object.keys(query).length,
      });

      // Base gRPC request with metadata
      const grpcRequest: any = {
        metadata: {
          requestId: metadata.requestId,
          correlationId,
          sourceIp: metadata.sourceIp,
          userAgent: metadata.userAgent,
          timestamp: metadata.startTime,
        },
      };

      // Add user context if available
      if (user) {
        grpcRequest.metadata.userId = user.id;
        grpcRequest.metadata.userEmail = user.email;
        grpcRequest.metadata.userRoles = user.roles;
      }

      // Add tenant context if available
      if (tenant) {
        grpcRequest.metadata.tenantId = tenant.id;
        grpcRequest.metadata.tenantCode = tenant.code;
      }

      // Apply custom transformation if specified
      if (route.transformation?.customTransformer) {
        return this.applyCustomTransformation(
          route.transformation.customTransformer,
          context,
          grpcRequest
        );
      }

      // Apply default transformation based on gRPC method
      return this.applyDefaultTransformation(context, grpcRequest);
    } catch (error) {
      this.logger.error('Error transforming request', {
        correlationId: context.correlationId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Transform gRPC response to HTTP response
   */
  transformResponse(grpcResponse: any, context: RoutingContext): any {
    try {
      const { match, correlationId } = context;
      const { route } = match;

      this.logger.debug('Transforming gRPC response to HTTP', {
        correlationId,
        route: route.path,
        grpcMethod: route.grpcMethod,
        hasResponse: !!grpcResponse,
      });

      // Apply custom transformation if specified
      if (route.transformation?.customTransformer) {
        return this.applyCustomResponseTransformation(
          route.transformation.customTransformer,
          grpcResponse,
          context
        );
      }

      // Apply default response transformation
      return this.applyDefaultResponseTransformation(grpcResponse, context);
    } catch (error) {
      this.logger.error('Error transforming response', {
        correlationId: context.correlationId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Apply default request transformation
   */
  private applyDefaultTransformation(context: RoutingContext, grpcRequest: any): any {
    const { match } = context;
    const { route, params, query, body } = match;

    // Handle different gRPC methods with common patterns
    switch (route.grpcMethod.toLowerCase()) {
      case 'login':
        return {
          ...grpcRequest,
          email: body?.email,
          password: body?.password,
        };

      case 'logout':
        return {
          ...grpcRequest,
          token: this.extractTokenFromHeaders(match.headers),
        };

      case 'refreshtoken':
        return {
          ...grpcRequest,
          refreshToken: body?.refreshToken,
        };

      case 'registeruser':
        return {
          ...grpcRequest,
          email: body?.email,
          password: body?.password,
          firstName: body?.firstName,
          lastName: body?.lastName,
          tenantCode: body?.tenantCode,
        };

      case 'getuser':
        return {
          ...grpcRequest,
          id: params['id'],
        };

      case 'createuser':
        return {
          ...grpcRequest,
          email: body?.email,
          firstName: body?.firstName,
          lastName: body?.lastName,
          role: body?.role,
        };

      case 'updateuser':
        return {
          ...grpcRequest,
          id: params['id'],
          email: body?.email,
          firstName: body?.firstName,
          lastName: body?.lastName,
          role: body?.role,
        };

      case 'gettenant':
        return {
          ...grpcRequest,
          id: params['id'],
        };

      case 'createtenant':
        return {
          ...grpcRequest,
          name: body?.name,
          code: body?.code,
          description: body?.description,
        };

      case 'updatetenant':
        return {
          ...grpcRequest,
          id: params['id'],
          name: body?.name,
          description: body?.description,
        };

      default:
        // Generic transformation: merge params, query, and body
        return {
          ...grpcRequest,
          ...params,
          ...query,
          ...body,
        };
    }
  }

  /**
   * Apply default response transformation
   */
  private applyDefaultResponseTransformation(grpcResponse: any, context: RoutingContext): any {
    const { correlationId, metadata } = context;

    // Standard HTTP response format
    const httpResponse = {
      success: true,
      data: grpcResponse,
      metadata: {
        requestId: metadata.requestId,
        correlationId,
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - metadata.startTime.getTime(),
      },
    };

    // Remove internal gRPC metadata if present
    if (httpResponse.data?.metadata) {
      delete httpResponse.data.metadata;
    }

    return httpResponse;
  }

  /**
   * Apply custom transformation using registered transformer
   */
  private applyCustomTransformation(
    transformerName: string,
    context: RoutingContext,
    grpcRequest: any
  ): any {
    // This would typically look up a registered custom transformer
    // For now, log and fall back to default transformation
    this.logger.warn('Custom transformer not implemented', {
      transformerName,
      correlationId: context.correlationId,
    });

    return this.applyDefaultTransformation(context, grpcRequest);
  }

  /**
   * Apply custom response transformation
   */
  private applyCustomResponseTransformation(
    transformerName: string,
    grpcResponse: any,
    context: RoutingContext
  ): any {
    // This would typically look up a registered custom transformer
    // For now, log and fall back to default transformation
    this.logger.warn('Custom response transformer not implemented', {
      transformerName,
      correlationId: context.correlationId,
    });

    return this.applyDefaultResponseTransformation(grpcResponse, context);
  }

  /**
   * Extract JWT token from request headers
   */
  private extractTokenFromHeaders(headers: Record<string, string>): string | undefined {
    const authHeader = headers['authorization'] || headers['Authorization'];
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    return undefined;
  }
}
