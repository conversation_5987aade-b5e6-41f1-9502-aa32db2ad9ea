import { Injectable, Logger } from '@nestjs/common';
import { IRouteMatcher } from '../interfaces/routing.interface';

/**
 * Service for matching URL patterns to routes
 * Supports parameter extraction like Express.js routing
 */
@Injectable()
export class RouteMatcherService implements IRouteMatcher {
  private readonly logger = new Logger(RouteMatcherService.name);
  private readonly compiledPatterns = new Map<string, RegExp>();

  /**
   * Check if a path matches a route pattern
   * Supports parameters like :id, :userId, etc.
   */
  match(pattern: string, path: string): { matches: boolean; params: Record<string, string> } {
    try {
      const compiledPattern = this.getCompiledPattern(pattern);
      const match = path.match(compiledPattern);
      
      if (!match) {
        return { matches: false, params: {} };
      }

      // Extract parameters from the match
      const params = this.extractParams(pattern, match);
      
      this.logger.debug('Route matched', { 
        pattern,
        path,
        params,
      });

      return { matches: true, params };
    } catch (error) {
      this.logger.error('Error matching route', {
        pattern,
        path,
        error: error instanceof Error ? error.message : String(error),
      });
      return { matches: false, params: {} };
    }
  }

  /**
   * Compile a route pattern for faster matching
   */
  compile(pattern: string): RegExp {
    try {
      // Convert Express-style route pattern to regex
      // :param becomes ([^/]+)
      // :param? becomes ([^/]*)
      // * becomes (.*)
      
      let regexPattern = pattern
        // Escape special regex characters except our route syntax
        .replace(/[.+?^${}()|[\]\\]/g, '\\$&')
        // Handle optional parameters :param?
        .replace(/:([a-zA-Z_][a-zA-Z0-9_]*)\?/g, '([^/]*)')
        // Handle required parameters :param
        .replace(/:([a-zA-Z_][a-zA-Z0-9_]*)/g, '([^/]+)')
        // Handle wildcards
        .replace(/\*/g, '(.*)');

      // Ensure exact match
      regexPattern = `^${regexPattern}$`;
      
      const compiled = new RegExp(regexPattern);
      
      this.logger.debug(`'Compiled route pattern', ${JSON.stringify({
        original: pattern,
        regex: regexPattern,
      })}`);

      return compiled;
    } catch (error) {
      this.logger.error('Error compiling route pattern', {
        pattern,
        error: error instanceof Error ? error.message : String(error),
      });
      throw new Error(`Invalid route pattern: ${pattern}`);
    }
  }

  /**
   * Get compiled pattern from cache or compile new one
   */
  private getCompiledPattern(pattern: string): RegExp {
    if (!this.compiledPatterns.has(pattern)) {
      const compiled = this.compile(pattern);
      this.compiledPatterns.set(pattern, compiled);
    }
    return this.compiledPatterns.get(pattern)!;
  }

  /**
   * Extract parameter names and values from a matched route
   */
  private extractParams(pattern: string, match: RegExpMatchArray): Record<string, string> {
    const params: Record<string, string> = {};
    
    // Find all parameter names in the pattern
    const paramNames: string[] = [];
    const paramRegex = /:([a-zA-Z_][a-zA-Z0-9_]*)\??/g;
    let paramMatch;
    
    while ((paramMatch = paramRegex.exec(pattern)) !== null) {
      paramNames.push(paramMatch[1]);
    }

    // Map captured groups to parameter names
    for (let i = 0; i < paramNames.length && i + 1 < match.length; i++) {
      const paramName = paramNames[i];
      const paramValue = match[i + 1];
      
      if (paramValue !== undefined) {
        params[paramName] = decodeURIComponent(paramValue);
      }
    }

    return params;
  }

  /**
   * Clear compiled pattern cache
   */
  clearCache(): void {
    this.compiledPatterns.clear();
    this.logger.debug('Route pattern cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; patterns: string[] } {
    return {
      size: this.compiledPatterns.size,
      patterns: Array.from(this.compiledPatterns.keys()),
    };
  }
}
