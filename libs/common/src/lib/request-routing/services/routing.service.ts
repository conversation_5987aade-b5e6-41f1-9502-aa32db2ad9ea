import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '../../config/config.service';
import { RouteConfig, RoutingConfig } from '../interfaces/route-config.interface';
import { IRoutingService, RouteMatch, RoutingContext } from '../interfaces/routing.interface';
import { RouteMatcherService } from './route-matcher.service';

/**
 * Main routing service that handles request routing logic
 */
@Injectable()
export class RoutingService implements IRoutingService, OnModuleInit {
  private readonly logger = new Logger(RoutingService.name);
  private routes: RouteConfig[] = [];
  private routingConfig: RoutingConfig | null = null;

  constructor(
    private readonly configService: ConfigService,
    private readonly routeMatcher: RouteMatcherService
  ) {}

  async onModuleInit() {
    await this.loadRoutingConfiguration();
    this.logger.log(`Routing service initialized with ${this.routes.length} routes`);
  }

  /**
   * Find matching route for a request
   */
  findRoute(method: string, path: string): RouteMatch | null {
    try {
      // Normalize method to uppercase
      const normalizedMethod = method.toUpperCase();
      
      // Find matching routes
      for (const route of this.routes) {
        if (route.method.toUpperCase() !== normalizedMethod) {
          continue;
        }

        const matchResult = this.routeMatcher.match(route.path, path);
        if (matchResult.matches) {
          this.logger.debug('Route found', {
            method: normalizedMethod,
            path,
            route: route.path,
            service: route.service,
            grpcMethod: route.grpcMethod,
            params: matchResult.params,
          });

          return {
            route,
            params: matchResult.params,
            query: {},
            body: null,
            headers: {},
          };
        }
      }

      this.logger.debug('No route found', {
        method: normalizedMethod,
        path,
        availableRoutes: this.routes.map(r => `${r.method} ${r.path}`),
      });

      return null;
    } catch (error) {
      this.logger.error('Error finding route', {
        method,
        path,
        error: error instanceof Error ? error.message : String(error),
      });
      return null;
    }
  }

  /**
   * Process a request through the routing pipeline
   */
  async processRequest(context: RoutingContext): Promise<any> {
    try {
      const { match, correlationId } = context;
      
      this.logger.debug('Processing request', {
        correlationId,
        route: match.route.path,
        service: match.route.service,
        grpcMethod: match.route.grpcMethod,
      });

      // Here we would typically:
      // 1. Apply rate limiting
      // 2. Validate authentication if required
      // 3. Transform the request
      // 4. Call the target service
      // 5. Transform the response
      // 6. Return the result

      // For now, return a placeholder response
      return {
        message: 'Request processed successfully',
        route: match.route.path,
        service: match.route.service,
        method: match.route.grpcMethod,
        correlationId,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Error processing request', {
        correlationId: context.correlationId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Get all registered routes
   */
  getRoutes(): RouteConfig[] {
    return [...this.routes];
  }

  /**
   * Register a new route
   */
  registerRoute(route: RouteConfig): void {
    try {
      // Validate route configuration
      this.validateRoute(route);
      
      // Check for duplicate routes
      const existingRoute = this.routes.find(
        r => r.method === route.method && r.path === route.path
      );
      
      if (existingRoute) {
        this.logger.warn('Overriding existing route', {
          method: route.method,
          path: route.path,
          oldService: existingRoute.service,
          newService: route.service,
        });
        
        // Remove existing route
        this.unregisterRoute(route.method, route.path);
      }

      this.routes.push(route);
      
      this.logger.log(`'Route registered', ${JSON.stringify({
        method: route.method,
        path: route.path,
        service: route.service,
        grpcMethod: route.grpcMethod,
      })}`);
    } catch (error) {
      this.logger.error('Error registering route', {
        route,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Remove a route
   */
  unregisterRoute(method: string, path: string): boolean {
    const initialLength = this.routes.length;
    this.routes = this.routes.filter(
      r => !(r.method === method && r.path === path)
    );
    
    const removed = this.routes.length < initialLength;
    
    if (removed) {
      this.logger.log('Route unregistered', { method, path });
    } else {
      this.logger.warn('Route not found for removal', { method, path });
    }
    
    return removed;
  }

  /**
   * Load routing configuration from config service
   */
  private async loadRoutingConfiguration(): Promise<void> {
    try {
      // Load default routes configuration
      const defaultRoutes = this.getDefaultRoutes();
      
      // Register default routes
      for (const route of defaultRoutes) {
        this.registerRoute(route);
      }
      
      this.logger.log('Default routing configuration loaded');
    } catch (error) {
      this.logger.error('Error loading routing configuration', {
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Get default route configurations
   */
  private getDefaultRoutes(): RouteConfig[] {
    return [
      // Auth service routes
      {
        method: 'POST',
        path: '/auth/login',
        service: 'auth-service',
        grpcMethod: 'login',
        requiresAuth: false,
      },
      {
        method: 'POST',
        path: '/auth/logout',
        service: 'auth-service',
        grpcMethod: 'logout',
        requiresAuth: true,
      },
      {
        method: 'POST',
        path: '/auth/refresh',
        service: 'auth-service',
        grpcMethod: 'refreshToken',
        requiresAuth: false,
      },
      {
        method: 'POST',
        path: '/auth/register',
        service: 'auth-service',
        grpcMethod: 'registerUser',
        requiresAuth: false,
      },
      
      // User service routes
      {
        method: 'GET',
        path: '/users/:id',
        service: 'user-service',
        grpcMethod: 'getUser',
        requiresAuth: true,
      },
      {
        method: 'POST',
        path: '/users',
        service: 'user-service',
        grpcMethod: 'createUser',
        requiresAuth: true,
      },
      {
        method: 'PUT',
        path: '/users/:id',
        service: 'user-service',
        grpcMethod: 'updateUser',
        requiresAuth: true,
      },
      
      // Tenant service routes
      {
        method: 'GET',
        path: '/tenants/:id',
        service: 'tenant-service',
        grpcMethod: 'getTenant',
        requiresAuth: true,
      },
      {
        method: 'POST',
        path: '/tenants',
        service: 'tenant-service',
        grpcMethod: 'createTenant',
        requiresAuth: true,
      },
      {
        method: 'PUT',
        path: '/tenants/:id',
        service: 'tenant-service',
        grpcMethod: 'updateTenant',
        requiresAuth: true,
      },
    ];
  }

  /**
   * Validate route configuration
   */
  private validateRoute(route: RouteConfig): void {
    if (!route.method) {
      throw new Error('Route method is required');
    }
    
    if (!route.path) {
      throw new Error('Route path is required');
    }
    
    if (!route.service) {
      throw new Error('Route service is required');
    }
    
    if (!route.grpcMethod) {
      throw new Error('Route gRPC method is required');
    }
    
    // Validate path pattern
    try {
      this.routeMatcher.compile(route.path);
    } catch (error) {
      throw new Error(`Invalid route path pattern: ${route.path}`);
    }
  }
}
