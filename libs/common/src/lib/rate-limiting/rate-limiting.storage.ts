/* eslint-disable @typescript-eslint/no-unused-vars */
import { Injectable, Logger } from '@nestjs/common';
import { ThrottlerStorage } from '@nestjs/throttler';
import Redis from 'ioredis';
import { RedisConfig, ThrottlerStorageRecord } from './interfaces/rate-limiting-options.interface';

@Injectable()
export class RateLimitingStorage implements ThrottlerStorage {
  private readonly logger = new Logger(RateLimitingStorage.name);
  private redis: Redis;

  constructor(redisConfig: RedisConfig | Redis) {
    if (redisConfig instanceof Redis) {
      this.redis = redisConfig;
    } else {
      this.redis = new Redis({
        host: redisConfig.host,
        port: redisConfig.port,
        password: redisConfig.password,
        db: redisConfig.db || 0,
        keyPrefix: redisConfig.keyPrefix || 'rate-limit:',
        maxRetriesPerRequest: redisConfig.maxRetriesPerRequest || 3,
        lazyConnect: true,
      });

      this.redis.on('connect', () => {
        this.logger.log('Connected to Redis for rate limiting');
      });

      this.redis.on('error', (error) => {
        this.logger.error('Redis connection error for rate limiting:', error);
      });

      this.redis.on('ready', () => {
        this.logger.log('Redis ready for rate limiting');
      });
    }
  }

  async increment(key: string, ttl: number, _limit: number, _blockDuration: number, _throttlerName: string): Promise<ThrottlerStorageRecord> {
    try {
      const multi = this.redis.multi();
      const redisKey = this.buildKey(key);

      // Increment the counter
      multi.incr(redisKey);
      
      // Set expiration if this is the first request
      multi.expire(redisKey, Math.ceil(ttl / 1000));
      
      // Get the TTL to calculate reset time
      multi.ttl(redisKey);

      const results = await multi.exec();

      if (!results || results.some(([err]) => err)) {
        throw new Error('Redis operation failed');
      }

      const count = results[0][1] as number;
      const ttlResult = results[2][1] as number;

      // Calculate reset time
      const resetTime = ttlResult > 0 ? Date.now() + (ttlResult * 1000) : Date.now() + ttl;

      return {
        totalHits: count,
        timeToExpire: Math.max(0, resetTime - Date.now()),
        isBlocked: false, // This will be determined by the guard
        timeToBlockExpire: 0, // No block by default
      };
    } catch (error) {
      this.logger.error(`Failed to increment rate limit for key ${key}:`, error);
      throw error;
    }
  }

  async getRecord(key: string): Promise<ThrottlerStorageRecord | undefined> {
    try {
      const redisKey = this.buildKey(key);
      const multi = this.redis.multi();

      multi.get(redisKey);
      multi.ttl(redisKey);

      const results = await multi.exec();

      if (!results || results.some(([err]) => err)) {
        return undefined;
      }

      const count = parseInt(results[0][1] as string, 10) || 0;
      const ttlResult = results[1][1] as number;

      if (count === 0 || ttlResult === -2) {
        return undefined;
      }

      const resetTime = ttlResult > 0 ? Date.now() + (ttlResult * 1000) : Date.now();

      return {
        totalHits: count,
        timeToExpire: Math.max(0, resetTime - Date.now()),
        isBlocked: false,
        timeToBlockExpire: 0,
      };
    } catch (error) {
      this.logger.error(`Failed to get record for key ${key}:`, error);
      return undefined;
    }
  }

  async reset(key: string): Promise<void> {
    try {
      const redisKey = this.buildKey(key);
      await this.redis.del(redisKey);
    } catch (error) {
      this.logger.error(`Failed to reset rate limit for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Set a block for a specific key
   */
  async setBlock(key: string, blockDuration: number): Promise<void> {
    try {
      const blockKey = this.buildBlockKey(key);
      await this.redis.setex(blockKey, blockDuration, '1');
    } catch (error) {
      this.logger.error(`Failed to set block for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Check if a key is currently blocked
   */
  async isBlocked(key: string): Promise<boolean> {
    try {
      const blockKey = this.buildBlockKey(key);
      const result = await this.redis.get(blockKey);
      return result === '1';
    } catch (error) {
      this.logger.error(`Failed to check block status for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Get remaining block time in seconds
   */
  async getBlockTimeRemaining(key: string): Promise<number> {
    try {
      const blockKey = this.buildBlockKey(key);
      const ttl = await this.redis.ttl(blockKey);
      return Math.max(0, ttl);
    } catch (error) {
      this.logger.error(`Failed to get block time remaining for key ${key}:`, error);
      return 0;
    }
  }

  /**
   * Remove block for a specific key
   */
  async removeBlock(key: string): Promise<void> {
    try {
      const blockKey = this.buildBlockKey(key);
      await this.redis.del(blockKey);
    } catch (error) {
      this.logger.error(`Failed to remove block for key ${key}:`, error);
      throw error;
    }
  }

  private buildKey(key: string): string {
    return `throttle:${key}`;
  }

  private buildBlockKey(key: string): string {
    return `block:${key}`;
  }

  /**
   * Get Redis instance for advanced operations
   */
  getRedisInstance(): Redis {
    return this.redis;
  }

  /**
   * Close Redis connection
   */
  async close(): Promise<void> {
    await this.redis.quit();
  }
}
