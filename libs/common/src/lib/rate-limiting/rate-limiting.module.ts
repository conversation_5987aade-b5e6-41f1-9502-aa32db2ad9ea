/* eslint-disable @typescript-eslint/no-explicit-any */
import { DynamicModule, Module } from '@nestjs/common';
import { ThrottlerModule } from '@nestjs/throttler';
import { RateLimitingModuleOptions } from './interfaces/rate-limiting-options.interface';
import { RateLimitingConfigService } from './rate-limiting-config.service';
import { RATE_LIMITING_OPTIONS } from './rate-limiting.constants';
import { RateLimitingGuard } from './rate-limiting.guard';
import { RateLimitingService } from './rate-limiting.service';
import { RateLimitingStorage } from './rate-limiting.storage';

@Module({})
export class RateLimitingModule {
  static forRoot(options: RateLimitingModuleOptions): DynamicModule {
    return {
      module: RateLimitingModule,
      imports: [
        ThrottlerModule.forRootAsync({
          useFactory: (configService: RateLimitingConfigService) => ({
            throttlers: configService.getThrottlerConfig(),
            storage: new RateLimitingStorage(configService.getRedisConfig()),
          }),
          inject: [RateLimitingConfigService],
        }),
      ],
      providers: [
        {
          provide: RATE_LIMITING_OPTIONS,
          useValue: options,
        },
        RateLimitingConfigService,
        {
          provide: RateLimitingStorage,
          useFactory: (configService: RateLimitingConfigService) => {
            return new RateLimitingStorage(configService.getRedisConfig());
          },
          inject: [RateLimitingConfigService],
        },
        RateLimitingService,
        RateLimitingGuard,
      ],
      exports: [
        RateLimitingService,
        RateLimitingGuard,
        RateLimitingConfigService,
        ThrottlerModule,
      ],
      global: true,
    };
  }

  static forRootAsync(options: {
    useFactory: (...args: any[]) => Promise<RateLimitingModuleOptions> | RateLimitingModuleOptions;
    inject?: any[];
  }): DynamicModule {
    return {
      module: RateLimitingModule,
      imports: [
        ThrottlerModule.forRootAsync({
          useFactory: async (configService: RateLimitingConfigService) => ({
            throttlers: configService.getThrottlerConfig(),
            storage: new RateLimitingStorage(configService.getRedisConfig()),
          }),
          inject: [RateLimitingConfigService],
        }),
      ],
      providers: [
        {
          provide: RATE_LIMITING_OPTIONS,
          useFactory: options.useFactory,
          inject: options.inject || [],
        },
        RateLimitingConfigService,
        {
          provide: RateLimitingStorage,
          useFactory: (configService: RateLimitingConfigService) => {
            return new RateLimitingStorage(configService.getRedisConfig());
          },
          inject: [RateLimitingConfigService],
        },
        RateLimitingService,
        RateLimitingGuard,
      ],
      exports: [
        RateLimitingService,
        RateLimitingGuard,
        RateLimitingConfigService,
        ThrottlerModule,
      ],
      global: true,
    };
  }
}
