 
import {
    CanActivate,
    ExecutionContext,
    HttpException,
    HttpStatus,
    Injectable,
    Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request, Response } from 'express';
import { RateLimitContext, RateLimitResult } from './interfaces/rate-limiting-options.interface';
import { RATE_LIMIT_HEADERS } from './rate-limiting.constants';
import { RateLimitingService } from './rate-limiting.service';

// Decorator for custom rate limiting
export const RateLimit = (limitName: string, customKey?: string) =>
  Reflector.createDecorator<{ limitName: string; customKey?: string }>()({
    limitName,
    customKey,
  });

// Decorator to skip rate limiting
export const SkipRateLimit = () => Reflector.createDecorator<boolean>()(true);

@Injectable()
export class RateLimitingGuard implements CanActivate {
  private readonly logger = new Logger(RateLimitingGuard.name);

  constructor(
    private readonly rateLimitingService: RateLimitingService,
    private readonly reflector: Reflector
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if rate limiting should be skipped
    const skipRateLimit = this.reflector.getAllAndOverride<boolean>('SkipRateLimit', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (skipRateLimit) {
      return true;
    }

    const request = context.switchToHttp().getRequest<Request & { user?: any; tenant?: any }>();
    const response = context.switchToHttp().getResponse<Response>();

    // Build rate limit context
    const rateLimitContext = this.buildRateLimitContext(request);

    // Check for custom rate limit decorator
    const customRateLimit = this.reflector.getAllAndOverride<{
      limitName: string;
      customKey?: string;
    }>('RateLimit', [context.getHandler(), context.getClass()]);

    let results: RateLimitResult[] = [];

    if (customRateLimit) {
      // Apply custom rate limit
      const customResult = await this.rateLimitingService.checkCustomRateLimit(
        rateLimitContext,
        customRateLimit.limitName,
        customRateLimit.customKey
      );
      if (customResult) {
        results.push(customResult);
      }
    } else {
      // Apply default rate limits
      results = await this.rateLimitingService.checkRateLimit(rateLimitContext);
    }

    // Check if any rate limit is exceeded
    const blockedResult = results.find((result) => !result.allowed);

    if (blockedResult) {
      // Set rate limit headers
      this.setRateLimitHeaders(response, blockedResult);

      // Log rate limit violation
      this.logger.warn(
        `Rate limit exceeded for ${blockedResult.limitType}: ${rateLimitContext.ip} - ${rateLimitContext.endpoint}`,
        {
          limitType: blockedResult.limitType,
          ip: rateLimitContext.ip,
          userId: rateLimitContext.userId,
          tenantId: rateLimitContext.tenantId,
          endpoint: rateLimitContext.endpoint,
          limit: blockedResult.limit,
          remaining: blockedResult.remaining,
        }
      );

      throw new HttpException(
        {
          statusCode: HttpStatus.TOO_MANY_REQUESTS,
          message: 'Rate limit exceeded',
          error: 'Too Many Requests',
          limitType: blockedResult.limitType,
          limit: blockedResult.limit,
          remaining: blockedResult.remaining,
          resetTime: blockedResult.resetTime,
          retryAfter: blockedResult.retryAfter,
        },
        HttpStatus.TOO_MANY_REQUESTS
      );
    }

    // Set rate limit headers for successful requests
    if (results.length > 0) {
      // Use the most restrictive limit for headers
      const mostRestrictive = results.reduce((prev, current) =>
        prev.remaining < current.remaining ? prev : current
      );
      this.setRateLimitHeaders(response, mostRestrictive);
    }

    return true;
  }

  private buildRateLimitContext(request: Request & { user?: any; tenant?: any }): RateLimitContext {
    // Extract user and tenant information from request
    // This assumes the authentication middleware has already run
    const userId = request.user?.id || request.user?.sub;
    const tenantId = request.user?.tenantId || request.tenant?.id;

    // Get client IP
    const ip = this.getClientIp(request);

    // Build endpoint identifier
    const endpoint = `${request.method}:${(request as any).route?.path || request.path}`;

    return {
      userId,
      tenantId,
      ip,
      endpoint,
      userAgent: (request as any).get('User-Agent'),
      method: request.method,
      path: request.path,
    };
  }

  private getClientIp(request: Request): string {
    // Check various headers for the real IP
    const xForwardedFor = request.get('X-Forwarded-For');
    if (xForwardedFor) {
      return xForwardedFor.split(',')[0].trim();
    }

    const xRealIp = request.get('X-Real-IP');
    if (xRealIp) {
      return xRealIp;
    }

    const xClientIp = request.get('X-Client-IP');
    if (xClientIp) {
      return xClientIp;
    }

    return request.ip || request.socket?.remoteAddress || '127.0.0.1';
  }

  private setRateLimitHeaders(response: Response, result: RateLimitResult): void {
    response.set(RATE_LIMIT_HEADERS.LIMIT, result.limit.toString());
    response.set(RATE_LIMIT_HEADERS.REMAINING, result.remaining.toString());
    response.set(RATE_LIMIT_HEADERS.RESET, Math.ceil(result.resetTime.getTime() / 1000).toString());
    response.set(RATE_LIMIT_HEADERS.LIMIT_TYPE, result.limitType);

    if (result.retryAfter) {
      response.set(RATE_LIMIT_HEADERS.RETRY_AFTER, result.retryAfter.toString());
    }
  }
}
