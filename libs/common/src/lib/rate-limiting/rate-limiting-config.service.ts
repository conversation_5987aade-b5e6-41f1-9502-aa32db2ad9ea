import { Inject, Injectable } from '@nestjs/common';
import { RateLimitConfig, RateLimitingModuleOptions } from './interfaces/rate-limiting-options.interface';
import { DEFAULT_RATE_LIMITS, RATE_LIMITING_OPTIONS } from './rate-limiting.constants';

@Injectable()
export class RateLimitingConfigService {
  constructor(
    @Inject(RATE_LIMITING_OPTIONS)
    private readonly options: RateLimitingModuleOptions
  ) {}

  getThrottlerConfig(): Array<{ name: string; ttl: number; limit: number }> {
    const throttlers = [];

    // Add default limits
    throttlers.push({
      name: this.options.defaultLimits.perUser.name,
      ttl: this.options.defaultLimits.perUser.ttl * 1000, // Convert to milliseconds
      limit: this.options.defaultLimits.perUser.limit,
    });

    throttlers.push({
      name: this.options.defaultLimits.perTenant.name,
      ttl: this.options.defaultLimits.perTenant.ttl * 1000,
      limit: this.options.defaultLimits.perTenant.limit,
    });

    throttlers.push({
      name: this.options.defaultLimits.perIp.name,
      ttl: this.options.defaultLimits.perIp.ttl * 1000,
      limit: this.options.defaultLimits.perIp.limit,
    });

    throttlers.push({
      name: this.options.defaultLimits.perEndpoint.name,
      ttl: this.options.defaultLimits.perEndpoint.ttl * 1000,
      limit: this.options.defaultLimits.perEndpoint.limit,
    });

    // Add custom limits if provided
    if (this.options.customLimits) {
      Object.values(this.options.customLimits).forEach((config) => {
        throttlers.push({
          name: config.name,
          ttl: config.ttl * 1000,
          limit: config.limit,
        });
      });
    }

    return throttlers;
  }

  getRateLimitConfig(limitType: string): RateLimitConfig | undefined {
    switch (limitType) {
      case 'per-user':
        return this.options.defaultLimits.perUser;
      case 'per-tenant':
        return this.options.defaultLimits.perTenant;
      case 'per-ip':
        return this.options.defaultLimits.perIp;
      case 'per-endpoint':
        return this.options.defaultLimits.perEndpoint;
      default:
        return this.options.customLimits?.[limitType];
    }
  }

  getDefaultLimits() {
    return this.options.defaultLimits;
  }

  getCustomLimits() {
    return this.options.customLimits || {};
  }

  isMetricsEnabled(): boolean {
    return this.options.enableMetrics ?? true;
  }

  isLoggingEnabled(): boolean {
    return this.options.enableLogging ?? true;
  }

  getKeyPrefix(): string {
    return this.options.keyPrefix || 'rate-limit';
  }

  getRedisConfig() {
    return this.options.redis;
  }

  /**
   * Create default configuration for rate limiting
   */
  static createDefaultConfig(): RateLimitingModuleOptions['defaultLimits'] {
    return {
      perUser: {
        name: DEFAULT_RATE_LIMITS.PER_USER.name,
        ttl: DEFAULT_RATE_LIMITS.PER_USER.ttl,
        limit: DEFAULT_RATE_LIMITS.PER_USER.limit,
        blockDuration: DEFAULT_RATE_LIMITS.PER_USER.blockDuration,
      },
      perTenant: {
        name: DEFAULT_RATE_LIMITS.PER_TENANT.name,
        ttl: DEFAULT_RATE_LIMITS.PER_TENANT.ttl,
        limit: DEFAULT_RATE_LIMITS.PER_TENANT.limit,
        blockDuration: DEFAULT_RATE_LIMITS.PER_TENANT.blockDuration,
      },
      perIp: {
        name: DEFAULT_RATE_LIMITS.PER_IP.name,
        ttl: DEFAULT_RATE_LIMITS.PER_IP.ttl,
        limit: DEFAULT_RATE_LIMITS.PER_IP.limit,
        blockDuration: DEFAULT_RATE_LIMITS.PER_IP.blockDuration,
      },
      perEndpoint: {
        name: DEFAULT_RATE_LIMITS.PER_ENDPOINT.name,
        ttl: DEFAULT_RATE_LIMITS.PER_ENDPOINT.ttl,
        limit: DEFAULT_RATE_LIMITS.PER_ENDPOINT.limit,
        blockDuration: DEFAULT_RATE_LIMITS.PER_ENDPOINT.blockDuration,
      },
    };
  }
}
