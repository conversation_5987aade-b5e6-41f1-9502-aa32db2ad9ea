/* eslint-disable @typescript-eslint/no-explicit-any */
import { Redis } from 'ioredis';

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db?: number;
  keyPrefix?: string;
  maxRetriesPerRequest?: number;
}

export interface RateLimitConfig {
  name: string;
  ttl: number; // Time to live in seconds
  limit: number; // Number of requests allowed
  blockDuration?: number; // Block duration in seconds after limit exceeded
  skipIf?: (context: any) => boolean; // Function to skip rate limiting
  generateKey?: (context: any) => string; // Custom key generation
}

export interface RateLimitingModuleOptions {
  redis: RedisConfig | Redis;
  defaultLimits: {
    perUser: RateLimitConfig;
    perTenant: RateLimitConfig;
    perIp: RateLimitConfig;
    perEndpoint: RateLimitConfig;
  };
  customLimits?: Record<string, RateLimitConfig>;
  enableMetrics?: boolean;
  enableLogging?: boolean;
  keyPrefix?: string;
}

export interface RateLimitContext {
  userId?: string;
  tenantId?: string;
  ip: string;
  endpoint: string;
  userAgent?: string;
  method: string;
  path: string;
}

export interface RateLimitResult {
  allowed: boolean;
  limit: number;
  remaining: number;
  resetTime: Date;
  retryAfter?: number;
  limitType: string;
  key: string;
}

export interface RateLimitMetrics {
  totalRequests: number;
  blockedRequests: number;
  allowedRequests: number;
  limitType: string;
  timestamp: Date;
}

// Define the interface since it's not exported from @nestjs/throttler
export interface ThrottlerStorageRecord {
  totalHits: number;
  timeToExpire: number;
  isBlocked: boolean;
  timeToBlockExpire: number;
}
