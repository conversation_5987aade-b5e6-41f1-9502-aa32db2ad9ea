import { Injectable, Logger } from '@nestjs/common';
import {
    RateLimitConfig,
    RateLimitContext,
    RateLimitMetrics,
    RateLimitResult,
} from './interfaces/rate-limiting-options.interface';
import { RateLimitingConfigService } from './rate-limiting-config.service';
import { RATE_LIMIT_KEYS } from './rate-limiting.constants';
import { RateLimitingStorage } from './rate-limiting.storage';

@Injectable()
export class RateLimitingService {
  private readonly logger = new Logger(RateLimitingService.name);

  constructor(
    private readonly configService: RateLimitingConfigService,
    private readonly storage: RateLimitingStorage
  ) {}

  /**
   * Check rate limit for a given context
   */
  async checkRateLimit(context: RateLimitContext): Promise<RateLimitResult[]> {
    const results: RateLimitResult[] = [];

    // Check all applicable rate limits
    const checks = [
      this.checkUserRateLimit(context),
      this.checkTenantRateLimit(context),
      this.checkIpRateLimit(context),
      this.checkEndpointRateLimit(context),
    ];

    const checkResults = await Promise.all(checks);
    results.push(...checkResults.filter(Boolean) as RateLimitResult[]);

    return results;
  }

  /**
   * Check user-specific rate limit
   */
  async checkUserRateLimit(context: RateLimitContext): Promise<RateLimitResult | null> {
    if (!context.userId) {
      return null;
    }

    const config = this.configService.getRateLimitConfig('per-user');
    if (!config) {
      return null;
    }

    const key = this.generateKey(RATE_LIMIT_KEYS.USER, context.userId);
    return this.performRateLimitCheck(key, config, 'per-user', context);
  }

  /**
   * Check tenant-specific rate limit
   */
  async checkTenantRateLimit(context: RateLimitContext): Promise<RateLimitResult | null> {
    if (!context.tenantId) {
      return null;
    }

    const config = this.configService.getRateLimitConfig('per-tenant');
    if (!config) {
      return null;
    }

    const key = this.generateKey(RATE_LIMIT_KEYS.TENANT, context.tenantId);
    return this.performRateLimitCheck(key, config, 'per-tenant', context);
  }

  /**
   * Check IP-specific rate limit
   */
  async checkIpRateLimit(context: RateLimitContext): Promise<RateLimitResult | null> {
    const config = this.configService.getRateLimitConfig('per-ip');
    if (!config) {
      return null;
    }

    const key = this.generateKey(RATE_LIMIT_KEYS.IP, context.ip);
    return this.performRateLimitCheck(key, config, 'per-ip', context);
  }

  /**
   * Check endpoint-specific rate limit
   */
  async checkEndpointRateLimit(context: RateLimitContext): Promise<RateLimitResult | null> {
    const config = this.configService.getRateLimitConfig('per-endpoint');
    if (!config) {
      return null;
    }

    const key = this.generateKey(RATE_LIMIT_KEYS.ENDPOINT, `${context.method}:${context.endpoint}`);
    return this.performRateLimitCheck(key, config, 'per-endpoint', context);
  }

  /**
   * Check custom rate limit
   */
  async checkCustomRateLimit(
    context: RateLimitContext,
    limitName: string,
    customKey?: string
  ): Promise<RateLimitResult | null> {
    const config = this.configService.getRateLimitConfig(limitName);
    if (!config) {
      return null;
    }

    const key = customKey || this.generateKey(RATE_LIMIT_KEYS.CUSTOM, limitName);
    return this.performRateLimitCheck(key, config, limitName, context);
  }

  /**
   * Perform the actual rate limit check
   */
  private async performRateLimitCheck(
    key: string,
    config: RateLimitConfig,
    limitType: string,
    context: RateLimitContext
  ): Promise<RateLimitResult> {
    try {
      // Check if currently blocked
      const isBlocked = await this.storage.isBlocked(key);
      if (isBlocked) {
        const retryAfter = await this.storage.getBlockTimeRemaining(key);
        return {
          allowed: false,
          limit: config.limit,
          remaining: 0,
          resetTime: new Date(Date.now() + retryAfter * 1000),
          retryAfter,
          limitType,
          key,
        };
      }

      // Check if should skip rate limiting
      if (config.skipIf && config.skipIf(context)) {
        return {
          allowed: true,
          limit: config.limit,
          remaining: config.limit,
          resetTime: new Date(Date.now() + config.ttl * 1000),
          limitType,
          key,
        };
      }

      // Use custom key generation if provided
      const finalKey = config.generateKey ? config.generateKey(context) : key;

      // Increment counter
      const record = await this.storage.increment(
        finalKey,
        config.ttl * 1000,
        config.limit,
        config.blockDuration || 0,
        limitType
      );

      const remaining = Math.max(0, config.limit - record.totalHits);
      const allowed = record.totalHits <= config.limit;

      // If limit exceeded and block duration is configured, set block
      if (!allowed && config.blockDuration) {
        await this.storage.setBlock(finalKey, config.blockDuration);
      }

      const resetTime = new Date(Date.now() + record.timeToExpire);

      const result: RateLimitResult = {
        allowed,
        limit: config.limit,
        remaining,
        resetTime,
        limitType,
        key: finalKey,
      };

      if (!allowed && config.blockDuration) {
        result.retryAfter = config.blockDuration;
      }

      // Log rate limit check if logging is enabled
      if (this.configService.isLoggingEnabled()) {
        this.logger.debug(`Rate limit check: ${limitType} - ${finalKey} - ${allowed ? 'ALLOWED' : 'BLOCKED'} (${record.totalHits}/${config.limit})`);
      }

      return result;
    } catch (error) {
      this.logger.error(`Rate limit check failed for ${limitType}:`, error);
      // In case of error, allow the request but log the issue
      return {
        allowed: true,
        limit: config.limit,
        remaining: config.limit,
        resetTime: new Date(Date.now() + config.ttl * 1000),
        limitType,
        key,
      };
    }
  }

  /**
   * Generate a rate limit key
   */
  private generateKey(type: string, identifier: string): string {
    const prefix = this.configService.getKeyPrefix();
    return `${prefix}:${type}:${identifier}`;
  }

  /**
   * Reset rate limit for a specific key
   */
  async resetRateLimit(key: string): Promise<void> {
    await this.storage.reset(key);
    await this.storage.removeBlock(key);
  }

  /**
   * Get current rate limit status without incrementing
   */
  async getRateLimitStatus(key: string): Promise<{ hits: number; remaining: number; resetTime: Date } | null> {
    const record = await this.storage.getRecord(key);
    if (!record) {
      return null;
    }

    return {
      hits: record.totalHits,
      remaining: Math.max(0, record.totalHits),
      resetTime: new Date(Date.now() + record.timeToExpire),
    };
  }

  /**
   * Get metrics for rate limiting (if enabled)
   */
  async getMetrics(): Promise<RateLimitMetrics[]> {
    if (!this.configService.isMetricsEnabled()) {
      return [];
    }

    // This would typically integrate with a metrics collection system
    // For now, return empty array - can be extended based on requirements
    return [];
  }
}
