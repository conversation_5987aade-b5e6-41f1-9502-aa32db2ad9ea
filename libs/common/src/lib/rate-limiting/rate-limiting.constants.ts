export const RATE_LIMITING_OPTIONS = 'RATE_LIMITING_OPTIONS';

export const DEFAULT_RATE_LIMITS = {
  PER_USER: {
    name: 'per-user',
    ttl: 60, // 1 minute
    limit: 100, // 100 requests per minute per user
    blockDuration: 300, // 5 minutes block
  },
  PER_TENANT: {
    name: 'per-tenant',
    ttl: 60, // 1 minute
    limit: 1000, // 1000 requests per minute per tenant
    blockDuration: 300, // 5 minutes block
  },
  PER_IP: {
    name: 'per-ip',
    ttl: 60, // 1 minute
    limit: 50, // 50 requests per minute per IP
    blockDuration: 600, // 10 minutes block
  },
  PER_ENDPOINT: {
    name: 'per-endpoint',
    ttl: 60, // 1 minute
    limit: 200, // 200 requests per minute per endpoint (default)
    blockDuration: 300, // 5 minutes block
  },
} as const;

export const RATE_LIMIT_HEADERS = {
  LIMIT: 'X-RateLimit-Limit',
  REMAINING: 'X-RateLimit-Remaining',
  RESET: 'X-RateLimit-Reset',
  RETRY_AFTER: 'Retry-After',
  LIMIT_TYPE: 'X-RateLimit-Type',
} as const;

export const RATE_LIMIT_KEYS = {
  USER: 'user',
  TENANT: 'tenant',
  IP: 'ip',
  ENDPOINT: 'endpoint',
  CUSTOM: 'custom',
} as const;

export const REDIS_KEY_PREFIX = 'rate-limit';

export const RATE_LIMIT_ERRORS = {
  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded',
  INVALID_CONFIGURATION: 'Invalid rate limiting configuration',
  REDIS_CONNECTION_ERROR: 'Redis connection error',
  UNKNOWN_ERROR: 'Unknown rate limiting error',
} as const;
