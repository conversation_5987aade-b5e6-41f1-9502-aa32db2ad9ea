/* eslint-disable @typescript-eslint/no-explicit-any */

/**
 * Base Repository Interface
 * Defines common CRUD operations that all repositories should implement
 */
export interface IBaseRepository<T, CreateInput, UpdateInput> {
  findById(id: string): Promise<T | null>;
  create(data: CreateInput): Promise<T>;
  update(id: string, data: UpdateInput): Promise<T>;
  delete(id: string): Promise<T>;
  findMany(whereOrOptions?: any): Promise<T[]>;
  count(where?: any): Promise<number>;
}

/**
 * Base Repository Implementation
 * Provides common CRUD operations for all repositories
 * Generic implementation that works with any Prisma service
 */
export abstract class BaseRepository<T, CreateInput, UpdateInput> implements IBaseRepository<T, CreateInput, UpdateInput> {
  constructor(protected readonly prisma: any) {}

  /**
   * Abstract property that must be implemented by child classes
   * Returns the Prisma model for the specific entity
   */
  protected abstract get model(): any;

  /**
   * Find entity by ID
   */
  async findById(id: string): Promise<T | null> {
    try {
      return await this.model.findUnique({ where: { id } });
    } catch (error) {
      throw new Error(`Failed to find entity by ID ${id}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Create new entity
   */
  async create(data: CreateInput): Promise<T> {
    try {
      return await this.model.create({ data });
    } catch (error) {
      throw new Error(`Failed to create entity: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Update entity by ID
   */
  async update(id: string, data: UpdateInput): Promise<T> {
    try {
      return await this.model.update({
        where: { id },
        data,
      });
    } catch (error) {
      throw new Error(`Failed to update entity ${id}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Delete entity by ID
   */
  async delete(id: string): Promise<T> {
    try {
      return await this.model.delete({ where: { id } });
    } catch (error) {
      throw new Error(`Failed to delete entity ${id}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Find multiple entities with optional filtering
   */
  async findMany(whereOrOptions?: any): Promise<T[]> {
    try {
      // If the parameter has a 'where' property, it's a full options object
      if (whereOrOptions && typeof whereOrOptions === 'object' && 'where' in whereOrOptions) {
        return await this.model.findMany(whereOrOptions);
      }
      // Otherwise, treat it as a where clause
      return await this.model.findMany({ where: whereOrOptions });
    } catch (error) {
      throw new Error(`Failed to find entities: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Count entities with optional filtering
   */
  async count(where?: any): Promise<number> {
    try {
      return await this.model.count({ where });
    } catch (error) {
      throw new Error(`Failed to count entities: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Execute operations within a transaction
   */
  async transaction<R>(fn: (tx: any) => Promise<R>): Promise<R> {
    try {
      return await this.prisma.$transaction(fn);
    } catch (error) {
      throw new Error(`Transaction failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Check if entity exists by ID
   */
  async exists(id: string): Promise<boolean> {
    try {
      const count = await this.model.count({ where: { id } });
      return count > 0;
    } catch (error) {
      throw new Error(`Failed to check if entity exists ${id}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
