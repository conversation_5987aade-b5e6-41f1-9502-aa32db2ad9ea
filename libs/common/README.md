# @qeep/common

Shared utilities, decorators, and common functionality for the Qeep platform.

## Features

- **Tenant Management**: Comprehensive tenant extraction, validation, and context management
- **Circuit Breaker**: Resilience patterns with failure detection, fallback mechanisms, and automatic recovery
- **gRPC Integration**: Built-in tenant service client for validation and data retrieval
- **Flexible Configuration**: Environment-based configuration with sensible defaults
- **Caching**: Built-in tenant information caching for performance
- **Type Safety**: Full TypeScript support with no `any` types

## Installation

This library is part of the Qeep NX workspace and is automatically available to all services.

```typescript
import { CommonModule, Tenant, TenantId, TenantService, CircuitBreakerModule, CircuitBreakerService, UseCircuitBreaker } from '@qeep/common';
```

## Quick Start

### 1. Module Setup

```typescript
// In your app module
import { CommonModule } from '@qeep/common';

@Module({
  imports: [
    // Enable global tenant interceptor and circuit breaker (default)
    CommonModule.forRoot(),

    // Or configure with custom options
    CommonModule.forRoot({
      tenantConfig: {
        requireTenant: true,
        enableDebugLogging: true,
      },
      enableGlobalTenantInterceptor: true,
      circuitBreakerConfig: {
        defaultConfig: {
          name: 'default',
          enabled: true,
          failureThreshold: 50,
          timeout: 30000,
          requestVolumeThreshold: 10,
          sleepWindowInMilliseconds: 30000,
          maxRetries: 3,
        },
        circuitBreakers: {
          'auth-service': {
            failureThreshold: 25,
            timeout: 5000,
          },
        },
        enableMetrics: true,
        enableLogging: true,
      },
      enableCircuitBreaker: true,
    }),

    // Or disable global interceptor for selective use
    CommonModule.forFeature({
      tenantConfig: {
        validateTenant: false,
      },
      enableCircuitBreaker: false,
    }),
  ],
})
export class AppModule {}
```

### 2. Controller Usage

```typescript
import { Controller, Get } from '@nestjs/common';
import { Tenant, TenantId, TenantCode, RequireActiveTenant, TenantInfo } from '@qeep/common';

@Controller('api/example')
export class ExampleController {
  @Get('basic')
  async basicExample(@Tenant() tenant: TenantInfo) {
    // tenant will be undefined if not provided and not required
    if (tenant) {
      console.log(`Request from tenant: ${tenant.name}`);
    }
    return { message: 'Hello World' };
  }

  @Get('required')
  @RequireActiveTenant()
  async requiredExample(@Tenant() tenant: TenantInfo, @TenantId() tenantId: string, @TenantCode() tenantCode: string) {
    // tenant is guaranteed to be present and active
    return {
      message: `Hello ${tenant.name}`,
      tenantId,
      tenantCode,
    };
  }

  @Get('specific-property')
  async specificProperty(@Tenant('name') tenantName: string) {
    return { tenantName };
  }

  @Get('circuit-breaker-example')
  @UseCircuitBreaker('example-service')
  async circuitBreakerExample() {
    // This method is protected by circuit breaker
    // If it fails repeatedly, circuit will open and fallback will be used
    return { message: 'Protected by circuit breaker' };
  }

  @Get('circuit-breaker-with-fallback')
  @UseCircuitBreaker('example-service', 'fallbackMethod')
  async protectedMethod() {
    // Simulate potential failure
    if (Math.random() > 0.7) {
      throw new Error('Service temporarily unavailable');
    }
    return { message: 'Success!' };
  }

  async fallbackMethod() {
    return { message: 'Fallback response - service is temporarily unavailable' };
  }
}
```

## Configuration

### Environment Variables

Add these to your `.env.development` file:

```bash
# Enable/disable tenant interceptor globally
TENANT_INTERCEPTOR_ENABLED=true

# Whether to validate tenant against tenant service
TENANT_INTERCEPTOR_VALIDATE=true

# Whether to require tenant for all requests
TENANT_INTERCEPTOR_REQUIRE=false

# Enable tenant information caching
TENANT_INTERCEPTOR_CACHE=true

# Cache TTL in seconds
TENANT_INTERCEPTOR_CACHE_TTL=300

# Header names for tenant extraction
TENANT_CODE_HEADER=x-tenant-code
TENANT_ID_HEADER=x-tenant-id

# Query parameter names
TENANT_CODE_QUERY_PARAM=tenant_code
TENANT_ID_QUERY_PARAM=tenant_id

# JWT claim names
JWT_TENANT_ID_CLAIM=tenant_id
JWT_TENANT_CODE_CLAIM=tenant_code

# Debug logging
TENANT_INTERCEPTOR_DEBUG=false

# =============================================================================
# CIRCUIT BREAKER CONFIGURATION
# =============================================================================

# Enable/disable circuit breaker globally
CIRCUIT_BREAKER_ENABLED=true

# Default failure threshold percentage (0-100)
CIRCUIT_BREAKER_FAILURE_THRESHOLD=50

# Default timeout in milliseconds
CIRCUIT_BREAKER_TIMEOUT=30000

# Minimum requests before calculating failure rate
CIRCUIT_BREAKER_REQUEST_VOLUME_THRESHOLD=10

# Sleep window before attempting to close circuit (milliseconds)
CIRCUIT_BREAKER_SLEEP_WINDOW=30000

# Maximum retries in half-open state
CIRCUIT_BREAKER_MAX_RETRIES=3

# Enable metrics collection
CIRCUIT_BREAKER_ENABLE_METRICS=true

# Enable circuit breaker logging
CIRCUIT_BREAKER_ENABLE_LOGGING=true

# Service-specific circuit breaker settings
CIRCUIT_BREAKER_AUTH_ENABLED=true
CIRCUIT_BREAKER_AUTH_FAILURE_THRESHOLD=50
CIRCUIT_BREAKER_AUTH_TIMEOUT=30000

CIRCUIT_BREAKER_USER_ENABLED=true
CIRCUIT_BREAKER_USER_FAILURE_THRESHOLD=50
CIRCUIT_BREAKER_USER_TIMEOUT=30000

CIRCUIT_BREAKER_TENANT_ENABLED=true
CIRCUIT_BREAKER_TENANT_FAILURE_THRESHOLD=50
CIRCUIT_BREAKER_TENANT_TIMEOUT=30000

CIRCUIT_BREAKER_NOTIFICATION_ENABLED=true
CIRCUIT_BREAKER_NOTIFICATION_FAILURE_THRESHOLD=50
CIRCUIT_BREAKER_NOTIFICATION_TIMEOUT=30000
```

### Programmatic Configuration

```typescript
import { TenantExtractionSource } from '@qeep/common';

CommonModule.forRoot({
  tenantConfig: {
    enabled: true,
    extractionSources: [TenantExtractionSource.HEADER, TenantExtractionSource.QUERY_PARAM, TenantExtractionSource.JWT_CLAIMS, TenantExtractionSource.DEFAULT],
    validateTenant: true,
    requireTenant: false,
    enableCache: true,
    cacheTtlSeconds: 300,
    tenantCodeHeader: 'x-tenant-code',
    tenantIdHeader: 'x-tenant-id',
    defaultTenantCode: 'ronna-bank',
    enableDebugLogging: false,
  },
});
```

## Tenant Extraction Sources

The interceptor can extract tenant information from multiple sources in order of priority:

1. **Headers**: `x-tenant-code`, `x-tenant-id`
2. **Query Parameters**: `tenant_code`, `tenant_id`
3. **JWT Claims**: `tenant_id`, `tenant_code`
4. **Path/Subdomain**: Extract from URL structure
5. **Default**: Use configured default tenant

### Example Requests

```bash
# Header-based
curl -H "x-tenant-code: acme" http://localhost:3000/api/example

# Query parameter-based
curl "http://localhost:3000/api/example?tenant_code=acme"

# JWT-based (tenant info in token claims)
curl -H "Authorization: Bearer <jwt-with-tenant-claims>" http://localhost:3000/api/example
```

## Decorators

### Parameter Decorators

- `@Tenant()` - Get full tenant object
- `@Tenant('property')` - Get specific tenant property
- `@TenantId()` - Get tenant ID
- `@TenantCode()` - Get tenant code
- `@TenantName()` - Get tenant name
- `@TenantStatus()` - Get tenant status

### Method Decorators

- `@RequireTenant()` - Require tenant for endpoint
- `@OptionalTenant()` - Override global requirement
- `@RequireActiveTenant()` - Require active tenant
- `@AllowTrialTenant()` - Allow trial tenants
- `@RequireTenantStatus(status...)` - Require specific statuses

### Examples

```typescript
@Get('admin')
@RequireTenantStatus(TenantStatus.TENANT_STATUS_ACTIVE)
async adminEndpoint(@Tenant() tenant: TenantInfo) {
  // Only active tenants can access
}

@Get('trial-feature')
@AllowTrialTenant()
async trialFeature(@Tenant() tenant: TenantInfo) {
  // Both active and trial tenants can access
}

@Get('public')
@OptionalTenant()
async publicEndpoint(@Tenant() tenant?: TenantInfo) {
  // Tenant is optional even if globally required
}
```

## Utility Functions

```typescript
import {
  isTenantActive,
  isTenantTrial,
  isTenantSuspended,
  getTenantStatusName
} from '@qeep/common';

@Get('status-check')
async statusCheck(@Tenant() tenant: TenantInfo) {
  return {
    isActive: isTenantActive(tenant),
    isTrial: isTenantTrial(tenant),
    isSuspended: isTenantSuspended(tenant),
    statusName: getTenantStatusName(tenant),
  };
}
```

## Integration with Existing JWT Validation

The tenant interceptor works alongside the existing JWT validation library:

```typescript
import { JwtAuthGuard } from '@qeep/jwt-validation';
import { RequireActiveTenant } from '@qeep/common';

@Get('protected')
@UseGuards(JwtAuthGuard)
@RequireActiveTenant()
async protectedEndpoint(
  @User() user: UserContext,           // From JWT validation
  @Tenant() tenant: TenantInfo         // From tenant interceptor
) {
  // Both user and tenant context available
}
```

## Running unit tests

Run `nx test common` to execute the unit tests via [Jest](https://jestjs.io).
