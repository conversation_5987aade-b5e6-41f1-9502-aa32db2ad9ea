# Telemetry Configuration

The Qeep common library includes telemetry functionality for observability, including tracing, metrics, and logging.

## Development Mode Behavior

**Telemetry is automatically disabled in development mode** for better performance and reduced noise during development.

### Environment Detection

The system detects development mode when:
- `NODE_ENV=development` (or NODE_ENV is not set, defaulting to 'development')

### Default Behavior by Environment

| Environment | Telemetry | Tracing | Metrics | Sample Rate |
|-------------|-----------|---------|---------|-------------|
| development | ❌ Disabled | ❌ Disabled | ❌ Disabled | 0.1 (if enabled) |
| staging     | ✅ Enabled  | ✅ Enabled  | ✅ Enabled  | 1.0 |
| production  | ✅ Enabled  | ✅ Enabled  | ✅ Enabled  | 1.0 |

## Overriding Default Behavior

You can override the default behavior using environment variables:

### Enable Telemetry in Development

```bash
# Enable all telemetry in development
TELEMETRY_ENABLED=true

# Enable only specific components
TELEMETRY_ENABLED=true
TRACING_ENABLED=false
METRICS_ENABLED=true
```

### Disable Telemetry in Production

```bash
# Disable all telemetry in production
TELEMETRY_ENABLED=false

# Disable only specific components
TELEMETRY_ENABLED=true
TRACING_ENABLED=false
METRICS_ENABLED=true
```

## Configuration Options

### Environment Variables

| Variable | Default (dev) | Default (prod) | Description |
|----------|---------------|----------------|-------------|
| `TELEMETRY_ENABLED` | `false` | `true` | Enable/disable all telemetry |
| `TRACING_ENABLED` | `false` | `true` | Enable/disable tracing |
| `METRICS_ENABLED` | `false` | `true` | Enable/disable metrics |
| `TRACING_SAMPLE_RATE` | `0.1` | `1.0` | Trace sampling rate (0.0-1.0) |
| `JAEGER_ENDPOINT` | `http://localhost:14268/api/traces` | - | Jaeger endpoint |
| `PROMETHEUS_ENDPOINT` | `/metrics` | `/metrics` | Prometheus metrics endpoint |

### Programmatic Configuration

```typescript
// Explicitly enable telemetry
CommonModule.forRoot({
  enableTelemetry: true,
  telemetryConfig: {
    enabled: true,
    serviceName: 'my-service',
    tracing: {
      enabled: true,
      sampleRate: 1.0
    },
    metrics: {
      enabled: true,
      collectionInterval: 5000
    }
  }
})

// Explicitly disable telemetry
CommonModule.forRoot({
  enableTelemetry: false
})
```

## Performance Impact

Disabling telemetry in development provides:
- ⚡ Faster startup times
- 🔇 Reduced log noise
- 💾 Lower memory usage
- 🚀 Better development experience

## Troubleshooting

### Telemetry Not Working in Production

1. Check environment variables:
   ```bash
   echo $NODE_ENV
   echo $TELEMETRY_ENABLED
   ```

2. Look for telemetry logs:
   ```
   [TelemetryService] Telemetry is disabled (development mode). Set TELEMETRY_ENABLED=true to enable.
   [TelemetryService] Initializing telemetry for service: my-service
   ```

### Force Enable in Development

```bash
# Set environment variable
export TELEMETRY_ENABLED=true
export NODE_ENV=development

# Or in .env file
TELEMETRY_ENABLED=true
NODE_ENV=development
```
