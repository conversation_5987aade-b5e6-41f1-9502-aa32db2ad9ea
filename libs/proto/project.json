{"name": "proto", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/proto/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/proto", "main": "libs/proto/src/index.ts", "tsConfig": "libs/proto/tsconfig.lib.json", "assets": ["libs/proto/*.md", "libs/proto/src/proto/**/*.proto"]}}, "generate-types": {"executor": "nx:run-commands", "outputs": ["{projectRoot}/src/generated"], "inputs": ["{projectRoot}/src/proto/**/*.proto", "{workspaceRoot}/scripts/update-proto-exports.js"], "cache": true, "options": {"commands": ["mkdir -p libs/proto/src/generated", "protoc --plugin=./node_modules/.bin/protoc-gen-ts_proto --ts_proto_out=libs/proto/src/generated --ts_proto_opt=nestJs=true,outputServices=grpc-js,env=node,esModuleInterop=true,stringEnums=true,useOptionals=messages,exportCommonSymbols=false,outputIndex=true,useDate=true,forceLong=string,useExactTypes=false --proto_path=libs/proto/src/proto libs/proto/src/proto/**/*.proto", "node scripts/update-proto-exports.js"], "parallel": false}}, "generate-types-watch": {"executor": "nx:run-commands", "options": {"commands": ["chokidar \"libs/proto/src/proto/**/*.proto\" -c \"nx run proto:generate-types\""]}}, "clean-generated": {"executor": "nx:run-commands", "options": {"commands": ["rm -rf libs/proto/src/generated/*", "echo '// Generated TypeScript interfaces from proto files' > libs/proto/src/generated/index.ts", "echo '// This file will be populated when running: nx run proto:generate-types' >> libs/proto/src/generated/index.ts", "echo '' >> libs/proto/src/generated/index.ts", "echo '// For now, export empty object to prevent build errors' >> libs/proto/src/generated/index.ts", "echo 'export {};' >> libs/proto/src/generated/index.ts"]}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/proto/jest.config.ts"}}}}