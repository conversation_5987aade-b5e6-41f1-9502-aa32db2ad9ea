syntax = "proto3";
package customer;

import "google/protobuf/timestamp.proto";

// Customer Service Protocol Buffer Definitions
//
// This file defines the gRPC service contract for customer management
// operations in the Qeep platform.
//
// Version: 1.0
// Author: Qeep Team

// Customer service definition
service CustomerService {
  // Customer management operations
  rpc GetAllCustomers(GetAllCustomersRequest) returns (GetAllCustomersResponse);
  rpc GetCustomerById(GetCustomerByIdRequest) returns (GetCustomerByIdResponse);
  rpc CreateCustomer(CreateCustomerRequest) returns (CreateCustomerResponse);
  rpc UpdateCustomer(UpdateCustomerRequest) returns (UpdateCustomerResponse);
  rpc DeleteCustomer(DeleteCustomerRequest) returns (DeleteCustomerResponse);
  rpc GetCustomerStats(GetCustomerStatsRequest) returns (GetCustomerStatsResponse);
  rpc SearchCustomers(SearchCustomersRequest) returns (SearchCustomersResponse);
  rpc UpdateCustomerStatus(UpdateCustomerStatusRequest) returns (UpdateCustomerStatusResponse);
  rpc UpdateCustomerRiskLevel(UpdateCustomerRiskLevelRequest) returns (UpdateCustomerRiskLevelResponse);
  rpc UpdateCustomerKycStatus(UpdateCustomerKycStatusRequest) returns (UpdateCustomerKycStatusResponse);
}

// Enums
enum CustomerType {
  CUSTOMER_TYPE_UNKNOWN = 0;
  CUSTOMER_TYPE_INDIVIDUAL = 1;
  CUSTOMER_TYPE_BUSINESS = 2;
  CUSTOMER_TYPE_TRUST = 3;
  CUSTOMER_TYPE_GOVERNMENT = 4;
  CUSTOMER_TYPE_NON_PROFIT = 5;
}

enum CustomerStatus {
  CUSTOMER_STATUS_UNKNOWN = 0;
  CUSTOMER_STATUS_ACTIVE = 1;
  CUSTOMER_STATUS_INACTIVE = 2;
  CUSTOMER_STATUS_SUSPENDED = 3;
  CUSTOMER_STATUS_CLOSED = 4;
  CUSTOMER_STATUS_PENDING_VERIFICATION = 5;
}

enum RiskLevel {
  RISK_LEVEL_UNKNOWN = 0;
  RISK_LEVEL_LOW = 1;
  RISK_LEVEL_MEDIUM = 2;
  RISK_LEVEL_HIGH = 3;
  RISK_LEVEL_CRITICAL = 4;
}

enum KycStatus {
  KYC_STATUS_UNKNOWN = 0;
  KYC_STATUS_PENDING = 1;
  KYC_STATUS_IN_PROGRESS = 2;
  KYC_STATUS_VERIFIED = 3;
  KYC_STATUS_REJECTED = 4;
  KYC_STATUS_EXPIRED = 5;
  KYC_STATUS_REQUIRES_UPDATE = 6;
}

enum SortOrder {
  SORT_ORDER_UNKNOWN = 0;
  SORT_ORDER_ASC = 1;
  SORT_ORDER_DESC = 2;
}

enum GenderType {
  GENDER_TYPE_UNKNOWN = 0;
  GENDER_TYPE_MALE = 1;
  GENDER_TYPE_FEMALE = 2;
  GENDER_TYPE_OTHER = 3;
}

enum AddressType {
  ADDRESS_TYPE_UNKNOWN = 0;
  ADDRESS_TYPE_RESIDENTIAL = 1;
  ADDRESS_TYPE_BUSINESS = 2;
  ADDRESS_TYPE_MAILING = 3;
}

// Common messages
message RequestMetadata {
  string requestId = 1;
  string sourceIp = 2;
  string userAgent = 3;
  google.protobuf.Timestamp timestamp = 4;
}

message ResponseMetadata {
  string requestId = 1;
  google.protobuf.Timestamp timestamp = 2;
  int32 processingTime = 3;
  string version = 4;
}

message ErrorDetails {
  string code = 1;
  string message = 2;
  string details = 3;
}

// Address message
message CustomerAddress {
  string street1 = 1;
  string street2 = 2;
  string city = 3;
  string state = 4;
  string postalCode = 5;
  string country = 6;
  AddressType addressType = 7;
}

// Profile data message
message CustomerProfileData {
  // Individual customer fields
  string firstName = 1;
  string lastName = 2;
  string middleName = 3;
  string dateOfBirth = 4;
  GenderType gender = 5;
  string nationality = 6;
  string countryOfBirth = 7;
  string placeOfBirth = 8;

  // Business customer fields
  string businessName = 9;
  string businessType = 10;
  string businessRegistrationNumber = 11;
  string businessRegistrationDate = 12;
  string businessCountry = 13;
  string businessIndustry = 14;
  string businessDescription = 15;

  // Contact information
  string email = 16;
  string phoneNumber = 17;
  string alternatePhoneNumber = 18;

  // Address information
  CustomerAddress address = 19;
  CustomerAddress mailingAddress = 20;

  // Employment/Business information
  string occupation = 21;
  string employer = 22;
  string employmentStatus = 23;
  double annualIncome = 24;
  repeated string sourceOfFunds = 25;
  repeated string sourceOfWealth = 26;

  // Risk indicators
  bool politicallyExposed = 27;
  bool sanctionsMatch = 28;
  bool adverseMediaMatch = 29;
  bool highRiskCountryExposure = 30;

  // Custom fields (JSON string)
  string customFields = 31;
}

// Customer message
message Customer {
  string id = 1;
  string tenantId = 2;
  CustomerType customerType = 3;
  CustomerStatus status = 4;
  RiskLevel riskLevel = 5;
  KycStatus kycStatus = 6;
  CustomerProfileData profileData = 7;
  string kycData = 8; // JSON string
  string riskAssessment = 9; // JSON string
  google.protobuf.Timestamp createdAt = 10;
  google.protobuf.Timestamp updatedAt = 11;
}

// Pagination message
message PaginationMeta {
  int32 total = 1;
  int32 limit = 2;
  int32 offset = 3;
  bool hasMore = 4;
  int32 page = 5;
  int32 totalPages = 6;
}

// Request messages
message GetAllCustomersRequest {
  int32 page = 1;
  int32 limit = 2;
  string search = 3;
  CustomerStatus status = 4;
  CustomerType customerType = 5;
  RiskLevel riskLevel = 6;
  KycStatus kycStatus = 7;
  string tenantId = 8;
  string sortBy = 9;
  SortOrder sortOrder = 10;
  RequestMetadata metadata = 11;
}

message GetCustomerByIdRequest {
  string id = 1;
  RequestMetadata metadata = 2;
}

message CreateCustomerRequest {
  CustomerType customerType = 1;
  CustomerProfileData profileData = 2;
  CustomerStatus status = 3;
  RiskLevel riskLevel = 4;
  KycStatus kycStatus = 5;
  string kycData = 6; // JSON string
  string riskAssessment = 7; // JSON string
  RequestMetadata metadata = 8;
}

message UpdateCustomerRequest {
  string id = 1;
  CustomerType customerType = 2;
  CustomerProfileData profileData = 3;
  CustomerStatus status = 4;
  RiskLevel riskLevel = 5;
  KycStatus kycStatus = 6;
  string kycData = 7; // JSON string
  string riskAssessment = 8; // JSON string
  RequestMetadata metadata = 9;
}

message DeleteCustomerRequest {
  string id = 1;
  string reason = 2;
  RequestMetadata metadata = 3;
}

message GetCustomerStatsRequest {
  string tenantId = 1;
  string dateFrom = 2;
  string dateTo = 3;
  CustomerType customerType = 4;
  RiskLevel riskLevel = 5;
  RequestMetadata metadata = 6;
}

message SearchCustomersRequest {
  string searchTerm = 1;
  string tenantId = 2;
  int32 limit = 3;
  CustomerType customerType = 4;
  CustomerStatus status = 5;
  RiskLevel riskLevel = 6;
  RequestMetadata metadata = 7;
}

message UpdateCustomerStatusRequest {
  string id = 1;
  CustomerStatus status = 2;
  string reason = 3;
  RequestMetadata metadata = 4;
}

message UpdateCustomerRiskLevelRequest {
  string id = 1;
  RiskLevel riskLevel = 2;
  string reason = 3;
  string assessmentData = 4; // JSON string
  RequestMetadata metadata = 5;
}

message UpdateCustomerKycStatusRequest {
  string id = 1;
  KycStatus kycStatus = 2;
  string reason = 3;
  string kycData = 4; // JSON string
  RequestMetadata metadata = 5;
}

// Response messages
message GetAllCustomersResponse {
  bool success = 1;
  repeated Customer customers = 2;
  PaginationMeta pagination = 3;
  string message = 4;
  ErrorDetails error = 5;
  ResponseMetadata metadata = 6;
}

message GetCustomerByIdResponse {
  bool success = 1;
  Customer customer = 2;
  string message = 3;
  ErrorDetails error = 4;
  ResponseMetadata metadata = 5;
}

message CreateCustomerResponse {
  bool success = 1;
  Customer customer = 2;
  string message = 3;
  ErrorDetails error = 4;
  ResponseMetadata metadata = 5;
}

message UpdateCustomerResponse {
  bool success = 1;
  Customer customer = 2;
  string message = 3;
  ErrorDetails error = 4;
  ResponseMetadata metadata = 5;
}

message DeleteCustomerResponse {
  bool success = 1;
  string message = 2;
  ErrorDetails error = 3;
  ResponseMetadata metadata = 4;
}

// Customer statistics message
message CustomerStatistics {
  int32 totalCustomers = 1;
  int32 activeCustomers = 2;
  int32 pendingKyc = 3;
  int32 highRiskCustomers = 4;
  int32 recentCustomers = 5;
  map<string, int32> customersByType = 6;
  map<string, int32> customersByRiskLevel = 7;
}

message GetCustomerStatsResponse {
  bool success = 1;
  CustomerStatistics statistics = 2;
  string message = 3;
  ErrorDetails error = 4;
  ResponseMetadata metadata = 5;
}

message SearchCustomersResponse {
  bool success = 1;
  repeated Customer customers = 2;
  int32 totalResults = 3;
  string searchTerm = 4;
  int32 searchTime = 5;
  string message = 6;
  ErrorDetails error = 7;
  ResponseMetadata metadata = 8;
}

message UpdateCustomerStatusResponse {
  bool success = 1;
  Customer customer = 2;
  string message = 3;
  ErrorDetails error = 4;
  ResponseMetadata metadata = 5;
}

message UpdateCustomerRiskLevelResponse {
  bool success = 1;
  Customer customer = 2;
  string message = 3;
  ErrorDetails error = 4;
  ResponseMetadata metadata = 5;
}

message UpdateCustomerKycStatusResponse {
  bool success = 1;
  Customer customer = 2;
  string message = 3;
  ErrorDetails error = 4;
  ResponseMetadata metadata = 5;
}
