syntax = "proto3";
package common;

import "google/protobuf/timestamp.proto";

// Health Check Protocol Buffer Definitions
//
// This file defines the gRPC service contract for health checking
// across all Qeep microservices.
//
// Version: 1.0
// Author: Qeep Team

// Health check service definition
service HealthService {
  // Basic health check
  rpc Check(HealthCheckRequest) returns (HealthCheckResponse);

  // Detailed health check with dependency information
  rpc CheckDetailed(HealthCheckRequest) returns (DetailedHealthCheckResponse);

  // Readiness check for Kubernetes probes
  rpc CheckReadiness(HealthCheckRequest) returns (ReadinessCheckResponse);

  // Liveness check for Kubernetes probes
  rpc CheckLiveness(HealthCheckRequest) returns (LivenessCheckResponse);
}

// Health check request
message HealthCheckRequest {
  // Service name to check (optional, defaults to current service)
  string service = 1;

  // Request metadata
  RequestMetadata metadata = 2;
}

// Basic health check response
message HealthCheckResponse {
  // Health status
  HealthStatus status = 1;

  // System information
  SystemInfo system = 2;

  // Optional message
  string message = 3;

  // Response metadata
  ResponseMetadata metadata = 4;
}

// Detailed health check response
message DetailedHealthCheckResponse {
  // Health status
  HealthStatus status = 1;

  // System information
  SystemInfo system = 2;

  // Dependency health information
  repeated ServiceHealth dependencies = 3;

  // Additional health checks
  map<string, string> checks = 4;

  // Optional message
  string message = 5;

  // Response metadata
  ResponseMetadata metadata = 6;
}

// Readiness check response
message ReadinessCheckResponse {
  // Readiness status
  ReadinessStatus status = 1;

  // Timestamp
  google.protobuf.Timestamp timestamp = 2;

  // Readiness checks
  map<string, string> checks = 3;

  // Optional message
  string message = 4;

  // Response metadata
  ResponseMetadata metadata = 5;
}

// Liveness check response
message LivenessCheckResponse {
  // Liveness status
  LivenessStatus status = 1;

  // Timestamp
  google.protobuf.Timestamp timestamp = 2;

  // Service uptime in milliseconds
  int64 uptime = 3;

  // Memory usage information
  MemoryUsage memoryUsage = 4;

  // Optional message
  string message = 5;

  // Response metadata
  ResponseMetadata metadata = 6;
}

// Health status enumeration
enum HealthStatus {
  HEALTH_STATUS_UNKNOWN = 0;
  HEALTH_STATUS_HEALTHY = 1;
  HEALTH_STATUS_UNHEALTHY = 2;
  HEALTH_STATUS_DEGRADED = 3;
}

// Service status enumeration
enum ServiceStatus {
  SERVICE_STATUS_UNKNOWN = 0;
  SERVICE_STATUS_UP = 1;
  SERVICE_STATUS_DOWN = 2;
}

// Readiness status enumeration
enum ReadinessStatus {
  READINESS_STATUS_UNKNOWN = 0;
  READINESS_STATUS_READY = 1;
  READINESS_STATUS_NOT_READY = 2;
}

// Liveness status enumeration
enum LivenessStatus {
  LIVENESS_STATUS_UNKNOWN = 0;
  LIVENESS_STATUS_ALIVE = 1;
  LIVENESS_STATUS_DEAD = 2;
}

// System information
message SystemInfo {
  // Service name
  string service = 1;

  // Service version
  string version = 2;

  // Environment (development, staging, production)
  string environment = 3;

  // Service uptime in milliseconds
  int64 uptime = 4;

  // Timestamp
  google.protobuf.Timestamp timestamp = 5;

  // Node.js version
  string nodeVersion = 6;

  // Memory usage information
  MemoryUsage memoryUsage = 7;

  // CPU usage percentage (optional)
  double cpuUsage = 8;
}

// Service health information
message ServiceHealth {
  // Service name
  string name = 1;

  // Service status
  ServiceStatus status = 2;

  // Response time in milliseconds
  int32 responseTime = 3;

  // Last checked timestamp
  google.protobuf.Timestamp lastChecked = 4;

  // Error message (if any)
  string error = 5;

  // Service URL
  string url = 6;
}

// Memory usage information
message MemoryUsage {
  // Used memory in MB
  int32 used = 1;

  // Total memory in MB
  int32 total = 2;

  // Memory usage percentage
  int32 percentage = 3;
}

// Request metadata
message RequestMetadata {
  // Request ID for tracing
  string requestId = 1;

  // Source IP address
  string sourceIp = 2;

  // User agent
  string userAgent = 3;

  // Timestamp
  google.protobuf.Timestamp timestamp = 4;
}

// Response metadata
message ResponseMetadata {
  // Request ID for tracing
  string requestId = 1;

  // Response timestamp
  google.protobuf.Timestamp timestamp = 2;

  // Processing time in milliseconds
  int32 processingTime = 3;
}
