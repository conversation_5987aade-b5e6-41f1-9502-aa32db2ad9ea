syntax = "proto3";
package aml;

import "google/protobuf/timestamp.proto";

// AML (Anti-Money Laundering) Service Protocol Buffer Definitions
//
// This file defines the gRPC service contract for AML risk evaluation
// operations in the Qeep platform.
//
// Version: 1.0
// Author: Qeep Team

// AML service definition
service AMLService {
  // Risk evaluation operations
  rpc EvaluateTransactionRisk(EvaluateTransactionRiskRequest) returns (EvaluateTransactionRiskResponse);
  rpc EvaluateCustomerRisk(EvaluateCustomerRiskRequest) returns (EvaluateCustomerRiskResponse);
  rpc GetRiskProfile(GetRiskProfileRequest) returns (GetRiskProfileResponse);
  rpc UpdateRiskRules(UpdateRiskRulesRequest) returns (UpdateRiskRulesResponse);
  rpc GetAMLStatus(GetAMLStatusRequest) returns (GetAMLStatusResponse);
}

// Enums
enum RiskLevel {
  RISK_LEVEL_UNKNOWN = 0;
  RISK_LEVEL_LOW = 1;
  RISK_LEVEL_MEDIUM = 2;
  RISK_LEVEL_HIGH = 3;
  RISK_LEVEL_CRITICAL = 4;
}

enum AlertType {
  ALERT_TYPE_UNKNOWN = 0;
  ALERT_TYPE_AMOUNT_THRESHOLD = 1;
  ALERT_TYPE_VELOCITY_CHECK = 2;
  ALERT_TYPE_TIME_ANOMALY = 3;
  ALERT_TYPE_GEOGRAPHIC_ANOMALY = 4;
  ALERT_TYPE_SANCTIONS_MATCH = 5;
  ALERT_TYPE_PEP_MATCH = 6;
  ALERT_TYPE_ADVERSE_MEDIA = 7;
  ALERT_TYPE_COMPLIANCE = 8;
  ALERT_TYPE_PATTERN_DETECTION = 9;
  ALERT_TYPE_BLACKLIST_MATCH = 10;
}

enum AlertSeverity {
  ALERT_SEVERITY_UNKNOWN = 0;
  ALERT_SEVERITY_LOW = 1;
  ALERT_SEVERITY_MEDIUM = 2;
  ALERT_SEVERITY_HIGH = 3;
  ALERT_SEVERITY_CRITICAL = 4;
}

enum TransactionStatus {
  TRANSACTION_STATUS_UNKNOWN = 0;
  TRANSACTION_STATUS_APPROVED = 1;
  TRANSACTION_STATUS_FLAGGED = 2;
  TRANSACTION_STATUS_BLOCKED = 3;
  TRANSACTION_STATUS_PENDING_REVIEW = 4;
}

enum CustomerRiskCategory {
  CUSTOMER_RISK_UNKNOWN = 0;
  CUSTOMER_RISK_LOW = 1;
  CUSTOMER_RISK_MEDIUM = 2;
  CUSTOMER_RISK_HIGH = 3;
  CUSTOMER_RISK_PROHIBITED = 4;
}

// Common messages
message RequestMetadata {
  string requestId = 1;
  string sourceIp = 2;
  string userAgent = 3;
  google.protobuf.Timestamp timestamp = 4;
  string correlationId = 5;
}

message ResponseMetadata {
  string requestId = 1;
  google.protobuf.Timestamp timestamp = 2;
  int32 processingTime = 3;
  string version = 4;
  string correlationId = 5;
}

message ErrorDetails {
  string code = 1;
  string message = 2;
  string details = 3;
}

// Account information for risk evaluation
message AccountInfo {
  string accountId = 1;
  string customerId = 2;
  string accountType = 3;
  string accountNumber = 4;
  double currentBalance = 5;
  string currency = 6;
  google.protobuf.Timestamp accountOpenDate = 7;
  string accountStatus = 8;
  string countryCode = 9;
}

// Customer information for risk evaluation
message CustomerInfo {
  string customerId = 1;
  string customerType = 2;
  string firstName = 3;
  string lastName = 4;
  string businessName = 5;
  string dateOfBirth = 6;
  string nationality = 7;
  string countryOfResidence = 8;
  bool isPoliticallyExposed = 9;
  bool isSanctioned = 10;
  CustomerRiskCategory riskCategory = 11;
  google.protobuf.Timestamp lastKycUpdate = 12;
  string kycStatus = 13;
}

// Transaction context for risk evaluation
message TransactionContext {
  string transactionId = 1;
  double amount = 2;
  string currency = 3;
  string description = 4;
  string transactionType = 5;
  string channel = 6;
  string ipAddress = 7;
  string deviceId = 8;
  string location = 9;
  string countryCode = 10;
  google.protobuf.Timestamp timestamp = 11;
  map<string, string> metadata = 12;
}

// Risk alert generated during evaluation
message RiskAlert {
  AlertType type = 1;
  AlertSeverity severity = 2;
  string message = 3;
  string ruleId = 4;
  string ruleName = 5;
  double confidence = 6;
  map<string, string> details = 7;
}

// Risk evaluation result
message RiskEvaluation {
  double riskScore = 1;
  RiskLevel riskLevel = 2;
  TransactionStatus recommendedStatus = 3;
  repeated RiskAlert alerts = 4;
  string evaluationId = 5;
  google.protobuf.Timestamp evaluatedAt = 6;
  string evaluationModel = 7;
  map<string, double> riskFactors = 8;
}

// Request messages
message EvaluateTransactionRiskRequest {
  string tenantId = 1;
  TransactionContext transaction = 2;
  AccountInfo fromAccount = 3;
  AccountInfo toAccount = 4;
  CustomerInfo fromCustomer = 5;
  CustomerInfo toCustomer = 6;
  RequestMetadata metadata = 7;
  bool includeHistoricalAnalysis = 8;
  bool includePatternDetection = 9;
  repeated string additionalChecks = 10;
}

message EvaluateCustomerRiskRequest {
  string tenantId = 1;
  CustomerInfo customer = 2;
  repeated AccountInfo accounts = 3;
  RequestMetadata metadata = 4;
  bool includeTransactionHistory = 5;
  string evaluationReason = 6;
}

message GetRiskProfileRequest {
  string tenantId = 1;
  string customerId = 2;
  RequestMetadata metadata = 3;
  bool includeRecentAlerts = 4;
  int32 historyDays = 5;
}

message UpdateRiskRulesRequest {
  string tenantId = 1;
  repeated RiskRule rules = 2;
  RequestMetadata metadata = 3;
  bool validateOnly = 4;
}

message GetAMLStatusRequest {
  string tenantId = 1;
  RequestMetadata metadata = 2;
}

// Risk rule definition
message RiskRule {
  string ruleId = 1;
  string ruleName = 2;
  string description = 3;
  AlertType alertType = 4;
  AlertSeverity severity = 5;
  bool isActive = 6;
  map<string, string> parameters = 7;
  double threshold = 8;
  string condition = 9;
}

// Customer risk profile
message CustomerRiskProfile {
  string customerId = 1;
  CustomerRiskCategory riskCategory = 2;
  double overallRiskScore = 3;
  repeated RiskAlert recentAlerts = 4;
  google.protobuf.Timestamp lastEvaluated = 5;
  map<string, double> riskFactors = 6;
  string riskReason = 7;
}

// AML service status
message AMLServiceStatus {
  bool isHealthy = 1;
  string version = 2;
  int32 activeRules = 3;
  int32 evaluationsToday = 4;
  double averageProcessingTime = 5;
  google.protobuf.Timestamp lastRuleUpdate = 6;
}

// Response messages
message EvaluateTransactionRiskResponse {
  bool success = 1;
  RiskEvaluation riskEvaluation = 2;
  string message = 3;
  ErrorDetails error = 4;
  ResponseMetadata metadata = 5;
}

message EvaluateCustomerRiskResponse {
  bool success = 1;
  CustomerRiskProfile riskProfile = 2;
  string message = 3;
  ErrorDetails error = 4;
  ResponseMetadata metadata = 5;
}

message GetRiskProfileResponse {
  bool success = 1;
  CustomerRiskProfile riskProfile = 2;
  string message = 3;
  ErrorDetails error = 4;
  ResponseMetadata metadata = 5;
}

message UpdateRiskRulesResponse {
  bool success = 1;
  int32 rulesUpdated = 2;
  repeated string validationErrors = 3;
  string message = 4;
  ErrorDetails error = 5;
  ResponseMetadata metadata = 6;
}

message GetAMLStatusResponse {
  bool success = 1;
  AMLServiceStatus status = 2;
  string message = 3;
  ErrorDetails error = 4;
  ResponseMetadata metadata = 5;
}
