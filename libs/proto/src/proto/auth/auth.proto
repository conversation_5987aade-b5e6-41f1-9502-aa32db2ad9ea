syntax = "proto3";
package auth;

import "google/protobuf/timestamp.proto";

// Authentication Service Protocol Buffer Definitions
//
// This file defines the gRPC service contract for authentication
// operations in the Qeep platform.
//
// Version: 1.0
// Author: Qeep Team

// Authentication service definition
service AuthService {
  // Authentication operations
  rpc Login(LoginRequest) returns (LoginResponse);
  rpc Logout(LogoutRequest) returns (LogoutResponse);
  rpc RefreshToken(RefreshTokenRequest) returns (RefreshTokenResponse);
  rpc ValidateToken(ValidateTokenRequest) returns (ValidateTokenResponse);

  // User management operations
  rpc RegisterUser(RegisterUserRequest) returns (RegisterUserResponse);
  rpc ResetPassword(ResetPasswordRequest) returns (ResetPasswordResponse);
  rpc ChangePassword(ChangePasswordRequest) returns (ChangePasswordResponse);

  // Session management operations
  rpc CreateUserSession(CreateUserSessionRequest) returns (CreateUserSessionResponse);
  rpc GetUserSessions(GetUserSessionsRequest) returns (GetUserSessionsResponse);
  rpc TerminateUserSession(TerminateUserSessionRequest) returns (TerminateUserSessionResponse);
  rpc TerminateAllUserSessions(TerminateAllUserSessionsRequest) returns (TerminateAllUserSessionsResponse);
}

// Login request message
message LoginRequest {
  string email = 1;
  string password = 2;
  string tenantCode = 3;
  bool rememberMe = 4;
  string deviceId = 5;
  RequestMetadata metadata = 6;
}

// Login response message
message LoginResponse {
  bool success = 1;
  string accessToken = 2;
  string refreshToken = 3;
  int64 expiresIn = 4;
  User user = 5;
  Tenant tenant = 6;
  AuthError error = 7;
  ResponseMetadata metadata = 8;
}

// Logout request message
message LogoutRequest {
  string token = 1;
  string userId = 2;
  string deviceId = 3;
  RequestMetadata metadata = 4;
}

// Logout response message
message LogoutResponse {
  bool success = 1;
  string message = 2;
  AuthError error = 3;
  ResponseMetadata metadata = 4;
}

// Refresh token request message
message RefreshTokenRequest {
  string refreshToken = 1;
  string deviceId = 2;
  RequestMetadata metadata = 3;
}

// Refresh token response message
message RefreshTokenResponse {
  bool success = 1;
  string accessToken = 2;
  string refreshToken = 3;
  int64 expiresIn = 4;
  AuthError error = 5;
  ResponseMetadata metadata = 6;
}

// Validate token request message
message ValidateTokenRequest {
  string token = 1;
  string tenantId = 2;
  RequestMetadata metadata = 3;
}

// Validate token response message
message ValidateTokenResponse {
  bool valid = 1;
  string userId = 2;
  string tenantId = 3;
  repeated string roles = 4;
  repeated string permissions = 5;
  google.protobuf.Timestamp expiresAt = 6;
  AuthError error = 7;
  ResponseMetadata metadata = 8;
}

// Register user request message
message RegisterUserRequest {
  string email = 1;
  string password = 2;
  string firstName = 3;
  string lastName = 4;
  string tenantCode = 5;
  repeated string roles = 6;
  RequestMetadata metadata = 7;
}

// Register user response message
message RegisterUserResponse {
  bool success = 1;
  User user = 2;
  AuthError error = 3;
  ResponseMetadata metadata = 4;
}

// Reset password request message
message ResetPasswordRequest {
  string email = 1;
  string tenantCode = 2;
  RequestMetadata metadata = 3;
}

// Reset password response message
message ResetPasswordResponse {
  bool success = 1;
  string message = 2;
  AuthError error = 3;
  ResponseMetadata metadata = 4;
}

// Change password request message
message ChangePasswordRequest {
  string userId = 1;
  string currentPassword = 2;
  string newPassword = 3;
  RequestMetadata metadata = 4;
}

// Change password response message
message ChangePasswordResponse {
  bool success = 1;
  string message = 2;
  AuthError error = 3;
  ResponseMetadata metadata = 4;
}

// User information message
message User {
  string id = 1;
  string email = 2;
  string firstName = 3;
  string lastName = 4;
  UserStatus status = 5;
  bool isEmailVerified = 6;
  google.protobuf.Timestamp createdAt = 7;
  google.protobuf.Timestamp updatedAt = 8;
  google.protobuf.Timestamp lastLoginAt = 9;
}

// Tenant information message
message Tenant {
  string id = 1;
  string code = 2;
  string name = 3;
  TenantStatus status = 4;
  google.protobuf.Timestamp createdAt = 5;
  google.protobuf.Timestamp updatedAt = 6;
}

// Authentication error message
message AuthError {
  string code = 1;
  string message = 2;
  map<string, string> details = 3;
  string traceId = 4;
}

// User status enumeration
enum UserStatus {
  USER_STATUS_UNKNOWN = 0;
  USER_STATUS_ACTIVE = 1;
  USER_STATUS_INACTIVE = 2;
  USER_STATUS_SUSPENDED = 3;
  USER_STATUS_PENDING = 4;
}

// Tenant status enumeration
enum TenantStatus {
  TENANT_STATUS_UNKNOWN = 0;
  TENANT_STATUS_ACTIVE = 1;
  TENANT_STATUS_INACTIVE = 2;
  TENANT_STATUS_SUSPENDED = 3;
  TENANT_STATUS_TRIAL = 4;
}

// Request metadata
message RequestMetadata {
  string requestId = 1;
  string sourceIp = 2;
  string userAgent = 3;
  google.protobuf.Timestamp timestamp = 4;
}

// Response metadata
message ResponseMetadata {
  string requestId = 1;
  google.protobuf.Timestamp timestamp = 2;
  int32 processingTime = 3;
}

// ============================================================================
// SESSION MANAGEMENT MESSAGES
// ============================================================================

// User session message
message UserSession {
  string id = 1;
  string userId = 2;
  optional string tenantId = 3; // Optional - no longer required for session management
  string sessionToken = 4;
  string deviceId = 5;
  string ipAddress = 6;
  string userAgent = 7;
  bool isActive = 8;
  google.protobuf.Timestamp expiresAt = 9;
  google.protobuf.Timestamp createdAt = 10;
  google.protobuf.Timestamp updatedAt = 11;
}

// Create user session request
message CreateUserSessionRequest {
  string userId = 1;
  optional string tenantId = 2; // Optional - no longer required for session management
  string sessionToken = 3;
  string deviceId = 4;
  string ipAddress = 5;
  string userAgent = 6;
  google.protobuf.Timestamp expiresAt = 7;
  RequestMetadata metadata = 8;
}

// Create user session response
message CreateUserSessionResponse {
  bool success = 1;
  UserSession session = 2;
  AuthError error = 3;
  ResponseMetadata metadata = 4;
}

// Get user sessions request
message GetUserSessionsRequest {
  string userId = 1;
  bool activeOnly = 2;
  RequestMetadata metadata = 3;
}

// Get user sessions response
message GetUserSessionsResponse {
  bool success = 1;
  repeated UserSession sessions = 2;
  AuthError error = 3;
  ResponseMetadata metadata = 4;
}

// Terminate user session request
message TerminateUserSessionRequest {
  string userId = 1;
  string sessionId = 2;
  RequestMetadata metadata = 3;
}

// Terminate user session response
message TerminateUserSessionResponse {
  bool success = 1;
  AuthError error = 2;
  ResponseMetadata metadata = 3;
}

// Terminate all user sessions request
message TerminateAllUserSessionsRequest {
  string userId = 1;
  RequestMetadata metadata = 2;
}

// Terminate all user sessions response
message TerminateAllUserSessionsResponse {
  bool success = 1;
  int32 sessionsTerminated = 2;
  AuthError error = 3;
  ResponseMetadata metadata = 4;
}
