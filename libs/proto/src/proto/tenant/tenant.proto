syntax = "proto3";
package tenant;

import "google/protobuf/timestamp.proto";

// Tenant Service Protocol Buffer Definitions
//
// This file defines the gRPC service contract for tenant management
// operations in the Qeep platform.
//
// Version: 1.0
// Author: Qeep Team

// Tenant service definition
service TenantService {
  // Tenant management operations
  rpc CreateTenant(CreateTenantRequest) returns (CreateTenantResponse);
  rpc GetTenant(GetTenantRequest) returns (GetTenantResponse);
  rpc GetTenantByCode(GetTenantByCodeRequest) returns (GetTenantByCodeResponse);
  rpc ValidateTenantCode(ValidateTenantCodeRequest) returns (ValidateTenantCodeResponse);
  rpc UpdateTenant(UpdateTenantRequest) returns (UpdateTenantResponse);
}

// Create tenant request message
message CreateTenantRequest {
  string code = 1;
  string name = 2;
  string description = 3;
  RequestMetadata metadata = 4;
}

// Create tenant response message
message CreateTenantResponse {
  bool success = 1;
  Tenant tenant = 2;
  TenantError error = 3;
  ResponseMetadata metadata = 4;
}

// Get tenant request message
message GetTenantRequest {
  string tenantId = 1;
  RequestMetadata metadata = 2;
}

// Get tenant response message
message GetTenantResponse {
  bool success = 1;
  Tenant tenant = 2;
  TenantError error = 3;
  ResponseMetadata metadata = 4;
}

// Get tenant by code request message
message GetTenantByCodeRequest {
  string code = 1;
  RequestMetadata metadata = 2;
}

// Get tenant by code response message
message GetTenantByCodeResponse {
  bool success = 1;
  Tenant tenant = 2;
  TenantError error = 3;
  ResponseMetadata metadata = 4;
}

// Validate tenant code request message
message ValidateTenantCodeRequest {
  string code = 1;
  RequestMetadata metadata = 2;
}

// Validate tenant code response message
message ValidateTenantCodeResponse {
  bool valid = 1;
  Tenant tenant = 2;
  TenantError error = 3;
  ResponseMetadata metadata = 4;
}

// Update tenant request message
message UpdateTenantRequest {
  string tenantId = 1;
  string name = 2;
  string description = 3;
  TenantStatus status = 4;
  RequestMetadata metadata = 5;
}

// Update tenant response message
message UpdateTenantResponse {
  bool success = 1;
  Tenant tenant = 2;
  TenantError error = 3;
  ResponseMetadata metadata = 4;
}

// Tenant information message
message Tenant {
  string id = 1;
  string code = 2;
  string name = 3;
  string description = 4;
  TenantStatus status = 5;
  google.protobuf.Timestamp createdAt = 6;
  google.protobuf.Timestamp updatedAt = 7;
}

// Tenant error message
message TenantError {
  string code = 1;
  string message = 2;
  map<string, string> details = 3;
  string traceId = 4;
}

// Tenant status enumeration
enum TenantStatus {
  TENANT_STATUS_UNKNOWN = 0;
  TENANT_STATUS_ACTIVE = 1;
  TENANT_STATUS_INACTIVE = 2;
  TENANT_STATUS_SUSPENDED = 3;
  TENANT_STATUS_TRIAL = 4;
}

// Request metadata
message RequestMetadata {
  string requestId = 1;
  string sourceIp = 2;
  string userAgent = 3;
  google.protobuf.Timestamp timestamp = 4;
}

// Response metadata
message ResponseMetadata {
  string requestId = 1;
  google.protobuf.Timestamp timestamp = 2;
  int32 processingTime = 3;
}
