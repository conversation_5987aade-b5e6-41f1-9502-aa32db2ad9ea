syntax = "proto3";
package user;

import "google/protobuf/timestamp.proto";

// User Service Protocol Buffer Definitions
//
// This file defines the gRPC service contract for user management
// operations in the Qeep platform.
//
// Version: 1.0
// Author: Qeep Team

// User service definition
service UserService {
  // User management operations
  rpc CreateUser(CreateUserRequest) returns (CreateUserResponse);
  rpc GetUser(GetUserRequest) returns (GetUserResponse);
  rpc GetUserByEmail(GetUserByEmailRequest) returns (GetUserByEmailResponse);
  rpc UpdateUser(UpdateUserRequest) returns (UpdateUserResponse);
  rpc UpdateUserLoginInfo(UpdateUserLoginInfoRequest) returns (UpdateUserLoginInfoResponse);
  rpc UpdatePassword(UpdatePasswordRequest) returns (UpdatePasswordResponse);

  // User deletion operations
  rpc SoftDeleteUser(SoftDeleteUserRequest) returns (SoftDeleteUserResponse);
  rpc HardDeleteUser(HardDeleteUserRequest) returns (HardDeleteUserResponse);
  rpc RestoreUser(RestoreUserRequest) returns (RestoreUserResponse);
  rpc GetDeletedUsers(GetDeletedUsersRequest) returns (GetDeletedUsersResponse);

  // Role management operations
  rpc AssignRole(AssignRoleRequest) returns (AssignRoleResponse);
  rpc RevokeRole(RevokeRoleRequest) returns (RevokeRoleResponse);
  rpc GetUserRoles(GetUserRolesRequest) returns (GetUserRolesResponse);
  rpc GetUserPermissions(GetUserPermissionsRequest) returns (GetUserPermissionsResponse);

}

// Create user request message
message CreateUserRequest {
  string email = 1;
  string passwordHash = 2;
  string firstName = 3;
  string lastName = 4;
  string tenantCode = 5; // Optional - null during signup, assigned during onboarding
  RequestMetadata metadata = 6;
  UserStatus status = 7;
  bool isEmailVerified = 8;
}

// Create user response message
message CreateUserResponse {
  bool success = 1;
  User user = 2;
  UserError error = 3;
  ResponseMetadata metadata = 4;
}

// Get user request message
message GetUserRequest {
  string userId = 1;
  RequestMetadata metadata = 2;
}

// Get user response message
message GetUserResponse {
  bool success = 1;
  User user = 2;
  UserError error = 3;
  ResponseMetadata metadata = 4;
}

// Get user by email request message
message GetUserByEmailRequest {
  string email = 1;
  optional string tenantCode = 2; // Optional tenant code
  RequestMetadata metadata = 3;
}

// Get user by email response message
message GetUserByEmailResponse {
  bool success = 1;
  User user = 2;
  UserError error = 3;
  ResponseMetadata metadata = 4;
}

// Update user request message
message UpdateUserRequest {
  string userId = 1;
  string firstName = 2;
  string lastName = 3;
  UserStatus status = 4;
  RequestMetadata metadata = 5;
  bool isEmailVerified = 6;
}

// Update user response message
message UpdateUserResponse {
  bool success = 1;
  User user = 2;
  UserError error = 3;
  ResponseMetadata metadata = 4;
}

// Update user login info request message
message UpdateUserLoginInfoRequest {
  string userId = 1;
  string ipAddress = 2;
  string userAgent = 3;
  RequestMetadata metadata = 4;
}

// Update user login info response message
message UpdateUserLoginInfoResponse {
  bool success = 1;
  UserError error = 2;
  ResponseMetadata metadata = 3;
}

// Update password request message
message UpdatePasswordRequest {
  string userId = 1;
  string passwordHash = 2;
  RequestMetadata metadata = 3;
}

// Update password response message
message UpdatePasswordResponse {
  bool success = 1;
  UserError error = 2;
  ResponseMetadata metadata = 3;
}

// User information message
message User {
  string id = 1;
  string email = 2;
  string passwordHash = 3;
  string firstName = 4;
  string lastName = 5;
  string tenantCode = 6; // Optional - null until tenant assignment
  UserStatus status = 7;
  bool isEmailVerified = 8;
  google.protobuf.Timestamp createdAt = 9;
  google.protobuf.Timestamp updatedAt = 10;
  google.protobuf.Timestamp lastLoginAt = 11;
  string lastLoginIp = 12;
  string lastLoginUserAgent = 13;
  int32 loginAttempts = 14;
  google.protobuf.Timestamp lockedUntil = 15;
}

// User error message
message UserError {
  string code = 1;
  string message = 2;
  map<string, string> details = 3;
  string traceId = 4;
}

// User status enumeration
enum UserStatus {
  USER_STATUS_UNKNOWN = 0;
  USER_STATUS_ACTIVE = 1;
  USER_STATUS_INACTIVE = 2;
  USER_STATUS_SUSPENDED = 3;
  USER_STATUS_PENDING = 4;
}

// Request metadata
message RequestMetadata {
  string requestId = 1;
  string sourceIp = 2;
  string userAgent = 3;
  google.protobuf.Timestamp timestamp = 4;
}

// Response metadata
message ResponseMetadata {
  string requestId = 1;
  google.protobuf.Timestamp timestamp = 2;
  int32 processingTime = 3;
}

// Soft delete user request
message SoftDeleteUserRequest {
  string userId = 1;
  string deletedBy = 2; // Optional - who is deleting the user
  RequestMetadata metadata = 3;
}

// Soft delete user response
message SoftDeleteUserResponse {
  bool success = 1;
  UserError error = 2;
  ResponseMetadata metadata = 3;
}

// Hard delete user request
message HardDeleteUserRequest {
  string userId = 1;
  string deletedBy = 2; // Optional - who is deleting the user
  RequestMetadata metadata = 3;
}

// Hard delete user response
message HardDeleteUserResponse {
  bool success = 1;
  UserError error = 2;
  ResponseMetadata metadata = 3;
}

// Restore user request
message RestoreUserRequest {
  string userId = 1;
  string restoredBy = 2; // Optional - who is restoring the user
  RequestMetadata metadata = 3;
}

// Restore user response
message RestoreUserResponse {
  bool success = 1;
  User user = 2;
  UserError error = 3;
  ResponseMetadata metadata = 4;
}

// Get deleted users request
message GetDeletedUsersRequest {
  int32 limit = 1; // Default 50
  int32 offset = 2; // Default 0
  RequestMetadata metadata = 3;
}

// Get deleted users response
message GetDeletedUsersResponse {
  bool success = 1;
  repeated User users = 2;
  int32 totalCount = 3;
  UserError error = 4;
  ResponseMetadata metadata = 5;
}

// ============================================================================
// ROLE MANAGEMENT MESSAGES
// ============================================================================

// Role message
message Role {
  string id = 1;
  string name = 2;
  string description = 3;
  bool isSystemRole = 4;
  google.protobuf.Timestamp createdAt = 5;
  google.protobuf.Timestamp updatedAt = 6;
}

// Assign role request
message AssignRoleRequest {
  string userId = 1;
  string roleName = 2;
  optional string assignedBy = 3;
  RequestMetadata metadata = 4;
}

// Assign role response
message AssignRoleResponse {
  bool success = 1;
  UserError error = 2;
  ResponseMetadata metadata = 3;
}

// Revoke role request
message RevokeRoleRequest {
  string userId = 1;
  string roleName = 2;
  optional string revokedBy = 3;
  RequestMetadata metadata = 4;
}

// Revoke role response
message RevokeRoleResponse {
  bool success = 1;
  UserError error = 2;
  ResponseMetadata metadata = 3;
}

// Get user roles request
message GetUserRolesRequest {
  string userId = 1;
  RequestMetadata metadata = 2;
}

// Get user roles response
message GetUserRolesResponse {
  bool success = 1;
  repeated Role roles = 2;
  UserError error = 3;
  ResponseMetadata metadata = 4;
}

// Get user permissions request
message GetUserPermissionsRequest {
  string userId = 1;
  RequestMetadata metadata = 2;
}

// Get user permissions response
message GetUserPermissionsResponse {
  bool success = 1;
  repeated string permissions = 2;
  UserError error = 3;
  ResponseMetadata metadata = 4;
}
