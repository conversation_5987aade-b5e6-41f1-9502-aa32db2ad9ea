syntax = "proto3";
package notification;

import "google/protobuf/timestamp.proto";

// Notification Service Protocol Buffer Definitions
//
// This file defines the gRPC service contract for notification management
// operations in the Qeep platform.
//
// Version: 1.0
// Author: Qeep Team

// Notification service definition
service NotificationService {
  // Email operations
  rpc SendEmail(SendEmailRequest) returns (SendEmailResponse);
  rpc SendBulkEmail(SendBulkEmailRequest) returns (SendBulkEmailResponse);
  rpc GetEmailStatus(GetEmailStatusRequest) returns (GetEmailStatusResponse);
  rpc GetEmailHistory(GetEmailHistoryRequest) returns (GetEmailHistoryResponse);
  
  // Template operations
  rpc GetEmailTemplate(GetEmailTemplateRequest) returns (GetEmailTemplateResponse);
  rpc CreateEmailTemplate(CreateEmailTemplateRequest) returns (CreateEmailTemplateResponse);
}

// Send email request message
message SendEmailRequest {
  string recipientEmail = 1;
  string recipientName = 2;
  string subject = 3;
  string templateSlug = 4;
  map<string, string> templateData = 5;
  string tenantCode = 6;
  RequestMetadata metadata = 7;
}

// Send email response message
message SendEmailResponse {
  bool success = 1;
  string notificationId = 2;
  string message = 3;
  NotificationError error = 4;
  ResponseMetadata metadata = 5;
}

// Send bulk email request message
message SendBulkEmailRequest {
  repeated EmailRecipient recipients = 1;
  string subject = 2;
  string templateSlug = 3;
  map<string, string> templateData = 4;
  string tenantCode = 5;
  RequestMetadata metadata = 6;
}

// Send bulk email response message
message SendBulkEmailResponse {
  bool success = 1;
  repeated string notificationIds = 2;
  string message = 3;
  NotificationError error = 4;
  ResponseMetadata metadata = 5;
}

// Get email status request message
message GetEmailStatusRequest {
  string notificationId = 1;
  RequestMetadata metadata = 2;
}

// Get email status response message
message GetEmailStatusResponse {
  bool success = 1;
  EmailStatus status = 2;
  NotificationError error = 3;
  ResponseMetadata metadata = 4;
}

// Get email history request message
message GetEmailHistoryRequest {
  string recipientId = 1;
  int32 limit = 2;
  int32 offset = 3;
  RequestMetadata metadata = 4;
}

// Get email history response message
message GetEmailHistoryResponse {
  bool success = 1;
  repeated EmailNotification notifications = 2;
  int32 totalCount = 3;
  NotificationError error = 4;
  ResponseMetadata metadata = 5;
}

// Get email template request message
message GetEmailTemplateRequest {
  string templateSlug = 1;
  string tenantCode = 2;
  RequestMetadata metadata = 3;
}

// Get email template response message
message GetEmailTemplateResponse {
  bool success = 1;
  EmailTemplate template = 2;
  NotificationError error = 3;
  ResponseMetadata metadata = 4;
}

// Create email template request message
message CreateEmailTemplateRequest {
  string slug = 1;
  string name = 2;
  string description = 3;
  string subject = 4;
  string htmlContent = 5;
  string textContent = 6;
  string category = 7;
  string tenantCode = 8;
  RequestMetadata metadata = 9;
}

// Create email template response message
message CreateEmailTemplateResponse {
  bool success = 1;
  EmailTemplate template = 2;
  NotificationError error = 3;
  ResponseMetadata metadata = 4;
}

// Email recipient message
message EmailRecipient {
  string email = 1;
  string name = 2;
  map<string, string> personalData = 3;
}

// Email status message
message EmailStatus {
  string notificationId = 1;
  string status = 2;
  string recipientEmail = 3;
  google.protobuf.Timestamp sentAt = 4;
  google.protobuf.Timestamp deliveredAt = 5;
  string errorMessage = 6;
}

// Email notification message
message EmailNotification {
  string notificationId = 1;
  string recipientEmail = 2;
  string subject = 3;
  string status = 4;
  string templateSlug = 5;
  google.protobuf.Timestamp createdAt = 6;
  google.protobuf.Timestamp sentAt = 7;
}

// Email template message
message EmailTemplate {
  string id = 1;
  string slug = 2;
  string name = 3;
  string description = 4;
  string subject = 5;
  string htmlContent = 6;
  string textContent = 7;
  string category = 8;
  string status = 9;
  string tenantCode = 10;
  google.protobuf.Timestamp createdAt = 11;
  google.protobuf.Timestamp updatedAt = 12;
}

// Notification error message
message NotificationError {
  string code = 1;
  string message = 2;
  string details = 3;
}

// Request metadata message
message RequestMetadata {
  string requestId = 1;
  string userId = 2;
  string tenantCode = 3;
  google.protobuf.Timestamp timestamp = 4;
}

// Response metadata message
message ResponseMetadata {
  string requestId = 1;
  google.protobuf.Timestamp timestamp = 2;
  string version = 3;
}
