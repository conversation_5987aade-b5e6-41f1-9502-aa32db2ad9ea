// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: auth/auth.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import type { handleUnaryCall, UntypedServiceImplementation } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { wrappers } from "protobufjs";
import { Observable } from "rxjs";
import { Timestamp } from "../google/protobuf/timestamp";

/** User status enumeration */
export enum UserStatus {
  USER_STATUS_UNKNOWN = "USER_STATUS_UNKNOWN",
  USER_STATUS_ACTIVE = "USER_STATUS_ACTIVE",
  USER_STATUS_INACTIVE = "USER_STATUS_INACTIVE",
  USER_STATUS_SUSPENDED = "USER_STATUS_SUSPENDED",
  USER_STATUS_PENDING = "USER_STATUS_PENDING",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function userStatusFromJSON(object: any): UserStatus {
  switch (object) {
    case 0:
    case "USER_STATUS_UNKNOWN":
      return UserStatus.USER_STATUS_UNKNOWN;
    case 1:
    case "USER_STATUS_ACTIVE":
      return UserStatus.USER_STATUS_ACTIVE;
    case 2:
    case "USER_STATUS_INACTIVE":
      return UserStatus.USER_STATUS_INACTIVE;
    case 3:
    case "USER_STATUS_SUSPENDED":
      return UserStatus.USER_STATUS_SUSPENDED;
    case 4:
    case "USER_STATUS_PENDING":
      return UserStatus.USER_STATUS_PENDING;
    case -1:
    case "UNRECOGNIZED":
    default:
      return UserStatus.UNRECOGNIZED;
  }
}

export function userStatusToNumber(object: UserStatus): number {
  switch (object) {
    case UserStatus.USER_STATUS_UNKNOWN:
      return 0;
    case UserStatus.USER_STATUS_ACTIVE:
      return 1;
    case UserStatus.USER_STATUS_INACTIVE:
      return 2;
    case UserStatus.USER_STATUS_SUSPENDED:
      return 3;
    case UserStatus.USER_STATUS_PENDING:
      return 4;
    case UserStatus.UNRECOGNIZED:
    default:
      return -1;
  }
}

/** Tenant status enumeration */
export enum TenantStatus {
  TENANT_STATUS_UNKNOWN = "TENANT_STATUS_UNKNOWN",
  TENANT_STATUS_ACTIVE = "TENANT_STATUS_ACTIVE",
  TENANT_STATUS_INACTIVE = "TENANT_STATUS_INACTIVE",
  TENANT_STATUS_SUSPENDED = "TENANT_STATUS_SUSPENDED",
  TENANT_STATUS_TRIAL = "TENANT_STATUS_TRIAL",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function tenantStatusFromJSON(object: any): TenantStatus {
  switch (object) {
    case 0:
    case "TENANT_STATUS_UNKNOWN":
      return TenantStatus.TENANT_STATUS_UNKNOWN;
    case 1:
    case "TENANT_STATUS_ACTIVE":
      return TenantStatus.TENANT_STATUS_ACTIVE;
    case 2:
    case "TENANT_STATUS_INACTIVE":
      return TenantStatus.TENANT_STATUS_INACTIVE;
    case 3:
    case "TENANT_STATUS_SUSPENDED":
      return TenantStatus.TENANT_STATUS_SUSPENDED;
    case 4:
    case "TENANT_STATUS_TRIAL":
      return TenantStatus.TENANT_STATUS_TRIAL;
    case -1:
    case "UNRECOGNIZED":
    default:
      return TenantStatus.UNRECOGNIZED;
  }
}

export function tenantStatusToNumber(object: TenantStatus): number {
  switch (object) {
    case TenantStatus.TENANT_STATUS_UNKNOWN:
      return 0;
    case TenantStatus.TENANT_STATUS_ACTIVE:
      return 1;
    case TenantStatus.TENANT_STATUS_INACTIVE:
      return 2;
    case TenantStatus.TENANT_STATUS_SUSPENDED:
      return 3;
    case TenantStatus.TENANT_STATUS_TRIAL:
      return 4;
    case TenantStatus.UNRECOGNIZED:
    default:
      return -1;
  }
}

/** Login request message */
export interface LoginRequest {
  email: string;
  password: string;
  tenantCode: string;
  rememberMe: boolean;
  deviceId: string;
  metadata?: RequestMetadata | undefined;
}

/** Login response message */
export interface LoginResponse {
  success: boolean;
  accessToken: string;
  refreshToken: string;
  expiresIn: string;
  user?: User | undefined;
  tenant?: Tenant | undefined;
  error?: AuthError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Logout request message */
export interface LogoutRequest {
  token: string;
  userId: string;
  deviceId: string;
  metadata?: RequestMetadata | undefined;
}

/** Logout response message */
export interface LogoutResponse {
  success: boolean;
  message: string;
  error?: AuthError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Refresh token request message */
export interface RefreshTokenRequest {
  refreshToken: string;
  deviceId: string;
  metadata?: RequestMetadata | undefined;
}

/** Refresh token response message */
export interface RefreshTokenResponse {
  success: boolean;
  accessToken: string;
  refreshToken: string;
  expiresIn: string;
  error?: AuthError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Validate token request message */
export interface ValidateTokenRequest {
  token: string;
  tenantId: string;
  metadata?: RequestMetadata | undefined;
}

/** Validate token response message */
export interface ValidateTokenResponse {
  valid: boolean;
  userId: string;
  tenantId: string;
  roles: string[];
  permissions: string[];
  expiresAt?: Date | undefined;
  error?: AuthError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Register user request message */
export interface RegisterUserRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  tenantCode: string;
  roles: string[];
  metadata?: RequestMetadata | undefined;
}

/** Register user response message */
export interface RegisterUserResponse {
  success: boolean;
  user?: User | undefined;
  error?: AuthError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Reset password request message */
export interface ResetPasswordRequest {
  email: string;
  tenantCode: string;
  metadata?: RequestMetadata | undefined;
}

/** Reset password response message */
export interface ResetPasswordResponse {
  success: boolean;
  message: string;
  error?: AuthError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Change password request message */
export interface ChangePasswordRequest {
  userId: string;
  currentPassword: string;
  newPassword: string;
  metadata?: RequestMetadata | undefined;
}

/** Change password response message */
export interface ChangePasswordResponse {
  success: boolean;
  message: string;
  error?: AuthError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** User information message */
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  status: UserStatus;
  isEmailVerified: boolean;
  createdAt?: Date | undefined;
  updatedAt?: Date | undefined;
  lastLoginAt?: Date | undefined;
}

/** Tenant information message */
export interface Tenant {
  id: string;
  code: string;
  name: string;
  status: TenantStatus;
  createdAt?: Date | undefined;
  updatedAt?: Date | undefined;
}

/** Authentication error message */
export interface AuthError {
  code: string;
  message: string;
  details: { [key: string]: string };
  traceId: string;
}

export interface AuthError_DetailsEntry {
  key: string;
  value: string;
}

/** Request metadata */
export interface RequestMetadata {
  requestId: string;
  sourceIp: string;
  userAgent: string;
  timestamp?: Date | undefined;
}

/** Response metadata */
export interface ResponseMetadata {
  requestId: string;
  timestamp?: Date | undefined;
  processingTime: number;
}

/** User session message */
export interface UserSession {
  id: string;
  userId: string;
  /** Optional - no longer required for session management */
  tenantId?: string | undefined;
  sessionToken: string;
  deviceId: string;
  ipAddress: string;
  userAgent: string;
  isActive: boolean;
  expiresAt?: Date | undefined;
  createdAt?: Date | undefined;
  updatedAt?: Date | undefined;
}

/** Create user session request */
export interface CreateUserSessionRequest {
  userId: string;
  /** Optional - no longer required for session management */
  tenantId?: string | undefined;
  sessionToken: string;
  deviceId: string;
  ipAddress: string;
  userAgent: string;
  expiresAt?: Date | undefined;
  metadata?: RequestMetadata | undefined;
}

/** Create user session response */
export interface CreateUserSessionResponse {
  success: boolean;
  session?: UserSession | undefined;
  error?: AuthError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Get user sessions request */
export interface GetUserSessionsRequest {
  userId: string;
  activeOnly: boolean;
  metadata?: RequestMetadata | undefined;
}

/** Get user sessions response */
export interface GetUserSessionsResponse {
  success: boolean;
  sessions: UserSession[];
  error?: AuthError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Terminate user session request */
export interface TerminateUserSessionRequest {
  userId: string;
  sessionId: string;
  metadata?: RequestMetadata | undefined;
}

/** Terminate user session response */
export interface TerminateUserSessionResponse {
  success: boolean;
  error?: AuthError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Terminate all user sessions request */
export interface TerminateAllUserSessionsRequest {
  userId: string;
  metadata?: RequestMetadata | undefined;
}

/** Terminate all user sessions response */
export interface TerminateAllUserSessionsResponse {
  success: boolean;
  sessionsTerminated: number;
  error?: AuthError | undefined;
  metadata?: ResponseMetadata | undefined;
}

wrappers[".google.protobuf.Timestamp"] = {
  fromObject(value: Date) {
    return { seconds: value.getTime() / 1000, nanos: (value.getTime() % 1000) * 1e6 };
  },
  toObject(message: { seconds: number; nanos: number }) {
    return new Date(message.seconds * 1000 + message.nanos / 1e6);
  },
} as any;

function createBaseLoginRequest(): LoginRequest {
  return { email: "", password: "", tenantCode: "", rememberMe: false, deviceId: "" };
}

export const LoginRequest: MessageFns<LoginRequest> = {
  encode(message: LoginRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.email !== "") {
      writer.uint32(10).string(message.email);
    }
    if (message.password !== "") {
      writer.uint32(18).string(message.password);
    }
    if (message.tenantCode !== "") {
      writer.uint32(26).string(message.tenantCode);
    }
    if (message.rememberMe !== false) {
      writer.uint32(32).bool(message.rememberMe);
    }
    if (message.deviceId !== "") {
      writer.uint32(42).string(message.deviceId);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LoginRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLoginRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.email = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.password = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.tenantCode = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.rememberMe = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.deviceId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseLoginResponse(): LoginResponse {
  return { success: false, accessToken: "", refreshToken: "", expiresIn: "0" };
}

export const LoginResponse: MessageFns<LoginResponse> = {
  encode(message: LoginResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.accessToken !== "") {
      writer.uint32(18).string(message.accessToken);
    }
    if (message.refreshToken !== "") {
      writer.uint32(26).string(message.refreshToken);
    }
    if (message.expiresIn !== "0") {
      writer.uint32(32).int64(message.expiresIn);
    }
    if (message.user !== undefined) {
      User.encode(message.user, writer.uint32(42).fork()).join();
    }
    if (message.tenant !== undefined) {
      Tenant.encode(message.tenant, writer.uint32(50).fork()).join();
    }
    if (message.error !== undefined) {
      AuthError.encode(message.error, writer.uint32(58).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(66).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LoginResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLoginResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accessToken = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.refreshToken = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.expiresIn = reader.int64().toString();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.user = User.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.tenant = Tenant.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.error = AuthError.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseLogoutRequest(): LogoutRequest {
  return { token: "", userId: "", deviceId: "" };
}

export const LogoutRequest: MessageFns<LogoutRequest> = {
  encode(message: LogoutRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.token !== "") {
      writer.uint32(10).string(message.token);
    }
    if (message.userId !== "") {
      writer.uint32(18).string(message.userId);
    }
    if (message.deviceId !== "") {
      writer.uint32(26).string(message.deviceId);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LogoutRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLogoutRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.token = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.deviceId = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseLogoutResponse(): LogoutResponse {
  return { success: false, message: "" };
}

export const LogoutResponse: MessageFns<LogoutResponse> = {
  encode(message: LogoutResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.message !== "") {
      writer.uint32(18).string(message.message);
    }
    if (message.error !== undefined) {
      AuthError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LogoutResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLogoutResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = AuthError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseRefreshTokenRequest(): RefreshTokenRequest {
  return { refreshToken: "", deviceId: "" };
}

export const RefreshTokenRequest: MessageFns<RefreshTokenRequest> = {
  encode(message: RefreshTokenRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.refreshToken !== "") {
      writer.uint32(10).string(message.refreshToken);
    }
    if (message.deviceId !== "") {
      writer.uint32(18).string(message.deviceId);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RefreshTokenRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRefreshTokenRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.refreshToken = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.deviceId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseRefreshTokenResponse(): RefreshTokenResponse {
  return { success: false, accessToken: "", refreshToken: "", expiresIn: "0" };
}

export const RefreshTokenResponse: MessageFns<RefreshTokenResponse> = {
  encode(message: RefreshTokenResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.accessToken !== "") {
      writer.uint32(18).string(message.accessToken);
    }
    if (message.refreshToken !== "") {
      writer.uint32(26).string(message.refreshToken);
    }
    if (message.expiresIn !== "0") {
      writer.uint32(32).int64(message.expiresIn);
    }
    if (message.error !== undefined) {
      AuthError.encode(message.error, writer.uint32(42).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RefreshTokenResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRefreshTokenResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accessToken = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.refreshToken = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.expiresIn = reader.int64().toString();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.error = AuthError.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseValidateTokenRequest(): ValidateTokenRequest {
  return { token: "", tenantId: "" };
}

export const ValidateTokenRequest: MessageFns<ValidateTokenRequest> = {
  encode(message: ValidateTokenRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.token !== "") {
      writer.uint32(10).string(message.token);
    }
    if (message.tenantId !== "") {
      writer.uint32(18).string(message.tenantId);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ValidateTokenRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseValidateTokenRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.token = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tenantId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseValidateTokenResponse(): ValidateTokenResponse {
  return { valid: false, userId: "", tenantId: "", roles: [], permissions: [] };
}

export const ValidateTokenResponse: MessageFns<ValidateTokenResponse> = {
  encode(message: ValidateTokenResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.valid !== false) {
      writer.uint32(8).bool(message.valid);
    }
    if (message.userId !== "") {
      writer.uint32(18).string(message.userId);
    }
    if (message.tenantId !== "") {
      writer.uint32(26).string(message.tenantId);
    }
    for (const v of message.roles) {
      writer.uint32(34).string(v!);
    }
    for (const v of message.permissions) {
      writer.uint32(42).string(v!);
    }
    if (message.expiresAt !== undefined) {
      Timestamp.encode(toTimestamp(message.expiresAt), writer.uint32(50).fork()).join();
    }
    if (message.error !== undefined) {
      AuthError.encode(message.error, writer.uint32(58).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(66).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ValidateTokenResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseValidateTokenResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.valid = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.tenantId = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.roles.push(reader.string());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.permissions.push(reader.string());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.expiresAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.error = AuthError.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseRegisterUserRequest(): RegisterUserRequest {
  return { email: "", password: "", firstName: "", lastName: "", tenantCode: "", roles: [] };
}

export const RegisterUserRequest: MessageFns<RegisterUserRequest> = {
  encode(message: RegisterUserRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.email !== "") {
      writer.uint32(10).string(message.email);
    }
    if (message.password !== "") {
      writer.uint32(18).string(message.password);
    }
    if (message.firstName !== "") {
      writer.uint32(26).string(message.firstName);
    }
    if (message.lastName !== "") {
      writer.uint32(34).string(message.lastName);
    }
    if (message.tenantCode !== "") {
      writer.uint32(42).string(message.tenantCode);
    }
    for (const v of message.roles) {
      writer.uint32(50).string(v!);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(58).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RegisterUserRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRegisterUserRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.email = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.password = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.firstName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.lastName = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.tenantCode = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.roles.push(reader.string());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseRegisterUserResponse(): RegisterUserResponse {
  return { success: false };
}

export const RegisterUserResponse: MessageFns<RegisterUserResponse> = {
  encode(message: RegisterUserResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.user !== undefined) {
      User.encode(message.user, writer.uint32(18).fork()).join();
    }
    if (message.error !== undefined) {
      AuthError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RegisterUserResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRegisterUserResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.user = User.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = AuthError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseResetPasswordRequest(): ResetPasswordRequest {
  return { email: "", tenantCode: "" };
}

export const ResetPasswordRequest: MessageFns<ResetPasswordRequest> = {
  encode(message: ResetPasswordRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.email !== "") {
      writer.uint32(10).string(message.email);
    }
    if (message.tenantCode !== "") {
      writer.uint32(18).string(message.tenantCode);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ResetPasswordRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseResetPasswordRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.email = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tenantCode = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseResetPasswordResponse(): ResetPasswordResponse {
  return { success: false, message: "" };
}

export const ResetPasswordResponse: MessageFns<ResetPasswordResponse> = {
  encode(message: ResetPasswordResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.message !== "") {
      writer.uint32(18).string(message.message);
    }
    if (message.error !== undefined) {
      AuthError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ResetPasswordResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseResetPasswordResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = AuthError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseChangePasswordRequest(): ChangePasswordRequest {
  return { userId: "", currentPassword: "", newPassword: "" };
}

export const ChangePasswordRequest: MessageFns<ChangePasswordRequest> = {
  encode(message: ChangePasswordRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.currentPassword !== "") {
      writer.uint32(18).string(message.currentPassword);
    }
    if (message.newPassword !== "") {
      writer.uint32(26).string(message.newPassword);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ChangePasswordRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChangePasswordRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.currentPassword = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.newPassword = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseChangePasswordResponse(): ChangePasswordResponse {
  return { success: false, message: "" };
}

export const ChangePasswordResponse: MessageFns<ChangePasswordResponse> = {
  encode(message: ChangePasswordResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.message !== "") {
      writer.uint32(18).string(message.message);
    }
    if (message.error !== undefined) {
      AuthError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ChangePasswordResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChangePasswordResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = AuthError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUser(): User {
  return {
    id: "",
    email: "",
    firstName: "",
    lastName: "",
    status: UserStatus.USER_STATUS_UNKNOWN,
    isEmailVerified: false,
  };
}

export const User: MessageFns<User> = {
  encode(message: User, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.email !== "") {
      writer.uint32(18).string(message.email);
    }
    if (message.firstName !== "") {
      writer.uint32(26).string(message.firstName);
    }
    if (message.lastName !== "") {
      writer.uint32(34).string(message.lastName);
    }
    if (message.status !== UserStatus.USER_STATUS_UNKNOWN) {
      writer.uint32(40).int32(userStatusToNumber(message.status));
    }
    if (message.isEmailVerified !== false) {
      writer.uint32(48).bool(message.isEmailVerified);
    }
    if (message.createdAt !== undefined) {
      Timestamp.encode(toTimestamp(message.createdAt), writer.uint32(58).fork()).join();
    }
    if (message.updatedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.updatedAt), writer.uint32(66).fork()).join();
    }
    if (message.lastLoginAt !== undefined) {
      Timestamp.encode(toTimestamp(message.lastLoginAt), writer.uint32(74).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): User {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUser();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.email = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.firstName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.lastName = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.status = userStatusFromJSON(reader.int32());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.isEmailVerified = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.createdAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.updatedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.lastLoginAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseTenant(): Tenant {
  return { id: "", code: "", name: "", status: TenantStatus.TENANT_STATUS_UNKNOWN };
}

export const Tenant: MessageFns<Tenant> = {
  encode(message: Tenant, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.code !== "") {
      writer.uint32(18).string(message.code);
    }
    if (message.name !== "") {
      writer.uint32(26).string(message.name);
    }
    if (message.status !== TenantStatus.TENANT_STATUS_UNKNOWN) {
      writer.uint32(32).int32(tenantStatusToNumber(message.status));
    }
    if (message.createdAt !== undefined) {
      Timestamp.encode(toTimestamp(message.createdAt), writer.uint32(42).fork()).join();
    }
    if (message.updatedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.updatedAt), writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Tenant {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTenant();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.code = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.status = tenantStatusFromJSON(reader.int32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.createdAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.updatedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseAuthError(): AuthError {
  return { code: "", message: "", details: {}, traceId: "" };
}

export const AuthError: MessageFns<AuthError> = {
  encode(message: AuthError, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.code !== "") {
      writer.uint32(10).string(message.code);
    }
    if (message.message !== "") {
      writer.uint32(18).string(message.message);
    }
    Object.entries(message.details).forEach(([key, value]) => {
      AuthError_DetailsEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    if (message.traceId !== "") {
      writer.uint32(34).string(message.traceId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AuthError {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAuthError();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.code = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = AuthError_DetailsEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.details[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.traceId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseAuthError_DetailsEntry(): AuthError_DetailsEntry {
  return { key: "", value: "" };
}

export const AuthError_DetailsEntry: MessageFns<AuthError_DetailsEntry> = {
  encode(message: AuthError_DetailsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AuthError_DetailsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAuthError_DetailsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseRequestMetadata(): RequestMetadata {
  return { requestId: "", sourceIp: "", userAgent: "" };
}

export const RequestMetadata: MessageFns<RequestMetadata> = {
  encode(message: RequestMetadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.requestId !== "") {
      writer.uint32(10).string(message.requestId);
    }
    if (message.sourceIp !== "") {
      writer.uint32(18).string(message.sourceIp);
    }
    if (message.userAgent !== "") {
      writer.uint32(26).string(message.userAgent);
    }
    if (message.timestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.timestamp), writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RequestMetadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRequestMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.requestId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.sourceIp = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.userAgent = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.timestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseResponseMetadata(): ResponseMetadata {
  return { requestId: "", processingTime: 0 };
}

export const ResponseMetadata: MessageFns<ResponseMetadata> = {
  encode(message: ResponseMetadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.requestId !== "") {
      writer.uint32(10).string(message.requestId);
    }
    if (message.timestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.timestamp), writer.uint32(18).fork()).join();
    }
    if (message.processingTime !== 0) {
      writer.uint32(24).int32(message.processingTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ResponseMetadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseResponseMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.requestId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.timestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.processingTime = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUserSession(): UserSession {
  return { id: "", userId: "", sessionToken: "", deviceId: "", ipAddress: "", userAgent: "", isActive: false };
}

export const UserSession: MessageFns<UserSession> = {
  encode(message: UserSession, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.userId !== "") {
      writer.uint32(18).string(message.userId);
    }
    if (message.tenantId !== undefined) {
      writer.uint32(26).string(message.tenantId);
    }
    if (message.sessionToken !== "") {
      writer.uint32(34).string(message.sessionToken);
    }
    if (message.deviceId !== "") {
      writer.uint32(42).string(message.deviceId);
    }
    if (message.ipAddress !== "") {
      writer.uint32(50).string(message.ipAddress);
    }
    if (message.userAgent !== "") {
      writer.uint32(58).string(message.userAgent);
    }
    if (message.isActive !== false) {
      writer.uint32(64).bool(message.isActive);
    }
    if (message.expiresAt !== undefined) {
      Timestamp.encode(toTimestamp(message.expiresAt), writer.uint32(74).fork()).join();
    }
    if (message.createdAt !== undefined) {
      Timestamp.encode(toTimestamp(message.createdAt), writer.uint32(82).fork()).join();
    }
    if (message.updatedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.updatedAt), writer.uint32(90).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserSession {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserSession();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.tenantId = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.sessionToken = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.deviceId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.ipAddress = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.userAgent = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.expiresAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.createdAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.updatedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseCreateUserSessionRequest(): CreateUserSessionRequest {
  return { userId: "", sessionToken: "", deviceId: "", ipAddress: "", userAgent: "" };
}

export const CreateUserSessionRequest: MessageFns<CreateUserSessionRequest> = {
  encode(message: CreateUserSessionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.tenantId !== undefined) {
      writer.uint32(18).string(message.tenantId);
    }
    if (message.sessionToken !== "") {
      writer.uint32(26).string(message.sessionToken);
    }
    if (message.deviceId !== "") {
      writer.uint32(34).string(message.deviceId);
    }
    if (message.ipAddress !== "") {
      writer.uint32(42).string(message.ipAddress);
    }
    if (message.userAgent !== "") {
      writer.uint32(50).string(message.userAgent);
    }
    if (message.expiresAt !== undefined) {
      Timestamp.encode(toTimestamp(message.expiresAt), writer.uint32(58).fork()).join();
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(66).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateUserSessionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateUserSessionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tenantId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.sessionToken = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.deviceId = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.ipAddress = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.userAgent = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.expiresAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseCreateUserSessionResponse(): CreateUserSessionResponse {
  return { success: false };
}

export const CreateUserSessionResponse: MessageFns<CreateUserSessionResponse> = {
  encode(message: CreateUserSessionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.session !== undefined) {
      UserSession.encode(message.session, writer.uint32(18).fork()).join();
    }
    if (message.error !== undefined) {
      AuthError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateUserSessionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateUserSessionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.session = UserSession.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = AuthError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetUserSessionsRequest(): GetUserSessionsRequest {
  return { userId: "", activeOnly: false };
}

export const GetUserSessionsRequest: MessageFns<GetUserSessionsRequest> = {
  encode(message: GetUserSessionsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.activeOnly !== false) {
      writer.uint32(16).bool(message.activeOnly);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserSessionsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserSessionsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.activeOnly = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetUserSessionsResponse(): GetUserSessionsResponse {
  return { success: false, sessions: [] };
}

export const GetUserSessionsResponse: MessageFns<GetUserSessionsResponse> = {
  encode(message: GetUserSessionsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    for (const v of message.sessions) {
      UserSession.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.error !== undefined) {
      AuthError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserSessionsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserSessionsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.sessions.push(UserSession.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = AuthError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseTerminateUserSessionRequest(): TerminateUserSessionRequest {
  return { userId: "", sessionId: "" };
}

export const TerminateUserSessionRequest: MessageFns<TerminateUserSessionRequest> = {
  encode(message: TerminateUserSessionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.sessionId !== "") {
      writer.uint32(18).string(message.sessionId);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TerminateUserSessionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTerminateUserSessionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.sessionId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseTerminateUserSessionResponse(): TerminateUserSessionResponse {
  return { success: false };
}

export const TerminateUserSessionResponse: MessageFns<TerminateUserSessionResponse> = {
  encode(message: TerminateUserSessionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.error !== undefined) {
      AuthError.encode(message.error, writer.uint32(18).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TerminateUserSessionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTerminateUserSessionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.error = AuthError.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseTerminateAllUserSessionsRequest(): TerminateAllUserSessionsRequest {
  return { userId: "" };
}

export const TerminateAllUserSessionsRequest: MessageFns<TerminateAllUserSessionsRequest> = {
  encode(message: TerminateAllUserSessionsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TerminateAllUserSessionsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTerminateAllUserSessionsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseTerminateAllUserSessionsResponse(): TerminateAllUserSessionsResponse {
  return { success: false, sessionsTerminated: 0 };
}

export const TerminateAllUserSessionsResponse: MessageFns<TerminateAllUserSessionsResponse> = {
  encode(message: TerminateAllUserSessionsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.sessionsTerminated !== 0) {
      writer.uint32(16).int32(message.sessionsTerminated);
    }
    if (message.error !== undefined) {
      AuthError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TerminateAllUserSessionsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTerminateAllUserSessionsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.sessionsTerminated = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = AuthError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

/** Authentication service definition */

export interface AuthServiceClient {
  /** Authentication operations */

  login(request: LoginRequest): Observable<LoginResponse>;

  logout(request: LogoutRequest): Observable<LogoutResponse>;

  refreshToken(request: RefreshTokenRequest): Observable<RefreshTokenResponse>;

  validateToken(request: ValidateTokenRequest): Observable<ValidateTokenResponse>;

  /** User management operations */

  registerUser(request: RegisterUserRequest): Observable<RegisterUserResponse>;

  resetPassword(request: ResetPasswordRequest): Observable<ResetPasswordResponse>;

  changePassword(request: ChangePasswordRequest): Observable<ChangePasswordResponse>;

  /** Session management operations */

  createUserSession(request: CreateUserSessionRequest): Observable<CreateUserSessionResponse>;

  getUserSessions(request: GetUserSessionsRequest): Observable<GetUserSessionsResponse>;

  terminateUserSession(request: TerminateUserSessionRequest): Observable<TerminateUserSessionResponse>;

  terminateAllUserSessions(request: TerminateAllUserSessionsRequest): Observable<TerminateAllUserSessionsResponse>;
}

/** Authentication service definition */

export interface AuthServiceController {
  /** Authentication operations */

  login(request: LoginRequest): Promise<LoginResponse> | Observable<LoginResponse> | LoginResponse;

  logout(request: LogoutRequest): Promise<LogoutResponse> | Observable<LogoutResponse> | LogoutResponse;

  refreshToken(
    request: RefreshTokenRequest,
  ): Promise<RefreshTokenResponse> | Observable<RefreshTokenResponse> | RefreshTokenResponse;

  validateToken(
    request: ValidateTokenRequest,
  ): Promise<ValidateTokenResponse> | Observable<ValidateTokenResponse> | ValidateTokenResponse;

  /** User management operations */

  registerUser(
    request: RegisterUserRequest,
  ): Promise<RegisterUserResponse> | Observable<RegisterUserResponse> | RegisterUserResponse;

  resetPassword(
    request: ResetPasswordRequest,
  ): Promise<ResetPasswordResponse> | Observable<ResetPasswordResponse> | ResetPasswordResponse;

  changePassword(
    request: ChangePasswordRequest,
  ): Promise<ChangePasswordResponse> | Observable<ChangePasswordResponse> | ChangePasswordResponse;

  /** Session management operations */

  createUserSession(
    request: CreateUserSessionRequest,
  ): Promise<CreateUserSessionResponse> | Observable<CreateUserSessionResponse> | CreateUserSessionResponse;

  getUserSessions(
    request: GetUserSessionsRequest,
  ): Promise<GetUserSessionsResponse> | Observable<GetUserSessionsResponse> | GetUserSessionsResponse;

  terminateUserSession(
    request: TerminateUserSessionRequest,
  ): Promise<TerminateUserSessionResponse> | Observable<TerminateUserSessionResponse> | TerminateUserSessionResponse;

  terminateAllUserSessions(
    request: TerminateAllUserSessionsRequest,
  ):
    | Promise<TerminateAllUserSessionsResponse>
    | Observable<TerminateAllUserSessionsResponse>
    | TerminateAllUserSessionsResponse;
}

export function AuthServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "login",
      "logout",
      "refreshToken",
      "validateToken",
      "registerUser",
      "resetPassword",
      "changePassword",
      "createUserSession",
      "getUserSessions",
      "terminateUserSession",
      "terminateAllUserSessions",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("AuthService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("AuthService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const AUTH_SERVICE_NAME = "AuthService";

/** Authentication service definition */
export type AuthServiceService = typeof AuthServiceService;
export const AuthServiceService = {
  /** Authentication operations */
  login: {
    path: "/auth.AuthService/Login",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: LoginRequest): Buffer => Buffer.from(LoginRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): LoginRequest => LoginRequest.decode(value),
    responseSerialize: (value: LoginResponse): Buffer => Buffer.from(LoginResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): LoginResponse => LoginResponse.decode(value),
  },
  logout: {
    path: "/auth.AuthService/Logout",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: LogoutRequest): Buffer => Buffer.from(LogoutRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): LogoutRequest => LogoutRequest.decode(value),
    responseSerialize: (value: LogoutResponse): Buffer => Buffer.from(LogoutResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): LogoutResponse => LogoutResponse.decode(value),
  },
  refreshToken: {
    path: "/auth.AuthService/RefreshToken",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: RefreshTokenRequest): Buffer => Buffer.from(RefreshTokenRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): RefreshTokenRequest => RefreshTokenRequest.decode(value),
    responseSerialize: (value: RefreshTokenResponse): Buffer =>
      Buffer.from(RefreshTokenResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): RefreshTokenResponse => RefreshTokenResponse.decode(value),
  },
  validateToken: {
    path: "/auth.AuthService/ValidateToken",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: ValidateTokenRequest): Buffer => Buffer.from(ValidateTokenRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): ValidateTokenRequest => ValidateTokenRequest.decode(value),
    responseSerialize: (value: ValidateTokenResponse): Buffer =>
      Buffer.from(ValidateTokenResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): ValidateTokenResponse => ValidateTokenResponse.decode(value),
  },
  /** User management operations */
  registerUser: {
    path: "/auth.AuthService/RegisterUser",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: RegisterUserRequest): Buffer => Buffer.from(RegisterUserRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): RegisterUserRequest => RegisterUserRequest.decode(value),
    responseSerialize: (value: RegisterUserResponse): Buffer =>
      Buffer.from(RegisterUserResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): RegisterUserResponse => RegisterUserResponse.decode(value),
  },
  resetPassword: {
    path: "/auth.AuthService/ResetPassword",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: ResetPasswordRequest): Buffer => Buffer.from(ResetPasswordRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): ResetPasswordRequest => ResetPasswordRequest.decode(value),
    responseSerialize: (value: ResetPasswordResponse): Buffer =>
      Buffer.from(ResetPasswordResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): ResetPasswordResponse => ResetPasswordResponse.decode(value),
  },
  changePassword: {
    path: "/auth.AuthService/ChangePassword",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: ChangePasswordRequest): Buffer =>
      Buffer.from(ChangePasswordRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): ChangePasswordRequest => ChangePasswordRequest.decode(value),
    responseSerialize: (value: ChangePasswordResponse): Buffer =>
      Buffer.from(ChangePasswordResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): ChangePasswordResponse => ChangePasswordResponse.decode(value),
  },
  /** Session management operations */
  createUserSession: {
    path: "/auth.AuthService/CreateUserSession",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: CreateUserSessionRequest): Buffer =>
      Buffer.from(CreateUserSessionRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): CreateUserSessionRequest => CreateUserSessionRequest.decode(value),
    responseSerialize: (value: CreateUserSessionResponse): Buffer =>
      Buffer.from(CreateUserSessionResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): CreateUserSessionResponse => CreateUserSessionResponse.decode(value),
  },
  getUserSessions: {
    path: "/auth.AuthService/GetUserSessions",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetUserSessionsRequest): Buffer =>
      Buffer.from(GetUserSessionsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): GetUserSessionsRequest => GetUserSessionsRequest.decode(value),
    responseSerialize: (value: GetUserSessionsResponse): Buffer =>
      Buffer.from(GetUserSessionsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): GetUserSessionsResponse => GetUserSessionsResponse.decode(value),
  },
  terminateUserSession: {
    path: "/auth.AuthService/TerminateUserSession",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: TerminateUserSessionRequest): Buffer =>
      Buffer.from(TerminateUserSessionRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): TerminateUserSessionRequest => TerminateUserSessionRequest.decode(value),
    responseSerialize: (value: TerminateUserSessionResponse): Buffer =>
      Buffer.from(TerminateUserSessionResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): TerminateUserSessionResponse => TerminateUserSessionResponse.decode(value),
  },
  terminateAllUserSessions: {
    path: "/auth.AuthService/TerminateAllUserSessions",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: TerminateAllUserSessionsRequest): Buffer =>
      Buffer.from(TerminateAllUserSessionsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): TerminateAllUserSessionsRequest =>
      TerminateAllUserSessionsRequest.decode(value),
    responseSerialize: (value: TerminateAllUserSessionsResponse): Buffer =>
      Buffer.from(TerminateAllUserSessionsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): TerminateAllUserSessionsResponse =>
      TerminateAllUserSessionsResponse.decode(value),
  },
} as const;

export interface AuthServiceServer extends UntypedServiceImplementation {
  /** Authentication operations */
  login: handleUnaryCall<LoginRequest, LoginResponse>;
  logout: handleUnaryCall<LogoutRequest, LogoutResponse>;
  refreshToken: handleUnaryCall<RefreshTokenRequest, RefreshTokenResponse>;
  validateToken: handleUnaryCall<ValidateTokenRequest, ValidateTokenResponse>;
  /** User management operations */
  registerUser: handleUnaryCall<RegisterUserRequest, RegisterUserResponse>;
  resetPassword: handleUnaryCall<ResetPasswordRequest, ResetPasswordResponse>;
  changePassword: handleUnaryCall<ChangePasswordRequest, ChangePasswordResponse>;
  /** Session management operations */
  createUserSession: handleUnaryCall<CreateUserSessionRequest, CreateUserSessionResponse>;
  getUserSessions: handleUnaryCall<GetUserSessionsRequest, GetUserSessionsResponse>;
  terminateUserSession: handleUnaryCall<TerminateUserSessionRequest, TerminateUserSessionResponse>;
  terminateAllUserSessions: handleUnaryCall<TerminateAllUserSessionsRequest, TerminateAllUserSessionsResponse>;
}

function toTimestamp(date: Date): Timestamp {
  const seconds = Math.trunc(date.getTime() / 1_000).toString();
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (globalThis.Number(t.seconds) || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
}
