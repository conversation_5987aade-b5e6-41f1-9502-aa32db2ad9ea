// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: user/user.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import type { handleUnaryCall, UntypedServiceImplementation } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { wrappers } from "protobufjs";
import { Observable } from "rxjs";
import { Timestamp } from "../google/protobuf/timestamp";

/** User status enumeration */
export enum UserStatus {
  USER_STATUS_UNKNOWN = "USER_STATUS_UNKNOWN",
  USER_STATUS_ACTIVE = "USER_STATUS_ACTIVE",
  USER_STATUS_INACTIVE = "USER_STATUS_INACTIVE",
  USER_STATUS_SUSPENDED = "USER_STATUS_SUSPENDED",
  USER_STATUS_PENDING = "USER_STATUS_PENDING",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function userStatusFromJSON(object: any): UserStatus {
  switch (object) {
    case 0:
    case "USER_STATUS_UNKNOWN":
      return UserStatus.USER_STATUS_UNKNOWN;
    case 1:
    case "USER_STATUS_ACTIVE":
      return UserStatus.USER_STATUS_ACTIVE;
    case 2:
    case "USER_STATUS_INACTIVE":
      return UserStatus.USER_STATUS_INACTIVE;
    case 3:
    case "USER_STATUS_SUSPENDED":
      return UserStatus.USER_STATUS_SUSPENDED;
    case 4:
    case "USER_STATUS_PENDING":
      return UserStatus.USER_STATUS_PENDING;
    case -1:
    case "UNRECOGNIZED":
    default:
      return UserStatus.UNRECOGNIZED;
  }
}

export function userStatusToNumber(object: UserStatus): number {
  switch (object) {
    case UserStatus.USER_STATUS_UNKNOWN:
      return 0;
    case UserStatus.USER_STATUS_ACTIVE:
      return 1;
    case UserStatus.USER_STATUS_INACTIVE:
      return 2;
    case UserStatus.USER_STATUS_SUSPENDED:
      return 3;
    case UserStatus.USER_STATUS_PENDING:
      return 4;
    case UserStatus.UNRECOGNIZED:
    default:
      return -1;
  }
}

/** Create user request message */
export interface CreateUserRequest {
  email: string;
  passwordHash: string;
  firstName: string;
  lastName: string;
  /** Optional - null during signup, assigned during onboarding */
  tenantCode: string;
  metadata?: RequestMetadata | undefined;
  status: UserStatus;
  isEmailVerified: boolean;
}

/** Create user response message */
export interface CreateUserResponse {
  success: boolean;
  user?: User | undefined;
  error?: UserError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Get user request message */
export interface GetUserRequest {
  userId: string;
  metadata?: RequestMetadata | undefined;
}

/** Get user response message */
export interface GetUserResponse {
  success: boolean;
  user?: User | undefined;
  error?: UserError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Get user by email request message */
export interface GetUserByEmailRequest {
  email: string;
  /** Optional tenant code */
  tenantCode?: string | undefined;
  metadata?: RequestMetadata | undefined;
}

/** Get user by email response message */
export interface GetUserByEmailResponse {
  success: boolean;
  user?: User | undefined;
  error?: UserError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Update user request message */
export interface UpdateUserRequest {
  userId: string;
  firstName: string;
  lastName: string;
  status: UserStatus;
  metadata?: RequestMetadata | undefined;
  isEmailVerified: boolean;
}

/** Update user response message */
export interface UpdateUserResponse {
  success: boolean;
  user?: User | undefined;
  error?: UserError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Update user login info request message */
export interface UpdateUserLoginInfoRequest {
  userId: string;
  ipAddress: string;
  userAgent: string;
  metadata?: RequestMetadata | undefined;
}

/** Update user login info response message */
export interface UpdateUserLoginInfoResponse {
  success: boolean;
  error?: UserError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Update password request message */
export interface UpdatePasswordRequest {
  userId: string;
  passwordHash: string;
  metadata?: RequestMetadata | undefined;
}

/** Update password response message */
export interface UpdatePasswordResponse {
  success: boolean;
  error?: UserError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** User information message */
export interface User {
  id: string;
  email: string;
  passwordHash: string;
  firstName: string;
  lastName: string;
  /** Optional - null until tenant assignment */
  tenantCode: string;
  status: UserStatus;
  isEmailVerified: boolean;
  createdAt?: Date | undefined;
  updatedAt?: Date | undefined;
  lastLoginAt?: Date | undefined;
  lastLoginIp: string;
  lastLoginUserAgent: string;
  loginAttempts: number;
  lockedUntil?: Date | undefined;
}

/** User error message */
export interface UserError {
  code: string;
  message: string;
  details: { [key: string]: string };
  traceId: string;
}

export interface UserError_DetailsEntry {
  key: string;
  value: string;
}

/** Request metadata */
export interface RequestMetadata {
  requestId: string;
  sourceIp: string;
  userAgent: string;
  timestamp?: Date | undefined;
}

/** Response metadata */
export interface ResponseMetadata {
  requestId: string;
  timestamp?: Date | undefined;
  processingTime: number;
}

/** Soft delete user request */
export interface SoftDeleteUserRequest {
  userId: string;
  /** Optional - who is deleting the user */
  deletedBy: string;
  metadata?: RequestMetadata | undefined;
}

/** Soft delete user response */
export interface SoftDeleteUserResponse {
  success: boolean;
  error?: UserError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Hard delete user request */
export interface HardDeleteUserRequest {
  userId: string;
  /** Optional - who is deleting the user */
  deletedBy: string;
  metadata?: RequestMetadata | undefined;
}

/** Hard delete user response */
export interface HardDeleteUserResponse {
  success: boolean;
  error?: UserError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Restore user request */
export interface RestoreUserRequest {
  userId: string;
  /** Optional - who is restoring the user */
  restoredBy: string;
  metadata?: RequestMetadata | undefined;
}

/** Restore user response */
export interface RestoreUserResponse {
  success: boolean;
  user?: User | undefined;
  error?: UserError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Get deleted users request */
export interface GetDeletedUsersRequest {
  /** Default 50 */
  limit: number;
  /** Default 0 */
  offset: number;
  metadata?: RequestMetadata | undefined;
}

/** Get deleted users response */
export interface GetDeletedUsersResponse {
  success: boolean;
  users: User[];
  totalCount: number;
  error?: UserError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Role message */
export interface Role {
  id: string;
  name: string;
  description: string;
  isSystemRole: boolean;
  createdAt?: Date | undefined;
  updatedAt?: Date | undefined;
}

/** Assign role request */
export interface AssignRoleRequest {
  userId: string;
  roleName: string;
  assignedBy?: string | undefined;
  metadata?: RequestMetadata | undefined;
}

/** Assign role response */
export interface AssignRoleResponse {
  success: boolean;
  error?: UserError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Revoke role request */
export interface RevokeRoleRequest {
  userId: string;
  roleName: string;
  revokedBy?: string | undefined;
  metadata?: RequestMetadata | undefined;
}

/** Revoke role response */
export interface RevokeRoleResponse {
  success: boolean;
  error?: UserError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Get user roles request */
export interface GetUserRolesRequest {
  userId: string;
  metadata?: RequestMetadata | undefined;
}

/** Get user roles response */
export interface GetUserRolesResponse {
  success: boolean;
  roles: Role[];
  error?: UserError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Get user permissions request */
export interface GetUserPermissionsRequest {
  userId: string;
  metadata?: RequestMetadata | undefined;
}

/** Get user permissions response */
export interface GetUserPermissionsResponse {
  success: boolean;
  permissions: string[];
  error?: UserError | undefined;
  metadata?: ResponseMetadata | undefined;
}

wrappers[".google.protobuf.Timestamp"] = {
  fromObject(value: Date) {
    return { seconds: value.getTime() / 1000, nanos: (value.getTime() % 1000) * 1e6 };
  },
  toObject(message: { seconds: number; nanos: number }) {
    return new Date(message.seconds * 1000 + message.nanos / 1e6);
  },
} as any;

function createBaseCreateUserRequest(): CreateUserRequest {
  return {
    email: "",
    passwordHash: "",
    firstName: "",
    lastName: "",
    tenantCode: "",
    status: UserStatus.USER_STATUS_UNKNOWN,
    isEmailVerified: false,
  };
}

export const CreateUserRequest: MessageFns<CreateUserRequest> = {
  encode(message: CreateUserRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.email !== "") {
      writer.uint32(10).string(message.email);
    }
    if (message.passwordHash !== "") {
      writer.uint32(18).string(message.passwordHash);
    }
    if (message.firstName !== "") {
      writer.uint32(26).string(message.firstName);
    }
    if (message.lastName !== "") {
      writer.uint32(34).string(message.lastName);
    }
    if (message.tenantCode !== "") {
      writer.uint32(42).string(message.tenantCode);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(50).fork()).join();
    }
    if (message.status !== UserStatus.USER_STATUS_UNKNOWN) {
      writer.uint32(56).int32(userStatusToNumber(message.status));
    }
    if (message.isEmailVerified !== false) {
      writer.uint32(64).bool(message.isEmailVerified);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateUserRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateUserRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.email = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.passwordHash = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.firstName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.lastName = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.tenantCode = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.status = userStatusFromJSON(reader.int32());
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.isEmailVerified = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseCreateUserResponse(): CreateUserResponse {
  return { success: false };
}

export const CreateUserResponse: MessageFns<CreateUserResponse> = {
  encode(message: CreateUserResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.user !== undefined) {
      User.encode(message.user, writer.uint32(18).fork()).join();
    }
    if (message.error !== undefined) {
      UserError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateUserResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateUserResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.user = User.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = UserError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetUserRequest(): GetUserRequest {
  return { userId: "" };
}

export const GetUserRequest: MessageFns<GetUserRequest> = {
  encode(message: GetUserRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetUserResponse(): GetUserResponse {
  return { success: false };
}

export const GetUserResponse: MessageFns<GetUserResponse> = {
  encode(message: GetUserResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.user !== undefined) {
      User.encode(message.user, writer.uint32(18).fork()).join();
    }
    if (message.error !== undefined) {
      UserError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.user = User.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = UserError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetUserByEmailRequest(): GetUserByEmailRequest {
  return { email: "" };
}

export const GetUserByEmailRequest: MessageFns<GetUserByEmailRequest> = {
  encode(message: GetUserByEmailRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.email !== "") {
      writer.uint32(10).string(message.email);
    }
    if (message.tenantCode !== undefined) {
      writer.uint32(18).string(message.tenantCode);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserByEmailRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserByEmailRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.email = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tenantCode = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetUserByEmailResponse(): GetUserByEmailResponse {
  return { success: false };
}

export const GetUserByEmailResponse: MessageFns<GetUserByEmailResponse> = {
  encode(message: GetUserByEmailResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.user !== undefined) {
      User.encode(message.user, writer.uint32(18).fork()).join();
    }
    if (message.error !== undefined) {
      UserError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserByEmailResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserByEmailResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.user = User.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = UserError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUpdateUserRequest(): UpdateUserRequest {
  return { userId: "", firstName: "", lastName: "", status: UserStatus.USER_STATUS_UNKNOWN, isEmailVerified: false };
}

export const UpdateUserRequest: MessageFns<UpdateUserRequest> = {
  encode(message: UpdateUserRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.firstName !== "") {
      writer.uint32(18).string(message.firstName);
    }
    if (message.lastName !== "") {
      writer.uint32(26).string(message.lastName);
    }
    if (message.status !== UserStatus.USER_STATUS_UNKNOWN) {
      writer.uint32(32).int32(userStatusToNumber(message.status));
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(42).fork()).join();
    }
    if (message.isEmailVerified !== false) {
      writer.uint32(48).bool(message.isEmailVerified);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateUserRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateUserRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.firstName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.lastName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.status = userStatusFromJSON(reader.int32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.isEmailVerified = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUpdateUserResponse(): UpdateUserResponse {
  return { success: false };
}

export const UpdateUserResponse: MessageFns<UpdateUserResponse> = {
  encode(message: UpdateUserResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.user !== undefined) {
      User.encode(message.user, writer.uint32(18).fork()).join();
    }
    if (message.error !== undefined) {
      UserError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateUserResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateUserResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.user = User.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = UserError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUpdateUserLoginInfoRequest(): UpdateUserLoginInfoRequest {
  return { userId: "", ipAddress: "", userAgent: "" };
}

export const UpdateUserLoginInfoRequest: MessageFns<UpdateUserLoginInfoRequest> = {
  encode(message: UpdateUserLoginInfoRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.ipAddress !== "") {
      writer.uint32(18).string(message.ipAddress);
    }
    if (message.userAgent !== "") {
      writer.uint32(26).string(message.userAgent);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateUserLoginInfoRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateUserLoginInfoRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.ipAddress = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.userAgent = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUpdateUserLoginInfoResponse(): UpdateUserLoginInfoResponse {
  return { success: false };
}

export const UpdateUserLoginInfoResponse: MessageFns<UpdateUserLoginInfoResponse> = {
  encode(message: UpdateUserLoginInfoResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.error !== undefined) {
      UserError.encode(message.error, writer.uint32(18).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateUserLoginInfoResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateUserLoginInfoResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.error = UserError.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUpdatePasswordRequest(): UpdatePasswordRequest {
  return { userId: "", passwordHash: "" };
}

export const UpdatePasswordRequest: MessageFns<UpdatePasswordRequest> = {
  encode(message: UpdatePasswordRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.passwordHash !== "") {
      writer.uint32(18).string(message.passwordHash);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdatePasswordRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdatePasswordRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.passwordHash = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUpdatePasswordResponse(): UpdatePasswordResponse {
  return { success: false };
}

export const UpdatePasswordResponse: MessageFns<UpdatePasswordResponse> = {
  encode(message: UpdatePasswordResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.error !== undefined) {
      UserError.encode(message.error, writer.uint32(18).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdatePasswordResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdatePasswordResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.error = UserError.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUser(): User {
  return {
    id: "",
    email: "",
    passwordHash: "",
    firstName: "",
    lastName: "",
    tenantCode: "",
    status: UserStatus.USER_STATUS_UNKNOWN,
    isEmailVerified: false,
    lastLoginIp: "",
    lastLoginUserAgent: "",
    loginAttempts: 0,
  };
}

export const User: MessageFns<User> = {
  encode(message: User, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.email !== "") {
      writer.uint32(18).string(message.email);
    }
    if (message.passwordHash !== "") {
      writer.uint32(26).string(message.passwordHash);
    }
    if (message.firstName !== "") {
      writer.uint32(34).string(message.firstName);
    }
    if (message.lastName !== "") {
      writer.uint32(42).string(message.lastName);
    }
    if (message.tenantCode !== "") {
      writer.uint32(50).string(message.tenantCode);
    }
    if (message.status !== UserStatus.USER_STATUS_UNKNOWN) {
      writer.uint32(56).int32(userStatusToNumber(message.status));
    }
    if (message.isEmailVerified !== false) {
      writer.uint32(64).bool(message.isEmailVerified);
    }
    if (message.createdAt !== undefined) {
      Timestamp.encode(toTimestamp(message.createdAt), writer.uint32(74).fork()).join();
    }
    if (message.updatedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.updatedAt), writer.uint32(82).fork()).join();
    }
    if (message.lastLoginAt !== undefined) {
      Timestamp.encode(toTimestamp(message.lastLoginAt), writer.uint32(90).fork()).join();
    }
    if (message.lastLoginIp !== "") {
      writer.uint32(98).string(message.lastLoginIp);
    }
    if (message.lastLoginUserAgent !== "") {
      writer.uint32(106).string(message.lastLoginUserAgent);
    }
    if (message.loginAttempts !== 0) {
      writer.uint32(112).int32(message.loginAttempts);
    }
    if (message.lockedUntil !== undefined) {
      Timestamp.encode(toTimestamp(message.lockedUntil), writer.uint32(122).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): User {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUser();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.email = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.passwordHash = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.firstName = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.lastName = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.tenantCode = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.status = userStatusFromJSON(reader.int32());
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.isEmailVerified = reader.bool();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.createdAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.updatedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.lastLoginAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.lastLoginIp = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.lastLoginUserAgent = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.loginAttempts = reader.int32();
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.lockedUntil = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUserError(): UserError {
  return { code: "", message: "", details: {}, traceId: "" };
}

export const UserError: MessageFns<UserError> = {
  encode(message: UserError, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.code !== "") {
      writer.uint32(10).string(message.code);
    }
    if (message.message !== "") {
      writer.uint32(18).string(message.message);
    }
    Object.entries(message.details).forEach(([key, value]) => {
      UserError_DetailsEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    if (message.traceId !== "") {
      writer.uint32(34).string(message.traceId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserError {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserError();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.code = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = UserError_DetailsEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.details[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.traceId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUserError_DetailsEntry(): UserError_DetailsEntry {
  return { key: "", value: "" };
}

export const UserError_DetailsEntry: MessageFns<UserError_DetailsEntry> = {
  encode(message: UserError_DetailsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserError_DetailsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserError_DetailsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseRequestMetadata(): RequestMetadata {
  return { requestId: "", sourceIp: "", userAgent: "" };
}

export const RequestMetadata: MessageFns<RequestMetadata> = {
  encode(message: RequestMetadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.requestId !== "") {
      writer.uint32(10).string(message.requestId);
    }
    if (message.sourceIp !== "") {
      writer.uint32(18).string(message.sourceIp);
    }
    if (message.userAgent !== "") {
      writer.uint32(26).string(message.userAgent);
    }
    if (message.timestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.timestamp), writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RequestMetadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRequestMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.requestId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.sourceIp = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.userAgent = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.timestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseResponseMetadata(): ResponseMetadata {
  return { requestId: "", processingTime: 0 };
}

export const ResponseMetadata: MessageFns<ResponseMetadata> = {
  encode(message: ResponseMetadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.requestId !== "") {
      writer.uint32(10).string(message.requestId);
    }
    if (message.timestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.timestamp), writer.uint32(18).fork()).join();
    }
    if (message.processingTime !== 0) {
      writer.uint32(24).int32(message.processingTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ResponseMetadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseResponseMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.requestId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.timestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.processingTime = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseSoftDeleteUserRequest(): SoftDeleteUserRequest {
  return { userId: "", deletedBy: "" };
}

export const SoftDeleteUserRequest: MessageFns<SoftDeleteUserRequest> = {
  encode(message: SoftDeleteUserRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.deletedBy !== "") {
      writer.uint32(18).string(message.deletedBy);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SoftDeleteUserRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSoftDeleteUserRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.deletedBy = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseSoftDeleteUserResponse(): SoftDeleteUserResponse {
  return { success: false };
}

export const SoftDeleteUserResponse: MessageFns<SoftDeleteUserResponse> = {
  encode(message: SoftDeleteUserResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.error !== undefined) {
      UserError.encode(message.error, writer.uint32(18).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SoftDeleteUserResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSoftDeleteUserResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.error = UserError.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseHardDeleteUserRequest(): HardDeleteUserRequest {
  return { userId: "", deletedBy: "" };
}

export const HardDeleteUserRequest: MessageFns<HardDeleteUserRequest> = {
  encode(message: HardDeleteUserRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.deletedBy !== "") {
      writer.uint32(18).string(message.deletedBy);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HardDeleteUserRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHardDeleteUserRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.deletedBy = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseHardDeleteUserResponse(): HardDeleteUserResponse {
  return { success: false };
}

export const HardDeleteUserResponse: MessageFns<HardDeleteUserResponse> = {
  encode(message: HardDeleteUserResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.error !== undefined) {
      UserError.encode(message.error, writer.uint32(18).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HardDeleteUserResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHardDeleteUserResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.error = UserError.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseRestoreUserRequest(): RestoreUserRequest {
  return { userId: "", restoredBy: "" };
}

export const RestoreUserRequest: MessageFns<RestoreUserRequest> = {
  encode(message: RestoreUserRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.restoredBy !== "") {
      writer.uint32(18).string(message.restoredBy);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RestoreUserRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRestoreUserRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.restoredBy = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseRestoreUserResponse(): RestoreUserResponse {
  return { success: false };
}

export const RestoreUserResponse: MessageFns<RestoreUserResponse> = {
  encode(message: RestoreUserResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.user !== undefined) {
      User.encode(message.user, writer.uint32(18).fork()).join();
    }
    if (message.error !== undefined) {
      UserError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RestoreUserResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRestoreUserResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.user = User.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = UserError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetDeletedUsersRequest(): GetDeletedUsersRequest {
  return { limit: 0, offset: 0 };
}

export const GetDeletedUsersRequest: MessageFns<GetDeletedUsersRequest> = {
  encode(message: GetDeletedUsersRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.limit !== 0) {
      writer.uint32(8).int32(message.limit);
    }
    if (message.offset !== 0) {
      writer.uint32(16).int32(message.offset);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetDeletedUsersRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetDeletedUsersRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.limit = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.offset = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetDeletedUsersResponse(): GetDeletedUsersResponse {
  return { success: false, users: [], totalCount: 0 };
}

export const GetDeletedUsersResponse: MessageFns<GetDeletedUsersResponse> = {
  encode(message: GetDeletedUsersResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    for (const v of message.users) {
      User.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.totalCount !== 0) {
      writer.uint32(24).int32(message.totalCount);
    }
    if (message.error !== undefined) {
      UserError.encode(message.error, writer.uint32(34).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetDeletedUsersResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetDeletedUsersResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.users.push(User.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.totalCount = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.error = UserError.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseRole(): Role {
  return { id: "", name: "", description: "", isSystemRole: false };
}

export const Role: MessageFns<Role> = {
  encode(message: Role, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.description !== "") {
      writer.uint32(26).string(message.description);
    }
    if (message.isSystemRole !== false) {
      writer.uint32(32).bool(message.isSystemRole);
    }
    if (message.createdAt !== undefined) {
      Timestamp.encode(toTimestamp(message.createdAt), writer.uint32(42).fork()).join();
    }
    if (message.updatedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.updatedAt), writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Role {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRole();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.isSystemRole = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.createdAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.updatedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseAssignRoleRequest(): AssignRoleRequest {
  return { userId: "", roleName: "" };
}

export const AssignRoleRequest: MessageFns<AssignRoleRequest> = {
  encode(message: AssignRoleRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.roleName !== "") {
      writer.uint32(18).string(message.roleName);
    }
    if (message.assignedBy !== undefined) {
      writer.uint32(26).string(message.assignedBy);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AssignRoleRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAssignRoleRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.roleName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.assignedBy = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseAssignRoleResponse(): AssignRoleResponse {
  return { success: false };
}

export const AssignRoleResponse: MessageFns<AssignRoleResponse> = {
  encode(message: AssignRoleResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.error !== undefined) {
      UserError.encode(message.error, writer.uint32(18).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AssignRoleResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAssignRoleResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.error = UserError.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseRevokeRoleRequest(): RevokeRoleRequest {
  return { userId: "", roleName: "" };
}

export const RevokeRoleRequest: MessageFns<RevokeRoleRequest> = {
  encode(message: RevokeRoleRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.roleName !== "") {
      writer.uint32(18).string(message.roleName);
    }
    if (message.revokedBy !== undefined) {
      writer.uint32(26).string(message.revokedBy);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RevokeRoleRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRevokeRoleRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.roleName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.revokedBy = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseRevokeRoleResponse(): RevokeRoleResponse {
  return { success: false };
}

export const RevokeRoleResponse: MessageFns<RevokeRoleResponse> = {
  encode(message: RevokeRoleResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.error !== undefined) {
      UserError.encode(message.error, writer.uint32(18).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RevokeRoleResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRevokeRoleResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.error = UserError.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetUserRolesRequest(): GetUserRolesRequest {
  return { userId: "" };
}

export const GetUserRolesRequest: MessageFns<GetUserRolesRequest> = {
  encode(message: GetUserRolesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserRolesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserRolesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetUserRolesResponse(): GetUserRolesResponse {
  return { success: false, roles: [] };
}

export const GetUserRolesResponse: MessageFns<GetUserRolesResponse> = {
  encode(message: GetUserRolesResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    for (const v of message.roles) {
      Role.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.error !== undefined) {
      UserError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserRolesResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserRolesResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.roles.push(Role.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = UserError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetUserPermissionsRequest(): GetUserPermissionsRequest {
  return { userId: "" };
}

export const GetUserPermissionsRequest: MessageFns<GetUserPermissionsRequest> = {
  encode(message: GetUserPermissionsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserPermissionsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserPermissionsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetUserPermissionsResponse(): GetUserPermissionsResponse {
  return { success: false, permissions: [] };
}

export const GetUserPermissionsResponse: MessageFns<GetUserPermissionsResponse> = {
  encode(message: GetUserPermissionsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    for (const v of message.permissions) {
      writer.uint32(18).string(v!);
    }
    if (message.error !== undefined) {
      UserError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserPermissionsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserPermissionsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.permissions.push(reader.string());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = UserError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

/** User service definition */

export interface UserServiceClient {
  /** User management operations */

  createUser(request: CreateUserRequest): Observable<CreateUserResponse>;

  getUser(request: GetUserRequest): Observable<GetUserResponse>;

  getUserByEmail(request: GetUserByEmailRequest): Observable<GetUserByEmailResponse>;

  updateUser(request: UpdateUserRequest): Observable<UpdateUserResponse>;

  updateUserLoginInfo(request: UpdateUserLoginInfoRequest): Observable<UpdateUserLoginInfoResponse>;

  updatePassword(request: UpdatePasswordRequest): Observable<UpdatePasswordResponse>;

  /** User deletion operations */

  softDeleteUser(request: SoftDeleteUserRequest): Observable<SoftDeleteUserResponse>;

  hardDeleteUser(request: HardDeleteUserRequest): Observable<HardDeleteUserResponse>;

  restoreUser(request: RestoreUserRequest): Observable<RestoreUserResponse>;

  getDeletedUsers(request: GetDeletedUsersRequest): Observable<GetDeletedUsersResponse>;

  /** Role management operations */

  assignRole(request: AssignRoleRequest): Observable<AssignRoleResponse>;

  revokeRole(request: RevokeRoleRequest): Observable<RevokeRoleResponse>;

  getUserRoles(request: GetUserRolesRequest): Observable<GetUserRolesResponse>;

  getUserPermissions(request: GetUserPermissionsRequest): Observable<GetUserPermissionsResponse>;
}

/** User service definition */

export interface UserServiceController {
  /** User management operations */

  createUser(
    request: CreateUserRequest,
  ): Promise<CreateUserResponse> | Observable<CreateUserResponse> | CreateUserResponse;

  getUser(request: GetUserRequest): Promise<GetUserResponse> | Observable<GetUserResponse> | GetUserResponse;

  getUserByEmail(
    request: GetUserByEmailRequest,
  ): Promise<GetUserByEmailResponse> | Observable<GetUserByEmailResponse> | GetUserByEmailResponse;

  updateUser(
    request: UpdateUserRequest,
  ): Promise<UpdateUserResponse> | Observable<UpdateUserResponse> | UpdateUserResponse;

  updateUserLoginInfo(
    request: UpdateUserLoginInfoRequest,
  ): Promise<UpdateUserLoginInfoResponse> | Observable<UpdateUserLoginInfoResponse> | UpdateUserLoginInfoResponse;

  updatePassword(
    request: UpdatePasswordRequest,
  ): Promise<UpdatePasswordResponse> | Observable<UpdatePasswordResponse> | UpdatePasswordResponse;

  /** User deletion operations */

  softDeleteUser(
    request: SoftDeleteUserRequest,
  ): Promise<SoftDeleteUserResponse> | Observable<SoftDeleteUserResponse> | SoftDeleteUserResponse;

  hardDeleteUser(
    request: HardDeleteUserRequest,
  ): Promise<HardDeleteUserResponse> | Observable<HardDeleteUserResponse> | HardDeleteUserResponse;

  restoreUser(
    request: RestoreUserRequest,
  ): Promise<RestoreUserResponse> | Observable<RestoreUserResponse> | RestoreUserResponse;

  getDeletedUsers(
    request: GetDeletedUsersRequest,
  ): Promise<GetDeletedUsersResponse> | Observable<GetDeletedUsersResponse> | GetDeletedUsersResponse;

  /** Role management operations */

  assignRole(
    request: AssignRoleRequest,
  ): Promise<AssignRoleResponse> | Observable<AssignRoleResponse> | AssignRoleResponse;

  revokeRole(
    request: RevokeRoleRequest,
  ): Promise<RevokeRoleResponse> | Observable<RevokeRoleResponse> | RevokeRoleResponse;

  getUserRoles(
    request: GetUserRolesRequest,
  ): Promise<GetUserRolesResponse> | Observable<GetUserRolesResponse> | GetUserRolesResponse;

  getUserPermissions(
    request: GetUserPermissionsRequest,
  ): Promise<GetUserPermissionsResponse> | Observable<GetUserPermissionsResponse> | GetUserPermissionsResponse;
}

export function UserServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "createUser",
      "getUser",
      "getUserByEmail",
      "updateUser",
      "updateUserLoginInfo",
      "updatePassword",
      "softDeleteUser",
      "hardDeleteUser",
      "restoreUser",
      "getDeletedUsers",
      "assignRole",
      "revokeRole",
      "getUserRoles",
      "getUserPermissions",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("UserService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("UserService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const USER_SERVICE_NAME = "UserService";

/** User service definition */
export type UserServiceService = typeof UserServiceService;
export const UserServiceService = {
  /** User management operations */
  createUser: {
    path: "/user.UserService/CreateUser",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: CreateUserRequest): Buffer => Buffer.from(CreateUserRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): CreateUserRequest => CreateUserRequest.decode(value),
    responseSerialize: (value: CreateUserResponse): Buffer => Buffer.from(CreateUserResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): CreateUserResponse => CreateUserResponse.decode(value),
  },
  getUser: {
    path: "/user.UserService/GetUser",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetUserRequest): Buffer => Buffer.from(GetUserRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): GetUserRequest => GetUserRequest.decode(value),
    responseSerialize: (value: GetUserResponse): Buffer => Buffer.from(GetUserResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): GetUserResponse => GetUserResponse.decode(value),
  },
  getUserByEmail: {
    path: "/user.UserService/GetUserByEmail",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetUserByEmailRequest): Buffer =>
      Buffer.from(GetUserByEmailRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): GetUserByEmailRequest => GetUserByEmailRequest.decode(value),
    responseSerialize: (value: GetUserByEmailResponse): Buffer =>
      Buffer.from(GetUserByEmailResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): GetUserByEmailResponse => GetUserByEmailResponse.decode(value),
  },
  updateUser: {
    path: "/user.UserService/UpdateUser",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: UpdateUserRequest): Buffer => Buffer.from(UpdateUserRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): UpdateUserRequest => UpdateUserRequest.decode(value),
    responseSerialize: (value: UpdateUserResponse): Buffer => Buffer.from(UpdateUserResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): UpdateUserResponse => UpdateUserResponse.decode(value),
  },
  updateUserLoginInfo: {
    path: "/user.UserService/UpdateUserLoginInfo",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: UpdateUserLoginInfoRequest): Buffer =>
      Buffer.from(UpdateUserLoginInfoRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): UpdateUserLoginInfoRequest => UpdateUserLoginInfoRequest.decode(value),
    responseSerialize: (value: UpdateUserLoginInfoResponse): Buffer =>
      Buffer.from(UpdateUserLoginInfoResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): UpdateUserLoginInfoResponse => UpdateUserLoginInfoResponse.decode(value),
  },
  updatePassword: {
    path: "/user.UserService/UpdatePassword",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: UpdatePasswordRequest): Buffer =>
      Buffer.from(UpdatePasswordRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): UpdatePasswordRequest => UpdatePasswordRequest.decode(value),
    responseSerialize: (value: UpdatePasswordResponse): Buffer =>
      Buffer.from(UpdatePasswordResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): UpdatePasswordResponse => UpdatePasswordResponse.decode(value),
  },
  /** User deletion operations */
  softDeleteUser: {
    path: "/user.UserService/SoftDeleteUser",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: SoftDeleteUserRequest): Buffer =>
      Buffer.from(SoftDeleteUserRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): SoftDeleteUserRequest => SoftDeleteUserRequest.decode(value),
    responseSerialize: (value: SoftDeleteUserResponse): Buffer =>
      Buffer.from(SoftDeleteUserResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): SoftDeleteUserResponse => SoftDeleteUserResponse.decode(value),
  },
  hardDeleteUser: {
    path: "/user.UserService/HardDeleteUser",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: HardDeleteUserRequest): Buffer =>
      Buffer.from(HardDeleteUserRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): HardDeleteUserRequest => HardDeleteUserRequest.decode(value),
    responseSerialize: (value: HardDeleteUserResponse): Buffer =>
      Buffer.from(HardDeleteUserResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): HardDeleteUserResponse => HardDeleteUserResponse.decode(value),
  },
  restoreUser: {
    path: "/user.UserService/RestoreUser",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: RestoreUserRequest): Buffer => Buffer.from(RestoreUserRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): RestoreUserRequest => RestoreUserRequest.decode(value),
    responseSerialize: (value: RestoreUserResponse): Buffer => Buffer.from(RestoreUserResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): RestoreUserResponse => RestoreUserResponse.decode(value),
  },
  getDeletedUsers: {
    path: "/user.UserService/GetDeletedUsers",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetDeletedUsersRequest): Buffer =>
      Buffer.from(GetDeletedUsersRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): GetDeletedUsersRequest => GetDeletedUsersRequest.decode(value),
    responseSerialize: (value: GetDeletedUsersResponse): Buffer =>
      Buffer.from(GetDeletedUsersResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): GetDeletedUsersResponse => GetDeletedUsersResponse.decode(value),
  },
  /** Role management operations */
  assignRole: {
    path: "/user.UserService/AssignRole",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: AssignRoleRequest): Buffer => Buffer.from(AssignRoleRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): AssignRoleRequest => AssignRoleRequest.decode(value),
    responseSerialize: (value: AssignRoleResponse): Buffer => Buffer.from(AssignRoleResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): AssignRoleResponse => AssignRoleResponse.decode(value),
  },
  revokeRole: {
    path: "/user.UserService/RevokeRole",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: RevokeRoleRequest): Buffer => Buffer.from(RevokeRoleRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): RevokeRoleRequest => RevokeRoleRequest.decode(value),
    responseSerialize: (value: RevokeRoleResponse): Buffer => Buffer.from(RevokeRoleResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): RevokeRoleResponse => RevokeRoleResponse.decode(value),
  },
  getUserRoles: {
    path: "/user.UserService/GetUserRoles",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetUserRolesRequest): Buffer => Buffer.from(GetUserRolesRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): GetUserRolesRequest => GetUserRolesRequest.decode(value),
    responseSerialize: (value: GetUserRolesResponse): Buffer =>
      Buffer.from(GetUserRolesResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): GetUserRolesResponse => GetUserRolesResponse.decode(value),
  },
  getUserPermissions: {
    path: "/user.UserService/GetUserPermissions",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetUserPermissionsRequest): Buffer =>
      Buffer.from(GetUserPermissionsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): GetUserPermissionsRequest => GetUserPermissionsRequest.decode(value),
    responseSerialize: (value: GetUserPermissionsResponse): Buffer =>
      Buffer.from(GetUserPermissionsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): GetUserPermissionsResponse => GetUserPermissionsResponse.decode(value),
  },
} as const;

export interface UserServiceServer extends UntypedServiceImplementation {
  /** User management operations */
  createUser: handleUnaryCall<CreateUserRequest, CreateUserResponse>;
  getUser: handleUnaryCall<GetUserRequest, GetUserResponse>;
  getUserByEmail: handleUnaryCall<GetUserByEmailRequest, GetUserByEmailResponse>;
  updateUser: handleUnaryCall<UpdateUserRequest, UpdateUserResponse>;
  updateUserLoginInfo: handleUnaryCall<UpdateUserLoginInfoRequest, UpdateUserLoginInfoResponse>;
  updatePassword: handleUnaryCall<UpdatePasswordRequest, UpdatePasswordResponse>;
  /** User deletion operations */
  softDeleteUser: handleUnaryCall<SoftDeleteUserRequest, SoftDeleteUserResponse>;
  hardDeleteUser: handleUnaryCall<HardDeleteUserRequest, HardDeleteUserResponse>;
  restoreUser: handleUnaryCall<RestoreUserRequest, RestoreUserResponse>;
  getDeletedUsers: handleUnaryCall<GetDeletedUsersRequest, GetDeletedUsersResponse>;
  /** Role management operations */
  assignRole: handleUnaryCall<AssignRoleRequest, AssignRoleResponse>;
  revokeRole: handleUnaryCall<RevokeRoleRequest, RevokeRoleResponse>;
  getUserRoles: handleUnaryCall<GetUserRolesRequest, GetUserRolesResponse>;
  getUserPermissions: handleUnaryCall<GetUserPermissionsRequest, GetUserPermissionsResponse>;
}

function toTimestamp(date: Date): Timestamp {
  const seconds = Math.trunc(date.getTime() / 1_000).toString();
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (globalThis.Number(t.seconds) || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
}
