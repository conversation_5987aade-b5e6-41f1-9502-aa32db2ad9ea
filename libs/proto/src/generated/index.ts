// Generated TypeScript interfaces from proto files
// This file is automatically updated by scripts/update-proto-exports.js
// Do not edit manually - changes will be overwritten

// Export conflicts detected and resolved with aliases:
// - RiskLevel (found in: aml, customer)
// - riskLevelFromJSON (found in: aml, customer)
// - riskLevelToNumber (found in: aml, customer)
// - RequestMetadata (found in: aml, auth, common, customer, notification, tenant, user)
// - ResponseMetadata (found in: aml, auth, common, customer, notification, tenant, user)
// - ErrorDetails (found in: aml, customer)
// - UserStatus (found in: auth, user)
// - userStatusFromJSON (found in: auth, user)
// - userStatusToNumber (found in: auth, user)
// - TenantStatus (found in: auth, tenant)
// - tenantStatusFromJSON (found in: auth, tenant)
// - tenantStatusToNumber (found in: auth, tenant)
// - User (found in: auth, user)
// - Tenant (found in: auth, tenant)

// Export aml module - with conflict resolution
export {
  AMLServiceClient,
  AMLServiceController,
  AMLServiceControllerMethods,
  AMLServiceServer,
  AMLServiceService,
  AMLServiceStatus,
  AML_SERVICE_NAME,
  AccountInfo,
  AlertSeverity,
  AlertType,
  CustomerInfo,
  CustomerRiskCategory,
  CustomerRiskProfile,
  CustomerRiskProfile_RiskFactorsEntry,
  EvaluateCustomerRiskRequest,
  EvaluateCustomerRiskResponse,
  EvaluateTransactionRiskRequest,
  EvaluateTransactionRiskResponse,
  GetAMLStatusRequest,
  GetAMLStatusResponse,
  GetRiskProfileRequest,
  GetRiskProfileResponse,
  RiskAlert,
  RiskAlert_DetailsEntry,
  RiskEvaluation,
  RiskEvaluation_RiskFactorsEntry,
  RiskRule,
  RiskRule_ParametersEntry,
  TransactionContext,
  TransactionContext_MetadataEntry,
  TransactionStatus,
  UpdateRiskRulesRequest,
  UpdateRiskRulesResponse,
  alertSeverityFromJSON,
  alertSeverityToNumber,
  alertTypeFromJSON,
  alertTypeToNumber,
  customerRiskCategoryFromJSON,
  customerRiskCategoryToNumber,
  transactionStatusFromJSON,
  transactionStatusToNumber,
  // Aliased exports to resolve conflicts
  ErrorDetails as AmlErrorDetails,
  RequestMetadata as AmlRequestMetadata,
  ResponseMetadata as AmlResponseMetadata,
  RiskLevel as AmlRiskLevel,
  riskLevelFromJSON as AmlriskLevelFromJSON,
  riskLevelToNumber as AmlriskLevelToNumber
} from './aml/aml';

// Export auth module - with conflict resolution
export {
  AUTH_SERVICE_NAME,
  AuthError,
  AuthError_DetailsEntry,
  AuthServiceClient,
  AuthServiceController,
  AuthServiceControllerMethods,
  AuthServiceServer,
  AuthServiceService,
  ChangePasswordRequest,
  ChangePasswordResponse,
  CreateUserSessionRequest,
  CreateUserSessionResponse,
  GetUserSessionsRequest,
  GetUserSessionsResponse,
  LoginRequest,
  LoginResponse,
  LogoutRequest,
  LogoutResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  RegisterUserRequest,
  RegisterUserResponse,
  ResetPasswordRequest,
  ResetPasswordResponse,
  TerminateAllUserSessionsRequest,
  TerminateAllUserSessionsResponse,
  TerminateUserSessionRequest,
  TerminateUserSessionResponse,
  UserSession,
  ValidateTokenRequest,
  ValidateTokenResponse,
  // Aliased exports to resolve conflicts
  RequestMetadata as AuthRequestMetadata,
  ResponseMetadata as AuthResponseMetadata,
  Tenant as AuthTenant,
  TenantStatus as AuthTenantStatus,
  User as AuthUser,
  UserStatus as AuthUserStatus,
  tenantStatusFromJSON as AuthtenantStatusFromJSON,
  tenantStatusToNumber as AuthtenantStatusToNumber,
  userStatusFromJSON as AuthuserStatusFromJSON,
  userStatusToNumber as AuthuserStatusToNumber
} from './auth/auth';

// Export common module - with conflict resolution
export {
  DetailedHealthCheckResponse,
  DetailedHealthCheckResponse_ChecksEntry,
  HEALTH_SERVICE_NAME,
  HealthCheckRequest,
  HealthCheckResponse,
  HealthServiceClient,
  HealthServiceController,
  HealthServiceControllerMethods,
  HealthServiceServer,
  HealthServiceService,
  HealthStatus,
  LivenessCheckResponse,
  LivenessStatus,
  MemoryUsage,
  ReadinessCheckResponse,
  ReadinessCheckResponse_ChecksEntry,
  ReadinessStatus,
  ServiceHealth,
  ServiceStatus,
  SystemInfo,
  healthStatusFromJSON,
  healthStatusToNumber,
  livenessStatusFromJSON,
  livenessStatusToNumber,
  readinessStatusFromJSON,
  readinessStatusToNumber,
  serviceStatusFromJSON,
  serviceStatusToNumber,
  // Aliased exports to resolve conflicts
  RequestMetadata as CommonRequestMetadata,
  ResponseMetadata as CommonResponseMetadata
} from './common/health';

// Export customer module - with conflict resolution
export {
  AddressType,
  CUSTOMER_SERVICE_NAME,
  CreateCustomerRequest,
  CreateCustomerResponse,
  Customer,
  CustomerAddress,
  CustomerProfileData,
  CustomerServiceClient,
  CustomerServiceController,
  CustomerServiceControllerMethods,
  CustomerServiceServer,
  CustomerServiceService,
  CustomerStatistics,
  CustomerStatistics_CustomersByRiskLevelEntry,
  CustomerStatistics_CustomersByTypeEntry,
  CustomerStatus,
  CustomerType,
  DeleteCustomerRequest,
  DeleteCustomerResponse,
  GenderType,
  GetAllCustomersRequest,
  GetAllCustomersResponse,
  GetCustomerByIdRequest,
  GetCustomerByIdResponse,
  GetCustomerStatsRequest,
  GetCustomerStatsResponse,
  KycStatus,
  PaginationMeta,
  SearchCustomersRequest,
  SearchCustomersResponse,
  SortOrder,
  UpdateCustomerKycStatusRequest,
  UpdateCustomerKycStatusResponse,
  UpdateCustomerRequest,
  UpdateCustomerResponse,
  UpdateCustomerRiskLevelRequest,
  UpdateCustomerRiskLevelResponse,
  UpdateCustomerStatusRequest,
  UpdateCustomerStatusResponse,
  addressTypeFromJSON,
  addressTypeToNumber,
  customerStatusFromJSON,
  customerStatusToNumber,
  customerTypeFromJSON,
  customerTypeToNumber,
  genderTypeFromJSON,
  genderTypeToNumber,
  kycStatusFromJSON,
  kycStatusToNumber,
  sortOrderFromJSON,
  sortOrderToNumber,
  // Aliased exports to resolve conflicts
  ErrorDetails as CustomerErrorDetails,
  RequestMetadata as CustomerRequestMetadata,
  ResponseMetadata as CustomerResponseMetadata,
  RiskLevel as CustomerRiskLevel,
  riskLevelFromJSON as CustomerriskLevelFromJSON,
  riskLevelToNumber as CustomerriskLevelToNumber
} from './customer/customer';

// Export google module
export {
  Timestamp,
} from './google/protobuf/timestamp';

// Export notification module - with conflict resolution
export {
  CreateEmailTemplateRequest,
  CreateEmailTemplateResponse,
  EmailNotification,
  EmailRecipient,
  EmailRecipient_PersonalDataEntry,
  EmailStatus,
  EmailTemplate,
  GetEmailHistoryRequest,
  GetEmailHistoryResponse,
  GetEmailStatusRequest,
  GetEmailStatusResponse,
  GetEmailTemplateRequest,
  GetEmailTemplateResponse,
  NOTIFICATION_SERVICE_NAME,
  NotificationError,
  NotificationServiceClient,
  NotificationServiceController,
  NotificationServiceControllerMethods,
  NotificationServiceServer,
  NotificationServiceService,
  SendBulkEmailRequest,
  SendBulkEmailRequest_TemplateDataEntry,
  SendBulkEmailResponse,
  SendEmailRequest,
  SendEmailRequest_TemplateDataEntry,
  SendEmailResponse,
  // Aliased exports to resolve conflicts
  RequestMetadata as NotificationRequestMetadata,
  ResponseMetadata as NotificationResponseMetadata
} from './notification/notification';

// Export tenant module - with conflict resolution
export {
  CreateTenantRequest,
  CreateTenantResponse,
  GetTenantByCodeRequest,
  GetTenantByCodeResponse,
  GetTenantRequest,
  GetTenantResponse,
  TENANT_SERVICE_NAME,
  TenantError,
  TenantError_DetailsEntry,
  TenantServiceClient,
  TenantServiceController,
  TenantServiceControllerMethods,
  TenantServiceServer,
  TenantServiceService,
  UpdateTenantRequest,
  UpdateTenantResponse,
  ValidateTenantCodeRequest,
  ValidateTenantCodeResponse,
  // Aliased exports to resolve conflicts
  RequestMetadata as TenantRequestMetadata,
  ResponseMetadata as TenantResponseMetadata,
  Tenant as TenantTenant,
  TenantStatus as TenantTenantStatus,
  tenantStatusFromJSON as TenanttenantStatusFromJSON,
  tenantStatusToNumber as TenanttenantStatusToNumber
} from './tenant/tenant';

// Export user module - with conflict resolution
export {
  AssignRoleRequest,
  AssignRoleResponse,
  CreateUserRequest,
  CreateUserResponse,
  GetDeletedUsersRequest,
  GetDeletedUsersResponse,
  GetUserByEmailRequest,
  GetUserByEmailResponse,
  GetUserPermissionsRequest,
  GetUserPermissionsResponse,
  GetUserRequest,
  GetUserResponse,
  GetUserRolesRequest,
  GetUserRolesResponse,
  HardDeleteUserRequest,
  HardDeleteUserResponse,
  RestoreUserRequest,
  RestoreUserResponse,
  RevokeRoleRequest,
  RevokeRoleResponse,
  Role,
  SoftDeleteUserRequest,
  SoftDeleteUserResponse,
  USER_SERVICE_NAME,
  UpdatePasswordRequest,
  UpdatePasswordResponse,
  UpdateUserLoginInfoRequest,
  UpdateUserLoginInfoResponse,
  UpdateUserRequest,
  UpdateUserResponse,
  UserError,
  UserError_DetailsEntry,
  UserServiceClient,
  UserServiceController,
  UserServiceControllerMethods,
  UserServiceServer,
  UserServiceService,
  // Aliased exports to resolve conflicts
  RequestMetadata as UserRequestMetadata,
  ResponseMetadata as UserResponseMetadata,
  User as UserUser,
  UserStatus as UserUserStatus,
  userStatusFromJSON as UseruserStatusFromJSON,
  userStatusToNumber as UseruserStatusToNumber
} from './user/user';

// Note: The following generated index files are excluded to prevent conflicts:
// - index.aml.ts
// - index.auth.ts
// - index.common.ts
// - index.customer.ts
// - index.google.protobuf.ts
// - index.google.ts
// - index.notification.ts
// - index.tenant.ts
// - index.user.ts
// All necessary exports are handled explicitly above
