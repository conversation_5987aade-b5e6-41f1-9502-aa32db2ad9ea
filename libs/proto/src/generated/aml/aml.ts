// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: aml/aml.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import type { handleUnaryCall, UntypedServiceImplementation } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { wrappers } from "protobufjs";
import { Observable } from "rxjs";
import { Timestamp } from "../google/protobuf/timestamp";

/** Enums */
export enum RiskLevel {
  RISK_LEVEL_UNKNOWN = "RISK_LEVEL_UNKNOWN",
  RISK_LEVEL_LOW = "RISK_LEVEL_LOW",
  RISK_LEVEL_MEDIUM = "RISK_LEVEL_MEDIUM",
  RISK_LEVEL_HIGH = "RISK_LEVEL_HIGH",
  RISK_LEVEL_CRITICAL = "RISK_LEVEL_CRITICAL",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function riskLevelFromJSON(object: any): RiskLevel {
  switch (object) {
    case 0:
    case "RISK_LEVEL_UNKNOWN":
      return RiskLevel.RISK_LEVEL_UNKNOWN;
    case 1:
    case "RISK_LEVEL_LOW":
      return RiskLevel.RISK_LEVEL_LOW;
    case 2:
    case "RISK_LEVEL_MEDIUM":
      return RiskLevel.RISK_LEVEL_MEDIUM;
    case 3:
    case "RISK_LEVEL_HIGH":
      return RiskLevel.RISK_LEVEL_HIGH;
    case 4:
    case "RISK_LEVEL_CRITICAL":
      return RiskLevel.RISK_LEVEL_CRITICAL;
    case -1:
    case "UNRECOGNIZED":
    default:
      return RiskLevel.UNRECOGNIZED;
  }
}

export function riskLevelToNumber(object: RiskLevel): number {
  switch (object) {
    case RiskLevel.RISK_LEVEL_UNKNOWN:
      return 0;
    case RiskLevel.RISK_LEVEL_LOW:
      return 1;
    case RiskLevel.RISK_LEVEL_MEDIUM:
      return 2;
    case RiskLevel.RISK_LEVEL_HIGH:
      return 3;
    case RiskLevel.RISK_LEVEL_CRITICAL:
      return 4;
    case RiskLevel.UNRECOGNIZED:
    default:
      return -1;
  }
}

export enum AlertType {
  ALERT_TYPE_UNKNOWN = "ALERT_TYPE_UNKNOWN",
  ALERT_TYPE_AMOUNT_THRESHOLD = "ALERT_TYPE_AMOUNT_THRESHOLD",
  ALERT_TYPE_VELOCITY_CHECK = "ALERT_TYPE_VELOCITY_CHECK",
  ALERT_TYPE_TIME_ANOMALY = "ALERT_TYPE_TIME_ANOMALY",
  ALERT_TYPE_GEOGRAPHIC_ANOMALY = "ALERT_TYPE_GEOGRAPHIC_ANOMALY",
  ALERT_TYPE_SANCTIONS_MATCH = "ALERT_TYPE_SANCTIONS_MATCH",
  ALERT_TYPE_PEP_MATCH = "ALERT_TYPE_PEP_MATCH",
  ALERT_TYPE_ADVERSE_MEDIA = "ALERT_TYPE_ADVERSE_MEDIA",
  ALERT_TYPE_COMPLIANCE = "ALERT_TYPE_COMPLIANCE",
  ALERT_TYPE_PATTERN_DETECTION = "ALERT_TYPE_PATTERN_DETECTION",
  ALERT_TYPE_BLACKLIST_MATCH = "ALERT_TYPE_BLACKLIST_MATCH",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function alertTypeFromJSON(object: any): AlertType {
  switch (object) {
    case 0:
    case "ALERT_TYPE_UNKNOWN":
      return AlertType.ALERT_TYPE_UNKNOWN;
    case 1:
    case "ALERT_TYPE_AMOUNT_THRESHOLD":
      return AlertType.ALERT_TYPE_AMOUNT_THRESHOLD;
    case 2:
    case "ALERT_TYPE_VELOCITY_CHECK":
      return AlertType.ALERT_TYPE_VELOCITY_CHECK;
    case 3:
    case "ALERT_TYPE_TIME_ANOMALY":
      return AlertType.ALERT_TYPE_TIME_ANOMALY;
    case 4:
    case "ALERT_TYPE_GEOGRAPHIC_ANOMALY":
      return AlertType.ALERT_TYPE_GEOGRAPHIC_ANOMALY;
    case 5:
    case "ALERT_TYPE_SANCTIONS_MATCH":
      return AlertType.ALERT_TYPE_SANCTIONS_MATCH;
    case 6:
    case "ALERT_TYPE_PEP_MATCH":
      return AlertType.ALERT_TYPE_PEP_MATCH;
    case 7:
    case "ALERT_TYPE_ADVERSE_MEDIA":
      return AlertType.ALERT_TYPE_ADVERSE_MEDIA;
    case 8:
    case "ALERT_TYPE_COMPLIANCE":
      return AlertType.ALERT_TYPE_COMPLIANCE;
    case 9:
    case "ALERT_TYPE_PATTERN_DETECTION":
      return AlertType.ALERT_TYPE_PATTERN_DETECTION;
    case 10:
    case "ALERT_TYPE_BLACKLIST_MATCH":
      return AlertType.ALERT_TYPE_BLACKLIST_MATCH;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AlertType.UNRECOGNIZED;
  }
}

export function alertTypeToNumber(object: AlertType): number {
  switch (object) {
    case AlertType.ALERT_TYPE_UNKNOWN:
      return 0;
    case AlertType.ALERT_TYPE_AMOUNT_THRESHOLD:
      return 1;
    case AlertType.ALERT_TYPE_VELOCITY_CHECK:
      return 2;
    case AlertType.ALERT_TYPE_TIME_ANOMALY:
      return 3;
    case AlertType.ALERT_TYPE_GEOGRAPHIC_ANOMALY:
      return 4;
    case AlertType.ALERT_TYPE_SANCTIONS_MATCH:
      return 5;
    case AlertType.ALERT_TYPE_PEP_MATCH:
      return 6;
    case AlertType.ALERT_TYPE_ADVERSE_MEDIA:
      return 7;
    case AlertType.ALERT_TYPE_COMPLIANCE:
      return 8;
    case AlertType.ALERT_TYPE_PATTERN_DETECTION:
      return 9;
    case AlertType.ALERT_TYPE_BLACKLIST_MATCH:
      return 10;
    case AlertType.UNRECOGNIZED:
    default:
      return -1;
  }
}

export enum AlertSeverity {
  ALERT_SEVERITY_UNKNOWN = "ALERT_SEVERITY_UNKNOWN",
  ALERT_SEVERITY_LOW = "ALERT_SEVERITY_LOW",
  ALERT_SEVERITY_MEDIUM = "ALERT_SEVERITY_MEDIUM",
  ALERT_SEVERITY_HIGH = "ALERT_SEVERITY_HIGH",
  ALERT_SEVERITY_CRITICAL = "ALERT_SEVERITY_CRITICAL",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function alertSeverityFromJSON(object: any): AlertSeverity {
  switch (object) {
    case 0:
    case "ALERT_SEVERITY_UNKNOWN":
      return AlertSeverity.ALERT_SEVERITY_UNKNOWN;
    case 1:
    case "ALERT_SEVERITY_LOW":
      return AlertSeverity.ALERT_SEVERITY_LOW;
    case 2:
    case "ALERT_SEVERITY_MEDIUM":
      return AlertSeverity.ALERT_SEVERITY_MEDIUM;
    case 3:
    case "ALERT_SEVERITY_HIGH":
      return AlertSeverity.ALERT_SEVERITY_HIGH;
    case 4:
    case "ALERT_SEVERITY_CRITICAL":
      return AlertSeverity.ALERT_SEVERITY_CRITICAL;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AlertSeverity.UNRECOGNIZED;
  }
}

export function alertSeverityToNumber(object: AlertSeverity): number {
  switch (object) {
    case AlertSeverity.ALERT_SEVERITY_UNKNOWN:
      return 0;
    case AlertSeverity.ALERT_SEVERITY_LOW:
      return 1;
    case AlertSeverity.ALERT_SEVERITY_MEDIUM:
      return 2;
    case AlertSeverity.ALERT_SEVERITY_HIGH:
      return 3;
    case AlertSeverity.ALERT_SEVERITY_CRITICAL:
      return 4;
    case AlertSeverity.UNRECOGNIZED:
    default:
      return -1;
  }
}

export enum TransactionStatus {
  TRANSACTION_STATUS_UNKNOWN = "TRANSACTION_STATUS_UNKNOWN",
  TRANSACTION_STATUS_APPROVED = "TRANSACTION_STATUS_APPROVED",
  TRANSACTION_STATUS_FLAGGED = "TRANSACTION_STATUS_FLAGGED",
  TRANSACTION_STATUS_BLOCKED = "TRANSACTION_STATUS_BLOCKED",
  TRANSACTION_STATUS_PENDING_REVIEW = "TRANSACTION_STATUS_PENDING_REVIEW",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function transactionStatusFromJSON(object: any): TransactionStatus {
  switch (object) {
    case 0:
    case "TRANSACTION_STATUS_UNKNOWN":
      return TransactionStatus.TRANSACTION_STATUS_UNKNOWN;
    case 1:
    case "TRANSACTION_STATUS_APPROVED":
      return TransactionStatus.TRANSACTION_STATUS_APPROVED;
    case 2:
    case "TRANSACTION_STATUS_FLAGGED":
      return TransactionStatus.TRANSACTION_STATUS_FLAGGED;
    case 3:
    case "TRANSACTION_STATUS_BLOCKED":
      return TransactionStatus.TRANSACTION_STATUS_BLOCKED;
    case 4:
    case "TRANSACTION_STATUS_PENDING_REVIEW":
      return TransactionStatus.TRANSACTION_STATUS_PENDING_REVIEW;
    case -1:
    case "UNRECOGNIZED":
    default:
      return TransactionStatus.UNRECOGNIZED;
  }
}

export function transactionStatusToNumber(object: TransactionStatus): number {
  switch (object) {
    case TransactionStatus.TRANSACTION_STATUS_UNKNOWN:
      return 0;
    case TransactionStatus.TRANSACTION_STATUS_APPROVED:
      return 1;
    case TransactionStatus.TRANSACTION_STATUS_FLAGGED:
      return 2;
    case TransactionStatus.TRANSACTION_STATUS_BLOCKED:
      return 3;
    case TransactionStatus.TRANSACTION_STATUS_PENDING_REVIEW:
      return 4;
    case TransactionStatus.UNRECOGNIZED:
    default:
      return -1;
  }
}

export enum CustomerRiskCategory {
  CUSTOMER_RISK_UNKNOWN = "CUSTOMER_RISK_UNKNOWN",
  CUSTOMER_RISK_LOW = "CUSTOMER_RISK_LOW",
  CUSTOMER_RISK_MEDIUM = "CUSTOMER_RISK_MEDIUM",
  CUSTOMER_RISK_HIGH = "CUSTOMER_RISK_HIGH",
  CUSTOMER_RISK_PROHIBITED = "CUSTOMER_RISK_PROHIBITED",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function customerRiskCategoryFromJSON(object: any): CustomerRiskCategory {
  switch (object) {
    case 0:
    case "CUSTOMER_RISK_UNKNOWN":
      return CustomerRiskCategory.CUSTOMER_RISK_UNKNOWN;
    case 1:
    case "CUSTOMER_RISK_LOW":
      return CustomerRiskCategory.CUSTOMER_RISK_LOW;
    case 2:
    case "CUSTOMER_RISK_MEDIUM":
      return CustomerRiskCategory.CUSTOMER_RISK_MEDIUM;
    case 3:
    case "CUSTOMER_RISK_HIGH":
      return CustomerRiskCategory.CUSTOMER_RISK_HIGH;
    case 4:
    case "CUSTOMER_RISK_PROHIBITED":
      return CustomerRiskCategory.CUSTOMER_RISK_PROHIBITED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CustomerRiskCategory.UNRECOGNIZED;
  }
}

export function customerRiskCategoryToNumber(object: CustomerRiskCategory): number {
  switch (object) {
    case CustomerRiskCategory.CUSTOMER_RISK_UNKNOWN:
      return 0;
    case CustomerRiskCategory.CUSTOMER_RISK_LOW:
      return 1;
    case CustomerRiskCategory.CUSTOMER_RISK_MEDIUM:
      return 2;
    case CustomerRiskCategory.CUSTOMER_RISK_HIGH:
      return 3;
    case CustomerRiskCategory.CUSTOMER_RISK_PROHIBITED:
      return 4;
    case CustomerRiskCategory.UNRECOGNIZED:
    default:
      return -1;
  }
}

/** Common messages */
export interface RequestMetadata {
  requestId: string;
  sourceIp: string;
  userAgent: string;
  timestamp?: Date | undefined;
  correlationId: string;
}

export interface ResponseMetadata {
  requestId: string;
  timestamp?: Date | undefined;
  processingTime: number;
  version: string;
  correlationId: string;
}

export interface ErrorDetails {
  code: string;
  message: string;
  details: string;
}

/** Account information for risk evaluation */
export interface AccountInfo {
  accountId: string;
  customerId: string;
  accountType: string;
  accountNumber: string;
  currentBalance: number;
  currency: string;
  accountOpenDate?: Date | undefined;
  accountStatus: string;
  countryCode: string;
}

/** Customer information for risk evaluation */
export interface CustomerInfo {
  customerId: string;
  customerType: string;
  firstName: string;
  lastName: string;
  businessName: string;
  dateOfBirth: string;
  nationality: string;
  countryOfResidence: string;
  isPoliticallyExposed: boolean;
  isSanctioned: boolean;
  riskCategory: CustomerRiskCategory;
  lastKycUpdate?: Date | undefined;
  kycStatus: string;
}

/** Transaction context for risk evaluation */
export interface TransactionContext {
  transactionId: string;
  amount: number;
  currency: string;
  description: string;
  transactionType: string;
  channel: string;
  ipAddress: string;
  deviceId: string;
  location: string;
  countryCode: string;
  timestamp?: Date | undefined;
  metadata: { [key: string]: string };
}

export interface TransactionContext_MetadataEntry {
  key: string;
  value: string;
}

/** Risk alert generated during evaluation */
export interface RiskAlert {
  type: AlertType;
  severity: AlertSeverity;
  message: string;
  ruleId: string;
  ruleName: string;
  confidence: number;
  details: { [key: string]: string };
}

export interface RiskAlert_DetailsEntry {
  key: string;
  value: string;
}

/** Risk evaluation result */
export interface RiskEvaluation {
  riskScore: number;
  riskLevel: RiskLevel;
  recommendedStatus: TransactionStatus;
  alerts: RiskAlert[];
  evaluationId: string;
  evaluatedAt?: Date | undefined;
  evaluationModel: string;
  riskFactors: { [key: string]: number };
}

export interface RiskEvaluation_RiskFactorsEntry {
  key: string;
  value: number;
}

/** Request messages */
export interface EvaluateTransactionRiskRequest {
  tenantId: string;
  transaction?: TransactionContext | undefined;
  fromAccount?: AccountInfo | undefined;
  toAccount?: AccountInfo | undefined;
  fromCustomer?: CustomerInfo | undefined;
  toCustomer?: CustomerInfo | undefined;
  metadata?: RequestMetadata | undefined;
  includeHistoricalAnalysis: boolean;
  includePatternDetection: boolean;
  additionalChecks: string[];
}

export interface EvaluateCustomerRiskRequest {
  tenantId: string;
  customer?: CustomerInfo | undefined;
  accounts: AccountInfo[];
  metadata?: RequestMetadata | undefined;
  includeTransactionHistory: boolean;
  evaluationReason: string;
}

export interface GetRiskProfileRequest {
  tenantId: string;
  customerId: string;
  metadata?: RequestMetadata | undefined;
  includeRecentAlerts: boolean;
  historyDays: number;
}

export interface UpdateRiskRulesRequest {
  tenantId: string;
  rules: RiskRule[];
  metadata?: RequestMetadata | undefined;
  validateOnly: boolean;
}

export interface GetAMLStatusRequest {
  tenantId: string;
  metadata?: RequestMetadata | undefined;
}

/** Risk rule definition */
export interface RiskRule {
  ruleId: string;
  ruleName: string;
  description: string;
  alertType: AlertType;
  severity: AlertSeverity;
  isActive: boolean;
  parameters: { [key: string]: string };
  threshold: number;
  condition: string;
}

export interface RiskRule_ParametersEntry {
  key: string;
  value: string;
}

/** Customer risk profile */
export interface CustomerRiskProfile {
  customerId: string;
  riskCategory: CustomerRiskCategory;
  overallRiskScore: number;
  recentAlerts: RiskAlert[];
  lastEvaluated?: Date | undefined;
  riskFactors: { [key: string]: number };
  riskReason: string;
}

export interface CustomerRiskProfile_RiskFactorsEntry {
  key: string;
  value: number;
}

/** AML service status */
export interface AMLServiceStatus {
  isHealthy: boolean;
  version: string;
  activeRules: number;
  evaluationsToday: number;
  averageProcessingTime: number;
  lastRuleUpdate?: Date | undefined;
}

/** Response messages */
export interface EvaluateTransactionRiskResponse {
  success: boolean;
  riskEvaluation?: RiskEvaluation | undefined;
  message: string;
  error?: ErrorDetails | undefined;
  metadata?: ResponseMetadata | undefined;
}

export interface EvaluateCustomerRiskResponse {
  success: boolean;
  riskProfile?: CustomerRiskProfile | undefined;
  message: string;
  error?: ErrorDetails | undefined;
  metadata?: ResponseMetadata | undefined;
}

export interface GetRiskProfileResponse {
  success: boolean;
  riskProfile?: CustomerRiskProfile | undefined;
  message: string;
  error?: ErrorDetails | undefined;
  metadata?: ResponseMetadata | undefined;
}

export interface UpdateRiskRulesResponse {
  success: boolean;
  rulesUpdated: number;
  validationErrors: string[];
  message: string;
  error?: ErrorDetails | undefined;
  metadata?: ResponseMetadata | undefined;
}

export interface GetAMLStatusResponse {
  success: boolean;
  status?: AMLServiceStatus | undefined;
  message: string;
  error?: ErrorDetails | undefined;
  metadata?: ResponseMetadata | undefined;
}

wrappers[".google.protobuf.Timestamp"] = {
  fromObject(value: Date) {
    return { seconds: value.getTime() / 1000, nanos: (value.getTime() % 1000) * 1e6 };
  },
  toObject(message: { seconds: number; nanos: number }) {
    return new Date(message.seconds * 1000 + message.nanos / 1e6);
  },
} as any;

function createBaseRequestMetadata(): RequestMetadata {
  return { requestId: "", sourceIp: "", userAgent: "", correlationId: "" };
}

export const RequestMetadata: MessageFns<RequestMetadata> = {
  encode(message: RequestMetadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.requestId !== "") {
      writer.uint32(10).string(message.requestId);
    }
    if (message.sourceIp !== "") {
      writer.uint32(18).string(message.sourceIp);
    }
    if (message.userAgent !== "") {
      writer.uint32(26).string(message.userAgent);
    }
    if (message.timestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.timestamp), writer.uint32(34).fork()).join();
    }
    if (message.correlationId !== "") {
      writer.uint32(42).string(message.correlationId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RequestMetadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRequestMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.requestId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.sourceIp = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.userAgent = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.timestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.correlationId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseResponseMetadata(): ResponseMetadata {
  return { requestId: "", processingTime: 0, version: "", correlationId: "" };
}

export const ResponseMetadata: MessageFns<ResponseMetadata> = {
  encode(message: ResponseMetadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.requestId !== "") {
      writer.uint32(10).string(message.requestId);
    }
    if (message.timestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.timestamp), writer.uint32(18).fork()).join();
    }
    if (message.processingTime !== 0) {
      writer.uint32(24).int32(message.processingTime);
    }
    if (message.version !== "") {
      writer.uint32(34).string(message.version);
    }
    if (message.correlationId !== "") {
      writer.uint32(42).string(message.correlationId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ResponseMetadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseResponseMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.requestId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.timestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.processingTime = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.version = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.correlationId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseErrorDetails(): ErrorDetails {
  return { code: "", message: "", details: "" };
}

export const ErrorDetails: MessageFns<ErrorDetails> = {
  encode(message: ErrorDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.code !== "") {
      writer.uint32(10).string(message.code);
    }
    if (message.message !== "") {
      writer.uint32(18).string(message.message);
    }
    if (message.details !== "") {
      writer.uint32(26).string(message.details);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ErrorDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseErrorDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.code = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.details = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseAccountInfo(): AccountInfo {
  return {
    accountId: "",
    customerId: "",
    accountType: "",
    accountNumber: "",
    currentBalance: 0,
    currency: "",
    accountStatus: "",
    countryCode: "",
  };
}

export const AccountInfo: MessageFns<AccountInfo> = {
  encode(message: AccountInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.accountId !== "") {
      writer.uint32(10).string(message.accountId);
    }
    if (message.customerId !== "") {
      writer.uint32(18).string(message.customerId);
    }
    if (message.accountType !== "") {
      writer.uint32(26).string(message.accountType);
    }
    if (message.accountNumber !== "") {
      writer.uint32(34).string(message.accountNumber);
    }
    if (message.currentBalance !== 0) {
      writer.uint32(41).double(message.currentBalance);
    }
    if (message.currency !== "") {
      writer.uint32(50).string(message.currency);
    }
    if (message.accountOpenDate !== undefined) {
      Timestamp.encode(toTimestamp(message.accountOpenDate), writer.uint32(58).fork()).join();
    }
    if (message.accountStatus !== "") {
      writer.uint32(66).string(message.accountStatus);
    }
    if (message.countryCode !== "") {
      writer.uint32(74).string(message.countryCode);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AccountInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAccountInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accountId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.customerId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.accountType = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.accountNumber = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.currentBalance = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.currency = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.accountOpenDate = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.accountStatus = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.countryCode = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseCustomerInfo(): CustomerInfo {
  return {
    customerId: "",
    customerType: "",
    firstName: "",
    lastName: "",
    businessName: "",
    dateOfBirth: "",
    nationality: "",
    countryOfResidence: "",
    isPoliticallyExposed: false,
    isSanctioned: false,
    riskCategory: CustomerRiskCategory.CUSTOMER_RISK_UNKNOWN,
    kycStatus: "",
  };
}

export const CustomerInfo: MessageFns<CustomerInfo> = {
  encode(message: CustomerInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.customerId !== "") {
      writer.uint32(10).string(message.customerId);
    }
    if (message.customerType !== "") {
      writer.uint32(18).string(message.customerType);
    }
    if (message.firstName !== "") {
      writer.uint32(26).string(message.firstName);
    }
    if (message.lastName !== "") {
      writer.uint32(34).string(message.lastName);
    }
    if (message.businessName !== "") {
      writer.uint32(42).string(message.businessName);
    }
    if (message.dateOfBirth !== "") {
      writer.uint32(50).string(message.dateOfBirth);
    }
    if (message.nationality !== "") {
      writer.uint32(58).string(message.nationality);
    }
    if (message.countryOfResidence !== "") {
      writer.uint32(66).string(message.countryOfResidence);
    }
    if (message.isPoliticallyExposed !== false) {
      writer.uint32(72).bool(message.isPoliticallyExposed);
    }
    if (message.isSanctioned !== false) {
      writer.uint32(80).bool(message.isSanctioned);
    }
    if (message.riskCategory !== CustomerRiskCategory.CUSTOMER_RISK_UNKNOWN) {
      writer.uint32(88).int32(customerRiskCategoryToNumber(message.riskCategory));
    }
    if (message.lastKycUpdate !== undefined) {
      Timestamp.encode(toTimestamp(message.lastKycUpdate), writer.uint32(98).fork()).join();
    }
    if (message.kycStatus !== "") {
      writer.uint32(106).string(message.kycStatus);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CustomerInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCustomerInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.customerId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.customerType = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.firstName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.lastName = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.businessName = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.dateOfBirth = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.nationality = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.countryOfResidence = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.isPoliticallyExposed = reader.bool();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.isSanctioned = reader.bool();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.riskCategory = customerRiskCategoryFromJSON(reader.int32());
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.lastKycUpdate = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.kycStatus = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseTransactionContext(): TransactionContext {
  return {
    transactionId: "",
    amount: 0,
    currency: "",
    description: "",
    transactionType: "",
    channel: "",
    ipAddress: "",
    deviceId: "",
    location: "",
    countryCode: "",
    metadata: {},
  };
}

export const TransactionContext: MessageFns<TransactionContext> = {
  encode(message: TransactionContext, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.transactionId !== "") {
      writer.uint32(10).string(message.transactionId);
    }
    if (message.amount !== 0) {
      writer.uint32(17).double(message.amount);
    }
    if (message.currency !== "") {
      writer.uint32(26).string(message.currency);
    }
    if (message.description !== "") {
      writer.uint32(34).string(message.description);
    }
    if (message.transactionType !== "") {
      writer.uint32(42).string(message.transactionType);
    }
    if (message.channel !== "") {
      writer.uint32(50).string(message.channel);
    }
    if (message.ipAddress !== "") {
      writer.uint32(58).string(message.ipAddress);
    }
    if (message.deviceId !== "") {
      writer.uint32(66).string(message.deviceId);
    }
    if (message.location !== "") {
      writer.uint32(74).string(message.location);
    }
    if (message.countryCode !== "") {
      writer.uint32(82).string(message.countryCode);
    }
    if (message.timestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.timestamp), writer.uint32(90).fork()).join();
    }
    Object.entries(message.metadata).forEach(([key, value]) => {
      TransactionContext_MetadataEntry.encode({ key: key as any, value }, writer.uint32(98).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TransactionContext {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTransactionContext();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.transactionId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.currency = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.transactionType = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.channel = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.ipAddress = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.deviceId = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.location = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.countryCode = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.timestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          const entry12 = TransactionContext_MetadataEntry.decode(reader, reader.uint32());
          if (entry12.value !== undefined) {
            message.metadata[entry12.key] = entry12.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseTransactionContext_MetadataEntry(): TransactionContext_MetadataEntry {
  return { key: "", value: "" };
}

export const TransactionContext_MetadataEntry: MessageFns<TransactionContext_MetadataEntry> = {
  encode(message: TransactionContext_MetadataEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TransactionContext_MetadataEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTransactionContext_MetadataEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseRiskAlert(): RiskAlert {
  return {
    type: AlertType.ALERT_TYPE_UNKNOWN,
    severity: AlertSeverity.ALERT_SEVERITY_UNKNOWN,
    message: "",
    ruleId: "",
    ruleName: "",
    confidence: 0,
    details: {},
  };
}

export const RiskAlert: MessageFns<RiskAlert> = {
  encode(message: RiskAlert, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.type !== AlertType.ALERT_TYPE_UNKNOWN) {
      writer.uint32(8).int32(alertTypeToNumber(message.type));
    }
    if (message.severity !== AlertSeverity.ALERT_SEVERITY_UNKNOWN) {
      writer.uint32(16).int32(alertSeverityToNumber(message.severity));
    }
    if (message.message !== "") {
      writer.uint32(26).string(message.message);
    }
    if (message.ruleId !== "") {
      writer.uint32(34).string(message.ruleId);
    }
    if (message.ruleName !== "") {
      writer.uint32(42).string(message.ruleName);
    }
    if (message.confidence !== 0) {
      writer.uint32(49).double(message.confidence);
    }
    Object.entries(message.details).forEach(([key, value]) => {
      RiskAlert_DetailsEntry.encode({ key: key as any, value }, writer.uint32(58).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RiskAlert {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRiskAlert();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.type = alertTypeFromJSON(reader.int32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.severity = alertSeverityFromJSON(reader.int32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.ruleId = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.ruleName = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 49) {
            break;
          }

          message.confidence = reader.double();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          const entry7 = RiskAlert_DetailsEntry.decode(reader, reader.uint32());
          if (entry7.value !== undefined) {
            message.details[entry7.key] = entry7.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseRiskAlert_DetailsEntry(): RiskAlert_DetailsEntry {
  return { key: "", value: "" };
}

export const RiskAlert_DetailsEntry: MessageFns<RiskAlert_DetailsEntry> = {
  encode(message: RiskAlert_DetailsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RiskAlert_DetailsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRiskAlert_DetailsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseRiskEvaluation(): RiskEvaluation {
  return {
    riskScore: 0,
    riskLevel: RiskLevel.RISK_LEVEL_UNKNOWN,
    recommendedStatus: TransactionStatus.TRANSACTION_STATUS_UNKNOWN,
    alerts: [],
    evaluationId: "",
    evaluationModel: "",
    riskFactors: {},
  };
}

export const RiskEvaluation: MessageFns<RiskEvaluation> = {
  encode(message: RiskEvaluation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.riskScore !== 0) {
      writer.uint32(9).double(message.riskScore);
    }
    if (message.riskLevel !== RiskLevel.RISK_LEVEL_UNKNOWN) {
      writer.uint32(16).int32(riskLevelToNumber(message.riskLevel));
    }
    if (message.recommendedStatus !== TransactionStatus.TRANSACTION_STATUS_UNKNOWN) {
      writer.uint32(24).int32(transactionStatusToNumber(message.recommendedStatus));
    }
    for (const v of message.alerts) {
      RiskAlert.encode(v!, writer.uint32(34).fork()).join();
    }
    if (message.evaluationId !== "") {
      writer.uint32(42).string(message.evaluationId);
    }
    if (message.evaluatedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.evaluatedAt), writer.uint32(50).fork()).join();
    }
    if (message.evaluationModel !== "") {
      writer.uint32(58).string(message.evaluationModel);
    }
    Object.entries(message.riskFactors).forEach(([key, value]) => {
      RiskEvaluation_RiskFactorsEntry.encode({ key: key as any, value }, writer.uint32(66).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RiskEvaluation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRiskEvaluation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.riskScore = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.riskLevel = riskLevelFromJSON(reader.int32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.recommendedStatus = transactionStatusFromJSON(reader.int32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.alerts.push(RiskAlert.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.evaluationId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.evaluatedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.evaluationModel = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          const entry8 = RiskEvaluation_RiskFactorsEntry.decode(reader, reader.uint32());
          if (entry8.value !== undefined) {
            message.riskFactors[entry8.key] = entry8.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseRiskEvaluation_RiskFactorsEntry(): RiskEvaluation_RiskFactorsEntry {
  return { key: "", value: 0 };
}

export const RiskEvaluation_RiskFactorsEntry: MessageFns<RiskEvaluation_RiskFactorsEntry> = {
  encode(message: RiskEvaluation_RiskFactorsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(17).double(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RiskEvaluation_RiskFactorsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRiskEvaluation_RiskFactorsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.value = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseEvaluateTransactionRiskRequest(): EvaluateTransactionRiskRequest {
  return { tenantId: "", includeHistoricalAnalysis: false, includePatternDetection: false, additionalChecks: [] };
}

export const EvaluateTransactionRiskRequest: MessageFns<EvaluateTransactionRiskRequest> = {
  encode(message: EvaluateTransactionRiskRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.tenantId !== "") {
      writer.uint32(10).string(message.tenantId);
    }
    if (message.transaction !== undefined) {
      TransactionContext.encode(message.transaction, writer.uint32(18).fork()).join();
    }
    if (message.fromAccount !== undefined) {
      AccountInfo.encode(message.fromAccount, writer.uint32(26).fork()).join();
    }
    if (message.toAccount !== undefined) {
      AccountInfo.encode(message.toAccount, writer.uint32(34).fork()).join();
    }
    if (message.fromCustomer !== undefined) {
      CustomerInfo.encode(message.fromCustomer, writer.uint32(42).fork()).join();
    }
    if (message.toCustomer !== undefined) {
      CustomerInfo.encode(message.toCustomer, writer.uint32(50).fork()).join();
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(58).fork()).join();
    }
    if (message.includeHistoricalAnalysis !== false) {
      writer.uint32(64).bool(message.includeHistoricalAnalysis);
    }
    if (message.includePatternDetection !== false) {
      writer.uint32(72).bool(message.includePatternDetection);
    }
    for (const v of message.additionalChecks) {
      writer.uint32(82).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EvaluateTransactionRiskRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEvaluateTransactionRiskRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tenantId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.transaction = TransactionContext.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.fromAccount = AccountInfo.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.toAccount = AccountInfo.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.fromCustomer = CustomerInfo.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.toCustomer = CustomerInfo.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.includeHistoricalAnalysis = reader.bool();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.includePatternDetection = reader.bool();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.additionalChecks.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseEvaluateCustomerRiskRequest(): EvaluateCustomerRiskRequest {
  return { tenantId: "", accounts: [], includeTransactionHistory: false, evaluationReason: "" };
}

export const EvaluateCustomerRiskRequest: MessageFns<EvaluateCustomerRiskRequest> = {
  encode(message: EvaluateCustomerRiskRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.tenantId !== "") {
      writer.uint32(10).string(message.tenantId);
    }
    if (message.customer !== undefined) {
      CustomerInfo.encode(message.customer, writer.uint32(18).fork()).join();
    }
    for (const v of message.accounts) {
      AccountInfo.encode(v!, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    if (message.includeTransactionHistory !== false) {
      writer.uint32(40).bool(message.includeTransactionHistory);
    }
    if (message.evaluationReason !== "") {
      writer.uint32(50).string(message.evaluationReason);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EvaluateCustomerRiskRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEvaluateCustomerRiskRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tenantId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.customer = CustomerInfo.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.accounts.push(AccountInfo.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.includeTransactionHistory = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.evaluationReason = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetRiskProfileRequest(): GetRiskProfileRequest {
  return { tenantId: "", customerId: "", includeRecentAlerts: false, historyDays: 0 };
}

export const GetRiskProfileRequest: MessageFns<GetRiskProfileRequest> = {
  encode(message: GetRiskProfileRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.tenantId !== "") {
      writer.uint32(10).string(message.tenantId);
    }
    if (message.customerId !== "") {
      writer.uint32(18).string(message.customerId);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    if (message.includeRecentAlerts !== false) {
      writer.uint32(32).bool(message.includeRecentAlerts);
    }
    if (message.historyDays !== 0) {
      writer.uint32(40).int32(message.historyDays);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetRiskProfileRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetRiskProfileRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tenantId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.customerId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.includeRecentAlerts = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.historyDays = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUpdateRiskRulesRequest(): UpdateRiskRulesRequest {
  return { tenantId: "", rules: [], validateOnly: false };
}

export const UpdateRiskRulesRequest: MessageFns<UpdateRiskRulesRequest> = {
  encode(message: UpdateRiskRulesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.tenantId !== "") {
      writer.uint32(10).string(message.tenantId);
    }
    for (const v of message.rules) {
      RiskRule.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    if (message.validateOnly !== false) {
      writer.uint32(32).bool(message.validateOnly);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateRiskRulesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateRiskRulesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tenantId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.rules.push(RiskRule.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.validateOnly = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetAMLStatusRequest(): GetAMLStatusRequest {
  return { tenantId: "" };
}

export const GetAMLStatusRequest: MessageFns<GetAMLStatusRequest> = {
  encode(message: GetAMLStatusRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.tenantId !== "") {
      writer.uint32(10).string(message.tenantId);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetAMLStatusRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetAMLStatusRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tenantId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseRiskRule(): RiskRule {
  return {
    ruleId: "",
    ruleName: "",
    description: "",
    alertType: AlertType.ALERT_TYPE_UNKNOWN,
    severity: AlertSeverity.ALERT_SEVERITY_UNKNOWN,
    isActive: false,
    parameters: {},
    threshold: 0,
    condition: "",
  };
}

export const RiskRule: MessageFns<RiskRule> = {
  encode(message: RiskRule, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ruleId !== "") {
      writer.uint32(10).string(message.ruleId);
    }
    if (message.ruleName !== "") {
      writer.uint32(18).string(message.ruleName);
    }
    if (message.description !== "") {
      writer.uint32(26).string(message.description);
    }
    if (message.alertType !== AlertType.ALERT_TYPE_UNKNOWN) {
      writer.uint32(32).int32(alertTypeToNumber(message.alertType));
    }
    if (message.severity !== AlertSeverity.ALERT_SEVERITY_UNKNOWN) {
      writer.uint32(40).int32(alertSeverityToNumber(message.severity));
    }
    if (message.isActive !== false) {
      writer.uint32(48).bool(message.isActive);
    }
    Object.entries(message.parameters).forEach(([key, value]) => {
      RiskRule_ParametersEntry.encode({ key: key as any, value }, writer.uint32(58).fork()).join();
    });
    if (message.threshold !== 0) {
      writer.uint32(65).double(message.threshold);
    }
    if (message.condition !== "") {
      writer.uint32(74).string(message.condition);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RiskRule {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRiskRule();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.ruleId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.ruleName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.alertType = alertTypeFromJSON(reader.int32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.severity = alertSeverityFromJSON(reader.int32());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          const entry7 = RiskRule_ParametersEntry.decode(reader, reader.uint32());
          if (entry7.value !== undefined) {
            message.parameters[entry7.key] = entry7.value;
          }
          continue;
        }
        case 8: {
          if (tag !== 65) {
            break;
          }

          message.threshold = reader.double();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.condition = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseRiskRule_ParametersEntry(): RiskRule_ParametersEntry {
  return { key: "", value: "" };
}

export const RiskRule_ParametersEntry: MessageFns<RiskRule_ParametersEntry> = {
  encode(message: RiskRule_ParametersEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RiskRule_ParametersEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRiskRule_ParametersEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseCustomerRiskProfile(): CustomerRiskProfile {
  return {
    customerId: "",
    riskCategory: CustomerRiskCategory.CUSTOMER_RISK_UNKNOWN,
    overallRiskScore: 0,
    recentAlerts: [],
    riskFactors: {},
    riskReason: "",
  };
}

export const CustomerRiskProfile: MessageFns<CustomerRiskProfile> = {
  encode(message: CustomerRiskProfile, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.customerId !== "") {
      writer.uint32(10).string(message.customerId);
    }
    if (message.riskCategory !== CustomerRiskCategory.CUSTOMER_RISK_UNKNOWN) {
      writer.uint32(16).int32(customerRiskCategoryToNumber(message.riskCategory));
    }
    if (message.overallRiskScore !== 0) {
      writer.uint32(25).double(message.overallRiskScore);
    }
    for (const v of message.recentAlerts) {
      RiskAlert.encode(v!, writer.uint32(34).fork()).join();
    }
    if (message.lastEvaluated !== undefined) {
      Timestamp.encode(toTimestamp(message.lastEvaluated), writer.uint32(42).fork()).join();
    }
    Object.entries(message.riskFactors).forEach(([key, value]) => {
      CustomerRiskProfile_RiskFactorsEntry.encode({ key: key as any, value }, writer.uint32(50).fork()).join();
    });
    if (message.riskReason !== "") {
      writer.uint32(58).string(message.riskReason);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CustomerRiskProfile {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCustomerRiskProfile();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.customerId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.riskCategory = customerRiskCategoryFromJSON(reader.int32());
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.overallRiskScore = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.recentAlerts.push(RiskAlert.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.lastEvaluated = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          const entry6 = CustomerRiskProfile_RiskFactorsEntry.decode(reader, reader.uint32());
          if (entry6.value !== undefined) {
            message.riskFactors[entry6.key] = entry6.value;
          }
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.riskReason = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseCustomerRiskProfile_RiskFactorsEntry(): CustomerRiskProfile_RiskFactorsEntry {
  return { key: "", value: 0 };
}

export const CustomerRiskProfile_RiskFactorsEntry: MessageFns<CustomerRiskProfile_RiskFactorsEntry> = {
  encode(message: CustomerRiskProfile_RiskFactorsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(17).double(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CustomerRiskProfile_RiskFactorsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCustomerRiskProfile_RiskFactorsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.value = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseAMLServiceStatus(): AMLServiceStatus {
  return { isHealthy: false, version: "", activeRules: 0, evaluationsToday: 0, averageProcessingTime: 0 };
}

export const AMLServiceStatus: MessageFns<AMLServiceStatus> = {
  encode(message: AMLServiceStatus, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isHealthy !== false) {
      writer.uint32(8).bool(message.isHealthy);
    }
    if (message.version !== "") {
      writer.uint32(18).string(message.version);
    }
    if (message.activeRules !== 0) {
      writer.uint32(24).int32(message.activeRules);
    }
    if (message.evaluationsToday !== 0) {
      writer.uint32(32).int32(message.evaluationsToday);
    }
    if (message.averageProcessingTime !== 0) {
      writer.uint32(41).double(message.averageProcessingTime);
    }
    if (message.lastRuleUpdate !== undefined) {
      Timestamp.encode(toTimestamp(message.lastRuleUpdate), writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AMLServiceStatus {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAMLServiceStatus();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isHealthy = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.version = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.activeRules = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.evaluationsToday = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.averageProcessingTime = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.lastRuleUpdate = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseEvaluateTransactionRiskResponse(): EvaluateTransactionRiskResponse {
  return { success: false, message: "" };
}

export const EvaluateTransactionRiskResponse: MessageFns<EvaluateTransactionRiskResponse> = {
  encode(message: EvaluateTransactionRiskResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.riskEvaluation !== undefined) {
      RiskEvaluation.encode(message.riskEvaluation, writer.uint32(18).fork()).join();
    }
    if (message.message !== "") {
      writer.uint32(26).string(message.message);
    }
    if (message.error !== undefined) {
      ErrorDetails.encode(message.error, writer.uint32(34).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EvaluateTransactionRiskResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEvaluateTransactionRiskResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.riskEvaluation = RiskEvaluation.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.error = ErrorDetails.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseEvaluateCustomerRiskResponse(): EvaluateCustomerRiskResponse {
  return { success: false, message: "" };
}

export const EvaluateCustomerRiskResponse: MessageFns<EvaluateCustomerRiskResponse> = {
  encode(message: EvaluateCustomerRiskResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.riskProfile !== undefined) {
      CustomerRiskProfile.encode(message.riskProfile, writer.uint32(18).fork()).join();
    }
    if (message.message !== "") {
      writer.uint32(26).string(message.message);
    }
    if (message.error !== undefined) {
      ErrorDetails.encode(message.error, writer.uint32(34).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EvaluateCustomerRiskResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEvaluateCustomerRiskResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.riskProfile = CustomerRiskProfile.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.error = ErrorDetails.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetRiskProfileResponse(): GetRiskProfileResponse {
  return { success: false, message: "" };
}

export const GetRiskProfileResponse: MessageFns<GetRiskProfileResponse> = {
  encode(message: GetRiskProfileResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.riskProfile !== undefined) {
      CustomerRiskProfile.encode(message.riskProfile, writer.uint32(18).fork()).join();
    }
    if (message.message !== "") {
      writer.uint32(26).string(message.message);
    }
    if (message.error !== undefined) {
      ErrorDetails.encode(message.error, writer.uint32(34).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetRiskProfileResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetRiskProfileResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.riskProfile = CustomerRiskProfile.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.error = ErrorDetails.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUpdateRiskRulesResponse(): UpdateRiskRulesResponse {
  return { success: false, rulesUpdated: 0, validationErrors: [], message: "" };
}

export const UpdateRiskRulesResponse: MessageFns<UpdateRiskRulesResponse> = {
  encode(message: UpdateRiskRulesResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.rulesUpdated !== 0) {
      writer.uint32(16).int32(message.rulesUpdated);
    }
    for (const v of message.validationErrors) {
      writer.uint32(26).string(v!);
    }
    if (message.message !== "") {
      writer.uint32(34).string(message.message);
    }
    if (message.error !== undefined) {
      ErrorDetails.encode(message.error, writer.uint32(42).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateRiskRulesResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateRiskRulesResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.rulesUpdated = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.validationErrors.push(reader.string());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.error = ErrorDetails.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetAMLStatusResponse(): GetAMLStatusResponse {
  return { success: false, message: "" };
}

export const GetAMLStatusResponse: MessageFns<GetAMLStatusResponse> = {
  encode(message: GetAMLStatusResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.status !== undefined) {
      AMLServiceStatus.encode(message.status, writer.uint32(18).fork()).join();
    }
    if (message.message !== "") {
      writer.uint32(26).string(message.message);
    }
    if (message.error !== undefined) {
      ErrorDetails.encode(message.error, writer.uint32(34).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetAMLStatusResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetAMLStatusResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.status = AMLServiceStatus.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.error = ErrorDetails.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

/** AML service definition */

export interface AMLServiceClient {
  /** Risk evaluation operations */

  evaluateTransactionRisk(request: EvaluateTransactionRiskRequest): Observable<EvaluateTransactionRiskResponse>;

  evaluateCustomerRisk(request: EvaluateCustomerRiskRequest): Observable<EvaluateCustomerRiskResponse>;

  getRiskProfile(request: GetRiskProfileRequest): Observable<GetRiskProfileResponse>;

  updateRiskRules(request: UpdateRiskRulesRequest): Observable<UpdateRiskRulesResponse>;

  getAmlStatus(request: GetAMLStatusRequest): Observable<GetAMLStatusResponse>;
}

/** AML service definition */

export interface AMLServiceController {
  /** Risk evaluation operations */

  evaluateTransactionRisk(
    request: EvaluateTransactionRiskRequest,
  ):
    | Promise<EvaluateTransactionRiskResponse>
    | Observable<EvaluateTransactionRiskResponse>
    | EvaluateTransactionRiskResponse;

  evaluateCustomerRisk(
    request: EvaluateCustomerRiskRequest,
  ): Promise<EvaluateCustomerRiskResponse> | Observable<EvaluateCustomerRiskResponse> | EvaluateCustomerRiskResponse;

  getRiskProfile(
    request: GetRiskProfileRequest,
  ): Promise<GetRiskProfileResponse> | Observable<GetRiskProfileResponse> | GetRiskProfileResponse;

  updateRiskRules(
    request: UpdateRiskRulesRequest,
  ): Promise<UpdateRiskRulesResponse> | Observable<UpdateRiskRulesResponse> | UpdateRiskRulesResponse;

  getAmlStatus(
    request: GetAMLStatusRequest,
  ): Promise<GetAMLStatusResponse> | Observable<GetAMLStatusResponse> | GetAMLStatusResponse;
}

export function AMLServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "evaluateTransactionRisk",
      "evaluateCustomerRisk",
      "getRiskProfile",
      "updateRiskRules",
      "getAmlStatus",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("AMLService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("AMLService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const AML_SERVICE_NAME = "AMLService";

/** AML service definition */
export type AMLServiceService = typeof AMLServiceService;
export const AMLServiceService = {
  /** Risk evaluation operations */
  evaluateTransactionRisk: {
    path: "/aml.AMLService/EvaluateTransactionRisk",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: EvaluateTransactionRiskRequest): Buffer =>
      Buffer.from(EvaluateTransactionRiskRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): EvaluateTransactionRiskRequest => EvaluateTransactionRiskRequest.decode(value),
    responseSerialize: (value: EvaluateTransactionRiskResponse): Buffer =>
      Buffer.from(EvaluateTransactionRiskResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): EvaluateTransactionRiskResponse =>
      EvaluateTransactionRiskResponse.decode(value),
  },
  evaluateCustomerRisk: {
    path: "/aml.AMLService/EvaluateCustomerRisk",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: EvaluateCustomerRiskRequest): Buffer =>
      Buffer.from(EvaluateCustomerRiskRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): EvaluateCustomerRiskRequest => EvaluateCustomerRiskRequest.decode(value),
    responseSerialize: (value: EvaluateCustomerRiskResponse): Buffer =>
      Buffer.from(EvaluateCustomerRiskResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): EvaluateCustomerRiskResponse => EvaluateCustomerRiskResponse.decode(value),
  },
  getRiskProfile: {
    path: "/aml.AMLService/GetRiskProfile",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetRiskProfileRequest): Buffer =>
      Buffer.from(GetRiskProfileRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): GetRiskProfileRequest => GetRiskProfileRequest.decode(value),
    responseSerialize: (value: GetRiskProfileResponse): Buffer =>
      Buffer.from(GetRiskProfileResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): GetRiskProfileResponse => GetRiskProfileResponse.decode(value),
  },
  updateRiskRules: {
    path: "/aml.AMLService/UpdateRiskRules",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: UpdateRiskRulesRequest): Buffer =>
      Buffer.from(UpdateRiskRulesRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): UpdateRiskRulesRequest => UpdateRiskRulesRequest.decode(value),
    responseSerialize: (value: UpdateRiskRulesResponse): Buffer =>
      Buffer.from(UpdateRiskRulesResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): UpdateRiskRulesResponse => UpdateRiskRulesResponse.decode(value),
  },
  getAmlStatus: {
    path: "/aml.AMLService/GetAMLStatus",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetAMLStatusRequest): Buffer => Buffer.from(GetAMLStatusRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): GetAMLStatusRequest => GetAMLStatusRequest.decode(value),
    responseSerialize: (value: GetAMLStatusResponse): Buffer =>
      Buffer.from(GetAMLStatusResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): GetAMLStatusResponse => GetAMLStatusResponse.decode(value),
  },
} as const;

export interface AMLServiceServer extends UntypedServiceImplementation {
  /** Risk evaluation operations */
  evaluateTransactionRisk: handleUnaryCall<EvaluateTransactionRiskRequest, EvaluateTransactionRiskResponse>;
  evaluateCustomerRisk: handleUnaryCall<EvaluateCustomerRiskRequest, EvaluateCustomerRiskResponse>;
  getRiskProfile: handleUnaryCall<GetRiskProfileRequest, GetRiskProfileResponse>;
  updateRiskRules: handleUnaryCall<UpdateRiskRulesRequest, UpdateRiskRulesResponse>;
  getAmlStatus: handleUnaryCall<GetAMLStatusRequest, GetAMLStatusResponse>;
}

function toTimestamp(date: Date): Timestamp {
  const seconds = Math.trunc(date.getTime() / 1_000).toString();
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (globalThis.Number(t.seconds) || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
}
