// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: tenant/tenant.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import type { handleUnaryCall, UntypedServiceImplementation } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { wrappers } from "protobufjs";
import { Observable } from "rxjs";
import { Timestamp } from "../google/protobuf/timestamp";

/** Tenant status enumeration */
export enum TenantStatus {
  TENANT_STATUS_UNKNOWN = "TENANT_STATUS_UNKNOWN",
  TENANT_STATUS_ACTIVE = "TENANT_STATUS_ACTIVE",
  TENANT_STATUS_INACTIVE = "TENANT_STATUS_INACTIVE",
  TENANT_STATUS_SUSPENDED = "TENANT_STATUS_SUSPENDED",
  TENANT_STATUS_TRIAL = "TENANT_STATUS_TRIAL",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function tenantStatusFromJSON(object: any): TenantStatus {
  switch (object) {
    case 0:
    case "TENANT_STATUS_UNKNOWN":
      return TenantStatus.TENANT_STATUS_UNKNOWN;
    case 1:
    case "TENANT_STATUS_ACTIVE":
      return TenantStatus.TENANT_STATUS_ACTIVE;
    case 2:
    case "TENANT_STATUS_INACTIVE":
      return TenantStatus.TENANT_STATUS_INACTIVE;
    case 3:
    case "TENANT_STATUS_SUSPENDED":
      return TenantStatus.TENANT_STATUS_SUSPENDED;
    case 4:
    case "TENANT_STATUS_TRIAL":
      return TenantStatus.TENANT_STATUS_TRIAL;
    case -1:
    case "UNRECOGNIZED":
    default:
      return TenantStatus.UNRECOGNIZED;
  }
}

export function tenantStatusToNumber(object: TenantStatus): number {
  switch (object) {
    case TenantStatus.TENANT_STATUS_UNKNOWN:
      return 0;
    case TenantStatus.TENANT_STATUS_ACTIVE:
      return 1;
    case TenantStatus.TENANT_STATUS_INACTIVE:
      return 2;
    case TenantStatus.TENANT_STATUS_SUSPENDED:
      return 3;
    case TenantStatus.TENANT_STATUS_TRIAL:
      return 4;
    case TenantStatus.UNRECOGNIZED:
    default:
      return -1;
  }
}

/** Create tenant request message */
export interface CreateTenantRequest {
  code: string;
  name: string;
  description: string;
  metadata?: RequestMetadata | undefined;
}

/** Create tenant response message */
export interface CreateTenantResponse {
  success: boolean;
  tenant?: Tenant | undefined;
  error?: TenantError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Get tenant request message */
export interface GetTenantRequest {
  tenantId: string;
  metadata?: RequestMetadata | undefined;
}

/** Get tenant response message */
export interface GetTenantResponse {
  success: boolean;
  tenant?: Tenant | undefined;
  error?: TenantError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Get tenant by code request message */
export interface GetTenantByCodeRequest {
  code: string;
  metadata?: RequestMetadata | undefined;
}

/** Get tenant by code response message */
export interface GetTenantByCodeResponse {
  success: boolean;
  tenant?: Tenant | undefined;
  error?: TenantError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Validate tenant code request message */
export interface ValidateTenantCodeRequest {
  code: string;
  metadata?: RequestMetadata | undefined;
}

/** Validate tenant code response message */
export interface ValidateTenantCodeResponse {
  valid: boolean;
  tenant?: Tenant | undefined;
  error?: TenantError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Update tenant request message */
export interface UpdateTenantRequest {
  tenantId: string;
  name: string;
  description: string;
  status: TenantStatus;
  metadata?: RequestMetadata | undefined;
}

/** Update tenant response message */
export interface UpdateTenantResponse {
  success: boolean;
  tenant?: Tenant | undefined;
  error?: TenantError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Tenant information message */
export interface Tenant {
  id: string;
  code: string;
  name: string;
  description: string;
  status: TenantStatus;
  createdAt?: Date | undefined;
  updatedAt?: Date | undefined;
}

/** Tenant error message */
export interface TenantError {
  code: string;
  message: string;
  details: { [key: string]: string };
  traceId: string;
}

export interface TenantError_DetailsEntry {
  key: string;
  value: string;
}

/** Request metadata */
export interface RequestMetadata {
  requestId: string;
  sourceIp: string;
  userAgent: string;
  timestamp?: Date | undefined;
}

/** Response metadata */
export interface ResponseMetadata {
  requestId: string;
  timestamp?: Date | undefined;
  processingTime: number;
}

wrappers[".google.protobuf.Timestamp"] = {
  fromObject(value: Date) {
    return { seconds: value.getTime() / 1000, nanos: (value.getTime() % 1000) * 1e6 };
  },
  toObject(message: { seconds: number; nanos: number }) {
    return new Date(message.seconds * 1000 + message.nanos / 1e6);
  },
} as any;

function createBaseCreateTenantRequest(): CreateTenantRequest {
  return { code: "", name: "", description: "" };
}

export const CreateTenantRequest: MessageFns<CreateTenantRequest> = {
  encode(message: CreateTenantRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.code !== "") {
      writer.uint32(10).string(message.code);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.description !== "") {
      writer.uint32(26).string(message.description);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateTenantRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateTenantRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.code = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseCreateTenantResponse(): CreateTenantResponse {
  return { success: false };
}

export const CreateTenantResponse: MessageFns<CreateTenantResponse> = {
  encode(message: CreateTenantResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.tenant !== undefined) {
      Tenant.encode(message.tenant, writer.uint32(18).fork()).join();
    }
    if (message.error !== undefined) {
      TenantError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateTenantResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateTenantResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tenant = Tenant.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = TenantError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetTenantRequest(): GetTenantRequest {
  return { tenantId: "" };
}

export const GetTenantRequest: MessageFns<GetTenantRequest> = {
  encode(message: GetTenantRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.tenantId !== "") {
      writer.uint32(10).string(message.tenantId);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetTenantRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetTenantRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tenantId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetTenantResponse(): GetTenantResponse {
  return { success: false };
}

export const GetTenantResponse: MessageFns<GetTenantResponse> = {
  encode(message: GetTenantResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.tenant !== undefined) {
      Tenant.encode(message.tenant, writer.uint32(18).fork()).join();
    }
    if (message.error !== undefined) {
      TenantError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetTenantResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetTenantResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tenant = Tenant.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = TenantError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetTenantByCodeRequest(): GetTenantByCodeRequest {
  return { code: "" };
}

export const GetTenantByCodeRequest: MessageFns<GetTenantByCodeRequest> = {
  encode(message: GetTenantByCodeRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.code !== "") {
      writer.uint32(10).string(message.code);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetTenantByCodeRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetTenantByCodeRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.code = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetTenantByCodeResponse(): GetTenantByCodeResponse {
  return { success: false };
}

export const GetTenantByCodeResponse: MessageFns<GetTenantByCodeResponse> = {
  encode(message: GetTenantByCodeResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.tenant !== undefined) {
      Tenant.encode(message.tenant, writer.uint32(18).fork()).join();
    }
    if (message.error !== undefined) {
      TenantError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetTenantByCodeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetTenantByCodeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tenant = Tenant.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = TenantError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseValidateTenantCodeRequest(): ValidateTenantCodeRequest {
  return { code: "" };
}

export const ValidateTenantCodeRequest: MessageFns<ValidateTenantCodeRequest> = {
  encode(message: ValidateTenantCodeRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.code !== "") {
      writer.uint32(10).string(message.code);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ValidateTenantCodeRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseValidateTenantCodeRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.code = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseValidateTenantCodeResponse(): ValidateTenantCodeResponse {
  return { valid: false };
}

export const ValidateTenantCodeResponse: MessageFns<ValidateTenantCodeResponse> = {
  encode(message: ValidateTenantCodeResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.valid !== false) {
      writer.uint32(8).bool(message.valid);
    }
    if (message.tenant !== undefined) {
      Tenant.encode(message.tenant, writer.uint32(18).fork()).join();
    }
    if (message.error !== undefined) {
      TenantError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ValidateTenantCodeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseValidateTenantCodeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.valid = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tenant = Tenant.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = TenantError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUpdateTenantRequest(): UpdateTenantRequest {
  return { tenantId: "", name: "", description: "", status: TenantStatus.TENANT_STATUS_UNKNOWN };
}

export const UpdateTenantRequest: MessageFns<UpdateTenantRequest> = {
  encode(message: UpdateTenantRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.tenantId !== "") {
      writer.uint32(10).string(message.tenantId);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.description !== "") {
      writer.uint32(26).string(message.description);
    }
    if (message.status !== TenantStatus.TENANT_STATUS_UNKNOWN) {
      writer.uint32(32).int32(tenantStatusToNumber(message.status));
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateTenantRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateTenantRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tenantId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.status = tenantStatusFromJSON(reader.int32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUpdateTenantResponse(): UpdateTenantResponse {
  return { success: false };
}

export const UpdateTenantResponse: MessageFns<UpdateTenantResponse> = {
  encode(message: UpdateTenantResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.tenant !== undefined) {
      Tenant.encode(message.tenant, writer.uint32(18).fork()).join();
    }
    if (message.error !== undefined) {
      TenantError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateTenantResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateTenantResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tenant = Tenant.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = TenantError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseTenant(): Tenant {
  return { id: "", code: "", name: "", description: "", status: TenantStatus.TENANT_STATUS_UNKNOWN };
}

export const Tenant: MessageFns<Tenant> = {
  encode(message: Tenant, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.code !== "") {
      writer.uint32(18).string(message.code);
    }
    if (message.name !== "") {
      writer.uint32(26).string(message.name);
    }
    if (message.description !== "") {
      writer.uint32(34).string(message.description);
    }
    if (message.status !== TenantStatus.TENANT_STATUS_UNKNOWN) {
      writer.uint32(40).int32(tenantStatusToNumber(message.status));
    }
    if (message.createdAt !== undefined) {
      Timestamp.encode(toTimestamp(message.createdAt), writer.uint32(50).fork()).join();
    }
    if (message.updatedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.updatedAt), writer.uint32(58).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Tenant {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTenant();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.code = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.status = tenantStatusFromJSON(reader.int32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.createdAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.updatedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseTenantError(): TenantError {
  return { code: "", message: "", details: {}, traceId: "" };
}

export const TenantError: MessageFns<TenantError> = {
  encode(message: TenantError, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.code !== "") {
      writer.uint32(10).string(message.code);
    }
    if (message.message !== "") {
      writer.uint32(18).string(message.message);
    }
    Object.entries(message.details).forEach(([key, value]) => {
      TenantError_DetailsEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    if (message.traceId !== "") {
      writer.uint32(34).string(message.traceId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TenantError {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTenantError();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.code = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = TenantError_DetailsEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.details[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.traceId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseTenantError_DetailsEntry(): TenantError_DetailsEntry {
  return { key: "", value: "" };
}

export const TenantError_DetailsEntry: MessageFns<TenantError_DetailsEntry> = {
  encode(message: TenantError_DetailsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TenantError_DetailsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTenantError_DetailsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseRequestMetadata(): RequestMetadata {
  return { requestId: "", sourceIp: "", userAgent: "" };
}

export const RequestMetadata: MessageFns<RequestMetadata> = {
  encode(message: RequestMetadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.requestId !== "") {
      writer.uint32(10).string(message.requestId);
    }
    if (message.sourceIp !== "") {
      writer.uint32(18).string(message.sourceIp);
    }
    if (message.userAgent !== "") {
      writer.uint32(26).string(message.userAgent);
    }
    if (message.timestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.timestamp), writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RequestMetadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRequestMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.requestId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.sourceIp = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.userAgent = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.timestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseResponseMetadata(): ResponseMetadata {
  return { requestId: "", processingTime: 0 };
}

export const ResponseMetadata: MessageFns<ResponseMetadata> = {
  encode(message: ResponseMetadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.requestId !== "") {
      writer.uint32(10).string(message.requestId);
    }
    if (message.timestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.timestamp), writer.uint32(18).fork()).join();
    }
    if (message.processingTime !== 0) {
      writer.uint32(24).int32(message.processingTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ResponseMetadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseResponseMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.requestId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.timestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.processingTime = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

/** Tenant service definition */

export interface TenantServiceClient {
  /** Tenant management operations */

  createTenant(request: CreateTenantRequest): Observable<CreateTenantResponse>;

  getTenant(request: GetTenantRequest): Observable<GetTenantResponse>;

  getTenantByCode(request: GetTenantByCodeRequest): Observable<GetTenantByCodeResponse>;

  validateTenantCode(request: ValidateTenantCodeRequest): Observable<ValidateTenantCodeResponse>;

  updateTenant(request: UpdateTenantRequest): Observable<UpdateTenantResponse>;
}

/** Tenant service definition */

export interface TenantServiceController {
  /** Tenant management operations */

  createTenant(
    request: CreateTenantRequest,
  ): Promise<CreateTenantResponse> | Observable<CreateTenantResponse> | CreateTenantResponse;

  getTenant(request: GetTenantRequest): Promise<GetTenantResponse> | Observable<GetTenantResponse> | GetTenantResponse;

  getTenantByCode(
    request: GetTenantByCodeRequest,
  ): Promise<GetTenantByCodeResponse> | Observable<GetTenantByCodeResponse> | GetTenantByCodeResponse;

  validateTenantCode(
    request: ValidateTenantCodeRequest,
  ): Promise<ValidateTenantCodeResponse> | Observable<ValidateTenantCodeResponse> | ValidateTenantCodeResponse;

  updateTenant(
    request: UpdateTenantRequest,
  ): Promise<UpdateTenantResponse> | Observable<UpdateTenantResponse> | UpdateTenantResponse;
}

export function TenantServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "createTenant",
      "getTenant",
      "getTenantByCode",
      "validateTenantCode",
      "updateTenant",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("TenantService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("TenantService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const TENANT_SERVICE_NAME = "TenantService";

/** Tenant service definition */
export type TenantServiceService = typeof TenantServiceService;
export const TenantServiceService = {
  /** Tenant management operations */
  createTenant: {
    path: "/tenant.TenantService/CreateTenant",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: CreateTenantRequest): Buffer => Buffer.from(CreateTenantRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): CreateTenantRequest => CreateTenantRequest.decode(value),
    responseSerialize: (value: CreateTenantResponse): Buffer =>
      Buffer.from(CreateTenantResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): CreateTenantResponse => CreateTenantResponse.decode(value),
  },
  getTenant: {
    path: "/tenant.TenantService/GetTenant",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetTenantRequest): Buffer => Buffer.from(GetTenantRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): GetTenantRequest => GetTenantRequest.decode(value),
    responseSerialize: (value: GetTenantResponse): Buffer => Buffer.from(GetTenantResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): GetTenantResponse => GetTenantResponse.decode(value),
  },
  getTenantByCode: {
    path: "/tenant.TenantService/GetTenantByCode",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetTenantByCodeRequest): Buffer =>
      Buffer.from(GetTenantByCodeRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): GetTenantByCodeRequest => GetTenantByCodeRequest.decode(value),
    responseSerialize: (value: GetTenantByCodeResponse): Buffer =>
      Buffer.from(GetTenantByCodeResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): GetTenantByCodeResponse => GetTenantByCodeResponse.decode(value),
  },
  validateTenantCode: {
    path: "/tenant.TenantService/ValidateTenantCode",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: ValidateTenantCodeRequest): Buffer =>
      Buffer.from(ValidateTenantCodeRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): ValidateTenantCodeRequest => ValidateTenantCodeRequest.decode(value),
    responseSerialize: (value: ValidateTenantCodeResponse): Buffer =>
      Buffer.from(ValidateTenantCodeResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): ValidateTenantCodeResponse => ValidateTenantCodeResponse.decode(value),
  },
  updateTenant: {
    path: "/tenant.TenantService/UpdateTenant",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: UpdateTenantRequest): Buffer => Buffer.from(UpdateTenantRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): UpdateTenantRequest => UpdateTenantRequest.decode(value),
    responseSerialize: (value: UpdateTenantResponse): Buffer =>
      Buffer.from(UpdateTenantResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): UpdateTenantResponse => UpdateTenantResponse.decode(value),
  },
} as const;

export interface TenantServiceServer extends UntypedServiceImplementation {
  /** Tenant management operations */
  createTenant: handleUnaryCall<CreateTenantRequest, CreateTenantResponse>;
  getTenant: handleUnaryCall<GetTenantRequest, GetTenantResponse>;
  getTenantByCode: handleUnaryCall<GetTenantByCodeRequest, GetTenantByCodeResponse>;
  validateTenantCode: handleUnaryCall<ValidateTenantCodeRequest, ValidateTenantCodeResponse>;
  updateTenant: handleUnaryCall<UpdateTenantRequest, UpdateTenantResponse>;
}

function toTimestamp(date: Date): Timestamp {
  const seconds = Math.trunc(date.getTime() / 1_000).toString();
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (globalThis.Number(t.seconds) || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
}
