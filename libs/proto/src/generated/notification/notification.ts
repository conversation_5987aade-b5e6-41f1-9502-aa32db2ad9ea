// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: notification/notification.proto

/* eslint-disable */
import { Binary<PERSON><PERSON>er, BinaryWriter } from "@bufbuild/protobuf/wire";
import type { handleUnaryCall, UntypedServiceImplementation } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { wrappers } from "protobufjs";
import { Observable } from "rxjs";
import { Timestamp } from "../google/protobuf/timestamp";

/** Send email request message */
export interface SendEmailRequest {
  recipientEmail: string;
  recipientName: string;
  subject: string;
  templateSlug: string;
  templateData: { [key: string]: string };
  tenantCode: string;
  metadata?: RequestMetadata | undefined;
}

export interface SendEmailRequest_TemplateDataEntry {
  key: string;
  value: string;
}

/** Send email response message */
export interface SendEmailResponse {
  success: boolean;
  notificationId: string;
  message: string;
  error?: NotificationError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Send bulk email request message */
export interface SendBulkEmailRequest {
  recipients: EmailRecipient[];
  subject: string;
  templateSlug: string;
  templateData: { [key: string]: string };
  tenantCode: string;
  metadata?: RequestMetadata | undefined;
}

export interface SendBulkEmailRequest_TemplateDataEntry {
  key: string;
  value: string;
}

/** Send bulk email response message */
export interface SendBulkEmailResponse {
  success: boolean;
  notificationIds: string[];
  message: string;
  error?: NotificationError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Get email status request message */
export interface GetEmailStatusRequest {
  notificationId: string;
  metadata?: RequestMetadata | undefined;
}

/** Get email status response message */
export interface GetEmailStatusResponse {
  success: boolean;
  status?: EmailStatus | undefined;
  error?: NotificationError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Get email history request message */
export interface GetEmailHistoryRequest {
  recipientId: string;
  limit: number;
  offset: number;
  metadata?: RequestMetadata | undefined;
}

/** Get email history response message */
export interface GetEmailHistoryResponse {
  success: boolean;
  notifications: EmailNotification[];
  totalCount: number;
  error?: NotificationError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Get email template request message */
export interface GetEmailTemplateRequest {
  templateSlug: string;
  tenantCode: string;
  metadata?: RequestMetadata | undefined;
}

/** Get email template response message */
export interface GetEmailTemplateResponse {
  success: boolean;
  template?: EmailTemplate | undefined;
  error?: NotificationError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Create email template request message */
export interface CreateEmailTemplateRequest {
  slug: string;
  name: string;
  description: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  category: string;
  tenantCode: string;
  metadata?: RequestMetadata | undefined;
}

/** Create email template response message */
export interface CreateEmailTemplateResponse {
  success: boolean;
  template?: EmailTemplate | undefined;
  error?: NotificationError | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Email recipient message */
export interface EmailRecipient {
  email: string;
  name: string;
  personalData: { [key: string]: string };
}

export interface EmailRecipient_PersonalDataEntry {
  key: string;
  value: string;
}

/** Email status message */
export interface EmailStatus {
  notificationId: string;
  status: string;
  recipientEmail: string;
  sentAt?: Date | undefined;
  deliveredAt?: Date | undefined;
  errorMessage: string;
}

/** Email notification message */
export interface EmailNotification {
  notificationId: string;
  recipientEmail: string;
  subject: string;
  status: string;
  templateSlug: string;
  createdAt?: Date | undefined;
  sentAt?: Date | undefined;
}

/** Email template message */
export interface EmailTemplate {
  id: string;
  slug: string;
  name: string;
  description: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  category: string;
  status: string;
  tenantCode: string;
  createdAt?: Date | undefined;
  updatedAt?: Date | undefined;
}

/** Notification error message */
export interface NotificationError {
  code: string;
  message: string;
  details: string;
}

/** Request metadata message */
export interface RequestMetadata {
  requestId: string;
  userId: string;
  tenantCode: string;
  timestamp?: Date | undefined;
}

/** Response metadata message */
export interface ResponseMetadata {
  requestId: string;
  timestamp?: Date | undefined;
  version: string;
}

wrappers[".google.protobuf.Timestamp"] = {
  fromObject(value: Date) {
    return { seconds: value.getTime() / 1000, nanos: (value.getTime() % 1000) * 1e6 };
  },
  toObject(message: { seconds: number; nanos: number }) {
    return new Date(message.seconds * 1000 + message.nanos / 1e6);
  },
} as any;

function createBaseSendEmailRequest(): SendEmailRequest {
  return { recipientEmail: "", recipientName: "", subject: "", templateSlug: "", templateData: {}, tenantCode: "" };
}

export const SendEmailRequest: MessageFns<SendEmailRequest> = {
  encode(message: SendEmailRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.recipientEmail !== "") {
      writer.uint32(10).string(message.recipientEmail);
    }
    if (message.recipientName !== "") {
      writer.uint32(18).string(message.recipientName);
    }
    if (message.subject !== "") {
      writer.uint32(26).string(message.subject);
    }
    if (message.templateSlug !== "") {
      writer.uint32(34).string(message.templateSlug);
    }
    Object.entries(message.templateData).forEach(([key, value]) => {
      SendEmailRequest_TemplateDataEntry.encode({ key: key as any, value }, writer.uint32(42).fork()).join();
    });
    if (message.tenantCode !== "") {
      writer.uint32(50).string(message.tenantCode);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(58).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendEmailRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendEmailRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.recipientEmail = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.recipientName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.subject = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.templateSlug = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          const entry5 = SendEmailRequest_TemplateDataEntry.decode(reader, reader.uint32());
          if (entry5.value !== undefined) {
            message.templateData[entry5.key] = entry5.value;
          }
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.tenantCode = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseSendEmailRequest_TemplateDataEntry(): SendEmailRequest_TemplateDataEntry {
  return { key: "", value: "" };
}

export const SendEmailRequest_TemplateDataEntry: MessageFns<SendEmailRequest_TemplateDataEntry> = {
  encode(message: SendEmailRequest_TemplateDataEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendEmailRequest_TemplateDataEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendEmailRequest_TemplateDataEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseSendEmailResponse(): SendEmailResponse {
  return { success: false, notificationId: "", message: "" };
}

export const SendEmailResponse: MessageFns<SendEmailResponse> = {
  encode(message: SendEmailResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.notificationId !== "") {
      writer.uint32(18).string(message.notificationId);
    }
    if (message.message !== "") {
      writer.uint32(26).string(message.message);
    }
    if (message.error !== undefined) {
      NotificationError.encode(message.error, writer.uint32(34).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendEmailResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendEmailResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.notificationId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.error = NotificationError.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseSendBulkEmailRequest(): SendBulkEmailRequest {
  return { recipients: [], subject: "", templateSlug: "", templateData: {}, tenantCode: "" };
}

export const SendBulkEmailRequest: MessageFns<SendBulkEmailRequest> = {
  encode(message: SendBulkEmailRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.recipients) {
      EmailRecipient.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.subject !== "") {
      writer.uint32(18).string(message.subject);
    }
    if (message.templateSlug !== "") {
      writer.uint32(26).string(message.templateSlug);
    }
    Object.entries(message.templateData).forEach(([key, value]) => {
      SendBulkEmailRequest_TemplateDataEntry.encode({ key: key as any, value }, writer.uint32(34).fork()).join();
    });
    if (message.tenantCode !== "") {
      writer.uint32(42).string(message.tenantCode);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendBulkEmailRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendBulkEmailRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.recipients.push(EmailRecipient.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.subject = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.templateSlug = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          const entry4 = SendBulkEmailRequest_TemplateDataEntry.decode(reader, reader.uint32());
          if (entry4.value !== undefined) {
            message.templateData[entry4.key] = entry4.value;
          }
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.tenantCode = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseSendBulkEmailRequest_TemplateDataEntry(): SendBulkEmailRequest_TemplateDataEntry {
  return { key: "", value: "" };
}

export const SendBulkEmailRequest_TemplateDataEntry: MessageFns<SendBulkEmailRequest_TemplateDataEntry> = {
  encode(message: SendBulkEmailRequest_TemplateDataEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendBulkEmailRequest_TemplateDataEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendBulkEmailRequest_TemplateDataEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseSendBulkEmailResponse(): SendBulkEmailResponse {
  return { success: false, notificationIds: [], message: "" };
}

export const SendBulkEmailResponse: MessageFns<SendBulkEmailResponse> = {
  encode(message: SendBulkEmailResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    for (const v of message.notificationIds) {
      writer.uint32(18).string(v!);
    }
    if (message.message !== "") {
      writer.uint32(26).string(message.message);
    }
    if (message.error !== undefined) {
      NotificationError.encode(message.error, writer.uint32(34).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendBulkEmailResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendBulkEmailResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.notificationIds.push(reader.string());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.error = NotificationError.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetEmailStatusRequest(): GetEmailStatusRequest {
  return { notificationId: "" };
}

export const GetEmailStatusRequest: MessageFns<GetEmailStatusRequest> = {
  encode(message: GetEmailStatusRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.notificationId !== "") {
      writer.uint32(10).string(message.notificationId);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetEmailStatusRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetEmailStatusRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.notificationId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetEmailStatusResponse(): GetEmailStatusResponse {
  return { success: false };
}

export const GetEmailStatusResponse: MessageFns<GetEmailStatusResponse> = {
  encode(message: GetEmailStatusResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.status !== undefined) {
      EmailStatus.encode(message.status, writer.uint32(18).fork()).join();
    }
    if (message.error !== undefined) {
      NotificationError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetEmailStatusResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetEmailStatusResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.status = EmailStatus.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = NotificationError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetEmailHistoryRequest(): GetEmailHistoryRequest {
  return { recipientId: "", limit: 0, offset: 0 };
}

export const GetEmailHistoryRequest: MessageFns<GetEmailHistoryRequest> = {
  encode(message: GetEmailHistoryRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.recipientId !== "") {
      writer.uint32(10).string(message.recipientId);
    }
    if (message.limit !== 0) {
      writer.uint32(16).int32(message.limit);
    }
    if (message.offset !== 0) {
      writer.uint32(24).int32(message.offset);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetEmailHistoryRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetEmailHistoryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.recipientId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.limit = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.offset = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetEmailHistoryResponse(): GetEmailHistoryResponse {
  return { success: false, notifications: [], totalCount: 0 };
}

export const GetEmailHistoryResponse: MessageFns<GetEmailHistoryResponse> = {
  encode(message: GetEmailHistoryResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    for (const v of message.notifications) {
      EmailNotification.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.totalCount !== 0) {
      writer.uint32(24).int32(message.totalCount);
    }
    if (message.error !== undefined) {
      NotificationError.encode(message.error, writer.uint32(34).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetEmailHistoryResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetEmailHistoryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.notifications.push(EmailNotification.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.totalCount = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.error = NotificationError.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetEmailTemplateRequest(): GetEmailTemplateRequest {
  return { templateSlug: "", tenantCode: "" };
}

export const GetEmailTemplateRequest: MessageFns<GetEmailTemplateRequest> = {
  encode(message: GetEmailTemplateRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.templateSlug !== "") {
      writer.uint32(10).string(message.templateSlug);
    }
    if (message.tenantCode !== "") {
      writer.uint32(18).string(message.tenantCode);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetEmailTemplateRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetEmailTemplateRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.templateSlug = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tenantCode = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetEmailTemplateResponse(): GetEmailTemplateResponse {
  return { success: false };
}

export const GetEmailTemplateResponse: MessageFns<GetEmailTemplateResponse> = {
  encode(message: GetEmailTemplateResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.template !== undefined) {
      EmailTemplate.encode(message.template, writer.uint32(18).fork()).join();
    }
    if (message.error !== undefined) {
      NotificationError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetEmailTemplateResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetEmailTemplateResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.template = EmailTemplate.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = NotificationError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseCreateEmailTemplateRequest(): CreateEmailTemplateRequest {
  return {
    slug: "",
    name: "",
    description: "",
    subject: "",
    htmlContent: "",
    textContent: "",
    category: "",
    tenantCode: "",
  };
}

export const CreateEmailTemplateRequest: MessageFns<CreateEmailTemplateRequest> = {
  encode(message: CreateEmailTemplateRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slug !== "") {
      writer.uint32(10).string(message.slug);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.description !== "") {
      writer.uint32(26).string(message.description);
    }
    if (message.subject !== "") {
      writer.uint32(34).string(message.subject);
    }
    if (message.htmlContent !== "") {
      writer.uint32(42).string(message.htmlContent);
    }
    if (message.textContent !== "") {
      writer.uint32(50).string(message.textContent);
    }
    if (message.category !== "") {
      writer.uint32(58).string(message.category);
    }
    if (message.tenantCode !== "") {
      writer.uint32(66).string(message.tenantCode);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(74).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateEmailTemplateRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateEmailTemplateRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.slug = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.subject = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.htmlContent = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.textContent = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.category = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.tenantCode = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseCreateEmailTemplateResponse(): CreateEmailTemplateResponse {
  return { success: false };
}

export const CreateEmailTemplateResponse: MessageFns<CreateEmailTemplateResponse> = {
  encode(message: CreateEmailTemplateResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.template !== undefined) {
      EmailTemplate.encode(message.template, writer.uint32(18).fork()).join();
    }
    if (message.error !== undefined) {
      NotificationError.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateEmailTemplateResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateEmailTemplateResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.template = EmailTemplate.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = NotificationError.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseEmailRecipient(): EmailRecipient {
  return { email: "", name: "", personalData: {} };
}

export const EmailRecipient: MessageFns<EmailRecipient> = {
  encode(message: EmailRecipient, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.email !== "") {
      writer.uint32(10).string(message.email);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    Object.entries(message.personalData).forEach(([key, value]) => {
      EmailRecipient_PersonalDataEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmailRecipient {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmailRecipient();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.email = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = EmailRecipient_PersonalDataEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.personalData[entry3.key] = entry3.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseEmailRecipient_PersonalDataEntry(): EmailRecipient_PersonalDataEntry {
  return { key: "", value: "" };
}

export const EmailRecipient_PersonalDataEntry: MessageFns<EmailRecipient_PersonalDataEntry> = {
  encode(message: EmailRecipient_PersonalDataEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmailRecipient_PersonalDataEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmailRecipient_PersonalDataEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseEmailStatus(): EmailStatus {
  return { notificationId: "", status: "", recipientEmail: "", errorMessage: "" };
}

export const EmailStatus: MessageFns<EmailStatus> = {
  encode(message: EmailStatus, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.notificationId !== "") {
      writer.uint32(10).string(message.notificationId);
    }
    if (message.status !== "") {
      writer.uint32(18).string(message.status);
    }
    if (message.recipientEmail !== "") {
      writer.uint32(26).string(message.recipientEmail);
    }
    if (message.sentAt !== undefined) {
      Timestamp.encode(toTimestamp(message.sentAt), writer.uint32(34).fork()).join();
    }
    if (message.deliveredAt !== undefined) {
      Timestamp.encode(toTimestamp(message.deliveredAt), writer.uint32(42).fork()).join();
    }
    if (message.errorMessage !== "") {
      writer.uint32(50).string(message.errorMessage);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmailStatus {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmailStatus();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.notificationId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.status = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.recipientEmail = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.sentAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.deliveredAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.errorMessage = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseEmailNotification(): EmailNotification {
  return { notificationId: "", recipientEmail: "", subject: "", status: "", templateSlug: "" };
}

export const EmailNotification: MessageFns<EmailNotification> = {
  encode(message: EmailNotification, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.notificationId !== "") {
      writer.uint32(10).string(message.notificationId);
    }
    if (message.recipientEmail !== "") {
      writer.uint32(18).string(message.recipientEmail);
    }
    if (message.subject !== "") {
      writer.uint32(26).string(message.subject);
    }
    if (message.status !== "") {
      writer.uint32(34).string(message.status);
    }
    if (message.templateSlug !== "") {
      writer.uint32(42).string(message.templateSlug);
    }
    if (message.createdAt !== undefined) {
      Timestamp.encode(toTimestamp(message.createdAt), writer.uint32(50).fork()).join();
    }
    if (message.sentAt !== undefined) {
      Timestamp.encode(toTimestamp(message.sentAt), writer.uint32(58).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmailNotification {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmailNotification();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.notificationId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.recipientEmail = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.subject = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.status = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.templateSlug = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.createdAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.sentAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseEmailTemplate(): EmailTemplate {
  return {
    id: "",
    slug: "",
    name: "",
    description: "",
    subject: "",
    htmlContent: "",
    textContent: "",
    category: "",
    status: "",
    tenantCode: "",
  };
}

export const EmailTemplate: MessageFns<EmailTemplate> = {
  encode(message: EmailTemplate, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.slug !== "") {
      writer.uint32(18).string(message.slug);
    }
    if (message.name !== "") {
      writer.uint32(26).string(message.name);
    }
    if (message.description !== "") {
      writer.uint32(34).string(message.description);
    }
    if (message.subject !== "") {
      writer.uint32(42).string(message.subject);
    }
    if (message.htmlContent !== "") {
      writer.uint32(50).string(message.htmlContent);
    }
    if (message.textContent !== "") {
      writer.uint32(58).string(message.textContent);
    }
    if (message.category !== "") {
      writer.uint32(66).string(message.category);
    }
    if (message.status !== "") {
      writer.uint32(74).string(message.status);
    }
    if (message.tenantCode !== "") {
      writer.uint32(82).string(message.tenantCode);
    }
    if (message.createdAt !== undefined) {
      Timestamp.encode(toTimestamp(message.createdAt), writer.uint32(90).fork()).join();
    }
    if (message.updatedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.updatedAt), writer.uint32(98).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmailTemplate {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmailTemplate();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.slug = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.subject = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.htmlContent = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.textContent = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.category = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.status = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.tenantCode = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.createdAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.updatedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseNotificationError(): NotificationError {
  return { code: "", message: "", details: "" };
}

export const NotificationError: MessageFns<NotificationError> = {
  encode(message: NotificationError, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.code !== "") {
      writer.uint32(10).string(message.code);
    }
    if (message.message !== "") {
      writer.uint32(18).string(message.message);
    }
    if (message.details !== "") {
      writer.uint32(26).string(message.details);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): NotificationError {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNotificationError();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.code = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.details = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseRequestMetadata(): RequestMetadata {
  return { requestId: "", userId: "", tenantCode: "" };
}

export const RequestMetadata: MessageFns<RequestMetadata> = {
  encode(message: RequestMetadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.requestId !== "") {
      writer.uint32(10).string(message.requestId);
    }
    if (message.userId !== "") {
      writer.uint32(18).string(message.userId);
    }
    if (message.tenantCode !== "") {
      writer.uint32(26).string(message.tenantCode);
    }
    if (message.timestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.timestamp), writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RequestMetadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRequestMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.requestId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.tenantCode = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.timestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseResponseMetadata(): ResponseMetadata {
  return { requestId: "", version: "" };
}

export const ResponseMetadata: MessageFns<ResponseMetadata> = {
  encode(message: ResponseMetadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.requestId !== "") {
      writer.uint32(10).string(message.requestId);
    }
    if (message.timestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.timestamp), writer.uint32(18).fork()).join();
    }
    if (message.version !== "") {
      writer.uint32(26).string(message.version);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ResponseMetadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseResponseMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.requestId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.timestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.version = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

/** Notification service definition */

export interface NotificationServiceClient {
  /** Email operations */

  sendEmail(request: SendEmailRequest): Observable<SendEmailResponse>;

  sendBulkEmail(request: SendBulkEmailRequest): Observable<SendBulkEmailResponse>;

  getEmailStatus(request: GetEmailStatusRequest): Observable<GetEmailStatusResponse>;

  getEmailHistory(request: GetEmailHistoryRequest): Observable<GetEmailHistoryResponse>;

  /** Template operations */

  getEmailTemplate(request: GetEmailTemplateRequest): Observable<GetEmailTemplateResponse>;

  createEmailTemplate(request: CreateEmailTemplateRequest): Observable<CreateEmailTemplateResponse>;
}

/** Notification service definition */

export interface NotificationServiceController {
  /** Email operations */

  sendEmail(request: SendEmailRequest): Promise<SendEmailResponse> | Observable<SendEmailResponse> | SendEmailResponse;

  sendBulkEmail(
    request: SendBulkEmailRequest,
  ): Promise<SendBulkEmailResponse> | Observable<SendBulkEmailResponse> | SendBulkEmailResponse;

  getEmailStatus(
    request: GetEmailStatusRequest,
  ): Promise<GetEmailStatusResponse> | Observable<GetEmailStatusResponse> | GetEmailStatusResponse;

  getEmailHistory(
    request: GetEmailHistoryRequest,
  ): Promise<GetEmailHistoryResponse> | Observable<GetEmailHistoryResponse> | GetEmailHistoryResponse;

  /** Template operations */

  getEmailTemplate(
    request: GetEmailTemplateRequest,
  ): Promise<GetEmailTemplateResponse> | Observable<GetEmailTemplateResponse> | GetEmailTemplateResponse;

  createEmailTemplate(
    request: CreateEmailTemplateRequest,
  ): Promise<CreateEmailTemplateResponse> | Observable<CreateEmailTemplateResponse> | CreateEmailTemplateResponse;
}

export function NotificationServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "sendEmail",
      "sendBulkEmail",
      "getEmailStatus",
      "getEmailHistory",
      "getEmailTemplate",
      "createEmailTemplate",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("NotificationService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("NotificationService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const NOTIFICATION_SERVICE_NAME = "NotificationService";

/** Notification service definition */
export type NotificationServiceService = typeof NotificationServiceService;
export const NotificationServiceService = {
  /** Email operations */
  sendEmail: {
    path: "/notification.NotificationService/SendEmail",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: SendEmailRequest): Buffer => Buffer.from(SendEmailRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): SendEmailRequest => SendEmailRequest.decode(value),
    responseSerialize: (value: SendEmailResponse): Buffer => Buffer.from(SendEmailResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): SendEmailResponse => SendEmailResponse.decode(value),
  },
  sendBulkEmail: {
    path: "/notification.NotificationService/SendBulkEmail",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: SendBulkEmailRequest): Buffer => Buffer.from(SendBulkEmailRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): SendBulkEmailRequest => SendBulkEmailRequest.decode(value),
    responseSerialize: (value: SendBulkEmailResponse): Buffer =>
      Buffer.from(SendBulkEmailResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): SendBulkEmailResponse => SendBulkEmailResponse.decode(value),
  },
  getEmailStatus: {
    path: "/notification.NotificationService/GetEmailStatus",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetEmailStatusRequest): Buffer =>
      Buffer.from(GetEmailStatusRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): GetEmailStatusRequest => GetEmailStatusRequest.decode(value),
    responseSerialize: (value: GetEmailStatusResponse): Buffer =>
      Buffer.from(GetEmailStatusResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): GetEmailStatusResponse => GetEmailStatusResponse.decode(value),
  },
  getEmailHistory: {
    path: "/notification.NotificationService/GetEmailHistory",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetEmailHistoryRequest): Buffer =>
      Buffer.from(GetEmailHistoryRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): GetEmailHistoryRequest => GetEmailHistoryRequest.decode(value),
    responseSerialize: (value: GetEmailHistoryResponse): Buffer =>
      Buffer.from(GetEmailHistoryResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): GetEmailHistoryResponse => GetEmailHistoryResponse.decode(value),
  },
  /** Template operations */
  getEmailTemplate: {
    path: "/notification.NotificationService/GetEmailTemplate",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetEmailTemplateRequest): Buffer =>
      Buffer.from(GetEmailTemplateRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): GetEmailTemplateRequest => GetEmailTemplateRequest.decode(value),
    responseSerialize: (value: GetEmailTemplateResponse): Buffer =>
      Buffer.from(GetEmailTemplateResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): GetEmailTemplateResponse => GetEmailTemplateResponse.decode(value),
  },
  createEmailTemplate: {
    path: "/notification.NotificationService/CreateEmailTemplate",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: CreateEmailTemplateRequest): Buffer =>
      Buffer.from(CreateEmailTemplateRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): CreateEmailTemplateRequest => CreateEmailTemplateRequest.decode(value),
    responseSerialize: (value: CreateEmailTemplateResponse): Buffer =>
      Buffer.from(CreateEmailTemplateResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): CreateEmailTemplateResponse => CreateEmailTemplateResponse.decode(value),
  },
} as const;

export interface NotificationServiceServer extends UntypedServiceImplementation {
  /** Email operations */
  sendEmail: handleUnaryCall<SendEmailRequest, SendEmailResponse>;
  sendBulkEmail: handleUnaryCall<SendBulkEmailRequest, SendBulkEmailResponse>;
  getEmailStatus: handleUnaryCall<GetEmailStatusRequest, GetEmailStatusResponse>;
  getEmailHistory: handleUnaryCall<GetEmailHistoryRequest, GetEmailHistoryResponse>;
  /** Template operations */
  getEmailTemplate: handleUnaryCall<GetEmailTemplateRequest, GetEmailTemplateResponse>;
  createEmailTemplate: handleUnaryCall<CreateEmailTemplateRequest, CreateEmailTemplateResponse>;
}

function toTimestamp(date: Date): Timestamp {
  const seconds = Math.trunc(date.getTime() / 1_000).toString();
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (globalThis.Number(t.seconds) || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
}
