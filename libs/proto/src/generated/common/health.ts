// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: common/health.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import type { handleUnaryCall, UntypedServiceImplementation } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { wrappers } from "protobufjs";
import { Observable } from "rxjs";
import { Timestamp } from "../google/protobuf/timestamp";

/** Health status enumeration */
export enum HealthStatus {
  HEALTH_STATUS_UNKNOWN = "HEALTH_STATUS_UNKNOWN",
  HEALTH_STATUS_HEALTHY = "HEALTH_STATUS_HEALTHY",
  HEALTH_STATUS_UNHEALTHY = "HEALTH_STATUS_UNHEALTHY",
  HEALTH_STATUS_DEGRADED = "HEALTH_STATUS_DEGRADED",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function healthStatusFromJSON(object: any): HealthStatus {
  switch (object) {
    case 0:
    case "HEALTH_STATUS_UNKNOWN":
      return HealthStatus.HEALTH_STATUS_UNKNOWN;
    case 1:
    case "HEALTH_STATUS_HEALTHY":
      return HealthStatus.HEALTH_STATUS_HEALTHY;
    case 2:
    case "HEALTH_STATUS_UNHEALTHY":
      return HealthStatus.HEALTH_STATUS_UNHEALTHY;
    case 3:
    case "HEALTH_STATUS_DEGRADED":
      return HealthStatus.HEALTH_STATUS_DEGRADED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return HealthStatus.UNRECOGNIZED;
  }
}

export function healthStatusToNumber(object: HealthStatus): number {
  switch (object) {
    case HealthStatus.HEALTH_STATUS_UNKNOWN:
      return 0;
    case HealthStatus.HEALTH_STATUS_HEALTHY:
      return 1;
    case HealthStatus.HEALTH_STATUS_UNHEALTHY:
      return 2;
    case HealthStatus.HEALTH_STATUS_DEGRADED:
      return 3;
    case HealthStatus.UNRECOGNIZED:
    default:
      return -1;
  }
}

/** Service status enumeration */
export enum ServiceStatus {
  SERVICE_STATUS_UNKNOWN = "SERVICE_STATUS_UNKNOWN",
  SERVICE_STATUS_UP = "SERVICE_STATUS_UP",
  SERVICE_STATUS_DOWN = "SERVICE_STATUS_DOWN",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function serviceStatusFromJSON(object: any): ServiceStatus {
  switch (object) {
    case 0:
    case "SERVICE_STATUS_UNKNOWN":
      return ServiceStatus.SERVICE_STATUS_UNKNOWN;
    case 1:
    case "SERVICE_STATUS_UP":
      return ServiceStatus.SERVICE_STATUS_UP;
    case 2:
    case "SERVICE_STATUS_DOWN":
      return ServiceStatus.SERVICE_STATUS_DOWN;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ServiceStatus.UNRECOGNIZED;
  }
}

export function serviceStatusToNumber(object: ServiceStatus): number {
  switch (object) {
    case ServiceStatus.SERVICE_STATUS_UNKNOWN:
      return 0;
    case ServiceStatus.SERVICE_STATUS_UP:
      return 1;
    case ServiceStatus.SERVICE_STATUS_DOWN:
      return 2;
    case ServiceStatus.UNRECOGNIZED:
    default:
      return -1;
  }
}

/** Readiness status enumeration */
export enum ReadinessStatus {
  READINESS_STATUS_UNKNOWN = "READINESS_STATUS_UNKNOWN",
  READINESS_STATUS_READY = "READINESS_STATUS_READY",
  READINESS_STATUS_NOT_READY = "READINESS_STATUS_NOT_READY",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function readinessStatusFromJSON(object: any): ReadinessStatus {
  switch (object) {
    case 0:
    case "READINESS_STATUS_UNKNOWN":
      return ReadinessStatus.READINESS_STATUS_UNKNOWN;
    case 1:
    case "READINESS_STATUS_READY":
      return ReadinessStatus.READINESS_STATUS_READY;
    case 2:
    case "READINESS_STATUS_NOT_READY":
      return ReadinessStatus.READINESS_STATUS_NOT_READY;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ReadinessStatus.UNRECOGNIZED;
  }
}

export function readinessStatusToNumber(object: ReadinessStatus): number {
  switch (object) {
    case ReadinessStatus.READINESS_STATUS_UNKNOWN:
      return 0;
    case ReadinessStatus.READINESS_STATUS_READY:
      return 1;
    case ReadinessStatus.READINESS_STATUS_NOT_READY:
      return 2;
    case ReadinessStatus.UNRECOGNIZED:
    default:
      return -1;
  }
}

/** Liveness status enumeration */
export enum LivenessStatus {
  LIVENESS_STATUS_UNKNOWN = "LIVENESS_STATUS_UNKNOWN",
  LIVENESS_STATUS_ALIVE = "LIVENESS_STATUS_ALIVE",
  LIVENESS_STATUS_DEAD = "LIVENESS_STATUS_DEAD",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function livenessStatusFromJSON(object: any): LivenessStatus {
  switch (object) {
    case 0:
    case "LIVENESS_STATUS_UNKNOWN":
      return LivenessStatus.LIVENESS_STATUS_UNKNOWN;
    case 1:
    case "LIVENESS_STATUS_ALIVE":
      return LivenessStatus.LIVENESS_STATUS_ALIVE;
    case 2:
    case "LIVENESS_STATUS_DEAD":
      return LivenessStatus.LIVENESS_STATUS_DEAD;
    case -1:
    case "UNRECOGNIZED":
    default:
      return LivenessStatus.UNRECOGNIZED;
  }
}

export function livenessStatusToNumber(object: LivenessStatus): number {
  switch (object) {
    case LivenessStatus.LIVENESS_STATUS_UNKNOWN:
      return 0;
    case LivenessStatus.LIVENESS_STATUS_ALIVE:
      return 1;
    case LivenessStatus.LIVENESS_STATUS_DEAD:
      return 2;
    case LivenessStatus.UNRECOGNIZED:
    default:
      return -1;
  }
}

/** Health check request */
export interface HealthCheckRequest {
  /** Service name to check (optional, defaults to current service) */
  service: string;
  /** Request metadata */
  metadata?: RequestMetadata | undefined;
}

/** Basic health check response */
export interface HealthCheckResponse {
  /** Health status */
  status: HealthStatus;
  /** System information */
  system?:
    | SystemInfo
    | undefined;
  /** Optional message */
  message: string;
  /** Response metadata */
  metadata?: ResponseMetadata | undefined;
}

/** Detailed health check response */
export interface DetailedHealthCheckResponse {
  /** Health status */
  status: HealthStatus;
  /** System information */
  system?:
    | SystemInfo
    | undefined;
  /** Dependency health information */
  dependencies: ServiceHealth[];
  /** Additional health checks */
  checks: { [key: string]: string };
  /** Optional message */
  message: string;
  /** Response metadata */
  metadata?: ResponseMetadata | undefined;
}

export interface DetailedHealthCheckResponse_ChecksEntry {
  key: string;
  value: string;
}

/** Readiness check response */
export interface ReadinessCheckResponse {
  /** Readiness status */
  status: ReadinessStatus;
  /** Timestamp */
  timestamp?:
    | Date
    | undefined;
  /** Readiness checks */
  checks: { [key: string]: string };
  /** Optional message */
  message: string;
  /** Response metadata */
  metadata?: ResponseMetadata | undefined;
}

export interface ReadinessCheckResponse_ChecksEntry {
  key: string;
  value: string;
}

/** Liveness check response */
export interface LivenessCheckResponse {
  /** Liveness status */
  status: LivenessStatus;
  /** Timestamp */
  timestamp?:
    | Date
    | undefined;
  /** Service uptime in milliseconds */
  uptime: string;
  /** Memory usage information */
  memoryUsage?:
    | MemoryUsage
    | undefined;
  /** Optional message */
  message: string;
  /** Response metadata */
  metadata?: ResponseMetadata | undefined;
}

/** System information */
export interface SystemInfo {
  /** Service name */
  service: string;
  /** Service version */
  version: string;
  /** Environment (development, staging, production) */
  environment: string;
  /** Service uptime in milliseconds */
  uptime: string;
  /** Timestamp */
  timestamp?:
    | Date
    | undefined;
  /** Node.js version */
  nodeVersion: string;
  /** Memory usage information */
  memoryUsage?:
    | MemoryUsage
    | undefined;
  /** CPU usage percentage (optional) */
  cpuUsage: number;
}

/** Service health information */
export interface ServiceHealth {
  /** Service name */
  name: string;
  /** Service status */
  status: ServiceStatus;
  /** Response time in milliseconds */
  responseTime: number;
  /** Last checked timestamp */
  lastChecked?:
    | Date
    | undefined;
  /** Error message (if any) */
  error: string;
  /** Service URL */
  url: string;
}

/** Memory usage information */
export interface MemoryUsage {
  /** Used memory in MB */
  used: number;
  /** Total memory in MB */
  total: number;
  /** Memory usage percentage */
  percentage: number;
}

/** Request metadata */
export interface RequestMetadata {
  /** Request ID for tracing */
  requestId: string;
  /** Source IP address */
  sourceIp: string;
  /** User agent */
  userAgent: string;
  /** Timestamp */
  timestamp?: Date | undefined;
}

/** Response metadata */
export interface ResponseMetadata {
  /** Request ID for tracing */
  requestId: string;
  /** Response timestamp */
  timestamp?:
    | Date
    | undefined;
  /** Processing time in milliseconds */
  processingTime: number;
}

wrappers[".google.protobuf.Timestamp"] = {
  fromObject(value: Date) {
    return { seconds: value.getTime() / 1000, nanos: (value.getTime() % 1000) * 1e6 };
  },
  toObject(message: { seconds: number; nanos: number }) {
    return new Date(message.seconds * 1000 + message.nanos / 1e6);
  },
} as any;

function createBaseHealthCheckRequest(): HealthCheckRequest {
  return { service: "" };
}

export const HealthCheckRequest: MessageFns<HealthCheckRequest> = {
  encode(message: HealthCheckRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.service !== "") {
      writer.uint32(10).string(message.service);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HealthCheckRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHealthCheckRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.service = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseHealthCheckResponse(): HealthCheckResponse {
  return { status: HealthStatus.HEALTH_STATUS_UNKNOWN, message: "" };
}

export const HealthCheckResponse: MessageFns<HealthCheckResponse> = {
  encode(message: HealthCheckResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.status !== HealthStatus.HEALTH_STATUS_UNKNOWN) {
      writer.uint32(8).int32(healthStatusToNumber(message.status));
    }
    if (message.system !== undefined) {
      SystemInfo.encode(message.system, writer.uint32(18).fork()).join();
    }
    if (message.message !== "") {
      writer.uint32(26).string(message.message);
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HealthCheckResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHealthCheckResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.status = healthStatusFromJSON(reader.int32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.system = SystemInfo.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseDetailedHealthCheckResponse(): DetailedHealthCheckResponse {
  return { status: HealthStatus.HEALTH_STATUS_UNKNOWN, dependencies: [], checks: {}, message: "" };
}

export const DetailedHealthCheckResponse: MessageFns<DetailedHealthCheckResponse> = {
  encode(message: DetailedHealthCheckResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.status !== HealthStatus.HEALTH_STATUS_UNKNOWN) {
      writer.uint32(8).int32(healthStatusToNumber(message.status));
    }
    if (message.system !== undefined) {
      SystemInfo.encode(message.system, writer.uint32(18).fork()).join();
    }
    for (const v of message.dependencies) {
      ServiceHealth.encode(v!, writer.uint32(26).fork()).join();
    }
    Object.entries(message.checks).forEach(([key, value]) => {
      DetailedHealthCheckResponse_ChecksEntry.encode({ key: key as any, value }, writer.uint32(34).fork()).join();
    });
    if (message.message !== "") {
      writer.uint32(42).string(message.message);
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DetailedHealthCheckResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDetailedHealthCheckResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.status = healthStatusFromJSON(reader.int32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.system = SystemInfo.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.dependencies.push(ServiceHealth.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          const entry4 = DetailedHealthCheckResponse_ChecksEntry.decode(reader, reader.uint32());
          if (entry4.value !== undefined) {
            message.checks[entry4.key] = entry4.value;
          }
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseDetailedHealthCheckResponse_ChecksEntry(): DetailedHealthCheckResponse_ChecksEntry {
  return { key: "", value: "" };
}

export const DetailedHealthCheckResponse_ChecksEntry: MessageFns<DetailedHealthCheckResponse_ChecksEntry> = {
  encode(message: DetailedHealthCheckResponse_ChecksEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DetailedHealthCheckResponse_ChecksEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDetailedHealthCheckResponse_ChecksEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseReadinessCheckResponse(): ReadinessCheckResponse {
  return { status: ReadinessStatus.READINESS_STATUS_UNKNOWN, checks: {}, message: "" };
}

export const ReadinessCheckResponse: MessageFns<ReadinessCheckResponse> = {
  encode(message: ReadinessCheckResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.status !== ReadinessStatus.READINESS_STATUS_UNKNOWN) {
      writer.uint32(8).int32(readinessStatusToNumber(message.status));
    }
    if (message.timestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.timestamp), writer.uint32(18).fork()).join();
    }
    Object.entries(message.checks).forEach(([key, value]) => {
      ReadinessCheckResponse_ChecksEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    if (message.message !== "") {
      writer.uint32(34).string(message.message);
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ReadinessCheckResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReadinessCheckResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.status = readinessStatusFromJSON(reader.int32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.timestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = ReadinessCheckResponse_ChecksEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.checks[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseReadinessCheckResponse_ChecksEntry(): ReadinessCheckResponse_ChecksEntry {
  return { key: "", value: "" };
}

export const ReadinessCheckResponse_ChecksEntry: MessageFns<ReadinessCheckResponse_ChecksEntry> = {
  encode(message: ReadinessCheckResponse_ChecksEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ReadinessCheckResponse_ChecksEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReadinessCheckResponse_ChecksEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseLivenessCheckResponse(): LivenessCheckResponse {
  return { status: LivenessStatus.LIVENESS_STATUS_UNKNOWN, uptime: "0", message: "" };
}

export const LivenessCheckResponse: MessageFns<LivenessCheckResponse> = {
  encode(message: LivenessCheckResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.status !== LivenessStatus.LIVENESS_STATUS_UNKNOWN) {
      writer.uint32(8).int32(livenessStatusToNumber(message.status));
    }
    if (message.timestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.timestamp), writer.uint32(18).fork()).join();
    }
    if (message.uptime !== "0") {
      writer.uint32(24).int64(message.uptime);
    }
    if (message.memoryUsage !== undefined) {
      MemoryUsage.encode(message.memoryUsage, writer.uint32(34).fork()).join();
    }
    if (message.message !== "") {
      writer.uint32(42).string(message.message);
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LivenessCheckResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLivenessCheckResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.status = livenessStatusFromJSON(reader.int32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.timestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.uptime = reader.int64().toString();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.memoryUsage = MemoryUsage.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseSystemInfo(): SystemInfo {
  return { service: "", version: "", environment: "", uptime: "0", nodeVersion: "", cpuUsage: 0 };
}

export const SystemInfo: MessageFns<SystemInfo> = {
  encode(message: SystemInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.service !== "") {
      writer.uint32(10).string(message.service);
    }
    if (message.version !== "") {
      writer.uint32(18).string(message.version);
    }
    if (message.environment !== "") {
      writer.uint32(26).string(message.environment);
    }
    if (message.uptime !== "0") {
      writer.uint32(32).int64(message.uptime);
    }
    if (message.timestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.timestamp), writer.uint32(42).fork()).join();
    }
    if (message.nodeVersion !== "") {
      writer.uint32(50).string(message.nodeVersion);
    }
    if (message.memoryUsage !== undefined) {
      MemoryUsage.encode(message.memoryUsage, writer.uint32(58).fork()).join();
    }
    if (message.cpuUsage !== 0) {
      writer.uint32(65).double(message.cpuUsage);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SystemInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSystemInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.service = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.version = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.environment = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.uptime = reader.int64().toString();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.timestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.nodeVersion = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.memoryUsage = MemoryUsage.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 65) {
            break;
          }

          message.cpuUsage = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseServiceHealth(): ServiceHealth {
  return { name: "", status: ServiceStatus.SERVICE_STATUS_UNKNOWN, responseTime: 0, error: "", url: "" };
}

export const ServiceHealth: MessageFns<ServiceHealth> = {
  encode(message: ServiceHealth, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.status !== ServiceStatus.SERVICE_STATUS_UNKNOWN) {
      writer.uint32(16).int32(serviceStatusToNumber(message.status));
    }
    if (message.responseTime !== 0) {
      writer.uint32(24).int32(message.responseTime);
    }
    if (message.lastChecked !== undefined) {
      Timestamp.encode(toTimestamp(message.lastChecked), writer.uint32(34).fork()).join();
    }
    if (message.error !== "") {
      writer.uint32(42).string(message.error);
    }
    if (message.url !== "") {
      writer.uint32(50).string(message.url);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ServiceHealth {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseServiceHealth();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.status = serviceStatusFromJSON(reader.int32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.responseTime = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.lastChecked = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.error = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.url = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseMemoryUsage(): MemoryUsage {
  return { used: 0, total: 0, percentage: 0 };
}

export const MemoryUsage: MessageFns<MemoryUsage> = {
  encode(message: MemoryUsage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.used !== 0) {
      writer.uint32(8).int32(message.used);
    }
    if (message.total !== 0) {
      writer.uint32(16).int32(message.total);
    }
    if (message.percentage !== 0) {
      writer.uint32(24).int32(message.percentage);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MemoryUsage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMemoryUsage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.used = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.total = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.percentage = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseRequestMetadata(): RequestMetadata {
  return { requestId: "", sourceIp: "", userAgent: "" };
}

export const RequestMetadata: MessageFns<RequestMetadata> = {
  encode(message: RequestMetadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.requestId !== "") {
      writer.uint32(10).string(message.requestId);
    }
    if (message.sourceIp !== "") {
      writer.uint32(18).string(message.sourceIp);
    }
    if (message.userAgent !== "") {
      writer.uint32(26).string(message.userAgent);
    }
    if (message.timestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.timestamp), writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RequestMetadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRequestMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.requestId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.sourceIp = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.userAgent = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.timestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseResponseMetadata(): ResponseMetadata {
  return { requestId: "", processingTime: 0 };
}

export const ResponseMetadata: MessageFns<ResponseMetadata> = {
  encode(message: ResponseMetadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.requestId !== "") {
      writer.uint32(10).string(message.requestId);
    }
    if (message.timestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.timestamp), writer.uint32(18).fork()).join();
    }
    if (message.processingTime !== 0) {
      writer.uint32(24).int32(message.processingTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ResponseMetadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseResponseMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.requestId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.timestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.processingTime = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

/** Health check service definition */

export interface HealthServiceClient {
  /** Basic health check */

  check(request: HealthCheckRequest): Observable<HealthCheckResponse>;

  /** Detailed health check with dependency information */

  checkDetailed(request: HealthCheckRequest): Observable<DetailedHealthCheckResponse>;

  /** Readiness check for Kubernetes probes */

  checkReadiness(request: HealthCheckRequest): Observable<ReadinessCheckResponse>;

  /** Liveness check for Kubernetes probes */

  checkLiveness(request: HealthCheckRequest): Observable<LivenessCheckResponse>;
}

/** Health check service definition */

export interface HealthServiceController {
  /** Basic health check */

  check(
    request: HealthCheckRequest,
  ): Promise<HealthCheckResponse> | Observable<HealthCheckResponse> | HealthCheckResponse;

  /** Detailed health check with dependency information */

  checkDetailed(
    request: HealthCheckRequest,
  ): Promise<DetailedHealthCheckResponse> | Observable<DetailedHealthCheckResponse> | DetailedHealthCheckResponse;

  /** Readiness check for Kubernetes probes */

  checkReadiness(
    request: HealthCheckRequest,
  ): Promise<ReadinessCheckResponse> | Observable<ReadinessCheckResponse> | ReadinessCheckResponse;

  /** Liveness check for Kubernetes probes */

  checkLiveness(
    request: HealthCheckRequest,
  ): Promise<LivenessCheckResponse> | Observable<LivenessCheckResponse> | LivenessCheckResponse;
}

export function HealthServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["check", "checkDetailed", "checkReadiness", "checkLiveness"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("HealthService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("HealthService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const HEALTH_SERVICE_NAME = "HealthService";

/** Health check service definition */
export type HealthServiceService = typeof HealthServiceService;
export const HealthServiceService = {
  /** Basic health check */
  check: {
    path: "/common.HealthService/Check",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: HealthCheckRequest): Buffer => Buffer.from(HealthCheckRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): HealthCheckRequest => HealthCheckRequest.decode(value),
    responseSerialize: (value: HealthCheckResponse): Buffer => Buffer.from(HealthCheckResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): HealthCheckResponse => HealthCheckResponse.decode(value),
  },
  /** Detailed health check with dependency information */
  checkDetailed: {
    path: "/common.HealthService/CheckDetailed",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: HealthCheckRequest): Buffer => Buffer.from(HealthCheckRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): HealthCheckRequest => HealthCheckRequest.decode(value),
    responseSerialize: (value: DetailedHealthCheckResponse): Buffer =>
      Buffer.from(DetailedHealthCheckResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): DetailedHealthCheckResponse => DetailedHealthCheckResponse.decode(value),
  },
  /** Readiness check for Kubernetes probes */
  checkReadiness: {
    path: "/common.HealthService/CheckReadiness",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: HealthCheckRequest): Buffer => Buffer.from(HealthCheckRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): HealthCheckRequest => HealthCheckRequest.decode(value),
    responseSerialize: (value: ReadinessCheckResponse): Buffer =>
      Buffer.from(ReadinessCheckResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): ReadinessCheckResponse => ReadinessCheckResponse.decode(value),
  },
  /** Liveness check for Kubernetes probes */
  checkLiveness: {
    path: "/common.HealthService/CheckLiveness",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: HealthCheckRequest): Buffer => Buffer.from(HealthCheckRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): HealthCheckRequest => HealthCheckRequest.decode(value),
    responseSerialize: (value: LivenessCheckResponse): Buffer =>
      Buffer.from(LivenessCheckResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): LivenessCheckResponse => LivenessCheckResponse.decode(value),
  },
} as const;

export interface HealthServiceServer extends UntypedServiceImplementation {
  /** Basic health check */
  check: handleUnaryCall<HealthCheckRequest, HealthCheckResponse>;
  /** Detailed health check with dependency information */
  checkDetailed: handleUnaryCall<HealthCheckRequest, DetailedHealthCheckResponse>;
  /** Readiness check for Kubernetes probes */
  checkReadiness: handleUnaryCall<HealthCheckRequest, ReadinessCheckResponse>;
  /** Liveness check for Kubernetes probes */
  checkLiveness: handleUnaryCall<HealthCheckRequest, LivenessCheckResponse>;
}

function toTimestamp(date: Date): Timestamp {
  const seconds = Math.trunc(date.getTime() / 1_000).toString();
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (globalThis.Number(t.seconds) || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
}
