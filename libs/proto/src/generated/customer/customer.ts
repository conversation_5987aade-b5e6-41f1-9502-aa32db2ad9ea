// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: customer/customer.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import type { handleUnaryCall, UntypedServiceImplementation } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { wrappers } from "protobufjs";
import { Observable } from "rxjs";
import { Timestamp } from "../google/protobuf/timestamp";

/** Enums */
export enum CustomerType {
  CUSTOMER_TYPE_UNKNOWN = "CUSTOMER_TYPE_UNKNOWN",
  CUSTOMER_TYPE_INDIVIDUAL = "CUSTOMER_TYPE_INDIVIDUAL",
  CUSTOMER_TYPE_BUSINESS = "CUSTOMER_TYPE_BUSINESS",
  CUSTOMER_TYPE_TRUST = "CUSTOMER_TYPE_TRUST",
  CUSTOMER_TYPE_GOVERNMENT = "CUSTOMER_TYPE_GOVERNMENT",
  CUSTOMER_TYPE_NON_PROFIT = "CUSTOMER_TYPE_NON_PROFIT",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function customerTypeFromJSON(object: any): CustomerType {
  switch (object) {
    case 0:
    case "CUSTOMER_TYPE_UNKNOWN":
      return CustomerType.CUSTOMER_TYPE_UNKNOWN;
    case 1:
    case "CUSTOMER_TYPE_INDIVIDUAL":
      return CustomerType.CUSTOMER_TYPE_INDIVIDUAL;
    case 2:
    case "CUSTOMER_TYPE_BUSINESS":
      return CustomerType.CUSTOMER_TYPE_BUSINESS;
    case 3:
    case "CUSTOMER_TYPE_TRUST":
      return CustomerType.CUSTOMER_TYPE_TRUST;
    case 4:
    case "CUSTOMER_TYPE_GOVERNMENT":
      return CustomerType.CUSTOMER_TYPE_GOVERNMENT;
    case 5:
    case "CUSTOMER_TYPE_NON_PROFIT":
      return CustomerType.CUSTOMER_TYPE_NON_PROFIT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CustomerType.UNRECOGNIZED;
  }
}

export function customerTypeToNumber(object: CustomerType): number {
  switch (object) {
    case CustomerType.CUSTOMER_TYPE_UNKNOWN:
      return 0;
    case CustomerType.CUSTOMER_TYPE_INDIVIDUAL:
      return 1;
    case CustomerType.CUSTOMER_TYPE_BUSINESS:
      return 2;
    case CustomerType.CUSTOMER_TYPE_TRUST:
      return 3;
    case CustomerType.CUSTOMER_TYPE_GOVERNMENT:
      return 4;
    case CustomerType.CUSTOMER_TYPE_NON_PROFIT:
      return 5;
    case CustomerType.UNRECOGNIZED:
    default:
      return -1;
  }
}

export enum CustomerStatus {
  CUSTOMER_STATUS_UNKNOWN = "CUSTOMER_STATUS_UNKNOWN",
  CUSTOMER_STATUS_ACTIVE = "CUSTOMER_STATUS_ACTIVE",
  CUSTOMER_STATUS_INACTIVE = "CUSTOMER_STATUS_INACTIVE",
  CUSTOMER_STATUS_SUSPENDED = "CUSTOMER_STATUS_SUSPENDED",
  CUSTOMER_STATUS_CLOSED = "CUSTOMER_STATUS_CLOSED",
  CUSTOMER_STATUS_PENDING_VERIFICATION = "CUSTOMER_STATUS_PENDING_VERIFICATION",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function customerStatusFromJSON(object: any): CustomerStatus {
  switch (object) {
    case 0:
    case "CUSTOMER_STATUS_UNKNOWN":
      return CustomerStatus.CUSTOMER_STATUS_UNKNOWN;
    case 1:
    case "CUSTOMER_STATUS_ACTIVE":
      return CustomerStatus.CUSTOMER_STATUS_ACTIVE;
    case 2:
    case "CUSTOMER_STATUS_INACTIVE":
      return CustomerStatus.CUSTOMER_STATUS_INACTIVE;
    case 3:
    case "CUSTOMER_STATUS_SUSPENDED":
      return CustomerStatus.CUSTOMER_STATUS_SUSPENDED;
    case 4:
    case "CUSTOMER_STATUS_CLOSED":
      return CustomerStatus.CUSTOMER_STATUS_CLOSED;
    case 5:
    case "CUSTOMER_STATUS_PENDING_VERIFICATION":
      return CustomerStatus.CUSTOMER_STATUS_PENDING_VERIFICATION;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CustomerStatus.UNRECOGNIZED;
  }
}

export function customerStatusToNumber(object: CustomerStatus): number {
  switch (object) {
    case CustomerStatus.CUSTOMER_STATUS_UNKNOWN:
      return 0;
    case CustomerStatus.CUSTOMER_STATUS_ACTIVE:
      return 1;
    case CustomerStatus.CUSTOMER_STATUS_INACTIVE:
      return 2;
    case CustomerStatus.CUSTOMER_STATUS_SUSPENDED:
      return 3;
    case CustomerStatus.CUSTOMER_STATUS_CLOSED:
      return 4;
    case CustomerStatus.CUSTOMER_STATUS_PENDING_VERIFICATION:
      return 5;
    case CustomerStatus.UNRECOGNIZED:
    default:
      return -1;
  }
}

export enum RiskLevel {
  RISK_LEVEL_UNKNOWN = "RISK_LEVEL_UNKNOWN",
  RISK_LEVEL_LOW = "RISK_LEVEL_LOW",
  RISK_LEVEL_MEDIUM = "RISK_LEVEL_MEDIUM",
  RISK_LEVEL_HIGH = "RISK_LEVEL_HIGH",
  RISK_LEVEL_CRITICAL = "RISK_LEVEL_CRITICAL",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function riskLevelFromJSON(object: any): RiskLevel {
  switch (object) {
    case 0:
    case "RISK_LEVEL_UNKNOWN":
      return RiskLevel.RISK_LEVEL_UNKNOWN;
    case 1:
    case "RISK_LEVEL_LOW":
      return RiskLevel.RISK_LEVEL_LOW;
    case 2:
    case "RISK_LEVEL_MEDIUM":
      return RiskLevel.RISK_LEVEL_MEDIUM;
    case 3:
    case "RISK_LEVEL_HIGH":
      return RiskLevel.RISK_LEVEL_HIGH;
    case 4:
    case "RISK_LEVEL_CRITICAL":
      return RiskLevel.RISK_LEVEL_CRITICAL;
    case -1:
    case "UNRECOGNIZED":
    default:
      return RiskLevel.UNRECOGNIZED;
  }
}

export function riskLevelToNumber(object: RiskLevel): number {
  switch (object) {
    case RiskLevel.RISK_LEVEL_UNKNOWN:
      return 0;
    case RiskLevel.RISK_LEVEL_LOW:
      return 1;
    case RiskLevel.RISK_LEVEL_MEDIUM:
      return 2;
    case RiskLevel.RISK_LEVEL_HIGH:
      return 3;
    case RiskLevel.RISK_LEVEL_CRITICAL:
      return 4;
    case RiskLevel.UNRECOGNIZED:
    default:
      return -1;
  }
}

export enum KycStatus {
  KYC_STATUS_UNKNOWN = "KYC_STATUS_UNKNOWN",
  KYC_STATUS_PENDING = "KYC_STATUS_PENDING",
  KYC_STATUS_IN_PROGRESS = "KYC_STATUS_IN_PROGRESS",
  KYC_STATUS_VERIFIED = "KYC_STATUS_VERIFIED",
  KYC_STATUS_REJECTED = "KYC_STATUS_REJECTED",
  KYC_STATUS_EXPIRED = "KYC_STATUS_EXPIRED",
  KYC_STATUS_REQUIRES_UPDATE = "KYC_STATUS_REQUIRES_UPDATE",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function kycStatusFromJSON(object: any): KycStatus {
  switch (object) {
    case 0:
    case "KYC_STATUS_UNKNOWN":
      return KycStatus.KYC_STATUS_UNKNOWN;
    case 1:
    case "KYC_STATUS_PENDING":
      return KycStatus.KYC_STATUS_PENDING;
    case 2:
    case "KYC_STATUS_IN_PROGRESS":
      return KycStatus.KYC_STATUS_IN_PROGRESS;
    case 3:
    case "KYC_STATUS_VERIFIED":
      return KycStatus.KYC_STATUS_VERIFIED;
    case 4:
    case "KYC_STATUS_REJECTED":
      return KycStatus.KYC_STATUS_REJECTED;
    case 5:
    case "KYC_STATUS_EXPIRED":
      return KycStatus.KYC_STATUS_EXPIRED;
    case 6:
    case "KYC_STATUS_REQUIRES_UPDATE":
      return KycStatus.KYC_STATUS_REQUIRES_UPDATE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return KycStatus.UNRECOGNIZED;
  }
}

export function kycStatusToNumber(object: KycStatus): number {
  switch (object) {
    case KycStatus.KYC_STATUS_UNKNOWN:
      return 0;
    case KycStatus.KYC_STATUS_PENDING:
      return 1;
    case KycStatus.KYC_STATUS_IN_PROGRESS:
      return 2;
    case KycStatus.KYC_STATUS_VERIFIED:
      return 3;
    case KycStatus.KYC_STATUS_REJECTED:
      return 4;
    case KycStatus.KYC_STATUS_EXPIRED:
      return 5;
    case KycStatus.KYC_STATUS_REQUIRES_UPDATE:
      return 6;
    case KycStatus.UNRECOGNIZED:
    default:
      return -1;
  }
}

export enum SortOrder {
  SORT_ORDER_UNKNOWN = "SORT_ORDER_UNKNOWN",
  SORT_ORDER_ASC = "SORT_ORDER_ASC",
  SORT_ORDER_DESC = "SORT_ORDER_DESC",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function sortOrderFromJSON(object: any): SortOrder {
  switch (object) {
    case 0:
    case "SORT_ORDER_UNKNOWN":
      return SortOrder.SORT_ORDER_UNKNOWN;
    case 1:
    case "SORT_ORDER_ASC":
      return SortOrder.SORT_ORDER_ASC;
    case 2:
    case "SORT_ORDER_DESC":
      return SortOrder.SORT_ORDER_DESC;
    case -1:
    case "UNRECOGNIZED":
    default:
      return SortOrder.UNRECOGNIZED;
  }
}

export function sortOrderToNumber(object: SortOrder): number {
  switch (object) {
    case SortOrder.SORT_ORDER_UNKNOWN:
      return 0;
    case SortOrder.SORT_ORDER_ASC:
      return 1;
    case SortOrder.SORT_ORDER_DESC:
      return 2;
    case SortOrder.UNRECOGNIZED:
    default:
      return -1;
  }
}

export enum GenderType {
  GENDER_TYPE_UNKNOWN = "GENDER_TYPE_UNKNOWN",
  GENDER_TYPE_MALE = "GENDER_TYPE_MALE",
  GENDER_TYPE_FEMALE = "GENDER_TYPE_FEMALE",
  GENDER_TYPE_OTHER = "GENDER_TYPE_OTHER",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function genderTypeFromJSON(object: any): GenderType {
  switch (object) {
    case 0:
    case "GENDER_TYPE_UNKNOWN":
      return GenderType.GENDER_TYPE_UNKNOWN;
    case 1:
    case "GENDER_TYPE_MALE":
      return GenderType.GENDER_TYPE_MALE;
    case 2:
    case "GENDER_TYPE_FEMALE":
      return GenderType.GENDER_TYPE_FEMALE;
    case 3:
    case "GENDER_TYPE_OTHER":
      return GenderType.GENDER_TYPE_OTHER;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GenderType.UNRECOGNIZED;
  }
}

export function genderTypeToNumber(object: GenderType): number {
  switch (object) {
    case GenderType.GENDER_TYPE_UNKNOWN:
      return 0;
    case GenderType.GENDER_TYPE_MALE:
      return 1;
    case GenderType.GENDER_TYPE_FEMALE:
      return 2;
    case GenderType.GENDER_TYPE_OTHER:
      return 3;
    case GenderType.UNRECOGNIZED:
    default:
      return -1;
  }
}

export enum AddressType {
  ADDRESS_TYPE_UNKNOWN = "ADDRESS_TYPE_UNKNOWN",
  ADDRESS_TYPE_RESIDENTIAL = "ADDRESS_TYPE_RESIDENTIAL",
  ADDRESS_TYPE_BUSINESS = "ADDRESS_TYPE_BUSINESS",
  ADDRESS_TYPE_MAILING = "ADDRESS_TYPE_MAILING",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function addressTypeFromJSON(object: any): AddressType {
  switch (object) {
    case 0:
    case "ADDRESS_TYPE_UNKNOWN":
      return AddressType.ADDRESS_TYPE_UNKNOWN;
    case 1:
    case "ADDRESS_TYPE_RESIDENTIAL":
      return AddressType.ADDRESS_TYPE_RESIDENTIAL;
    case 2:
    case "ADDRESS_TYPE_BUSINESS":
      return AddressType.ADDRESS_TYPE_BUSINESS;
    case 3:
    case "ADDRESS_TYPE_MAILING":
      return AddressType.ADDRESS_TYPE_MAILING;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AddressType.UNRECOGNIZED;
  }
}

export function addressTypeToNumber(object: AddressType): number {
  switch (object) {
    case AddressType.ADDRESS_TYPE_UNKNOWN:
      return 0;
    case AddressType.ADDRESS_TYPE_RESIDENTIAL:
      return 1;
    case AddressType.ADDRESS_TYPE_BUSINESS:
      return 2;
    case AddressType.ADDRESS_TYPE_MAILING:
      return 3;
    case AddressType.UNRECOGNIZED:
    default:
      return -1;
  }
}

/** Common messages */
export interface RequestMetadata {
  requestId: string;
  sourceIp: string;
  userAgent: string;
  timestamp?: Date | undefined;
}

export interface ResponseMetadata {
  requestId: string;
  timestamp?: Date | undefined;
  processingTime: number;
  version: string;
}

export interface ErrorDetails {
  code: string;
  message: string;
  details: string;
}

/** Address message */
export interface CustomerAddress {
  street1: string;
  street2: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  addressType: AddressType;
}

/** Profile data message */
export interface CustomerProfileData {
  /** Individual customer fields */
  firstName: string;
  lastName: string;
  middleName: string;
  dateOfBirth: string;
  gender: GenderType;
  nationality: string;
  countryOfBirth: string;
  placeOfBirth: string;
  /** Business customer fields */
  businessName: string;
  businessType: string;
  businessRegistrationNumber: string;
  businessRegistrationDate: string;
  businessCountry: string;
  businessIndustry: string;
  businessDescription: string;
  /** Contact information */
  email: string;
  phoneNumber: string;
  alternatePhoneNumber: string;
  /** Address information */
  address?: CustomerAddress | undefined;
  mailingAddress?:
    | CustomerAddress
    | undefined;
  /** Employment/Business information */
  occupation: string;
  employer: string;
  employmentStatus: string;
  annualIncome: number;
  sourceOfFunds: string[];
  sourceOfWealth: string[];
  /** Risk indicators */
  politicallyExposed: boolean;
  sanctionsMatch: boolean;
  adverseMediaMatch: boolean;
  highRiskCountryExposure: boolean;
  /** Custom fields (JSON string) */
  customFields: string;
}

/** Customer message */
export interface Customer {
  id: string;
  tenantId: string;
  customerType: CustomerType;
  status: CustomerStatus;
  riskLevel: RiskLevel;
  kycStatus: KycStatus;
  profileData?:
    | CustomerProfileData
    | undefined;
  /** JSON string */
  kycData: string;
  /** JSON string */
  riskAssessment: string;
  createdAt?: Date | undefined;
  updatedAt?: Date | undefined;
}

/** Pagination message */
export interface PaginationMeta {
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
  page: number;
  totalPages: number;
}

/** Request messages */
export interface GetAllCustomersRequest {
  page: number;
  limit: number;
  search: string;
  status: CustomerStatus;
  customerType: CustomerType;
  riskLevel: RiskLevel;
  kycStatus: KycStatus;
  tenantId: string;
  sortBy: string;
  sortOrder: SortOrder;
  metadata?: RequestMetadata | undefined;
}

export interface GetCustomerByIdRequest {
  id: string;
  metadata?: RequestMetadata | undefined;
}

export interface CreateCustomerRequest {
  customerType: CustomerType;
  profileData?: CustomerProfileData | undefined;
  status: CustomerStatus;
  riskLevel: RiskLevel;
  kycStatus: KycStatus;
  /** JSON string */
  kycData: string;
  /** JSON string */
  riskAssessment: string;
  metadata?: RequestMetadata | undefined;
}

export interface UpdateCustomerRequest {
  id: string;
  customerType: CustomerType;
  profileData?: CustomerProfileData | undefined;
  status: CustomerStatus;
  riskLevel: RiskLevel;
  kycStatus: KycStatus;
  /** JSON string */
  kycData: string;
  /** JSON string */
  riskAssessment: string;
  metadata?: RequestMetadata | undefined;
}

export interface DeleteCustomerRequest {
  id: string;
  reason: string;
  metadata?: RequestMetadata | undefined;
}

export interface GetCustomerStatsRequest {
  tenantId: string;
  dateFrom: string;
  dateTo: string;
  customerType: CustomerType;
  riskLevel: RiskLevel;
  metadata?: RequestMetadata | undefined;
}

export interface SearchCustomersRequest {
  searchTerm: string;
  tenantId: string;
  limit: number;
  customerType: CustomerType;
  status: CustomerStatus;
  riskLevel: RiskLevel;
  metadata?: RequestMetadata | undefined;
}

export interface UpdateCustomerStatusRequest {
  id: string;
  status: CustomerStatus;
  reason: string;
  metadata?: RequestMetadata | undefined;
}

export interface UpdateCustomerRiskLevelRequest {
  id: string;
  riskLevel: RiskLevel;
  reason: string;
  /** JSON string */
  assessmentData: string;
  metadata?: RequestMetadata | undefined;
}

export interface UpdateCustomerKycStatusRequest {
  id: string;
  kycStatus: KycStatus;
  reason: string;
  /** JSON string */
  kycData: string;
  metadata?: RequestMetadata | undefined;
}

/** Response messages */
export interface GetAllCustomersResponse {
  success: boolean;
  customers: Customer[];
  pagination?: PaginationMeta | undefined;
  message: string;
  error?: ErrorDetails | undefined;
  metadata?: ResponseMetadata | undefined;
}

export interface GetCustomerByIdResponse {
  success: boolean;
  customer?: Customer | undefined;
  message: string;
  error?: ErrorDetails | undefined;
  metadata?: ResponseMetadata | undefined;
}

export interface CreateCustomerResponse {
  success: boolean;
  customer?: Customer | undefined;
  message: string;
  error?: ErrorDetails | undefined;
  metadata?: ResponseMetadata | undefined;
}

export interface UpdateCustomerResponse {
  success: boolean;
  customer?: Customer | undefined;
  message: string;
  error?: ErrorDetails | undefined;
  metadata?: ResponseMetadata | undefined;
}

export interface DeleteCustomerResponse {
  success: boolean;
  message: string;
  error?: ErrorDetails | undefined;
  metadata?: ResponseMetadata | undefined;
}

/** Customer statistics message */
export interface CustomerStatistics {
  totalCustomers: number;
  activeCustomers: number;
  pendingKyc: number;
  highRiskCustomers: number;
  recentCustomers: number;
  customersByType: { [key: string]: number };
  customersByRiskLevel: { [key: string]: number };
}

export interface CustomerStatistics_CustomersByTypeEntry {
  key: string;
  value: number;
}

export interface CustomerStatistics_CustomersByRiskLevelEntry {
  key: string;
  value: number;
}

export interface GetCustomerStatsResponse {
  success: boolean;
  statistics?: CustomerStatistics | undefined;
  message: string;
  error?: ErrorDetails | undefined;
  metadata?: ResponseMetadata | undefined;
}

export interface SearchCustomersResponse {
  success: boolean;
  customers: Customer[];
  totalResults: number;
  searchTerm: string;
  searchTime: number;
  message: string;
  error?: ErrorDetails | undefined;
  metadata?: ResponseMetadata | undefined;
}

export interface UpdateCustomerStatusResponse {
  success: boolean;
  customer?: Customer | undefined;
  message: string;
  error?: ErrorDetails | undefined;
  metadata?: ResponseMetadata | undefined;
}

export interface UpdateCustomerRiskLevelResponse {
  success: boolean;
  customer?: Customer | undefined;
  message: string;
  error?: ErrorDetails | undefined;
  metadata?: ResponseMetadata | undefined;
}

export interface UpdateCustomerKycStatusResponse {
  success: boolean;
  customer?: Customer | undefined;
  message: string;
  error?: ErrorDetails | undefined;
  metadata?: ResponseMetadata | undefined;
}

wrappers[".google.protobuf.Timestamp"] = {
  fromObject(value: Date) {
    return { seconds: value.getTime() / 1000, nanos: (value.getTime() % 1000) * 1e6 };
  },
  toObject(message: { seconds: number; nanos: number }) {
    return new Date(message.seconds * 1000 + message.nanos / 1e6);
  },
} as any;

function createBaseRequestMetadata(): RequestMetadata {
  return { requestId: "", sourceIp: "", userAgent: "" };
}

export const RequestMetadata: MessageFns<RequestMetadata> = {
  encode(message: RequestMetadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.requestId !== "") {
      writer.uint32(10).string(message.requestId);
    }
    if (message.sourceIp !== "") {
      writer.uint32(18).string(message.sourceIp);
    }
    if (message.userAgent !== "") {
      writer.uint32(26).string(message.userAgent);
    }
    if (message.timestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.timestamp), writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RequestMetadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRequestMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.requestId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.sourceIp = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.userAgent = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.timestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseResponseMetadata(): ResponseMetadata {
  return { requestId: "", processingTime: 0, version: "" };
}

export const ResponseMetadata: MessageFns<ResponseMetadata> = {
  encode(message: ResponseMetadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.requestId !== "") {
      writer.uint32(10).string(message.requestId);
    }
    if (message.timestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.timestamp), writer.uint32(18).fork()).join();
    }
    if (message.processingTime !== 0) {
      writer.uint32(24).int32(message.processingTime);
    }
    if (message.version !== "") {
      writer.uint32(34).string(message.version);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ResponseMetadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseResponseMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.requestId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.timestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.processingTime = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.version = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseErrorDetails(): ErrorDetails {
  return { code: "", message: "", details: "" };
}

export const ErrorDetails: MessageFns<ErrorDetails> = {
  encode(message: ErrorDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.code !== "") {
      writer.uint32(10).string(message.code);
    }
    if (message.message !== "") {
      writer.uint32(18).string(message.message);
    }
    if (message.details !== "") {
      writer.uint32(26).string(message.details);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ErrorDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseErrorDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.code = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.details = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseCustomerAddress(): CustomerAddress {
  return {
    street1: "",
    street2: "",
    city: "",
    state: "",
    postalCode: "",
    country: "",
    addressType: AddressType.ADDRESS_TYPE_UNKNOWN,
  };
}

export const CustomerAddress: MessageFns<CustomerAddress> = {
  encode(message: CustomerAddress, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.street1 !== "") {
      writer.uint32(10).string(message.street1);
    }
    if (message.street2 !== "") {
      writer.uint32(18).string(message.street2);
    }
    if (message.city !== "") {
      writer.uint32(26).string(message.city);
    }
    if (message.state !== "") {
      writer.uint32(34).string(message.state);
    }
    if (message.postalCode !== "") {
      writer.uint32(42).string(message.postalCode);
    }
    if (message.country !== "") {
      writer.uint32(50).string(message.country);
    }
    if (message.addressType !== AddressType.ADDRESS_TYPE_UNKNOWN) {
      writer.uint32(56).int32(addressTypeToNumber(message.addressType));
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CustomerAddress {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCustomerAddress();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.street1 = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.street2 = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.city = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.state = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.postalCode = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.country = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.addressType = addressTypeFromJSON(reader.int32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseCustomerProfileData(): CustomerProfileData {
  return {
    firstName: "",
    lastName: "",
    middleName: "",
    dateOfBirth: "",
    gender: GenderType.GENDER_TYPE_UNKNOWN,
    nationality: "",
    countryOfBirth: "",
    placeOfBirth: "",
    businessName: "",
    businessType: "",
    businessRegistrationNumber: "",
    businessRegistrationDate: "",
    businessCountry: "",
    businessIndustry: "",
    businessDescription: "",
    email: "",
    phoneNumber: "",
    alternatePhoneNumber: "",
    occupation: "",
    employer: "",
    employmentStatus: "",
    annualIncome: 0,
    sourceOfFunds: [],
    sourceOfWealth: [],
    politicallyExposed: false,
    sanctionsMatch: false,
    adverseMediaMatch: false,
    highRiskCountryExposure: false,
    customFields: "",
  };
}

export const CustomerProfileData: MessageFns<CustomerProfileData> = {
  encode(message: CustomerProfileData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.firstName !== "") {
      writer.uint32(10).string(message.firstName);
    }
    if (message.lastName !== "") {
      writer.uint32(18).string(message.lastName);
    }
    if (message.middleName !== "") {
      writer.uint32(26).string(message.middleName);
    }
    if (message.dateOfBirth !== "") {
      writer.uint32(34).string(message.dateOfBirth);
    }
    if (message.gender !== GenderType.GENDER_TYPE_UNKNOWN) {
      writer.uint32(40).int32(genderTypeToNumber(message.gender));
    }
    if (message.nationality !== "") {
      writer.uint32(50).string(message.nationality);
    }
    if (message.countryOfBirth !== "") {
      writer.uint32(58).string(message.countryOfBirth);
    }
    if (message.placeOfBirth !== "") {
      writer.uint32(66).string(message.placeOfBirth);
    }
    if (message.businessName !== "") {
      writer.uint32(74).string(message.businessName);
    }
    if (message.businessType !== "") {
      writer.uint32(82).string(message.businessType);
    }
    if (message.businessRegistrationNumber !== "") {
      writer.uint32(90).string(message.businessRegistrationNumber);
    }
    if (message.businessRegistrationDate !== "") {
      writer.uint32(98).string(message.businessRegistrationDate);
    }
    if (message.businessCountry !== "") {
      writer.uint32(106).string(message.businessCountry);
    }
    if (message.businessIndustry !== "") {
      writer.uint32(114).string(message.businessIndustry);
    }
    if (message.businessDescription !== "") {
      writer.uint32(122).string(message.businessDescription);
    }
    if (message.email !== "") {
      writer.uint32(130).string(message.email);
    }
    if (message.phoneNumber !== "") {
      writer.uint32(138).string(message.phoneNumber);
    }
    if (message.alternatePhoneNumber !== "") {
      writer.uint32(146).string(message.alternatePhoneNumber);
    }
    if (message.address !== undefined) {
      CustomerAddress.encode(message.address, writer.uint32(154).fork()).join();
    }
    if (message.mailingAddress !== undefined) {
      CustomerAddress.encode(message.mailingAddress, writer.uint32(162).fork()).join();
    }
    if (message.occupation !== "") {
      writer.uint32(170).string(message.occupation);
    }
    if (message.employer !== "") {
      writer.uint32(178).string(message.employer);
    }
    if (message.employmentStatus !== "") {
      writer.uint32(186).string(message.employmentStatus);
    }
    if (message.annualIncome !== 0) {
      writer.uint32(193).double(message.annualIncome);
    }
    for (const v of message.sourceOfFunds) {
      writer.uint32(202).string(v!);
    }
    for (const v of message.sourceOfWealth) {
      writer.uint32(210).string(v!);
    }
    if (message.politicallyExposed !== false) {
      writer.uint32(216).bool(message.politicallyExposed);
    }
    if (message.sanctionsMatch !== false) {
      writer.uint32(224).bool(message.sanctionsMatch);
    }
    if (message.adverseMediaMatch !== false) {
      writer.uint32(232).bool(message.adverseMediaMatch);
    }
    if (message.highRiskCountryExposure !== false) {
      writer.uint32(240).bool(message.highRiskCountryExposure);
    }
    if (message.customFields !== "") {
      writer.uint32(250).string(message.customFields);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CustomerProfileData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCustomerProfileData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.firstName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.lastName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.middleName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.dateOfBirth = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.gender = genderTypeFromJSON(reader.int32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.nationality = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.countryOfBirth = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.placeOfBirth = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.businessName = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.businessType = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.businessRegistrationNumber = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.businessRegistrationDate = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.businessCountry = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.businessIndustry = reader.string();
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.businessDescription = reader.string();
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.email = reader.string();
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          message.phoneNumber = reader.string();
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.alternatePhoneNumber = reader.string();
          continue;
        }
        case 19: {
          if (tag !== 154) {
            break;
          }

          message.address = CustomerAddress.decode(reader, reader.uint32());
          continue;
        }
        case 20: {
          if (tag !== 162) {
            break;
          }

          message.mailingAddress = CustomerAddress.decode(reader, reader.uint32());
          continue;
        }
        case 21: {
          if (tag !== 170) {
            break;
          }

          message.occupation = reader.string();
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.employer = reader.string();
          continue;
        }
        case 23: {
          if (tag !== 186) {
            break;
          }

          message.employmentStatus = reader.string();
          continue;
        }
        case 24: {
          if (tag !== 193) {
            break;
          }

          message.annualIncome = reader.double();
          continue;
        }
        case 25: {
          if (tag !== 202) {
            break;
          }

          message.sourceOfFunds.push(reader.string());
          continue;
        }
        case 26: {
          if (tag !== 210) {
            break;
          }

          message.sourceOfWealth.push(reader.string());
          continue;
        }
        case 27: {
          if (tag !== 216) {
            break;
          }

          message.politicallyExposed = reader.bool();
          continue;
        }
        case 28: {
          if (tag !== 224) {
            break;
          }

          message.sanctionsMatch = reader.bool();
          continue;
        }
        case 29: {
          if (tag !== 232) {
            break;
          }

          message.adverseMediaMatch = reader.bool();
          continue;
        }
        case 30: {
          if (tag !== 240) {
            break;
          }

          message.highRiskCountryExposure = reader.bool();
          continue;
        }
        case 31: {
          if (tag !== 250) {
            break;
          }

          message.customFields = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseCustomer(): Customer {
  return {
    id: "",
    tenantId: "",
    customerType: CustomerType.CUSTOMER_TYPE_UNKNOWN,
    status: CustomerStatus.CUSTOMER_STATUS_UNKNOWN,
    riskLevel: RiskLevel.RISK_LEVEL_UNKNOWN,
    kycStatus: KycStatus.KYC_STATUS_UNKNOWN,
    kycData: "",
    riskAssessment: "",
  };
}

export const Customer: MessageFns<Customer> = {
  encode(message: Customer, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.tenantId !== "") {
      writer.uint32(18).string(message.tenantId);
    }
    if (message.customerType !== CustomerType.CUSTOMER_TYPE_UNKNOWN) {
      writer.uint32(24).int32(customerTypeToNumber(message.customerType));
    }
    if (message.status !== CustomerStatus.CUSTOMER_STATUS_UNKNOWN) {
      writer.uint32(32).int32(customerStatusToNumber(message.status));
    }
    if (message.riskLevel !== RiskLevel.RISK_LEVEL_UNKNOWN) {
      writer.uint32(40).int32(riskLevelToNumber(message.riskLevel));
    }
    if (message.kycStatus !== KycStatus.KYC_STATUS_UNKNOWN) {
      writer.uint32(48).int32(kycStatusToNumber(message.kycStatus));
    }
    if (message.profileData !== undefined) {
      CustomerProfileData.encode(message.profileData, writer.uint32(58).fork()).join();
    }
    if (message.kycData !== "") {
      writer.uint32(66).string(message.kycData);
    }
    if (message.riskAssessment !== "") {
      writer.uint32(74).string(message.riskAssessment);
    }
    if (message.createdAt !== undefined) {
      Timestamp.encode(toTimestamp(message.createdAt), writer.uint32(82).fork()).join();
    }
    if (message.updatedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.updatedAt), writer.uint32(90).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Customer {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCustomer();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tenantId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.customerType = customerTypeFromJSON(reader.int32());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.status = customerStatusFromJSON(reader.int32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.riskLevel = riskLevelFromJSON(reader.int32());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.kycStatus = kycStatusFromJSON(reader.int32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.profileData = CustomerProfileData.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.kycData = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.riskAssessment = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.createdAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.updatedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBasePaginationMeta(): PaginationMeta {
  return { total: 0, limit: 0, offset: 0, hasMore: false, page: 0, totalPages: 0 };
}

export const PaginationMeta: MessageFns<PaginationMeta> = {
  encode(message: PaginationMeta, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.total !== 0) {
      writer.uint32(8).int32(message.total);
    }
    if (message.limit !== 0) {
      writer.uint32(16).int32(message.limit);
    }
    if (message.offset !== 0) {
      writer.uint32(24).int32(message.offset);
    }
    if (message.hasMore !== false) {
      writer.uint32(32).bool(message.hasMore);
    }
    if (message.page !== 0) {
      writer.uint32(40).int32(message.page);
    }
    if (message.totalPages !== 0) {
      writer.uint32(48).int32(message.totalPages);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PaginationMeta {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePaginationMeta();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.total = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.limit = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.offset = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.hasMore = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.page = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.totalPages = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetAllCustomersRequest(): GetAllCustomersRequest {
  return {
    page: 0,
    limit: 0,
    search: "",
    status: CustomerStatus.CUSTOMER_STATUS_UNKNOWN,
    customerType: CustomerType.CUSTOMER_TYPE_UNKNOWN,
    riskLevel: RiskLevel.RISK_LEVEL_UNKNOWN,
    kycStatus: KycStatus.KYC_STATUS_UNKNOWN,
    tenantId: "",
    sortBy: "",
    sortOrder: SortOrder.SORT_ORDER_UNKNOWN,
  };
}

export const GetAllCustomersRequest: MessageFns<GetAllCustomersRequest> = {
  encode(message: GetAllCustomersRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.page !== 0) {
      writer.uint32(8).int32(message.page);
    }
    if (message.limit !== 0) {
      writer.uint32(16).int32(message.limit);
    }
    if (message.search !== "") {
      writer.uint32(26).string(message.search);
    }
    if (message.status !== CustomerStatus.CUSTOMER_STATUS_UNKNOWN) {
      writer.uint32(32).int32(customerStatusToNumber(message.status));
    }
    if (message.customerType !== CustomerType.CUSTOMER_TYPE_UNKNOWN) {
      writer.uint32(40).int32(customerTypeToNumber(message.customerType));
    }
    if (message.riskLevel !== RiskLevel.RISK_LEVEL_UNKNOWN) {
      writer.uint32(48).int32(riskLevelToNumber(message.riskLevel));
    }
    if (message.kycStatus !== KycStatus.KYC_STATUS_UNKNOWN) {
      writer.uint32(56).int32(kycStatusToNumber(message.kycStatus));
    }
    if (message.tenantId !== "") {
      writer.uint32(66).string(message.tenantId);
    }
    if (message.sortBy !== "") {
      writer.uint32(74).string(message.sortBy);
    }
    if (message.sortOrder !== SortOrder.SORT_ORDER_UNKNOWN) {
      writer.uint32(80).int32(sortOrderToNumber(message.sortOrder));
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(90).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetAllCustomersRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetAllCustomersRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.page = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.limit = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.search = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.status = customerStatusFromJSON(reader.int32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.customerType = customerTypeFromJSON(reader.int32());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.riskLevel = riskLevelFromJSON(reader.int32());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.kycStatus = kycStatusFromJSON(reader.int32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.tenantId = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.sortBy = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.sortOrder = sortOrderFromJSON(reader.int32());
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetCustomerByIdRequest(): GetCustomerByIdRequest {
  return { id: "" };
}

export const GetCustomerByIdRequest: MessageFns<GetCustomerByIdRequest> = {
  encode(message: GetCustomerByIdRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetCustomerByIdRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetCustomerByIdRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseCreateCustomerRequest(): CreateCustomerRequest {
  return {
    customerType: CustomerType.CUSTOMER_TYPE_UNKNOWN,
    status: CustomerStatus.CUSTOMER_STATUS_UNKNOWN,
    riskLevel: RiskLevel.RISK_LEVEL_UNKNOWN,
    kycStatus: KycStatus.KYC_STATUS_UNKNOWN,
    kycData: "",
    riskAssessment: "",
  };
}

export const CreateCustomerRequest: MessageFns<CreateCustomerRequest> = {
  encode(message: CreateCustomerRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.customerType !== CustomerType.CUSTOMER_TYPE_UNKNOWN) {
      writer.uint32(8).int32(customerTypeToNumber(message.customerType));
    }
    if (message.profileData !== undefined) {
      CustomerProfileData.encode(message.profileData, writer.uint32(18).fork()).join();
    }
    if (message.status !== CustomerStatus.CUSTOMER_STATUS_UNKNOWN) {
      writer.uint32(24).int32(customerStatusToNumber(message.status));
    }
    if (message.riskLevel !== RiskLevel.RISK_LEVEL_UNKNOWN) {
      writer.uint32(32).int32(riskLevelToNumber(message.riskLevel));
    }
    if (message.kycStatus !== KycStatus.KYC_STATUS_UNKNOWN) {
      writer.uint32(40).int32(kycStatusToNumber(message.kycStatus));
    }
    if (message.kycData !== "") {
      writer.uint32(50).string(message.kycData);
    }
    if (message.riskAssessment !== "") {
      writer.uint32(58).string(message.riskAssessment);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(66).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateCustomerRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateCustomerRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.customerType = customerTypeFromJSON(reader.int32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.profileData = CustomerProfileData.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.status = customerStatusFromJSON(reader.int32());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.riskLevel = riskLevelFromJSON(reader.int32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.kycStatus = kycStatusFromJSON(reader.int32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.kycData = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.riskAssessment = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUpdateCustomerRequest(): UpdateCustomerRequest {
  return {
    id: "",
    customerType: CustomerType.CUSTOMER_TYPE_UNKNOWN,
    status: CustomerStatus.CUSTOMER_STATUS_UNKNOWN,
    riskLevel: RiskLevel.RISK_LEVEL_UNKNOWN,
    kycStatus: KycStatus.KYC_STATUS_UNKNOWN,
    kycData: "",
    riskAssessment: "",
  };
}

export const UpdateCustomerRequest: MessageFns<UpdateCustomerRequest> = {
  encode(message: UpdateCustomerRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.customerType !== CustomerType.CUSTOMER_TYPE_UNKNOWN) {
      writer.uint32(16).int32(customerTypeToNumber(message.customerType));
    }
    if (message.profileData !== undefined) {
      CustomerProfileData.encode(message.profileData, writer.uint32(26).fork()).join();
    }
    if (message.status !== CustomerStatus.CUSTOMER_STATUS_UNKNOWN) {
      writer.uint32(32).int32(customerStatusToNumber(message.status));
    }
    if (message.riskLevel !== RiskLevel.RISK_LEVEL_UNKNOWN) {
      writer.uint32(40).int32(riskLevelToNumber(message.riskLevel));
    }
    if (message.kycStatus !== KycStatus.KYC_STATUS_UNKNOWN) {
      writer.uint32(48).int32(kycStatusToNumber(message.kycStatus));
    }
    if (message.kycData !== "") {
      writer.uint32(58).string(message.kycData);
    }
    if (message.riskAssessment !== "") {
      writer.uint32(66).string(message.riskAssessment);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(74).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateCustomerRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateCustomerRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.customerType = customerTypeFromJSON(reader.int32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.profileData = CustomerProfileData.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.status = customerStatusFromJSON(reader.int32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.riskLevel = riskLevelFromJSON(reader.int32());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.kycStatus = kycStatusFromJSON(reader.int32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.kycData = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.riskAssessment = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseDeleteCustomerRequest(): DeleteCustomerRequest {
  return { id: "", reason: "" };
}

export const DeleteCustomerRequest: MessageFns<DeleteCustomerRequest> = {
  encode(message: DeleteCustomerRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.reason !== "") {
      writer.uint32(18).string(message.reason);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteCustomerRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteCustomerRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.reason = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetCustomerStatsRequest(): GetCustomerStatsRequest {
  return {
    tenantId: "",
    dateFrom: "",
    dateTo: "",
    customerType: CustomerType.CUSTOMER_TYPE_UNKNOWN,
    riskLevel: RiskLevel.RISK_LEVEL_UNKNOWN,
  };
}

export const GetCustomerStatsRequest: MessageFns<GetCustomerStatsRequest> = {
  encode(message: GetCustomerStatsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.tenantId !== "") {
      writer.uint32(10).string(message.tenantId);
    }
    if (message.dateFrom !== "") {
      writer.uint32(18).string(message.dateFrom);
    }
    if (message.dateTo !== "") {
      writer.uint32(26).string(message.dateTo);
    }
    if (message.customerType !== CustomerType.CUSTOMER_TYPE_UNKNOWN) {
      writer.uint32(32).int32(customerTypeToNumber(message.customerType));
    }
    if (message.riskLevel !== RiskLevel.RISK_LEVEL_UNKNOWN) {
      writer.uint32(40).int32(riskLevelToNumber(message.riskLevel));
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetCustomerStatsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetCustomerStatsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tenantId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.dateFrom = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.dateTo = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.customerType = customerTypeFromJSON(reader.int32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.riskLevel = riskLevelFromJSON(reader.int32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseSearchCustomersRequest(): SearchCustomersRequest {
  return {
    searchTerm: "",
    tenantId: "",
    limit: 0,
    customerType: CustomerType.CUSTOMER_TYPE_UNKNOWN,
    status: CustomerStatus.CUSTOMER_STATUS_UNKNOWN,
    riskLevel: RiskLevel.RISK_LEVEL_UNKNOWN,
  };
}

export const SearchCustomersRequest: MessageFns<SearchCustomersRequest> = {
  encode(message: SearchCustomersRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.searchTerm !== "") {
      writer.uint32(10).string(message.searchTerm);
    }
    if (message.tenantId !== "") {
      writer.uint32(18).string(message.tenantId);
    }
    if (message.limit !== 0) {
      writer.uint32(24).int32(message.limit);
    }
    if (message.customerType !== CustomerType.CUSTOMER_TYPE_UNKNOWN) {
      writer.uint32(32).int32(customerTypeToNumber(message.customerType));
    }
    if (message.status !== CustomerStatus.CUSTOMER_STATUS_UNKNOWN) {
      writer.uint32(40).int32(customerStatusToNumber(message.status));
    }
    if (message.riskLevel !== RiskLevel.RISK_LEVEL_UNKNOWN) {
      writer.uint32(48).int32(riskLevelToNumber(message.riskLevel));
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(58).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchCustomersRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchCustomersRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.searchTerm = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tenantId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.limit = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.customerType = customerTypeFromJSON(reader.int32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.status = customerStatusFromJSON(reader.int32());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.riskLevel = riskLevelFromJSON(reader.int32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUpdateCustomerStatusRequest(): UpdateCustomerStatusRequest {
  return { id: "", status: CustomerStatus.CUSTOMER_STATUS_UNKNOWN, reason: "" };
}

export const UpdateCustomerStatusRequest: MessageFns<UpdateCustomerStatusRequest> = {
  encode(message: UpdateCustomerStatusRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.status !== CustomerStatus.CUSTOMER_STATUS_UNKNOWN) {
      writer.uint32(16).int32(customerStatusToNumber(message.status));
    }
    if (message.reason !== "") {
      writer.uint32(26).string(message.reason);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateCustomerStatusRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateCustomerStatusRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.status = customerStatusFromJSON(reader.int32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.reason = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUpdateCustomerRiskLevelRequest(): UpdateCustomerRiskLevelRequest {
  return { id: "", riskLevel: RiskLevel.RISK_LEVEL_UNKNOWN, reason: "", assessmentData: "" };
}

export const UpdateCustomerRiskLevelRequest: MessageFns<UpdateCustomerRiskLevelRequest> = {
  encode(message: UpdateCustomerRiskLevelRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.riskLevel !== RiskLevel.RISK_LEVEL_UNKNOWN) {
      writer.uint32(16).int32(riskLevelToNumber(message.riskLevel));
    }
    if (message.reason !== "") {
      writer.uint32(26).string(message.reason);
    }
    if (message.assessmentData !== "") {
      writer.uint32(34).string(message.assessmentData);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateCustomerRiskLevelRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateCustomerRiskLevelRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.riskLevel = riskLevelFromJSON(reader.int32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.reason = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.assessmentData = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUpdateCustomerKycStatusRequest(): UpdateCustomerKycStatusRequest {
  return { id: "", kycStatus: KycStatus.KYC_STATUS_UNKNOWN, reason: "", kycData: "" };
}

export const UpdateCustomerKycStatusRequest: MessageFns<UpdateCustomerKycStatusRequest> = {
  encode(message: UpdateCustomerKycStatusRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.kycStatus !== KycStatus.KYC_STATUS_UNKNOWN) {
      writer.uint32(16).int32(kycStatusToNumber(message.kycStatus));
    }
    if (message.reason !== "") {
      writer.uint32(26).string(message.reason);
    }
    if (message.kycData !== "") {
      writer.uint32(34).string(message.kycData);
    }
    if (message.metadata !== undefined) {
      RequestMetadata.encode(message.metadata, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateCustomerKycStatusRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateCustomerKycStatusRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.kycStatus = kycStatusFromJSON(reader.int32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.reason = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.kycData = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = RequestMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetAllCustomersResponse(): GetAllCustomersResponse {
  return { success: false, customers: [], message: "" };
}

export const GetAllCustomersResponse: MessageFns<GetAllCustomersResponse> = {
  encode(message: GetAllCustomersResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    for (const v of message.customers) {
      Customer.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.pagination !== undefined) {
      PaginationMeta.encode(message.pagination, writer.uint32(26).fork()).join();
    }
    if (message.message !== "") {
      writer.uint32(34).string(message.message);
    }
    if (message.error !== undefined) {
      ErrorDetails.encode(message.error, writer.uint32(42).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetAllCustomersResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetAllCustomersResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.customers.push(Customer.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.pagination = PaginationMeta.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.error = ErrorDetails.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetCustomerByIdResponse(): GetCustomerByIdResponse {
  return { success: false, message: "" };
}

export const GetCustomerByIdResponse: MessageFns<GetCustomerByIdResponse> = {
  encode(message: GetCustomerByIdResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.customer !== undefined) {
      Customer.encode(message.customer, writer.uint32(18).fork()).join();
    }
    if (message.message !== "") {
      writer.uint32(26).string(message.message);
    }
    if (message.error !== undefined) {
      ErrorDetails.encode(message.error, writer.uint32(34).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetCustomerByIdResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetCustomerByIdResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.customer = Customer.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.error = ErrorDetails.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseCreateCustomerResponse(): CreateCustomerResponse {
  return { success: false, message: "" };
}

export const CreateCustomerResponse: MessageFns<CreateCustomerResponse> = {
  encode(message: CreateCustomerResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.customer !== undefined) {
      Customer.encode(message.customer, writer.uint32(18).fork()).join();
    }
    if (message.message !== "") {
      writer.uint32(26).string(message.message);
    }
    if (message.error !== undefined) {
      ErrorDetails.encode(message.error, writer.uint32(34).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateCustomerResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateCustomerResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.customer = Customer.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.error = ErrorDetails.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUpdateCustomerResponse(): UpdateCustomerResponse {
  return { success: false, message: "" };
}

export const UpdateCustomerResponse: MessageFns<UpdateCustomerResponse> = {
  encode(message: UpdateCustomerResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.customer !== undefined) {
      Customer.encode(message.customer, writer.uint32(18).fork()).join();
    }
    if (message.message !== "") {
      writer.uint32(26).string(message.message);
    }
    if (message.error !== undefined) {
      ErrorDetails.encode(message.error, writer.uint32(34).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateCustomerResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateCustomerResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.customer = Customer.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.error = ErrorDetails.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseDeleteCustomerResponse(): DeleteCustomerResponse {
  return { success: false, message: "" };
}

export const DeleteCustomerResponse: MessageFns<DeleteCustomerResponse> = {
  encode(message: DeleteCustomerResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.message !== "") {
      writer.uint32(18).string(message.message);
    }
    if (message.error !== undefined) {
      ErrorDetails.encode(message.error, writer.uint32(26).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteCustomerResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteCustomerResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.error = ErrorDetails.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseCustomerStatistics(): CustomerStatistics {
  return {
    totalCustomers: 0,
    activeCustomers: 0,
    pendingKyc: 0,
    highRiskCustomers: 0,
    recentCustomers: 0,
    customersByType: {},
    customersByRiskLevel: {},
  };
}

export const CustomerStatistics: MessageFns<CustomerStatistics> = {
  encode(message: CustomerStatistics, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.totalCustomers !== 0) {
      writer.uint32(8).int32(message.totalCustomers);
    }
    if (message.activeCustomers !== 0) {
      writer.uint32(16).int32(message.activeCustomers);
    }
    if (message.pendingKyc !== 0) {
      writer.uint32(24).int32(message.pendingKyc);
    }
    if (message.highRiskCustomers !== 0) {
      writer.uint32(32).int32(message.highRiskCustomers);
    }
    if (message.recentCustomers !== 0) {
      writer.uint32(40).int32(message.recentCustomers);
    }
    Object.entries(message.customersByType).forEach(([key, value]) => {
      CustomerStatistics_CustomersByTypeEntry.encode({ key: key as any, value }, writer.uint32(50).fork()).join();
    });
    Object.entries(message.customersByRiskLevel).forEach(([key, value]) => {
      CustomerStatistics_CustomersByRiskLevelEntry.encode({ key: key as any, value }, writer.uint32(58).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CustomerStatistics {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCustomerStatistics();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.totalCustomers = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.activeCustomers = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.pendingKyc = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.highRiskCustomers = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.recentCustomers = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          const entry6 = CustomerStatistics_CustomersByTypeEntry.decode(reader, reader.uint32());
          if (entry6.value !== undefined) {
            message.customersByType[entry6.key] = entry6.value;
          }
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          const entry7 = CustomerStatistics_CustomersByRiskLevelEntry.decode(reader, reader.uint32());
          if (entry7.value !== undefined) {
            message.customersByRiskLevel[entry7.key] = entry7.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseCustomerStatistics_CustomersByTypeEntry(): CustomerStatistics_CustomersByTypeEntry {
  return { key: "", value: 0 };
}

export const CustomerStatistics_CustomersByTypeEntry: MessageFns<CustomerStatistics_CustomersByTypeEntry> = {
  encode(message: CustomerStatistics_CustomersByTypeEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CustomerStatistics_CustomersByTypeEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCustomerStatistics_CustomersByTypeEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseCustomerStatistics_CustomersByRiskLevelEntry(): CustomerStatistics_CustomersByRiskLevelEntry {
  return { key: "", value: 0 };
}

export const CustomerStatistics_CustomersByRiskLevelEntry: MessageFns<CustomerStatistics_CustomersByRiskLevelEntry> = {
  encode(
    message: CustomerStatistics_CustomersByRiskLevelEntry,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CustomerStatistics_CustomersByRiskLevelEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCustomerStatistics_CustomersByRiskLevelEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseGetCustomerStatsResponse(): GetCustomerStatsResponse {
  return { success: false, message: "" };
}

export const GetCustomerStatsResponse: MessageFns<GetCustomerStatsResponse> = {
  encode(message: GetCustomerStatsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.statistics !== undefined) {
      CustomerStatistics.encode(message.statistics, writer.uint32(18).fork()).join();
    }
    if (message.message !== "") {
      writer.uint32(26).string(message.message);
    }
    if (message.error !== undefined) {
      ErrorDetails.encode(message.error, writer.uint32(34).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetCustomerStatsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetCustomerStatsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.statistics = CustomerStatistics.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.error = ErrorDetails.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseSearchCustomersResponse(): SearchCustomersResponse {
  return { success: false, customers: [], totalResults: 0, searchTerm: "", searchTime: 0, message: "" };
}

export const SearchCustomersResponse: MessageFns<SearchCustomersResponse> = {
  encode(message: SearchCustomersResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    for (const v of message.customers) {
      Customer.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.totalResults !== 0) {
      writer.uint32(24).int32(message.totalResults);
    }
    if (message.searchTerm !== "") {
      writer.uint32(34).string(message.searchTerm);
    }
    if (message.searchTime !== 0) {
      writer.uint32(40).int32(message.searchTime);
    }
    if (message.message !== "") {
      writer.uint32(50).string(message.message);
    }
    if (message.error !== undefined) {
      ErrorDetails.encode(message.error, writer.uint32(58).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(66).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchCustomersResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchCustomersResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.customers.push(Customer.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.totalResults = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.searchTerm = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.searchTime = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.error = ErrorDetails.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUpdateCustomerStatusResponse(): UpdateCustomerStatusResponse {
  return { success: false, message: "" };
}

export const UpdateCustomerStatusResponse: MessageFns<UpdateCustomerStatusResponse> = {
  encode(message: UpdateCustomerStatusResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.customer !== undefined) {
      Customer.encode(message.customer, writer.uint32(18).fork()).join();
    }
    if (message.message !== "") {
      writer.uint32(26).string(message.message);
    }
    if (message.error !== undefined) {
      ErrorDetails.encode(message.error, writer.uint32(34).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateCustomerStatusResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateCustomerStatusResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.customer = Customer.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.error = ErrorDetails.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUpdateCustomerRiskLevelResponse(): UpdateCustomerRiskLevelResponse {
  return { success: false, message: "" };
}

export const UpdateCustomerRiskLevelResponse: MessageFns<UpdateCustomerRiskLevelResponse> = {
  encode(message: UpdateCustomerRiskLevelResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.customer !== undefined) {
      Customer.encode(message.customer, writer.uint32(18).fork()).join();
    }
    if (message.message !== "") {
      writer.uint32(26).string(message.message);
    }
    if (message.error !== undefined) {
      ErrorDetails.encode(message.error, writer.uint32(34).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateCustomerRiskLevelResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateCustomerRiskLevelResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.customer = Customer.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.error = ErrorDetails.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

function createBaseUpdateCustomerKycStatusResponse(): UpdateCustomerKycStatusResponse {
  return { success: false, message: "" };
}

export const UpdateCustomerKycStatusResponse: MessageFns<UpdateCustomerKycStatusResponse> = {
  encode(message: UpdateCustomerKycStatusResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    if (message.customer !== undefined) {
      Customer.encode(message.customer, writer.uint32(18).fork()).join();
    }
    if (message.message !== "") {
      writer.uint32(26).string(message.message);
    }
    if (message.error !== undefined) {
      ErrorDetails.encode(message.error, writer.uint32(34).fork()).join();
    }
    if (message.metadata !== undefined) {
      ResponseMetadata.encode(message.metadata, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateCustomerKycStatusResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateCustomerKycStatusResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.customer = Customer.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.error = ErrorDetails.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = ResponseMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
};

/** Customer service definition */

export interface CustomerServiceClient {
  /** Customer management operations */

  getAllCustomers(request: GetAllCustomersRequest): Observable<GetAllCustomersResponse>;

  getCustomerById(request: GetCustomerByIdRequest): Observable<GetCustomerByIdResponse>;

  createCustomer(request: CreateCustomerRequest): Observable<CreateCustomerResponse>;

  updateCustomer(request: UpdateCustomerRequest): Observable<UpdateCustomerResponse>;

  deleteCustomer(request: DeleteCustomerRequest): Observable<DeleteCustomerResponse>;

  getCustomerStats(request: GetCustomerStatsRequest): Observable<GetCustomerStatsResponse>;

  searchCustomers(request: SearchCustomersRequest): Observable<SearchCustomersResponse>;

  updateCustomerStatus(request: UpdateCustomerStatusRequest): Observable<UpdateCustomerStatusResponse>;

  updateCustomerRiskLevel(request: UpdateCustomerRiskLevelRequest): Observable<UpdateCustomerRiskLevelResponse>;

  updateCustomerKycStatus(request: UpdateCustomerKycStatusRequest): Observable<UpdateCustomerKycStatusResponse>;
}

/** Customer service definition */

export interface CustomerServiceController {
  /** Customer management operations */

  getAllCustomers(
    request: GetAllCustomersRequest,
  ): Promise<GetAllCustomersResponse> | Observable<GetAllCustomersResponse> | GetAllCustomersResponse;

  getCustomerById(
    request: GetCustomerByIdRequest,
  ): Promise<GetCustomerByIdResponse> | Observable<GetCustomerByIdResponse> | GetCustomerByIdResponse;

  createCustomer(
    request: CreateCustomerRequest,
  ): Promise<CreateCustomerResponse> | Observable<CreateCustomerResponse> | CreateCustomerResponse;

  updateCustomer(
    request: UpdateCustomerRequest,
  ): Promise<UpdateCustomerResponse> | Observable<UpdateCustomerResponse> | UpdateCustomerResponse;

  deleteCustomer(
    request: DeleteCustomerRequest,
  ): Promise<DeleteCustomerResponse> | Observable<DeleteCustomerResponse> | DeleteCustomerResponse;

  getCustomerStats(
    request: GetCustomerStatsRequest,
  ): Promise<GetCustomerStatsResponse> | Observable<GetCustomerStatsResponse> | GetCustomerStatsResponse;

  searchCustomers(
    request: SearchCustomersRequest,
  ): Promise<SearchCustomersResponse> | Observable<SearchCustomersResponse> | SearchCustomersResponse;

  updateCustomerStatus(
    request: UpdateCustomerStatusRequest,
  ): Promise<UpdateCustomerStatusResponse> | Observable<UpdateCustomerStatusResponse> | UpdateCustomerStatusResponse;

  updateCustomerRiskLevel(
    request: UpdateCustomerRiskLevelRequest,
  ):
    | Promise<UpdateCustomerRiskLevelResponse>
    | Observable<UpdateCustomerRiskLevelResponse>
    | UpdateCustomerRiskLevelResponse;

  updateCustomerKycStatus(
    request: UpdateCustomerKycStatusRequest,
  ):
    | Promise<UpdateCustomerKycStatusResponse>
    | Observable<UpdateCustomerKycStatusResponse>
    | UpdateCustomerKycStatusResponse;
}

export function CustomerServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "getAllCustomers",
      "getCustomerById",
      "createCustomer",
      "updateCustomer",
      "deleteCustomer",
      "getCustomerStats",
      "searchCustomers",
      "updateCustomerStatus",
      "updateCustomerRiskLevel",
      "updateCustomerKycStatus",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("CustomerService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("CustomerService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const CUSTOMER_SERVICE_NAME = "CustomerService";

/** Customer service definition */
export type CustomerServiceService = typeof CustomerServiceService;
export const CustomerServiceService = {
  /** Customer management operations */
  getAllCustomers: {
    path: "/customer.CustomerService/GetAllCustomers",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetAllCustomersRequest): Buffer =>
      Buffer.from(GetAllCustomersRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): GetAllCustomersRequest => GetAllCustomersRequest.decode(value),
    responseSerialize: (value: GetAllCustomersResponse): Buffer =>
      Buffer.from(GetAllCustomersResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): GetAllCustomersResponse => GetAllCustomersResponse.decode(value),
  },
  getCustomerById: {
    path: "/customer.CustomerService/GetCustomerById",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetCustomerByIdRequest): Buffer =>
      Buffer.from(GetCustomerByIdRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): GetCustomerByIdRequest => GetCustomerByIdRequest.decode(value),
    responseSerialize: (value: GetCustomerByIdResponse): Buffer =>
      Buffer.from(GetCustomerByIdResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): GetCustomerByIdResponse => GetCustomerByIdResponse.decode(value),
  },
  createCustomer: {
    path: "/customer.CustomerService/CreateCustomer",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: CreateCustomerRequest): Buffer =>
      Buffer.from(CreateCustomerRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): CreateCustomerRequest => CreateCustomerRequest.decode(value),
    responseSerialize: (value: CreateCustomerResponse): Buffer =>
      Buffer.from(CreateCustomerResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): CreateCustomerResponse => CreateCustomerResponse.decode(value),
  },
  updateCustomer: {
    path: "/customer.CustomerService/UpdateCustomer",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: UpdateCustomerRequest): Buffer =>
      Buffer.from(UpdateCustomerRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): UpdateCustomerRequest => UpdateCustomerRequest.decode(value),
    responseSerialize: (value: UpdateCustomerResponse): Buffer =>
      Buffer.from(UpdateCustomerResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): UpdateCustomerResponse => UpdateCustomerResponse.decode(value),
  },
  deleteCustomer: {
    path: "/customer.CustomerService/DeleteCustomer",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: DeleteCustomerRequest): Buffer =>
      Buffer.from(DeleteCustomerRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): DeleteCustomerRequest => DeleteCustomerRequest.decode(value),
    responseSerialize: (value: DeleteCustomerResponse): Buffer =>
      Buffer.from(DeleteCustomerResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): DeleteCustomerResponse => DeleteCustomerResponse.decode(value),
  },
  getCustomerStats: {
    path: "/customer.CustomerService/GetCustomerStats",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetCustomerStatsRequest): Buffer =>
      Buffer.from(GetCustomerStatsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): GetCustomerStatsRequest => GetCustomerStatsRequest.decode(value),
    responseSerialize: (value: GetCustomerStatsResponse): Buffer =>
      Buffer.from(GetCustomerStatsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): GetCustomerStatsResponse => GetCustomerStatsResponse.decode(value),
  },
  searchCustomers: {
    path: "/customer.CustomerService/SearchCustomers",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: SearchCustomersRequest): Buffer =>
      Buffer.from(SearchCustomersRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): SearchCustomersRequest => SearchCustomersRequest.decode(value),
    responseSerialize: (value: SearchCustomersResponse): Buffer =>
      Buffer.from(SearchCustomersResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): SearchCustomersResponse => SearchCustomersResponse.decode(value),
  },
  updateCustomerStatus: {
    path: "/customer.CustomerService/UpdateCustomerStatus",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: UpdateCustomerStatusRequest): Buffer =>
      Buffer.from(UpdateCustomerStatusRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): UpdateCustomerStatusRequest => UpdateCustomerStatusRequest.decode(value),
    responseSerialize: (value: UpdateCustomerStatusResponse): Buffer =>
      Buffer.from(UpdateCustomerStatusResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): UpdateCustomerStatusResponse => UpdateCustomerStatusResponse.decode(value),
  },
  updateCustomerRiskLevel: {
    path: "/customer.CustomerService/UpdateCustomerRiskLevel",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: UpdateCustomerRiskLevelRequest): Buffer =>
      Buffer.from(UpdateCustomerRiskLevelRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): UpdateCustomerRiskLevelRequest => UpdateCustomerRiskLevelRequest.decode(value),
    responseSerialize: (value: UpdateCustomerRiskLevelResponse): Buffer =>
      Buffer.from(UpdateCustomerRiskLevelResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): UpdateCustomerRiskLevelResponse =>
      UpdateCustomerRiskLevelResponse.decode(value),
  },
  updateCustomerKycStatus: {
    path: "/customer.CustomerService/UpdateCustomerKycStatus",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: UpdateCustomerKycStatusRequest): Buffer =>
      Buffer.from(UpdateCustomerKycStatusRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer): UpdateCustomerKycStatusRequest => UpdateCustomerKycStatusRequest.decode(value),
    responseSerialize: (value: UpdateCustomerKycStatusResponse): Buffer =>
      Buffer.from(UpdateCustomerKycStatusResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer): UpdateCustomerKycStatusResponse =>
      UpdateCustomerKycStatusResponse.decode(value),
  },
} as const;

export interface CustomerServiceServer extends UntypedServiceImplementation {
  /** Customer management operations */
  getAllCustomers: handleUnaryCall<GetAllCustomersRequest, GetAllCustomersResponse>;
  getCustomerById: handleUnaryCall<GetCustomerByIdRequest, GetCustomerByIdResponse>;
  createCustomer: handleUnaryCall<CreateCustomerRequest, CreateCustomerResponse>;
  updateCustomer: handleUnaryCall<UpdateCustomerRequest, UpdateCustomerResponse>;
  deleteCustomer: handleUnaryCall<DeleteCustomerRequest, DeleteCustomerResponse>;
  getCustomerStats: handleUnaryCall<GetCustomerStatsRequest, GetCustomerStatsResponse>;
  searchCustomers: handleUnaryCall<SearchCustomersRequest, SearchCustomersResponse>;
  updateCustomerStatus: handleUnaryCall<UpdateCustomerStatusRequest, UpdateCustomerStatusResponse>;
  updateCustomerRiskLevel: handleUnaryCall<UpdateCustomerRiskLevelRequest, UpdateCustomerRiskLevelResponse>;
  updateCustomerKycStatus: handleUnaryCall<UpdateCustomerKycStatusRequest, UpdateCustomerKycStatusResponse>;
}

function toTimestamp(date: Date): Timestamp {
  const seconds = Math.trunc(date.getTime() / 1_000).toString();
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (globalThis.Number(t.seconds) || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
}
