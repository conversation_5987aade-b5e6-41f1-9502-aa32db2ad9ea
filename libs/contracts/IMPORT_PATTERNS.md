# Contract Import Patterns

This document explains the hybrid import strategy implemented in the `@qeep/contracts` library to provide both conflict resolution and clean code.

## Overview

The contracts library supports two import patterns:

1. **Namespace imports** - For cross-module usage and conflict resolution
2. **Direct imports** - For cleaner code within individual services

## Import Patterns

### 1. Namespace Imports (Cross-Module Usage)

Use namespace imports when:

- Working across multiple contract modules
- Need to avoid naming conflicts between modules
- Writing shared utilities or common code

```typescript
// Import namespaces for cross-module usage
import { Customer, AML, Auth } from '@qeep/contracts';

// Use with namespace syntax
const customerStatus: Customer.Enums.CustomerStatus = 'ACTIVE';
const riskLevel: AML.Enums.RiskLevel = 'HIGH';
const authStatus: Auth.Enums.AuthStatus = 'ACTIVE';

// Access DTOs, schemas, and interfaces
const customerDto: Customer.DTOs.CustomerCreateRequestDto = { ... };
const amlDto: AML.DTOs.RiskEvaluationDto = { ... };
```

### 2. Direct Imports (Within-Service Usage)

Use direct imports when:

- Working within a single service (e.g., customer-service)
- Types are unambiguous within the service context
- Want cleaner, more readable code

```typescript
// Import directly from submodules
import {
  CustomerStatus,
  CustomerType,
  ICustomer,
  CustomerCreateRequestDto,
  CustomerQuerySchema
} from '@qeep/contracts/customer';

// Use directly without namespace prefix
const status: CustomerStatus = 'ACTIVE';
const customer: ICustomer = { ... };
const request: CustomerCreateRequestDto = { ... };
```

## Available Direct Import Paths

### Customer Module

```typescript
// DTOs
import { CustomerCreateRequestDto, CustomerQueryDto } from '@qeep/contracts/customer/dtos';

// Enums
import { CustomerStatus, CustomerType, KycStatus } from '@qeep/contracts/customer/enums';

// Interfaces
import { ICustomer, ICustomerProfileData } from '@qeep/contracts/customer/interfaces';

// Schemas
import { CustomerQuerySchema, CustomerCreateRequestSchema } from '@qeep/contracts/customer/schemas';

// All customer types
import { CustomerStatus, ICustomer, CustomerCreateRequestDto } from '@qeep/contracts/customer';
```

### AML Module

```typescript
// DTOs
import { RiskEvaluationDto, RiskAlertDto } from '@qeep/contracts/aml/dtos';

// Enums
import { RiskLevel, AlertType } from '@qeep/contracts/aml/enums';

// Interfaces
import { IRiskEvaluationEngine } from '@qeep/contracts/aml/interfaces';

// Schemas
import { RiskEvaluationSchema } from '@qeep/contracts/aml/schemas';

// All AML types
import { RiskLevel, RiskEvaluationDto } from '@qeep/contracts/aml';
```

### Auth Module

```typescript
// DTOs
import { LoginRequestDto, SignupRequestDto } from '@qeep/contracts/auth/dtos';

// Enums
import { AuthStatus, SessionStatus } from '@qeep/contracts/auth/enums';

// Interfaces
import { IAuthUser } from '@qeep/contracts/auth/interfaces';

// Schemas
import { LoginRequestSchema } from '@qeep/contracts/auth/schemas';

// All auth types
import { AuthStatus, LoginRequestDto } from '@qeep/contracts/auth';
```

### Shared Module

```typescript
// DTOs
import { PaginationMeta, ApiResponseMeta } from '@qeep/contracts/shared/dtos';

// Interfaces
import { BaseEntity, TenantEntity } from '@qeep/contracts/shared/interfaces';

// Schemas
import { PaginationQuerySchema } from '@qeep/contracts/shared/schemas';

// All shared types
import { PaginationMeta, BaseEntity } from '@qeep/contracts/shared';
```

## Guidelines

### When to Use Namespace Imports

✅ **Use namespace imports when:**

- Writing code that uses multiple contract modules
- Creating shared utilities or common libraries
- Working with conflicting type names (e.g., `Customer.Enums.RiskLevel` vs `AML.Enums.RiskLevel`)
- Writing integration or orchestration code

```typescript
// Good: Cross-module usage
import { Customer, AML } from '@qeep/contracts';

function evaluateCustomerRisk(customer: Customer.DTOs.CustomerResponseDto, evaluation: AML.DTOs.RiskEvaluationDto): AML.Enums.RiskLevel {
  // Implementation
}
```

### When to Use Direct Imports

✅ **Use direct imports when:**

- Working within a single service (customer-service, auth-service, etc.)
- Types are unambiguous within the context
- Want cleaner, more readable code
- Building service-specific business logic

```typescript
// Good: Within customer-service
import { CustomerStatus, ICustomer, CustomerCreateRequestDto } from '@qeep/contracts/customer';

class CustomerService {
  async createCustomer(request: CustomerCreateRequestDto): Promise<ICustomer> {
    // Implementation
  }
}
```

## Migration Guide

If you have existing code using individual imports from the main package:

```typescript
// Old pattern (now deprecated)
import {
  CustomerStatus,
  ICustomer,
  RiskLevel, // Ambiguous - which module?
  LoginRequestDto,
} from '@qeep/contracts';
```

Migrate to:

```typescript
// New pattern: Use direct imports for single-module usage
import { CustomerStatus, ICustomer } from '@qeep/contracts/customer';
import { LoginRequestDto } from '@qeep/contracts/auth';

// Or use namespaces for cross-module usage
import { Customer, AML, Auth } from '@qeep/contracts';
const status: Customer.Enums.CustomerStatus = 'ACTIVE';
const risk: AML.Enums.RiskLevel = 'HIGH';
const login: Auth.DTOs.LoginRequestDto = { ... };
```

## Benefits

### Namespace Imports

- ✅ Prevents naming conflicts between modules
- ✅ Clear module boundaries and ownership
- ✅ Explicit about which module types come from
- ✅ Better for cross-module integration code

### Direct Imports

- ✅ Cleaner, more readable code within services
- ✅ Shorter import statements
- ✅ Familiar pattern for developers
- ✅ Better IDE autocomplete and navigation
- ✅ Easier refactoring within service boundaries

This hybrid approach gives you the best of both worlds: conflict resolution when needed, and clean code when possible.
