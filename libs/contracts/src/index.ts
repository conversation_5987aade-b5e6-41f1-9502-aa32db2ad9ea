/**
 * Qeep Contracts Library
 * Centralized type definitions, DTOs, and interfaces for the Qeep platform
 *
 * This library provides:
 * - Zod validation schemas for runtime type checking
 * - TypeScript interfaces for compile-time type safety
 * - Domain-driven organization of contracts
 * - Consistent API response formats
 * - Shared enums and constants
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// Export the NestJS module for dependency injection
export * from './lib/contracts.module';

// Export common schemas and utilities
export * from './common';

// Export domain modules with explicit re-exports to resolve conflicts
export * from './auth';
export * from './tenant';

// Export customer module with renamed conflicting exports
export {
  AccountAccessLevel,
  AccountCurrency,
  AccountResponseDto,
  // Account enums
  AccountStatus,
  AddressType,
  AlertStatus,
  // Account DTOs
  CreateAccountRequestDto,
  // Customer enums - renamed to avoid conflicts with AML
  AccountType as CustomerAccountType,
  CustomerAddressDto,
  CustomerAddressRequestDto,
  AlertSeverity as CustomerAlertSeverity,
  CustomerAlertSummaryDto,
  AlertType as CustomerAlertType,
  CustomerCreateRequestDto,
  CustomerDocumentSummaryDto,
  CustomerType as CustomerEntityType,
  CustomerKycDataDto,
  KycStatus as CustomerKycStatus,
  CustomerListResponseDto,
  CustomerProfileDataDto,
  CustomerProfileDataRequestDto,
  CustomerQueryDto,
  CustomerQueryOptions,
  CustomerResponseDto,
  CustomerRiskAssessmentDto,
  RiskLevel as CustomerRiskLevel,
  CustomerRiskScoreSummaryDto,
  CustomerSearchDto,
  CustomerSortField,
  CustomerSortFieldTransformer,
  CustomerStatisticsResponseDto,
  CustomerStatsQueryDto,
  // Non-conflicting customer enums
  CustomerStatus,
  CustomerUpdateRequestDto,
  DocumentType,
  FindAccountsRequestDto,
  FindAccountsResponseDto,
  // Customer DTOs
  GenderType,
  // Customer interfaces
  ICustomer,
  ICustomerAddress,
  ICustomerAlert,
  ICustomerDocument,
  ICustomerKycData,
  ICustomerProfileData,
  ICustomerQueryOptions,
  ICustomerRelationship,
  ICustomerRiskAssessment,
  ICustomerRiskScore,
  ICustomerStatistics,
  RelationshipStatus,
  RelationshipType,
  SortOrder,
  // Customer transformers
  transformSortFieldFromSnakeCase,
  UpdateAccountBalanceRequestDto,
  UpdateAccountRequestDto,
  ValidateAccountRequestDto,
  ValidateAccountResponseDto,
  VerificationStatus,
} from './customer';

// Re-export the customer namespace
export { Customer } from './customer';

// Export AML module - primary source for risk management types
export {
  AccountInfoDto,
  AccountInfoSchema,
  AccountType,
  AlertSeverity,
  AlertType,
  // AML utilities
  AMLDtoTransforms,
  AMLEnums,
  AuditActionType,
  ConfigurationType,
  CurrencyRiskLevel,
  CustomerInfoDto,
  CustomerInfoSchema,
  CustomerRiskCategory,
  CustomerRiskProfileDto,
  CustomerRiskProfileSchema,
  CustomerType,
  DataSourceType,
  ErrorDetailsDto,
  ErrorDetailsSchema,
  EvaluateCustomerRiskRequestDto,
  EvaluateCustomerRiskRequestSchema,
  EvaluateCustomerRiskResponseDto,
  EvaluateCustomerRiskResponseSchema,
  EvaluateTransactionRiskRequestDto,
  EvaluateTransactionRiskRequestSchema,
  EvaluateTransactionRiskResponseDto,
  EvaluateTransactionRiskResponseSchema,
  GeographicRiskLevel,
  GetAMLStatusRequestDto,
  GetAMLStatusRequestSchema,
  GetAMLStatusResponseDto,
  GetAMLStatusResponseSchema,
  GetRiskProfileRequestDto,
  GetRiskProfileRequestSchema,
  GetRiskProfileResponseDto,
  GetRiskProfileResponseSchema,
  InternalCustomerRiskProfileDto,
  InternalRiskAlertDto,
  InternalRiskEvaluationDto,
  KycStatus,
  MonitoringFrequency,
  PatternType,
  PepCategory,
  // AML DTOs
  RequestMetadataDto,
  // AML schemas
  RequestMetadataSchema,
  ResponseMetadataDto,
  ResponseMetadataSchema,
  RetentionPeriod,
  ReviewStatus,
  RiskAlertDto,
  RiskAlertSchema,
  RiskEvaluationDto,
  RiskEvaluationSchema,
  // AML enums (primary definitions)
  RiskLevel,
  RiskRuleType,
  RuleExecutionStatus,
  SanctionsListType,
  SanctionsMatchType,
  TransactionContextDto,
  TransactionContextSchema,
  TransactionStatus,
  UpdateRiskRulesRequestDto,
  UpdateRiskRulesRequestSchema,
  UpdateRiskRulesResponseDto,
  UpdateRiskRulesResponseSchema,
} from './aml';

// Re-export the AML namespace
export { AML } from './aml';

// Export transaction module with renamed conflicting exports
export {
  // Transaction DTOs
  ProcessTransactionRequestDto,
  // Transaction schemas
  ProcessTransactionRequestSchema,
  ProcessTransactionResponseDto,
  ProcessTransactionResponseSchema,

  // Transaction-specific types (renamed to avoid conflicts)
  AccountInfoDto as TransactionAccountInfoDto,
  AccountInfoSchema as TransactionAccountInfoSchema,
  AlertDto as TransactionAlertDto,
  AlertSchema as TransactionAlertSchema,
  // Transaction enums
  TransactionAlertSeverity,
  TransactionAlertType,
  TransactionStatus as TransactionProcessingStatus,
} from './transaction';

// Re-export the transaction namespace
export { Transaction } from './transaction';

// Shared namespace is now exported from './shared'
export { Shared } from './shared';
