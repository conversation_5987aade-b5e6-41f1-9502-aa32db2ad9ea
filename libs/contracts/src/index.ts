/**
 * Qeep Contracts Library
 * Centralized type definitions, DTOs, and interfaces for the Qeep platform
 *
 * This library provides:
 * - Zod validation schemas for runtime type checking
 * - TypeScript interfaces for compile-time type safety
 * - Domain-driven organization of contracts using namespaces
 * - Direct imports from submodules for cleaner code within services
 * - Consistent API response formats
 * - Shared enums and constants
 *
 * Import Patterns:
 * - Cross-module usage: import { Customer, AML } from '@qeep/contracts'
 * - Within-service usage: import { CustomerCreateRequestDto } from '@qeep/contracts/customer/dtos'
 *
 * @version 2.1.0
 * <AUTHOR> Development Team
 */

// Export the NestJS module for dependency injection
export * from './lib/contracts.module';

// Export common schemas and utilities (these don't conflict)
export * from './common';

// Export domain namespaces for cross-module usage and conflict resolution
export { AML } from './aml';
export { Audit } from './audit';
export { Auth } from './auth';
export { Customer } from './customer';
export { Notification } from './notification';
export { Shared } from './shared';
export { Tenant } from './tenant';
export { Transaction } from './transaction';
export { User } from './user';
