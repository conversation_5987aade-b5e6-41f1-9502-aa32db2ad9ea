import { z } from 'zod';
import * as OnboardingSchemas from '../schemas/onboarding.schema';

/**
 * DTO for initiating a new tenant onboarding workflow
 */
export type OnboardingInitiateRequestDto = z.infer<typeof OnboardingSchemas.OnboardingInitiateRequestSchema>;

/**
 * DTO for onboarding initiation response
 */
export type OnboardingInitiateResponseDto = z.infer<typeof OnboardingSchemas.OnboardingInitiateResponseSchema>;

/**
 * DTO for onboarding workflow status response
 */
export type OnboardingWorkflowStatusResponseDto = z.infer<typeof OnboardingSchemas.OnboardingWorkflowStatusResponseSchema>;

/**
 * DTO for task update request
 */
export type TaskUpdateRequestDto = z.infer<typeof OnboardingSchemas.TaskUpdateRequestSchema>;

/**
 * DTO for task response
 */
export type TaskResponseDto = z.infer<typeof OnboardingSchemas.TaskResponseSchema>;

/**
 * DTO for tasks list response
 */
export type TasksListResponseDto = z.infer<typeof OnboardingSchemas.TasksListResponseSchema>;

/**
 * DTO for completing the onboarding workflow
 */
export type CompleteOnboardingRequestDto = z.infer<typeof OnboardingSchemas.CompleteOnboardingRequestSchema>;

/**
 * DTO for onboarding completion response
 */
export type CompleteOnboardingResponseDto = z.infer<typeof OnboardingSchemas.CompleteOnboardingResponseSchema>;

/**
 * DTO for API credentials response
 */
export type ApiCredentialsResponseDto = z.infer<typeof OnboardingSchemas.ApiCredentialsResponseSchema>;

/**
 * DTO for regenerating credentials request
 */
export type RegenerateCredentialsRequestDto = z.infer<typeof OnboardingSchemas.RegenerateCredentialsRequestSchema>;

/**
 * DTO for environment switch request
 */
export type EnvironmentSwitchRequestDto = z.infer<typeof OnboardingSchemas.EnvironmentSwitchRequestSchema>;

/**
 * DTO for environment switch response
 */
export type EnvironmentSwitchResponseDto = z.infer<typeof OnboardingSchemas.EnvironmentSwitchResponseSchema>;
