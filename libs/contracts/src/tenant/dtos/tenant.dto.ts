import { z } from 'zod';
import {
  MinimalTenantResponseSchema,
  SingleTenantResponseSchema,
  TenantCreateRequestSchema,
  TenantListResponseSchema,
  TenantQueryMetaSchema,
  TenantQueryRequestSchema,
  TenantResponseSchema,
  TenantStatusUpdateRequestSchema,
  TenantUpdateRequestSchema,
} from '../schemas';

export type TenantCreateRequestDto = z.infer<typeof TenantCreateRequestSchema>;
export type TenantUpdateRequestDto = z.infer<typeof TenantUpdateRequestSchema>;
export type TenantStatusUpdateRequestDto = z.infer<typeof TenantStatusUpdateRequestSchema>;

export type TenantQueryRequestDto = z.infer<typeof TenantQueryRequestSchema>;
export type TenantQueryMetaDto = z.infer<typeof TenantQueryMetaSchema>;

export type TenantResponseDto = z.infer<typeof TenantResponseSchema>;
export type MinimalTenantResponseDto = z.infer<typeof MinimalTenantResponseSchema>;
export type TenantListResponseDto = z.infer<typeof TenantListResponseSchema>;
export type SingleTenantResponseDto = z.infer<typeof SingleTenantResponseSchema>;
