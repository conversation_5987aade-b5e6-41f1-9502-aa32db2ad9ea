import { z } from 'zod';
import { CreateApiKeySchema, CreateApiKeyResponseSchema } from '../schemas/create-api-key.schema';
import { ListApiKeysSchema, ApiKeyListItemSchema, ListApiKeysResponseSchema } from '../schemas/list-api-keys.schema';
import { RotateApiKeySchema, RotateApiKeyResponseSchema } from '../schemas/rotate-api-key.schema';

/**
 * Create API key request DTO inferred from schema
 */
export type CreateApiKeyRequestDto = z.infer<typeof CreateApiKeySchema>;

/**
 * Create API key response DTO inferred from schema
 */
export type CreateApiKeyResponseDto = z.infer<typeof CreateApiKeyResponseSchema>;

/**
 * List API keys query DTO inferred from schema
 */
export type ListApiKeysQueryDto = z.infer<typeof ListApiKeysSchema>;

/**
 * API key list item DTO inferred from schema
 */
export type ApiKeyListItemDto = z.infer<typeof ApiKeyListItemSchema>;

/**
 * List API keys response DTO inferred from schema
 */
export type ListApiKeysResponseDto = z.infer<typeof ListApiKeysResponseSchema>;

/**
 * Rotate API key request DTO inferred from schema
 */
export type RotateApiKeyRequestDto = z.infer<typeof RotateApiKeySchema>;

/**
 * Rotate API key response DTO inferred from schema
 */
export type RotateApiKeyResponseDto = z.infer<typeof RotateApiKeyResponseSchema>;
