import { z } from 'zod';
import { CreateWebhookKeySchema, CreateWebhookKeyResponseSchema } from '../schemas/create-webhook-key.schema';
import { ListWebhookKeysSchema, WebhookKeyListItemSchema, ListWebhookKeysResponseSchema } from '../schemas/list-webhook-keys.schema';
import { RotateWebhookKeySchema, RotateWebhookKeyResponseSchema } from '../schemas/rotate-webhook-key.schema';

/**
 * Create webhook key request DTO inferred from schema
 */
export type CreateWebhookKeyRequestDto = z.infer<typeof CreateWebhookKeySchema>;

/**
 * Create webhook key response DTO inferred from schema
 */
export type CreateWebhookKeyResponseDto = z.infer<typeof CreateWebhookKeyResponseSchema>;

/**
 * List webhook keys query DTO inferred from schema
 */
export type ListWebhookKeysQueryDto = z.infer<typeof ListWebhookKeysSchema>;

/**
 * Webhook key list item DTO inferred from schema
 */
export type WebhookKeyListItemDto = z.infer<typeof WebhookKeyListItemSchema>;

/**
 * List webhook keys response DTO inferred from schema
 */
export type ListWebhookKeysResponseDto = z.infer<typeof ListWebhookKeysResponseSchema>;

/**
 * Rotate webhook key request DTO inferred from schema
 */
export type RotateWebhookKeyRequestDto = z.infer<typeof RotateWebhookKeySchema>;

/**
 * Rotate webhook key response DTO inferred from schema
 */
export type RotateWebhookKeyResponseDto = z.infer<typeof RotateWebhookKeyResponseSchema>;
