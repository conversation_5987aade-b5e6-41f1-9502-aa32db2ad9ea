/* eslint-disable @typescript-eslint/no-explicit-any */
import { z } from 'zod';
import {
  CreateRiskProfileRequestSchema,
  UpdateRiskProfileRequestSchema,
  CreateRuleRequestSchema,
  UpdateRuleRequestSchema,
  AssignCustomerToRiskProfileRequestSchema,
  UpdateCustomerRiskProfileAssignmentRequestSchema,
  AutoAssignCustomersRequestSchema,
  RiskProfileQuerySchema,
  RuleQuerySchema,
  CustomerAssignmentQuerySchema
} from '../schemas/risk-profile.schema';

/**
 * Risk Profile DTOs
 * Type-safe data transfer objects inferred from Zod schemas
 */

// Risk Profile DTOs
export type CreateRiskProfileRequestDto = z.infer<typeof CreateRiskProfileRequestSchema>;
export type UpdateRiskProfileRequestDto = z.infer<typeof UpdateRiskProfileRequestSchema>;
export type RiskProfileQueryDto = z.infer<typeof RiskProfileQuerySchema>;

// Rule DTOs
export type CreateRuleRequestDto = z.infer<typeof CreateRuleRequestSchema>;
export type UpdateRuleRequestDto = z.infer<typeof UpdateRuleRequestSchema>;
export type RuleQueryDto = z.infer<typeof RuleQuerySchema>;

// Customer Assignment DTOs
export type AssignCustomerToRiskProfileRequestDto = z.infer<typeof AssignCustomerToRiskProfileRequestSchema>;
export type UpdateCustomerRiskProfileAssignmentRequestDto = z.infer<typeof UpdateCustomerRiskProfileAssignmentRequestSchema>;
export type CustomerAssignmentQueryDto = z.infer<typeof CustomerAssignmentQuerySchema>;

// Auto Assignment DTOs
export type AutoAssignCustomersRequestDto = z.infer<typeof AutoAssignCustomersRequestSchema>;

// Response DTOs
export interface RiskProfileResponseDto {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  ruleCategory: string;
  isActive: boolean;
  isSystemGenerated: boolean;
  priority: number;
  assignmentPriority: number;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  rulesCount?: number;
  customersCount?: number;
}

export interface RuleResponseDto {
  id: string;
  tenantId: string;
  riskProfileId: string;
  name: string;
  description?: string;
  ruleType: string;
  ruleCategory: string;
  conditions: Record<string, any>;
  actions: Record<string, any>;
  thresholds?: Record<string, number>;
  priority: number;
  isActive: boolean;
  effectiveFrom?: string;
  effectiveUntil?: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  riskProfileName?: string;
}

export interface CustomerRiskProfileAssignmentResponseDto {
  id: string;
  tenantId: string;
  customerId: string;
  riskProfileId: string;
  assignmentType: string;
  assignmentCriteria?: Record<string, any>;
  assignmentPriority: number;
  allowOverride: boolean;
  assignedAt: string;
  assignedBy?: string;
  isActive: boolean;
  effectiveFrom?: string;
  effectiveUntil?: string;
  metadata?: Record<string, any>;
  riskProfileName?: string;
  customerName?: string;
}

export interface RuleExecutionHistoryResponseDto {
  id: string;
  tenantId: string;
  ruleId: string;
  customerId?: string;
  riskProfileId: string;
  executionContext?: Record<string, any>;
  ruleResult?: Record<string, any>;
  actionsTaken?: Record<string, any>;
  executedAt: string;
  executionTimeMs?: number;
  priorityUsed?: number;
  ruleName?: string;
  riskProfileName?: string;
  customerName?: string;
}

// List Response DTOs
export interface RiskProfileListResponseDto {
  riskProfiles: RiskProfileResponseDto[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface RuleListResponseDto {
  rules: RuleResponseDto[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface CustomerAssignmentListResponseDto {
  assignments: CustomerRiskProfileAssignmentResponseDto[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface RuleExecutionHistoryListResponseDto {
  executions: RuleExecutionHistoryResponseDto[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Single Item Response DTOs
export interface SingleRiskProfileResponseDto {
  riskProfile: RiskProfileResponseDto;
}

export interface SingleRuleResponseDto {
  rule: RuleResponseDto;
}

export interface SingleCustomerAssignmentResponseDto {
  assignment: CustomerRiskProfileAssignmentResponseDto;
}

// Customer Risk Profile Summary DTOs
export interface CustomerRiskProfileSummaryResponseDto {
  customerId: string;
  tenantId: string;
  assignments: CustomerRiskProfileAssignmentResponseDto[];
  applicableRules: RuleResponseDto[];
  effectiveRiskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  precedenceResult: {
    applicableRules: RuleResponseDto[];
    conflictingRules: Array<{
      rules: RuleResponseDto[];
      conflictType: 'PRIORITY' | 'ACTION' | 'CONDITION';
      resolution: 'HIGHEST_PRIORITY' | 'MANUAL_OVERRIDE' | 'SYSTEM_DEFAULT';
      resolvedRule: RuleResponseDto;
    }>;
    precedenceLog: Array<{
      ruleId: string;
      priority: number;
      assignmentType: string;
      selected: boolean;
      reason: string;
    }>;
  };
  lastUpdated: string;
}

// Auto Assignment Result DTOs
export interface AutoAssignmentResultDto {
  dryRun: boolean;
  totalCustomersEvaluated: number;
  customersToAssign: number;
  assignments: Array<{
    customerId: string;
    customerName?: string;
    riskProfileId: string;
    riskProfileName: string;
    assignmentType: string;
    assignmentCriteria: Record<string, any>;
    reason: string;
  }>;
  conflicts: Array<{
    customerId: string;
    customerName?: string;
    conflictType: string;
    conflictingAssignments: Array<{
      riskProfileId: string;
      riskProfileName: string;
      reason: string;
    }>;
    resolution: string;
  }>;
  executedAt: string;
}

// Bulk Operation DTOs
export interface BulkAssignmentRequestDto {
  customerIds: string[];
  riskProfileId: string;
  assignmentType: string;
  assignmentPriority?: number;
  allowOverride?: boolean;
  effectiveFrom?: string;
  effectiveUntil?: string;
  metadata?: Record<string, any>;
}

export interface BulkAssignmentResultDto {
  totalRequested: number;
  successful: number;
  failed: number;
  results: Array<{
    customerId: string;
    success: boolean;
    assignmentId?: string;
    error?: string;
  }>;
  executedAt: string;
}

// Statistics DTOs
export interface RiskProfileStatsDto {
  totalRiskProfiles: number;
  systemGenerated: number;
  customProfiles: number;
  activeProfiles: number;
  inactiveProfiles: number;
  byCategory: Record<string, number>;
  totalRules: number;
  activeRules: number;
  totalAssignments: number;
  activeAssignments: number;
  byAssignmentType: Record<string, number>;
}

export interface RuleExecutionStatsDto {
  totalExecutions: number;
  executionsToday: number;
  executionsThisWeek: number;
  executionsThisMonth: number;
  averageExecutionTime: number;
  byRuleCategory: Record<string, number>;
  byRuleType: Record<string, number>;
  topExecutedRules: Array<{
    ruleId: string;
    ruleName: string;
    executionCount: number;
    averageExecutionTime: number;
  }>;
}
