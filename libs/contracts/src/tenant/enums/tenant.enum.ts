/**
 * Tenant status enumeration
 * Defines the possible states of a tenant
 */
export enum TenantStatus {
  /** Tenant is active and operational */
  ACTIVE = 'ACTIVE',

  /** Tenant is inactive (temporarily disabled) */
  INACTIVE = 'INACTIVE',

  /** Tenant is suspended (blocked due to policy violation) */
  SUSPENDED = 'SUSPENDED',

  /** Tenant is in trial period */
  TRIAL = 'TRIAL',

  /** Tenant status is unknown or not set */
  UNKNOWN = 'UNKNOWN',
}

/**
 * Array of all tenant status values for validation
 */
export const TENANT_STATUS_VALUES = Object.values(TenantStatus) as [TenantStatus, ...TenantStatus[]];

/**
 * Mapping from API status values (lowercase) to enum values
 */
export const TENANT_STATUS_MAP: Record<string, TenantStatus> = {
  active: TenantStatus.ACTIVE,
  inactive: TenantStatus.INACTIVE,
  suspended: TenantStatus.SUSPENDED,
  trial: TenantStatus.TRIAL,
  unknown: TenantStatus.UNKNOWN,
} as const;

/**
 * Tenant sorting field enumeration
 * Defines the available fields for sorting tenant queries
 */
export enum TenantSortField {
  /** Sort by tenant name */
  NAME = 'name',

  /** Sort by tenant code */
  CODE = 'code',

  /** Sort by creation date */
  CREATED_AT = 'createdAt',

  /** Sort by last update date */
  UPDATED_AT = 'updatedAt',
}

/**
 * Sort order enumeration for tenant queries
 * Defines ascending and descending sort directions
 */
export enum TenantSortOrder {
  /** Sort in ascending order (A-Z, 0-9, oldest first) */
  ASC = 'asc',

  /** Sort in descending order (Z-A, 9-0, newest first) */
  DESC = 'desc',
}

/**
 * Array of all tenant sort field values for validation
 */
export const TENANT_SORT_FIELD_VALUES = Object.values(TenantSortField) as [TenantSortField, ...TenantSortField[]];

/**
 * Array of all valid sort order values
 * Used for validation and type checking
 */
export const TENANT_SORT_ORDER_VALUES = Object.values(TenantSortOrder) as [TenantSortOrder, ...TenantSortOrder[]];

/**
 * Mapping from API sort field values (snake_case) to enum values (camelCase)
 */
export const TENANT_SORT_FIELD_MAP: Record<string, TenantSortField> = {
  name: TenantSortField.NAME,
  code: TenantSortField.CODE,
  created_at: TenantSortField.CREATED_AT,
  updated_at: TenantSortField.UPDATED_AT,
} as const;

/**
 * Tenant subscription plan enumeration
 * Defines the available subscription plans for tenants
 */
export enum TenantPlan {
  /** Basic plan with limited features */
  BASIC = 'BASIC',

  /** Professional plan with advanced features */
  PROFESSIONAL = 'PROFESSIONAL',

  /** Enterprise plan with full features */
  ENTERPRISE = 'ENTERPRISE',
}

/**
 * Array of all tenant plan values for validation
 */
export const TENANT_PLAN_VALUES = Object.values(TenantPlan) as [TenantPlan, ...TenantPlan[]];

/**
 * Mapping from API plan values (lowercase) to enum values
 */
export const TENANT_PLAN_MAP: Record<string, TenantPlan> = {
  basic: TenantPlan.BASIC,
  professional: TenantPlan.PROFESSIONAL,
  enterprise: TenantPlan.ENTERPRISE,
} as const;
