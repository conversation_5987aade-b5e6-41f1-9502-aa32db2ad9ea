export enum OnboardingStatus {
  PENDING = 'PENDING',
  INITIATED = 'INITIATED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

export enum OnboardingTaskStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  FAILED = 'FAILED',
}

export enum OnboardingStepType {
  ORGANIZATION_INFO = 'ORGANIZATION_INFO',
  COMPLIANCE_SETUP = 'COMPLIANCE_SETUP',
  TECHNICAL_CONFIG = 'TECHNICAL_CONFIG',
  ADMIN_USER = 'ADMIN_USER',
  DOCUMENT_VERIFICATION = 'DOCUMENT_VERIFICATION',
  REVIEW_ACTIVATION = 'REVIEW_ACTIVATION',
}

export enum OnboardingStepStatus {
  NOT_STARTED = 'NOT_STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  SKIPPED = 'SKIPPED',
  FAILED = 'FAILED',
}

export enum OnboardingDocumentType {
  BANKING_LICENSE = 'BANKING_LICENSE',
  INCORPORATION_CERTIFICATE = 'INCORPORATION_CERTIFICATE',
  TAX_CERTIFICATE = 'TAX_CERTIFICATE',
  BUSINESS_REGISTRATION = 'BUSINESS_REGISTRATION',
  INSURANCE_CERTIFICATE = 'INSURANCE_CERTIFICATE',
  AML_POLICY = 'AML_POLICY',
  OTHER = 'OTHER',
}

export enum DocumentVerificationStatus {
  PENDING = 'PENDING',
  VERIFIED = 'VERIFIED',
  REJECTED = 'REJECTED',
  EXPIRED = 'EXPIRED',
}

export enum WorkflowStatus {
  INITIATED = 'INITIATED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  FAILED = 'FAILED',
}

export enum TaskStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  FAILED = 'FAILED',
}

export enum SubscriptionPlan {
  BASIC = 'BASIC',
  PREMIUM = 'PREMIUM',
  ENTERPRISE = 'ENTERPRISE',
}

export enum InstitutionType {
  COMMUNITY_BANK = 'COMMUNITY_BANK',
  INVESTMENT_BANK = 'INVESTMENT_BANK',
  CREDIT_UNION = 'CREDIT_UNION',
  FINTECH = 'FINTECH',
  PAYMENT_PROCESSOR = 'PAYMENT_PROCESSOR',
  MICROFINANCE = 'MICROFINANCE',
}

export enum OnboardingTaskPriority {
  CRITICAL = 'CRITICAL',
  HIGH = 'HIGH',
  MEDIUM = 'MEDIUM',
  LOW = 'LOW',
}

export enum OnboardingPhase {
  INITIAL_SETUP = 'INITIAL_SETUP',
  REGULATORY = 'REGULATORY',
  TECHNICAL = 'TECHNICAL',
  INSTITUTION_SPECIFIC = 'INSTITUTION_SPECIFIC',
  TECHNICAL_INFRASTRUCTURE = 'TECHNICAL_INFRASTRUCTURE',
  INSTITUTION_CONFIGURATION = 'INSTITUTION_CONFIGURATION',
  REGULATORY_COMPLIANCE = 'REGULATORY_COMPLIANCE',
  TESTING = 'TESTING',
  GO_LIVE = 'GO_LIVE',
  TESTING_VALIDATION = 'TESTING_VALIDATION',
  TRAINING_GO_LIVE = 'TRAINING_GO_LIVE',
}

export enum OnboardingTaskCategory {
  ADMINISTRATIVE = 'ADMINISTRATIVE',
  DOCUMENTATION = 'DOCUMENTATION',
  RISK_MANAGEMENT = 'RISK_MANAGEMENT',
  PROJECT_MANAGEMENT = 'PROJECT_MANAGEMENT',
  REGULATORY_COMPLIANCE = 'REGULATORY_COMPLIANCE',
  TECHNICAL_INFRASTRUCTURE = 'TECHNICAL_INFRASTRUCTURE',
  COMPLIANCE = 'COMPLIANCE',
  TECHNICAL = 'TECHNICAL',
  TRAINING = 'TRAINING',
  CONFIGURATION = 'CONFIGURATION',
  TESTING = 'TESTING',
  DEPLOYMENT = 'DEPLOYMENT',
  SECURITY = 'SECURITY',
  GO_LIVE = 'GO_LIVE',
}
