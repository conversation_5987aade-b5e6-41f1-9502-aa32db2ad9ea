import { KeyEnvironment } from '../enums/key-environment.enum';

/**
 * API Key entity interface representing the database model
 */
export interface ApiKeyEntity {
	/** Unique identifier for the API key */
	id: string;
	/** Tenant ID that owns this API key */
	tenantId: string;
	/** User-friendly name for the API key */
	name: string;
	/** Environment (LIVE or TEST) */
	environment: KeyEnvironment;
	/** Key prefix for identification */
	keyPrefix: string;
	/** Hashed version of the API key */
	keyHash: string;
	/** When the key was last used */
	lastUsedAt: Date | null;
	/** When the key expires (if set) */
	expiresAt: Date | null;
	/** Whether the key is active */
	isActive: boolean;
	/** When the key was created */
	createdAt: Date;
	/** When the key was last updated */
	updatedAt: Date;
}

/**
 * API Key creation data interface
 */
export interface CreateApiKeyData {
	/** Tenant ID that will own this API key */
	tenantId: string;
	/** User-friendly name for the API key */
	name: string;
	/** Environment (LIVE or TEST) */
	environment: KeyEnvironment;
	/** Key prefix for identification */
	keyPrefix: string;
	/** Hashed version of the API key */
	keyHash: string;
	/** When the key expires (if set) */
	expiresAt?: Date;
	/** Whether the key is active */
	isActive?: boolean;
}

/**
 * API Key update data interface
 */
export interface UpdateApiKeyData {
	/** User-friendly name for the API key */
	name?: string;
	/** Key prefix for identification */
	keyPrefix?: string;
	/** Hashed version of the API key */
	keyHash?: string;
	/** When the key was last used */
	lastUsedAt?: Date;
	/** When the key expires (if set) */
	expiresAt?: Date;
	/** Whether the key is active */
	isActive?: boolean;
}

/**
 * API Key query filters interface
 */
export interface ApiKeyFilters {
	/** Filter by tenant ID */
	tenantId: string;
	/** Filter by environment */
	environment?: KeyEnvironment;
	/** Filter by active status */
	isActive?: boolean;
	/** Search in name */
	search?: string;
}
