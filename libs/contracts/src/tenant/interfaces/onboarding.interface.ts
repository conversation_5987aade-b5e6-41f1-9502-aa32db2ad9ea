import { TaskStatus, OnboardingPhase, OnboardingTaskCategory } from '../enums/onboarding.enum';

export interface OnboardingTask {
  id?: string;
  workflowId: string;
  taskId: string;
  name: string;
  description?: string;
  status: TaskStatus;
  assignee: string;
  dueDate: Date;
  startedAt?: Date;
  completedAt?: Date;
  priority: number;
  dependsOn: string[];
  metadata?: Record<string, unknown>;
  createdAt?: Date;
  updatedAt?: Date;
  phase?: OnboardingPhase;
  category?: OnboardingTaskCategory;
  estimatedHours?: number;
}
