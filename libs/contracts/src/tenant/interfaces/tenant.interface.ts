/* eslint-disable @typescript-eslint/no-explicit-any */
import { TenantPlan, TenantSortField, TenantSortOrder, TenantStatus } from '../enums';

/**
 * Base tenant interface
 * Represents the core tenant entity
 */
export interface ITenant {
  /** Unique tenant identifier */
  id: string;

  /** Unique tenant code (used in URLs, etc.) */
  code: string;

  /** Display name of the tenant */
  name: string;

  /** Optional tenant description */
  description?: string;

  /** Tenant domain (for custom domains) */
  domain?: string;

  /** URL to tenant logo */
  logoUrl?: string;

  /** Tenant website URL */
  websiteUrl?: string;

  /** Current tenant status */
  status: TenantStatus;

  /** Subscription plan */
  plan: TenantPlan;

  /** Maximum number of users allowed */
  maxUsers?: number;

  /** Current number of users */
  currentUsers: number;

  /** Tenant-specific settings */
  settings?: Record<string, any>;

  /** Additional metadata */
  metadata?: Record<string, any>;

  /** Tenant creation timestamp */
  createdAt: Date;

  /** Last update timestamp */
  updatedAt: Date;

  /** Subscription expiration date */
  subscriptionExpiresAt?: Date;
}

/**
 * Minimal tenant interface for basic operations
 */
export interface IMinimalTenant {
  /** Unique tenant identifier */
  id: string;

  /** Unique tenant code */
  code: string;

  /** Display name of the tenant */
  name: string;

  /** Current tenant status */
  status: TenantStatus;

  /** Tenant creation timestamp */
  createdAt: Date;

  /** Last update timestamp */
  updatedAt: Date;
}

/**
 * Tenant query parameters interface
 * Defines the structure for tenant listing queries
 */
export interface ITenantQuery {
  /** Page number for pagination (1-based) */
  page?: number;

  /** Number of items per page */
  limit?: number;

  /** Search term for name or code */
  search?: string;

  /** Filter by tenant status */
  status?: TenantStatus;

  /** Field to sort by */
  sortBy?: TenantSortField;

  /** Sort order */
  sortOrder?: TenantSortOrder;
}

/**
 * Tenant query result metadata interface
 */
export interface ITenantQueryMeta {
  /** Current page number */
  page: number;

  /** Items per page */
  limit: number;

  /** Total number of items */
  total: number;

  /** Total number of pages */
  totalPages: number;

  /** Whether there is a next page */
  hasNext: boolean;

  /** Whether there is a previous page */
  hasPrev: boolean;
}

/**
 * Single tenant response interface
 */
export interface ITenantResponse {
  /** Tenant data */
  data: ITenant;
}

/**
 * Tenant list response interface
 */
export interface ITenantListResponse {
  /** Array of tenant data */
  tenants: IMinimalTenant[];

  /** Pagination metadata */
  pagination: ITenantQueryMeta;
}

/**
 * Tenant creation response interface
 */
export interface ITenantCreateResponse {
  /** Created tenant data */
  data: ITenant;
}

/**
 * Tenant update response interface
 */
export interface ITenantUpdateResponse {
  /** Updated tenant data */
  data: ITenant;
}

/**
 * Tenant deletion response interface
 */
export interface ITenantDeleteResponse {
  /** Success message */
  message: string;
}
