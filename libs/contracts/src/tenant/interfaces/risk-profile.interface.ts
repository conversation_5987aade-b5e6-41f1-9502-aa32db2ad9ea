/**
 * Risk Profile Management System Interfaces
 * Defines the contract interfaces for risk profiles, rules, and customer assignments
 */

/**
 * Rule Category Enum - Categories for organizing rules
 */
export enum RuleCategory {
  TRANSACTION = 'TRANSACTION',
  BEHAVIORAL = 'BEHAVIORAL', 
  GEOGRAPHIC = 'GEOGRAPHIC',
  FREQUENCY = 'FREQUENCY',
  CHANNEL = 'CHANNEL',
  COUNTERPARTY = 'COUNTERPARTY',
  ANOMALY = 'ANOMALY'
}

/**
 * Rule Type Enum - Types of rule implementations
 */
export enum RuleType {
  THRESHOLD = 'THRESHOLD',
  CONDITION = 'CONDITION',
  PATTERN = 'PATTERN',
  GEOGRAPHIC = 'GEOGRAPHIC',
  FREQUENCY_BASED = 'FREQUENCY_BASED',
  AMOUNT_BASED = 'AMOUNT_BASED',
  CUSTOM = 'CUSTOM'
}

/**
 * Assignment Type Enum - How customers are assigned to risk profiles
 */
export enum AssignmentType {
  AUTOMATIC = 'AUTOMATIC',
  MANUAL = 'MANUAL',
  INHERITED = 'INHERITED'
}

/**
 * Risk Profile Interface
 * Represents a collection of rules that can be applied to customers
 */
export interface IRiskProfile {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  ruleCategory: RuleCategory;
  isActive: boolean;
  isSystemGenerated: boolean;
  priority: number;
  assignmentPriority: number;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
}

/**
 * Rule Condition Interface
 * Defines the structure for rule conditions
 */
export interface IRuleCondition {
  type: 'AND' | 'OR' | 'NOT';
  conditions: Array<{
    field: string;
    operator: 'EQUALS' | 'NOT_EQUALS' | 'GREATER_THAN' | 'LESS_THAN' | 'GREATER_THAN_OR_EQUAL' | 'LESS_THAN_OR_EQUAL' | 'IN' | 'NOT_IN' | 'CONTAINS' | 'NOT_CONTAINS' | 'STARTS_WITH' | 'ENDS_WITH' | 'REGEX_MATCH';
    value: any;
    metadata?: Record<string, any>;
  }>;
}

/**
 * Rule Action Interface
 * Defines the structure for rule actions
 */
export interface IRuleAction {
  actions: Array<{
    type: 'SET_RISK_LEVEL' | 'GENERATE_ALERT' | 'REQUIRE_ENHANCED_DUE_DILIGENCE' | 'BLOCK_TRANSACTION' | 'FLAG_FOR_REVIEW' | 'SEND_NOTIFICATION' | 'UPDATE_CUSTOMER_STATUS' | 'CUSTOM';
    value?: any;
    severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    message?: string;
    metadata?: Record<string, any>;
  }>;
}

/**
 * Geographic Condition Interface
 * Specialized interface for geographic-based rules
 */
export interface IGeographicCondition {
  countryCodes?: string[]; // ISO 3166-1 alpha-2 codes
  sanctionLists?: string[];
  crossBorderPatterns?: {
    fromCountries?: string[];
    toCountries?: string[];
    restrictedPairs?: Array<{ from: string; to: string }>;
  };
  riskRegions?: string[];
}

/**
 * Frequency Condition Interface
 * Specialized interface for frequency-based rules
 */
export interface IFrequencyCondition {
  timeWindow: {
    value: number;
    unit: 'MINUTES' | 'HOURS' | 'DAYS' | 'WEEKS' | 'MONTHS';
  };
  maxTransactions?: number;
  maxAmount?: number;
  patterns?: {
    velocityThreshold?: number;
    burstDetection?: boolean;
    timeBasedPatterns?: string[];
  };
}

/**
 * Rule Interface
 * Represents an individual rule within a risk profile
 */
export interface IRule {
  id: string;
  tenantId: string;
  riskProfileId: string;
  name: string;
  description?: string;
  ruleType: RuleType;
  ruleCategory: RuleCategory;
  conditions: IRuleCondition;
  actions: IRuleAction;
  thresholds?: Record<string, number>;
  priority: number;
  isActive: boolean;
  effectiveFrom?: Date;
  effectiveUntil?: Date;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
}

/**
 * Customer Risk Profile Assignment Interface
 * Represents the assignment of a customer to a risk profile
 */
export interface ICustomerRiskProfileAssignment {
  id: string;
  tenantId: string;
  customerId: string;
  riskProfileId: string;
  assignmentType: AssignmentType;
  assignmentCriteria?: Record<string, any>;
  assignmentPriority: number;
  allowOverride: boolean;
  assignedAt: Date;
  assignedBy?: string;
  isActive: boolean;
  effectiveFrom?: Date;
  effectiveUntil?: Date;
  metadata?: Record<string, any>;
}

/**
 * Rule Execution History Interface
 * Represents the history of rule executions
 */
export interface IRuleExecutionHistory {
  id: string;
  tenantId: string;
  ruleId: string;
  customerId?: string;
  riskProfileId: string;
  executionContext?: Record<string, any>;
  ruleResult?: Record<string, any>;
  actionsTaken?: Record<string, any>;
  executedAt: Date;
  executionTimeMs?: number;
  priorityUsed?: number;
}

/**
 * Rule Precedence Result Interface
 * Represents the result of rule precedence resolution
 */
export interface IRulePrecedenceResult {
  applicableRules: IRule[];
  conflictingRules: Array<{
    rules: IRule[];
    conflictType: 'PRIORITY' | 'ACTION' | 'CONDITION';
    resolution: 'HIGHEST_PRIORITY' | 'MANUAL_OVERRIDE' | 'SYSTEM_DEFAULT';
    resolvedRule: IRule;
  }>;
  precedenceLog: Array<{
    ruleId: string;
    priority: number;
    assignmentType: AssignmentType;
    selected: boolean;
    reason: string;
  }>;
}

/**
 * Customer Risk Profile Summary Interface
 * Provides a summary of a customer's risk profile assignments and applicable rules
 */
export interface ICustomerRiskProfileSummary {
  customerId: string;
  tenantId: string;
  assignments: ICustomerRiskProfileAssignment[];
  applicableRules: IRule[];
  effectiveRiskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  precedenceResult: IRulePrecedenceResult;
  lastUpdated: Date;
}
