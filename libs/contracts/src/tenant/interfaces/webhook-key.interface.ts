import { KeyEnvironment } from '../enums/key-environment.enum';

/**
 * Webhook Key entity interface representing the database model
 */
export interface WebhookKeyEntity {
	/** Unique identifier for the webhook key */
	id: string;
	/** Tenant ID that owns this webhook key */
	tenantId: string;
	/** User-friendly name for the webhook key */
	name: string;
	/** Environment (LIVE or TEST) */
	environment: KeyEnvironment;
	/** Key prefix for identification */
	keyPrefix: string;
	/** Hashed version of the webhook key */
	keyHash: string;
	/** When the key was last used */
	lastUsedAt: Date | null;
	/** When the key expires (if set) */
	expiresAt: Date | null;
	/** Whether the key is active */
	isActive: boolean;
	/** When the key was created */
	createdAt: Date;
	/** When the key was last updated */
	updatedAt: Date;
}

/**
 * Webhook Key creation data interface
 */
export interface CreateWebhookKeyData {
	/** Tenant ID that will own this webhook key */
	tenantId: string;
	/** User-friendly name for the webhook key */
	name: string;
	/** Environment (LIVE or TEST) */
	environment: KeyEnvironment;
	/** Key prefix for identification */
	keyPrefix: string;
	/** Hashed version of the webhook key */
	keyHash: string;
	/** When the key expires (if set) */
	expiresAt?: Date;
	/** Whether the key is active */
	isActive?: boolean;
}

/**
 * Webhook Key update data interface
 */
export interface UpdateWebhookKeyData {
	/** User-friendly name for the webhook key */
	name?: string;
	/** Key prefix for identification */
	keyPrefix?: string;
	/** Hashed version of the webhook key */
	keyHash?: string;
	/** When the key was last used */
	lastUsedAt?: Date;
	/** When the key expires (if set) */
	expiresAt?: Date;
	/** Whether the key is active */
	isActive?: boolean;
}

/**
 * Webhook Key query filters interface
 */
export interface WebhookKeyFilters {
	/** Filter by tenant ID */
	tenantId: string;
	/** Filter by environment */
	environment?: KeyEnvironment;
	/** Filter by active status */
	isActive?: boolean;
	/** Search in name */
	search?: string;
}
