/* eslint-disable @typescript-eslint/no-namespace */
/**
 * Tenant Domain Namespace
 *
 * This namespace organizes all tenant-related contracts including:
 * - API key management (create, list, rotate)
 * - Webhook key management (create, list, rotate)
 * - Entity interfaces for repository pattern
 * - Onboarding workflows and tasks
 */

import * as TenantDTOs from './dtos';
import * as TenantEnums from './enums';
import * as TenantInterfaces from './interfaces';
import * as TenantSchemas from './schemas';

export namespace Tenant {
  // Enums
  export namespace Enums {
    export import KeyEnvironment = TenantEnums.KeyEnvironment;
    export import KeyType = TenantEnums.KeyType;

    // Onboarding Enums
    export import OnboardingStatus = TenantEnums.OnboardingStatus;
    export import OnboardingTaskStatus = TenantEnums.OnboardingTaskStatus;
    export import OnboardingStepType = TenantEnums.OnboardingStepType;
    export import OnboardingStepStatus = TenantEnums.OnboardingStepStatus;
    export import OnboardingDocumentType = TenantEnums.OnboardingDocumentType;
    export import DocumentVerificationStatus = TenantEnums.DocumentVerificationStatus;
    export import WorkflowStatus = TenantEnums.WorkflowStatus;
    export import TaskStatus = TenantEnums.TaskStatus;
    export import SubscriptionPlan = TenantEnums.SubscriptionPlan;
    export import InstitutionType = TenantEnums.InstitutionType;
    export import OnboardingTaskPriority = TenantEnums.OnboardingTaskPriority;
    export import OnboardingPhase = TenantEnums.OnboardingPhase;
    export import OnboardingTaskCategory = TenantEnums.OnboardingTaskCategory;
  }

  // DTOs
  export namespace DTOs {
    // API Key DTOs
    export import CreateApiKeyRequestDto = TenantDTOs.CreateApiKeyRequestDto;
    export import CreateApiKeyResponseDto = TenantDTOs.CreateApiKeyResponseDto;
    export import ListApiKeysQueryDto = TenantDTOs.ListApiKeysQueryDto;
    export import ListApiKeysResponseDto = TenantDTOs.ListApiKeysResponseDto;
    export import ApiKeyListItemDto = TenantDTOs.ApiKeyListItemDto;
    export import RotateApiKeyRequestDto = TenantDTOs.RotateApiKeyRequestDto;
    export import RotateApiKeyResponseDto = TenantDTOs.RotateApiKeyResponseDto;

    // Webhook Key DTOs
    export import CreateWebhookKeyRequestDto = TenantDTOs.CreateWebhookKeyRequestDto;
    export import CreateWebhookKeyResponseDto = TenantDTOs.CreateWebhookKeyResponseDto;
    export import ListWebhookKeysQueryDto = TenantDTOs.ListWebhookKeysQueryDto;
    export import ListWebhookKeysResponseDto = TenantDTOs.ListWebhookKeysResponseDto;
    export import WebhookKeyListItemDto = TenantDTOs.WebhookKeyListItemDto;
    export import RotateWebhookKeyRequestDto = TenantDTOs.RotateWebhookKeyRequestDto;
    export import RotateWebhookKeyResponseDto = TenantDTOs.RotateWebhookKeyResponseDto;

    // Tenant DTOs
    export import TenantCreateRequestDto = TenantDTOs.TenantCreateRequestDto;
    export import TenantUpdateRequestDto = TenantDTOs.TenantUpdateRequestDto;
    export import TenantQueryRequestDto = TenantDTOs.TenantQueryRequestDto;
    export import TenantStatusUpdateRequestDto = TenantDTOs.TenantStatusUpdateRequestDto;
  }

  // Schemas
  export namespace Schemas {
    // API Key Schemas
    export import CreateApiKeySchema = TenantSchemas.CreateApiKeySchema;
    export import CreateApiKeyResponseSchema = TenantSchemas.CreateApiKeyResponseSchema;
    export import ListApiKeysSchema = TenantSchemas.ListApiKeysSchema;
    export import ApiKeyListItemSchema = TenantSchemas.ApiKeyListItemSchema;
    export import ListApiKeysResponseSchema = TenantSchemas.ListApiKeysResponseSchema;
    export import RotateApiKeySchema = TenantSchemas.RotateApiKeySchema;
    export import RotateApiKeyResponseSchema = TenantSchemas.RotateApiKeyResponseSchema;

    // Webhook Key Schemas
    export import CreateWebhookKeySchema = TenantSchemas.CreateWebhookKeySchema;
    export import CreateWebhookKeyResponseSchema = TenantSchemas.CreateWebhookKeyResponseSchema;
    export import ListWebhookKeysSchema = TenantSchemas.ListWebhookKeysSchema;
    export import WebhookKeyListItemSchema = TenantSchemas.WebhookKeyListItemSchema;
    export import ListWebhookKeysResponseSchema = TenantSchemas.ListWebhookKeysResponseSchema;
    export import RotateWebhookKeySchema = TenantSchemas.RotateWebhookKeySchema;
    export import RotateWebhookKeyResponseSchema = TenantSchemas.RotateWebhookKeyResponseSchema;

    // Tenant Schemas
    export import TenantCreateRequestSchema = TenantSchemas.TenantCreateRequestSchema;
    export import TenantUpdateRequestSchema = TenantSchemas.TenantUpdateRequestSchema;
    export import TenantQueryRequestSchema = TenantSchemas.TenantQueryRequestSchema;
    export import TenantStatusUpdateRequestSchema = TenantSchemas.TenantStatusUpdateRequestSchema;
  }

  // Entity Interfaces
  export namespace Entities {
    export import ApiKeyEntity = TenantInterfaces.ApiKeyEntity;
    export import CreateApiKeyData = TenantInterfaces.CreateApiKeyData;
    export import UpdateApiKeyData = TenantInterfaces.UpdateApiKeyData;
    export import ApiKeyFilters = TenantInterfaces.ApiKeyFilters;
    export import WebhookKeyEntity = TenantInterfaces.WebhookKeyEntity;
    export import CreateWebhookKeyData = TenantInterfaces.CreateWebhookKeyData;
    export import UpdateWebhookKeyData = TenantInterfaces.UpdateWebhookKeyData;
    export import WebhookKeyFilters = TenantInterfaces.WebhookKeyFilters;
  }

  // Onboarding Interfaces
  export namespace Interfaces {
    export import OnboardingTask = TenantInterfaces.OnboardingTask;
  }
}
