import { z } from 'zod';
import { KeyEnvironment } from '../enums/key-environment.enum';

/**
 * Schema for creating a new webhook key
 */
export const CreateWebhookKeySchema = z.object({
  /** User-friendly name for the webhook key */
  name: z.string().min(1, 'Name is required').max(100, 'Name must be 100 characters or less'),
  /** Environment for the webhook key (LIVE or TEST) */
  environment: z.enum(KeyEnvironment),
  /** Optional expiration date for the webhook key */
  expiresAt: z.iso.datetime().optional(),
});

/**
 * Schema for webhook key response data
 */
export const CreateWebhookKeyResponseSchema = z.object({
  /** Unique identifier for the webhook key */
  id: z.string(),
  /** User-friendly name for the webhook key */
  name: z.string(),
  /** Environment (LIVE or TEST) */
  environment: z.enum(KeyEnvironment),
  /** Full webhook key (only returned on creation) */
  key: z.string(),
  /** Key prefix for identification */
  keyPrefix: z.string(),
  /** Whether the key is active */
  isActive: z.boolean(),
  /** When the key was created */
  createdAt: z.iso.datetime(),
  /** When the key expires (if set) */
  expiresAt: z.iso.datetime().optional(),
  /** When the key was last used (if ever) */
  lastUsedAt: z.iso.datetime().optional(),
});
