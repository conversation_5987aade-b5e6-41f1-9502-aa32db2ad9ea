import { z } from 'zod';
import { KeyEnvironment } from '../enums/key-environment.enum';

/**
 * Schema for listing API keys query parameters
 */
export const ListApiKeysSchema = z.object({
  /** Filter by environment */
  environment: z.enum(KeyEnvironment).optional(),
  /** Filter by active status */
  isActive: z.boolean().optional(),
  /** Page number for pagination */
  page: z.number().int().min(1).default(1).optional(),
  /** Number of items per page */
  limit: z.number().int().min(1).max(100).default(20).optional(),
});

/**
 * Schema for API key list item (without full key)
 */
export const ApiKeyListItemSchema = z.object({
  /** Unique identifier for the API key */
  id: z.string(),
  /** User-friendly name for the API key */
  name: z.string(),
  /** Environment (LIVE or TEST) */
  environment: z.enum(KeyEnvironment),
  /** Key prefix for identification */
  keyPrefix: z.string(),
  /** Whether the key is active */
  isActive: z.boolean(),
  /** When the key was created */
  createdAt: z.string().datetime(),
  /** When the key expires (if set) */
  expiresAt: z.string().datetime().optional(),
  /** When the key was last used (if ever) */
  lastUsedAt: z.string().datetime().optional(),
});

/**
 * Schema for API keys list response
 */
export const ListApiKeysResponseSchema = z.object({
  /** Array of API keys */
  apiKeys: z.array(ApiKeyListItemSchema),
  /** Pagination metadata */
  pagination: z.object({
    /** Current page number */
    page: z.number(),
    /** Number of items per page */
    limit: z.number(),
    /** Total number of items */
    total: z.number(),
    /** Total number of pages */
    totalPages: z.number(),
  }),
});
