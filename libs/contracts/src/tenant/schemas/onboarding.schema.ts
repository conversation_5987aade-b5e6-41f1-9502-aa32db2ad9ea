import { z } from 'zod';

/**
 * Schema for initiating a new tenant onboarding workflow
 */
export const OnboardingInitiateRequestSchema = z.object({
  /** Institution name */
  institutionName: z.string().min(1, 'Institution name is required').max(100, 'Institution name must be 100 characters or less'),
  /** Institution code (unique identifier) */
  institutionCode: z.string().min(2, 'Institution code is required').max(20, 'Institution code must be 20 characters or less'),
  /** Primary contact email */
  contactEmail: z.email('Valid email is required'),
  /** Primary contact name */
  contactName: z.string().min(1, 'Contact name is required'),
  /** Contact phone (optional) */
  contactPhone: z.string().optional(),
  /** Selected pricing plan */
  pricingPlan: z.string().min(1, 'Pricing plan is required'),
  /** Additional metadata (optional) */
  metadata: z.record(z.string(), z.any()).optional(),
});

/**
 * Schema for onboarding initiation response
 */
export const OnboardingInitiateResponseSchema = z.object({
  /** Unique identifier for the onboarding workflow */
  workflowId: z.string(),
  /** Institution name */
  institutionName: z.string(),
  /** Institution code */
  institutionCode: z.string(),
  /** Current workflow status */
  status: z.string(),
  /** Timestamp when the workflow was created */
  createdAt: z.string().datetime(),
  /** Tasks required for completion */
  tasks: z.array(
    z.object({
      id: z.string(),
      name: z.string(),
      description: z.string(),
      status: z.string(),
      isRequired: z.boolean(),
    }),
  ),
});

/**
 * Schema for onboarding workflow status response
 */
export const OnboardingWorkflowStatusResponseSchema = z.object({
  /** Unique identifier for the workflow */
  workflowId: z.string(),
  /** Institution name */
  institutionName: z.string(),
  /** Institution code */
  institutionCode: z.string(),
  /** Current workflow status */
  status: z.string(),
  /** Progress percentage (0-100) */
  progressPercentage: z.number().min(0).max(100),
  /** Timestamp when the workflow was created */
  createdAt: z.string().datetime(),
  /** Timestamp when the workflow was last updated */
  updatedAt: z.string().datetime(),
  /** Timestamp when the workflow was completed (if applicable) */
  completedAt: z.string().datetime().optional(),
});

/**
 * Schema for task update request
 */
export const TaskUpdateRequestSchema = z.object({
  /** New status for the task */
  status: z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED']),
  /** Notes related to the task update (optional) */
  notes: z.string().optional(),
});

/**
 * Schema for task response
 */
export const TaskResponseSchema = z.object({
  /** Task identifier */
  id: z.string(),
  /** Task name */
  name: z.string(),
  /** Task description */
  description: z.string(),
  /** Current task status */
  status: z.string(),
  /** Whether the task is required for completion */
  isRequired: z.boolean(),
  /** Task completion order (if applicable) */
  order: z.number().optional(),
  /** Timestamp when the task was created */
  createdAt: z.string().datetime(),
  /** Timestamp when the task was last updated */
  updatedAt: z.string().datetime(),
  /** Timestamp when the task was completed (if applicable) */
  completedAt: z.string().datetime().optional(),
  /** Notes related to the task */
  notes: z.string().optional(),
});

/**
 * Schema for tasks list response
 */
export const TasksListResponseSchema = z.object({
  /** List of tasks */
  tasks: z.array(TaskResponseSchema),
  /** Total number of tasks */
  totalTasks: z.number(),
  /** Number of completed tasks */
  completedTasks: z.number(),
  /** Overall progress percentage */
  progressPercentage: z.number().min(0).max(100),
});

/**
 * Schema for completing the onboarding workflow
 */
export const CompleteOnboardingRequestSchema = z.object({
  /** Notes about the completion (optional) */
  notes: z.string().optional(),
  /** Whether to automatically create API credentials */
  createCredentials: z.boolean().optional().default(true),
});

/**
 * Schema for onboarding completion response
 */
export const CompleteOnboardingResponseSchema = z.object({
  /** Workflow identifier */
  workflowId: z.string(),
  /** Institution name */
  institutionName: z.string(),
  /** Institution code */
  institutionCode: z.string(),
  /** Tenant identifier created as result of onboarding */
  tenantId: z.string(),
  /** Completion timestamp */
  completedAt: z.string().datetime(),
  /** API credentials if requested */
  credentials: z
    .object({
      apiKeys: z.array(
        z.object({
          keyPrefix: z.string(),
          environment: z.string(),
        }),
      ),
      webhookKeys: z.array(
        z.object({
          keyPrefix: z.string(),
          environment: z.string(),
        }),
      ),
    })
    .optional(),
});

/**
 * Schema for API credentials response
 */
export const ApiCredentialsResponseSchema = z.object({
  /** Tenant identifier */
  tenantId: z.string(),
  /** API keys */
  apiKeys: z.array(
    z.object({
      id: z.string(),
      name: z.string(),
      keyPrefix: z.string(),
      environment: z.string(),
      isActive: z.boolean(),
      createdAt: z.string().datetime(),
      expiresAt: z.string().datetime().optional(),
      lastUsedAt: z.string().datetime().optional(),
    }),
  ),
  /** Webhook keys */
  webhookKeys: z.array(
    z.object({
      id: z.string(),
      name: z.string(),
      keyPrefix: z.string(),
      environment: z.string(),
      isActive: z.boolean(),
      createdAt: z.string().datetime(),
      expiresAt: z.string().datetime().optional(),
      lastUsedAt: z.string().datetime().optional(),
    }),
  ),
});

/**
 * Schema for regenerating credentials request
 */
export const RegenerateCredentialsRequestSchema = z.object({
  /** Type of credential to regenerate */
  type: z.enum(['API_KEY', 'WEBHOOK_KEY']),
  /** ID of the specific credential to regenerate */
  credentialId: z.string().min(1, 'Credential ID is required'),
});

/**
 * Schema for environment switch request
 */
export const EnvironmentSwitchRequestSchema = z.object({
  /** Notes about the environment switch (optional) */
  notes: z.string().optional(),
  /** Whether to automatically create API credentials for the new environment */
  createCredentials: z.boolean().optional().default(true),
});

/**
 * Schema for environment switch response
 */
export const EnvironmentSwitchResponseSchema = z.object({
  /** Workflow identifier */
  workflowId: z.string(),
  /** Environment that was switched to */
  environment: z.enum(['test', 'live']),
  /** Switch timestamp */
  switchedAt: z.string().datetime(),
  /** API credentials if requested */
  credentials: z
    .object({
      apiKeys: z.array(
        z.object({
          id: z.string(),
          keyPrefix: z.string(),
          environment: z.string(),
        }),
      ),
      webhookKeys: z.array(
        z.object({
          id: z.string(),
          keyPrefix: z.string(),
          environment: z.string(),
        }),
      ),
    })
    .optional(),
});
