import { z } from 'zod';
import { KeyEnvironment } from '../enums/key-environment.enum';

/**
 * Schema for creating a new API key
 */
export const CreateApiKeySchema = z.object({
  /** User-friendly name for the API key */
  name: z.string().min(1, 'Name is required').max(100, 'Name must be 100 characters or less'),
  /** Environment for the API key (LIVE or TEST) */
  environment: z.enum(KeyEnvironment),
  /** Optional expiration date for the API key */
  expiresAt: z.iso.datetime().optional(),
});

/**
 * Schema for API key response data
 */
export const CreateApiKeyResponseSchema = z.object({
  /** Unique identifier for the API key */
  id: z.string(),
  /** User-friendly name for the API key */
  name: z.string(),
  /** Environment (LIVE or TEST) */
  environment: z.enum(KeyEnvironment),
  /** Full API key (only returned on creation) */
  key: z.string(),
  /** Key prefix for identification */
  keyPrefix: z.string(),
  /** Whether the key is active */
  isActive: z.boolean(),
  /** When the key was created */
  createdAt: z.iso.datetime(),
  /** When the key expires (if set) */
  expiresAt: z.iso.datetime().optional(),
  /** When the key was last used (if ever) */
  lastUsedAt: z.iso.datetime().optional(),
});
