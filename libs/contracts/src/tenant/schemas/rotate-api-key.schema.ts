import { z } from 'zod';
import { KeyEnvironment } from '../enums/key-environment.enum';

/**
 * Schema for rotating an API key
 */
export const RotateApiKeySchema = z.object({
  /** Optional new expiration date for the rotated key */
  expiresAt: z.iso.datetime().optional(),
});

/**
 * Schema for API key rotation response
 */
export const RotateApiKeyResponseSchema = z.object({
  /** Unique identifier for the API key */
  id: z.string(),
  /** User-friendly name for the API key */
  name: z.string(),
  /** Environment (LIVE or TEST) */
  environment: z.nativeEnum(KeyEnvironment),
  /** New API key (only returned on rotation) */
  key: z.string(),
  /** Key prefix for identification */
  keyPrefix: z.string(),
  /** Whether the key is active */
  isActive: z.boolean(),
  /** When the key was created */
  createdAt: z.iso.datetime(),
  /** When the key was rotated */
  rotatedAt: z.iso.datetime(),
  /** When the key expires (if set) */
  expiresAt: z.iso.datetime().optional(),
});
