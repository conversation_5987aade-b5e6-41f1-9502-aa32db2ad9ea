import { z } from 'zod';
import { KeyEnvironment } from '../enums/key-environment.enum';

/**
 * Schema for listing webhook keys query parameters
 */
export const ListWebhookKeysSchema = z.object({
  /** Filter by environment */
  environment: z.enum(KeyEnvironment).optional(),
  /** Filter by active status */
  isActive: z.boolean().optional(),
  /** Page number for pagination */
  page: z.number().int().min(1).default(1).optional(),
  /** Number of items per page */
  limit: z.number().int().min(1).max(100).default(20).optional(),
});

/**
 * Schema for webhook key list item (without full key)
 */
export const WebhookKeyListItemSchema = z.object({
  /** Unique identifier for the webhook key */
  id: z.string(),
  /** User-friendly name for the webhook key */
  name: z.string(),
  /** Environment (LIVE or TEST) */
  environment: z.enum(KeyEnvironment),
  /** Key prefix for identification */
  keyPrefix: z.string(),
  /** Whether the key is active */
  isActive: z.boolean(),
  /** When the key was created */
  createdAt: z.iso.datetime(),
  /** When the key expires (if set) */
  expiresAt: z.iso.datetime().optional(),
  /** When the key was last used (if ever) */
  lastUsedAt: z.iso.datetime().optional(),
});

/**
 * Schema for webhook keys list response
 */
export const ListWebhookKeysResponseSchema = z.object({
  /** Array of webhook keys */
  webhookKeys: z.array(WebhookKeyListItemSchema),
  /** Pagination metadata */
  pagination: z.object({
    /** Current page number */
    page: z.number(),
    /** Number of items per page */
    limit: z.number(),
    /** Total number of items */
    total: z.number(),
    /** Total number of pages */
    totalPages: z.number(),
  }),
});
