import { z } from 'zod';
import { TENANT_PLAN_VALUES, TENANT_STATUS_VALUES } from '../enums';
import { TenantIdSchema } from '../../common/schemas/cuid.schema';

/**
 * Tenant creation request schema (snake_case for external API)
 * Validates tenant creation data from external clients
 */
export const TenantCreateRequestSchema = z.object({
  /** Display name of the tenant */
  name: z.string().min(2, 'Name must be at least 2 characters long').max(100, 'Name must not exceed 100 characters').trim(),

  /** Unique tenant code */
  code: z
    .string()
    .min(2, 'Code must be at least 2 characters long')
    .max(50, 'Code must not exceed 50 characters')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Code can only contain letters, numbers, underscores, and hyphens')
    .toLowerCase(),

  /** Optional tenant description */
  description: z.string().max(500, 'Description must not exceed 500 characters').trim().optional(),

  /** Optional tenant domain */
  domain: z
    .string()
    .regex(/^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/, 'Invalid domain format')
    .optional(),

  /** Optional URL to tenant logo */
  logo_url: z.string().url().optional(),

  /** Optional tenant website URL */
  website_url: z.string().url().optional(),

  /** Optional subscription plan */
  plan: z.enum(['basic', 'professional', 'enterprise']).optional().default('basic'),

  /** Optional maximum number of users */
  max_users: z.number().int().positive('Max users must be a positive number').optional(),

  /** Optional tenant-specific settings */
  settings: z.record(z.string(), z.any()).optional(),

  /** Optional additional metadata */
  metadata: z.record(z.string(), z.any()).optional(),
});

/**
 * Tenant query request schema (snake_case for external API)
 * Validates tenant listing query parameters from external clients
 */
export const TenantQueryRequestSchema = z.object({
  /** Page number for pagination (1-based) */
  page: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 1))
    .pipe(z.number().int().min(1)),

  /** Number of items per page */
  limit: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 10))
    .pipe(z.number().int().min(1).max(100)),

  /** Search term for name or code */
  search: z.string().optional(),

  /** Filter by tenant status (lowercase in API) */
  status: z.enum(['active', 'inactive', 'suspended']).optional(),

  /** Field to sort by (snake_case in API) */
  sort_by: z.enum(['name', 'code', 'created_at', 'updated_at']).optional().default('created_at'),

  /** Sort order */
  sort_order: z.enum(['asc', 'desc']).optional().default('desc'),
});

/**
 * Pagination metadata schema
 */
export const TenantQueryMetaSchema = z.object({
  /** Current page number */
  page: z.number().int().min(1),

  /** Items per page */
  limit: z.number().int().min(1).max(100),

  /** Total number of items */
  total: z.number().int().min(0),

  /** Total number of pages */
  totalPages: z.number().int().min(0),

  /** Whether there is a next page */
  hasNext: z.boolean(),

  /** Whether there is a previous page */
  hasPrev: z.boolean(),
});

/**
 * Tenant response schema (snake_case for external API)
 * Used for returning tenant data to external clients
 */
export const TenantResponseSchema = z.object({
  /** Unique tenant identifier */
  id: TenantIdSchema,

  /** Unique tenant code */
  code: z.string(),

  /** Display name of the tenant */
  name: z.string(),

  /** Optional tenant description */
  description: z.string().nullable().optional(),

  /** Tenant domain */
  domain: z.string().nullable().optional(),

  /** URL to tenant logo */
  logoUrl: z.url().nullable().optional(),

  /** Tenant website URL */
  websiteUrl: z.url().nullable().optional(),

  /** Current tenant status */
  status: z.enum(TENANT_STATUS_VALUES),

  /** Subscription plan */
  plan: z.enum(TENANT_PLAN_VALUES),

  /** Maximum number of users allowed */
  max_users: z.number().int().positive().nullable().optional(),

  /** Current number of users */
  current_users: z.number().int().min(0),

  /** Tenant-specific settings */
  settings: z.record(z.string(), z.any()).nullable().optional(),

  /** Additional metadata */
  metadata: z.record(z.string(), z.any()).nullable().optional(),

  /** Tenant creation timestamp */
  createdAt: z.iso.datetime(),

  /** Last update timestamp */
  updatedAt: z.iso.datetime(),

  /** Subscription expiration date */
  subscriptionExpiresAtt: z.iso.datetime().nullable().optional(),
});

/**
 * Minimal tenant response schema for list operations
 */
export const MinimalTenantResponseSchema = z.object({
  /** Unique tenant identifier */
  id: TenantIdSchema,

  /** Unique tenant code */
  code: z.string(),

  /** Display name of the tenant */
  name: z.string(),

  /** Current tenant status */
  status: z.enum(TENANT_STATUS_VALUES),

  /** Tenant-specific settings */
  settings: z.record(z.string(), z.any()).nullable().optional(),

  /** Tenant creation timestamp */
  createdAt: z.iso.datetime(),

  /** Last update timestamp */
  updatedAt: z.iso.datetime(),
});

/**
 * Tenant list response schema
 */
export const TenantListResponseSchema = z.object({
  /** Array of tenant data */
  tenants: z.array(MinimalTenantResponseSchema),

  /** Pagination metadata */
  pagination: TenantQueryMetaSchema,
});

/**
 * Single tenant response schema
 */
export const SingleTenantResponseSchema = z.object({
  /** Tenant data */
  data: TenantResponseSchema,
});

/**
 * Tenant update request schema (snake_case for external API)
 * Validates tenant update data from external clients
 */
export const TenantUpdateRequestSchema = z.object({
  /** Display name of the tenant */
  name: z.string().min(2, 'Name must be at least 2 characters long').max(100, 'Name must not exceed 100 characters').trim().optional(),

  /** Optional tenant description */
  description: z.string().max(500, 'Description must not exceed 500 characters').trim().optional(),

  /** Optional tenant domain */
  domain: z
    .string()
    .regex(/^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/, 'Invalid domain format')
    .optional(),

  /** Optional URL to tenant logo */
  logo_url: z.string().url().optional(),

  /** Optional tenant website URL */
  website_url: z.string().url().optional(),

  /** Optional subscription plan */
  plan: z.enum(['basic', 'professional', 'enterprise']).optional(),

  /** Optional maximum number of users */
  max_users: z.number().int().positive('Max users must be a positive number').optional(),

  /** Optional tenant-specific settings */
  settings: z.record(z.string(), z.any()).optional(),

  /** Optional additional metadata */
  metadata: z.record(z.string(), z.any()).optional(),
});

/**
 * Tenant status update request schema
 */
export const TenantStatusUpdateRequestSchema = z.object({
  /** New tenant status */
  status: z.enum(['active', 'inactive', 'suspended']),
});
