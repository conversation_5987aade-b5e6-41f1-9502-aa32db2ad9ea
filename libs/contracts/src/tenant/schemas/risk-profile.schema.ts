import { z } from 'zod';
import { AssignmentType, RuleCategory, RuleType } from '../interfaces/risk-profile.interface';

/**
 * Validation Schemas for Risk Profile Management System
 */

// Enum Schemas
export const RuleCategorySchema = z.nativeEnum(RuleCategory);
export const RuleTypeSchema = z.nativeEnum(RuleType);
export const AssignmentTypeSchema = z.nativeEnum(AssignmentType);

// ISO 3166-1 alpha-2 country code validation
export const CountryCodeSchema = z
  .string()
  .length(2)
  .regex(/^[A-Z]{2}$/, 'Must be a valid ISO 3166-1 alpha-2 country code');

// Rule Condition Schemas
export const RuleConditionOperatorSchema = z.enum([
  'EQUALS',
  'NOT_EQUALS',
  'GREATER_THAN',
  'LESS_THAN',
  'GREATER_THAN_OR_EQUAL',
  'LESS_THAN_OR_EQUAL',
  'IN',
  'NOT_IN',
  'CONTAINS',
  'NOT_CONTAINS',
  'STARTS_WITH',
  'ENDS_WITH',
  'REGEX_MATCH',
]);

export const SingleConditionSchema = z.object({
  field: z.string().min(1, 'Field name is required'),
  operator: RuleConditionOperatorSchema,
  value: z.any(),
  metadata: z.record(z.string(), z.any()).optional(),
});

export const RuleConditionSchema: z.ZodType<any> = z.object({
  type: z.enum(['AND', 'OR', 'NOT']),
  conditions: z.array(SingleConditionSchema),
});

// Rule Action Schemas
export const RuleActionTypeSchema = z.enum([
  'SET_RISK_LEVEL',
  'GENERATE_ALERT',
  'REQUIRE_ENHANCED_DUE_DILIGENCE',
  'BLOCK_TRANSACTION',
  'FLAG_FOR_REVIEW',
  'SEND_NOTIFICATION',
  'UPDATE_CUSTOMER_STATUS',
  'CUSTOM',
]);

export const RuleActionSeveritySchema = z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']);

export const SingleActionSchema = z.object({
  type: RuleActionTypeSchema,
  value: z.any().optional(),
  severity: RuleActionSeveritySchema.optional(),
  message: z.string().optional(),
  metadata: z.record(z.string(), z.any()).optional(),
});

export const RuleActionSchema = z.object({
  actions: z.array(SingleActionSchema).min(1, 'At least one action is required'),
});

// Geographic Condition Schema
export const GeographicConditionSchema = z.object({
  countryCodes: z.array(CountryCodeSchema).optional(),
  sanctionLists: z.array(z.string()).optional(),
  crossBorderPatterns: z
    .object({
      fromCountries: z.array(CountryCodeSchema).optional(),
      toCountries: z.array(CountryCodeSchema).optional(),
      restrictedPairs: z
        .array(
          z.object({
            from: CountryCodeSchema,
            to: CountryCodeSchema,
          }),
        )
        .optional(),
    })
    .optional(),
  riskRegions: z.array(z.string()).optional(),
});

// Frequency Condition Schema
export const FrequencyConditionSchema = z.object({
  timeWindow: z.object({
    value: z.number().positive('Time window value must be positive'),
    unit: z.enum(['MINUTES', 'HOURS', 'DAYS', 'WEEKS', 'MONTHS']),
  }),
  maxTransactions: z.number().positive().optional(),
  maxAmount: z.number().positive().optional(),
  patterns: z
    .object({
      velocityThreshold: z.number().positive().optional(),
      burstDetection: z.boolean().optional(),
      timeBasedPatterns: z.array(z.string()).optional(),
    })
    .optional(),
});

// Risk Profile Schemas
export const CreateRiskProfileRequestSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name must be 255 characters or less'),
  description: z.string().max(1000, 'Description must be 1000 characters or less').optional(),
  ruleCategory: RuleCategorySchema,
  priority: z.number().int().min(0, 'Priority must be non-negative').default(0),
  assignmentPriority: z.number().int().min(0, 'Assignment priority must be non-negative').default(0),
  metadata: z.record(z.string(), z.any()).optional(),
});

export const UpdateRiskProfileRequestSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name must be 255 characters or less').optional(),
  description: z.string().max(1000, 'Description must be 1000 characters or less').optional(),
  priority: z.number().int().min(0, 'Priority must be non-negative').optional(),
  assignmentPriority: z.number().int().min(0, 'Assignment priority must be non-negative').optional(),
  isActive: z.boolean().optional(),
  metadata: z.record(z.string(), z.any()).optional(),
});

// Rule Schemas
export const CreateRuleRequestSchema = z
  .object({
    name: z.string().min(1, 'Name is required').max(255, 'Name must be 255 characters or less'),
    description: z.string().max(1000, 'Description must be 1000 characters or less').optional(),
    ruleType: RuleTypeSchema,
    ruleCategory: RuleCategorySchema,
    conditions: RuleConditionSchema,
    actions: RuleActionSchema,
    thresholds: z.record(z.string(), z.number()).optional(),
    priority: z.number().int().min(0, 'Priority must be non-negative').default(0),
    effectiveFrom: z.string().datetime().optional(),
    effectiveUntil: z.string().datetime().optional(),
    metadata: z.record(z.string(), z.any()).optional(),
  })
  .refine(
    (data) => {
      if (data.effectiveFrom && data.effectiveUntil) {
        return new Date(data.effectiveFrom) < new Date(data.effectiveUntil);
      }
      return true;
    },
    {
      message: 'Effective from date must be before effective until date',
      path: ['effectiveUntil'],
    },
  );

export const UpdateRuleRequestSchema = z
  .object({
    name: z.string().min(1, 'Name is required').max(255, 'Name must be 255 characters or less').optional(),
    description: z.string().max(1000, 'Description must be 1000 characters or less').optional(),
    conditions: RuleConditionSchema.optional(),
    actions: RuleActionSchema.optional(),
    thresholds: z.record(z.string(), z.number()).optional(),
    priority: z.number().int().min(0, 'Priority must be non-negative').optional(),
    isActive: z.boolean().optional(),
    effectiveFrom: z.string().datetime().optional(),
    effectiveUntil: z.string().datetime().optional(),
    metadata: z.record(z.string(), z.any()).optional(),
  })
  .refine(
    (data) => {
      if (data.effectiveFrom && data.effectiveUntil) {
        return new Date(data.effectiveFrom) < new Date(data.effectiveUntil);
      }
      return true;
    },
    {
      message: 'Effective from date must be before effective until date',
      path: ['effectiveUntil'],
    },
  );

// Customer Assignment Schemas
export const AssignCustomerToRiskProfileRequestSchema = z
  .object({
    riskProfileIds: z.array(z.string().min(1, 'Risk profile ID is required')).min(1, 'At least one risk profile ID is required'),
    assignmentType: AssignmentTypeSchema.default(AssignmentType.MANUAL),
    assignmentCriteria: z.record(z.string(), z.any()).optional(),
    assignmentPriority: z.number().int().min(0, 'Assignment priority must be non-negative').default(0),
    allowOverride: z.boolean().default(false),
    effectiveFrom: z.string().datetime().optional(),
    effectiveUntil: z.string().datetime().optional(),
    metadata: z.record(z.string(), z.any()).optional(),
  })
  .refine(
    (data) => {
      if (data.effectiveFrom && data.effectiveUntil) {
        return new Date(data.effectiveFrom) < new Date(data.effectiveUntil);
      }
      return true;
    },
    {
      message: 'Effective from date must be before effective until date',
      path: ['effectiveUntil'],
    },
  );

export const UpdateCustomerRiskProfileAssignmentRequestSchema = z
  .object({
    assignmentPriority: z.number().int().min(0, 'Assignment priority must be non-negative').optional(),
    allowOverride: z.boolean().optional(),
    isActive: z.boolean().optional(),
    effectiveFrom: z.string().datetime().optional(),
    effectiveUntil: z.string().datetime().optional(),
    metadata: z.record(z.string(), z.any()).optional(),
  })
  .refine(
    (data) => {
      if (data.effectiveFrom && data.effectiveUntil) {
        return new Date(data.effectiveFrom) < new Date(data.effectiveUntil);
      }
      return true;
    },
    {
      message: 'Effective from date must be before effective until date',
      path: ['effectiveUntil'],
    },
  );

// Auto Assignment Schema
export const AutoAssignCustomersRequestSchema = z.object({
  criteria: z.object({
    riskLevel: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).optional(),
    customerStatus: z.array(z.string()).optional(),
    country: z.array(CountryCodeSchema).optional(),
    customFilters: z.record(z.string(), z.any()).optional(),
  }),
  dryRun: z.boolean().default(false),
});

// Query Schemas
export const RiskProfileQuerySchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  search: z.string().optional(),
  ruleCategory: RuleCategorySchema.optional(),
  isActive: z.boolean().optional(),
  isSystemGenerated: z.boolean().optional(),
  sortBy: z.enum(['name', 'createdAt', 'updatedAt', 'priority', 'assignmentPriority']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export const RuleQuerySchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  search: z.string().optional(),
  ruleType: RuleTypeSchema.optional(),
  ruleCategory: RuleCategorySchema.optional(),
  isActive: z.boolean().optional(),
  sortBy: z.enum(['name', 'createdAt', 'updatedAt', 'priority']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export const CustomerAssignmentQuerySchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  assignmentType: AssignmentTypeSchema.optional(),
  isActive: z.boolean().optional(),
  sortBy: z.enum(['assignedAt', 'assignmentPriority']).default('assignedAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});
