/* eslint-disable @typescript-eslint/no-namespace */
/**
 * Shared namespace organization
 * Organizes all shared contracts into DTOs, Interfaces, and Schemas namespaces
 */

import * as SharedDTOs from './dtos';
import * as SharedInterfaces from './interfaces';
import * as SharedSchemas from './schemas';

export namespace Shared {
  export namespace DTOs {
    // API Response types
    export type ApiResponseMeta = SharedDTOs.ApiResponseMeta;
    export type PaginationMeta = SharedDTOs.PaginationMeta;
    export type ErrorDetails = SharedDTOs.ErrorDetails;
    export type StandardSuccessResponse<T> = SharedDTOs.StandardSuccessResponse<T>;
    export type StandardErrorResponse = SharedDTOs.StandardErrorResponse;
    export type PaginatedResponse<T> = SharedDTOs.PaginatedResponse<T>;
    export type HealthCheckResponse = SharedDTOs.HealthCheckResponse;
    export type PaginationQueryDto = SharedDTOs.PaginationQueryDto;
  }

  export namespace Interfaces {
    // Base interfaces
    export type BaseEntity = SharedInterfaces.BaseEntity;
    export type TenantEntity = SharedInterfaces.TenantEntity;
    export type AuditableEntity = SharedInterfaces.AuditableEntity;
    export type SearchableEntity = SharedInterfaces.SearchableEntity;

    // Behavior interfaces
    export type Sortable = SharedInterfaces.Sortable;
    export type Activatable = SharedInterfaces.Activatable;
    export type Expirable = SharedInterfaces.Expirable;
    export type Lockable = SharedInterfaces.Lockable;
    export type Taggable = SharedInterfaces.Taggable;
    export type Categorizable = SharedInterfaces.Categorizable;
    export type Rateable = SharedInterfaces.Rateable;
    export type Commentable = SharedInterfaces.Commentable;
    export type Viewable = SharedInterfaces.Viewable;
    export type Downloadable = SharedInterfaces.Downloadable;
    export type Geolocated = SharedInterfaces.Geolocated;
    export type WithMetadata = SharedInterfaces.WithMetadata;

    // Service interfaces
    export type ServiceResponse<T> = SharedInterfaces.ServiceResponse<T>;
    export type PaginationOptions = SharedInterfaces.PaginationOptions;
    export type SortingOptions = SharedInterfaces.SortingOptions;
    export type FilteringOptions = SharedInterfaces.FilteringOptions;
    export type QueryOptions = SharedInterfaces.QueryOptions;
    export type BulkOperationResult = SharedInterfaces.BulkOperationResult;

    // Event-driven architecture
    export type DomainEvent = SharedInterfaces.DomainEvent;
    export type Command = SharedInterfaces.Command;
    export type Query = SharedInterfaces.Query;
  }

  export namespace Schemas {
    // API Response schemas
    export import ApiResponseMetaSchema = SharedSchemas.ApiResponseMetaSchema;
    export import ErrorDetailsSchema = SharedSchemas.ErrorDetailsSchema;
    export import StandardSuccessResponseSchema = SharedSchemas.StandardSuccessResponseSchema;
    export import StandardErrorResponseSchema = SharedSchemas.StandardErrorResponseSchema;

    // Pagination schemas
    export import PaginationQuerySchema = SharedSchemas.PaginationQuerySchema;
    export import PaginationMetaSchema = SharedSchemas.PaginationMetaSchema;
    export import PaginatedResponseSchema = SharedSchemas.PaginatedResponseSchema;

    // Health Check schemas
    export import HealthCheckResponseSchema = SharedSchemas.HealthCheckResponseSchema;

    // Utility functions
    export import createSuccessResponse = SharedSchemas.createSuccessResponse;
    export import createErrorResponse = SharedSchemas.createErrorResponse;
    export import createPaginatedResponse = SharedSchemas.createPaginatedResponse;
  }
}
