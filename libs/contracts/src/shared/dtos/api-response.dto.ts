/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * API Response DTOs
 * TypeScript interfaces and types for API responses
 */

/**
 * API response metadata type
 * Contains common metadata for all API responses
 */
export interface ApiResponseMeta {
  /** Response timestamp */
  timestamp: string;

  /** Unique request identifier */
  request_id: string;

  /** API version */
  version?: string;

  /** Response processing time in milliseconds */
  processing_time_ms?: number;

  /** Rate limiting information */
  rate_limit?: {
    /** Remaining requests in current window */
    remaining: number;

    /** Rate limit window reset time */
    reset_at: string;

    /** Total requests allowed in window */
    limit: number;
  };
}

/**
 * Error details type
 * Provides detailed error information
 */
export interface ErrorDetails {
  /** Error message */
  message: string;

  /** Error code */
  code?: string;

  /** Field-specific errors */
  field_errors?: Record<string, string[]>;

  /** Additional error context */
  details?: Record<string, any>;

  /** Error timestamp */
  timestamp: string;

  /** Request path where error occurred */
  path?: string;

  /** Stack trace (only in development) */
  stack?: string;
}

/**
 * Standard success response type
 * Generic type for successful API responses
 */
export interface StandardSuccessResponse<T> {
  /** Success indicator */
  success: true;

  /** HTTP status code */
  status_code: number;

  /** Success message */
  message: string;

  /** Response data */
  data: T;

  /** Response metadata */
  meta: ApiResponseMeta;
}

/**
 * Standard error response type
 * Generic type for error API responses
 */
export interface StandardErrorResponse {
  /** Success indicator */
  success: false;

  /** HTTP status code */
  status_code: number;

  /** Error message */
  message: string;

  /** Error details */
  error: ErrorDetails;

  /** Response metadata */
  meta: ApiResponseMeta;
}
