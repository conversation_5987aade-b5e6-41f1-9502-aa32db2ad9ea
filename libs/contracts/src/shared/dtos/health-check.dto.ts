/**
 * Health Check DTOs
 * TypeScript interfaces and types for health check responses
 */

import { ApiResponseMeta } from './api-response.dto';

/**
 * Health check response type
 * Used for service health endpoints
 */
export interface HealthCheckResponse {
  /** Success indicator */
  success: boolean;

  /** HTTP status code */
  status_code: number;

  /** Health status message */
  message: string;

  /** Health check data */
  data: {
    /** Service status */
    status: 'healthy' | 'unhealthy' | 'degraded';

    /** Service uptime in seconds */
    uptime: number;

    /** Service version */
    version: string;

    /** Environment */
    environment: string;

    /** Dependency checks */
    dependencies?: Record<string, {
      /** Dependency status */
      status: 'healthy' | 'unhealthy' | 'unknown';

      /** Response time in milliseconds */
      response_time_ms?: number;

      /** Error message if unhealthy */
      error?: string;
    }>;

    /** System metrics */
    metrics?: {
      /** Memory usage */
      memory_usage?: {
        /** Used memory in bytes */
        used: number;

        /** Total memory in bytes */
        total: number;

        /** Memory usage percentage */
        percentage: number;
      };

      /** CPU usage percentage */
      cpu_usage?: number;
    };
  };

  /** Response metadata */
  meta: ApiResponseMeta;
}
