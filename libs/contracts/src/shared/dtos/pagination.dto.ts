/**
 * Pagination DTOs
 * TypeScript interfaces and types for pagination
 */

import { ApiResponseMeta } from './api-response.dto';

/**
 * Pagination metadata type
 * Used for paginated responses
 */
export interface PaginationMeta {
  /** Total number of items */
  total: number;

  /** Number of items per page */
  limit: number;

  /** Number of items to skip */
  offset: number;

  /** Whether there are more items available */
  hasMore: boolean;

  /** Current page number (1-based) */
  page?: number;

  /** Total number of pages */
  totalPages?: number;
}

/**
 * Pagination query DTO
 * Used for paginated requests
 */
export interface PaginationQueryDto {
  /** Page number (1-based) */
  page?: number;

  /** Number of items per page */
  limit?: number;
}

/**
 * Paginated response type
 * Generic type for paginated API responses
 */
export interface PaginatedResponse<T> {
  /** Success indicator */
  success: true;

  /** HTTP status code */
  statusCode: number;

  /** Success message */
  message: string;

  /** Response data */
  data: {
    /** Array of items */
    items: T[];

    /** Pagination metadata */
    pagination: PaginationMeta;
  };

  /** Response metadata */
  meta: ApiResponseMeta;
}
