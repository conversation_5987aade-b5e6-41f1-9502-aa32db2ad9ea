/**
 * Shared Contracts
 *
 * This module supports both import patterns:
 * - Namespace imports: import { Shared } from '@qeep/contracts'
 * - Direct imports: import { PaginationMeta } from '@qeep/contracts/shared/dtos'
 */

// Export the namespace for cross-module usage
export { Shared } from './namespace';

// Export individual modules for direct imports
export * from './dtos';
export * from './interfaces';
export * from './schemas';

// Re-export commonly used CUID schemas for convenience
export { AuthSessionIdSchema, TenantIdSchema, UserIdSchema } from './schemas/cuid.schema';
