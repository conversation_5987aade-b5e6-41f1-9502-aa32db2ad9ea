import { z } from 'zod';

/**
 * Pagination query schema
 * Used for validating pagination query parameters
 */
export const PaginationQuerySchema = z.object({
  /** Page number for pagination (1-based) */
  page: z
    .string()
    .transform(Number)
    .refine((val) => !isNaN(val) && val > 0, {
      message: 'page must be a number greater than 0',
    })
    .optional(),

  /** Number of items per page */
  limit: z
    .string()
    .transform(Number)
    .refine((val) => !isNaN(val) && val > 0, {
      message: 'limit must be a number greater than 0',
    })
    .optional(),
});

/**
 * Pagination metadata schema
 * Used for paginated responses
 */
export const PaginationMetaSchema = z.object({
  /** Total number of items */
  total: z.number().nonnegative(),

  /** Number of items per page */
  limit: z.number().positive(),

  /** Number of items to skip */
  offset: z.number().nonnegative(),

  /** Whether there are more items available */
  hasMore: z.boolean(),

  /** Current page number (1-based) */
  page: z.number().positive().optional(),

  /** Total number of pages */
  totalPages: z.number().positive().optional(),
});

/**
 * Paginated response schema
 * Generic schema for paginated API responses
 */
export const PaginatedResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z.object({
    /** Success indicator */
    success: z.literal(true),

    /** HTTP status code */
    status_code: z.number().int().min(200).max(299),

    /** Success message */
    message: z.string(),

    /** Response data */
    data: z.object({
      /** Array of items */
      items: z.array(itemSchema),

      /** Pagination metadata */
      pagination: PaginationMetaSchema,
    }),

    /** Response metadata */
    meta: z.object({
      /** Response timestamp */
      timestamp: z.iso.datetime(),

      /** Unique request identifier */
      request_id: z.uuid(),

      /** API version */
      version: z.string().optional(),

      /** Response processing time in milliseconds */
      processing_time_ms: z.number().nonnegative().optional(),

      /** Rate limiting information */
      rate_limit: z
        .object({
          /** Remaining requests in current window */
          remaining: z.number().nonnegative(),

          /** Rate limit window reset time */
          reset_at: z.iso.datetime(),

          /** Total requests allowed in window */
          limit: z.number().positive(),
        })
        .optional(),
    }),
  });

/**
 * Create a paginated response
 */
export function createPaginatedResponse<T>(
  items: T[],
  pagination: z.infer<typeof PaginationMetaSchema>,
  message = 'Request successful',
  statusCode = 200,
  meta?: Partial<z.infer<typeof PaginationMetaSchema>>,
): z.infer<ReturnType<typeof PaginatedResponseSchema<z.ZodType<T>>>> {
  return {
    success: true,
    status_code: statusCode,
    message,
    data: {
      items,
      pagination,
    },
    meta: {
      timestamp: new Date().toISOString(),
      request_id: crypto.randomUUID(),
      ...meta,
    },
  };
}
