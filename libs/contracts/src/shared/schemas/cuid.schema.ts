import { z } from 'zod';

/**
 * CUID validation schemas for consistent ID validation across the application
 */

/**
 * Base CUID validation pattern
 * CUIDs are 24 characters long and use base32 encoding
 */
const CUID_PATTERN = /^[0-9a-z]{24}$/;

/**
 * Prefixed CUID validation pattern
 * Format: prefix_cuid (e.g., usr_clrm1234567890abcdef)
 */
const PREFIXED_CUID_PATTERN = /^[a-z]{3}_[0-9a-z]{24}$/;

/**
 * Generic CUID validation schema
 * Validates a CUID without prefix requirements
 */
export const CuidSchema = z.string().regex(CUID_PATTERN, 'Invalid CUID format');

/**
 * Generic prefixed CUID validation schema
 * Validates a CUID with any 3-letter prefix
 */
export const PrefixedCuidSchema = z.string().regex(PREFIXED_CUID_PATTERN, 'Invalid prefixed CUID format');

/**
 * Create a CUID schema with a specific prefix
 * @param prefix - The expected prefix (e.g., 'usr_', 'rol_')
 * @param entityName - The entity name for error messages (e.g., 'user', 'role')
 */
export function createPrefixedCuidSchema(prefix: string, entityName: string) {
  const prefixPattern = new RegExp(`^${prefix.replace('_', '\\_')}[0-9a-z]{24}$`);
  return z.string().regex(prefixPattern, `Invalid ${entityName} ID format`);
}

/**
 * User ID validation schema
 */
export const UserIdSchema = createPrefixedCuidSchema('usr_', 'user');

/**
 * Role ID validation schema
 */
export const RoleIdSchema = createPrefixedCuidSchema('rol_', 'role');

/**
 * Permission ID validation schema
 */
export const PermissionIdSchema = createPrefixedCuidSchema('per_', 'permission');

/**
 * User Role ID validation schema
 */
export const UserRoleIdSchema = createPrefixedCuidSchema('uro_', 'user role');

/**
 * Role Permission ID validation schema
 */
export const RolePermissionIdSchema = createPrefixedCuidSchema('rpe_', 'role permission');

/**
 * User Permission ID validation schema
 */
export const UserPermissionIdSchema = createPrefixedCuidSchema('upe_', 'user permission');

/**
 * Role Audit ID validation schema
 */
export const RoleAuditIdSchema = createPrefixedCuidSchema('rau_', 'role audit');

/**
 * Auth Session ID validation schema
 */
export const AuthSessionIdSchema = createPrefixedCuidSchema('ses_', 'auth session');

/**
 * User Session ID validation schema
 */
export const UserSessionIdSchema = createPrefixedCuidSchema('use_', 'user session');

/**
 * Auth Event ID validation schema
 */
export const AuthEventIdSchema = createPrefixedCuidSchema('aev_', 'auth event');

/**
 * Auth Token ID validation schema
 */
export const AuthTokenIdSchema = createPrefixedCuidSchema('tok_', 'auth token');

/**
 * Security Settings ID validation schema
 */
export const SecuritySettingsIdSchema = createPrefixedCuidSchema('sec_', 'security settings');

/**
 * Tenant ID validation schema
 */
export const TenantIdSchema = createPrefixedCuidSchema('ten_', 'tenant');

/**
 * Customer ID validation schema
 */
export const CustomerIdSchema = createPrefixedCuidSchema('cus_', 'customer');

/**
 * Optional CUID schemas for nullable fields
 */
export const OptionalUserIdSchema = UserIdSchema.optional();
export const OptionalRoleIdSchema = RoleIdSchema.optional();
export const OptionalPermissionIdSchema = PermissionIdSchema.optional();
export const OptionalTenantIdSchema = TenantIdSchema.optional();
export const OptionalCustomerIdSchema = CustomerIdSchema.optional();

/**
 * Nullable CUID schemas for fields that can be null
 */
export const NullableUserIdSchema = UserIdSchema.nullable();
export const NullableRoleIdSchema = RoleIdSchema.nullable();
export const NullablePermissionIdSchema = PermissionIdSchema.nullable();
export const NullableTenantIdSchema = TenantIdSchema.nullable();
export const NullableCustomerIdSchema = CustomerIdSchema.nullable();

/**
 * Array CUID schemas for multiple IDs
 */
export const UserIdArraySchema = z.array(UserIdSchema);
export const RoleIdArraySchema = z.array(RoleIdSchema);
export const PermissionIdArraySchema = z.array(PermissionIdSchema);
export const TenantIdArraySchema = z.array(TenantIdSchema);
export const CustomerIdArraySchema = z.array(CustomerIdSchema);

/**
 * Utility function to validate if a string matches a specific CUID prefix
 * @param id - The ID to validate
 * @param prefix - The expected prefix
 * @returns boolean indicating if the ID is valid
 */
export function isValidPrefixedCuid(id: string, prefix: string): boolean {
  if (!id || typeof id !== 'string') {
    return false;
  }
  
  const prefixPattern = new RegExp(`^${prefix.replace('_', '\\_')}[0-9a-z]{24}$`);
  return prefixPattern.test(id);
}

/**
 * Utility function to extract prefix from a CUID
 * @param id - The CUID to extract prefix from
 * @returns The prefix or null if invalid
 */
export function extractPrefix(id: string): string | null {
  if (!id || typeof id !== 'string') {
    return null;
  }
  
  const match = id.match(/^([a-z]{3}_)/);
  return match ? match[1] : null;
}

/**
 * Utility function to validate CUID format without prefix
 * @param cuid - The CUID part to validate
 * @returns boolean indicating if the CUID is valid
 */
export function isValidCuid(cuid: string): boolean {
  if (!cuid || typeof cuid !== 'string') {
    return false;
  }
  
  return CUID_PATTERN.test(cuid);
}
