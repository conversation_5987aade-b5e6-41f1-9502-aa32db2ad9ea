import z from 'zod';

/**
 * Email validation schema
 * Validates email format and length
 */
export const EmailSchema = z.email('Please provide a valid email address').max(255, 'Email must not exceed 255 characters').toLowerCase();

/**
 * Name validation schema
 * Validates first and last names
 */
export const NameSchema = z.string().min(1, 'Name is required').max(50, 'Name must not exceed 50 characters').trim();

/**
 * Password validation schema - reusable for all password fields
 */
export const PasswordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters long')
  .max(128, 'Password must not exceed 128 characters')
  .regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
  );
