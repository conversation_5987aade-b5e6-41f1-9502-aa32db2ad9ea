import { z } from 'zod';

/**
 * Health Check Schemas
 * Zod validation schemas for health check responses
 */

/**
 * Health check response schema
 * Used for service health endpoints
 */
export const HealthCheckResponseSchema = z.object({
  /** Success indicator */
  success: z.boolean(),

  /** HTTP status code */
  status_code: z.number().int(),

  /** Health status message */
  message: z.string(),

  /** Health check data */
  data: z.object({
    /** Service status */
    status: z.enum(['healthy', 'unhealthy', 'degraded']),

    /** Service uptime in seconds */
    uptime: z.number().nonnegative(),

    /** Service version */
    version: z.string(),

    /** Environment */
    environment: z.string(),

    /** Dependency checks */
    dependencies: z
      .record(
        z.string(),
        z.object({
          /** Dependency status */
          status: z.enum(['healthy', 'unhealthy', 'unknown']),

          /** Response time in milliseconds */
          response_time_ms: z.number().nonnegative().optional(),

          /** Error message if unhealthy */
          error: z.string().optional(),
        }),
      )
      .optional(),

    /** System metrics */
    metrics: z
      .object({
        /** Memory usage */
        memory_usage: z
          .object({
            /** Used memory in bytes */
            used: z.number().nonnegative(),

            /** Total memory in bytes */
            total: z.number().positive(),

            /** Memory usage percentage */
            percentage: z.number().min(0).max(100),
          })
          .optional(),

        /** CPU usage percentage */
        cpu_usage: z.number().min(0).max(100).optional(),
      })
      .optional(),
  }),

  /** Response metadata */
  meta: z.object({
    /** Response timestamp */
    timestamp: z.iso.datetime(),

    /** Unique request identifier */
    request_id: z.uuid(),

    /** API version */
    version: z.string().optional(),

    /** Response processing time in milliseconds */
    processing_time_ms: z.number().nonnegative().optional(),

    /** Rate limiting information */
    rate_limit: z
      .object({
        /** Remaining requests in current window */
        remaining: z.number().nonnegative(),

        /** Rate limit window reset time */
        reset_at: z.iso.datetime(),

        /** Total requests allowed in window */
        limit: z.number().positive(),
      })
      .optional(),
  }),
});
