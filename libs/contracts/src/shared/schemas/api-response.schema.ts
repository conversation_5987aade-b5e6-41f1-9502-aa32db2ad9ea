import { z } from 'zod';

/**
 * API Response Schemas
 * Zod validation schemas for API responses
 */

/**
 * Standard API response metadata schema
 * Contains common metadata for all API responses
 */
export const ApiResponseMetaSchema = z.object({
  /** Response timestamp */
  timestamp: z.iso.datetime(),

  /** Unique request identifier */
  request_id: z.uuid(),

  /** API version */
  version: z.string().optional(),

  /** Response processing time in milliseconds */
  processing_time_ms: z.number().nonnegative().optional(),

  /** Rate limiting information */
  rate_limit: z
    .object({
      /** Remaining requests in current window */
      remaining: z.number().nonnegative(),

      /** Rate limit window reset time */
      reset_at: z.iso.datetime(),

      /** Total requests allowed in window */
      limit: z.number().positive(),
    })
    .optional(),
});

/**
 * Error details schema
 * Provides detailed error information
 */
export const ErrorDetailsSchema = z.object({
  /** Error message */
  message: z.string(),

  /** Error code */
  code: z.string().optional(),

  /** Field-specific errors */
  field_errors: z.record(z.string(), z.array(z.string())).optional(),

  /** Additional error context */
  details: z.record(z.string(), z.any()).optional(),

  /** Error timestamp */
  timestamp: z.iso.datetime(),

  /** Request path where error occurred */
  path: z.string().optional(),

  /** Stack trace (only in development) */
  stack: z.string().optional(),
});

/**
 * Standard success response schema
 * Generic schema for successful API responses
 */
export const StandardSuccessResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    /** Success indicator */
    success: z.literal(true),

    /** HTTP status code */
    status_code: z.number().int().min(200).max(299),

    /** Success message */
    message: z.string(),

    /** Response data */
    data: dataSchema,

    /** Response metadata */
    meta: ApiResponseMetaSchema,
  });

/**
 * Standard error response schema
 * Generic schema for error API responses
 */
export const StandardErrorResponseSchema = z.object({
  /** Success indicator */
  success: z.literal(false),

  /** HTTP status code */
  status_code: z.number().int().min(400).max(599),

  /** Error message */
  message: z.string(),

  /** Error details */
  error: ErrorDetailsSchema,

  /** Response metadata */
  meta: ApiResponseMetaSchema,
});

/**
 * Utility functions for creating standard responses
 */

/**
 * Create a standard success response
 */
export function createSuccessResponse<T>(
  data: T,
  message = 'Request successful',
  statusCode = 200,
  meta?: Partial<z.infer<typeof ApiResponseMetaSchema>>,
): z.infer<ReturnType<typeof StandardSuccessResponseSchema<z.ZodType<T>>>> {
  return {
    success: true,
    status_code: statusCode,
    message,
    data,
    meta: {
      timestamp: new Date().toISOString(),
      request_id: crypto.randomUUID(),
      ...meta,
    },
  };
}

/**
 * Create a standard error response
 */
export function createErrorResponse(
  message: string,
  statusCode: number,
  error?: Partial<z.infer<typeof ErrorDetailsSchema>>,
  meta?: Partial<z.infer<typeof ApiResponseMetaSchema>>,
): z.infer<typeof StandardErrorResponseSchema> {
  return {
    success: false,
    status_code: statusCode,
    message,
    error: {
      message,
      timestamp: new Date().toISOString(),
      ...error,
    },
    meta: {
      timestamp: new Date().toISOString(),
      request_id: crypto.randomUUID(),
      ...meta,
    },
  };
}
