/**
 * Behavioral interfaces
 * Interfaces that define specific behaviors for entities
 */

/**
 * Sortable interface
 * For entities that can be sorted
 */
export interface Sortable {
  /** Sort order */
  sortOrder?: number;
}

/**
 * Activatable interface
 * For entities that can be activated/deactivated
 */
export interface Activatable {
  /** Whether the entity is active */
  isActive: boolean;
  
  /** Activation timestamp */
  activatedAt?: Date;
  
  /** Deactivation timestamp */
  deactivatedAt?: Date;
}

/**
 * Expirable interface
 * For entities that have expiration dates
 */
export interface Expirable {
  /** Expiration timestamp */
  expiresAt?: Date;
  
  /** Whether the entity is expired */
  isExpired?: boolean;
}

/**
 * Lockable interface
 * For entities that can be locked
 */
export interface Lockable {
  /** Whether the entity is locked */
  isLocked: boolean;
  
  /** Lock timestamp */
  lockedAt?: Date;
  
  /** Lock expiration timestamp */
  lockedUntil?: Date;
  
  /** User who locked the entity */
  lockedBy?: string;
  
  /** Lock reason */
  lockReason?: string;
}

/**
 * Taggable interface
 * For entities that support tagging
 */
export interface Taggable {
  /** Entity tags */
  tags?: string[];
}

/**
 * Categorizable interface
 * For entities that belong to categories
 */
export interface Categorizable {
  /** Category identifier */
  categoryId?: string;
  
  /** Category name */
  categoryName?: string;
}

/**
 * Rateable interface
 * For entities that can be rated
 */
export interface Rateable {
  /** Average rating */
  averageRating?: number;
  
  /** Total number of ratings */
  ratingCount?: number;
}

/**
 * Commentable interface
 * For entities that support comments
 */
export interface Commentable {
  /** Number of comments */
  commentCount?: number;
  
  /** Last comment timestamp */
  lastCommentAt?: Date;
}

/**
 * Viewable interface
 * For entities that track views
 */
export interface Viewable {
  /** View count */
  viewCount?: number;
  
  /** Last viewed timestamp */
  lastViewedAt?: Date;
}

/**
 * Downloadable interface
 * For entities that can be downloaded
 */
export interface Downloadable {
  /** Download count */
  downloadCount?: number;
  
  /** Last downloaded timestamp */
  lastDownloadedAt?: Date;
  
  /** File size in bytes */
  fileSize?: number;
  
  /** MIME type */
  mimeType?: string;
}

/**
 * Geolocation interface
 * For entities with geographic coordinates
 */
export interface Geolocated {
  /** Latitude */
  latitude?: number;
  
  /** Longitude */
  longitude?: number;
  
  /** Address */
  address?: string;
  
  /** City */
  city?: string;
  
  /** State/Province */
  state?: string;
  
  /** Country */
  country?: string;
  
  /** Postal code */
  postalCode?: string;
}

/**
 * Metadata interface
 * For entities with flexible metadata
 */
export interface WithMetadata {
  /** Flexible metadata object */
  metadata?: Record<string, any>;
}
