/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * Event-driven architecture interfaces
 * Interfaces for events, commands, and queries in CQRS/Event Sourcing
 */

/**
 * Event interface
 * Standard event structure for event-driven architecture
 */
export interface DomainEvent {
  /** Event identifier */
  id: string;
  
  /** Event type */
  type: string;
  
  /** Event version */
  version: string;
  
  /** Event timestamp */
  timestamp: Date;
  
  /** Event source */
  source: string;
  
  /** Event data */
  data: Record<string, any>;
  
  /** Event metadata */
  metadata?: Record<string, any>;
  
  /** Correlation ID for tracing */
  correlationId?: string;
  
  /** Causation ID for event sourcing */
  causationId?: string;
}

/**
 * Command interface
 * Standard command structure for CQRS
 */
export interface Command {
  /** Command identifier */
  id: string;
  
  /** Command type */
  type: string;
  
  /** Command timestamp */
  timestamp: Date;
  
  /** User who issued the command */
  userId?: string;
  
  /** Tenant context */
  tenantId?: string;
  
  /** Command data */
  data: Record<string, any>;
  
  /** Command metadata */
  metadata?: Record<string, any>;
  
  /** Correlation ID for tracing */
  correlationId?: string;
}

/**
 * Query interface
 * Standard query structure for CQRS
 */
export interface Query {
  /** Query identifier */
  id: string;
  
  /** Query type */
  type: string;
  
  /** Query timestamp */
  timestamp: Date;
  
  /** User who issued the query */
  userId?: string;
  
  /** Tenant context */
  tenantId?: string;
  
  /** Query parameters */
  parameters: Record<string, any>;
  
  /** Query metadata */
  metadata?: Record<string, any>;
  
  /** Correlation ID for tracing */
  correlationId?: string;
}
