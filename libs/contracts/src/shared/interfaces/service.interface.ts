/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * Service interfaces
 * Interfaces for service operations and responses
 */

/**
 * Service response interface
 * Standard response format for service operations
 */
export interface ServiceResponse<T = any> {
  /** Whether the operation was successful */
  success: boolean;
  
  /** Response data */
  data?: T;
  
  /** Error message if operation failed */
  error?: string;
  
  /** Error code if operation failed */
  errorCode?: string;
  
  /** Additional error details */
  errorDetails?: Record<string, any>;
  
  /** Response metadata */
  metadata?: Record<string, any>;
}

/**
 * Pagination options interface
 * Standard pagination parameters
 */
export interface PaginationOptions {
  /** Number of items per page */
  limit?: number;
  
  /** Number of items to skip */
  offset?: number;
  
  /** Page number (1-based) */
  page?: number;
}

/**
 * Sorting options interface
 * Standard sorting parameters
 */
export interface SortingOptions {
  /** Field to sort by */
  sortBy?: string;
  
  /** Sort direction */
  sortOrder?: 'asc' | 'desc';
}

/**
 * Filtering options interface
 * Standard filtering parameters
 */
export interface FilteringOptions {
  /** Search query */
  search?: string;
  
  /** Date range filter */
  dateRange?: {
    from?: Date;
    to?: Date;
  };
  
  /** Status filter */
  status?: string[];
  
  /** Category filter */
  categories?: string[];
  
  /** Tags filter */
  tags?: string[];
  
  /** Custom filters */
  filters?: Record<string, any>;
}

/**
 * Query options interface
 * Combines pagination, sorting, and filtering
 */
export interface QueryOptions extends PaginationOptions, SortingOptions, FilteringOptions {
  /** Include deleted entities */
  includeDeleted?: boolean;
  
  /** Include inactive entities */
  includeInactive?: boolean;
  
  /** Fields to include in response */
  fields?: string[];
  
  /** Relations to include */
  include?: string[];
}

/**
 * Bulk operation result interface
 * Result of bulk operations
 */
export interface BulkOperationResult {
  /** Total number of items processed */
  total: number;
  
  /** Number of successful operations */
  successful: number;
  
  /** Number of failed operations */
  failed: number;
  
  /** Details of failed operations */
  failures?: Array<{
    id: string;
    error: string;
    errorCode?: string;
  }>;
  
  /** Operation metadata */
  metadata?: Record<string, any>;
}
