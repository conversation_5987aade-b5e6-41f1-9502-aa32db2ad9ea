/**
 * Base entity interfaces
 * Core interfaces for all entities in the system
 */

/**
 * Base entity interface
 * Common properties for all entities in the system
 */
export interface BaseEntity {
  /** Unique identifier */
  id: string;
  
  /** Creation timestamp */
  createdAt: Date;
  
  /** Last update timestamp */
  updatedAt: Date;
  
  /** Soft delete timestamp (null if not deleted) */
  deletedAt?: Date | null;
}

/**
 * Tenant-aware entity interface
 * For entities that belong to a specific tenant
 */
export interface TenantEntity extends BaseEntity {
  /** Tenant identifier */
  tenantId?: string;
  
  /** Tenant code */
  tenantCode?: string;
}

/**
 * Auditable entity interface
 * For entities that require audit trail
 */
export interface AuditableEntity extends BaseEntity {
  /** User who created the entity */
  createdBy?: string;
  
  /** User who last updated the entity */
  updatedBy?: string;
  
  /** User who deleted the entity */
  deletedBy?: string;
  
  /** Version number for optimistic locking */
  version?: number;
}

/**
 * Searchable entity interface
 * For entities that support full-text search
 */
export interface SearchableEntity {
  /** Search vector for full-text search */
  searchVector?: string;
  
  /** Searchable text content */
  searchableText?: string;
}
