import { z } from 'zod';

/**
 * CUID validation schema for 35-character prefixed CUIDs
 * Format: prefix_randomstring (total length: 35 characters)
 */
export const CuidSchema = z
  .string()
  .length(35, 'CUID must be exactly 35 characters long')
  .regex(/^[a-z]{3,4}_[a-z0-9]{28,31}$/, 'Invalid CUID format. Must be prefix_randomstring with total length of 35 characters');

/**
 * Tenant ID validation schema
 * Validates tenant IDs with 'ten_' prefix
 */
export const TenantIdSchema = z
  .string()
  .length(35, 'Tenant ID must be exactly 35 characters long')
  .regex(/^ten_[a-z0-9]{31}$/, 'Invalid tenant ID format. Must start with ten_ and be 35 characters total');

/**
 * Customer ID validation schema
 * Validates customer IDs with 'cus_' prefix
 */
export const CustomerIdSchema = z
  .string()
  .length(35, 'Customer ID must be exactly 35 characters long')
  .regex(/^cus_[a-z0-9]{31}$/, 'Invalid customer ID format. Must start with cus_ and be 35 characters total');

/**
 * API Key ID validation schema
 * Validates API key IDs with 'tak_' prefix
 */
export const ApiKeyIdSchema = z
  .string()
  .length(35, 'API Key ID must be exactly 35 characters long')
  .regex(/^tak_[a-z0-9]{31}$/, 'Invalid API key ID format. Must start with tak_ and be 35 characters total');

/**
 * Webhook Key ID validation schema
 * Validates webhook key IDs with 'twk_' prefix
 */
export const WebhookKeyIdSchema = z
  .string()
  .length(35, 'Webhook Key ID must be exactly 35 characters long')
  .regex(/^twk_[a-z0-9]{31}$/, 'Invalid webhook key ID format. Must start with twk_ and be 35 characters total');

/**
 * Configuration ID validation schema
 * Validates configuration IDs with 'tcf_' prefix
 */
export const ConfigurationIdSchema = z
  .string()
  .length(35, 'Configuration ID must be exactly 35 characters long')
  .regex(/^tcf_[a-z0-9]{31}$/, 'Invalid configuration ID format. Must start with tcf_ and be 35 characters total');

/**
 * Onboarding Session ID validation schema
 * Validates onboarding session IDs with 'ons_' prefix
 */
export const OnboardingSessionIdSchema = z
  .string()
  .length(35, 'Onboarding Session ID must be exactly 35 characters long')
  .regex(/^ons_[a-z0-9]{31}$/, 'Invalid onboarding session ID format. Must start with ons_ and be 35 characters total');

/**
 * Onboarding Step ID validation schema
 * Validates onboarding step IDs with 'ost_' prefix
 */
export const OnboardingStepIdSchema = z
  .string()
  .length(35, 'Onboarding Step ID must be exactly 35 characters long')
  .regex(/^ost_[a-z0-9]{31}$/, 'Invalid onboarding step ID format. Must start with ost_ and be 35 characters total');

/**
 * Document Upload ID validation schema
 * Validates document upload IDs with 'doc_' prefix
 */
export const DocumentUploadIdSchema = z
  .string()
  .length(35, 'Document Upload ID must be exactly 35 characters long')
  .regex(/^doc_[a-z0-9]{31}$/, 'Invalid document upload ID format. Must start with doc_ and be 35 characters total');

/**
 * Onboarding Workflow ID validation schema
 * Validates onboarding workflow IDs with 'onw_' prefix
 */
export const OnboardingWorkflowIdSchema = z
  .string()
  .length(35, 'Onboarding Workflow ID must be exactly 35 characters long')
  .regex(/^onw_[a-z0-9]{31}$/, 'Invalid onboarding workflow ID format. Must start with onw_ and be 35 characters total');
