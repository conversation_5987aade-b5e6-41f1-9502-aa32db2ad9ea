/* eslint-disable @typescript-eslint/no-explicit-any */
import { TenantEntity } from '../../shared/interfaces/base-entity.interface';
import {
  AlertSeverity,
  AlertStatus,
  AlertType,
  CustomerStatus,
  CustomerType,
  KycStatus,
  NoteType,
  RelationshipStatus,
  RelationshipType,
  RiskLevel,
  VerificationStatus,
} from '../enums';

/**
 * Base Customer Interface
 * Represents the core customer entity
 */
export interface ICustomer extends TenantEntity {
  /** Customer type (individual, business, etc.) */
  customerType: CustomerType;

  /** Current customer status */
  status: CustomerStatus;

  /** Risk level assessment */
  riskLevel: RiskLevel;

  /** Core profile data (encrypted JSON) */
  profileData: ICustomerProfileData;

  /** KYC status */
  kycStatus: KycStatus;

  /** KYC data (optional) */
  kycData?: ICustomerKycData;

  /** Risk assessment data (optional) */
  riskAssessment?: ICustomerRiskAssessment;

  /** Related documents */
  documents?: ICustomerDocument[];

  /** Risk scores history */
  riskScores?: ICustomerRiskScore[];

  /** Customer relationships */
  relationships?: ICustomerRelationship[];

  /** Customer alerts */
  alerts?: ICustomerAlert[];

  /** Customer notes */
  notes?: ICustomerNote[];

  /** Customer statistics */
  statistics?: ICustomerStatistics;
}

/**
 * Customer Statistics Interface
 */
export interface ICustomerStatistics {
  /** Total counts */
  totalCustomers: number;
  activeCustomers: number;
  pendingKyc: number;
  highRiskCustomers: number;
  recentCustomers: number;

  /** Breakdown by type */
  customersByType: Record<CustomerType, number>;

  /** Breakdown by risk level */
  customersByRiskLevel: Record<RiskLevel, number>;

  /** Breakdown by status */
  customersByStatus: Record<CustomerStatus, number>;

  /** Breakdown by KYC status */
  customersByKycStatus: Record<KycStatus, number>;
}

/**
 * Customer Query Options Interface
 */
export interface ICustomerQueryOptions {
  /** Pagination */
  page?: number;
  limit?: number;

  /** Filtering */
  search?: string;
  status?: CustomerStatus;
  customerType?: CustomerType;
  riskLevel?: RiskLevel;
  kycStatus?: KycStatus;
  tenantId?: string;

  /** Sorting */
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';

  /** Include relations */
  includeDocuments?: boolean;
  includeRiskScores?: boolean;
  includeRelationships?: boolean;
  includeAlerts?: boolean;
  includeNotes?: boolean;
}

/**
 * Customer KYC Data Interface
 */
export interface ICustomerKycData {
  /** KYC completion date */
  completedAt?: Date;

  /** KYC expiry date */
  expiryDate?: Date;

  /** KYC verification method */
  verificationMethod?: string;

  /** Identity verification results */
  identityVerification?: {
    status: VerificationStatus;
    verifiedAt?: Date;
    verificationProvider?: string;
    verificationReference?: string;
    matchScore?: number;
  };

  /** Address verification results */
  addressVerification?: {
    status: VerificationStatus;
    verifiedAt?: Date;
    verificationProvider?: string;
    verificationReference?: string;
  };

  /** Document verification results */
  documentVerification?: {
    status: VerificationStatus;
    verifiedAt?: Date;
    documentsVerified: string[];
  };

  /** Sanctions screening results */
  sanctionsScreening?: {
    status: VerificationStatus;
    screenedAt?: Date;
    provider?: string;
    matches?: any[];
  };

  /** PEP screening results */
  pepScreening?: {
    status: VerificationStatus;
    screenedAt?: Date;
    provider?: string;
    matches?: any[];
  };

  /** Adverse media screening results */
  adverseMediaScreening?: {
    status: VerificationStatus;
    screenedAt?: Date;
    provider?: string;
    matches?: any[];
  };

  /** Additional KYC data */
  additionalData?: Record<string, any>;
}

/**
 * Customer Document Interface
 */
export interface ICustomerDocument extends TenantEntity {
  /** Customer ID */
  customerId: string;

  /** Document type */
  documentType: DocumentType;

  /** File information */
  fileName: string;
  fileSize: number;
  mimeType: string;

  /** Document metadata */
  metadata?: Record<string, any>;

  /** Storage information */
  storageKey: string;
  encryptionKey?: string;

  /** Verification status */
  verificationStatus: VerificationStatus;
  verifiedAt?: Date;
  verifiedBy?: string;

  /** Upload timestamp */
  uploadedAt: Date;
}

/**
 * Customer Profile Data Interface
 * Contains the core profile information for a customer
 */
export interface ICustomerProfileData {
  // Individual customer fields
  firstName?: string;
  lastName?: string;
  middleName?: string;
  dateOfBirth?: string;
  gender?: 'MALE' | 'FEMALE' | 'OTHER';
  nationality?: string;
  countryOfBirth?: string;
  placeOfBirth?: string;

  // Business customer fields
  businessName?: string;
  businessType?: string;
  businessRegistrationNumber?: string;
  businessRegistrationDate?: string;
  businessCountry?: string;
  businessIndustry?: string;
  businessDescription?: string;

  // Contact information
  email?: string;
  phoneNumber?: string;
  alternatePhoneNumber?: string;

  // Address information
  address?: ICustomerAddress;
  mailingAddress?: ICustomerAddress;

  // Employment/Business information
  occupation?: string;
  employer?: string;
  employmentStatus?: string;
  annualIncome?: number;
  sourceOfFunds?: string[];
  sourceOfWealth?: string[];

  // Additional information
  politicallyExposed?: boolean;
  sanctionsMatch?: boolean;
  adverseMediaMatch?: boolean;
  highRiskCountryExposure?: boolean;

  // Custom fields for tenant-specific data
  customFields?: Record<string, any>;
}

/**
 * Customer Address Interface
 */
export interface ICustomerAddress {
  street1: string;
  street2?: string;
  city: string;
  state?: string;
  postalCode?: string;
  country: string;
  addressType?: 'RESIDENTIAL' | 'BUSINESS' | 'MAILING';
}

/**
 * Customer Relationship Interface
 */
export interface ICustomerRelationship extends TenantEntity {
  /** Primary customer ID */
  primaryCustomerId: string;

  /** Related customer ID */
  relatedCustomerId: string;

  /** Relationship type */
  relationshipType: RelationshipType;

  /** Relationship data */
  relationshipData?: Record<string, any>;

  /** Status and validity */
  status: RelationshipStatus;
  validFrom: Date;
  validTo?: Date;

  /** Related customer information */
  primaryCustomer?: Partial<ICustomer>;
  relatedCustomer?: Partial<ICustomer>;
}

/**
 * Customer Alert Interface
 */
export interface ICustomerAlert extends TenantEntity {
  /** Customer ID */
  customerId: string;

  /** Alert information */
  alertType: AlertType;
  severity: AlertSeverity;
  title: string;
  description: string;

  /** Alert data */
  alertData?: Record<string, any>;

  /** Status and resolution */
  status: AlertStatus;
  resolvedAt?: Date;
  resolvedBy?: string;
  resolution?: string;
}

/**
 * Customer Note Interface
 */
export interface ICustomerNote extends TenantEntity {
  /** Customer ID */
  customerId: string;

  /** Note information */
  noteType: NoteType;
  title: string;
  content: string;

  /** Note metadata */
  isConfidential: boolean;
  tags: string[];

  /** Author information */
  authorId: string;
  authorName: string;
}

/**
 * Customer Risk Assessment Interface
 */
export interface ICustomerRiskAssessment {
  /** Overall risk score */
  overallScore: number;

  /** Risk level */
  riskLevel: RiskLevel;

  /** Risk factors breakdown */
  riskFactors: {
    geographic?: number;
    occupational?: number;
    transactional?: number;
    behavioral?: number;
    regulatory?: number;
    reputational?: number;
  };

  /** Assessment methodology */
  assessmentMethod: string;

  /** Assessment date */
  assessmentDate: Date;

  /** Next review date */
  nextReviewDate?: Date;

  /** Assessment notes */
  notes?: string;

  /** Additional assessment data */
  additionalData?: Record<string, any>;
}

/**
 * Customer Risk Score Interface
 */
export interface ICustomerRiskScore extends TenantEntity {
  /** Customer ID */
  customerId: string;

  /** Risk scoring */
  overallScore: number;
  riskLevel: RiskLevel;

  /** Risk factors breakdown */
  riskFactors: Record<string, any>;

  /** Assessment metadata */
  assessmentDate: Date;
  assessmentMethod: string;
  assessedBy?: string;

  /** Review information */
  nextReviewDate?: Date;
  reviewReason?: string;
}
