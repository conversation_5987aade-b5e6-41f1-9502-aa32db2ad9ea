import { AccountAccessLevel, AccountCurrency, AccountStatus, AccountType } from '../enums/accounts.enums';

/**
 * Core account entity interface
 */
export interface AccountEntity {
  id: string;
  tenantId: string;
  customerId: string;
  accountNumber: string;
  accountType: AccountType;
  accountStatus: AccountStatus;
  currency: AccountCurrency;
  accessLevel: AccountAccessLevel;
  balance: number;
  availableBalance: number;
  overdraftLimit: number;
  interestRate: number;
  minimumBalance: number;
  accountName: string;
  description?: string;
  metadata?: Record<string, unknown>;
  isActive: boolean;
  lastTransactionAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Account creation data interface
 */
export interface CreateAccountData {
  tenantId: string;
  customerId: string;
  accountType: AccountType;
  currency: AccountCurrency;
  accountName: string;
  description?: string;
  initialBalance?: number;
  overdraftLimit?: number;
  interestRate?: number;
  minimumBalance?: number;
  metadata?: Record<string, unknown>;
}

/**
 * Account update data interface
 */
export interface UpdateAccountData {
  accountName?: string;
  description?: string;
  accountStatus?: AccountStatus;
  accessLevel?: AccountAccessLevel;
  overdraftLimit?: number;
  interestRate?: number;
  minimumBalance?: number;
  metadata?: Record<string, unknown>;
  isActive?: boolean;
}

/**
 * Account balance update interface
 */
export interface UpdateAccountBalanceData {
  balance: number;
  availableBalance: number;
  lastTransactionAt: Date;
}

/**
 * Account query filters interface
 */
export interface FindAccountsFilters {
  tenantId: string;
  customerId?: string;
  accountType?: AccountType;
  accountStatus?: AccountStatus;
  currency?: AccountCurrency;
  accessLevel?: AccountAccessLevel;
  isActive?: boolean;
  minBalance?: number;
  maxBalance?: number;
}

/**
 * Account validation result interface
 */
export interface ValidateAccountResult {
  isValid: boolean;
  accountStatus: AccountStatus;
  availableBalance: number;
  currency: AccountCurrency;
  accessLevel: AccountAccessLevel;
  canDebit: boolean;
  canCredit: boolean;
  reason?: string;
}
