/**
 * Customer Alert Enums
 *
 * Defines enums related to customer alerts and notifications
 */

/**
 * Alert Type Enum
 * Defines the types of alerts that can be generated for customers
 */
export enum AlertType {
  KYC_EXPIRY = 'KYC_EXPIRY',
  RISK_LEVEL_CHANGE = 'RISK_LEVEL_CHANGE',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  DOCUMENT_EXPIRY = 'DOCUMENT_EXPIRY',
  COMPLIANCE_VIOLATION = 'COMPLIANCE_VIOLATION',
  DATA_QUALITY_ISSUE = 'DATA_QUALITY_ISSUE',
  RELATIONSHIP_CHANGE = 'R<PERSON><PERSON><PERSON>SH<PERSON>_CHANGE',
  SANCTIONS_MATCH = 'SANCTIONS_MATCH',
  PEP_MATCH = 'PEP_MATCH',
  ADVERSE_MEDIA = 'ADVERSE_MEDIA',
  OTHER = 'OTHER',
}

/**
 * Alert Severity Enum
 * Defines the severity levels of customer alerts
 */
export enum AlertSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

/**
 * Alert Status Enum
 * Defines the status of customer alerts
 */
export enum AlertStatus {
  OPEN = 'OPEN',
  IN_PROGRESS = 'IN_PROGRESS',
  RESOLVED = 'RESOLVED',
  DISMISSED = 'DISMISSED',
  ESCALATED = 'ESCALATED',
}

/**
 * Customer Type Enum
 * Defines the different types of customers in the system
 */
export enum CustomerType {
  INDIVIDUAL = 'INDIVIDUAL',
  BUSINESS = 'BUSINESS',
  TRUST = 'TRUST',
  GOVERNMENT = 'GOVERNMENT',
  NON_PROFIT = 'NON_PROFIT',
}

/**
 * Customer Status Enum
 * Defines the lifecycle status of a customer
 */
export enum CustomerStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
  CLOSED = 'CLOSED',
  PENDING_VERIFICATION = 'PENDING_VERIFICATION',
}

/**
 * Risk Level Enum
 * Defines the risk assessment levels for customers
 */
export enum RiskLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

/**
 * KYC Status Enum
 * Defines the Know Your Customer verification status
 */
export enum KycStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  VERIFIED = 'VERIFIED',
  REJECTED = 'REJECTED',
  EXPIRED = 'EXPIRED',
  REQUIRES_UPDATE = 'REQUIRES_UPDATE',
}

/**
 * Document Type Enum
 * Defines the types of documents that can be uploaded for customers
 */
export enum DocumentType {
  PASSPORT = 'PASSPORT',
  NATIONAL_ID = 'NATIONAL_ID',
  DRIVERS_LICENSE = 'DRIVERS_LICENSE',
  UTILITY_BILL = 'UTILITY_BILL',
  BANK_STATEMENT = 'BANK_STATEMENT',
  BUSINESS_REGISTRATION = 'BUSINESS_REGISTRATION',
  TAX_CERTIFICATE = 'TAX_CERTIFICATE',
  PROOF_OF_ADDRESS = 'PROOF_OF_ADDRESS',
  PROOF_OF_INCOME = 'PROOF_OF_INCOME',
  OTHER = 'OTHER',
}

/**
 * Verification Status Enum
 * Defines the verification status of documents
 */
export enum VerificationStatus {
  PENDING = 'PENDING',
  VERIFIED = 'VERIFIED',
  REJECTED = 'REJECTED',
  EXPIRED = 'EXPIRED',
  REQUIRES_REVIEW = 'REQUIRES_REVIEW',
}

/**
 * Note Type Enum
 * Defines the types of notes that can be added to customers
 */
export enum NoteType {
  GENERAL = 'GENERAL',
  KYC = 'KYC',
  RISK_ASSESSMENT = 'RISK_ASSESSMENT',
  COMPLIANCE = 'COMPLIANCE',
  INVESTIGATION = 'INVESTIGATION',
  CUSTOMER_SERVICE = 'CUSTOMER_SERVICE',
  INTERNAL = 'INTERNAL',
  REGULATORY = 'REGULATORY',
}

/**
 * Relationship Type Enum
 * Defines the types of relationships between customers
 */
export enum RelationshipType {
  SPOUSE = 'SPOUSE',
  PARENT = 'PARENT',
  CHILD = 'CHILD',
  SIBLING = 'SIBLING',
  BUSINESS_PARTNER = 'BUSINESS_PARTNER',
  DIRECTOR = 'DIRECTOR',
  SHAREHOLDER = 'SHAREHOLDER',
  BENEFICIAL_OWNER = 'BENEFICIAL_OWNER',
  AUTHORIZED_SIGNATORY = 'AUTHORIZED_SIGNATORY',
  POWER_OF_ATTORNEY = 'POWER_OF_ATTORNEY',
  OTHER = 'OTHER',
}

/**
 * Relationship Status Enum
 * Defines the status of customer relationships
 */
export enum RelationshipStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  TERMINATED = 'TERMINATED',
  PENDING_VERIFICATION = 'PENDING_VERIFICATION',
}

/**
 * Customer Sort Fields Enum
 * Defines the fields that can be used for sorting customers
 */
export enum CustomerSortField {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  NAME = 'name',
  EMAIL = 'email',
  STATUS = 'status',
  RISK_LEVEL = 'riskLevel',
  KYC_STATUS = 'kycStatus',
  CUSTOMER_TYPE = 'customerType',
}

/**
 * Sort Order Enum
 * Defines the sort order options
 */
export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

/**
 * Customer Sort Field API Enum
 * Defines the snake_case sort field values for API validation
 */
export enum CustomerSortFieldApi {
  CREATED_AT = 'created_at',
  UPDATED_AT = 'updated_at',
  NAME = 'name',
  EMAIL = 'email',
  STATUS = 'status',
  RISK_LEVEL = 'risk_level',
  KYC_STATUS = 'kyc_status',
  CUSTOMER_TYPE = 'customer_type',
}
