import { z } from 'zod';
import { CustomerSortFieldApi, CustomerStatus, CustomerType, KycStatus, RiskLevel, SortOrder } from '../enums';

/**
 * Customer Query Schemas
 * Zod validation schemas for customer query operations
 */

/**
 * Customer Query Schema (snake_case for external API)
 * Used for filtering and pagination when retrieving customers
 */
export const CustomerQuerySchema = z.object({
  /** Page number for pagination (1-based) */
  page: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 1))
    .pipe(z.number().int().min(1)),

  /** Number of items per page */
  limit: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 10))
    .pipe(z.number().int().min(1).max(100)),

  /** Search term for customer data */
  search: z.string().max(255, 'Search term must not exceed 255 characters').optional(),

  /** Filter by customer status */
  status: z.enum(CustomerStatus).optional(),

  /** Filter by customer type */
  customer_type: z.enum(CustomerType).optional(),

  /** Filter by risk level */
  risk_level: z.enum(RiskLevel).optional(),

  /** Filter by KYC status */
  kyc_status: z.enum(KycStatus).optional(),

  /** Filter by tenant ID */
  tenant_id: z.uuid('Invalid tenant ID format').optional(),

  /** Sort field (snake_case values) */
  sort_by: z
    .enum(Object.values(CustomerSortFieldApi) as [string, ...string[]])
    .optional()
    .default(CustomerSortFieldApi.CREATED_AT),

  /** Sort order */
  sort_order: z.enum(SortOrder).optional().default(SortOrder.DESC),
});

/**
 * Customer Search Schema (snake_case for external API)
 * Used for advanced customer search operations
 */
export const CustomerSearchSchema = z.object({
  /** Search term for customer data */
  search_term: z.string().min(1, 'Search term is required').max(255, 'Search term must not exceed 255 characters'),

  /** Filter by tenant ID */
  tenant_id: z.uuid('Invalid tenant ID format').optional(),

  /** Maximum number of results */
  limit: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 50))
    .pipe(z.number().int().min(1).max(100)),

  /** Filter by customer type */
  customer_type: z.enum(CustomerType).optional(),

  /** Filter by customer status */
  status: z.enum(CustomerStatus).optional(),

  /** Filter by risk level */
  risk_level: z.enum(RiskLevel).optional(),
});

/**
 * Customer Statistics Query Schema (snake_case for external API)
 * Used for retrieving customer statistics with optional filtering
 */
export const CustomerStatsQuerySchema = z.object({
  /** Filter by tenant ID */
  tenant_id: z.uuid('Invalid tenant ID format').optional(),

  /** Start date for filtering (ISO 8601 format) */
  date_from: z.iso.datetime('Invalid date format, use ISO 8601').optional(),

  /** End date for filtering (ISO 8601 format) */
  date_to: z.iso.datetime('Invalid date format, use ISO 8601').optional(),

  /** Filter by customer type */
  customer_type: z.enum(CustomerType).optional(),

  /** Filter by risk level */
  risk_level: z.enum(RiskLevel).optional(),
});

/**
 * Customer Request Schemas
 * Zod validation schemas for customer creation and update requests
 */

/**
 * Gender enum for customer profile data
 */
export const GenderEnum = z.enum(['MALE', 'FEMALE', 'OTHER']);

/**
 * Address type enum
 */
export const AddressTypeEnum = z.enum(['RESIDENTIAL', 'BUSINESS', 'MAILING']);

/**
 * Customer Address Request Schema
 * Used for address information in customer requests
 */
export const CustomerAddressRequestSchema = z.object({
  /** Street address line 1 (required) */
  street1: z.string().min(1, 'Street address is required').max(255, 'Street address must not exceed 255 characters'),

  /** Street address line 2 (optional) */
  street2: z.string().max(255, 'Street address line 2 must not exceed 255 characters').optional(),

  /** City (required) */
  city: z.string().min(1, 'City is required').max(100, 'City must not exceed 100 characters'),

  /** State/Province (optional) */
  state: z.string().max(100, 'State must not exceed 100 characters').optional(),

  /** Postal/ZIP code (optional) */
  postalCode: z.string().max(20, 'Postal code must not exceed 20 characters').optional(),

  /** Country (required) */
  country: z.string().min(1, 'Country is required').max(100, 'Country must not exceed 100 characters'),

  /** Address type (optional) */
  addressType: AddressTypeEnum.optional(),
});

/**
 * Customer Profile Data Request Schema
 * Used for customer profile data in create/update requests
 */
export const CustomerProfileDataRequestSchema = z.object({
  // Individual customer fields
  /** First name (optional for updates, required for individual customers) */
  firstName: z.string().max(100, 'First name must not exceed 100 characters').optional(),

  /** Last name (optional for updates, required for individual customers) */
  lastName: z.string().max(100, 'Last name must not exceed 100 characters').optional(),

  /** Middle name (optional) */
  middleName: z.string().max(100, 'Middle name must not exceed 100 characters').optional(),

  /** Date of birth (ISO 8601 format) */
  dateOfBirth: z.iso.datetime('Invalid date format, use ISO 8601').optional(),

  /** Gender */
  gender: GenderEnum.optional(),

  /** Nationality */
  nationality: z.string().max(100, 'Nationality must not exceed 100 characters').optional(),

  /** Country of birth */
  countryOfBirth: z.string().max(100, 'Country of birth must not exceed 100 characters').optional(),

  /** Place of birth */
  placeOfBirth: z.string().max(255, 'Place of birth must not exceed 255 characters').optional(),

  // Business customer fields
  /** Business name (required for business customers) */
  businessName: z.string().max(255, 'Business name must not exceed 255 characters').optional(),

  /** Business type */
  businessType: z.string().max(100, 'Business type must not exceed 100 characters').optional(),

  /** Business registration number */
  businessRegistrationNumber: z.string().max(100, 'Business registration number must not exceed 100 characters').optional(),

  /** Business registration date (ISO 8601 format) */
  businessRegistrationDate: z.iso.datetime('Invalid date format, use ISO 8601').optional(),

  /** Business country */
  businessCountry: z.string().max(100, 'Business country must not exceed 100 characters').optional(),

  /** Business industry */
  businessIndustry: z.string().max(100, 'Business industry must not exceed 100 characters').optional(),

  /** Business description */
  businessDescription: z.string().max(1000, 'Business description must not exceed 1000 characters').optional(),

  // Contact information
  /** Email address */
  email: z.email('Invalid email format').max(255, 'Email must not exceed 255 characters').optional(),

  /** Primary phone number */
  phoneNumber: z.string().max(20, 'Phone number must not exceed 20 characters').optional(),

  /** Alternate phone number */
  alternatePhoneNumber: z.string().max(20, 'Alternate phone number must not exceed 20 characters').optional(),

  // Address information
  /** Primary address */
  address: CustomerAddressRequestSchema.optional(),

  /** Mailing address (if different from primary) */
  mailingAddress: CustomerAddressRequestSchema.optional(),

  // Employment/Business information
  /** Occupation */
  occupation: z.string().max(100, 'Occupation must not exceed 100 characters').optional(),

  /** Employer name */
  employer: z.string().max(255, 'Employer must not exceed 255 characters').optional(),

  /** Employment status */
  employmentStatus: z.string().max(50, 'Employment status must not exceed 50 characters').optional(),

  /** Annual income */
  annualIncome: z.number().nonnegative('Annual income must be non-negative').optional(),

  /** Source of funds */
  sourceOfFunds: z.array(z.string().max(100)).optional(),

  /** Source of wealth */
  sourceOfWealth: z.array(z.string().max(100)).optional(),

  // Risk indicators
  /** Politically exposed person */
  politicallyExposed: z.boolean().optional(),

  /** Sanctions match indicator */
  sanctionsMatch: z.boolean().optional(),

  /** Adverse media match indicator */
  adverseMediaMatch: z.boolean().optional(),

  /** High risk country exposure */
  highRiskCountryExposure: z.boolean().optional(),

  // Custom fields
  /** Custom fields for tenant-specific data */
  customFields: z.record(z.string(), z.any()).optional(),
});

/**
 * Customer Create Request Schema
 * Used for creating new customers
 */
export const CustomerCreateRequestSchema = z.object({
  /** Customer type (required) */
  customerType: z.enum(CustomerType),

  /** Customer profile data (required) */
  profileData: CustomerProfileDataRequestSchema,

  /** Customer status (optional, defaults to PENDING_VERIFICATION) */
  status: z.enum(CustomerStatus).optional(),

  /** Risk level (optional) */
  riskLevel: z.enum(RiskLevel).optional(),

  /** KYC status (optional, defaults to PENDING) */
  kycStatus: z.enum(KycStatus).optional(),

  /** KYC data (optional) */
  kycData: z.record(z.string(), z.any()).optional(),

  /** Risk assessment data (optional) */
  riskAssessment: z.record(z.string(), z.any()).optional(),
});

/**
 * Customer Update Request Schema
 * Used for updating existing customers
 */
export const CustomerUpdateRequestSchema = z.object({
  /** Customer type (optional) */
  customerType: z.enum(CustomerType).optional(),

  /** Customer profile data (optional) */
  profileData: CustomerProfileDataRequestSchema.optional(),

  /** Customer status (optional) */
  status: z.enum(CustomerStatus).optional(),

  /** Risk level (optional) */
  riskLevel: z.enum(RiskLevel).optional(),

  /** KYC status (optional) */
  kycStatus: z.enum(KycStatus).optional(),

  /** KYC data (optional) */
  kycData: z.record(z.string(), z.any()).optional(),

  /** Risk assessment data (optional) */
  riskAssessment: z.record(z.string(), z.any()).optional(),
});

/**
 * Customer Status Update Request Schema
 * Used for updating customer status only
 */
export const CustomerStatusUpdateRequestSchema = z.object({
  /** Customer status (required) */
  status: z.enum(CustomerStatus),

  /** Reason for status change (optional) */
  reason: z.string().max(500, 'Reason must not exceed 500 characters').optional(),
});

/**
 * Customer Risk Level Update Request Schema
 * Used for updating customer risk level
 */
export const CustomerRiskLevelUpdateRequestSchema = z.object({
  /** Risk level (required) */
  riskLevel: z.enum(RiskLevel),

  /** Reason for risk level change (optional) */
  reason: z.string().max(500, 'Reason must not exceed 500 characters').optional(),

  /** Assessment data (optional) */
  assessmentData: z.record(z.string(), z.any()).optional(),
});

/**
 * Customer KYC Status Update Request Schema
 * Used for updating customer KYC status
 */
export const CustomerKycStatusUpdateRequestSchema = z.object({
  /** KYC status (required) */
  kycStatus: z.enum(KycStatus),

  /** Reason for KYC status change (optional) */
  reason: z.string().max(500, 'Reason must not exceed 500 characters').optional(),

  /** KYC data (optional) */
  kycData: z.record(z.string(), z.any()).optional(),
});

import { AlertSeverity, AlertStatus, AlertType, DocumentType, VerificationStatus } from '../enums';

/**
 * Customer Response Schemas
 * Zod validation schemas for customer API responses
 */

/**
 * Customer Address Schema
 */
export const CustomerAddressSchema = z.object({
  street1: z.string(),
  street2: z.string().optional(),
  city: z.string(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string(),
  addressType: z.enum(['RESIDENTIAL', 'BUSINESS', 'MAILING']).optional(),
});

/**
 * Customer Profile Data Schema
 * Contains the core profile information for a customer
 */
export const CustomerProfileDataSchema = z.object({
  // Individual customer fields
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  middleName: z.string().optional(),
  dateOfBirth: z.string().optional(),
  gender: z.enum(['MALE', 'FEMALE', 'OTHER']).optional(),
  nationality: z.string().optional(),
  countryOfBirth: z.string().optional(),
  placeOfBirth: z.string().optional(),

  // Business customer fields
  businessName: z.string().optional(),
  businessType: z.string().optional(),
  businessRegistrationNumber: z.string().optional(),
  businessRegistrationDate: z.string().optional(),
  businessCountry: z.string().optional(),
  businessIndustry: z.string().optional(),
  businessDescription: z.string().optional(),

  // Contact information
  email: z.string().optional(),
  phoneNumber: z.string().optional(),
  alternatePhoneNumber: z.string().optional(),

  // Address information
  address: CustomerAddressSchema.optional(),
  mailingAddress: CustomerAddressSchema.optional(),

  // Employment/Business information
  occupation: z.string().optional(),
  employer: z.string().optional(),
  employmentStatus: z.string().optional(),
  annualIncome: z.number().optional(),
  sourceOfFunds: z.array(z.string()).optional(),
  sourceOfWealth: z.array(z.string()).optional(),

  // Risk indicators
  politicallyExposed: z.boolean().optional(),
  sanctionsMatch: z.boolean().optional(),
  adverseMediaMatch: z.boolean().optional(),
  highRiskCountryExposure: z.boolean().optional(),

  // Custom fields
  customFields: z.record(z.string(), z.any()).optional(),
});

/**
 * Customer KYC Data Schema
 */
export const CustomerKycDataSchema = z.object({
  completedAt: z.date().optional(),
  expiryDate: z.date().optional(),
  verificationMethod: z.string().optional(),
  identityVerification: z
    .object({
      status: z.enum(VerificationStatus),
      verifiedAt: z.date().optional(),
      verificationProvider: z.string().optional(),
      verificationReference: z.string().optional(),
      matchScore: z.number().optional(),
    })
    .optional(),
  addressVerification: z
    .object({
      status: z.enum(VerificationStatus),
      verifiedAt: z.date().optional(),
      verificationProvider: z.string().optional(),
      verificationReference: z.string().optional(),
    })
    .optional(),
  documentVerification: z
    .object({
      status: z.enum(VerificationStatus),
      verifiedAt: z.date().optional(),
      documentsVerified: z.array(z.string()),
    })
    .optional(),
  sanctionsScreening: z
    .object({
      status: z.enum(VerificationStatus),
      screenedAt: z.date().optional(),
      provider: z.string().optional(),
      matches: z.array(z.any()).optional(),
    })
    .optional(),
  pepScreening: z
    .object({
      status: z.enum(VerificationStatus),
      screenedAt: z.date().optional(),
      provider: z.string().optional(),
      matches: z.array(z.any()).optional(),
    })
    .optional(),
  adverseMediaScreening: z
    .object({
      status: z.enum(VerificationStatus),
      screenedAt: z.date().optional(),
      provider: z.string().optional(),
      matches: z.array(z.any()).optional(),
    })
    .optional(),
  additionalData: z.record(z.string(), z.any()).optional(),
});

/**
 * Customer Risk Assessment Schema
 */
export const CustomerRiskAssessmentSchema = z.object({
  overallScore: z.number(),
  riskLevel: z.enum(RiskLevel),
  riskFactors: z.object({
    geographic: z.number().optional(),
    occupational: z.number().optional(),
    transactional: z.number().optional(),
    behavioral: z.number().optional(),
    regulatory: z.number().optional(),
    reputational: z.number().optional(),
  }),
  assessmentMethod: z.string(),
  assessmentDate: z.date(),
  nextReviewDate: z.date().optional(),
  notes: z.string().optional(),
  additionalData: z.record(z.string(), z.any()).optional(),
});

/**
 * Customer Document Summary Schema
 */
export const CustomerDocumentSummarySchema = z.object({
  id: z.string(),
  documentType: z.enum(DocumentType),
  fileName: z.string(),
  verificationStatus: z.enum(VerificationStatus),
  uploadedAt: z.date(),
  verifiedAt: z.date().optional(),
});

/**
 * Customer Risk Score Summary Schema
 */
export const CustomerRiskScoreSummarySchema = z.object({
  id: z.string(),
  overallScore: z.number(),
  riskLevel: z.enum(RiskLevel),
  assessmentDate: z.date(),
  assessmentMethod: z.string(),
  nextReviewDate: z.date().optional(),
});

/**
 * Customer Alert Summary Schema
 */
export const CustomerAlertSummarySchema = z.object({
  id: z.string(),
  alertType: z.enum(AlertType),
  severity: z.enum(AlertSeverity),
  title: z.string(),
  status: z.enum(AlertStatus),
  createdAt: z.date(),
  resolvedAt: z.date().optional(),
});

/**
 * Customer Response Schema
 * Used for returning customer data in API responses
 */
export const CustomerResponseSchema = z.object({
  id: z.string(),
  tenantId: z.string(),
  customerType: z.enum(CustomerType),
  status: z.enum(CustomerStatus),
  riskLevel: z.enum(RiskLevel),
  kycStatus: z.enum(KycStatus),
  profileData: CustomerProfileDataSchema,
  kycData: CustomerKycDataSchema.optional(),
  riskAssessment: CustomerRiskAssessmentSchema.optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
  documents: z.array(CustomerDocumentSummarySchema).optional(),
  riskScores: z.array(CustomerRiskScoreSummarySchema).optional(),
  alerts: z.array(CustomerAlertSummarySchema).optional(),
});

/**
 * Customer List Response Schema
 * Used for paginated customer list responses
 */
export const CustomerListResponseSchema = z.object({
  customers: z.array(CustomerResponseSchema),
  pagination: z.object({
    total: z.number(),
    page: z.number(),
    limit: z.number(),
    totalPages: z.number(),
    hasMore: z.boolean(),
  }),
});

/**
 * Customer Statistics Response Schema
 * Used for customer statistics responses
 */
export const CustomerStatisticsResponseSchema = z.object({
  /** */
  totalCustomers: z.number(),
  activeCustomers: z.number(),
  pendingKyc: z.number(),
  highRiskCustomers: z.number(),
  recentCustomers: z.number(),
  customersByType: z.record(z.enum(CustomerType), z.number()),
  customersByRiskLevel: z.record(z.enum(RiskLevel), z.number()),
  customersByStatus: z.record(z.enum(CustomerStatus), z.number()),
  customersByKycStatus: z.record(z.enum(KycStatus), z.number()),
});

/**
 * Customer Search Response Schema
 * Used for customer search responses
 */
export const CustomerSearchResponseSchema = z.object({
  customers: z.array(CustomerResponseSchema),
  totalResults: z.number(),
  searchTerm: z.string(),
  searchTime: z.number(),
});
