import { z } from 'zod';
import { AccountAccessLevel, AccountCurrency, AccountStatus, AccountType } from '../enums';

/**
 * Schema for creating a new account
 *
 * Validates input data for account creation including:
 * - Required tenant and customer identification
 * - Account type and currency selection
 * - Financial parameters (balance, limits, rates)
 * - Optional metadata for extensibility
 *
 * All monetary values are validated to be non-negative
 * Interest rates are capped at 100% for business rule compliance
 */
export const createAccountSchema = z.object({
  /** Unique identifier for the tenant owning this account */
  tenantId: z.string().min(1, 'Tenant ID is required'),

  /** Unique identifier for the customer owning this account */
  customerId: z.string().min(1, 'Customer ID is required'),

  /** Type of account (checking, savings, business, etc.) */
  accountType: z.enum(AccountType),

  /** Currency for all monetary operations on this account */
  currency: z.enum(AccountCurrency),

  /** Human-readable name for the account */
  accountName: z.string().min(1, 'Account name is required').max(100, 'Account name too long'),

  /** Optional description providing additional context */
  description: z.string().max(500, 'Description too long').optional(),

  /** Starting balance when account is created (defaults to 0) */
  initialBalance: z.number().min(0, 'Initial balance cannot be negative').default(0),

  /** Maximum overdraft allowed (defaults to 0 for no overdraft) */
  overdraftLimit: z.number().min(0, 'Overdraft limit cannot be negative').default(0),

  /** Annual interest rate as percentage (0-100, defaults to 0) */
  interestRate: z.number().min(0, 'Interest rate cannot be negative').max(100, 'Interest rate too high').default(0),

  /** Minimum balance required to avoid fees (defaults to 0) */
  minimumBalance: z.number().min(0, 'Minimum balance cannot be negative').default(0),

  /** Additional metadata for custom business logic */
  metadata: z.record(z.string(), z.any()).optional(),
});

/**
 * Schema for updating an existing account
 *
 * Allows partial updates to account properties including:
 * - Account metadata (name, description)
 * - Status and access level changes
 * - Financial parameter adjustments
 * - Activation/deactivation
 *
 * All fields are optional to support partial updates
 */
export const updateAccountSchema = z.object({
  /** Updated human-readable name for the account */
  accountName: z.string().min(1, 'Account name is required').max(100, 'Account name too long').optional(),

  /** Updated description providing additional context */
  description: z.string().max(500, 'Description too long').optional(),

  /** New account status (active, frozen, closed, etc.) */
  accountStatus: z.enum(AccountStatus).optional(),

  /** New access level (full, read-only, restricted, blocked) */
  accessLevel: z.enum(AccountAccessLevel).optional(),

  /** Updated maximum overdraft allowed */
  overdraftLimit: z.number().min(0, 'Overdraft limit cannot be negative').optional(),

  /** Updated annual interest rate as percentage (0-100) */
  interestRate: z.number().min(0, 'Interest rate cannot be negative').max(100, 'Interest rate too high').optional(),

  /** Updated minimum balance required to avoid fees */
  minimumBalance: z.number().min(0, 'Minimum balance cannot be negative').optional(),

  /** Updated metadata for custom business logic */
  metadata: z.record(z.string(), z.any()).optional(),

  /** Whether the account is active for operations */
  isActive: z.boolean().optional(),
});

/**
 * Schema for updating account balance
 *
 * Used internally by transaction processing to update:
 * - Current account balance
 * - Available balance (considering holds/pending)
 * - Last transaction timestamp for audit trails
 *
 * This schema is typically used by the transaction service
 * via gRPC calls to maintain balance consistency
 */
export const updateAccountBalanceSchema = z.object({
  /** Current total balance in the account */
  balance: z.number(),

  /** Available balance for new transactions (balance minus holds) */
  availableBalance: z.number(),

  /** ISO datetime string of the last transaction */
  lastTransactionAt: z.string().datetime(),
});

/**
 * Schema for account query filters (snake_case version)
 *
 * This schema accepts snake_case field names from API requests
 * and transforms them to camelCase for internal use.
 *
 * Used specifically for query parameter validation where the API
 * expects snake_case but the application uses camelCase internally.
 */
export const findAccountsFiltersSchema = z
  .object({
    /** Required tenant ID for data isolation */
    tenant_id: z.string().min(1, 'Tenant ID is required'),

    /** Optional customer ID to filter accounts for specific customer */
    customer_id: z.string().optional(),

    /** Filter by account type (checking, savings, etc.) */
    account_type: z.enum(AccountType).optional(),

    /** Filter by account status (active, frozen, etc.) */
    account_status: z.enum(AccountStatus).optional(),

    /** Filter by currency */
    currency: z.enum(AccountCurrency).optional(),

    /** Filter by access level */
    access_level: z.enum(AccountAccessLevel).optional(),

    /** Filter by active/inactive status */
    is_active: z.coerce.boolean().optional(),

    /** Minimum balance filter */
    min_balance: z.coerce.number().optional(),

    /** Maximum balance filter */
    max_balance: z.coerce.number().optional(),

    /** Page number for pagination (starts at 1) */
    page: z.coerce.number().min(1).default(1),

    /** Number of results per page (1-100) */
    limit: z.coerce.number().min(1).max(100).default(20),
  })
  .transform((data) => ({
    // Transform snake_case to camelCase for internal use
    tenantId: data.tenant_id,
    customerId: data.customer_id,
    accountType: data.account_type,
    accountStatus: data.account_status,
    currency: data.currency,
    accessLevel: data.access_level,
    isActive: data.is_active,
    minBalance: data.min_balance,
    maxBalance: data.max_balance,
    page: data.page,
    limit: data.limit,
  }));

/**
 * Schema for account validation request
 *
 * Used by transaction service to validate account operations:
 * - Checks account existence and status
 * - Validates operation permissions
 * - Verifies sufficient balance for debits
 * - Ensures compliance with account limits
 *
 * This is a critical validation step before processing transactions
 */
export const validateAccountSchema = z.object({
  /** Account ID to validate */
  accountId: z.string().min(1, 'Account ID is required'),

  /** Tenant ID for security validation */
  tenantId: z.string().min(1, 'Tenant ID is required'),

  /** Type of operation to validate (debit or credit) */
  operation: z.enum(['DEBIT', 'CREDIT']),

  /** Amount for the proposed operation */
  amount: z.number().min(0, 'Amount must be positive'),
});

/**
 * Type exports for use in services
 *
 * These types are inferred from the Zod schemas above and provide
 * TypeScript type safety throughout the application.
 *
 * Used by:
 * - Service layer for business logic
 * - Repository layer for data access
 * - Controller layer for request/response handling
 * - gRPC clients for inter-service communication
 */
export type CreateAccountInput = z.infer<typeof createAccountSchema>;
export type UpdateAccountInput = z.infer<typeof updateAccountSchema>;
export type UpdateAccountBalanceInput = z.infer<typeof updateAccountBalanceSchema>;
export type FindAccountsFiltersInput = z.infer<typeof findAccountsFiltersSchema>;
export type ValidateAccountInput = z.infer<typeof validateAccountSchema>;
