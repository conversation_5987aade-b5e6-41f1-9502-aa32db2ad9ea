/* eslint-disable @typescript-eslint/no-namespace */
/**
 * Customer namespace organization
 * Organizes all customer contracts into DTOs, Interfaces, Schemas, Enums, and Transformers namespaces
 */

import * as CustomerDTOs from './dtos';
import * as CustomerEnums from './enums';
import * as CustomerInterfaces from './interfaces';
import * as CustomerSchemas from './schemas';
import * as CustomerTransformers from './transformers';

export namespace Customer {
  export namespace DTOs {
    // Query DTOs
    export type CustomerQueryDto = CustomerDTOs.CustomerQueryDto;
    export type CustomerSearchDto = CustomerDTOs.CustomerSearchDto;
    export type CustomerStatsQueryDto = CustomerDTOs.CustomerStatsQueryDto;
    export type CustomerQueryOptions = CustomerDTOs.CustomerQueryOptions;

    // Create DTOs
    export type GenderType = CustomerDTOs.GenderType;
    export type AddressType = CustomerDTOs.AddressType;
    export type CustomerAddressRequestDto = CustomerDTOs.CustomerAddressRequestDto;
    export type CustomerProfileDataRequestDto = CustomerDTOs.CustomerProfileDataRequestDto;
    export type CustomerCreateRequestDto = CustomerDTOs.CustomerCreateRequestDto;

    // Update DTOs
    export type CustomerUpdateRequestDto = CustomerDTOs.CustomerUpdateRequestDto;
    export type CustomerStatusUpdateRequestDto = CustomerDTOs.CustomerStatusUpdateRequestDto;
    export type CustomerRiskLevelUpdateRequestDto = CustomerDTOs.CustomerRiskLevelUpdateRequestDto;
    export type CustomerKycStatusUpdateRequestDto = CustomerDTOs.CustomerKycStatusUpdateRequestDto;

    // Response DTOs
    export type CustomerAddressDto = CustomerDTOs.CustomerAddressDto;
    export type CustomerProfileDataDto = CustomerDTOs.CustomerProfileDataDto;
    export type CustomerKycDataDto = CustomerDTOs.CustomerKycDataDto;
    export type CustomerRiskAssessmentDto = CustomerDTOs.CustomerRiskAssessmentDto;
    export type CustomerDocumentSummaryDto = CustomerDTOs.CustomerDocumentSummaryDto;
    export type CustomerRiskScoreSummaryDto = CustomerDTOs.CustomerRiskScoreSummaryDto;
    export type CustomerAlertSummaryDto = CustomerDTOs.CustomerAlertSummaryDto;
    export type CustomerResponseDto = CustomerDTOs.CustomerResponseDto;
    export type CustomerListResponseDto = CustomerDTOs.CustomerListResponseDto;
    export type CustomerStatisticsResponseDto = CustomerDTOs.CustomerStatisticsResponseDto;
    export type CustomerSearchResponseDto = CustomerDTOs.CustomerSearchResponseDto;
  }

  export namespace Interfaces {
    // Entity interfaces
    export type ICustomer = CustomerInterfaces.ICustomer;
    export type ICustomerStatistics = CustomerInterfaces.ICustomerStatistics;
    export type ICustomerQueryOptions = CustomerInterfaces.ICustomerQueryOptions;

    // Profile interfaces
    export type ICustomerProfileData = CustomerInterfaces.ICustomerProfileData;
    export type ICustomerAddress = CustomerInterfaces.ICustomerAddress;

    // KYC interfaces
    export type ICustomerKycData = CustomerInterfaces.ICustomerKycData;
    export type ICustomerDocument = CustomerInterfaces.ICustomerDocument;

    // Risk interfaces
    export type ICustomerRiskAssessment = CustomerInterfaces.ICustomerRiskAssessment;
    export type ICustomerRiskScore = CustomerInterfaces.ICustomerRiskScore;

    // Relationships interfaces
    export type ICustomerRelationship = CustomerInterfaces.ICustomerRelationship;
    export type ICustomerAlert = CustomerInterfaces.ICustomerAlert;
    export type ICustomerNote = CustomerInterfaces.ICustomerNote;
  }

  export namespace Schemas {
    // Query schemas
    export import CustomerQuerySchema = CustomerSchemas.CustomerQuerySchema;
    export import CustomerSearchSchema = CustomerSchemas.CustomerSearchSchema;
    export import CustomerStatsQuerySchema = CustomerSchemas.CustomerStatsQuerySchema;

    // Request schemas
    export import GenderEnum = CustomerSchemas.GenderEnum;
    export import AddressTypeEnum = CustomerSchemas.AddressTypeEnum;
    export import CustomerAddressRequestSchema = CustomerSchemas.CustomerAddressRequestSchema;
    export import CustomerProfileDataRequestSchema = CustomerSchemas.CustomerProfileDataRequestSchema;
    export import CustomerCreateRequestSchema = CustomerSchemas.CustomerCreateRequestSchema;
    export import CustomerUpdateRequestSchema = CustomerSchemas.CustomerUpdateRequestSchema;
    export import CustomerStatusUpdateRequestSchema = CustomerSchemas.CustomerStatusUpdateRequestSchema;
    export import CustomerRiskLevelUpdateRequestSchema = CustomerSchemas.CustomerRiskLevelUpdateRequestSchema;
    export import CustomerKycStatusUpdateRequestSchema = CustomerSchemas.CustomerKycStatusUpdateRequestSchema;

    // Response schemas
    export import CustomerAddressSchema = CustomerSchemas.CustomerAddressSchema;
    export import CustomerProfileDataSchema = CustomerSchemas.CustomerProfileDataSchema;
    export import CustomerKycDataSchema = CustomerSchemas.CustomerKycDataSchema;
    export import CustomerRiskAssessmentSchema = CustomerSchemas.CustomerRiskAssessmentSchema;
    export import CustomerDocumentSummarySchema = CustomerSchemas.CustomerDocumentSummarySchema;
    export import CustomerRiskScoreSummarySchema = CustomerSchemas.CustomerRiskScoreSummarySchema;
    export import CustomerAlertSummarySchema = CustomerSchemas.CustomerAlertSummarySchema;
    export import CustomerResponseSchema = CustomerSchemas.CustomerResponseSchema;
    export import CustomerListResponseSchema = CustomerSchemas.CustomerListResponseSchema;
    export import CustomerStatisticsResponseSchema = CustomerSchemas.CustomerStatisticsResponseSchema;
    export import CustomerSearchResponseSchema = CustomerSchemas.CustomerSearchResponseSchema;
  }

  export namespace Enums {
    // Customer enums
    export import CustomerStatus = CustomerEnums.CustomerStatus;
    export import CustomerType = CustomerEnums.CustomerType;
    export import KycStatus = CustomerEnums.KycStatus;
    export import RiskLevel = CustomerEnums.RiskLevel;
    export import DocumentType = CustomerEnums.DocumentType;
    export import VerificationStatus = CustomerEnums.VerificationStatus;
    export import AlertType = CustomerEnums.AlertType;
    export import AlertSeverity = CustomerEnums.AlertSeverity;
    export import AlertStatus = CustomerEnums.AlertStatus;
    export import RelationshipType = CustomerEnums.RelationshipType;
    export import RelationshipStatus = CustomerEnums.RelationshipStatus;
    export import NoteType = CustomerEnums.NoteType;
    export import CustomerSortField = CustomerEnums.CustomerSortField;
    export import CustomerSortFieldApi = CustomerEnums.CustomerSortFieldApi;
    export import SortOrder = CustomerEnums.SortOrder;
  }

  export namespace Transformers {
    // Sort field transformers
    export import CustomerSortFieldTransformer = CustomerTransformers.CustomerSortFieldTransformer;
    export import transformSortFieldFromSnakeCase = CustomerTransformers.transformSortFieldFromSnakeCase;
  }
}
