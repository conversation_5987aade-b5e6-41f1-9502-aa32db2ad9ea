/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * Customer Create DTOs
 * TypeScript types for customer creation requests
 */

import { AlertSeverity, AlertStatus, AlertType, CustomerStatus, CustomerType, KycStatus, RiskLevel, SortOrder, VerificationStatus } from '../enums';

/**
 * Gender enum for customer profile data
 */
export type GenderType = 'MALE' | 'FEMALE' | 'OTHER';

/**
 * Address type enum
 */
export type AddressType = 'RESIDENTIAL' | 'BUSINESS' | 'MAILING';

/**
 * Customer Address Request DTO
 * Used for address information in customer requests
 */
export interface CustomerAddressRequestDto {
  /** Street address line 1 (required) */
  street1: string;

  /** Street address line 2 (optional) */
  street2?: string;

  /** City (required) */
  city: string;

  /** State/Province (optional) */
  state?: string;

  /** Postal/ZIP code (optional) */
  postalCode?: string;

  /** Country (required) */
  country: string;

  /** Address type (optional) */
  addressType?: AddressType;
}

/**
 * Customer Profile Data Request DTO
 * Used for customer profile data in create/update requests
 */
export interface CustomerProfileDataRequestDto {
  // Individual customer fields
  /** First name (optional for updates, required for individual customers) */
  firstName?: string;

  /** Last name (optional for updates, required for individual customers) */
  lastName?: string;

  /** Middle name (optional) */
  middleName?: string;

  /** Date of birth (ISO 8601 format) */
  dateOfBirth?: string;

  /** Gender */
  gender?: GenderType;

  /** Nationality */
  nationality?: string;

  /** Country of birth */
  countryOfBirth?: string;

  /** Place of birth */
  placeOfBirth?: string;

  // Business customer fields
  /** Business name (required for business customers) */
  businessName?: string;

  /** Business type */
  businessType?: string;

  /** Business registration number */
  businessRegistrationNumber?: string;

  /** Business registration date (ISO 8601 format) */
  businessRegistrationDate?: string;

  /** Business country */
  businessCountry?: string;

  /** Business industry */
  businessIndustry?: string;

  /** Business description */
  businessDescription?: string;

  // Contact information
  /** Email address */
  email?: string;

  /** Primary phone number */
  phoneNumber?: string;

  /** Alternate phone number */
  alternatePhoneNumber?: string;

  // Address information
  /** Primary address */
  address?: CustomerAddressRequestDto;

  /** Mailing address (if different from primary) */
  mailingAddress?: CustomerAddressRequestDto;

  // Employment/Business information
  /** Occupation */
  occupation?: string;

  /** Employer name */
  employer?: string;

  /** Employment status */
  employmentStatus?: string;

  /** Annual income */
  annualIncome?: number;

  /** Source of funds */
  sourceOfFunds?: string[];

  /** Source of wealth */
  sourceOfWealth?: string[];

  // Risk indicators
  /** Politically exposed person */
  politicallyExposed?: boolean;

  /** Sanctions match indicator */
  sanctionsMatch?: boolean;

  /** Adverse media match indicator */
  adverseMediaMatch?: boolean;

  /** High risk country exposure */
  highRiskCountryExposure?: boolean;

  // Custom fields
  /** Custom fields for tenant-specific data */
  customFields?: Record<string, any>;
}

/**
 * Customer Create Request DTO
 * Used for creating new customers
 */
export interface CustomerCreateRequestDto {
  /** Customer type (required) */
  customerType: CustomerType;

  /** Customer profile data (required) */
  profileData: CustomerProfileDataRequestDto;

  /** Customer status (optional, defaults to PENDING_VERIFICATION) */
  status?: CustomerStatus;

  /** Risk level (optional) */
  riskLevel?: RiskLevel;

  /** KYC status (optional, defaults to PENDING) */
  kycStatus?: KycStatus;

  /** KYC data (optional) */
  kycData?: Record<string, any>;

  /** Risk assessment data (optional) */
  riskAssessment?: Record<string, any>;
}

/**
 * Customer Query DTO
 * Used for filtering and pagination when retrieving customers
 */
export interface CustomerQueryDto {
  /** Page number for pagination (1-based) */
  page?: number;

  /** Number of items per page */
  limit?: number;

  /** Search term for customer data */
  search?: string;

  /** Filter by customer status */
  status?: CustomerStatus;

  /** Filter by customer type */
  customer_type?: CustomerType;

  /** Filter by risk level */
  risk_level?: RiskLevel;

  /** Filter by KYC status */
  kyc_status?: KycStatus;

  /** Filter by tenant ID */
  tenant_id?: string;

  /** Sort field (snake_case string that will be transformed to CustomerSortField enum) */
  sort_by?: string;

  /** Sort order */
  sort_order?: SortOrder;
}

/**
 * Customer Search DTO
 * Used for advanced customer search operations
 */
export interface CustomerSearchDto {
  /** Search term for customer data */
  search_term: string;

  /** Filter by tenant ID */
  tenant_id?: string;

  /** Maximum number of results */
  limit?: number;

  /** Filter by customer type */
  customer_type?: CustomerType;

  /** Filter by customer status */
  status?: CustomerStatus;

  /** Filter by risk level */
  risk_level?: RiskLevel;
}

/**
 * Customer Statistics Query DTO
 * Used for retrieving customer statistics with optional filtering
 */
export interface CustomerStatsQueryDto {
  /** Filter by tenant ID */
  tenant_id?: string;

  /** Start date for filtering (ISO 8601 format) */
  date_from?: string;

  /** End date for filtering (ISO 8601 format) */
  date_to?: string;

  /** Filter by customer type */
  customer_type?: CustomerType;

  /** Filter by risk level */
  risk_level?: RiskLevel;
}

/**
 * Service-level interfaces for internal use
 */
export interface CustomerQueryOptions {
  page?: number;
  limit?: number;
  search?: string;
  status?: CustomerStatus;
  customerType?: CustomerType;
  riskLevel?: RiskLevel;
  kycStatus?: KycStatus;
  tenantId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Customer Address DTO
 */
export interface CustomerAddressDto {
  street1: string;
  street2?: string;
  city: string;
  state?: string;
  postalCode?: string;
  country: string;
  addressType?: 'RESIDENTIAL' | 'BUSINESS' | 'MAILING';
}

/**
 * Customer Profile Data DTO
 * Contains the core profile information for a customer
 */
export interface CustomerProfileDataDto {
  // Individual customer fields
  firstName?: string;
  lastName?: string;
  middleName?: string;
  dateOfBirth?: string;
  gender?: 'MALE' | 'FEMALE' | 'OTHER';
  nationality?: string;
  countryOfBirth?: string;
  placeOfBirth?: string;

  // Business customer fields
  businessName?: string;
  businessType?: string;
  businessRegistrationNumber?: string;
  businessRegistrationDate?: string;
  businessCountry?: string;
  businessIndustry?: string;
  businessDescription?: string;

  // Contact information
  email?: string;
  phoneNumber?: string;
  alternatePhoneNumber?: string;

  // Address information
  address?: CustomerAddressDto;
  mailingAddress?: CustomerAddressDto;

  // Employment/Business information
  occupation?: string;
  employer?: string;
  employmentStatus?: string;
  annualIncome?: number;
  sourceOfFunds?: string[];
  sourceOfWealth?: string[];

  // Risk indicators
  politicallyExposed?: boolean;
  sanctionsMatch?: boolean;
  adverseMediaMatch?: boolean;
  highRiskCountryExposure?: boolean;

  // Custom fields
  customFields?: Record<string, any>;
}

/**
 * Customer KYC Data DTO
 */
export interface CustomerKycDataDto {
  completedAt?: Date;
  expiryDate?: Date;
  verificationMethod?: string;
  identityVerification?: {
    status: VerificationStatus;
    verifiedAt?: Date;
    verificationProvider?: string;
    verificationReference?: string;
    matchScore?: number;
  };
  addressVerification?: {
    status: VerificationStatus;
    verifiedAt?: Date;
    verificationProvider?: string;
    verificationReference?: string;
  };
  documentVerification?: {
    status: VerificationStatus;
    verifiedAt?: Date;
    documentsVerified: string[];
  };
  sanctionsScreening?: {
    status: VerificationStatus;
    screenedAt?: Date;
    provider?: string;
    matches?: any[];
  };
  pepScreening?: {
    status: VerificationStatus;
    screenedAt?: Date;
    provider?: string;
    matches?: any[];
  };
  adverseMediaScreening?: {
    status: VerificationStatus;
    screenedAt?: Date;
    provider?: string;
    matches?: any[];
  };
  additionalData?: Record<string, any>;
}

/**
 * Customer Risk Assessment DTO
 */
export interface CustomerRiskAssessmentDto {
  overallScore: number;
  riskLevel: RiskLevel;
  riskFactors: {
    geographic?: number;
    occupational?: number;
    transactional?: number;
    behavioral?: number;
    regulatory?: number;
    reputational?: number;
  };
  assessmentMethod: string;
  assessmentDate: Date;
  nextReviewDate?: Date;
  notes?: string;
  additionalData?: Record<string, any>;
}

/**
 * Customer Document Summary DTO
 */
export interface CustomerDocumentSummaryDto {
  id: string;
  documentType: DocumentType;
  fileName: string;
  verificationStatus: VerificationStatus;
  uploadedAt: Date;
  verifiedAt?: Date;
}

/**
 * Customer Risk Score Summary DTO
 */
export interface CustomerRiskScoreSummaryDto {
  id: string;
  overallScore: number;
  riskLevel: RiskLevel;
  assessmentDate: Date;
  assessmentMethod: string;
  nextReviewDate?: Date;
}

/**
 * Customer Alert Summary DTO
 */
export interface CustomerAlertSummaryDto {
  id: string;
  alertType: AlertType;
  severity: AlertSeverity;
  title: string;
  status: AlertStatus;
  createdAt: Date;
  resolvedAt?: Date;
}

/**
 * Customer Response DTO
 * Used for returning customer data in API responses
 */
export interface CustomerResponseDto {
  id: string;
  tenantId: string;
  customerType: CustomerType;
  status: CustomerStatus;
  riskLevel: RiskLevel;
  kycStatus: KycStatus;
  profileData: CustomerProfileDataDto;
  kycData?: CustomerKycDataDto;
  riskAssessment?: CustomerRiskAssessmentDto;
  createdAt: Date;
  updatedAt: Date;
  documents?: CustomerDocumentSummaryDto[];
  riskScores?: CustomerRiskScoreSummaryDto[];
  alerts?: CustomerAlertSummaryDto[];
}

/**
 * Customer List Response DTO
 * Used for paginated customer list responses
 */
export interface CustomerListResponseDto {
  customers: CustomerResponseDto[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasMore: boolean;
  };
}

/**
 * Customer Statistics Response DTO
 * Used for customer statistics responses
 */
export interface CustomerStatisticsResponseDto {
  totalCustomers: number;
  activeCustomers: number;
  pendingKyc: number;
  highRiskCustomers: number;
  recentCustomers: number;
  customersByType: Record<CustomerType, number>;
  customersByRiskLevel: Record<RiskLevel, number>;
  customersByStatus: Record<CustomerStatus, number>;
  customersByKycStatus: Record<KycStatus, number>;
}

/**
 * Customer Search Response DTO
 * Used for customer search responses
 */
export interface CustomerSearchResponseDto {
  customers: CustomerResponseDto[];
  totalResults: number;
  searchTerm: string;
  searchTime: number;
}

/**
 * Customer Update Request DTO
 * Used for updating existing customers
 */
export interface CustomerUpdateRequestDto {
  /** Customer type (optional) */
  customerType?: CustomerType;

  /** Customer profile data (optional) */
  profileData?: CustomerProfileDataRequestDto;

  /** Customer status (optional) */
  status?: CustomerStatus;

  /** Risk level (optional) */
  riskLevel?: RiskLevel;

  /** KYC status (optional) */
  kycStatus?: KycStatus;

  /** KYC data (optional) */
  kycData?: Record<string, any>;

  /** Risk assessment data (optional) */
  riskAssessment?: Record<string, any>;
}

/**
 * Customer Status Update Request DTO
 * Used for updating customer status only
 */
export interface CustomerStatusUpdateRequestDto {
  /** Customer status (required) */
  status: CustomerStatus;

  /** Reason for status change (optional) */
  reason?: string;
}

/**
 * Customer Risk Level Update Request DTO
 * Used for updating customer risk level
 */
export interface CustomerRiskLevelUpdateRequestDto {
  /** Risk level (required) */
  riskLevel: RiskLevel;

  /** Reason for risk level change (optional) */
  reason?: string;

  /** Assessment data (optional) */
  assessmentData?: Record<string, any>;
}

/**
 * Customer KYC Status Update Request DTO
 * Used for updating customer KYC status
 */
export interface CustomerKycStatusUpdateRequestDto {
  /** KYC status (required) */
  kycStatus: KycStatus;

  /** Reason for KYC status change (optional) */
  reason?: string;

  /** KYC data (optional) */
  kycData?: Record<string, any>;
}
