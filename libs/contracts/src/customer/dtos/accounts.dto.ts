import { PaginationMeta } from '../../shared';
import { AccountAccessLevel, AccountCurrency, AccountStatus, AccountType } from '../enums/accounts.enums';
import { CreateAccountInput, FindAccountsFiltersInput, UpdateAccountBalanceInput, UpdateAccountInput, ValidateAccountInput } from '../schemas/accounts.schema';

// ============================================================================
// REQUEST DTOs (Inferred from Schemas)
// ============================================================================

/**
 * Request DTO for creating a new account
 *
 * Inferred from createAccountSchema to ensure type consistency
 * between validation and data transfer objects.
 *
 * Used by:
 * - REST API endpoints for account creation
 * - gRPC service methods
 * - Internal service communication
 */
export type CreateAccountRequestDto = CreateAccountInput;

/**
 * Request DTO for updating an existing account
 *
 * Inferred from updateAccountSchema to ensure type consistency.
 * All fields are optional to support partial updates.
 *
 * Used by:
 * - REST API endpoints for account updates
 * - Internal service methods for account modifications
 * - Administrative operations
 */
export type UpdateAccountRequestDto = UpdateAccountInput;

/**
 * Request DTO for updating account balance
 *
 * Inferred from updateAccountBalanceSchema.
 * Used internally by transaction processing to maintain balance consistency.
 *
 * Used by:
 * - Transaction service via gRPC calls
 * - Balance reconciliation processes
 * - Audit and reporting systems
 */
export type UpdateAccountBalanceRequestDto = UpdateAccountBalanceInput;

/**
 * Request DTO for account query filters
 *
 * Inferred from findAccountsFiltersSchema.
 * Supports comprehensive account searching and filtering with pagination.
 *
 * Used by:
 * - Account listing endpoints
 * - Search and filter operations
 * - Administrative dashboards
 * - Reporting queries
 */
export type FindAccountsRequestDto = FindAccountsFiltersInput;

/**
 * Request DTO for account validation
 *
 * Inferred from validateAccountSchema.
 * Used by transaction service to validate account operations before processing.
 *
 * Used by:
 * - Transaction validation endpoints
 * - gRPC service calls
 * - Pre-transaction checks
 */
export type ValidateAccountRequestDto = ValidateAccountInput;

// ============================================================================
// RESPONSE DTOs
// ============================================================================

/**
 * Response DTO for account data
 *
 * Represents the complete account data returned by API endpoints.
 * Contains all account properties including computed fields and timestamps.
 *
 * Used by:
 * - REST API responses
 * - gRPC service responses
 * - Frontend applications
 * - External integrations
 */
export class AccountResponseDto {
  /** Unique account identifier (CUID with 'acc_' prefix) */
  id!: string;

  /** Tenant identifier for data isolation */
  tenantId!: string;

  /** Customer identifier who owns this account */
  customerId!: string;

  /** Unique account number for external reference */
  accountNumber!: string;

  /** Type of account (checking, savings, business, etc.) */
  accountType!: AccountType;

  /** Current account status */
  accountStatus!: AccountStatus;

  /** Account currency */
  currency!: AccountCurrency;

  /** Current access level */
  accessLevel!: AccountAccessLevel;

  /** Current total balance */
  balance!: number;

  /** Available balance for transactions */
  availableBalance!: number;

  /** Maximum overdraft allowed */
  overdraftLimit!: number;

  /** Annual interest rate as percentage */
  interestRate!: number;

  /** Minimum balance required */
  minimumBalance!: number;

  /** Human-readable account name */
  accountName!: string;

  /** Optional description */
  description?: string;

  /** Additional metadata */
  metadata?: Record<string, unknown>;

  /** Whether account is active */
  isActive!: boolean;

  /** Timestamp of last transaction */
  lastTransactionAt?: Date;

  /** Account creation timestamp */
  createdAt!: Date;

  /** Last update timestamp */
  updatedAt!: Date;
}

/**
 * Response DTO for account validation
 *
 * Contains the result of account validation operations.
 * Provides detailed information about account capabilities and restrictions.
 *
 * Used by:
 * - Transaction service responses
 * - Validation endpoint responses
 * - Business rule engines
 */
export class ValidateAccountResponseDto {
  /** Whether the account passed validation */
  isValid!: boolean;

  /** Current account status */
  accountStatus!: AccountStatus;

  /** Available balance for transactions */
  availableBalance!: number;

  /** Account currency */
  currency!: AccountCurrency;

  /** Current access level */
  accessLevel!: AccountAccessLevel;

  /** Whether debit operations are allowed */
  canDebit!: boolean;

  /** Whether credit operations are allowed */
  canCredit!: boolean;

  /** Reason for validation failure (if applicable) */
  reason?: string;
}

/**
 * Response DTO for paginated accounts
 *
 * Contains paginated list of accounts with metadata.
 * Follows standard pagination patterns used throughout the platform.
 *
 * Used by:
 * - Account listing endpoints
 * - Search result responses
 * - Administrative interfaces
 */
export type FindAccountsResponseDto = {
  /** Array of account data */
  accounts: AccountResponseDto[];

  pagination: PaginationMeta;
};

/**
 * Response DTO for account creation
 *
 * Returns the newly created account data.
 * Extends AccountResponseDto with creation-specific information.
 */
export type CreateAccountResponseDto = AccountResponseDto;

/**
 * Response DTO for account updates
 *
 * Returns the updated account data.
 * Extends AccountResponseDto with update-specific information.
 */
export type UpdateAccountResponseDto = AccountResponseDto;
