/**
 * Customer Service Contracts
 *
 * This module exports all customer-related contracts including:
 * - Enums for customer types, statuses, and other constants
 * - Interfaces for customer entities and related data structures
 * - DTOs for API requests and responses
 * - Schemas for validation
 * - Transformers for data transformation
 */

// Export all individual modules
export * from './dtos';
export * from './enums';
export * from './interfaces';
export * from './schemas';
export * from './transformers';

// Export the organized namespace
export * from './namespace';

// Customer namespace is now exported from './namespace'
