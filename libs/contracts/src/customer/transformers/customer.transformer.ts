/**
 * Customer Sort Field Transformer
 *
 * Provides transformation to convert CustomerSortField enum values to camelCase format
 * and convert snake_case API parameters to CustomerSortField enum values
 */

import { CustomerSortField, CustomerSortFieldApi } from '../enums';

/**
 * Mapping from API sort field values (snake_case) to internal enum values (camelCase)
 */
const CUSTOMER_SORT_FIELD_MAP: Record<CustomerSortFieldApi, CustomerSortField> = {
  [CustomerSortFieldApi.CREATED_AT]: CustomerSortField.CREATED_AT,
  [CustomerSortFieldApi.UPDATED_AT]: CustomerSortField.UPDATED_AT,
  [CustomerSortFieldApi.NAME]: CustomerSortField.NAME,
  [CustomerSortFieldApi.EMAIL]: CustomerSortField.EMAIL,
  [CustomerSortFieldApi.STATUS]: CustomerSortField.STATUS,
  [CustomerSortFieldApi.RISK_LEVEL]: CustomerSortField.RISK_LEVEL,
  [CustomerSortFieldApi.KYC_STATUS]: CustomerSortField.KYC_STATUS,
  [CustomerSortFieldApi.CUSTOMER_TYPE]: CustomerSortField.CUSTOMER_TYPE,
} as const;

/**
 * Transform snake_case sort field string to CustomerSortField enum
 * @param sortField - The snake_case sort field string from API request
 * @returns The corresponding CustomerSortField enum value
 * @throws Error if the sort field is not recognized
 *
 * @example
 * ```typescript
 * transformSortFieldFromSnakeCase('created_at') // returns CustomerSortField.CREATED_AT
 * transformSortFieldFromSnakeCase('customer_type') // returns CustomerSortField.CUSTOMER_TYPE
 * ```
 */
export function transformSortFieldFromSnakeCase(sortField: string): CustomerSortField {
  const enumValue = CUSTOMER_SORT_FIELD_MAP[sortField as CustomerSortFieldApi];
  if (!enumValue) {
    throw new Error(`Invalid sort field: ${sortField}. Valid values are: ${Object.values(CustomerSortFieldApi).join(', ')}`);
  }
  return enumValue;
}

/**
 * CustomerSortField transformer object
 */
export const CustomerSortFieldTransformer = {
  fromSnakeCase: transformSortFieldFromSnakeCase,
} as const;
