import {
  RiskEvaluationDto,
  CustomerRiskProfileDto,
  RiskAlertDto,
  TransactionContextDto,
  CustomerInfoDto,
  AccountInfoDto,
} from '../dtos/aml.dto';

// ============================================================================
// RISK EVALUATION ENGINE INTERFACES
// ============================================================================

export interface IRiskEvaluationEngine {
  /**
   * Evaluate transaction risk
   */
  evaluateTransactionRisk(
    tenantId: string,
    transaction: TransactionContextDto,
    fromAccount: AccountInfoDto,
    toAccount: AccountInfoDto,
    fromCustomer: CustomerInfoDto,
    toCustomer: CustomerInfoDto,
    options?: {
      includeHistoricalAnalysis?: boolean;
      includePatternDetection?: boolean;
    }
  ): Promise<RiskEvaluationDto>;

  /**
   * Evaluate customer risk
   */
  evaluateCustomerRisk(
    tenantId: string,
    customer: CustomerInfoDto,
    accounts: AccountInfoDto[],
    options?: {
      includeTransactionHistory?: boolean;
    }
  ): Promise<CustomerRiskProfileDto>;

  /**
   * Get customer risk profile
   */
  getCustomerRiskProfile(
    tenantId: string,
    customerId: string,
    options?: {
      includeRecentAlerts?: boolean;
      historyDays?: number;
    }
  ): Promise<CustomerRiskProfileDto | null>;
}

// ============================================================================
// RISK RULES ENGINE INTERFACES
// ============================================================================

export interface IRiskRule {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  ruleType: string;
  severity: string;
  threshold?: number;
  parameters?: Record<string, any>;
  conditions?: Record<string, any>;
  isActive: boolean;
}

export interface IRiskRulesEngine {
  /**
   * Get active rules for tenant
   */
  getActiveRules(tenantId: string): Promise<IRiskRule[]>;

  /**
   * Evaluate rules against transaction
   */
  evaluateRules(
    tenantId: string,
    transaction: TransactionContextDto,
    context: {
      fromAccount: AccountInfoDto;
      toAccount: AccountInfoDto;
      fromCustomer: CustomerInfoDto;
      toCustomer: CustomerInfoDto;
    }
  ): Promise<RiskAlertDto[]>;

  /**
   * Update rules
   */
  updateRules(tenantId: string, rules: Partial<IRiskRule>[]): Promise<number>;

  /**
   * Validate rule configuration
   */
  validateRule(rule: Partial<IRiskRule>): Promise<string[]>;
}

// ============================================================================
// PATTERN DETECTION INTERFACES
// ============================================================================

export interface ITransactionPattern {
  patternType: string;
  confidence: number;
  description: string;
  riskScore: number;
  metadata?: Record<string, any>;
}

export interface IPatternDetectionEngine {
  /**
   * Detect suspicious patterns in transaction
   */
  detectPatterns(
    tenantId: string,
    transaction: TransactionContextDto,
    historicalData?: any[]
  ): Promise<ITransactionPattern[]>;

  /**
   * Analyze velocity patterns
   */
  analyzeVelocity(
    tenantId: string,
    customerId: string,
    timeWindow: number
  ): Promise<{
    transactionCount: number;
    totalAmount: number;
    averageAmount: number;
    riskScore: number;
  }>;

  /**
   * Check for geographic anomalies
   */
  checkGeographicAnomalies(
    tenantId: string,
    customerId: string,
    currentLocation: string
  ): Promise<{
    isAnomalous: boolean;
    riskScore: number;
    reason?: string;
  }>;
}

// ============================================================================
// SANCTIONS & WATCHLIST INTERFACES
// ============================================================================

export interface ISanctionsMatch {
  listName: string;
  matchType: 'exact' | 'fuzzy' | 'phonetic';
  confidence: number;
  matchedFields: string[];
  details: Record<string, any>;
}

export interface ISanctionsEngine {
  /**
   * Check customer against sanctions lists
   */
  checkSanctions(customer: CustomerInfoDto): Promise<ISanctionsMatch[]>;

  /**
   * Check for PEP (Politically Exposed Person) status
   */
  checkPEP(customer: CustomerInfoDto): Promise<{
    isPEP: boolean;
    confidence: number;
    details?: Record<string, any>;
  }>;

  /**
   * Check adverse media
   */
  checkAdverseMedia(customer: CustomerInfoDto): Promise<{
    hasAdverseMedia: boolean;
    riskScore: number;
    articles?: Array<{
      title: string;
      source: string;
      date: string;
      relevance: number;
    }>;
  }>;
}

// ============================================================================
// HISTORICAL ANALYSIS INTERFACES
// ============================================================================

export interface IHistoricalAnalysisEngine {
  /**
   * Analyze customer transaction history
   */
  analyzeCustomerHistory(
    tenantId: string,
    customerId: string,
    timeWindow: number
  ): Promise<{
    totalTransactions: number;
    totalAmount: number;
    averageAmount: number;
    patterns: ITransactionPattern[];
    riskScore: number;
  }>;

  /**
   * Compare transaction against historical patterns
   */
  compareToHistory(
    tenantId: string,
    transaction: TransactionContextDto,
    customerId: string
  ): Promise<{
    isAnomalous: boolean;
    deviationScore: number;
    reasons: string[];
  }>;
}

// ============================================================================
// AUDIT & COMPLIANCE INTERFACES
// ============================================================================

export interface IAuditLogger {
  /**
   * Log risk evaluation
   */
  logEvaluation(
    tenantId: string,
    evaluationId: string,
    action: string,
    details: Record<string, any>
  ): Promise<void>;

  /**
   * Log rule changes
   */
  logRuleChange(
    tenantId: string,
    ruleId: string,
    action: string,
    oldValues?: Record<string, any>,
    newValues?: Record<string, any>
  ): Promise<void>;

  /**
   * Get audit trail
   */
  getAuditTrail(
    tenantId: string,
    filters: {
      entityType?: string;
      entityId?: string;
      action?: string;
      fromDate?: Date;
      toDate?: Date;
    }
  ): Promise<Array<{
    id: string;
    action: string;
    entityType: string;
    entityId?: string;
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    createdAt: Date;
  }>>;
}

// ============================================================================
// CONFIGURATION INTERFACES
// ============================================================================

export interface IAMLConfiguration {
  /**
   * Get configuration value
   */
  get<T>(tenantId: string, key: string, defaultValue?: T): Promise<T>;

  /**
   * Set configuration value
   */
  set<T>(tenantId: string, key: string, value: T): Promise<void>;

  /**
   * Get all configurations for tenant
   */
  getAll(tenantId: string): Promise<Record<string, any>>;

  /**
   * Update multiple configurations
   */
  updateMany(tenantId: string, configs: Record<string, any>): Promise<void>;
}

// ============================================================================
// SERVICE INTERFACES
// ============================================================================

export interface IAMLService {
  /**
   * Main risk evaluation service interface
   */
  riskEvaluationEngine: IRiskEvaluationEngine;
  riskRulesEngine: IRiskRulesEngine;
  patternDetectionEngine: IPatternDetectionEngine;
  sanctionsEngine: ISanctionsEngine;
  historicalAnalysisEngine: IHistoricalAnalysisEngine;
  auditLogger: IAuditLogger;
  configuration: IAMLConfiguration;

  /**
   * Health check
   */
  isHealthy(): Promise<boolean>;

  /**
   * Get service status
   */
  getStatus(): Promise<{
    isHealthy: boolean;
    version: string;
    activeRules: number;
    evaluationsToday: number;
    averageProcessingTime: number;
    lastRuleUpdate?: Date;
  }>;
}
