/**
 * AML Service Enums
 * 
 * Defines all enums used in the AML (Anti-Money Laundering) service
 * for risk evaluation, compliance, and monitoring.
 */

/**
 * Risk levels for transactions and customers
 */
export enum RiskLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

/**
 * Transaction status after AML evaluation
 */
export enum TransactionStatus {
  APPROVED = 'APPROVED',
  PENDING_REVIEW = 'PENDING_REVIEW',
  FLAGGED = 'FLAGGED',
  BLOCKED = 'BLOCKED',
  REJECTED = 'REJECTED',
}

/**
 * Alert types for risk evaluation
 */
export enum AlertType {
  AMOUNT_THRESHOLD = 'AMOUNT_THRESHOLD',
  VELOCITY_CHECK = 'VELOCITY_CHECK',
  TIME_ANOMALY = 'TIME_ANOMALY',
  GEOGRAPHIC_ANOMALY = 'GEOGRAPHIC_ANOMALY',
  PATTERN_DETECTION = 'PATTERN_DETECTION',
  SANCTIONS_MATCH = 'SANCTIONS_MATCH',
  PEP_MATCH = 'PEP_MATCH',
  ADVERSE_MEDIA = 'ADVERSE_MEDIA',
  COMPLIANCE = 'COMPLIANCE',
  CUSTOMER_RISK = 'CUSTOMER_RISK',
  UNKNOWN = 'UNKNOWN',
}

/**
 * Alert severity levels
 */
export enum AlertSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

/**
 * Risk rule types
 */
export enum RiskRuleType {
  AMOUNT_THRESHOLD = 'AMOUNT_THRESHOLD',
  VELOCITY_CHECK = 'VELOCITY_CHECK',
  TIME_ANOMALY = 'TIME_ANOMALY',
  GEOGRAPHIC_ANOMALY = 'GEOGRAPHIC_ANOMALY',
  PATTERN_DETECTION = 'PATTERN_DETECTION',
  SANCTIONS_SCREENING = 'SANCTIONS_SCREENING',
  PEP_SCREENING = 'PEP_SCREENING',
  ADVERSE_MEDIA_SCREENING = 'ADVERSE_MEDIA_SCREENING',
  COMPLIANCE_CHECK = 'COMPLIANCE_CHECK',
  CUSTOMER_RISK_ASSESSMENT = 'CUSTOMER_RISK_ASSESSMENT',
  CUSTOM = 'CUSTOM',
}

/**
 * Customer risk categories
 */
export enum CustomerRiskCategory {
  LOW_RISK = 'LOW_RISK',
  MEDIUM_RISK = 'MEDIUM_RISK',
  HIGH_RISK = 'HIGH_RISK',
  PROHIBITED = 'PROHIBITED',
}

/**
 * KYC (Know Your Customer) status
 */
export enum KycStatus {
  NOT_STARTED = 'NOT_STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  PENDING_REVIEW = 'PENDING_REVIEW',
  VERIFIED = 'VERIFIED',
  REJECTED = 'REJECTED',
  EXPIRED = 'EXPIRED',
}

/**
 * Customer types for risk assessment
 */
export enum CustomerType {
  INDIVIDUAL = 'INDIVIDUAL',
  BUSINESS = 'BUSINESS',
  GOVERNMENT = 'GOVERNMENT',
  NON_PROFIT = 'NON_PROFIT',
  FINANCIAL_INSTITUTION = 'FINANCIAL_INSTITUTION',
}

/**
 * Account types
 */
export enum AccountType {
  CHECKING = 'CHECKING',
  SAVINGS = 'SAVINGS',
  BUSINESS = 'BUSINESS',
  INVESTMENT = 'INVESTMENT',
  TRUST = 'TRUST',
  ESCROW = 'ESCROW',
}

/**
 * Transaction pattern types
 */
export enum PatternType {
  ROUND_NUMBER = 'ROUND_NUMBER',
  STRUCTURED_TRANSACTION = 'STRUCTURED_TRANSACTION',
  RAPID_SUCCESSION = 'RAPID_SUCCESSION',
  UNUSUAL_TIMING = 'UNUSUAL_TIMING',
  CROSS_BORDER = 'CROSS_BORDER',
  ESCALATING_AMOUNTS = 'ESCALATING_AMOUNTS',
  CONSISTENT_ROUND_NUMBERS = 'CONSISTENT_ROUND_NUMBERS',
  WEEKEND_BUSINESS = 'WEEKEND_BUSINESS',
}

/**
 * Sanctions list types
 */
export enum SanctionsListType {
  OFAC_SDN = 'OFAC_SDN',
  OFAC_CONSOLIDATED = 'OFAC_CONSOLIDATED',
  EU_SANCTIONS = 'EU_SANCTIONS',
  UN_SANCTIONS = 'UN_SANCTIONS',
  UK_SANCTIONS = 'UK_SANCTIONS',
  CUSTOM = 'CUSTOM',
}

/**
 * Sanctions match types
 */
export enum SanctionsMatchType {
  EXACT = 'EXACT',
  FUZZY = 'FUZZY',
  PHONETIC = 'PHONETIC',
  ALIAS = 'ALIAS',
}

/**
 * PEP (Politically Exposed Person) categories
 */
export enum PepCategory {
  DOMESTIC_PEP = 'DOMESTIC_PEP',
  FOREIGN_PEP = 'FOREIGN_PEP',
  INTERNATIONAL_ORGANIZATION = 'INTERNATIONAL_ORGANIZATION',
  FAMILY_MEMBER = 'FAMILY_MEMBER',
  CLOSE_ASSOCIATE = 'CLOSE_ASSOCIATE',
}

/**
 * Audit action types
 */
export enum AuditActionType {
  RULE_CREATED = 'RULE_CREATED',
  RULE_UPDATED = 'RULE_UPDATED',
  RULE_DELETED = 'RULE_DELETED',
  RULE_ACTIVATED = 'RULE_ACTIVATED',
  RULE_DEACTIVATED = 'RULE_DEACTIVATED',
  EVALUATION_PERFORMED = 'EVALUATION_PERFORMED',
  ALERT_GENERATED = 'ALERT_GENERATED',
  ALERT_RESOLVED = 'ALERT_RESOLVED',
  CUSTOMER_RISK_UPDATED = 'CUSTOMER_RISK_UPDATED',
  CONFIGURATION_CHANGED = 'CONFIGURATION_CHANGED',
  SANCTIONS_LIST_UPDATED = 'SANCTIONS_LIST_UPDATED',
  SYSTEM_MAINTENANCE = 'SYSTEM_MAINTENANCE',
}

/**
 * Configuration types
 */
export enum ConfigurationType {
  RISK_THRESHOLDS = 'RISK_THRESHOLDS',
  ALERT_SETTINGS = 'ALERT_SETTINGS',
  SANCTIONS_SETTINGS = 'SANCTIONS_SETTINGS',
  REPORTING_SETTINGS = 'REPORTING_SETTINGS',
  SYSTEM_SETTINGS = 'SYSTEM_SETTINGS',
  INTEGRATION_SETTINGS = 'INTEGRATION_SETTINGS',
}

/**
 * Review status for alerts and evaluations
 */
export enum ReviewStatus {
  PENDING = 'PENDING',
  IN_REVIEW = 'IN_REVIEW',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  ESCALATED = 'ESCALATED',
  CLOSED = 'CLOSED',
}

/**
 * Data retention periods
 */
export enum RetentionPeriod {
  ONE_YEAR = 'ONE_YEAR',
  THREE_YEARS = 'THREE_YEARS',
  FIVE_YEARS = 'FIVE_YEARS',
  SEVEN_YEARS = 'SEVEN_YEARS',
  TEN_YEARS = 'TEN_YEARS',
  INDEFINITE = 'INDEFINITE',
}

/**
 * Geographic risk levels by country/region
 */
export enum GeographicRiskLevel {
  VERY_LOW = 'VERY_LOW',
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  VERY_HIGH = 'VERY_HIGH',
  PROHIBITED = 'PROHIBITED',
}

/**
 * Currency risk levels
 */
export enum CurrencyRiskLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  RESTRICTED = 'RESTRICTED',
}

/**
 * Monitoring frequency for customer risk profiles
 */
export enum MonitoringFrequency {
  REAL_TIME = 'REAL_TIME',
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
  QUARTERLY = 'QUARTERLY',
  ANNUALLY = 'ANNUALLY',
}

/**
 * Rule execution status
 */
export enum RuleExecutionStatus {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  SKIPPED = 'SKIPPED',
  TIMEOUT = 'TIMEOUT',
  ERROR = 'ERROR',
}

/**
 * Data source types for risk evaluation
 */
export enum DataSourceType {
  INTERNAL = 'INTERNAL',
  EXTERNAL_API = 'EXTERNAL_API',
  THIRD_PARTY_DATABASE = 'THIRD_PARTY_DATABASE',
  MANUAL_INPUT = 'MANUAL_INPUT',
  BATCH_IMPORT = 'BATCH_IMPORT',
}

/**
 * Export all enums for easy importing
 */
export const AMLEnums = {
  RiskLevel,
  TransactionStatus,
  AlertType,
  AlertSeverity,
  RiskRuleType,
  CustomerRiskCategory,
  KycStatus,
  CustomerType,
  AccountType,
  PatternType,
  SanctionsListType,
  SanctionsMatchType,
  PepCategory,
  AuditActionType,
  ConfigurationType,
  ReviewStatus,
  RetentionPeriod,
  GeographicRiskLevel,
  CurrencyRiskLevel,
  MonitoringFrequency,
  RuleExecutionStatus,
  DataSourceType,
} as const;
