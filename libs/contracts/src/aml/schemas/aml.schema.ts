import { z } from 'zod';

// ============================================================================
// ENUMS
// ============================================================================

export const RiskLevelEnum = z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']);
export const AlertTypeEnum = z.enum([
  'AMOUNT_THRESHOLD',
  'VELOCITY_CHECK',
  'TIME_ANOMALY',
  'GEOGRAPHIC_ANOMALY',
  'PATTERN_DETECTION',
  'SANCTIONS_MATCH',
  'PEP_MATCH',
  'ADVERSE_MEDIA',
  'COMPLIANCE',
  'CUSTOMER_RISK',
  'UNKNOWN',
]);
export const AlertSeverityEnum = z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']);
export const TransactionStatusEnum = z.enum(['APPROVED', 'PENDING_REVIEW', 'FLAGGED', 'BLOCKED', 'REJECTED']);
export const CustomerRiskCategoryEnum = z.enum(['LOW_RISK', 'MEDIUM_RISK', 'HIGH_RISK', 'PROHIBITED']);

// ============================================================================
// COMMON SCHEMAS
// ============================================================================

export const RequestMetadataSchema = z.object({
  requestId: z.string().min(1),
  sourceIp: z.string().optional(),
  userAgent: z.string().optional(),
  timestamp: z.string().datetime(),
  correlationId: z.string().optional(),
});

export const ResponseMetadataSchema = z.object({
  requestId: z.string().min(1),
  timestamp: z.string().datetime(),
  processingTime: z.number().int().nonnegative(),
  version: z.string().optional(),
  correlationId: z.string().optional(),
});

export const ErrorDetailsSchema = z.object({
  code: z.string().min(1),
  message: z.string().min(1),
  details: z.string().optional(),
});

// ============================================================================
// ACCOUNT & CUSTOMER SCHEMAS
// ============================================================================

export const AccountInfoSchema = z.object({
  accountId: z.string().min(1),
  customerId: z.string().min(1),
  accountType: z.string().min(1),
  accountNumber: z.string().optional(),
  currentBalance: z.number().optional(),
  currency: z.string().length(3),
  accountOpenDate: z.string().datetime().optional(),
  accountStatus: z.string().optional(),
  countryCode: z.string().length(2).optional(),
});

export const CustomerInfoSchema = z.object({
  customerId: z.string().min(1),
  customerType: z.string().min(1),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  businessName: z.string().optional(),
  dateOfBirth: z.string().optional(),
  nationality: z.string().optional(),
  countryOfResidence: z.string().optional(),
  isPoliticallyExposed: z.boolean().default(false),
  isSanctioned: z.boolean().default(false),
  riskCategory: CustomerRiskCategoryEnum,
  lastKycUpdate: z.string().datetime().optional(),
  kycStatus: z.string().optional(),
});

export const TransactionContextSchema = z.object({
  transactionId: z.string().min(1),
  amount: z.number().positive(),
  currency: z.string().length(3),
  description: z.string().optional(),
  transactionType: z.string().optional(),
  channel: z.string().optional(),
  ipAddress: z.string().optional(),
  deviceId: z.string().optional(),
  location: z.string().optional(),
  countryCode: z.string().length(2).optional(),
  timestamp: z.string().datetime(),
  metadata: z.record(z.string(), z.any()).optional(),
});

// ============================================================================
// RISK EVALUATION SCHEMAS
// ============================================================================

export const RiskAlertSchema = z.object({
  type: AlertTypeEnum,
  severity: AlertSeverityEnum,
  message: z.string().min(1),
  ruleId: z.string().optional(),
  ruleName: z.string().optional(),
  confidence: z.number().min(0).max(1).optional(),
  details: z.record(z.string(), z.any()).optional(),
});

export const RiskEvaluationSchema = z.object({
  riskScore: z.number().min(0).max(100),
  riskLevel: RiskLevelEnum,
  recommendedStatus: TransactionStatusEnum,
  alerts: z.array(RiskAlertSchema),
  evaluationId: z.string().min(1),
  evaluatedAt: z.string().datetime(),
  evaluationModel: z.string().optional(),
  riskFactors: z.record(z.string(), z.number()).optional(),
});

export const CustomerRiskProfileSchema = z.object({
  customerId: z.string().min(1),
  riskCategory: CustomerRiskCategoryEnum,
  overallRiskScore: z.number().min(0).max(100),
  recentAlerts: z.array(RiskAlertSchema).optional(),
  lastEvaluated: z.string().datetime(),
  riskFactors: z.record(z.string(), z.number()).optional(),
  riskReason: z.string().optional(),
});

// ============================================================================
// REQUEST SCHEMAS
// ============================================================================

export const EvaluateTransactionRiskRequestSchema = z
  .object({
    tenantId: z.string().min(1),
    transaction: TransactionContextSchema,
    fromAccount: AccountInfoSchema,
    toAccount: AccountInfoSchema,
    fromCustomer: CustomerInfoSchema,
    toCustomer: CustomerInfoSchema,
    metadata: RequestMetadataSchema,
    includeHistoricalAnalysis: z.boolean().default(true),
    includePatternDetection: z.boolean().default(true),
    additionalChecks: z.array(z.string()).optional(),
  })
  .transform((data) => ({
    ...data,
    // Transform fields if needed
    transaction: {
      ...data.transaction,
      amount: Number(data.transaction.amount),
    },
  }));

export const EvaluateCustomerRiskRequestSchema = z.object({
  tenantId: z.string().min(1),
  customer: CustomerInfoSchema,
  accounts: z.array(AccountInfoSchema),
  metadata: RequestMetadataSchema,
  includeTransactionHistory: z.boolean().default(true),
  evaluationReason: z.string().optional(),
});

export const GetRiskProfileRequestSchema = z.object({
  tenantId: z.string().min(1),
  customerId: z.string().min(1),
  metadata: RequestMetadataSchema,
  includeRecentAlerts: z.boolean().default(true),
  historyDays: z.number().int().positive().default(30),
});

export const UpdateRiskRulesRequestSchema = z.object({
  tenantId: z.string().min(1),
  rules: z.array(
    z.object({
      ruleId: z.string().min(1),
      ruleName: z.string().min(1),
      description: z.string().optional(),
      alertType: AlertTypeEnum,
      severity: AlertSeverityEnum,
      isActive: z.boolean(),
      parameters: z.record(z.string(), z.any()).optional(),
      threshold: z.number().optional(),
      condition: z.string().optional(),
    }),
  ),
  metadata: RequestMetadataSchema,
  validateOnly: z.boolean().default(false),
});

export const GetAMLStatusRequestSchema = z.object({
  tenantId: z.string().min(1),
  metadata: RequestMetadataSchema,
});

// ============================================================================
// RESPONSE SCHEMAS
// ============================================================================

export const EvaluateTransactionRiskResponseSchema = z.object({
  success: z.boolean(),
  riskEvaluation: RiskEvaluationSchema.optional(),
  message: z.string().optional(),
  error: ErrorDetailsSchema.optional(),
  metadata: ResponseMetadataSchema,
});

export const EvaluateCustomerRiskResponseSchema = z.object({
  success: z.boolean(),
  riskProfile: CustomerRiskProfileSchema.optional(),
  message: z.string().optional(),
  error: ErrorDetailsSchema.optional(),
  metadata: ResponseMetadataSchema,
});

export const GetRiskProfileResponseSchema = z.object({
  success: z.boolean(),
  riskProfile: CustomerRiskProfileSchema.optional(),
  message: z.string().optional(),
  error: ErrorDetailsSchema.optional(),
  metadata: ResponseMetadataSchema,
});

export const UpdateRiskRulesResponseSchema = z.object({
  success: z.boolean(),
  rulesUpdated: z.number().int().nonnegative(),
  validationErrors: z.array(z.string()).optional(),
  message: z.string().optional(),
  error: ErrorDetailsSchema.optional(),
  metadata: ResponseMetadataSchema,
});

export const GetAMLStatusResponseSchema = z.object({
  success: z.boolean(),
  status: z
    .object({
      isHealthy: z.boolean(),
      version: z.string(),
      activeRules: z.number().int().nonnegative(),
      evaluationsToday: z.number().int().nonnegative(),
      averageProcessingTime: z.number().nonnegative(),
      lastRuleUpdate: z.string().datetime().optional(),
    })
    .optional(),
  message: z.string().optional(),
  error: ErrorDetailsSchema.optional(),
  metadata: ResponseMetadataSchema,
});
