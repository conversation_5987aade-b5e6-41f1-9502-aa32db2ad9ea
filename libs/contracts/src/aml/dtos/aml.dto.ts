import { z } from 'zod';
import {
  AccountInfoSchema,
  CustomerInfoSchema,
  CustomerRiskProfileSchema,
  ErrorDetailsSchema,
  EvaluateCustomerRiskRequestSchema,
  EvaluateCustomerRiskResponseSchema,
  EvaluateTransactionRiskRequestSchema,
  EvaluateTransactionRiskResponseSchema,
  GetAMLStatusRequestSchema,
  GetAMLStatusResponseSchema,
  GetRiskProfileRequestSchema,
  GetRiskProfileResponseSchema,
  RequestMetadataSchema,
  ResponseMetadataSchema,
  RiskAlertSchema,
  RiskEvaluationSchema,
  TransactionContextSchema,
  UpdateRiskRulesRequestSchema,
  UpdateRiskRulesResponseSchema,
} from '../schemas/aml.schema';

// ============================================================================
// COMMON DTOs
// ============================================================================

export type RequestMetadataDto = z.infer<typeof RequestMetadataSchema>;
export type ResponseMetadataDto = z.infer<typeof ResponseMetadataSchema>;
export type ErrorDetailsDto = z.infer<typeof ErrorDetailsSchema>;

export type AccountInfoDto = z.infer<typeof AccountInfoSchema>;
export type CustomerInfoDto = z.infer<typeof CustomerInfoSchema>;
export type TransactionContextDto = z.infer<typeof TransactionContextSchema>;

export type RiskAlertDto = z.infer<typeof RiskAlertSchema>;
export type RiskEvaluationDto = z.infer<typeof RiskEvaluationSchema>;
export type CustomerRiskProfileDto = z.infer<typeof CustomerRiskProfileSchema>;

// ============================================================================
// REQUEST DTOs
// ============================================================================

export type EvaluateTransactionRiskRequestDto = z.infer<typeof EvaluateTransactionRiskRequestSchema>;
export type EvaluateCustomerRiskRequestDto = z.infer<typeof EvaluateCustomerRiskRequestSchema>;
export type GetRiskProfileRequestDto = z.infer<typeof GetRiskProfileRequestSchema>;
export type UpdateRiskRulesRequestDto = z.infer<typeof UpdateRiskRulesRequestSchema>;
export type GetAMLStatusRequestDto = z.infer<typeof GetAMLStatusRequestSchema>;

// ============================================================================
// RESPONSE DTOs
// ============================================================================

export type EvaluateTransactionRiskResponseDto = z.infer<typeof EvaluateTransactionRiskResponseSchema>;
export type EvaluateCustomerRiskResponseDto = z.infer<typeof EvaluateCustomerRiskResponseSchema>;
export type GetRiskProfileResponseDto = z.infer<typeof GetRiskProfileResponseSchema>;
export type UpdateRiskRulesResponseDto = z.infer<typeof UpdateRiskRulesResponseSchema>;
export type GetAMLStatusResponseDto = z.infer<typeof GetAMLStatusResponseSchema>;

// ============================================================================
// INTERNAL DTOs (Additional fields for internal processing)
// ============================================================================

export interface InternalRiskEvaluationDto extends RiskEvaluationDto {
  tenantId: string;
  transactionId?: string;
  customerId?: string;
  processingTime: number;
  evaluationType: 'transaction' | 'customer' | 'periodic';
  evaluationModel?: string;
}

export interface InternalRiskAlertDto extends RiskAlertDto {
  id?: string;
  evaluationId: string;
  ruleId?: string;
  createdAt?: string;
}

export interface InternalCustomerRiskProfileDto extends CustomerRiskProfileDto {
  id?: string;
  tenantId: string;
  nextReviewDate?: string;
  createdAt?: string;
  updatedAt?: string;
}

// ============================================================================
// TRANSFORM METHODS
// ============================================================================

export class AMLDtoTransforms {
  /**
   * Transform proto request to internal DTO
   */
  static transformProtoToInternal<T>(protoData: any): T {
    // Handle timestamp conversions from proto format
    const transformed = { ...protoData };

    // Convert proto timestamps to ISO strings
    if (transformed.timestamp && typeof transformed.timestamp === 'object') {
      transformed.timestamp = new Date(transformed.timestamp.seconds * 1000 + Math.floor(transformed.timestamp.nanos / 1000000)).toISOString();
    }

    if (transformed.metadata?.timestamp && typeof transformed.metadata.timestamp === 'object') {
      transformed.metadata.timestamp = new Date(transformed.metadata.timestamp.seconds * 1000 + Math.floor(transformed.metadata.timestamp.nanos / 1000000)).toISOString();
    }

    return transformed as T;
  }

  /**
   * Transform internal DTO to proto response
   */
  static transformInternalToProto<T>(internalData: any): T {
    const transformed = { ...internalData };

    // Convert ISO strings to proto timestamps
    if (transformed.timestamp && typeof transformed.timestamp === 'string') {
      const date = new Date(transformed.timestamp);
      transformed.timestamp = {
        seconds: Math.floor(date.getTime() / 1000),
        nanos: (date.getTime() % 1000) * 1000000,
      };
    }

    if (transformed.metadata?.timestamp && typeof transformed.metadata.timestamp === 'string') {
      const date = new Date(transformed.metadata.timestamp);
      transformed.metadata.timestamp = {
        seconds: Math.floor(date.getTime() / 1000),
        nanos: (date.getTime() % 1000) * 1000000,
      };
    }

    return transformed as T;
  }

  /**
   * Create response metadata
   */
  static createResponseMetadata(requestId: string, processingTime: number): ResponseMetadataDto {
    return {
      requestId,
      timestamp: new Date().toISOString(),
      processingTime,
      version: '1.0',
      correlationId: requestId,
    };
  }

  /**
   * Create error response
   */
  static createErrorResponse(
    requestId: string,
    processingTime: number,
    error: { code: string; message: string; details?: string },
  ): {
    success: false;
    error: ErrorDetailsDto;
    metadata: ResponseMetadataDto;
  } {
    return {
      success: false,
      error,
      metadata: this.createResponseMetadata(requestId, processingTime),
    };
  }

  /**
   * Validate and transform request data
   */
  static validateAndTransform<T>(schema: z.ZodSchema<T>, data: unknown): T {
    try {
      return schema.parse(data);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation failed: ${error.issues.map((e) => e.message).join(', ')}`);
      }
      throw error;
    }
  }
}
