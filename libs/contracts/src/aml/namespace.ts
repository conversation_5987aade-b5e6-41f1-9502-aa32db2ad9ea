/* eslint-disable @typescript-eslint/no-namespace */
/**
 * AML namespace organization
 * Organizes all AML contracts into DTOs, Schemas, Interfaces, and Enums namespaces
 */

import * as AMLDTOs from './dtos';
import * as AMLSchemas from './schemas';
import * as AMLInterfaces from './interfaces';

export namespace AML {
  export namespace DTOs {
    // Common DTOs
    export type RequestMetadataDto = AMLDTOs.RequestMetadataDto;
    export type ResponseMetadataDto = AMLDTOs.ResponseMetadataDto;
    export type ErrorDetailsDto = AMLDTOs.ErrorDetailsDto;
    export type AccountInfoDto = AMLDTOs.AccountInfoDto;
    export type CustomerInfoDto = AMLDTOs.CustomerInfoDto;
    export type TransactionContextDto = AMLDTOs.TransactionContextDto;
    export type RiskAlertDto = AMLDTOs.RiskAlertDto;
    export type RiskEvaluationDto = AMLDTOs.RiskEvaluationDto;
    export type CustomerRiskProfileDto = AMLDTOs.CustomerRiskProfileDto;

    // Request DTOs
    export type EvaluateTransactionRiskRequestDto = AMLDTOs.EvaluateTransactionRiskRequestDto;
    export type EvaluateCustomerRiskRequestDto = AMLDTOs.EvaluateCustomerRiskRequestDto;
    export type GetRiskProfileRequestDto = AMLDTOs.GetRiskProfileRequestDto;
    export type UpdateRiskRulesRequestDto = AMLDTOs.UpdateRiskRulesRequestDto;
    export type GetAMLStatusRequestDto = AMLDTOs.GetAMLStatusRequestDto;

    // Response DTOs
    export type EvaluateTransactionRiskResponseDto = AMLDTOs.EvaluateTransactionRiskResponseDto;
    export type EvaluateCustomerRiskResponseDto = AMLDTOs.EvaluateCustomerRiskResponseDto;
    export type GetRiskProfileResponseDto = AMLDTOs.GetRiskProfileResponseDto;
    export type UpdateRiskRulesResponseDto = AMLDTOs.UpdateRiskRulesResponseDto;
    export type GetAMLStatusResponseDto = AMLDTOs.GetAMLStatusResponseDto;

    // Internal DTOs
    export type InternalRiskEvaluationDto = AMLDTOs.InternalRiskEvaluationDto;
    export type InternalRiskAlertDto = AMLDTOs.InternalRiskAlertDto;
    export type InternalCustomerRiskProfileDto = AMLDTOs.InternalCustomerRiskProfileDto;

    // Transform class
    export import AMLDtoTransforms = AMLDTOs.AMLDtoTransforms;
  }

  export namespace Schemas {
    // Common Schemas
    export import RequestMetadataSchema = AMLSchemas.RequestMetadataSchema;
    export import ResponseMetadataSchema = AMLSchemas.ResponseMetadataSchema;
    export import ErrorDetailsSchema = AMLSchemas.ErrorDetailsSchema;
    export import AccountInfoSchema = AMLSchemas.AccountInfoSchema;
    export import CustomerInfoSchema = AMLSchemas.CustomerInfoSchema;
    export import TransactionContextSchema = AMLSchemas.TransactionContextSchema;
    export import RiskAlertSchema = AMLSchemas.RiskAlertSchema;
    export import RiskEvaluationSchema = AMLSchemas.RiskEvaluationSchema;
    export import CustomerRiskProfileSchema = AMLSchemas.CustomerRiskProfileSchema;

    // Request Schemas
    export import EvaluateTransactionRiskRequestSchema = AMLSchemas.EvaluateTransactionRiskRequestSchema;
    export import EvaluateCustomerRiskRequestSchema = AMLSchemas.EvaluateCustomerRiskRequestSchema;
    export import GetRiskProfileRequestSchema = AMLSchemas.GetRiskProfileRequestSchema;
    export import UpdateRiskRulesRequestSchema = AMLSchemas.UpdateRiskRulesRequestSchema;
    export import GetAMLStatusRequestSchema = AMLSchemas.GetAMLStatusRequestSchema;

    // Response Schemas
    export import EvaluateTransactionRiskResponseSchema = AMLSchemas.EvaluateTransactionRiskResponseSchema;
    export import EvaluateCustomerRiskResponseSchema = AMLSchemas.EvaluateCustomerRiskResponseSchema;
    export import GetRiskProfileResponseSchema = AMLSchemas.GetRiskProfileResponseSchema;
    export import UpdateRiskRulesResponseSchema = AMLSchemas.UpdateRiskRulesResponseSchema;
    export import GetAMLStatusResponseSchema = AMLSchemas.GetAMLStatusResponseSchema;

    // Enum Schemas
    export import RiskLevelEnum = AMLSchemas.RiskLevelEnum;
    export import AlertTypeEnum = AMLSchemas.AlertTypeEnum;
    export import AlertSeverityEnum = AMLSchemas.AlertSeverityEnum;
    export import TransactionStatusEnum = AMLSchemas.TransactionStatusEnum;
    export import CustomerRiskCategoryEnum = AMLSchemas.CustomerRiskCategoryEnum;
  }

  export namespace Interfaces {
    // Risk Evaluation Engine
    export type IRiskEvaluationEngine = AMLInterfaces.IRiskEvaluationEngine;
    export type IRiskRule = AMLInterfaces.IRiskRule;
    export type IRiskRulesEngine = AMLInterfaces.IRiskRulesEngine;

    // Pattern Detection
    export type ITransactionPattern = AMLInterfaces.ITransactionPattern;
    export type IPatternDetectionEngine = AMLInterfaces.IPatternDetectionEngine;

    // Sanctions & Watchlist
    export type ISanctionsMatch = AMLInterfaces.ISanctionsMatch;
    export type ISanctionsEngine = AMLInterfaces.ISanctionsEngine;

    // Historical Analysis
    export type IHistoricalAnalysisEngine = AMLInterfaces.IHistoricalAnalysisEngine;

    // Audit & Compliance
    export type IAuditLogger = AMLInterfaces.IAuditLogger;

    // Configuration
    export type IAMLConfiguration = AMLInterfaces.IAMLConfiguration;

    // Main Service
    export type IAMLService = AMLInterfaces.IAMLService;
  }

  export namespace Enums {
    export const RiskLevel = {
      LOW: 'LOW',
      MEDIUM: 'MEDIUM',
      HIGH: 'HIGH',
      CRITICAL: 'CRITICAL',
    } as const;

    export const AlertType = {
      AMOUNT_THRESHOLD: 'AMOUNT_THRESHOLD',
      VELOCITY_CHECK: 'VELOCITY_CHECK',
      TIME_ANOMALY: 'TIME_ANOMALY',
      GEOGRAPHIC_ANOMALY: 'GEOGRAPHIC_ANOMALY',
      SANCTIONS_MATCH: 'SANCTIONS_MATCH',
      PEP_MATCH: 'PEP_MATCH',
      ADVERSE_MEDIA: 'ADVERSE_MEDIA',
      COMPLIANCE: 'COMPLIANCE',
      PATTERN_DETECTION: 'PATTERN_DETECTION',
      BLACKLIST_MATCH: 'BLACKLIST_MATCH',
    } as const;

    export const AlertSeverity = {
      LOW: 'LOW',
      MEDIUM: 'MEDIUM',
      HIGH: 'HIGH',
      CRITICAL: 'CRITICAL',
    } as const;

    export const TransactionStatus = {
      APPROVED: 'APPROVED',
      FLAGGED: 'FLAGGED',
      BLOCKED: 'BLOCKED',
      PENDING_REVIEW: 'PENDING_REVIEW',
    } as const;

    export const CustomerRiskCategory = {
      LOW: 'LOW',
      MEDIUM: 'MEDIUM',
      HIGH: 'HIGH',
      PROHIBITED: 'PROHIBITED',
    } as const;

    export type RiskLevel = typeof RiskLevel[keyof typeof RiskLevel];
    export type AlertType = typeof AlertType[keyof typeof AlertType];
    export type AlertSeverity = typeof AlertSeverity[keyof typeof AlertSeverity];
    export type TransactionStatus = typeof TransactionStatus[keyof typeof TransactionStatus];
    export type CustomerRiskCategory = typeof CustomerRiskCategory[keyof typeof CustomerRiskCategory];
  }
}
