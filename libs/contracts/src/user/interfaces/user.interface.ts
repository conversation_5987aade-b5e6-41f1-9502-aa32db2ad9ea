/**
 * User Interface
 *
 * Defines the core user entity interface for the Qeep platform.
 * This interface represents a user in the system with all associated properties.
 */

export interface IUser {
  /** Unique identifier for the user */
  id: string;

  /** User's email address (must be unique) */
  email: string;

  /** User's first name */
  firstName: string;

  /** User's last name */
  lastName: string;

  /** User's display name (optional, can be different from first+last) */
  displayName?: string;

  /** User's current status (active, inactive, suspended, etc.) */
  status: string;

  /** Whether the user's email has been verified */
  isEmailVerified: boolean;

  /** Whether the user's phone number has been verified */
  isPhoneVerified?: boolean;

  /** URL to the user's profile picture/avatar */
  avatarUrl?: string;

  /** User's preferred language code (e.g., 'en', 'fr') */
  preferredLanguage?: string;

  /** User's timezone (e.g., 'America/New_York') */
  timezone?: string;

  /** User's phone number (optional) */
  phoneNumber?: string;

  /** User's title/position (e.g., 'Software Engineer') */
  title?: string;

  /** User's department/team */
  department?: string;

  /** User's roles in the system */
  roles: string[];

  /** User's permissions (flattened from roles) */
  permissions: string[];

  /** User's tenant information */
  tenant?: {
    /** Tenant ID */
    id: string;

    /** Tenant code */
    code: string;

    /** Tenant name */
    name: string;
  };

  /** Last login timestamp */
  lastLoginAt?: Date | null;

  /** Last login IP address */
  lastLoginIp?: string | null;

  /** Last login user agent */
  lastLoginUserAgent?: string | null;

  /** Number of failed login attempts */
  loginAttempts: number;

  /** Account locked until timestamp */
  lockedUntil?: Date | null;

  /** Password last changed timestamp */
  passwordChangedAt?: Date | null;

  /** When the user was created */
  createdAt: Date;

  /** When the user was last updated */
  updatedAt: Date;

  /** When the user was deleted (soft delete) */
  deletedAt?: Date | null;

  /** Additional metadata */
  metadata?: Record<string, unknown>;
}

/**
 * User creation data interface
 * Used when creating a new user
 */
export interface ICreateUserData
  extends Omit<
    IUser,
    | 'id'
    | 'createdAt'
    | 'updatedAt'
    | 'deletedAt'
    | 'isEmailVerified'
    | 'isPhoneVerified'
    | 'loginAttempts'
    | 'lockedUntil'
    | 'lastLoginAt'
    | 'lastLoginIp'
    | 'lastLoginUserAgent'
    | 'passwordChangedAt'
    | 'metadata'
  > {
  /** User's password (only used during creation) */
  password: string;

  /** Whether to send a welcome email */
  sendWelcomeEmail?: boolean;
}

/**
 * User update data interface
 * Used when updating an existing user
 */
export type IUpdateUserData = Partial<Omit<ICreateUserData, 'email' | 'password'>> & {
  /** Whether to revoke all active sessions after update */
  revokeSessions?: boolean;
};

/**
 * User query filter interface
 * Used for filtering and searching users
 */
export interface IUserFilter {
  /** Search term (searches email, firstName, lastName, etc.) */
  search?: string;

  /** Filter by status */
  status?: string | string[];

  /** Filter by role */
  role?: string | string[];

  /** Filter by tenant ID */
  tenantId?: string;

  /** Pagination options */
  pagination?: {
    page: number;
    limit: number;
  };

  /** Sorting options */
  sort?: {
    field: keyof IUser;
    order: 'asc' | 'desc';
  };
}
