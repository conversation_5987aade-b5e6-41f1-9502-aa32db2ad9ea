/* eslint-disable @typescript-eslint/no-namespace */
/**
 * Transaction namespace organization
 * Organizes all transaction contracts into DTOs, Schemas, and Enums namespaces
 */

import * as TransactionDTOs from './dtos';
import * as TransactionEnums from './enums';
import * as TransactionSchemas from './schemas';

export namespace Transaction {
  export namespace DTOs {
    // Process Transaction DTOs
    export type AccountInfoDto = TransactionDTOs.AccountInfoDto;
    export type AlertDto = TransactionDTOs.AlertDto;
    export type ProcessTransactionRequestDto = TransactionDTOs.ProcessTransactionRequestDto;
    export type ProcessTransactionResponseDto = TransactionDTOs.ProcessTransactionResponseDto;
  }

  export namespace Schemas {
    // Process Transaction Schemas
    export import AccountInfoSchema = TransactionSchemas.AccountInfoSchema;
    export import AlertSchema = TransactionSchemas.AlertSchema;
    export import ProcessTransactionRequestSchema = TransactionSchemas.ProcessTransactionRequestSchema;
    export import ProcessTransactionResponseSchema = TransactionSchemas.ProcessTransactionResponseSchema;
  }

  export namespace Enums {
    export import TransactionStatus = TransactionEnums.TransactionStatus;
    export import TransactionAlertSeverity = TransactionEnums.TransactionAlertSeverity;
    export import TransactionAlertType = TransactionEnums.TransactionAlertType;
  }
}
