/**
 * Transaction processing status enum
 * Represents the outcome of real-time transaction evaluation
 */
export enum TransactionStatus {
  /** Transaction approved and can proceed */
  APPROVED = 'APPROVED',
  
  /** Transaction flagged for review but can proceed */
  FLAGGED = 'FLAGGED',
  
  /** Transaction blocked and cannot proceed */
  BLOCKED = 'BLOCKED'
}

/**
 * Alert severity levels for transaction monitoring
 */
export enum TransactionAlertSeverity {
  /** Low priority alert */
  LOW = 'LOW',
  
  /** Medium priority alert */
  MEDIUM = 'MEDIUM',
  
  /** High priority alert */
  HIGH = 'HIGH',
  
  /** Critical priority alert requiring immediate attention */
  CRITICAL = 'CRITICAL'
}

/**
 * Alert types for transaction monitoring
 */
export enum TransactionAlertType {
  /** Velocity check alert - transaction frequency limits */
  VELOCITY_CHECK = 'VELOCITY_CHECK',
  
  /** Amount threshold alert - transaction amount limits */
  AMOUNT_THRESHOLD = 'AMOUNT_THRESHOLD',
  
  /** Suspicious pattern alert - unusual transaction patterns */
  SUSPICIOUS_PATTERN = 'SUSPICIOUS_PATTERN',
  
  /** Geographic anomaly alert - unusual location */
  GEOGRAPHIC_ANOMALY = 'GEOGRAPHIC_ANOMALY',
  
  /** Time anomaly alert - unusual transaction time */
  TIME_ANOMALY = 'TIME_ANOMALY',
  
  /** Account risk alert - high-risk account involved */
  ACCOUNT_RISK = 'ACCOUNT_RISK',
  
  /** Compliance alert - regulatory compliance issues */
  COMPLIANCE = 'COMPLIANCE',
  
  /** Fraud alert - potential fraudulent activity */
  FRAUD = 'FRAUD'
}
