import { z } from 'zod';
import { TransactionStatus, TransactionAlertSeverity, TransactionAlertType } from '../enums/transaction-status.enum';

/**
 * Account information schema for transaction participants
 */
export const AccountInfoSchema = z.object({
  /** Unique identifier for the account */
  accountId: z.string().min(1, 'Account ID is required'),
  
  /** Customer ID who owns the account */
  customerId: z.string().min(1, 'Customer ID is required'),
  
  /** Type of account (e.g., CHECKING, SAVINGS, CREDIT) */
  accountType: z.string().min(1, 'Account type is required')
});

/**
 * Alert information schema for transaction monitoring alerts
 */
export const AlertSchema = z.object({
  /** Type of alert (e.g., VELOCITY_CHECK, AMOUNT_THRESHOLD) */
  type: z.enum(TransactionAlertType),
  
  /** Severity level of the alert */
  severity: z.enum(TransactionAlertSeverity),
  
  /** Human-readable alert message */
  message: z.string().min(1, 'Alert message is required')
});

/**
 * Process transaction request schema
 * Defines the structure for incoming transaction data from tenants
 */
export const ProcessTransactionRequestSchema = z.object({
  /** External transaction ID provided by the tenant */
  transactionId: z.string().min(1, 'Transaction ID is required'),
  
  /** Transaction amount in the specified currency */
  amount: z.number().positive('Amount must be positive'),
  
  /** ISO 4217 currency code (e.g., USD, EUR, GBP) */
  currency: z.string().length(3, 'Currency must be 3 characters'),
  
  /** Source account information */
  fromAccount: AccountInfoSchema,
  
  /** Destination account information */
  toAccount: AccountInfoSchema,
  
  /** ISO 8601 timestamp when the transaction occurred */
  timestamp: z.iso.datetime('Invalid timestamp format'),
  
  /** Optional transaction description */
  description: z.string().optional(),
  
  /** Optional metadata for additional transaction context */
  metadata: z.record(z.string(), z.any()).optional()
});

/**
 * Process transaction response schema
 * Defines the structure for synchronous transaction processing results
 */
export const ProcessTransactionResponseSchema = z.object({
  /** Indicates if the transaction processing was successful */
  success: z.boolean(),
  
  /** Internal Qeep transaction ID generated for this transaction */
  transactionId: z.string().min(1, 'Transaction ID is required'),
  
  /** Processing status of the transaction */
  status: z.enum(TransactionStatus),
  
  /** Risk score calculated for this transaction (0-100) */
  riskScore: z.number().min(0).max(100),
  
  /** Array of alerts generated during transaction evaluation */
  alerts: z.array(AlertSchema),
  
  /** Processing time in milliseconds */
  processingTime: z.number().nonnegative(),
  
  /** ISO 8601 timestamp when the response was generated */
  timestamp: z.iso.datetime('Invalid timestamp format')
});
