import { z } from 'zod';
import {
  AccountInfoSchema,
  AlertSchema,
  ProcessTransactionRequestSchema,
  ProcessTransactionResponseSchema
} from '../schemas/process-transaction.schema';

/**
 * Account information DTO inferred from schema
 */
export type AccountInfoDto = z.infer<typeof AccountInfoSchema>;

/**
 * Alert information DTO inferred from schema
 */
export type AlertDto = z.infer<typeof AlertSchema>;

/**
 * Process transaction request DTO inferred from schema
 */
export type ProcessTransactionRequestDto = z.infer<typeof ProcessTransactionRequestSchema>;

/**
 * Process transaction response DTO inferred from schema
 */
export type ProcessTransactionResponseDto = z.infer<typeof ProcessTransactionResponseSchema>;
