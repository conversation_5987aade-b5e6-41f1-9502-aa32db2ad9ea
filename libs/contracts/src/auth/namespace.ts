/* eslint-disable @typescript-eslint/no-namespace */
/**
 * Auth namespace organization
 * Organizes all auth contracts into DTOs, Interfaces, and Enums namespaces
 */

import * as AuthDTOs from './dtos';
import * as AuthEnums from './enums';
import * as AuthInterfaces from './interfaces';
import * as AuthSchemas from './schemas';

export namespace Auth {
  export namespace DTOs {
    // Account Management DTOs
    export type SignupRequestDto = AuthDTOs.SignupRequestDto;
    export type SignupResponseDto = AuthDTOs.SignupResponseDto;
    export type SignupInternalDto = AuthDTOs.SignupInternalDto;
    export type EmailVerificationRequestDto = AuthDTOs.EmailVerificationRequestDto;
    export type EmailVerificationResponseDto = AuthDTOs.EmailVerificationResponseDto;
    export type ResendVerificationRequestDto = AuthDTOs.ResendVerificationRequestDto;
    export type CheckEmailAvailabilityRequestDto = AuthDTOs.CheckEmailAvailabilityRequestDto;
    export type CheckEmailAvailabilityResponseDto = AuthDTOs.CheckEmailAvailabilityResponseDto;
    export type UnlockAccountRequestDto = AuthDTOs.UnlockAccountRequestDto;
    export type UnlockAccountResponseDto = AuthDTOs.UnlockAccountResponseDto;

    // User DTOs
    export type UserResponseDto = AuthDTOs.UserResponseDto;
    export type MinimalUserResponseDto = AuthDTOs.MinimalUserResponseDto;
    export type UserInternalDto = AuthDTOs.UserInternalDto;
    export type MinimalUserInternalDto = AuthDTOs.MinimalUserInternalDto;

    // Session DTOs
    export type SessionDataDto = AuthDTOs.SessionDataDto;
    export type GetUserSessionsResponseDto = AuthDTOs.GetUserSessionsResponseDto;
    export type TerminateSessionRequestDto = AuthDTOs.TerminateSessionRequestDto;
    export type TerminateSessionResponseDto = AuthDTOs.TerminateSessionResponseDto;
    export type TerminateAllSessionsResponseDto = AuthDTOs.TerminateAllSessionsResponseDto;
    export type CurrentSessionResponseDto = AuthDTOs.CurrentSessionResponseDto;
    export type SessionInternalDto = AuthDTOs.SessionInternalDto;

    // Session Service Request DTOs
    export type CreateSessionRequestDto = AuthDTOs.CreateSessionRequestDto;
    export type GetUserSessionsRequestDto = AuthDTOs.GetUserSessionsRequestDto;
    export type GetSessionByTokenRequestDto = AuthDTOs.GetSessionByTokenRequestDto;
    export type UpdateSessionActivityRequestDto = AuthDTOs.UpdateSessionActivityRequestDto;
    export type TerminateUserSessionRequestDto = AuthDTOs.TerminateUserSessionRequestDto;
    export type TerminateSessionByTokenRequestDto = AuthDTOs.TerminateSessionByTokenRequestDto;
    export type TerminateUserSessionsByDeviceRequestDto = AuthDTOs.TerminateUserSessionsByDeviceRequestDto;
    export type TerminateAllUserSessionsRequestDto = AuthDTOs.TerminateAllUserSessionsRequestDto;

    // Authentication DTOs
    export type LogoutRequestDto = AuthDTOs.LogoutRequestDto;
    export type LoginRequestDto = AuthDTOs.LoginRequestDto;
    export type TokenRefreshRequestDto = AuthDTOs.TokenRefreshRequestDto;
    export type LoginResponseDto = AuthDTOs.LoginResponseDto;
    export type TokenRefreshResponseDto = AuthDTOs.TokenRefreshResponseDto;
    export type LogoutResponseDto = AuthDTOs.LogoutResponseDto;
    export type RevokeTokenRequestDto = AuthDTOs.RevokeTokenRequestDto;
    export type RevokeTokenResponseDto = AuthDTOs.RevokeTokenResponseDto;
    export type RevokeAllTokensResponseDto = AuthDTOs.RevokeAllTokensResponseDto;
    export type LoginInternalDto = AuthDTOs.LoginInternalDto;

    // Device Management DTOs
    export type DeviceInfoDto = AuthDTOs.DeviceInfoDto;
    export type RegisterTrustedDeviceDto = AuthDTOs.RegisterTrustedDeviceDto;
    export type UpdateTrustedDeviceDto = AuthDTOs.UpdateTrustedDeviceDto;
    export type CheckDeviceTrustDto = AuthDTOs.CheckDeviceTrustDto;
    export type TrustedDeviceDto = AuthDTOs.TrustedDeviceDto;
    export type DeviceTrustStatusDto = AuthDTOs.DeviceTrustStatusDto;

    // Password Management DTOs
    export type ForgotPasswordRequestDto = AuthDTOs.ForgotPasswordRequestDto;
    export type ForgotPasswordResponseDto = AuthDTOs.ForgotPasswordResponseDto;
    export type ResetPasswordRequestDto = AuthDTOs.ResetPasswordRequestDto;
    export type ResetPasswordResponseDto = AuthDTOs.ResetPasswordResponseDto;
    export type ChangePasswordRequestDto = AuthDTOs.ChangePasswordRequestDto;
    export type ChangePasswordResponseDto = AuthDTOs.ChangePasswordResponseDto;

    // MFA DTOs
    export type MfaSetupRequestDto = AuthDTOs.MfaSetupRequestDto;
    export type MfaSetupResponseDto = AuthDTOs.MfaSetupResponseDto;
    export type MfaVerificationRequestDto = AuthDTOs.MfaVerificationRequestDto;
    export type MfaVerificationResponseDto = AuthDTOs.MfaVerificationResponseDto;
    export type MfaChallengeRequestDto = AuthDTOs.MfaChallengeRequestDto;
    export type MfaChallengeResponseDto = AuthDTOs.MfaChallengeResponseDto;
    export type MfaDisableRequestDto = AuthDTOs.MfaDisableRequestDto;
    export type MfaStatusResponseDto = AuthDTOs.MfaStatusResponseDto;
    export type RegenerateBackupCodesRequestDto = AuthDTOs.RegenerateBackupCodesRequestDto;
    export type RegenerateBackupCodesResponseDto = AuthDTOs.RegenerateBackupCodesResponseDto;
    export type VerifyMfaChallengeRequestDto = AuthDTOs.VerifyMfaChallengeRequestDto;
    export type VerifyMfaChallengeResponseDto = AuthDTOs.VerifyMfaChallengeResponseDto;
  }

  export namespace Schemas {
    // Account Management
    export import CheckEmailAvailabilityRequestSchema = AuthSchemas.CheckEmailAvailabilityRequestSchema;
    export import CheckEmailAvailabilityResponseSchema = AuthSchemas.CheckEmailAvailabilityResponseSchema;
    export import SignupRequestSchema = AuthSchemas.SignupRequestSchema;
    export import SignupResponseSchema = AuthSchemas.SignupResponseSchema;
    export import EmailVerificationRequestSchema = AuthSchemas.EmailVerificationRequestSchema;
    export import ResendVerificationRequestSchema = AuthSchemas.ResendVerificationRequestSchema;

    // User Schemas
    export import MinimalUserResponseSchema = AuthSchemas.MinimalUserResponseSchema;

    // Session Schemas
    export import TerminateSessionRequestSchema = AuthSchemas.TerminateSessionRequestSchema;
    export import TerminateAllSessionsRequestSchema = AuthSchemas.TerminateAllSessionsRequestSchema;
    export import CreateSessionRequestSchema = AuthSchemas.SessionSchema;
    export import GetUserSessionsRequestSchema = AuthSchemas.SessionSchema;
    export import GetSessionByTokenRequestSchema = AuthSchemas.SessionSchema;
    export import UpdateSessionActivityRequestSchema = AuthSchemas.SessionSchema;
    export import TerminateUserSessionRequestSchema = AuthSchemas.SessionSchema;
    export import TerminateSessionByTokenRequestSchema = AuthSchemas.SessionSchema;
    export import TerminateUserSessionsByDeviceRequestSchema = AuthSchemas.SessionSchema;
    export import TerminateAllUserSessionsRequestSchema = AuthSchemas.SessionSchema;

    // Password Management Schemas
    export import ForgotPasswordRequestSchema = AuthSchemas.ForgotPasswordRequestSchema;
    export import ForgotPasswordResponseSchema = AuthSchemas.ForgotPasswordResponseSchema;
    export import ResetPasswordRequestSchema = AuthSchemas.ResetPasswordRequestSchema;
    export import ResetPasswordResponseSchema = AuthSchemas.ResetPasswordResponseSchema;
    export import ChangePasswordRequestSchema = AuthSchemas.ChangePasswordRequestSchema;
    export import ChangePasswordResponseSchema = AuthSchemas.ChangePasswordResponseSchema;

    // Authentication Schemas
    export import LoginRequestSchema = AuthSchemas.LoginRequestSchema;
    export import LoginResponseSchema = AuthSchemas.LoginResponseSchema;
    export import LogoutRequestSchema = AuthSchemas.LogoutRequestSchema;
    export import LogoutResponseSchema = AuthSchemas.LogoutResponseSchema;
    export import TokenRefreshRequestSchema = AuthSchemas.TokenRefreshRequestSchema;
    export import TokenRefreshResponseSchema = AuthSchemas.TokenRefreshResponseSchema;
    export import RevokeTokenRequestSchema = AuthSchemas.RevokeTokenRequestSchema;
    export import RevokeTokenResponseSchema = AuthSchemas.RevokeTokenResponseSchema;
    export import RevokeAllTokensResponseSchema = AuthSchemas.RevokeAllTokensResponseSchema;

    // Device Management Schemas
    export import DeviceInfoSchema = AuthSchemas.DeviceInfoSchema;
    export import RegisterTrustedDeviceSchema = AuthSchemas.RegisterTrustedDeviceSchema;
    export import UpdateTrustedDeviceSchema = AuthSchemas.UpdateTrustedDeviceSchema;
    export import CheckDeviceTrustSchema = AuthSchemas.CheckDeviceTrustSchema;
    export import TrustedDeviceSchema = AuthSchemas.TrustedDeviceSchema;
    export import DeviceTrustStatusSchema = AuthSchemas.DeviceTrustStatusSchema;

    // MFA Schemas
    export import MfaSetupRequestSchema = AuthSchemas.MfaSetupRequestSchema;
    export import MfaSetupResponseSchema = AuthSchemas.MfaSetupResponseSchema;
    export import MfaVerificationRequestSchema = AuthSchemas.MfaVerificationRequestSchema;
    export import MfaVerificationResponseSchema = AuthSchemas.MfaVerificationResponseSchema;
    export import MfaChallengeRequestSchema = AuthSchemas.MfaChallengeRequestSchema;
    export import MfaChallengeResponseSchema = AuthSchemas.MfaChallengeResponseSchema;
    export import MfaDisableRequestSchema = AuthSchemas.MfaDisableRequestSchema;
    export import MfaStatusResponseSchema = AuthSchemas.MfaStatusResponseSchema;
    export import RegenerateBackupCodesRequestSchema = AuthSchemas.RegenerateBackupCodesRequestSchema;
    export import RegenerateBackupCodesResponseSchema = AuthSchemas.RegenerateBackupCodesResponseSchema;
    export import VerifyMfaChallengeRequestSchema = AuthSchemas.VerifyMfaChallengeRequestSchema;
    export import VerifyMfaChallengeResponseSchema = AuthSchemas.VerifyMfaChallengeResponseSchema;
  }

  export namespace Enums {
    export import AuthStatus = AuthEnums.AuthStatus;
    export import SessionStatus = AuthEnums.SessionStatus;
    export import DeviceStatus = AuthDTOs.DeviceStatus;
    export import MfaMethod = AuthEnums.MfaMethod;
    export import AuthEventType = AuthEnums.AuthEventType;
    export import SignupAuditAction = AuthEnums.SignupAuditAction;
    export import LoginAuditAction = AuthEnums.LoginAuditAction;
    export import AuditResult = AuthEnums.AuditResult;
    export import OnboardingStep = AuthEnums.OnboardingStep;
    export import EmailTriggerType = AuthEnums.EmailTriggerType;
    export import EmailTemplateType = AuthEnums.EmailTemplateType;
    export import EmailVerificationAuditAction = AuthEnums.EmailVerificationAuditAction;
    export import AccountManagementAuditAction = AuthEnums.AccountManagementAuditAction;
    export import AuthErrorCode = AuthEnums.AuthErrorCode;
  }

  export namespace Interfaces {
    export type AuthUser = AuthInterfaces.AuthUser;
    export type AuthSession = AuthInterfaces.AuthSession;
    export type JwtPayload = AuthInterfaces.JwtPayload;
    export type MfaConfig = AuthInterfaces.MfaConfig;
    export type MfaUserData = AuthInterfaces.MfaUserData;
    export type MfaVerificationResult = AuthInterfaces.MfaVerificationResult;
    export type PasswordPolicy = AuthInterfaces.PasswordPolicy;
    export type IAuthService = AuthInterfaces.IAuthService;
    export type LoginOptions = AuthInterfaces.LoginOptions;
    export type AuthResult = AuthInterfaces.AuthResult;
    export type TokenResult = AuthInterfaces.TokenResult;
  }
}
