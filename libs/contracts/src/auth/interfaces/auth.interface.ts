import { <PERSON><PERSON><PERSON>eth<PERSON>, SessionStatus } from '../enums';

/**
 * Base user information interface
 * @deprecated Use UserInternalDto from user.dto instead
 * This is kept for backward compatibility and will be removed in a future version
 */
export interface AuthUser {
  /** Unique user identifier */
  id: string;

  /** User's email address */
  email: string;

  /** User's first name */
  firstName: string;

  /** User's last name */
  lastName: string;

  /** User's current status */
  status: string;

  /** Whether the user's email is verified */
  isEmailVerified: boolean;

  /** Tenant code the user belongs to */
  tenantCode?: string;

  /** User's assigned roles */
  roles?: string[];

  /** Tenant information */
  tenant?: {
    id: string;
    code: string;
    name: string;
  };

  /** Last login timestamp */
  lastLoginAt?: Date | null;

  /** Last login IP address */
  lastLoginIp?: string | null;

  /** Last login user agent */
  lastLoginUserAgent?: string | null;

  /** Number of failed login attempts */
  loginAttempts: number;

  /** Account locked until timestamp */
  lockedUntil?: Date | null;

  /** Password last changed timestamp */
  passwordChangedAt?: Date | null;

  /** Account creation timestamp */
  createdAt: Date;

  /** Account last update timestamp */
  updatedAt: Date;
}

/**
 * Authentication session interface
 * Represents an active user session
 */
export interface AuthSession {
  /** Unique session identifier */
  id: string;

  /** User ID associated with the session */
  userId: string;

  /** Session status */
  status: SessionStatus;

  /** Device identifier */
  deviceId?: string;

  /** IP address where session was created */
  ipAddress?: string;

  /** User agent string */
  userAgent?: string;

  /** Session creation timestamp */
  createdAt: Date;

  /** Session expiration timestamp */
  expiresAt: Date;

  /** Last activity timestamp */
  lastActivityAt: Date;

  /** Whether this is a remembered session */
  isRemembered: boolean;
}

/**
 * JWT token payload interface
 * Defines the structure of JWT token claims
 */
export interface JwtPayload {
  /** Subject (user ID) */
  sub: string;

  /** User's email */
  email: string;

  /** Tenant code */
  tenantCode?: string;

  /** User roles */
  roles?: string[];

  /** Session ID */
  sessionId: string;

  /** Issued at timestamp */
  iat: number;

  /** Expiration timestamp */
  exp: number;

  /** Issuer */
  iss: string;

  /** Audience */
  aud: string;
}

/**
 * Multi-factor authentication configuration interface
 */
export interface MfaConfig {
  /** Whether MFA is enabled */
  enabled: boolean;

  /** Primary MFA method */
  primaryMethod?: MfaMethod;

  /** Backup MFA methods */
  backupMethods?: MfaMethod[];

  /** Phone number for SMS MFA */
  phoneNumber?: string;

  /** Email for email-based MFA */
  email?: string;

  /** TOTP secret (encrypted) */
  totpSecret?: string;

  /** Backup codes (encrypted) */
  backupCodes?: string[];

  /** MFA setup completion timestamp */
  setupAt?: Date;

  /** Last MFA verification timestamp */
  lastVerifiedAt?: Date;
}

/**
 * Password policy interface
 * Defines password requirements and constraints
 */
export interface PasswordPolicy {
  /** Minimum password length */
  minLength: number;

  /** Maximum password length */
  maxLength: number;

  /** Require uppercase letters */
  requireUppercase: boolean;

  /** Require lowercase letters */
  requireLowercase: boolean;

  /** Require numbers */
  requireNumbers: boolean;

  /** Require special characters */
  requireSpecialChars: boolean;

  /** Prevent password reuse (number of previous passwords to check) */
  preventReuse: number;

  /** Password expiration days */
  expirationDays?: number;

  /** Maximum failed attempts before lockout */
  maxFailedAttempts: number;

  /** Lockout duration in minutes */
  lockoutDuration: number;
}

/**
 * Authentication service interface
 * Defines the contract for authentication services
 */
export interface IAuthService {
  /**
   * Authenticate user with email and password
   */
  login(email: string, password: string, options?: LoginOptions): Promise<AuthResult>;

  /**
   * Refresh authentication tokens
   */
  refreshToken(refreshToken: string): Promise<TokenResult>;

  /**
   * Logout user and invalidate session
   */
  logout(sessionId: string): Promise<void>;

  /**
   * Validate JWT token
   */
  validateToken(token: string): Promise<JwtPayload>;

  /**
   * Change user password
   */
  changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void>;

  /**
   * Request password reset
   */
  requestPasswordReset(email: string): Promise<void>;

  /**
   * Reset password with token
   */
  resetPassword(token: string, newPassword: string): Promise<void>;
}

/**
 * Login options interface
 */
export interface LoginOptions {
  /** Remember the session */
  rememberMe?: boolean;

  /** Device identifier */
  deviceId?: string;

  /** IP address */
  ipAddress?: string;

  /** User agent */
  userAgent?: string;

  /** Tenant code */
  tenantCode?: string;
}

/**
 * Authentication result interface
 */
export interface AuthResult {
  /** Whether authentication was successful */
  success: boolean;

  /** Access token */
  accessToken?: string;

  /** Refresh token */
  refreshToken?: string;

  /** Token expiration time in seconds */
  expiresIn?: number;

  /** User information */
  user?: AuthUser;

  /** Whether MFA is required */
  mfaRequired?: boolean;

  /** MFA challenge token */
  mfaToken?: string;

  /** Error message if authentication failed */
  error?: string;
}

/**
 * Token result interface
 */
export interface TokenResult {
  /** Access token */
  accessToken: string;

  /** Refresh token */
  refreshToken?: string;

  /** Token expiration time in seconds */
  expiresIn: number;
}
