import { <PERSON><PERSON><PERSON>ethod } from '../enums';

/**
 * Multi-Factor Authentication Interfaces
 * Defines interfaces for MFA-related data structures
 */

/**
 * MFA user data interface
 * Represents the MFA configuration and state for a user
 */
export interface MfaUserData {
  /** User ID this MFA data belongs to */
  userId: string;

  /** MFA method configured for the user */
  method: MfaMethod;

  /** TOTP secret (for TOTP method) */
  secret?: string;

  /** Phone number (for SMS method) */
  phoneNumber?: string;

  /** Email address (for EMAIL method) */
  email?: string;

  /** Backup codes for account recovery */
  backupCodes: string[];

  /** Whether MFA is enabled and active */
  isEnabled: boolean;

  /** Last time MFA was used for authentication */
  lastUsed?: Date;

  /** When MFA was first set up */
  setupAt?: Date;

  /** Number of failed MFA attempts */
  failedAttempts?: number;

  /** When the user was last locked out due to failed MFA attempts */
  lockedUntil?: Date;
}

/**
 * MFA verification result interface
 * Represents the result of an MFA verification attempt
 */
export interface MfaVerificationResult {
  /** User ID that was verified */
  userId: string;

  /** Whether the verification was successful */
  isValid: boolean;

  /** Remaining verification attempts (if failed) */
  remainingAttempts?: number;

  /** Whether the account is now locked due to too many failed attempts */
  isLocked?: boolean;

  /** When the account will be unlocked (if locked) */
  unlockedAt?: Date;

  /** The method that was used for verification */
  method: MfaMethod;

  /** Whether a backup code was used */
  usedBackupCode?: boolean;
}
