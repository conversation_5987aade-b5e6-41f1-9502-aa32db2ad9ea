/**
 * Authentication Event Enums
 * 
 * Defines enums related to authentication events and audit logging
 */

/**
 * Authentication event types for audit logging
 */
export enum AuthEventType {
  /** User login attempt */
  LOGIN_ATTEMPT = 'LOGIN_ATTEMPT',

  /** Successful user login */
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',

  /** Failed user login */
  LOGIN_FAILURE = 'LOGIN_FAILURE',

  /** User logout */
  LOGOUT = 'LOGOUT',

  /** Password change */
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',

  /** Password reset request */
  PASSWORD_RESET_REQUEST = 'PASSWORD_RESET_REQUEST',

  /** Password reset completion */
  PASSWORD_RESET_COMPLETE = 'PASSWORD_RESET_COMPLETE',

  /** Account locked */
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',

  /** Account unlocked */
  ACCOUNT_UNLOCKED = 'ACCOUNT_UNLOCKED',

  /** MFA setup */
  MFA_SETUP = 'MFA_SETUP',

  /** MFA verification */
  MFA_VERIFICATION = 'MFA_VERIFICATION',
}

/**
 * Audit result types
 */
export enum AuditResult {
  /** Operation is pending */
  PENDING = 'PENDING',

  /** Operation succeeded */
  SUCCESS = 'SUCCESS',

  /** Operation failed */
  FAILURE = 'FAILURE',
}
