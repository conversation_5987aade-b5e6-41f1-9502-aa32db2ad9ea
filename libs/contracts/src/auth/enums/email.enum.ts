/**
 * Email Enums
 * 
 * Defines enums related to email functionality in authentication
 */

/**
 * Email trigger types
 */
export enum EmailTriggerType {
  /** Post email verification trigger */
  POST_VERIFICATION = 'POST_VERIFICATION',

  /** Welcome email trigger */
  WELCOME = 'WELCOME',

  /** Password reset trigger */
  PASSWORD_RESET = 'PASSWORD_RESET',

  /** Account locked trigger */
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
}

/**
 * Email template types
 */
export enum EmailTemplateType {
  /** Post verification welcome email */
  POST_VERIFICATION_WELCOME = 'POST_VERIFICATION_WELCOME',

  /** General welcome email */
  WELCOME = 'WELCOME',

  /** Email verification */
  EMAIL_VERIFICATION = 'EMAIL_VERIFICATION',

  /** Password reset */
  PASSWORD_RESET = 'PASSWORD_RESET',
}
