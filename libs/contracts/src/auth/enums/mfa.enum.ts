/**
 * Multi-Factor Authentication Enums
 *
 * Defines enums related to MFA functionality
 */

/**
 * Multi-factor authentication method enumeration
 */
export enum MfaMethod {
  /** Time-based One-Time Password (TOTP) */
  TOTP = 'TOTP',

  /** SMS-based verification */
  SMS = 'SMS',

  /** Email-based verification */
  EMAIL = 'EMAIL',

  /** Hardware security key */
  HARDWARE_KEY = 'HARDWARE_KEY',
}
