/**
 * Core Authentication Enums
 * 
 * Defines the core enums used in the authentication domain
 */

/**
 * Authentication status enumeration
 * Represents the various states of user authentication and account status
 */
export enum AuthStatus {
  /** User account is active and can authenticate */
  ACTIVE = 'ACTIVE',

  /** User account is inactive (temporarily disabled) */
  INACTIVE = 'INACTIVE',

  /** User account is suspended (disciplinary action) */
  SUSPENDED = 'SUSPENDED',

  /** User account is pending email verification */
  PENDING = 'PENDING',

  /** User account is locked due to security reasons */
  LOCKED = 'LOCKED',

  /** User account has been permanently deleted */
  DELETED = 'DELETED',
}

/**
 * Session status enumeration
 * Represents the various states of user sessions
 */
export enum SessionStatus {
  /** Session is currently active */
  ACTIVE = 'ACTIVE',

  /** Session has expired */
  EXPIRED = 'EXPIRED',

  /** Session has been revoked */
  REVOKED = 'REVOKED',

  /** Session is suspended */
  SUSPENDED = 'SUSPENDED',
}

/**
 * Authentication error codes
 */
export enum AuthErrorCode {
  /** Account is temporarily locked */
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',

  /** Invalid login credentials */
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',

  /** Account is not active */
  ACCOUNT_INACTIVE = 'ACCOUNT_INACTIVE',

  /** Email not verified */
  EMAIL_NOT_VERIFIED = 'EMAIL_NOT_VERIFIED',

  /** Invalid or expired token */
  INVALID_TOKEN = 'INVALID_TOKEN',

  /** Token expired */
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',

  /** Insufficient permissions */
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
}
