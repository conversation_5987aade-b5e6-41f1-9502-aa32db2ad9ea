import {
  CheckEmailAvailabilityRequest,
  CheckEmailAvailabilityResponse,
  EmailVerificationRequest,
  EmailVerificationResponse,
  ResendVerificationRequest,
  ResendVerificationResponse,
  SignupRequest,
  SignupResponse,
  UnlockUserAccountRequest,
  UnlockUserAccountResponse,
} from '../schemas/account-management.schema';

export type SignupRequestDto = SignupRequest;
export type SignupResponseDto = SignupResponse;

/**
 * Internal signup DTO (camelCase for internal processing)
 */
export interface SignupInternalDto extends SignupRequestDto {
  /** IP address */
  ipAddress?: string;

  /** User agent */
  userAgent?: string;

  /** Device ID */
  deviceId?: string;
}

/**
 * Email verification request DTO
 */
export type EmailVerificationRequestDto = EmailVerificationRequest;
export type EmailVerificationResponseDto = EmailVerificationResponse;

/**
 * Resend verification email request DTO
 */
export type ResendVerificationRequestDto = ResendVerificationRequest;
export type ResendVerificationResponseDto = ResendVerificationResponse;

/**
 * Check email availability request DTO
 */
export type CheckEmailAvailabilityRequestDto = CheckEmailAvailabilityRequest;
export type CheckEmailAvailabilityResponseDto = CheckEmailAvailabilityResponse;

/**
 * Unlock user account request DTO
 */
export type UnlockUserAccountRequestDto = UnlockUserAccountRequest;
export type UnlockUserAccountResponseDto = UnlockUserAccountResponse;
