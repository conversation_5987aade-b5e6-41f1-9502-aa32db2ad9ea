/**
 * Password Management DTOs
 * TypeScript types for password-related operations
 */

import {
  ChangePasswordRequest,
  ChangePasswordResponse,
  ForgotPasswordRequest,
  ForgotPasswordResponse,
  GenerateTokensForUserRequest,
  PasswordManagementResponse,
  ResetPasswordRequest,
  ResetPasswordResponse,
  ValidateResetTokenRequest,
  ValidateResetTokenResponse,
  VerifyUserPasswordRequest,
} from '../schemas';

export type ForgotPasswordRequestDto = ForgotPasswordRequest;
export type ForgotPasswordResponseDto = ForgotPasswordResponse;
export type ResetPasswordRequestDto = ResetPasswordRequest;
export type ResetPasswordResponseDto = ResetPasswordResponse;
export type ChangePasswordRequestDto = ChangePasswordRequest;
export type ChangePasswordResponseDto = ChangePasswordResponse;
export type ValidateResetTokenRequestDto = ValidateResetTokenRequest;
export type ValidateResetTokenResponseDto = ValidateResetTokenResponse;
export type VerifyUserPasswordRequestDto = VerifyUserPasswordRequest;
export type GenerateTokensForUserRequestDto = GenerateTokensForUserRequest;
export type PasswordManagementResponseDto = PasswordManagementResponse;
