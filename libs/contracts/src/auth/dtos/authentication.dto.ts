import {
  LoginRequest,
  LoginResponse,
  LogoutRequest,
  LogoutResponse,
  RevokeAllTokensResponse,
  RevokeTokenRequest,
  RevokeTokenResponse,
  TokenRefreshRequest,
  TokenRefreshResponse,
} from '../schemas/authentication.schema';

// Type aliases for service layer
export type LoginInternalDto = LoginRequestDto & { ipAddress?: string; userAgent?: string };

// Infer types from additional schemas
export type LogoutResponseDto = LogoutResponse;
export type LogoutRequestDto = LogoutRequest;

export type RevokeTokenRequestDto = RevokeTokenRequest;
export type RevokeTokenResponseDto = RevokeTokenResponse;

export type RevokeAllTokensResponseDto = RevokeAllTokensResponse;

export type LoginRequestDto = LoginRequest;
export type LoginResponseDto = LoginResponse;

export type TokenRefreshRequestDto = TokenRefreshRequest;
export type TokenRefreshResponseDto = TokenRefreshResponse;
