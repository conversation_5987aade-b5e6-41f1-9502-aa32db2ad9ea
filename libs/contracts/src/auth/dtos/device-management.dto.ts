import {
  CheckDeviceTrustRequest,
  DeviceInfoRequest,
  DeviceStatus,
  DeviceTrustStatusRequest,
  GetTrustedDeviceResponse,
  ListTrustedDevicesQueryRequest,
  // Import response types
  ListTrustedDevicesResponse,
  RegisterTrustedDeviceResponse,
  RegisterTrustedDeviceRequest,
  RevokeAllTrustedDevicesResponse,
  RevokeTrustedDeviceResponse,
  TrustedDeviceRequest,
  UpdateTrustedDeviceResponse,
  UpdateTrustedDeviceRequest,
} from '../schemas/device-management.schema';

/**
 * Type definitions derived from device management schemas
 */

/**
 * Device information DTO for device fingerprinting
 * - userAgent: Browser/client user agent string
 * - screenResolution: User's screen resolution (e.g. 1920x1080)
 * - platform: Operating system or platform identifier
 * - timezone: User's timezone
 * - language: User's preferred language
 * - ipAddress: User's IP address
 */
export type DeviceInfoDto = DeviceInfoRequest;

/**
 * DTO for registering a trusted device
 * - deviceName: Custom name for the device (optional)
 * - deviceType: Type of device (desktop, mobile, tablet, etc.) (optional)
 * - deviceInfo: Information for device fingerprinting
 */
export type RegisterTrustedDeviceDto = RegisterTrustedDeviceRequest;

/**
 * DTO for updating an existing trusted device
 * - deviceName: Updated custom name for the device (optional)
 * - status: Updated device status (optional)
 */
export type UpdateTrustedDeviceDto = UpdateTrustedDeviceRequest;

/**
 * DTO for checking if a device is trusted
 * - deviceInfo: Information for device fingerprinting
 */
export type CheckDeviceTrustDto = CheckDeviceTrustRequest;

/**
 * DTO representing a trusted device entity
 * - id: Unique identifier for the trusted device record
 * - deviceId: Unique device identifier generated from device fingerprint
 * - deviceName: Custom name assigned to the device
 * - deviceType: Type of device (desktop, mobile, tablet, etc.)
 * - operatingSystem: Device operating system
 * - browser: Browser used on the device
 * - status: Current status of the trusted device
 * - lastUsedAt: Timestamp of last authentication with this device
 * - trustedAt: Timestamp when device was marked as trusted
 * - expiresAt: Timestamp when device trust expires
 */
export type TrustedDeviceDto = TrustedDeviceRequest;

/**
 * DTO for device trust status check response
 * - isTrusted: Whether the device is currently trusted
 * - deviceId: Device identifier (if trusted)
 */
export type DeviceTrustStatusDto = DeviceTrustStatusRequest;
export type ListTrustedDevicesQueryDto = ListTrustedDevicesQueryRequest;

// Re-export response DTOs for controllers
export type ListTrustedDevicesResponseDto = ListTrustedDevicesResponse;
export type GetTrustedDeviceResponseDto = GetTrustedDeviceResponse;
export type RegisterTrustedDeviceResponseDto = RegisterTrustedDeviceResponse;
export type UpdateTrustedDeviceResponseDto = UpdateTrustedDeviceResponse;
export type RevokeTrustedDeviceResponseDto = RevokeTrustedDeviceResponse;
export type RevokeAllTrustedDevicesResponseDto = RevokeAllTrustedDevicesResponse;

/**
 * Re-export DeviceStatus enum
 */
export { DeviceStatus };
