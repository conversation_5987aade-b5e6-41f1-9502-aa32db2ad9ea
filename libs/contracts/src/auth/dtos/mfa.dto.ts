/**
 * Multi-Factor Authentication DTOs
 * TypeScript types for MFA operations
 */

import {
  MfaChallengeRequest,
  MfaChallengeResponse,
  MfaDisableRequest,
  MfaSetupRequest,
  MfaSetupResponse,
  MfaStatusResponse,
  MfaVerificationRequest,
  MfaVerificationResponse,
  RegenerateBackupCodesRequest,
  RegenerateBackupCodesResponse,
  VerifyMfaChallengeRequest,
  VerifyMfaChallengeResponse,
} from '../schemas';

// Core MFA DTOs
export type MfaSetupRequestDto = MfaSetupRequest;
export type MfaSetupResponseDto = MfaSetupResponse;
export type MfaVerificationRequestDto = MfaVerificationRequest;
export type MfaVerificationResponseDto = MfaVerificationResponse;
export type MfaChallengeRequestDto = MfaChallengeRequest;
export type MfaChallengeResponseDto = MfaChallengeResponse;
export type MfaDisableRequestDto = MfaDisableRequest;
export type MfaStatusResponseDto = MfaStatusResponse;
export type RegenerateBackupCodesRequestDto = RegenerateBackupCodesRequest;
export type RegenerateBackupCodesResponseDto = RegenerateBackupCodesResponse;
export type VerifyMfaChallengeRequestDto = VerifyMfaChallengeRequest;
export type VerifyMfaChallengeResponseDto = VerifyMfaChallengeResponse;
