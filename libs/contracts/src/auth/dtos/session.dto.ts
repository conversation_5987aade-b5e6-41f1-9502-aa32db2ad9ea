import { SessionStatus } from '../enums';
import {
  CreateSessionRequest,
  GetSessionByTokenRequest,
  GetUserSessionsRequest,
  SessionInternal,
  TerminateAllUserSessionsRequest,
  TerminateSessionByTokenRequest,
  TerminateUserSessionRequest,
  TerminateUserSessionsByDeviceRequest,
  UpdateSessionActivityRequest,
} from '../schemas/session.schema';

/**
 * Session DTOs
 * TypeScript types for session-related operations
 */

/**
 * Session data DTO (snake_case for external API)
 */
export interface SessionDataDto {
  /** Session ID */
  id: string;

  /** User ID */
  userId: string;

  /** Tenant ID (optional) */
  tenantId?: string | null;

  /** Tenant code (optional) */
  tenantCode?: string | null;

  /** Session status */
  status: SessionStatus;

  /** IP address */
  ipAddress?: string | null;

  /** User agent */
  userAgent?: string | null;

  /** Device information */
  deviceInfo?: Record<string, any> | null;

  /** Location information */
  location?: Record<string, any> | null;

  /** Session creation timestamp */
  createdAt: string;

  /** Last accessed timestamp */
  lastAccessedAt: string;

  /** Session expiration timestamp */
  expiresAt: string;

  /** Revocation timestamp (optional) */
  revokedAt?: string | null;

  /** Revoked by user ID (optional) */
  revokedBy?: string | null;

  /** Revocation reason (optional) */
  revokedReason?: string | null;
}

/**
 * Get user sessions response DTO (snake_case for external API)
 */
export interface GetUserSessionsResponseDto {
  /** Array of user sessions */
  sessions: SessionDataDto[];

  /** Total number of sessions */
  totalCount: number;

  /** Number of active sessions */
  activeCount: number;
}

/**
 * Terminate session request DTO (snake_case for external API)
 */
export interface TerminateSessionRequestDto {
  /** Optional reason for termination */
  reason?: string;
}

/**
 * Terminate session response DTO (snake_case for external API)
 */
export interface TerminateSessionResponseDto {
  /** Success message */
  message: string;

  /** Terminated session ID */
  sessionId: string;

  /** Termination timestamp */
  terminatedAt: string;
}

/**
 * Terminate all sessions response DTO (snake_case for external API)
 */
export interface TerminateAllSessionsResponseDto {
  /** Success message */
  message: string;

  /** Number of sessions terminated */
  terminated_count: number;

  /** Termination timestamp */
  terminated_at: string;
}

/**
 * Current session response DTO (snake_case for external API)
 */
export interface CurrentSessionResponseDto {
  /** Current session data */
  session: SessionDataDto | null;

  /** Whether this is the only active session */
  isOnlySession: boolean;
}

// Infer types from additional schemas
export type SessionInternalDto = SessionInternal;
export type CreateSessionRequestDto = CreateSessionRequest;
export type GetUserSessionsRequestDto = GetUserSessionsRequest;
export type GetSessionByTokenRequestDto = GetSessionByTokenRequest;
export type UpdateSessionActivityRequestDto = UpdateSessionActivityRequest;
export type TerminateUserSessionRequestDto = TerminateUserSessionRequest;
export type TerminateSessionByTokenRequestDto = TerminateSessionByTokenRequest;
export type TerminateUserSessionsByDeviceRequestDto = TerminateUserSessionsByDeviceRequest;
export type TerminateAllUserSessionsRequestDto = TerminateAllUserSessionsRequest;
