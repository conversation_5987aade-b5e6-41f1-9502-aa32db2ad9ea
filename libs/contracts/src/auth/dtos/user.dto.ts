/**
 * User DTOs
 * TypeScript types for user-related operations
 */

/**
 * User response DTO (snake_case for external API)
 */
export interface UserResponseDto {
  /** User ID */
  id: string;

  /** User's email address */
  email: string;

  /** User's first name */
  firstName: string;

  /** User's last name */
  lastName: string;

  /** User's current status */
  status: string;

  /** Whether the user's email is verified */
  isEmailVerified: boolean;

  /** User's assigned roles */
  roles: string[];

  /** User's tenant information */
  tenant?: {
    /** Tenant ID */
    id: string;

    /** Tenant code */
    code: string;

    /** Tenant name */
    name: string;
  };

  /** Last login timestamp */
  lastLoginAt: string | null;

  /** Last login IP address */
  lastLoginIp: string | null;

  /** Last login user agent */
  lastLoginUserAgent: string | null;

  /** Number of failed login attempts */
  loginAttempts: number;

  /** Account locked until timestamp */
  lockedUntil: string | null;

  /** Password last changed timestamp */
  passwordChangedAt: string | null;

  /** Account creation timestamp */
  createdAt: string;

  /** Account last updated timestamp */
  updatedAt: string;
}

/**
 * Minimal user response DTO (snake_case for external API)
 * Used for endpoints that only need basic user information
 */
export interface MinimalUserResponseDto {
  /** User ID */
  id: string;

  /** User's email address */
  email: string;

  /** User's first name */
  firstName: string;

  /** User's last name */
  lastName: string;

  /** User's current status */
  status: string;

  /** Whether the user's email is verified */
  isEmailVerified: boolean;

  /** User's assigned roles */
  roles: string[];

  /** User's permissions */
  permissions: string[];

  /** User's tenant information */
  tenant?: {
    /** Tenant ID */
    id: string;

    /** Tenant code */
    code: string;

    /** Tenant name */
    name: string;
  };

  /** Last login timestamp */
  lastLoginAt?: string | null;

  /** Profile completion percentage */
  profileCompletion?: number;

  /** Next onboarding step */
  nextStep?: string;
}

// /**
//  * User profile response DTO
//  */
// export interface UserProfileResponseDto {
//   /** User ID */
//   id: string;

//   /** User's email address */
//   email: string;

//   /** User's first name */
//   firstName: string;

//   /** User's last name */
//   lastName: string;

//   /** User's current status */
//   status: string;

//   /** Whether the user's email is verified */
//   isEmailVerified: boolean;

//   /** User's assigned roles */
//   roles: string[];

//   /** User's tenant information */
//   tenant?: {
//     id: string;
//     code: string;
//     name: string;
//   };

//   /** Last login timestamp */
//   lastLoginAt: string | null;

//   /** Last login IP address */
//   lastLoginIp: string | null;

//   /** Last login user agent */
//   lastLoginUserAgent: string | null;

//   /** Number of failed login attempts */
//   loginAttempts: number;

//   /** Account locked until timestamp */
//   lockedUntil: string | null;

//   /** Password last changed timestamp */
//   passwordChangedAt: string | null;

//   /** Account creation timestamp */
//   createdAt: string;

//   /** Account last updated timestamp */
//   updatedAt: string;
// }

/**
 * Unlock account request DTO
 */
export interface UnlockAccountRequestDto {
  /** User ID to unlock */
  userId: string;

  /** Optional reason for unlocking */
  reason?: string;
}

/**
 * Unlock account response DTO
 */
export interface UnlockAccountResponseDto {
  /** Success message */
  message: string;

  /** Unlock timestamp */
  unlockedAt: string;

  /** User ID that was unlocked */
  userId: string;
}

// Type definitions for backward compatibility
export type UserInternalDto = UserResponseDto; // Alias for backward compatibility
export type MinimalUserInternalDto = MinimalUserResponseDto; // Alias for backward compatibility
