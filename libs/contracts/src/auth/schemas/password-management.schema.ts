import { z } from 'zod';
import { PasswordSchema } from '../../shared';

/**
 * Password Management Schemas
 * Zod validation schemas for password-related operations
 */

/**
 * Forgot password request schema (snake_case for external API)
 */
export const ForgotPasswordRequestSchema = z.object({
  /** User's email address */
  email: z.email('Please provide a valid email address'),
});

/**
 * Forgot password response schema (snake_case for external API)
 */
export const ForgotPasswordResponseSchema = z.object({
  /** Success message */
  message: z.string(),

  /** Request timestamp */
  requestedAt: z.string(),
});

/**
 * Reset password request schema (snake_case for external API)
 */
export const ResetPasswordRequestSchema = z.object({
  /** Password reset token */
  token: z.string().min(1, 'Reset token is required'),

  /** New password */
  newPassword: PasswordSchema,
});

/**
 * Reset password response schema (snake_case for external API)
 */
export const ResetPasswordResponseSchema = z.object({
  /** Success message */
  message: z.string(),

  /** Reset completion timestamp */
  resetAt: z.string(),
});

/**
 * Change password request schema (snake_case for external API)
 */
export const ChangePasswordRequestSchema = z.object({
  /** Current password */
  currentPassword: z.string().min(1, 'Current password is required'),

  /** New password */
  newPassword: PasswordSchema,
});

/**
 * Change password response schema (snake_case for external API)
 */
export const ChangePasswordResponseSchema = z.object({
  /** Success message */
  message: z.string(),

  /** Change completion timestamp */
  changedAt: z.string(),
});

/**
 * Validate password reset token request schema
 */
export const ValidateResetTokenRequestSchema = z.object({
  /** Password reset token to validate */
  token: z.string().min(1, 'Reset token is required'),
});

/**
 * Validate password reset token response schema
 */
export const ValidateResetTokenResponseSchema = z.object({
  /** Whether the token is valid */
  isValid: z.boolean(),

  /** Token expiration timestamp */
  expiresAt: z.iso.datetime().optional(),

  /** Associated user email (masked) */
  email: z.string().optional(),
});

/**
 * Verify user password
 * Used by MFA and other security operations
 */
export const VerifyUserPasswordRequestSchema = z.object({
  /** User ID */
  userId: z.uuid(),

  /** Password to verify */
  password: z.string().min(1, 'Password is required'),
});

/**
 * Generate tokens for user
 * Used when resetting password or other cases where we need to generate new tokens
 */
export const GenerateTokensForUserRequestSchema = z.object({
  /** User ID */
  userId: z.uuid(),
});

/**
 * Password Management Service Response Schema
 * Generic response schema for password management service operations
 */
export const PasswordManagementResponseSchema = z.object({
  /** Success message */
  message: z.string(),

  /** Operation completion timestamp */
  completedAt: z.string(),
});

export type ForgotPasswordRequest = z.infer<typeof ForgotPasswordRequestSchema>;
export type ForgotPasswordResponse = z.infer<typeof ForgotPasswordResponseSchema>;
export type ResetPasswordRequest = z.infer<typeof ResetPasswordRequestSchema>;
export type ResetPasswordResponse = z.infer<typeof ResetPasswordResponseSchema>;
export type ChangePasswordRequest = z.infer<typeof ChangePasswordRequestSchema>;
export type ChangePasswordResponse = z.infer<typeof ChangePasswordResponseSchema>;
export type ValidateResetTokenRequest = z.infer<typeof ValidateResetTokenRequestSchema>;
export type ValidateResetTokenResponse = z.infer<typeof ValidateResetTokenResponseSchema>;
export type VerifyUserPasswordRequest = z.infer<typeof VerifyUserPasswordRequestSchema>;
export type GenerateTokensForUserRequest = z.infer<typeof GenerateTokensForUserRequestSchema>;
export type PasswordManagementResponse = z.infer<typeof PasswordManagementResponseSchema>;
