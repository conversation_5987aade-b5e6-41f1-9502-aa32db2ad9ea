import { z } from 'zod';
import { createPrefixedCuidSchema } from '../../shared/schemas/cuid.schema';
import { MfaMethod } from '../enums';

// Session token validation schema (expects tok_ prefix)
const SessionTokenSchema = createPrefixedCuidSchema('tok_', 'session token');

/**
 * Multi-Factor Authentication Schemas
 * Zod validation schemas for MFA operations
 */

/**
 * MFA setup request schema
 */
export const MfaSetupRequestSchema = z.object({
  /** MFA method to set up */
  method: z.enum(MfaMethod),

  /** Phone number for SMS method */
  phoneNumber: z.string().optional(),

  /** Email for email method */
  email: z.email().optional(),
});

/**
 * MFA setup response schema
 */
export const MfaSetupResponseSchema = z.object({
  /** Setup method */
  method: z.enum(MfaMethod),

  /** QR code for TOTP setup */
  qrCode: z.string().optional(),

  /** Secret key for manual TOTP setup */
  secret: z.string().optional(),

  /** Backup codes */
  backupCodes: z.array(z.string()).optional(),

  /** Setup instructions */
  instructions: z.string().optional(),
});

/**
 * MFA verification request schema
 */
export const MfaVerificationRequestSchema = z.object({
  /** MFA method being verified */
  method: z.enum(MfaMethod),

  /** Verification code */
  code: z.string().min(1, 'Verification code is required'),

  /** Session token or identifier */
  sessionToken: z.string().optional(),
});

/**
 * MFA verification response schema
 */
export const MfaVerificationResponseSchema = z.object({
  /** Whether verification was successful */
  verified: z.boolean(),

  /** Access token if verification successful */
  accessToken: z.string().optional(),

  /** Refresh token if verification successful */
  refreshToken: z.string().optional(),

  /** Token expiration time */
  expiresIn: z.number().optional(),

  /** Remaining attempts */
  remainingAttempts: z.number().optional(),
});

/**
 * MFA challenge request schema
 */
export const MfaChallengeRequestSchema = z.object({
  /** MFA method to challenge */
  method: z.enum(MfaMethod),

  /** Session token (format: tok_...) */
  sessionToken: SessionTokenSchema,
});

/**
 * MFA challenge response schema
 */
export const MfaChallengeResponseSchema = z.object({
  /** Challenge identifier */
  challengeId: z.string(),

  /** Challenge method */
  method: z.enum(MfaMethod),

  /** Challenge expiration time */
  expiresAt: z.iso.datetime(),

  /** Instructions for the user */
  instructions: z.string(),
});

/**
 * MFA disable request schema
 */
export const MfaDisableRequestSchema = z.object({
  /** Current password for verification */
  password: z.string().min(1, 'Password is required'),

  /** MFA method to disable */
  method: z.enum(MfaMethod).optional(),

  /** Backup code for verification */
  backupCode: z.string().optional(),
});

/**
 * MFA status response schema
 */
export const MfaStatusResponseSchema = z.object({
  /** Whether MFA is enabled */
  enabled: z.boolean(),

  /** Configured MFA methods */
  methods: z.array(z.enum(MfaMethod)),

  /** Primary MFA method */
  primaryMethod: z.enum(MfaMethod).optional(),

  /** Number of backup codes remaining */
  backup_codes_remaining: z.number().optional(),
});

/**
 * Backup codes regeneration request schema
 */
export const RegenerateBackupCodesRequestSchema = z.object({
  /** Current password for verification */
  password: z.string().min(1, 'Password is required'),
});

/**
 * Backup codes regeneration response schema
 */
export const RegenerateBackupCodesResponseSchema = z.object({
  /** New backup codes */
  backupCodes: z.array(z.string()),

  /** Generation timestamp */
  generatedAt: z.iso.datetime(),
});

/**
 * Verify MFA challenge request schema
 */
export const VerifyMfaChallengeRequestSchema = z
  .object({
    /** Challenge identifier */
    challengeId: z.string().min(1, 'Challenge ID is required'),

    /** Verification code */
    code: z.string().optional(),

    /** Backup code for verification */
    backupCode: z.string().optional(),
  })
  .refine((data) => data.code || data.backupCode, {
    message: 'Either code or backup code is required',
    path: ['code'],
  });

/**
 * Verify MFA challenge response schema
 */
export const VerifyMfaChallengeResponseSchema = z.object({
  /** Access token if verification successful */
  accessToken: z.string(),

  /** Refresh token if verification successful */
  refreshToken: z.string(),

  /** Token expiration time in seconds */
  expiresIn: z.number(),

  /** User information */
  user: z.object({
    id: z.string(),
    email: z.string(),
    emailVerified: z.boolean(),
    tenantCode: z.string().optional(),
  }),
});

export type MfaSetupRequest = z.infer<typeof MfaSetupRequestSchema>;
export type MfaSetupResponse = z.infer<typeof MfaSetupResponseSchema>;
export type MfaVerificationRequest = z.infer<typeof MfaVerificationRequestSchema>;
export type MfaVerificationResponse = z.infer<typeof MfaVerificationResponseSchema>;
export type MfaChallengeRequest = z.infer<typeof MfaChallengeRequestSchema>;
export type MfaChallengeResponse = z.infer<typeof MfaChallengeResponseSchema>;
export type MfaDisableRequest = z.infer<typeof MfaDisableRequestSchema>;
export type MfaStatusResponse = z.infer<typeof MfaStatusResponseSchema>;
export type RegenerateBackupCodesRequest = z.infer<typeof RegenerateBackupCodesRequestSchema>;
export type RegenerateBackupCodesResponse = z.infer<typeof RegenerateBackupCodesResponseSchema>;
export type VerifyMfaChallengeRequest = z.infer<typeof VerifyMfaChallengeRequestSchema>;
export type VerifyMfaChallengeResponse = z.infer<typeof VerifyMfaChallengeResponseSchema>;
