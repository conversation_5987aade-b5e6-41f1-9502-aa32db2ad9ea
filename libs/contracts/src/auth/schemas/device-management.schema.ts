import { z } from 'zod';
import { Shared } from '../../shared/namespace';

/**
 * Device status enum
 */
export enum DeviceStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  BLOCKED = 'BLOCKED',
  SUSPICIOUS = 'SUSPICIOUS',
}

/**
 * Device info schema for device fingerprinting
 */
export const DeviceInfoSchema = z
  .object({
    userAgent: z.string().optional(),
    screenResolution: z.string().optional(),
    platform: z.string().optional(),
    timezone: z.string().optional(),
    language: z.string().optional(),
    ipAddress: z.string().optional(),
  })
  .catchall(z.unknown());

/**
 * Schema for registering a trusted device
 */
export const RegisterTrustedDeviceSchema = z.object({
  deviceName: z.string().max(255).optional(),
  deviceType: z.string().max(50).optional(),
  deviceInfo: DeviceInfoSchema,
});

/**
 * Schema for updating a trusted device
 */
export const UpdateTrustedDeviceSchema = z.object({
  deviceName: z.string().max(255).optional(),
  status: z.nativeEnum(DeviceStatus).optional(),
});

/**
 * Schema for checking device trust status
 */
export const CheckDeviceTrustSchema = z.object({
  deviceInfo: DeviceInfoSchema,
});

/**
 * Schema representing a trusted device in responses
 */
export const TrustedDeviceSchema = z.object({
  id: z.string(),
  deviceId: z.string(),
  deviceName: z.string().nullable(),
  deviceType: z.string().nullable(),
  operatingSystem: z.string().nullable(),
  browser: z.string().nullable(),
  status: z.nativeEnum(DeviceStatus),
  lastUsedAt: z.date().nullable(),
  trustedAt: z.date(),
  expiresAt: z.date().nullable(),
});

/**
 * Schema for device trust status check response
 */
export const DeviceTrustStatusSchema = z.object({
  isTrusted: z.boolean(),
  deviceId: z.string().optional(),
});

// Query schema for listing trusted devices with filters
export const ListTrustedDevicesQuerySchema = z
  .object({
    page: z.coerce.number().min(1).optional().default(1),
    limit: z.coerce.number().min(1).max(100).optional().default(10),
    status: z.nativeEnum(DeviceStatus).optional(),
    device_type: z.string().optional(),
    device_name: z.string().optional(),
    browser: z.string().optional(),
    operating_system: z.string().optional(),
    sort_by: z.enum(['last_used_at', 'trusted_at', 'device_name']).optional().default('last_used_at'),
    sort_order: z.enum(['asc', 'desc']).optional().default('desc'),
  })
  .catchall(z.unknown());

// Response schema for list devices endpoint
export const ListTrustedDevicesResponseSchema = z.object({
  devices: z.array(TrustedDeviceSchema),
  pagination: Shared.Schemas.PaginationMetaSchema,
});

// Response schema for get device endpoint
export const GetTrustedDeviceResponseSchema = TrustedDeviceSchema;

// Response schema for register device endpoint
export const RegisterTrustedDeviceResponseSchema = TrustedDeviceSchema;

// Response schema for update device endpoint
export const UpdateTrustedDeviceResponseSchema = TrustedDeviceSchema;

// Response schema for revoke device endpoint
export const RevokeTrustedDeviceResponseSchema = z.object({
  revokedAt: z.date(),
  deviceId: z.string(),
});

// Response schema for revoke all devices endpoint
export const RevokeAllTrustedDevicesResponseSchema = z.object({
  revokedCount: z.number(),
  revokedAt: z.date(),
});

// Export type definitions derived from schemas
export type DeviceInfoRequest = z.infer<typeof DeviceInfoSchema>;
export type RegisterTrustedDeviceRequest = z.infer<typeof RegisterTrustedDeviceSchema>;
export type UpdateTrustedDeviceRequest = z.infer<typeof UpdateTrustedDeviceSchema>;
export type CheckDeviceTrustRequest = z.infer<typeof CheckDeviceTrustSchema>;
export type TrustedDeviceRequest = z.infer<typeof TrustedDeviceSchema>;
export type DeviceTrustStatusRequest = z.infer<typeof DeviceTrustStatusSchema>;
export type ListTrustedDevicesQueryRequest = z.infer<typeof ListTrustedDevicesQuerySchema>;

// Export request and response types
export type ListTrustedDevicesResponse = z.infer<typeof ListTrustedDevicesResponseSchema>;
export type GetTrustedDeviceResponse = z.infer<typeof GetTrustedDeviceResponseSchema>;
export type RegisterTrustedDeviceResponse = z.infer<typeof RegisterTrustedDeviceResponseSchema>;
export type UpdateTrustedDeviceResponse = z.infer<typeof UpdateTrustedDeviceResponseSchema>;
export type RevokeTrustedDeviceResponse = z.infer<typeof RevokeTrustedDeviceResponseSchema>;
export type RevokeAllTrustedDevicesResponse = z.infer<typeof RevokeAllTrustedDevicesResponseSchema>;
