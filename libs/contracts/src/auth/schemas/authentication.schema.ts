import { z } from 'zod';
import { AuthSessionIdSchema, TenantIdSchema, UserIdSchema } from '../../shared/schemas/cuid.schema';
import { AuthStatus, MfaMethod, SessionStatus } from '../enums';

/**
 * Authentication Schemas
 * Zod validation schemas for authentication operations
 */

/**
 * Login request schema (camelCase for internal use)
 * Validates user login credentials from external clients
 */
export const LoginRequestSchema = z.object({
  /** User's email address */
  email: z.email('Please provide a valid email address').max(255, 'Email must not exceed 255 characters').toLowerCase(),

  /** User's password */
  password: z.string().min(1, 'Password is required').max(128, 'Password must not exceed 128 characters'),

  /** Optional remember me flag */
  rememberMe: z.boolean().optional().default(false),

  /** Optional MFA code */
  mfaCode: z.string().max(10, 'MFA code must not exceed 10 characters').optional(),

  /** Optional device identifier */
  deviceId: z.string().max(100, 'Device ID must not exceed 100 characters').optional(),

  /** Optional tenant code */
  tenantId: z.uuid().optional(),
});

/**
 * Minimal user response schema for authentication responses
 */
export const MinimalUserResponseSchema = z.object({
  /** User ID */
  id: UserIdSchema,

  /** User's email */
  email: z.email(),

  /** User's first name */
  firstName: z.string(),

  /** User's last name */
  lastName: z.string(),

  /** User's status */
  status: z.enum(AuthStatus),

  /** Whether email is verified */
  isEmailVerified: z.boolean(),

  /** User's roles */
  roles: z.array(z.string()),

  /** User's permissions */
  permissions: z.array(z.string()),

  /** Tenant information */
  tenant: z
    .object({
      id: TenantIdSchema,
      code: z.string(),
      name: z.string(),
    })
    .optional(),

  /** Last login timestamp */
  lastLoginAt: z.string().nullable().optional(),

  /** Profile completion percentage */
  profileCompletion: z.number().min(0).max(100).optional(),

  /** Next onboarding step */
  nextStep: z.string().optional(),
});

/**
 * Session information schema
 */
export const SessionInfoSchema = z.object({
  /** Session ID */
  id: AuthSessionIdSchema,

  /** Session status */
  status: z.enum(SessionStatus),

  /** Session creation timestamp */
  createdAt: z.string(),

  /** Session expiration timestamp */
  expiresAt: z.string(),

  /** Last activity timestamp */
  lastActivityAt: z.string(),

  /** Session token for MFA flow */
  sessionToken: z.string().optional(),

  /** Device information */
  device: z
    .object({
      id: z.string().optional(),
      name: z.string().optional(),
      type: z.string().optional(),
      os: z.string().optional(),
      browser: z.string().optional(),
    })
    .optional(),

  /** IP address */
  ipAddress: z.string().optional(),

  /** User agent */
  userAgent: z.string().optional(),
});

/**
 * Login response schema (camelCase for internal use)
 * Defines the structure of successful login responses
 */
export const LoginResponseSchema = z.object({
  /** Access token */
  accessToken: z.string(),

  /** Refresh token */
  refreshToken: z.string(),

  /** Token expiration time in seconds */
  expiresIn: z.number().positive(),

  /** Token type (usually 'Bearer') */
  tokenType: z.string().default('Bearer'),

  /** User information */
  user: MinimalUserResponseSchema,

  /** Session information */
  session: SessionInfoSchema,

  /** MFA requirement flag */
  requiresMfa: z.boolean().optional().default(false),

  /** Available MFA methods */
  mfaMethods: z.array(z.enum(MfaMethod)).optional(),
});

/**
 * Logout request schema
 */
export const LogoutRequestSchema = z
  .object({
    /** Refresh token to invalidate */
    refresh_token: z.string().optional(),

    /** Whether to logout from all devices */
    all_devices: z.boolean().optional().default(false),
  })
  .transform((data) => ({
    refreshToken: data.refresh_token,
    allDevices: data.all_devices,
  }));

/**
 * Token refresh request schema
 */
export const TokenRefreshRequestSchema = z
  .object({
    /** Refresh token */
    refresh_token: z.string().min(1, 'Refresh token is required'),
  })
  .transform((data) => ({
    refreshToken: data.refresh_token,
  }));

/**
 * Token refresh response schema
 */
export const TokenRefreshResponseSchema = z.object({
  /** New access token */
  accessToken: z.string(),

  /** New refresh token */
  refreshToken: z.string(),

  /** Token expiration time in seconds */
  expiresIn: z.number().positive(),

  /** Token type */
  tokenType: z.string().default('Bearer'),
});

// Response Schemas
// Additional schemas for operations not in main schema file
export const LogoutResponseSchema = z.object({
  loggedOutAt: z.string(),
});

export const RevokeTokenRequestSchema = z.object({
  refreshToken: z.string(),
  deviceId: z.string().optional(),
});

export const RevokeTokenResponseSchema = z.object({
  revokedAt: z.string(),
  tokenId: z.string().optional(),
});

export const RevokeAllTokensResponseSchema = z.object({
  revokedTokensCount: z.number(),
  revokedAt: z.string(),
});

// Infer types from additional schemas
export type LogoutResponse = z.infer<typeof LogoutResponseSchema>;
export type LogoutRequest = z.infer<typeof LogoutRequestSchema>;

export type RevokeTokenRequest = z.infer<typeof RevokeTokenRequestSchema>;
export type RevokeTokenResponse = z.infer<typeof RevokeTokenResponseSchema>;

export type RevokeAllTokensResponse = z.infer<typeof RevokeAllTokensResponseSchema>;

export type LoginRequest = z.infer<typeof LoginRequestSchema>;
export type LoginResponse = z.infer<typeof LoginResponseSchema>;

export type TokenRefreshRequest = z.infer<typeof TokenRefreshRequestSchema>;
export type TokenRefreshResponse = z.infer<typeof TokenRefreshResponseSchema>;
