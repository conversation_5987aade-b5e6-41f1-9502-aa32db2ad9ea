import { z } from 'zod';
import { EmailSchema, NameSchema, PasswordSchema, UserIdSchema } from '../../shared';
import { OnboardingStep } from '../enums';

/**
 * User Signup Schemas
 * Zod validation schemas for user registration operations
 */
export const SignupRequestSchema = z.object({
  /** User's email address */
  email: EmailSchema,

  /** User's password */
  password: PasswordSchema,

  /** User's first name */
  firstName: NameSchema,

  /** User's last name */
  lastName: NameSchema,

  /** Acceptance of terms and conditions */
  acceptTerms: z.boolean().refine((val) => val === true, {
    message: 'You must accept the terms and conditions',
  }),

  /** Acceptance of privacy policy */
  acceptPrivacy: z.boolean().refine((val) => val === true, {
    message: 'You must accept the privacy policy',
  }),

  /** Optional invitation token */
  invitationToken: z.string().optional(),

  /** Optional marketing consent */
  marketingConsent: z.boolean().optional().default(false),

  /** Optional tenant ID */
  tenantId: z.string().optional(),
});

export const SignupResponseSchema = z.object({
  /** User ID */
  userId: UserIdSchema,

  /** User's email */
  email: z.email(),

  /** User's first name */
  firstName: z.string(),

  /** User's last name */
  lastName: z.string(),

  /** User status */
  status: z.string(),

  /** Whether email verification is required */
  requiresEmailVerification: z.boolean(),

  /** Next onboarding step */
  nextStep: z.enum(OnboardingStep).optional(),

  /** Verification email sent timestamp */
  verificationEmailSentAt: z.iso.datetime().optional(),

  /** Tenant information if applicable */
  tenant: z
    .object({
      id: z.uuid(),
      code: z.string(),
      name: z.string(),
    })
    .optional(),
});

/**
 * Email verification request schema
 */
export const EmailVerificationRequestSchema = z.object({
  /** Verification token */
  token: z.string().min(1, 'Verification token is required'),
});

export const EmailVerificationResponseSchema = z.object({
  /** Success message */
  message: z.string(),

  /** Verification timestamp */
  verifiedAt: z.iso.datetime(),

  /** Next onboarding step */
  nextStep: z.enum(OnboardingStep).optional(),

  /** Whether user can now login */
  canLogin: z.boolean(),
});

/**
 * Resend verification email request schema
 */
export const ResendVerificationRequestSchema = z.object({
  /** User's email address */
  email: EmailSchema,

  /** Optional tenant code */
  tenantCode: z.string().optional(),
});

export const ResendVerificationResponseSchema = z.object({
  /** Success message */
  message: z.string(),

  /** Email sent timestamp */
  sentAt: z.iso.datetime(),

  /** Cooldown period in seconds */
  cooldownSeconds: z.number().optional(),
});

/**
 * Check email availability request schema
 */
export const CheckEmailAvailabilityRequestSchema = z.object({
  /** Email to check */
  email: EmailSchema,

  /** Optional tenant code */
  tenantCode: z.string().optional(),
});

export const CheckEmailAvailabilityResponseSchema = z.object({
  /** Whether email is available */
  available: z.boolean(),

  /** Reason if not available */
  reason: z.string().optional(),

  /** Suggestions if email is taken */
  suggestions: z.array(z.string()).optional(),
});

/**
 * Unlock user account request schema
 */
export const UnlockUserAccountRequestSchema = z.object({
  /** user id */
  userId: UserIdSchema,

  /** Reason */
  reason: z.string().optional(),
});

export const UnlockUserAccountResponseSchema = z.object({
  /** Success message */
  message: z.string(),

  /** Unlock timestamp */
  unlockedAt: z.string(),

  /** User ID that was unlocked */
  userId: UserIdSchema,
});

export type SignupRequest = z.infer<typeof SignupRequestSchema>;
export type SignupResponse = z.infer<typeof SignupResponseSchema>;
export type EmailVerificationRequest = z.infer<typeof EmailVerificationRequestSchema>;
export type EmailVerificationResponse = z.infer<typeof EmailVerificationResponseSchema>;
export type ResendVerificationRequest = z.infer<typeof ResendVerificationRequestSchema>;
export type ResendVerificationResponse = z.infer<typeof ResendVerificationResponseSchema>;
export type CheckEmailAvailabilityRequest = z.infer<typeof CheckEmailAvailabilityRequestSchema>;
export type CheckEmailAvailabilityResponse = z.infer<typeof CheckEmailAvailabilityResponseSchema>;
export type UnlockUserAccountRequest = z.infer<typeof UnlockUserAccountRequestSchema>;
export type UnlockUserAccountResponse = z.infer<typeof UnlockUserAccountResponseSchema>;
