import { z } from 'zod';
import { PrefixedCuidSchema } from '../../shared/schemas/cuid.schema';
import { SessionStatus } from '../enums';

/**
 * Session Management Schemas
 * Zod validation schemas for session operations
 */

/**
 * Session device information schema
 */
export const SessionDeviceSchema = z.object({
  /** Device identifier */
  id: z.string().optional(),

  /** Device name */
  name: z.string().optional(),

  /** Device type (mobile, desktop, tablet) */
  type: z.string().optional(),

  /** Operating system */
  os: z.string().optional(),

  /** Browser name */
  browser: z.string().optional(),

  /** Browser version */
  browserVersion: z.string().optional(),

  /** Whether this is a trusted device */
  isTrusted: z.boolean().optional(),
});

/**
 * Session information schema
 */
export const SessionSchema = z.object({
  /** Session ID */
  id: PrefixedCuidSchema,

  /** User ID */
  userId: PrefixedCuidSchema,

  /** Tenant ID (optional) */
  tenantId: PrefixedCuidSchema.optional(),

  /** Tenant code (optional) */
  tenantCode: z.string().optional(),

  /** Session status */
  status: z.enum(SessionStatus),

  /** Session creation timestamp */
  createdAt: z.iso.datetime(),

  /** Session expiration timestamp */
  expiresAt: z.iso.datetime(),

  /** Last activity timestamp */
  lastActivityAt: z.iso.datetime(),

  /** IP address */
  ipAddress: z.string().optional(),

  /** User agent */
  userAgent: z.string().optional(),

  /** Device information */
  device: SessionDeviceSchema.optional(),

  /** Whether this is the current session */
  isCurrent: z.boolean().optional(),

  /** Location information */
  location: z
    .object({
      country: z.string().optional(),
      region: z.string().optional(),
      city: z.string().optional(),
    })
    .optional(),
});

/**
 * Active sessions response schema
 */
export const ActiveSessionsResponseSchema = z.object({
  /** List of active sessions */
  sessions: z.array(SessionSchema),

  /** Total number of sessions */
  total: z.number(),

  /** Current session ID */
  currentSessionId: PrefixedCuidSchema,
});

/**
 * Terminate session request schema
 */
export const TerminateSessionRequestSchema = z.object({
  /** Session ID to terminate */
  sessionId: PrefixedCuidSchema,
});

/**
 * Terminate all sessions request schema
 */
export const TerminateAllSessionsRequestSchema = z.object({
  /** Whether to exclude current session */
  excludeCurrent: z.boolean().optional().default(true),

  /** Current password for verification */
  password: z.string().min(1, 'Password is required'),
});

/**
 * Session activity response schema
 */
export const SessionActivityResponseSchema = z.object({
  /** Session activities */
  activities: z.array(
    z.object({
      /** Activity timestamp */
      timestamp: z.iso.datetime(),

      /** Activity type */
      type: z.string(),

      /** Activity description */
      description: z.string(),

      /** IP address */
      ipAddress: z.string().optional(),

      /** Location */
      location: z.string().optional(),
    }),
  ),

  /** Total activities count */
  total: z.number(),

  /** Pagination info */
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    totalPages: z.number(),
    hasNext: z.boolean(),
    hasPrev: z.boolean(),
  }),
});

// Infer types from additional schemas
export type SessionInternal = z.infer<typeof SessionSchema>;
export type CreateSessionRequest = z.infer<typeof SessionSchema>;
export type GetUserSessionsRequest = z.infer<typeof SessionSchema>;
export type GetSessionByTokenRequest = z.infer<typeof SessionSchema>;
export type UpdateSessionActivityRequest = z.infer<typeof SessionSchema>;
export type TerminateUserSessionRequest = z.infer<typeof SessionSchema>;
export type TerminateSessionByTokenRequest = z.infer<typeof SessionSchema>;
export type TerminateUserSessionsByDeviceRequest = z.infer<typeof SessionSchema>;
export type TerminateAllUserSessionsRequest = z.infer<typeof SessionSchema>;
