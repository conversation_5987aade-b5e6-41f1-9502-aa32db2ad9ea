# # Qeep Contracts Library

A centralized type definitions, DTOs, and interfaces library for the Qeep platform. This library provides runtime validation with Zod schemas, compile-time type safety with TypeScript interfaces, and domain-driven organization of contracts.

## 🎯 Purpose

The Qeep Contracts Library serves as the single source of truth for:

- **Type Definitions**: Consistent TypeScript interfaces across all services
- **Data Validation**: Runtime validation using Zod schemas
- **API Contracts**: Standardized request/response formats
- **Domain Models**: Domain-driven organization of business entities
- **Shared Enums**: Common enumerations and constants

## 📦 Installation

The library is already configured in the monorepo. Import it in your services:

```typescript
import { Auth, Shared } from '@qeep/contracts';
```

## 🏗️ Architecture

### Domain-Driven Structure

```
libs/contracts/src/
├── auth/                       # Authentication domain
│   ├── dtos/                  # Data Transfer Objects
│   │   ├── signup.dto.ts      # Signup validation schemas
│   │   ├── login.dto.ts       # Login validation schemas
│   │   └── index.ts           # DTO exports
│   ├── interfaces/            # TypeScript interfaces
│   │   ├── auth.interface.ts  # Auth service interfaces
│   │   └── index.ts           # Interface exports
│   ├── enums/                 # Enums and constants
│   │   ├── auth-status.enum.ts
│   │   └── index.ts           # Enum exports
│   └── index.ts               # Auth domain exports
├── shared/                    # Cross-domain shared types
│   ├── dtos/
│   │   ├── api-response.dto.ts # Standard API responses
│   │   └── index.ts
│   ├── interfaces/
│   │   ├── common.interface.ts # Common interfaces
│   │   └── index.ts
│   └── index.ts
├── user/                      # User domain (placeholder)
├── tenant/                    # Tenant domain (placeholder)
├── audit/                     # Audit domain (placeholder)
├── notification/              # Notification domain (placeholder)
└── index.ts                   # Main exports with namespaces
```

### Namespace Organization

The library exports contracts using TypeScript namespaces for better organization:

```typescript
// Organized by domain and type
Auth.DTOs.SignupRequestSchema;
Auth.Enums.AuthStatus;
Auth.Interfaces.AuthUser;

Shared.DTOs.StandardSuccessResponse;
Shared.Interfaces.BaseEntity;
```

## 🔧 Usage Examples

### 1. Authentication Flow

```typescript
import { Auth } from '@qeep/contracts';

// Validate signup request
const signupData = {
  email: '<EMAIL>',
  password: 'SecurePassword123!',
  first_name: 'John',
  last_name: 'Doe',
  accept_terms: true,
  accept_privacy_policy: true,
};


### 2. API Response Formatting

```typescript
import { Shared } from '@qeep/contracts';

// Create standardized success response
const response = Shared.DTOs.createSuccessResponse({ userId: '123', email: '<EMAIL>' }, 'User created successfully', 201);

// Create error response
const errorResponse = Shared.DTOs.createErrorResponse('Validation failed', 400, { field_errors: { email: ['Email is required'] } });
```

### 3. Type-Safe Interfaces

```typescript
import { Auth, Shared } from '@qeep/contracts';

// Use interfaces for type safety
const user: Auth.Interfaces.AuthUser = {
  id: '123',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  status: Auth.Enums.AuthStatus.ACTIVE,
  isEmailVerified: true,
  loginAttempts: 0,
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Service response with generic typing
const serviceResponse: Shared.Interfaces.ServiceResponse<Auth.Interfaces.AuthUser> = {
  success: true,
  data: user,
};
```

## 📋 Available Contracts

### Auth Domain

#### DTOs (Data Transfer Objects)

- **Signup**: `SignupRequestDto`, `SignupResponseDto`, `SignupInternalDto`
- **Login**: `LoginRequestDto`, `LoginResponseDto`, `MfaChallengeResponseDto`
- **Tokens**: `RefreshTokenRequestDto`, `RefreshTokenResponseDto`
- **Verification**: `EmailVerificationRequestDto`, `ResendVerificationRequestDto`

#### Enums

- **AuthStatus**: `ACTIVE`, `INACTIVE`, `SUSPENDED`, `PENDING`, `LOCKED`, `DELETED`
- **SessionStatus**: `ACTIVE`, `EXPIRED`, `REVOKED`, `SUSPENDED`
- **MfaMethod**: `TOTP`, `SMS`, `EMAIL`, `HARDWARE_KEY`
- **AuthEventType**: Various authentication event types for audit logging

#### Interfaces

- **AuthUser**: Complete user information for authentication contexts
- **AuthSession**: User session data
- **JwtPayload**: JWT token structure
- **MfaConfig**: Multi-factor authentication configuration
- **IAuthService**: Authentication service contract

### Shared Domain

#### DTOs

- **API Responses**: Standardized success, error, and paginated response formats
- **Metadata**: Request metadata, pagination info, error details
- **Health Checks**: Service health check response format

#### Interfaces

- **Base Entities**: `BaseEntity`, `TenantEntity`, `AuditableEntity`
- **Behaviors**: `Activatable`, `Expirable`, `Lockable`, `Taggable`, etc.
- **Service Contracts**: `ServiceResponse`, `QueryOptions`, `BulkOperationResult`
- **Event Architecture**: `DomainEvent`, `Command`, `Query`

## 🔄 Data Transformation

The library provides utility functions for transforming between external (snake_case) and internal (camelCase) formats:

```typescript
// Transform external API request to internal service format
const internalData = Auth.DTOs.transformSignupRequestToInternal(externalRequest, metadata);

// Transform internal service response to external API format
const externalResponse = Auth.DTOs.transformSignupResponseToExternal(internalResult);
```

## ✅ Validation with Zod

All DTOs use Zod for runtime validation:

```typescript
// Schema validation
const schema = Auth.DTOs.SignupRequestSchema;
const result = schema.safeParse(data);

if (result.success) {
  // Type-safe data access
  console.log(result.data.email); // TypeScript knows this is a string
} else {
  // Detailed error information
  console.log(result.error.issues);
}
```

## 🚀 Building and Testing

### Build the Library

```bash
npx nx build contracts
```

### Run Tests

```bash
npx nx test contracts
```

### Lint the Code

```bash
npx nx lint contracts
```

## 🔮 Future Domains

The following domains are planned for future implementation:

- **User Domain**: User management contracts
- **Tenant Domain**: Multi-tenancy contracts
- **Audit Domain**: Audit logging contracts
- **Notification Domain**: Notification service contracts

## 📝 Contributing

When adding new contracts:

1. **Follow Domain Structure**: Place contracts in appropriate domain folders
2. **Use Zod Schemas**: All DTOs should have corresponding Zod validation schemas
3. **Maintain Snake Case**: External APIs use snake_case, internal services use camelCase
4. **Add Transformations**: Provide utility functions for data transformation
5. **Update Namespaces**: Add new exports to the main namespace structure
6. **Document Everything**: Include comprehensive JSDoc comments

### Adding a New DTO

1. Create the Zod schema:

```typescript
export const MyRequestSchema = z.object({
  field_name: z.string().min(1),
});
```

2. Infer the TypeScript type:

```typescript
export type MyRequestDto = z.infer<typeof MyRequestSchema>;
```

3. Add transformation utilities:

```typescript
export function transformMyRequestToInternal(request: MyRequestDto): MyInternalDto {
  return { fieldName: request.field_name };
}
```

4. Export in namespace:

```typescript
export namespace MyDomain {
  export namespace DTOs {
    export import MyRequestSchema = MyDTOs.MyRequestSchema;
    export type MyRequestDto = MyDTOs.MyRequestDto;
  }
}
```

## 📄 License

This library is part of the Qeep platform and follows the same licensing terms.
