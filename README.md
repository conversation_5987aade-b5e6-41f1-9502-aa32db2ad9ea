# Qeep Backend Documentation

Welcome to the comprehensive documentation for the Qeep backend platform - a robust, scalable microservices architecture designed for financial institutions and fintech companies.

## 🏗️ Platform Overview

Qeep is a modern, cloud-native backend platform that provides:

- **Multi-tenant Architecture**: Secure tenant isolation with comprehensive onboarding
- **Microservices Design**: Scalable, maintainable service architecture
- **Real-time Processing**: Event-driven architecture with Redis and gRPC
- **Enterprise Security**: JWT authentication, role-based access control, and audit trails
- **API Gateway**: Centralized routing, rate limiting, and load balancing with Nginx

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and pnpm
- PostgreSQL 14+
- Redis 6+
- Docker (optional)

### Development Setup

```bash
# Clone and install dependencies
git clone <repository-url>
cd qeep-backend
pnpm install

# Setup environment
cp .env.example .env
# Edit .env with your configuration

# Start infrastructure
pnpm run dev:infrastructure:start

# Start services
pnpm run dev:services:start

# Verify setup
pnpm run dev:status
```

## 📚 Documentation Structure

This documentation is organized into the following sections:

- **[Architecture](architecture/)** - System design, patterns, and principles
- **[Services](services/)** - Individual microservice documentation
- **[Development](development/)** - Development guides and workflows
- **[Operations](operations/)** - Deployment, monitoring, and maintenance
- **[Security](security/)** - Authentication, authorization, and security practices
- **[Database](database/)** - Schema design, migrations, and data management
- **[Database Infrastructure](database-infrastructure.md)** - Service-isolated database architecture
- **[Mailpit Integration](mailpit-integration.md)** - Email testing and debugging in development
- **[Notification Service SMTP](notification-service-smtp.md)** - SMTP integration replacing Resend
- **[Infrastructure](infrastructure/)** - Docker, Kubernetes, and cloud deployment
- **[Testing](testing/)** - Testing strategies, frameworks, and best practices
- **[User Guides](user-guides/)** - End-user documentation and tutorials

## 🔧 Core Services

| Service                  | Port | Description                            |
| ------------------------ | ---- | -------------------------------------- |
| **API Gateway**          | 8080 | Nginx-based routing and load balancing |
| **Auth Service**         | 3001 | Authentication and authorization       |
| **User Service**         | 3002 | User management and profiles           |
| **Tenant Service**       | 3003 | Multi-tenant management and onboarding |
| **Audit Service**        | 3004 | Audit logging and compliance           |
| **Notification Service** | 3005 | Real-time notifications and alerts     |

## 🌟 Key Features

### Multi-Tenant Onboarding

- **Simplified 3-Step Process**: Organization info, admin setup, and activation
- **Flexible Configuration**: Detailed settings configurable post-onboarding
- **Compliance Ready**: Built-in audit trails and security controls

### Microservices Architecture

- **Service Isolation**: Independent deployment and scaling
- **gRPC Communication**: High-performance inter-service communication
- **Event-Driven**: Asynchronous processing with Redis pub/sub
- **Circuit Breakers**: Resilient service communication

### Developer Experience

- **Nx Monorepo**: Efficient development and build tooling
- **TypeScript**: Type-safe development across all services
- **Hot Reload**: Fast development iteration
- **Comprehensive Testing**: Unit, integration, and e2e testing

## 🔗 Quick Links

- [Getting Started Guide](development/getting-started.md)
- [API Reference](services/api-reference.md)
- [Architecture Overview](architecture/overview.md)
- [Deployment Guide](operations/deployment.md)
- [Contributing Guidelines](development/contributing.md)

## 📞 Support

For questions, issues, or contributions:

- **Documentation Issues**: Create an issue in the repository
- **Development Questions**: Check the [Development Guide](development/)
- **Architecture Questions**: Review [Architecture Documentation](architecture/)

---

**Version**: {{ book.version }}  
**Environment**: {{ book.environment }}  
**Last Updated**: {{ gitbook.time }}
