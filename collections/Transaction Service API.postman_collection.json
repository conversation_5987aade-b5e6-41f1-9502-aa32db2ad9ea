{"info": {"_postman_id": "f03c2e75-bd6e-410c-b7ab-aa1c31ee0c5f", "name": "Transaction Service API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "********", "_collection_link": "https://qeep-tech.postman.co/workspace/Team-Workspace~7c1d815d-f7bd-4c42-8bbf-8af4753cac13/collection/********-f03c2e75-bd6e-410c-b7ab-aa1c31ee0c5f?action=share&source=collection_link&creator=********"}, "item": [{"name": "🚀 Ingestion", "item": [{"name": "Process Transaction", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"transaction_id\": \"\",\n    \"amount\": 40.00,\n    \"currency\": \"GHS\",\n    \"from_account\": {\n        \"account_id\": \"\",\n        \"customer_id\": \"\",\n        \"account_type\": \"\"\n    },\n    \"to_account\": {\n        \"account_id\": \"\",\n        \"customer_id\": \"\",\n        \"account_type\": \"\"\n    },\n    \"timestamp\": \"\",\n    \"description\": \"\",\n    \"metadata\": {}\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/transactions/process", "host": ["{{base_url}}"], "path": ["transactions", "process"]}}, "response": []}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}