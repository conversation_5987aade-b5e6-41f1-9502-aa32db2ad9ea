{"info": {"_postman_id": "97fce5f2-3dcb-4337-b9dc-d3e088823c65", "name": "Notification Service API", "description": "Complete API collection for the Qeep Notification Service including all modules: Email Notifications, SMS, In-App Notifications, Queue Management, and Templates", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "46240596", "_collection_link": "https://qeep-tech.postman.co/workspace/Team-Workspace~7c1d815d-f7bd-4c42-8bbf-8af4753cac13/collection/46240596-97fce5f2-3dcb-4337-b9dc-d3e088823c65?action=share&source=collection_link&creator=46240596"}, "item": [{"name": "📧 Email Notifications", "item": [{"name": "Send Single Email", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "  const responseJson = pm.response.json();", "  if (responseJson.success && responseJson.data) {", "    pm.environment.set('notification_id', responseJson.data.notificationId);", "    console.log('Notification ID saved:', responseJson.data.notificationId);", "  }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "body": {"mode": "raw", "raw": "{\n  \"templateSlug\": \"welcome-email\",\n  \"to\": {\n    \"email\": \"<EMAIL>\",\n    \"name\": \"<PERSON>\"\n  },\n  \"from\": {\n    \"email\": \"<EMAIL>\",\n    \"name\": \"Qeep Platform\"\n  },\n  \"variables\": {\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"Doe\",\n    \"companyName\": \"Regional Community Bank\",\n    \"loginUrl\": \"https://app.qeep.com/login\",\n    \"supportEmail\": \"<EMAIL>\"\n  },\n  \"tenantId\": \"550e8400-e29b-41d4-a716-************\",\n  \"provider\": \"resend\",\n  \"tags\": [\"welcome\", \"onboarding\"],\n  \"metadata\": {\n    \"source\": \"user-registration\",\n    \"campaign\": \"welcome-series\"\n  }\n}"}, "url": {"raw": "{{base_url}}/notifications/email/send", "host": ["{{base_url}}"], "path": ["notifications", "email", "send"]}, "description": "Send a single email using a template"}, "response": []}, {"name": "Send Bulk Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "body": {"mode": "raw", "raw": "{\n  \"templateSlug\": \"monthly-newsletter\",\n  \"recipients\": [\n    {\n      \"email\": \"<EMAIL>\",\n      \"name\": \"User One\"\n    },\n    {\n      \"email\": \"<EMAIL>\",\n      \"name\": \"User Two\"\n    },\n    {\n      \"email\": \"<EMAIL>\",\n      \"name\": \"User Three\"\n    }\n  ],\n  \"from\": {\n    \"email\": \"<EMAIL>\",\n    \"name\": \"Qeep Newsletter\"\n  },\n  \"variables\": {\n    \"month\": \"December\",\n    \"year\": \"2024\",\n    \"unsubscribeUrl\": \"https://app.qeep.com/unsubscribe\"\n  },\n  \"tenantId\": \"550e8400-e29b-41d4-a716-************\",\n  \"provider\": \"resend\",\n  \"metadata\": {\n    \"campaign\": \"monthly-newsletter\",\n    \"batch\": \"december-2024\"\n  }\n}"}, "url": {"raw": "{{base_url}}/notifications/email/send-bulk", "host": ["{{base_url}}"], "path": ["notifications", "email", "send-bulk"]}, "description": "Send bulk emails to multiple recipients"}, "response": []}, {"name": "Send Email with Attachments", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "body": {"mode": "raw", "raw": "{\n  \"templateSlug\": \"document-delivery\",\n  \"to\": {\n    \"email\": \"<EMAIL>\",\n    \"name\": \"Client Name\"\n  },\n  \"from\": {\n    \"email\": \"<EMAIL>\",\n    \"name\": \"Qeep Documents\"\n  },\n  \"variables\": {\n    \"clientName\": \"Client Name\",\n    \"documentType\": \"Compliance Report\",\n    \"reportDate\": \"2024-12-15\"\n  },\n  \"attachments\": [\n    {\n      \"filename\": \"compliance-report.pdf\",\n      \"content\": \"JVBERi0xLjQKJcOkw7zDtsO4CjIgMCBvYmoKPDwKL0xlbmd0aCAzIDAgUgo+PgpzdHJlYW0KQNP...\",\n      \"contentType\": \"application/pdf\"\n    }\n  ],\n  \"tenantId\": \"550e8400-e29b-41d4-a716-************\",\n  \"tags\": [\"document\", \"compliance\"],\n  \"metadata\": {\n    \"documentId\": \"doc-12345\",\n    \"reportType\": \"monthly-compliance\"\n  }\n}"}, "url": {"raw": "{{base_url}}/notifications/email/send", "host": ["{{base_url}}"], "path": ["notifications", "email", "send"]}, "description": "Send email with file attachments"}, "response": []}, {"name": "Get Email Status", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "url": {"raw": "{{base_url}}/notifications/email/status/{{notification_id}}", "host": ["{{base_url}}"], "path": ["notifications", "email", "status", "{{notification_id}}"]}, "description": "Get delivery status of a specific email notification"}, "response": []}, {"name": "Get Email History", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "url": {"raw": "{{base_url}}/notifications/email/history/{{recipient_id}}?tenantId={{tenant_code}}&limit=20&offset=0", "host": ["{{base_url}}"], "path": ["notifications", "email", "history", "{{recipient_id}}"], "query": [{"key": "tenantId", "value": "{{tenant_code}}", "description": "Filter by tenant ID"}, {"key": "limit", "value": "20", "description": "Number of records to return (default: 50)"}, {"key": "offset", "value": "0", "description": "Number of records to skip (default: 0)"}]}, "description": "Get email history for a specific recipient"}, "response": []}, {"name": "Get Available Email Providers", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/notifications/email/providers", "host": ["{{base_url}}"], "path": ["notifications", "email", "providers"]}, "description": "Get list of available email providers and their status"}, "response": []}], "description": "Email sending, tracking, and management"}, {"name": "📱 SMS Notifications", "item": [{"name": "Send SMS", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "body": {"mode": "raw", "raw": "{\n  \"to\": \"+**********\",\n  \"message\": \"Your verification code is: 123456\",\n  \"templateSlug\": \"verification-sms\",\n  \"variables\": {\n    \"code\": \"123456\",\n    \"expiryMinutes\": \"5\"\n  },\n  \"tenantId\": \"550e8400-e29b-41d4-a716-************\",\n  \"metadata\": {\n    \"purpose\": \"verification\",\n    \"userId\": \"user-123\"\n  }\n}"}, "url": {"raw": "{{base_url}}/notifications/sms/send", "host": ["{{base_url}}"], "path": ["notifications", "sms", "send"]}, "description": "Send SMS notification (Future Implementation)"}, "response": []}, {"name": "Get SMS Status", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "url": {"raw": "{{base_url}}/notifications/sms/status/{{notification_id}}", "host": ["{{base_url}}"], "path": ["notifications", "sms", "status", "{{notification_id}}"]}, "description": "Get SMS delivery status (Future Implementation)"}, "response": []}], "description": "SMS sending and management (Future Implementation)"}, {"name": "🔔 In-App Notifications", "item": [{"name": "Send In-App Notification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"user-123\",\n  \"title\": \"New Alert Generated\",\n  \"message\": \"A new suspicious transaction alert has been generated for review.\",\n  \"type\": \"alert\",\n  \"priority\": \"high\",\n  \"actionUrl\": \"/alerts/alert-456\",\n  \"tenantId\": \"550e8400-e29b-41d4-a716-************\",\n  \"metadata\": {\n    \"alertId\": \"alert-456\",\n    \"transactionId\": \"txn-789\"\n  }\n}"}, "url": {"raw": "{{base_url}}/notifications/in-app/send", "host": ["{{base_url}}"], "path": ["notifications", "in-app", "send"]}, "description": "Send in-app notification to user (Future Implementation)"}, "response": []}, {"name": "Get User Notifications", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "url": {"raw": "{{base_url}}/notifications/in-app/user?unread=true&limit=20&offset=0", "host": ["{{base_url}}"], "path": ["notifications", "in-app", "user"], "query": [{"key": "unread", "value": "true", "description": "Filter for unread notifications only"}, {"key": "limit", "value": "20", "description": "Number of notifications to return"}, {"key": "offset", "value": "0", "description": "Number of notifications to skip"}]}, "description": "Get in-app notifications for current user (Future Implementation)"}, "response": []}, {"name": "Mark Notification as Read", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "url": {"raw": "{{base_url}}/notifications/in-app/{{notification_id}}/read", "host": ["{{base_url}}"], "path": ["notifications", "in-app", "{{notification_id}}", "read"]}, "description": "Mark in-app notification as read (Future Implementation)"}, "response": []}], "description": "In-app notification management (Future Implementation)"}, {"name": "⚙️ Queue Management", "item": [{"name": "Get Queue Statistics", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/queue/stats", "host": ["{{base_url}}"], "path": ["queue", "stats"]}, "description": "Get email queue statistics and health metrics"}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/queue/pause", "host": ["{{base_url}}"], "path": ["queue", "pause"]}, "description": "Pause email queue processing (Admin only)"}, "response": []}, {"name": "Resume Queue", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/queue/resume", "host": ["{{base_url}}"], "path": ["queue", "resume"]}, "description": "Resume email queue processing (Admin only)"}, "response": []}, {"name": "Retry Failed Jobs", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/queue/retry-failed", "host": ["{{base_url}}"], "path": ["queue", "retry-failed"]}, "description": "Retry all failed email jobs (Admin only)"}, "response": []}, {"name": "Clear Queue", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/queue/clear", "host": ["{{base_url}}"], "path": ["queue", "clear"]}, "description": "Clear all jobs from email queue (Admin only - USE WITH CAUTION)"}, "response": []}], "description": "Email queue monitoring and management"}, {"name": "📄 Email Templates", "item": [{"name": "Get Email Template", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "url": {"raw": "{{base_url}}/notifications/templates/{{template_slug}}", "host": ["{{base_url}}"], "path": ["notifications", "templates", "{{template_slug}}"]}, "description": "Get email template by slug (Future Implementation)"}, "response": []}, {"name": "Create <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "body": {"mode": "raw", "raw": "{\n  \"slug\": \"custom-alert-email\",\n  \"name\": \"Custom Alert Email\",\n  \"description\": \"Email template for custom alerts\",\n  \"subject\": \"Alert: {{alertType}} - {{alertTitle}}\",\n  \"htmlContent\": \"<html><body><h1>Alert Notification</h1><p>Dear {{recipientName}},</p><p>A {{alertType}} alert has been triggered: {{alertTitle}}</p><p>Details: {{alertDescription}}</p><p>Please review this alert in your dashboard.</p><p>Best regards,<br>Qeep Team</p></body></html>\",\n  \"textContent\": \"Alert Notification\\n\\nDear {{recipientName}},\\n\\nA {{alertType}} alert has been triggered: {{alertTitle}}\\n\\nDetails: {{alertDescription}}\\n\\nPlease review this alert in your dashboard.\\n\\nBest regards,\\nQeep Team\",\n  \"variables\": [\n    {\n      \"name\": \"recipientName\",\n      \"type\": \"string\",\n      \"required\": true,\n      \"description\": \"Name of the recipient\"\n    },\n    {\n      \"name\": \"alertType\",\n      \"type\": \"string\",\n      \"required\": true,\n      \"description\": \"Type of alert (e.g., AML, Transaction)\"\n    },\n    {\n      \"name\": \"alertTitle\",\n      \"type\": \"string\",\n      \"required\": true,\n      \"description\": \"Title of the alert\"\n    },\n    {\n      \"name\": \"alertDescription\",\n      \"type\": \"string\",\n      \"required\": false,\n      \"description\": \"Detailed description of the alert\"\n    }\n  ],\n  \"tenantId\": \"550e8400-e29b-41d4-a716-************\",\n  \"isActive\": true\n}"}, "url": {"raw": "{{base_url}}/notifications/templates", "host": ["{{base_url}}"], "path": ["notifications", "templates"]}, "description": "Create a new email template (Future Implementation)"}, "response": []}, {"name": "List Email Templates", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "url": {"raw": "{{base_url}}/notifications/templates?active=true&limit=20&offset=0", "host": ["{{base_url}}"], "path": ["notifications", "templates"], "query": [{"key": "active", "value": "true", "description": "Filter for active templates only"}, {"key": "limit", "value": "20", "description": "Number of templates to return"}, {"key": "offset", "value": "0", "description": "Number of templates to skip"}]}, "description": "Get list of email templates (Future Implementation)"}, "response": []}, {"name": "Update <PERSON><PERSON> Template", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Custom Alert Email\",\n  \"description\": \"Updated email template for custom alerts\",\n  \"subject\": \"Updated Alert: {{alertType}} - {{alertTitle}}\",\n  \"htmlContent\": \"<html><body><h1>Updated Alert Notification</h1><p>Dear {{recipientName}},</p><p>A {{alertType}} alert has been triggered: {{alertTitle}}</p><p>Details: {{alertDescription}}</p><p>Please review this alert immediately in your dashboard.</p><p>Best regards,<br>Qeep Team</p></body></html>\",\n  \"textContent\": \"Updated Alert Notification\\n\\nDear {{recipientName}},\\n\\nA {{alertType}} alert has been triggered: {{alertTitle}}\\n\\nDetails: {{alertDescription}}\\n\\nPlease review this alert immediately in your dashboard.\\n\\nBest regards,\\nQeep Team\",\n  \"isActive\": true\n}"}, "url": {"raw": "{{base_url}}/notifications/templates/{{template_slug}}", "host": ["{{base_url}}"], "path": ["notifications", "templates", "{{template_slug}}"]}, "description": "Update an existing email template (Future Implementation)"}, "response": []}, {"name": "Delete Email Template", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "url": {"raw": "{{base_url}}/notifications/templates/{{template_slug}}", "host": ["{{base_url}}"], "path": ["notifications", "templates", "{{template_slug}}"]}, "description": "Delete an email template (Future Implementation)"}, "response": []}], "description": "Email template management (Future Implementation)"}, {"name": "🔧 Service Health & Utilities", "item": [{"name": "Service Health Check", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{notification_service_url}}/v1/health", "host": ["{{notification_service_url}}"], "path": ["v1", "health"]}, "description": "Check the health status of the notification service"}, "response": []}, {"name": "Service Info", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{notification_service_url}}/v1", "host": ["{{notification_service_url}}"], "path": ["v1"]}, "description": "Get basic service information and status"}, "response": []}, {"name": "API Gateway Health", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:8080/health", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["health"]}, "description": "Check the health status of the API Gateway"}, "response": []}, {"name": "Test API Gateway Routing", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/notifications/email/providers", "host": ["{{base_url}}"], "path": ["notifications", "email", "providers"]}, "description": "Test API Gateway routing to notification service"}, "response": []}], "description": "Service health checks, monitoring, and utility endpoints"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-generate request ID for tracing", "pm.globals.set('request_id', pm.variables.replaceIn('{{$guid}}'));", "", "// Set common headers", "pm.request.headers.add({", "  key: 'X-Request-ID',", "  value: pm.globals.get('request_id')", "});", "", "// Add tenant context if available", "if (pm.environment.get('tenant_code')) {", "  pm.request.headers.add({", "    key: 'X-Tenant-Code',", "    value: pm.environment.get('tenant_code')", "  });", "}", "", "// Log request details", "console.log('Request ID:', pm.globals.get('request_id'));", "console.log('Request URL:', pm.request.url.toString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Common response validation", "pm.test('Response time is reasonable', function () {", "  pm.expect(pm.response.responseTime).to.be.below(10000);", "});", "", "pm.test('Response has correct content type', function () {", "  pm.expect(pm.response.headers.get('Content-Type')).to.include('application/json');", "});", "", "// Log response details", "console.log('Response Status:', pm.response.status);", "console.log('Response Time:', pm.response.responseTime + 'ms');", "", "// Parse response if JSON", "if (pm.response.headers.get('Content-Type').includes('application/json')) {", "  try {", "    const responseJson = pm.response.json();", "    console.log('Response Body:', JSON.stringify(response<PERSON><PERSON>, null, 2));", "    ", "    // Validate response format", "    if (responseJson.success !== undefined) {", "      pm.test('Response follows expected format', function () {", "        pm.expect(response<PERSON>son).to.have.property('success');", "      });", "    }", "    ", "    // Auto-save notification ID from email responses", "    if (responseJson.success && responseJson.data && responseJson.data.notificationId) {", "      pm.environment.set('notification_id', responseJson.data.notificationId);", "      console.log('Notification ID automatically saved:', responseJson.data.notificationId);", "    }", "  } catch (e) {", "    console.log('Response is not valid JSON');", "  }", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080/api/v1"}, {"key": "notification_service_url", "value": "http://localhost:3004/api"}, {"key": "access_token", "value": ""}, {"key": "tenant_code", "value": "demo"}, {"key": "notification_id", "value": ""}, {"key": "recipient_id", "value": ""}, {"key": "template_slug", "value": "welcome-email"}]}