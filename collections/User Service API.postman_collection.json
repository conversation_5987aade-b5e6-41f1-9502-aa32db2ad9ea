{"info": {"_postman_id": "57d50fa9-b561-44b7-9650-fa3e1c751ce9", "name": "User Service API", "description": "Complete API collection for the Qeep User Service including all modules: User Management, Session Management, Settings, and User Operations", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "46240596", "_collection_link": "https://qeep-tech.postman.co/workspace/Team-Workspace~7c1d815d-f7bd-4c42-8bbf-8af4753cac13/collection/46240596-57d50fa9-b561-44b7-9650-fa3e1c751ce9?action=share&source=collection_link&creator=46240596"}, "item": [{"name": "👤 User Management", "item": [{"name": "Create User (Internal)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Service-Auth", "value": "internal-service-token", "description": "Internal service authentication"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"passwordHash\": \"$2b$10$hashed_password_here\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Doe\",\n  \"tenantCode\": \"{{tenant_code}}\",\n  \"status\": \"ACTIVE\",\n  \"isEmailVerified\": false,\n  \"metadata\": {\n    \"requestId\": \"req-123\",\n    \"timestamp\": \"2024-12-15T10:00:00Z\",\n    \"processingTime\": 0\n  }\n}"}, "url": {"raw": "{{user_service_url}}/grpc/users", "host": ["{{user_service_url}}"], "path": ["grpc", "users"]}, "description": "Create a new user (Internal gRPC endpoint simulation)"}, "response": []}, {"name": "Get User by ID (Internal)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Service-Auth", "value": "internal-service-token"}], "url": {"raw": "{{user_service_url}}/grpc/users/{{user_id}}", "host": ["{{user_service_url}}"], "path": ["grpc", "users", "{{user_id}}"]}, "description": "Get user by ID (Internal gRPC endpoint simulation)"}, "response": []}, {"name": "Get User by Email (Internal)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Service-Auth", "value": "internal-service-token"}], "url": {"raw": "{{user_service_url}}/grpc/users/email/<EMAIL>?tenantCode={{tenant_code}}", "host": ["{{user_service_url}}"], "path": ["grpc", "users", "email", "<EMAIL>"], "query": [{"key": "tenantCode", "value": "{{tenant_code}}", "description": "Tenant code for user lookup"}]}, "description": "Get user by email and tenant (Internal gRPC endpoint simulation)"}, "response": []}, {"name": "Update User (Internal)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Service-Auth", "value": "internal-service-token"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"{{user_id}}\",\n  \"firstName\": \"John Updated\",\n  \"lastName\": \"Doe Updated\",\n  \"status\": \"ACTIVE\",\n  \"isEmailVerified\": true,\n  \"metadata\": {\n    \"requestId\": \"req-456\",\n    \"timestamp\": \"2024-12-15T10:30:00Z\",\n    \"processingTime\": 0\n  }\n}"}, "url": {"raw": "{{user_service_url}}/grpc/users/{{user_id}}", "host": ["{{user_service_url}}"], "path": ["grpc", "users", "{{user_id}}"]}, "description": "Update user information (Internal gRPC endpoint simulation)"}, "response": []}, {"name": "Update User Login Info (Internal)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Service-Auth", "value": "internal-service-token"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"{{user_id}}\",\n  \"lastLoginAt\": \"2024-12-15T10:45:00Z\",\n  \"loginCount\": 25,\n  \"lastLoginIp\": \"*************\",\n  \"metadata\": {\n    \"requestId\": \"req-789\",\n    \"timestamp\": \"2024-12-15T10:45:00Z\",\n    \"processingTime\": 0\n  }\n}"}, "url": {"raw": "{{user_service_url}}/grpc/users/{{user_id}}/login-info", "host": ["{{user_service_url}}"], "path": ["grpc", "users", "{{user_id}}", "login-info"]}, "description": "Update user login information (Internal gRPC endpoint simulation)"}, "response": []}, {"name": "Update User Password (Internal)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Service-Auth", "value": "internal-service-token"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"{{user_id}}\",\n  \"passwordHash\": \"$2b$10$new_hashed_password_here\",\n  \"metadata\": {\n    \"requestId\": \"req-101\",\n    \"timestamp\": \"2024-12-15T11:00:00Z\",\n    \"processingTime\": 0\n  }\n}"}, "url": {"raw": "{{user_service_url}}/grpc/users/{{user_id}}/password", "host": ["{{user_service_url}}"], "path": ["grpc", "users", "{{user_id}}", "password"]}, "description": "Update user password hash (Internal gRPC endpoint simulation)"}, "response": []}, {"name": "Soft Delete User (Internal)", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Service-Auth", "value": "internal-service-token"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"{{user_id}}\",\n  \"reason\": \"User requested account deletion\",\n  \"metadata\": {\n    \"requestId\": \"req-202\",\n    \"timestamp\": \"2024-12-15T11:15:00Z\",\n    \"processingTime\": 0\n  }\n}"}, "url": {"raw": "{{user_service_url}}/grpc/users/{{user_id}}/soft-delete", "host": ["{{user_service_url}}"], "path": ["grpc", "users", "{{user_id}}", "soft-delete"]}, "description": "Soft delete user (mark as deleted) (Internal gRPC endpoint simulation)"}, "response": []}, {"name": "<PERSON>ore User (Internal)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Service-Auth", "value": "internal-service-token"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"{{user_id}}\",\n  \"reason\": \"Administrative restoration\",\n  \"metadata\": {\n    \"requestId\": \"req-303\",\n    \"timestamp\": \"2024-12-15T11:30:00Z\",\n    \"processingTime\": 0\n  }\n}"}, "url": {"raw": "{{user_service_url}}/grpc/users/{{user_id}}/restore", "host": ["{{user_service_url}}"], "path": ["grpc", "users", "{{user_id}}", "restore"]}, "description": "Restore soft-deleted user (Internal gRPC endpoint simulation)"}, "response": []}, {"name": "Hard Delete User (Internal)", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Service-Auth", "value": "internal-service-token"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"{{user_id}}\",\n  \"reason\": \"GDPR compliance - permanent deletion\",\n  \"metadata\": {\n    \"requestId\": \"req-404\",\n    \"timestamp\": \"2024-12-15T11:45:00Z\",\n    \"processingTime\": 0\n  }\n}"}, "url": {"raw": "{{user_service_url}}/grpc/users/{{user_id}}/hard-delete", "host": ["{{user_service_url}}"], "path": ["grpc", "users", "{{user_id}}", "hard-delete"]}, "description": "Permanently delete user (Internal gRPC endpoint simulation - USE WITH EXTREME CAUTION)"}, "response": []}, {"name": "Get Deleted Users (Internal)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Service-Auth", "value": "internal-service-token"}], "url": {"raw": "{{user_service_url}}/grpc/users/deleted?limit=20&offset=0", "host": ["{{user_service_url}}"], "path": ["grpc", "users", "deleted"], "query": [{"key": "limit", "value": "20", "description": "Number of deleted users to return (default: 50)"}, {"key": "offset", "value": "0", "description": "Number of records to skip (default: 0)"}]}, "description": "Get list of soft-deleted users (Internal gRPC endpoint simulation)"}, "response": []}], "description": "Core user CRUD operations and management (gRPC endpoints - for internal service communication)"}, {"name": "⚙️ User Settings", "item": [{"name": "Get User Settings", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "url": {"raw": "{{base_url}}/users/settings", "host": ["{{base_url}}"], "path": ["users", "settings"]}, "description": "Get current user's settings and preferences (Future Implementation)"}, "response": []}, {"name": "Update User Settings", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "body": {"mode": "raw", "raw": "{\n  \"preferences\": {\n    \"theme\": \"dark\",\n    \"language\": \"en\",\n    \"timezone\": \"America/New_York\",\n    \"dateFormat\": \"MM/DD/YYYY\",\n    \"timeFormat\": \"12h\"\n  },\n  \"notifications\": {\n    \"email\": {\n      \"alerts\": true,\n      \"reports\": true,\n      \"marketing\": false\n    },\n    \"inApp\": {\n      \"alerts\": true,\n      \"mentions\": true,\n      \"updates\": false\n    },\n    \"sms\": {\n      \"critical\": true,\n      \"alerts\": false\n    }\n  },\n  \"dashboard\": {\n    \"defaultView\": \"alerts\",\n    \"refreshInterval\": 30,\n    \"showTutorials\": false\n  }\n}"}, "url": {"raw": "{{base_url}}/users/settings", "host": ["{{base_url}}"], "path": ["users", "settings"]}, "description": "Update user settings and preferences (Future Implementation)"}, "response": []}, {"name": "Reset User Settings", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "url": {"raw": "{{base_url}}/users/settings/reset", "host": ["{{base_url}}"], "path": ["users", "settings", "reset"]}, "description": "Reset user settings to default values (Future Implementation)"}, "response": []}], "description": "User preferences and settings management (Future Implementation)"}, {"name": "👥 User Profile & RBAC", "item": [{"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "url": {"raw": "{{base_url}}/users/profile", "host": ["{{base_url}}"], "path": ["users", "profile"]}, "description": "Get current user's profile information (Future Implementation)"}, "response": []}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON> Updated\",\n  \"lastName\": \"<PERSON>e Updated\",\n  \"phoneNumber\": \"******-0123\",\n  \"jobTitle\": \"Senior Compliance Officer\",\n  \"department\": \"Risk Management\",\n  \"bio\": \"Experienced compliance professional with 10+ years in AML\",\n  \"avatar\": \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...\"\n}"}, "url": {"raw": "{{base_url}}/users/profile", "host": ["{{base_url}}"], "path": ["users", "profile"]}, "description": "Update user profile information (Future Implementation)"}, "response": []}, {"name": "Get User Roles", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "url": {"raw": "{{base_url}}/users/roles", "host": ["{{base_url}}"], "path": ["users", "roles"]}, "description": "Get current user's roles and permissions (Future Implementation)"}, "response": []}, {"name": "Check User Permission", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-Tenant-Code", "value": "{{tenant_code}}"}], "body": {"mode": "raw", "raw": "{\n  \"permission\": \"alerts.view\"\n}"}, "url": {"raw": "{{base_url}}/users/permissions/check", "host": ["{{base_url}}"], "path": ["users", "permissions", "check"]}, "description": "Check if user has specific permission (Future Implementation)"}, "response": []}, {"name": "Get User with Roles and Permissions (Internal)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Service-Auth", "value": "internal-service-token"}], "url": {"raw": "{{user_service_url}}/grpc/users/{{user_id}}/roles-permissions", "host": ["{{user_service_url}}"], "path": ["grpc", "users", "{{user_id}}", "roles-permissions"]}, "description": "Get user with complete roles and permissions (Internal gRPC endpoint simulation)"}, "response": []}], "description": "User profile management and role-based access control (Future Implementation)"}, {"name": "🔧 Service Health & Utilities", "item": [{"name": "Service Health Check", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/v1/health", "host": ["{{base_url}}"], "path": ["v1", "health"]}, "description": "Check the health status of the user service"}, "response": []}, {"name": "Service Info", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}}/v1", "host": ["{{base_url}}}"], "path": ["v1"]}, "description": "Get basic service information and status"}, "response": []}, {"name": "Database Health Check", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/health/database", "host": ["{{base_url}}"], "path": ["health", "database"]}, "description": "Check database connectivity and health"}, "response": []}, {"name": "API Gateway Health", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check the health status of the API Gateway"}, "response": []}, {"name": "Test gRPC Connectivity", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Service-Auth", "value": "internal-service-token"}], "url": {"raw": "{{user_service_url}}/grpc/health", "host": ["{{user_service_url}}"], "path": ["grpc", "health"]}, "description": "Test gRPC service connectivity and health"}, "response": []}], "description": "Service health checks, monitoring, and utility endpoints"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-generate request ID for tracing", "pm.globals.set('request_id', pm.variables.replaceIn('{{$guid}}'));", "", "// Set common headers", "pm.request.headers.add({", "  key: 'X-Request-ID',", "  value: pm.globals.get('request_id')", "});", "", "// Add tenant context if available", "if (pm.environment.get('tenant_code')) {", "  pm.request.headers.add({", "    key: 'X-Tenant-Code',", "    value: pm.environment.get('tenant_code')", "  });", "}", "", "// Log request details", "console.log('Request ID:', pm.globals.get('request_id'));", "console.log('Request URL:', pm.request.url.toString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Common response validation", "pm.test('Response time is reasonable', function () {", "  pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Response has correct content type', function () {", "  pm.expect(pm.response.headers.get('Content-Type')).to.include('application/json');", "});", "", "// Log response details", "console.log('Response Status:', pm.response.status);", "console.log('Response Time:', pm.response.responseTime + 'ms');", "", "// Parse response if JSON", "if (pm.response.headers.get('Content-Type').includes('application/json')) {", "  try {", "    const responseJson = pm.response.json();", "    console.log('Response Body:', JSON.stringify(response<PERSON><PERSON>, null, 2));", "    ", "    // Validate gRPC response format", "    if (responseJson.success !== undefined) {", "      pm.test('Response follows gRPC response format', function () {", "        pm.expect(response<PERSON>son).to.have.property('success');", "        if (responseJson.metadata) {", "          pm.expect(responseJson.metadata).to.have.property('requestId');", "          pm.expect(responseJson.metadata).to.have.property('timestamp');", "        }", "      });", "    }", "    ", "    // Auto-save user ID from user creation responses", "    if (responseJson.success && responseJson.user && responseJson.user.id) {", "      pm.environment.set('user_id', responseJson.user.id);", "      console.log('User ID automatically saved:', responseJson.user.id);", "    }", "    ", "    // Auto-save session ID from session creation responses", "    if (responseJson.success && responseJson.session && responseJson.session.sessionId) {", "      pm.environment.set('session_id', responseJson.session.sessionId);", "      console.log('Session ID automatically saved:', responseJson.session.sessionId);", "    }", "  } catch (e) {", "    console.log('Response is not valid JSON');", "  }", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080/api/v1"}, {"key": "access_token", "value": ""}, {"key": "user_id", "value": ""}, {"key": "tenant_code", "value": "demo"}, {"key": "tenant_id", "value": "550e8400-e29b-41d4-a716-446655440000"}, {"key": "session_id", "value": ""}]}