{"id": "7defa9b0-78da-49a6-a377-e35589ee579a", "name": "Local", "values": [{"key": "base_url", "value": "http://localhost:8080/api/v1", "type": "secret", "enabled": true}, {"key": "tenant_id", "value": "", "type": "secret", "enabled": true}, {"key": "device_id", "value": "", "type": "default", "enabled": true}, {"key": "tenant_code", "value": "", "type": "secret", "enabled": true}, {"key": "tenant_base_url", "value": "", "type": "secret", "enabled": true}, {"key": "access_token", "value": "", "type": "secret", "enabled": true}, {"key": "refresh_token", "value": "", "type": "secret", "enabled": true}, {"key": "user_email", "value": "", "type": "default", "enabled": true}, {"key": "user_id", "value": "", "type": "any", "enabled": true}, {"key": "onboarding_workflow_id", "value": "", "type": "any", "enabled": true}, {"key": "onboarding_task_id", "value": "", "type": "any", "enabled": true}, {"key": "session_token", "value": "", "type": "any", "enabled": true}, {"key": "challenge_id", "value": "", "type": "any", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-07-21T04:07:05.786Z", "_postman_exported_using": "Postman/11.54.6"}