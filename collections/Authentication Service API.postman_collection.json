{"info": {"_postman_id": "4c39f55f-d9cb-4b64-aa83-e46b6eb1b1de", "name": "Authentication Service API", "description": "Complete API collection for the Qeep Authentication Service including all modules: Authentication, Account Management, MFA, WebAuthn, Session Management, Password Management, Security Settings, Token Management, and Health", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "********", "_collection_link": "https://qeep-tech.postman.co/workspace/Team-Workspace~7c1d815d-f7bd-4c42-8bbf-8af4753cac13/collection/********-4c39f55f-d9cb-4b64-aa83-e46b6eb1b1de?action=share&source=collection_link&creator=********"}, "item": [{"name": "🔐 Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "  const responseJson = pm.response.json();", "  if (responseJson.success && responseJson.data) {", "    pm.environment.set('access_token', responseJson.data.access_token);", "    pm.environment.set('refresh_token', responseJson.data.refresh_token);", "    pm.environment.set('user_id', responseJson.data.user.id);", "    pm.environment.set('session_token', responseJson.data.session.session_token);", "    console.log('Tokens saved to environment');", "  }", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"superadmin123\",\n  \"remember_me\": true,\n  \"device_id\": \"postman-test-device\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}, "description": "Authenticate user and receive access/refresh tokens"}, "response": []}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "  const responseJson = pm.response.json();", "  if (responseJson.success && responseJson.data) {", "    pm.environment.set('access_token', responseJson.data.accessToken);", "    pm.environment.set('refresh_token', responseJson.data.refreshToken);", "    console.log('New tokens saved to environment');", "  }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/refresh-token", "host": ["{{base_url}}"], "path": ["auth", "refresh-token"]}, "description": "Refresh access token using refresh token"}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}, "description": "Logout user and invalidate tokens"}, "response": []}, {"name": "Revoke Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/revoke-token", "host": ["{{base_url}}"], "path": ["auth", "revoke-token"]}, "description": "Revoke a specific token"}, "response": []}, {"name": "Revoke All Tokens", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/revoke-all-tokens", "host": ["{{base_url}}"], "path": ["auth", "revoke-all-tokens"]}, "description": "Revoke all user tokens (force logout from all devices)"}, "response": []}], "description": "Core authentication flows - login, logout, token management"}, {"name": "👤 Account Management", "item": [{"name": "Signup", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "  const responseJson = pm.response.json();", "  if (responseJson.success && responseJson.data) {", "    pm.environment.set('user_id', responseJson.data.userId);", "    pm.environment.set('user_email', responseJson.data.email);", "    console.log('User ID saved to environment:', responseJson.data.userId);", "  }", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{$randomEmail}}\",\n  \"password\": \"SecurePass123!@#\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"accept_terms\": true,\n  \"accept_privacy\": true,\n  \"accept_marketing_emails\": false\n}"}, "url": {"raw": "{{base_url}}/auth/signup", "host": ["{{base_url}}"], "path": ["auth", "signup"]}, "description": "Create a new user account"}, "response": []}, {"name": "Check Email Availability", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/auth/check-email", "host": ["{{base_url}}"], "path": ["auth", "check-email"]}, "description": "Check if email is available for registration"}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/auth/verify-email?token=860e5fdf-0763-49c9-b142-761e287223f0", "host": ["{{base_url}}"], "path": ["auth", "verify-email"], "query": [{"key": "token", "value": "860e5fdf-0763-49c9-b142-761e287223f0", "description": "Email verification token from email"}]}, "description": "Verify user email address using token from email"}, "response": []}, {"name": "Resend Verification Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{user_email}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/resend-verification", "host": ["{{base_url}}"], "path": ["auth", "resend-verification"]}, "description": "Resend email verification link"}, "response": []}, {"name": "Get Current User Profile", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/me", "host": ["{{base_url}}"], "path": ["auth", "me"]}, "description": "Get current authenticated user profile information"}, "response": []}, {"name": "Unlock Account (Admin)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"{{user_id}}\",\n  \"reason\": \"Administrative unlock after security review\"\n}"}, "url": {"raw": "{{base_url}}/auth/unlock", "host": ["{{base_url}}"], "path": ["auth", "unlock"]}, "description": "Unlock a locked user account (requires admin privileges)"}, "response": []}], "description": "User account lifecycle management - signup, verification, profile"}, {"name": "🛡️ Multi-Factor Authentication (MFA)", "item": [{"name": "Setup MFA", "event": [{"listen": "test", "script": {"exec": ["   const jsonData = pm.response.json().data;", "   const base64Image = jsonData.qr_code;", "   const template = `<img src=\"${base64Image}\" />`;", "   pm.visualizer.set(template, { response: jsonData });"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"method\": \"TOTP\",\n  \"device_name\": \"My Phone\"\n}"}, "url": {"raw": "{{base_url}}/auth/mfa/setup", "host": ["{{base_url}}"], "path": ["auth", "mfa", "setup"]}, "description": "Initialize MFA setup (returns QR code for TOTP)"}, "response": []}, {"name": "Verify MFA Setup", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"method\": \"TOTP\",\n  \"code\": \"900215\"\n}"}, "url": {"raw": "{{base_url}}/auth/mfa/verify-setup", "host": ["{{base_url}}"], "path": ["auth", "mfa", "verify-setup"]}, "description": "Complete MFA setup by verifying TOTP code"}, "response": []}, {"name": "Create MFA Challenge", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "  const responseJson = pm.response.json();", "  if (responseJson.success && responseJson.data) {", "    pm.environment.set('challenge_id', responseJson.data.challenge_id);", "  }", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"method\":\"TOTP\",\n  \"session_token\": \"{{session_token}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/mfa/challenge", "host": ["{{base_url}}"], "path": ["auth", "mfa", "challenge"]}, "description": "Create MFA challenge during login process"}, "response": []}, {"name": "Verify MFA Challenge", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"challenge_id\": \"{{challenge_id}}\",\n  \"code\": \"460256\"\n}"}, "url": {"raw": "{{base_url}}/auth/mfa/verify", "host": ["{{base_url}}"], "path": ["auth", "mfa", "verify"]}, "description": "Verify MFA challenge with TOTP code"}, "response": []}, {"name": "Get MFA Status", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/auth/mfa/status", "host": ["{{base_url}}"], "path": ["auth", "mfa", "status"]}, "description": "Get current MFA status and configuration"}, "response": []}, {"name": "Disable MFA", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"password\": \"superadmin123\"\n}"}, "url": {"raw": "{{base_url}}/auth/mfa/disable", "host": ["{{base_url}}"], "path": ["auth", "mfa", "disable"]}, "description": "Disable MFA for the user account"}, "response": []}, {"name": "Regenerate Backup Codes", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"password\": \"superadmin123\"\n}"}, "url": {"raw": "{{base_url}}/auth/mfa/regenerate-backup-codes", "host": ["{{base_url}}"], "path": ["auth", "mfa", "regenerate-backup-codes"]}, "description": "Generate new MFA backup codes"}, "response": []}], "description": "MFA setup, verification, and management"}, {"name": "🔑 Password Management", "item": [{"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{user_email}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/forgot-password", "host": ["{{base_url}}"], "path": ["auth", "forgot-password"]}, "description": "Request password reset email"}, "response": []}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"d1ccba57-d94a-40d4-b702-23bf2de732c5\",\n  \"newPassword\": \"NewSecurePass123!@#\"\n}"}, "url": {"raw": "{{base_url}}/auth/reset-password", "host": ["{{base_url}}"], "path": ["auth", "reset-password"]}, "description": "Reset password using token from email"}, "response": []}, {"name": "Change Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"NewSecurePass123!@#\",\n  \"newPassword\": \"SecurePass123!@#\"\n}"}, "url": {"raw": "{{base_url}}/auth/change-password", "host": ["{{base_url}}"], "path": ["auth", "change-password"]}, "description": "Change password for authenticated user"}, "response": []}], "description": "Password operations - forgot, reset, change password"}, {"name": "🔐 WebAuthn / Passkeys", "item": [{"name": "Start Passkey Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"displayName\": \"My Security Key\"\n}"}, "url": {"raw": "{{base_url}}/webauthn/register/start", "host": ["{{base_url}}"], "path": ["webauthn", "register", "start"]}, "description": "Start passkey registration process"}, "response": []}, {"name": "Complete Passkey Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"credential\": {\n    \"id\": \"credential-id\",\n    \"rawId\": \"raw-credential-id\",\n    \"response\": {\n      \"attestationObject\": \"attestation-object\",\n      \"clientDataJSON\": \"client-data-json\"\n    },\n    \"type\": \"public-key\"\n  }\n}"}, "url": {"raw": "{{base_url}}/webauthn/register/complete", "host": ["{{base_url}}"], "path": ["webauthn", "register", "complete"]}, "description": "Complete passkey registration with credential data"}, "response": []}, {"name": "Start WebAuthn Authentication", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/webauthn/authenticate/start", "host": ["{{base_url}}"], "path": ["webauthn", "authenticate", "start"]}, "description": "Start WebAuthn authentication process"}, "response": []}, {"name": "Complete WebAuthn Authentication", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"credential\": {\n    \"id\": \"credential-id\",\n    \"rawId\": \"raw-credential-id\",\n    \"response\": {\n      \"authenticatorData\": \"authenticator-data\",\n      \"clientDataJSON\": \"client-data-json\",\n      \"signature\": \"signature\"\n    },\n    \"type\": \"public-key\"\n  }\n}"}, "url": {"raw": "{{base_url}}/webauthn/authenticate/complete", "host": ["{{base_url}}"], "path": ["webauthn", "authenticate", "complete"]}, "description": "Complete WebAuthn authentication with credential data"}, "response": []}, {"name": "List User Passkeys", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/webauthn/passkeys", "host": ["{{base_url}}"], "path": ["webauthn", "passkeys"]}, "description": "Get list of user's registered passkeys"}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Security Key Name\"\n}"}, "url": {"raw": "{{base_url}}/webauthn/passkeys/:passkeyId/rename", "host": ["{{base_url}}"], "path": ["webauthn", "passkeys", ":passkeyId", "rename"], "variable": [{"key": "passkeyId", "value": "passkey-u<PERSON>", "description": "Passkey ID to rename"}]}, "description": "Rename a registered passkey"}, "response": []}, {"name": "Remove <PERSON>", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/webauthn/passkeys/:passkeyId", "host": ["{{base_url}}"], "path": ["webauthn", "passkeys", ":passkeyId"], "variable": [{"key": "passkeyId", "value": "passkey-u<PERSON>", "description": "Passkey ID to remove"}]}, "description": "Remove a registered passkey"}, "response": []}], "description": "FIDO2/WebAuthn authentication and passkey management"}, {"name": "📱 Session Management", "item": [{"name": "Get Active Sessions", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/sessions", "host": ["{{base_url}}"], "path": ["auth", "sessions"]}, "description": "Get list of active user sessions"}, "response": []}, {"name": "Terminate Session", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/sessions/:sessionId", "host": ["{{base_url}}"], "path": ["auth", "sessions", ":sessionId"], "variable": [{"key": "sessionId", "value": "session-uuid", "description": "Session ID to terminate"}]}, "description": "Terminate a specific session"}, "response": []}, {"name": "Force Logout All Sessions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/force-logout-all", "host": ["{{base_url}}"], "path": ["auth", "force-logout-all"]}, "description": "Force logout from all sessions (emergency action)"}, "response": []}], "description": "User session monitoring and management"}, {"name": "🔒 Security Settings", "item": [{"name": "Get Security Settings", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/security-settings", "host": ["{{base_url}}"], "path": ["auth", "security-settings"]}, "description": "Get current user security settings and preferences"}, "response": []}], "description": "User security preferences and settings"}, {"name": "🔧 Service Health & Utilities", "item": [{"name": "Service Health Check", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{auth_service_url}}/v1/health", "host": ["{{auth_service_url}}"], "path": ["v1", "health"]}, "description": "Check the health status of the auth service"}, "response": []}, {"name": "Service Readiness Check", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/health/ready", "host": ["{{base_url}}"], "path": ["health", "ready"]}, "description": "Check if the auth service is ready to accept requests"}, "response": []}, {"name": "Service Liveness Check", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/health/live", "host": ["{{base_url}}"], "path": ["health", "live"]}, "description": "Check if the auth service is alive and responding"}, "response": []}, {"name": "Detailed Health Check", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/health/detailed", "host": ["{{base_url}}"], "path": ["health", "detailed"]}, "description": "Get detailed health information including dependencies"}, "response": []}, {"name": "Service Info", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{auth_service_url}}/v1", "host": ["{{auth_service_url}}"], "path": ["v1"]}, "description": "Get basic service information and status"}, "response": []}, {"name": "Auth Health Check", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/health", "host": ["{{base_url}}"], "path": ["auth", "health"]}, "description": "Check auth-specific health status"}, "response": []}, {"name": "API Gateway Health", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:8080/health", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["health"]}, "description": "Check the health status of the API Gateway"}, "response": []}], "description": "Service health checks, monitoring, and utility endpoints"}, {"name": "🚗Device Management", "item": [{"name": "List Trusted Devices", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json().data", "if (jsonData && jsonData.devices) {", "    pm.environment.set(\"device_id\", jsonData.devices[0].id);", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/devices", "host": ["{{base_url}}"], "path": ["auth", "devices"]}, "description": "List all trusted devices for the authenticated user with pagination and filtering options"}, "response": []}, {"name": "Register Trusted Device", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "if (jsonData && jsonData.data && jsonData.data.id) {", "    pm.environment.set(\"device_id\", jsonData.data.id);", "    console.log(\"Device ID saved: \" + jsonData.data.id);", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"device_info\": {\n    \"user_agent\": \"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1\",\n    \"platform\": \"iPhone\",\n    \"screen_resolution\": \"390x844\",\n    \"language\": \"en-US\",\n    \"timezone\": \"America/New_York\"\n  },\n  \"device_name\": \"My iPhone\",\n  \"device_type\": \"Mobile\",\n  \"trust_duration_days\": 90\n}"}, "url": {"raw": "{{base_url}}/auth/devices/trust", "host": ["{{base_url}}"], "path": ["auth", "devices", "trust"]}, "description": "Register a new trusted device for the authenticated user"}, "response": []}, {"name": "Get Device Trust Status", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_agent\": \"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1\",\n  \"platform\": \"iPhone\",\n  \"screen_resolution\": \"390x844\",\n  \"language\": \"en-US\",\n  \"timezone\": \"America/New_York\"\n}"}, "url": {"raw": "{{base_url}}/auth/devices/{{device_id}}", "host": ["{{base_url}}"], "path": ["auth", "devices", "{{device_id}}"]}, "description": "Check if a device is trusted for the authenticated user"}, "response": []}, {"name": "Revoke Trusted Device", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/auth/devices/{{deviceId}}", "host": ["{{base_url}}"], "path": ["auth", "devices", "{{deviceId}}"]}, "description": "Revoke trust for a specific device"}, "response": []}, {"name": "Revoke All Trusted Devices", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/auth/devices/all", "host": ["{{base_url}}"], "path": ["auth", "devices", "all"]}, "description": "Revoke all trusted devices for the authenticated user"}, "response": []}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-generate request ID for tracing", "pm.globals.set('request_id', pm.variables.replaceIn('{{$guid}}'));", "", "// Set common headers", "pm.request.headers.add({", "  key: 'X-Request-ID',", "  value: pm.globals.get('request_id')", "});", "", "// Add tenant context if available", "if (pm.environment.get('tenant_code')) {", "  pm.request.headers.add({", "    key: 'X-Tenant-Code',", "    value: pm.environment.get('tenant_code')", "  });", "}", "", "// Add tenant context if available", "if (pm.environment.get('device_id')) {", "  pm.request.headers.add({", "    key: 'X-Device-ID',", "    value: pm.environment.get('device_id')", "  });", "}", "", "// Log request details", "console.log('Request ID:', pm.globals.get('request_id'));", "console.log('Request URL:', pm.request.url.toString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Common response validation", "pm.test('Response time is reasonable', function () {", "  pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Response has correct content type', function () {", "  pm.expect(pm.response.headers.get('Content-Type')).to.include('application/json');", "});", "", "// Log response details", "console.log('Response Status:', pm.response.status);", "console.log('Response Time:', pm.response.responseTime + 'ms');", "", "// Parse response if JSON", "if (pm.response.headers.get('Content-Type').includes('application/json')) {", "  try {", "    const responseJson = pm.response.json();", "    console.log('Response Body:', JSON.stringify(response<PERSON><PERSON>, null, 2));", "    ", "    // Validate StandardApiResponse format", "    if (responseJson.success !== undefined) {", "      pm.test('Response follows StandardApiResponse format', function () {", "        pm.expect(response<PERSON>son).to.have.property('success');", "        pm.expect(responseJson).to.have.property('statusCode');", "        pm.expect(responseJson).to.have.property('message');", "      });", "    }", "    ", "    // Auto-save tokens from login responses", "    if (responseJson.success && responseJson.data && responseJson.data.accessToken) {", "      pm.environment.set('access_token', responseJson.data.accessToken);", "      if (responseJson.data.refreshToken) {", "        pm.environment.set('refresh_token', responseJson.data.refreshToken);", "      }", "      console.log('Tokens automatically saved to environment');", "    }", "  } catch (e) {", "    console.log('Response is not valid JSON');", "  }", "}"]}}]}