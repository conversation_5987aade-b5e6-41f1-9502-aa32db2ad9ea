{"info": {"_postman_id": "1ba145a2-2e19-4eac-ab9f-9e245567b4ec", "name": "Tenant Service API", "description": "Collection for testing tenant service API keys and webhook endpoints with CUID-prefixed IDs using snake_case naming convention", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "46240596", "_collection_link": "https://qeep-tech.postman.co/workspace/Team-Workspace~7c1d815d-f7bd-4c42-8bbf-8af4753cac13/collection/46240596-1ba145a2-2e19-4eac-ab9f-9e245567b4ec?action=share&source=collection_link&creator=46240596"}, "item": [{"name": "🏢 Tenants", "item": [{"name": "Get All Tenants", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/tenants", "host": ["{{base_url}}"], "path": ["tenants"]}}, "response": []}, {"name": "Get Tenant by ID", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}"]}}, "response": []}, {"name": "Create Tenant", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"test-bank\",\n  \"display_name\": \"Test Bank Ltd\",\n  \"code\": \"TESTBANK\",\n  \"industry\": \"Banking\",\n  \"jurisdiction\": \"Ghana\",\n  \"plan\": \"premium\",\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{base_url}}/tenants", "host": ["{{base_url}}"], "path": ["tenants"]}}, "response": []}]}, {"name": "🔑 API Keys", "item": [{"name": "Get All API Keys for Tenant", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/api-keys", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "api-keys"]}}, "response": []}, {"name": "Get API Key by ID", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/api-keys/{{api_key_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "api-keys", "{{api_key_id}}"]}}, "response": []}, {"name": "Create API Key", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Production API Key\",\n  \"environment\": \"LIVE\",\n  \"expires_at\": null,\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/api-keys", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "api-keys"]}}, "response": []}, {"name": "Update API Key", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated API Key Name\",\n  \"is_active\": true,\n  \"expires_at\": \"2025-12-31T23:59:59Z\"\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/api-keys/{{api_key_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "api-keys", "{{api_key_id}}"]}}, "response": []}, {"name": "Delete API Key", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/api-keys/{{api_key_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "api-keys", "{{api_key_id}}"]}}, "response": []}, {"name": "Rotate API Key", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"preserve_old_key\": false,\n  \"expires_at\": null\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/api-keys/{{api_key_id}}/rotate", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "api-keys", "{{api_key_id}}", "rotate"]}}, "response": []}]}, {"name": "🪝 Webhook Keys", "item": [{"name": "Get All Webhook Keys for Tenant", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/webhook-keys", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "webhook-keys"]}}, "response": []}, {"name": "Get Webhook Key by ID", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/webhook-keys/{{webhook_key_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "webhook-keys", "{{webhook_key_id}}"]}}, "response": []}, {"name": "Create Webhook Key", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Production Webhook Key\",\n  \"environment\": \"LIVE\",\n  \"expires_at\": null,\n  \"is_active\": true,\n  \"webhook_url\": \"https://api.example.com/webhooks/qeep\"\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/webhook-keys", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "webhook-keys"]}}, "response": []}, {"name": "Update Webhook Key", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Webhook Key Name\",\n  \"is_active\": true,\n  \"webhook_url\": \"https://api.updated-example.com/webhooks/qeep\",\n  \"expires_at\": \"2025-12-31T23:59:59Z\"\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/webhook-keys/{{webhook_key_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "webhook-keys", "{{webhook_key_id}}"]}}, "response": []}, {"name": "Delete Webhook Key", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/webhook-keys/{{webhook_key_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "webhook-keys", "{{webhook_key_id}}"]}}, "response": []}, {"name": "Rotate Webhook Key", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"preserve_old_key\": false,\n  \"expires_at\": null\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/webhook-keys/{{webhook_key_id}}/rotate", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "webhook-keys", "{{webhook_key_id}}", "rotate"]}}, "response": []}]}, {"name": "🚀 Onboarding", "item": [{"name": "Get Tenant Onboarding Status", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/onboarding", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "onboarding"]}}, "response": []}, {"name": "Start Onboarding Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"workflow_type\": \"STANDARD_KYC\",\n  \"metadata\": {\n    \"initiated_by\": \"admin\",\n    \"priority\": \"HIGH\",\n    \"client_ip\": \"***********\",\n    \"user_agent\": \"Mozilla/5.0\"\n  }\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/onboarding/sessions", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "onboarding", "sessions"]}}, "response": []}, {"name": "Get Onboarding Session", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/onboarding/sessions/{{session_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "onboarding", "sessions", "{{session_id}}"]}}, "response": []}]}, {"name": "⚙️ Configuration", "item": [{"name": "Get Tenant Configuration", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/configuration", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "configuration"]}}, "response": []}, {"name": "Update Tenant Configuration", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"aml_settings\": {\n    \"risk_threshold\": \"MEDIUM\",\n    \"auto_approval_limit\": 10000,\n    \"require_manual_review\": true,\n    \"screening_enabled\": true\n  },\n  \"kyc_settings\": {\n    \"document_types\": [\"PASSPORT\", \"NATIONAL_ID\", \"DRIVERS_LICENSE\"],\n    \"require_liveness_check\": true,\n    \"max_retries\": 3,\n    \"verification_timeout\": 300\n  },\n  \"notification_settings\": {\n    \"email_enabled\": true,\n    \"sms_enabled\": false,\n    \"webhook_enabled\": true\n  }\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}/configuration", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}", "configuration"]}}, "response": []}]}, {"name": "🧪 Testing & Validation", "item": [{"name": "Validate CUID Format - Tenant ID", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/tenants/ten_fc0ygk48vir9fjxt8h993uwv", "host": ["{{base_url}}"], "path": ["api", "tenants", "ten_fc0ygk48vir9fjxt8h993uwv"]}, "description": "Test with a valid CUID-prefixed tenant ID (35 characters, ten_ prefix)"}, "response": []}, {"name": "Test Invalid Tenant ID Format", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/tenants/invalid-id-format", "host": ["{{base_url}}"], "path": ["api", "tenants", "invalid-id-format"]}, "description": "Test with invalid ID format to verify validation"}, "response": []}, {"name": "Health Check", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/health", "host": ["{{base_url}}"], "path": ["api", "health"]}}, "response": []}, {"name": "Get API Keys with Query Parameters", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/tenants/{{tenant_id}}/api-keys?environment=LIVE&is_active=true&limit=10&offset=0", "host": ["{{base_url}}"], "path": ["api", "tenants", "{{tenant_id}}", "api-keys"], "query": [{"key": "environment", "value": "LIVE"}, {"key": "is_active", "value": "true"}, {"key": "limit", "value": "10"}, {"key": "offset", "value": "0"}]}, "description": "Test query parameters with snake_case naming"}, "response": []}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "tenant_id", "value": "ten_fc0ygk48vir9fjxt8h993uwv"}, {"key": "api_key_id", "value": "tak_x7c8ann6h4euuluu0e17h0kv"}, {"key": "webhook_key_id", "value": "twk_e14j41gjlsnanwvdkwsrli28"}, {"key": "session_id", "value": "ows_abc123def456ghi789jkl012"}]}