{"info": {"_postman_id": "84f7d16e-53a7-4cac-b057-0cccf0d2f00d", "name": "Audit Service API", "description": "Complete API collection for the Qeep Audit Service including audit logging, event processing, analytics, search, compliance reporting, and monitoring capabilities", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "46240596", "_collection_link": "https://qeep-tech.postman.co/workspace/Team-Workspace~7c1d815d-f7bd-4c42-8bbf-8af4753cac13/collection/46240596-84f7d16e-53a7-4cac-b057-0cccf0d2f00d?action=share&source=collection_link&creator=46240596"}, "item": [{"name": "📋 <PERSON>t Logging", "item": [{"name": "Get All Audit Logs", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success field', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON>son).to.have.property('success');", "    pm.expect(responseJson.success).to.be.true;", "});", "", "pm.test('Response has data and pagination', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('logs');", "    pm.expect(responseJson.data).to.have.property('pagination');", "    pm.expect(responseJson.data.logs).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Correlation-ID", "value": "{{correlation_id}}"}], "url": {"raw": "{{base_url}}/audit/logs?limit=50&offset=0", "host": ["{{base_url}}"], "path": ["audit", "logs"], "query": [{"key": "limit", "value": "50", "description": "Number of records to return"}, {"key": "offset", "value": "0", "description": "Number of records to skip"}]}, "description": "Retrieve all audit logs with pagination support"}, "response": []}, {"name": "Get Audit Logs with Filters", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response structure is valid', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON>son).to.have.property('success');", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('logs');", "    pm.expect(responseJson.data).to.have.property('pagination');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Correlation-ID", "value": "{{correlation_id}}"}], "url": {"raw": "{{base_url}}/audit/logs?tenant_code={{tenant_code}}&event_type=USER_CREATED&severity=MEDIUM&start_date={{start_date}}&end_date={{end_date}}&limit=25&offset=0", "host": ["{{base_url}}"], "path": ["audit", "logs"], "query": [{"key": "tenant_code", "value": "{{tenant_code}}", "description": "Filter by tenant"}, {"key": "event_type", "value": "USER_CREATED", "description": "Filter by event type"}, {"key": "event_category", "value": "", "description": "Filter by event category", "disabled": true}, {"key": "severity", "value": "MEDIUM", "description": "Filter by severity level"}, {"key": "actor_user_id", "value": "", "description": "Filter by user who performed the action", "disabled": true}, {"key": "start_date", "value": "{{start_date}}", "description": "Start date for filtering"}, {"key": "end_date", "value": "{{end_date}}", "description": "End date for filtering"}, {"key": "limit", "value": "25", "description": "Number of records to return"}, {"key": "offset", "value": "0", "description": "Number of records to skip"}]}, "description": "Retrieve audit logs with comprehensive filtering options including tenant, event type, severity, date range, and pagination"}, "response": []}, {"name": "Get Audit Statistics", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has statistics data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON>son).to.have.property('success');", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('totalEntries');", "    pm.expect(responseJson.data).to.have.property('entries24h');", "    pm.expect(responseJson.data).to.have.property('statsByEventType');", "    pm.expect(responseJson.data).to.have.property('statsByCategory');", "    pm.expect(responseJson.data).to.have.property('statsBySeverity');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Correlation-ID", "value": "{{correlation_id}}"}], "url": {"raw": "{{base_url}}/audit/stats?tenant_code={{tenant_code}}&start_date={{start_date}}&end_date={{end_date}}", "host": ["{{base_url}}"], "path": ["audit", "stats"], "query": [{"key": "tenant_code", "value": "{{tenant_code}}", "description": "Filter statistics by tenant"}, {"key": "start_date", "value": "{{start_date}}", "description": "Start date for statistics calculation"}, {"key": "end_date", "value": "{{end_date}}", "description": "End date for statistics calculation"}]}, "description": "Get comprehensive audit statistics including total entries, time-based metrics, and breakdowns by event type, category, and severity"}, "response": []}], "description": "Core audit log retrieval and management operations"}, {"name": "🔍 Event Processing", "item": [{"name": "Get Auth Events", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains auth events', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON>son).to.have.property('success');", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('logs');", "    pm.expect(responseJson.data.logs).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Correlation-ID", "value": "{{correlation_id}}"}], "url": {"raw": "{{base_url}}/audit/events/auth?tenant_code={{tenant_code}}&limit=50&offset=0", "host": ["{{base_url}}"], "path": ["audit", "events", "auth"], "query": [{"key": "tenant_code", "value": "{{tenant_code}}", "description": "Filter by tenant"}, {"key": "limit", "value": "50", "description": "Number of records to return"}, {"key": "offset", "value": "0", "description": "Number of records to skip"}]}, "description": "Retrieve authentication-related audit events including login, logout, token operations, and security events"}, "response": []}, {"name": "Get User Registration Events", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains user registration events', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON>son).to.have.property('success');", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('logs');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Correlation-ID", "value": "{{correlation_id}}"}], "url": {"raw": "{{base_url}}/audit/events/user-registration?tenant_code={{tenant_code}}&start_date={{start_date}}&end_date={{end_date}}&limit=25&offset=0", "host": ["{{base_url}}"], "path": ["audit", "events", "user-registration"], "query": [{"key": "tenant_code", "value": "{{tenant_code}}", "description": "Filter by tenant"}, {"key": "start_date", "value": "{{start_date}}", "description": "Start date for filtering"}, {"key": "end_date", "value": "{{end_date}}", "description": "End date for filtering"}, {"key": "limit", "value": "25", "description": "Number of records to return"}, {"key": "offset", "value": "0", "description": "Number of records to skip"}]}, "description": "Retrieve user registration and signup related audit events including account creation, profile setup, and onboarding activities"}, "response": []}, {"name": "Get Email Verification Events", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains email verification events', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON>son).to.have.property('success');", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('logs');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Correlation-ID", "value": "{{correlation_id}}"}], "url": {"raw": "{{base_url}}/audit/events/email-verification?tenant_code={{tenant_code}}&actor_user_id={{user_id}}&limit=25&offset=0", "host": ["{{base_url}}"], "path": ["audit", "events", "email-verification"], "query": [{"key": "tenant_code", "value": "{{tenant_code}}", "description": "Filter by tenant"}, {"key": "actor_user_id", "value": "{{user_id}}", "description": "Filter by specific user"}, {"key": "limit", "value": "25", "description": "Number of records to return"}, {"key": "offset", "value": "0", "description": "Number of records to skip"}]}, "description": "Retrieve email verification related audit events including verification initiation, completion, and resend activities"}, "response": []}], "description": "Event-specific audit log retrieval and analysis"}, {"name": "📊 Analytics & Monitoring", "item": [{"name": "Search Audit Logs", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Search results are valid', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON>son).to.have.property('success');", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('logs');", "    pm.expect(responseJson.data).to.have.property('pagination');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Correlation-ID", "value": "{{correlation_id}}"}], "url": {"raw": "{{base_url}}/audit/logs?tenant_code={{tenant_code}}&event_category=AUTHENTICATION&severity=HIGH&limit=20&offset=0", "host": ["{{base_url}}"], "path": ["audit", "logs"], "query": [{"key": "tenant_code", "value": "{{tenant_code}}", "description": "Filter by tenant"}, {"key": "event_category", "value": "AUTHENTICATION", "description": "Search by event category"}, {"key": "severity", "value": "HIGH", "description": "Filter by severity level"}, {"key": "limit", "value": "20", "description": "Number of records to return"}, {"key": "offset", "value": "0", "description": "Number of records to skip"}]}, "description": "Advanced search functionality for audit logs with multiple filter criteria and full-text search capabilities"}, "response": []}, {"name": "Get Compliance Metrics", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Compliance metrics are present', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON>son).to.have.property('success');", "    pm.expect(responseJson).to.have.property('data');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Correlation-ID", "value": "{{correlation_id}}"}], "url": {"raw": "{{base_url}}/audit/stats?tenant_code={{tenant_code}}", "host": ["{{base_url}}"], "path": ["audit", "stats"], "query": [{"key": "tenant_code", "value": "{{tenant_code}}", "description": "Filter by tenant for compliance metrics"}]}, "description": "Retrieve compliance-focused metrics and statistics for regulatory reporting and monitoring"}, "response": []}], "description": "Advanced analytics, search, and monitoring capabilities"}, {"name": "🔧 Health & Diagnostics", "item": [{"name": "Health Check", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Service is healthy', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('status');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{audit_service_url}}/health", "host": ["{{audit_service_url}}"], "path": ["health"]}, "description": "Check the health status of the audit service including database connectivity and Kafka consumer status"}, "response": []}], "description": "Service health checks and diagnostic endpoints"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8080/api/v1"}, {"key": "audit_service_url", "value": "http://localhost:3005/api"}, {"key": "access_token", "value": ""}, {"key": "tenant_code", "value": "demo"}, {"key": "user_id", "value": ""}, {"key": "correlation_id", "value": "{{$randomUUID}}"}, {"key": "start_date", "value": "2024-01-01T00:00:00Z"}, {"key": "end_date", "value": "{{$isoTimestamp}}"}]}