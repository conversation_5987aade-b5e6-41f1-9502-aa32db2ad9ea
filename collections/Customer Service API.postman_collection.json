{"info": {"_postman_id": "ab632feb-d875-4ab7-b7fd-7ec2df0b63de", "name": "Customer Service API", "description": "Comprehensive API collection for the AlertHub Customer Service. This collection includes all endpoints for customer management operations with proper authentication and validation.\n\n## Authentication\nAll endpoints require JWT authentication with SUPER_ADMIN role.\n\n## Base URL\nDefault: `http://localhost:3001/api/v1`\n\n## Features\n- Customer listing with advanced filtering and pagination\n- Individual customer retrieval\n- Customer statistics and analytics\n- Comprehensive query parameters for all endpoints\n\n## Environment Variables\nSet up the following variables in your Postman environment:\n- `base_url`: Base URL for the customer service (default: http://localhost:3001/api/v1)\n- `jwt_token`: JWT token for authentication\n- `tenant_id`: Tenant ID for filtering (optional)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "********", "_collection_link": "https://qeep-tech.postman.co/workspace/Team-Workspace~7c1d815d-f7bd-4c42-8bbf-8af4753cac13/collection/********-ab632feb-d875-4ab7-b7fd-7ec2df0b63de?action=share&source=collection_link&creator=********"}, "item": [{"name": "💳 Account Management", "item": [{"name": "Create Account", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"tenant_id\": \"{{tenant_id}}\",\n  \"customer_id\": \"{{customer_id}}\",\n  \"account_type\": \"CHECKING\",\n  \"currency\": \"GHS\",\n  \"account_name\": \"Primary Checking Account\",\n  \"description\": \"Main checking account for daily transactions\",\n  \"initial_balance\": 1000.00,\n  \"overdraft_limit\": 500.00,\n  \"interest_rate\": 0.02,\n  \"minimum_balance\": 100.00,\n  \"metadata\": {\n    \"account_purpose\": \"PRIMARY_CHECKING\",\n    \"created_via\": \"API\",\n    \"branch_code\": \"ACC001\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/accounts", "host": ["{{base_url}}"], "path": ["accounts"]}, "description": "Creates a new account for a customer.\n\nRequired fields:\n- tenant_id: Tenant identifier\n- customer_id: Customer identifier\n- account_type: Type of account (CHECKING, SAVINGS, BUSINESS, INVESTMENT, CREDIT)\n- currency: Account currency (GHS, USD, EUR, etc.)\n- account_name: Human-readable account name\n\nOptional fields:\n- description: Account description\n- initial_balance: Starting balance (default: 0)\n- overdraft_limit: Maximum overdraft (default: 0)\n- interest_rate: Annual interest rate percentage (default: 0)\n- minimum_balance: Minimum required balance (default: 0)\n- metadata: Additional custom data"}, "response": []}, {"name": "Find Accounts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/accounts?tenant_id={{tenant_id}}", "host": ["{{base_url}}"], "path": ["accounts"], "query": [{"key": "tenant_id", "value": "{{tenant_id}}", "description": "Required tenant ID"}, {"key": "customer_id", "value": "{{customer_id}}", "description": "Filter by specific customer", "disabled": true}, {"key": "account_type", "value": "CHECKING", "description": "Filter by account type (CHECKING, SAVINGS, BUSINESS, INVESTMENT, CREDIT)", "disabled": true}, {"key": "account_status", "value": "ACTIVE", "description": "Filter by status (ACTIVE, INACTIVE, SUSPENDED, CLOSED, PENDING)", "disabled": true}, {"key": "currency", "value": "GHS", "description": "Filter by currency", "disabled": true}, {"key": "access_level", "value": "FULL", "description": "Filter by access level (FULL, read_only, BLOCKED)", "disabled": true}, {"key": "is_active", "value": "true", "description": "Filter by active status", "disabled": true}, {"key": "min_balance", "value": "0", "description": "Minimum balance filter", "disabled": true}, {"key": "max_balance", "value": "10000", "description": "Maximum balance filter", "disabled": true}, {"key": "page", "value": "1", "description": "Page number", "disabled": true}, {"key": "limit", "value": "10", "description": "Records per page", "disabled": true}]}, "description": "Retrieves accounts with advanced filtering options.\n\nSupported filters:\n- customer_id: Filter by specific customer\n- account_type: Filter by account type\n- account_status: Filter by account status\n- currency: Filter by currency\n- access_level: Filter by access level\n- is_active: Filter by active status\n- min_balance/max_balance: Filter by balance range\n\nAll filters are optional except tenant_id."}, "response": []}, {"name": "Get Account by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/accounts/:account_id", "host": ["{{base_url}}"], "path": ["accounts", ":account_id"], "variable": [{"key": "account_id", "value": "acc_t5yoas52epw8b5cu1w1eth8h"}]}, "description": "Retrieves a specific account by its unique identifier.\n\nReturns complete account information including:\n- Account details and balances\n- Customer information\n- Account status and access level\n- Metadata and timestamps"}, "response": []}, {"name": "Update Account", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"account_name\": \"Updated Account Name\",\n  \"description\": \"Updated account description\",\n  \"account_status\": \"ACTIVE\",\n  \"access_level\": \"FULL\",\n  \"overdraft_limit\": 750.00,\n  \"interest_rate\": 0.025,\n  \"minimum_balance\": 150.00,\n  \"metadata\": {\n    \"last_updated_by\": \"admin\",\n    \"update_reason\": \"customer_request\"\n  },\n  \"is_active\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/accounts/{{account_id}}", "host": ["{{base_url}}"], "path": ["accounts", "{{account_id}}"]}, "description": "Updates an existing account with new information.\n\nSupports partial updates - only include fields you want to change:\n- account_name: Update account name\n- description: Update description\n- account_status: Change status (ACTIVE, INACTIVE, SUSPENDED, CLOSED, PENDING)\n- access_level: Change access (FULL, read_only, BLOCKED)\n- overdraft_limit: Update overdraft limit\n- interest_rate: Update interest rate\n- minimum_balance: Update minimum balance requirement\n- metadata: Update custom metadata\n- is_active: Enable/disable account"}, "response": []}, {"name": "Delete Account", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/accounts/{{account_id}}", "host": ["{{base_url}}"], "path": ["accounts", "{{account_id}}"]}, "description": "Soft deletes an account by setting its status to CLOSED.\n\nThis operation:\n- Preserves account data for audit trail\n- Sets account status to CLOSED\n- Maintains referential integrity\n- Does not physically delete the record"}, "response": []}, {"name": "Validate Account for Transaction", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"account_id\": \"{{account_id}}\",\n  \"transaction_type\": \"DEBIT\",\n  \"amount\": 250.00,\n  \"currency\": \"GHS\",\n  \"validation_context\": {\n    \"transaction_reference\": \"TXN-2024-001\",\n    \"merchant_id\": \"MERCHANT_123\",\n    \"channel\": \"API\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/accounts/validate", "host": ["{{base_url}}"], "path": ["accounts", "validate"]}, "description": "Validates an account for transaction processing.\n\nUsed by the transaction service to verify:\n- Account exists and is active\n- Account has sufficient balance (for debits)\n- Account access level permits the operation\n- Account status allows transactions\n- Currency matches transaction currency\n\nRequest fields:\n- account_id: Account to validate\n- transaction_type: Type of transaction (DEBIT, CREDIT)\n- amount: Transaction amount\n- currency: Transaction currency\n- validation_context: Additional context for validation"}, "response": []}, {"name": "Get Customer Accounts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/accounts/customer/{{customer_id}}?tenant_id={{tenant_id}}&page=1&limit=20", "host": ["{{base_url}}"], "path": ["accounts", "customer", "{{customer_id}}"], "query": [{"key": "tenant_id", "value": "{{tenant_id}}", "description": "Required tenant ID"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Records per page"}]}, "description": "Retrieves all accounts for a specific customer.\n\nPath parameters:\n- customer_id: The customer's unique identifier\n\nQuery parameters:\n- tenant_id: Required tenant ID\n- page: Page number for pagination\n- limit: Records per page\n- All other filtering options from Find Accounts are also supported"}, "response": []}], "description": "Core account management operations"}, {"name": "😙 Customer Management", "item": [{"name": "Get All Customers", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has success structure\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success', true);", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('data');", "});", "", "pm.test(\"Response contains customer list\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('customers');", "    pm.expect(jsonData.data).to.have.property('pagination');", "    pm.expect(jsonData.data.customers).to.be.an('array');", "});", "", "// Store first customer ID for other requests", "if (pm.response.json().data.customers.length > 0) {", "    pm.collectionVariables.set('customer_id', pm.response.json().data.customers[0].id);", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/customers?page=1&limit=10&sort_order=desc&sort_by=created_at", "host": ["{{base_url}}"], "path": ["customers"], "query": [{"key": "page", "value": "1", "description": "Page number (1-based)"}, {"key": "limit", "value": "10", "description": "Number of items per page (1-100)"}, {"key": "search", "value": "", "description": "Search term for customer data", "disabled": true}, {"key": "status", "value": "", "description": "Filter by customer status (ACTIVE, INACTIVE, SUSPENDED, CLOSED, PENDING_VERIFICATION)", "disabled": true}, {"key": "customer_type", "value": "", "description": "Filter by customer type (INDIVIDUAL, BUSINESS, TRUST, GOVERNMENT, NON_PROFIT)", "disabled": true}, {"key": "risk_level", "value": "", "description": "Filter by risk level (LOW, MEDIUM, HIGH, CRITICAL)", "disabled": true}, {"key": "kyc_status", "value": "", "description": "Filter by KYC status (PENDING, IN_PROGRESS, VERIFIED, REJECTED, EXPIRED, REQUIRES_UPDATE)", "disabled": true}, {"key": "tenant_id", "value": "", "description": "Filter by tenant ID (UUID format)", "disabled": true}, {"key": "sort_order", "value": "desc", "description": "Sort order (asc, desc)"}, {"key": "sort_by", "value": "created_at"}]}, "description": "Retrieve all customers with advanced filtering, searching, and pagination capabilities. This endpoint is restricted to SUPER_ADMIN users only.\n\n**Query Parameters:**\n- `page`: Page number for pagination (default: 1)\n- `limit`: Number of items per page (1-100, default: 10)\n- `search`: Search term for customer data (max 255 characters)\n- `status`: Filter by customer status\n- `customer_type`: Filter by customer type\n- `risk_level`: Filter by risk level\n- `kyc_status`: Filter by KYC status\n- `tenant_id`: Filter by tenant ID (UUID format)\n- `sort_by`: Field to sort by (default: createdAt)\n- `sort_order`: Sort order (default: desc)"}, "response": []}, {"name": "Get Customer by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has success structure\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success', true);", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('data');", "});", "", "pm.test(\"Response contains customer data\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('id');", "    pm.expect(jsonData.data).to.have.property('email');", "    pm.expect(jsonData.data).to.have.property('status');", "    pm.expect(jsonData.data).to.have.property('customerType');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/customers/{{customer_id}}", "host": ["{{base_url}}"], "path": ["customers", "{{customer_id}}"]}, "description": "Retrieve detailed information about a specific customer by their UUID. This endpoint is restricted to SUPER_ADMIN users only.\n\n**Path Parameters:**\n- `id`: Customer UUID (required)\n\n**Response includes:**\n- Complete customer profile data\n- KYC information and status\n- Risk assessment details\n- Address information\n- Document summaries\n- Alert summaries\n- Relationship information"}, "response": []}, {"name": "Get Customer Statistics", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has success structure\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success', true);", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('data');", "});", "", "pm.test(\"Response contains statistics data\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('totalCustomers');", "    pm.expect(jsonData.data).to.have.property('activeCustomers');", "    pm.expect(jsonData.data).to.have.property('pendingKyc');", "    pm.expect(jsonData.data).to.have.property('highRiskCustomers');", "    pm.expect(jsonData.data).to.have.property('recentCustomers');", "    pm.expect(jsonData.data).to.have.property('customersByType');", "    pm.expect(jsonData.data).to.have.property('customersByRiskLevel');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/customers/stats/overview", "host": ["{{base_url}}"], "path": ["customers", "stats", "overview"]}, "description": "Retrieve comprehensive customer statistics across all tenants for superadmin dashboard and reporting. This endpoint is restricted to SUPER_ADMIN users only.\n\n**Response includes:**\n- Total number of customers\n- Active customers count\n- Pending KYC customers count\n- High-risk customers count\n- Recent customers count\n- Breakdown by customer type\n- Breakdown by risk level"}, "response": []}], "description": "Endpoints for managing customers across all tenants (SUPER_ADMIN only)"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "customer_id", "value": ""}]}