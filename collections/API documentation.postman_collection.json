{"info": {"_postman_id": "1277c31a-b31b-4d95-94de-008ab9af1068", "name": "API documentation", "description": "# G-Money API - Getting Started Guide\n\nThis document provides instructions for integrating with and consuming the G-Money API.\n\n## Base URL\n\nAll API requests should be made to:\n\n```\nhttps://api.gmoney.com/v1\n\n ```\n\nFor development/testing environments:\n\n```\nhttps://dev.api.gmoney.com/v1\n\n ```\n\nFor staging environments:\n\n```\nhttps://stg.api.gmoney.com/v1\n\n ```\n\n## Authentication\n\nG-Money API uses JWT (JSON Web Token) authentication for all endpoints.\n\n### Obtaining API Tokens\n\nTo use the G-Money API, first obtain an access token:\n\n**URL:** `POST /auth/login`\n\n**Headers:**\n\n```\nContent-Type: application/json\n\n ```\n\n**Request Body:**\n\n```\n{\n  \"username\": \"your_username\",\n  \"password\": \"your_password\"\n}\n\n ```\n\n**Response:**\n\n```\n{\n  \"success\": true,\n  \"message\": \"Login successful\",\n  \"data\": {\n    \"accessToken\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n    \"refreshToken\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n    \"user\": {\n      \"id\": \"user-id\",\n      \"customerId\": \"customer-id\",\n      \"name\": \"User Name\",\n      \"email\": \"<EMAIL>\"\n    }\n  }\n}\n\n ```\n\n### Using the Access Token\n\nInclude the access token in the `Authorization` header for all API requests:\n\n```\nAuthorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\n\n ```\n\n### Token Expiration and Refresh\n\nAccess tokens expire after 1 hour. When an access token expires, use the refresh token to get a new one:\n\n**URL:** `POST /auth/refresh-token`\n\n**Headers:**\n\n```\nContent-Type: application/json\n\n ```\n\n**Request Body:**\n\n```\n{\n  \"refreshToken\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\"\n}\n\n ```\n\n## Response Format\n\nAll API responses follow a standard format:\n\n### Success Response\n\n```\n{\n  \"success\": true,\n  \"message\": \"Operation successful\",\n  \"data\": {\n    // Response data\n  },\n  \"pagination\": {\n    // Pagination details (if applicable)\n  }\n}\n\n ```\n\n### Error Response\n\n```\n{\n  \"success\": false,\n  \"error\": {\n    \"code\": \"ERROR_CODE\",\n    \"details\": \"Additional error information\"\n  },\n  \"message\": \"Error message\"\n}\n\n ```\n\n## Common HTTP Status Codes\n\n| Status Code | Description |\n| --- | --- |\n| 200 | OK - Request successful |\n| 201 | Created - Resource created successfully |\n| 400 | Bad Request - Invalid request parameters |\n| 401 | Unauthorized - Authentication required or invalid credentials |\n| 403 | Forbidden - Insufficient permissions |\n| 404 | Not Found - Resource not found |\n| 422 | Unprocessable Entity - Validation errors |\n| 500 | Internal Server Error - Server-side error |\n\n## Pagination\n\nMost list endpoints support pagination. You can control pagination using these query parameters:\n\n- `page` - Page number (default: 1)\n    \n- `limit` - Number of items per page (default: 10)\n    \n\nResponse includes pagination information:\n\n```\n{\n  \"success\": true,\n  \"data\": [],\n  \"pagination\": {\n    \"page\": 1,\n    \"limit\": 10,\n    \"total\": 50,\n    \"totalPages\": 5\n  }\n}\n\n ```\n\n## Error Handling\n\nWhen an error occurs, the API returns a standard error response:\n\n```\n{\n  \"success\": false,\n  \"error\": {\n    \"code\": \"ERROR_CODE\",\n    \"details\": \"Additional details\"\n  },\n  \"message\": \"Human-readable error message\"\n}\n\n ```\n\nCommon error codes:\n\n- `INVALID_INPUT` - Validation error in request data\n    \n- `UNAUTHORIZED` - Authentication required or invalid\n    \n- `TOKEN_EXPIRED` - JWT token has expired\n    \n- `FORBIDDEN` - Insufficient permissions\n    \n- `NOT_FOUND` - Resource not found\n    \n- `INTERNAL_ERROR` - Server-side error\n    \n\n## Rate Limiting\n\nThe API employs rate limiting to ensure fair usage. Current limits:\n\n- 100 requests per minute per IP address\n    \n- 1000 requests per hour per user\n    \n\nWhen rate limited, you'll receive a `429 Too Many Requests` response.\n\n## SDK and Client Libraries\n\nFor easier integration, we provide client libraries for popular programming languages:\n\n- [JavaScript SDK](https://github.com/gmoney/js-sdk)\n    \n- [Android SDK](https://github.com/gmoney/android-sdk)\n    \n- [iOS SDK](https://github.com/gmoney/ios-sdk)\n    \n\n## Support and Contact\n\nIf you encounter any issues or have questions:\n\n- Check the [API documentation](https://docs.gmoney.com/api)\n    \n- Contact our support team at [<EMAIL>](https://mailto:<EMAIL>)\n    \n- Join our developer community on [Slack](https://gmoney-dev.slack.com/)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "41622417"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "Get Authenticated User", "event": [{"listen": "test", "script": {"exec": ["const data = pm.response.json().data", "", "if (data.customerId) {", "    // Save the token as an environment variable", "    pm.environment.set(\"customerId\", data.user.customerId);", "} else {", "    console.log(\"User not found\");", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/auth/me", "host": ["{{BASE_URL}}"], "path": ["auth", "me"]}, "description": "Gets information about the authenticated user."}, "response": []}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["const data = pm.response.json().data", "", "// Assuming your token is in a cookie named \"authToken\"", "if (data.accessToken) {", "    // Save the token as an environment variable", "    pm.environment.set(\"authToken\", data.accessToken);", "} else {", "    console.log(\"User not found\");", "}", "", "// Assuming your token is in a cookie named \"authToken\"", "if (data.refreshToken) {", "    // Save the token as an environment variable", "    pm.environment.set(\"refreshToken\", data.refreshToken);", "} else {", "    console.log(\"User not found\");", "}", "", "if (data.user[\"customerId\"]) {", "    // Save the token as an environment variable", "    pm.environment.set(\"customerId\", data.user.customerId);", "} else {", "    console.log(\"User not found\");", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"username\": \"00000140\",\n    \"password\": \"123456A\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/auth/login", "host": ["{{BASE_URL}}"], "path": ["auth", "login"]}}, "response": []}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["const data = pm.response.json().data", "", "// Assuming your token is in a cookie named \"authToken\"", "if (data.accessToken) {", "    // Save the token as an environment variable", "    pm.environment.set(\"authToken\", data.accessToken);", "} else {", "    console.log(\"User not found\");", "}", "", "if (data.user[\"customerId\"]) {", "    // Save the token as an environment variable", "    pm.environment.set(\"customerId\", data.user.customerId);", "} else {", "    console.log(\"User not found\");", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"refreshToken\": \"{{refreshToken}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/auth/refresh-token", "host": ["{{BASE_URL}}"], "path": ["auth", "refresh-token"]}}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [], "url": {"raw": "{{BASE_URL}}/auth/logout", "host": ["{{BASE_URL}}"], "path": ["auth", "logout"]}}, "response": []}], "description": "The `/me` endpoints let you manage information about the authenticated user.", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}, {"name": "Onboarding", "item": []}, {"name": "User", "item": [{"name": "Get user", "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/users/{{customerId}}", "host": ["{{BASE_URL}}"], "path": ["users", "{{customerId}}"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}, {"name": "Accounts", "item": [{"name": "Get Balance", "request": {"method": "GET", "header": [{"key": "x-psl-authorization", "value": "{{pslToken}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/accounts/balance", "host": ["{{BASE_URL}}"], "path": ["accounts", "balance"]}}, "response": []}, {"name": "Get Accounts", "request": {"method": "GET", "header": [{"key": "x-psl-authorization", "value": "{{pslToken}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/accounts", "host": ["{{BASE_URL}}"], "path": ["accounts"]}}, "response": []}, {"name": "Link Account", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"bankCode\": \"300304\",\n    \"accountNumber\": \"1i+/oS8q1rrZGpRbk6D2eg==\",\n    \"accountHolderName\": \"OHENEBA-AMISSAH FELIX\",\n    \"phoneNumber\": \"*********\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/accounts/link", "host": ["{{BASE_URL}}"], "path": ["accounts", "link"]}}, "response": []}, {"name": "Link Account - Confirm", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"pin\": \"300304\",\n    \"confirmPin\": \"*************\",\n    \"token\": \"OHnNEBA-AMISSAH FELIX\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/accounts/link/confirm", "host": ["{{BASE_URL}}"], "path": ["accounts", "link", "confirm"]}}, "response": []}, {"name": "Delink Account", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"accountNumber\": \"hh\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/accounts/unlink", "host": ["{{BASE_URL}}"], "path": ["accounts", "unlink"]}}, "response": []}, {"name": "Delink Account - Confirm", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"pin\": \"300304\",\n    \"confirmPin\": \"*************\",\n    \"token\": \"OHnNEBA-AMISSAH FELIX\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/accounts/unlink/confirm", "host": ["{{BASE_URL}}"], "path": ["accounts", "unlink", "confirm"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}, {"name": "Transactions", "item": [{"name": "Bundles", "item": [{"name": "Bundle Billers", "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/transactions/billers?group=data", "host": ["{{BASE_URL}}"], "path": ["transactions", "billers"], "query": [{"key": "group", "value": "data"}]}}, "response": []}, {"name": "Bundles", "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/transactions/bundles?provider=MTN&phoneNumber=************", "host": ["{{BASE_URL}}"], "path": ["transactions", "bundles"], "query": [{"key": "provider", "value": "MTN"}, {"key": "phoneNumber", "value": "************"}]}}, "response": []}, {"name": "Data Bundle", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"walletId\": \"CUWL471568\", // Senders wallet\n    \"recipientWalletId\": \"************\", // Recipients Wallets\n    \"amount\":  2.00, \n    \"remark\": \"Some random remark\",\n    \"recipientName\": \"<PERSON> Guy\",\n    \"bundleProviderId\": \"MTN\",\n    \"bundleId\": \"MTNSMSB2\",\n    \"type\": \"DATA_BUNDLE\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/transactions/initiate", "host": ["{{BASE_URL}}"], "path": ["transactions", "initiate"]}}, "response": []}]}, {"name": "Airtime", "item": [{"name": "Airtime Billers", "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/transactions/billers?group=airtime", "host": ["{{BASE_URL}}"], "path": ["transactions", "billers"], "query": [{"key": "group", "value": "airtime"}]}}, "response": []}, {"name": "Airtime Self", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"walletId\": \"CUWL471568\", // Senders wallet\n    \"recipientWalletId\": \"************\", // Recipients Wallets\n    \"amount\":  2.00, \n    \"remark\": \"Some random remark\",\n    \"recipientName\": \"<PERSON> Guy\",\n    \"airtimeProviderId\": \"MTN\",\n    \"type\": \"AIRTIME_TO_SELF\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/transactions/initiate", "host": ["{{BASE_URL}}"], "path": ["transactions", "initiate"]}}, "response": []}, {"name": "Airtime Others", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"walletId\": \"CUWL471568\", // Senders wallet\n    \"recipientWalletId\": \"************\", // Recipients Wallets\n    \"amount\":  2.00, \n    \"remark\": \"Some random remark\",\n    \"recipientName\": \"<PERSON> Guy\",\n    \"airtimeProviderId\": \"MTN\",\n    \"type\": \"AIRTIME_TO_OTHER\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/transactions/initiate", "host": ["{{BASE_URL}}"], "path": ["transactions", "initiate"]}}, "response": []}]}, {"name": "G-Money", "item": [{"name": "G-Money G-Money", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"walletId\": \"CUWL471568\",\n    \"recipientWalletId\": \"CUWL458076\",\n    \"amount\":  2.00,\n    \"remark\": \"Some random remark\",\n    \"type\": \"G_MONEY_TO_G_MONEY_BENEFICIARY\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/transactions/initiate", "host": ["{{BASE_URL}}"], "path": ["transactions", "initiate"]}}, "response": []}]}, {"name": "Bank", "item": [{"name": "G-Money GCB Bank", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"walletId\": \"CUWL471568\",\n    \"amount\":  2.00,\n    \"remark\": \"Some random remark\",\n    \"recipientWalletId\": \"*************\",\n    \"recipientName\": \"<PERSON> Guy\",\n    \"bankCode\": \"300304\",\n    \"type\": \"G_MONEY_TO_BANK\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/transactions/initiate", "host": ["{{BASE_URL}}"], "path": ["transactions", "initiate"]}}, "response": []}, {"name": "G-Money NON GCB Bank", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"walletId\": \"CUWL471568\",\n    \"amount\":  2.00,\n    \"remark\": \"Some random remark\",\n    \"recipientWalletId\": \"************\",\n    \"recipientName\": \"<PERSON> Guy\",\n    \"bankCode\": \"300315\",\n    \"type\": \"G_MONEY_TO_BANK\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/transactions/initiate", "host": ["{{BASE_URL}}"], "path": ["transactions", "initiate"]}}, "response": []}]}, {"name": "Pay Bill", "item": [{"name": "Pay Bill Billers", "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/transactions/billers?group=bill", "host": ["{{BASE_URL}}"], "path": ["transactions", "billers"], "query": [{"key": "group", "value": "bill"}]}}, "response": []}, {"name": "Postpaid Packages", "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/transactions/billers/:billerAlias/postpaid?billingAccountNumber=************", "host": ["{{BASE_URL}}"], "path": ["transactions", "billers", ":<PERSON><PERSON><PERSON><PERSON><PERSON>", "postpaid"], "query": [{"key": "billingAccountNumber", "value": "************"}], "variable": [{"key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "DSTV"}]}}, "response": []}, {"name": "Pay Bill", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"walletId\": \"CUWL471568\", // Sender's wallet\n    \"recipientWalletId\": \"************\", // Recipient's Wallet\n    \"amount\":  2.00, \n    \"remark\": \"Some random remark\",\n    \"recipientName\": \"<PERSON> Guy\",\n    \"payBillProviderId\": \"DSTV\",\n    \"type\": \"PAY_BILL\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/transactions/initiate", "host": ["{{BASE_URL}}"], "path": ["transactions", "initiate"]}}, "response": []}]}, {"name": "History", "request": {"method": "GET", "header": [{"key": "psl-authorization", "value": "{{pslToken}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/transactions/history?accountNumber=CUWL471568&startDate=2025-01-15T00:00:03.984Z&endDate=2025-02-01T13:58:03.984Z&limit=2", "host": ["{{BASE_URL}}"], "path": ["transactions", "history"], "query": [{"key": "accountNumber", "value": "CUWL471568"}, {"key": "startDate", "value": "2025-01-15T00:00:03.984Z"}, {"key": "endDate", "value": "2025-02-01T13:58:03.984Z"}, {"key": "limit", "value": "2"}]}}, "response": []}, {"name": "Confirm Transaction", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"pin\": \"wNdQrRxBfXEn2yGqCoqMSQ==\",\n    \"confirmPin\": \"wNdQrRxBfXEn2yGqCoqMSQ==\",\n    \"transactionId\": \"SM25B7DF3BD301\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/transactions/confirm", "host": ["{{BASE_URL}}"], "path": ["transactions", "confirm"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}, {"name": "Wallets", "item": [{"name": "New Request", "request": {"method": "GET", "header": []}, "response": []}]}, {"name": "Groups", "item": [{"name": "Create Group", "item": [{"name": "Create Group (Initiate)", "event": [{"listen": "test", "script": {"exec": ["const data = pm.response.json().data", "", "// Assuming your token is in a cookie named \"authToken\"", "if (data.token) {", "    // Save the token as an environment variable", "    pm.environment.set(\"createGroupToken\", data.token);", "} "], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-psl-authorization", "value": "{{pslToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"test123\",\n    \"description\":\" This is a nest group\",\n    \"creatorAccountNumber\": \"CUWL471568\",\n    \"logoUrl\": \"https://digiits-dev.fra1.cdn.digitaloceanspaces.com/gmoney-dev-pubic/rounde-icon.png\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/groups/initiate", "host": ["{{BASE_URL}}"], "path": ["groups", "initiate"]}}, "response": []}, {"name": "Create Group (Confirm)", "request": {"method": "POST", "header": [{"key": "x-psl-authorization", "value": "{{pslToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"token\": \"{{createGroupToken}}\",\n    \"pin\": \"TTn2Mgp9GiancoeHj3YjeQ==\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/groups/confirm", "host": ["{{BASE_URL}}"], "path": ["groups", "confirm"]}}, "response": []}]}, {"name": "Groups", "item": [{"name": "Groups", "request": {"method": "GET", "header": [{"key": "x-psl-authorization", "value": "{{pslToken}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/groups", "host": ["{{BASE_URL}}"], "path": ["groups"]}}, "response": []}, {"name": "Get Group", "request": {"method": "GET", "header": [{"key": "x-psl-authorization", "value": "{{pslToken}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/groups/{{groupCode}}", "host": ["{{BASE_URL}}"], "path": ["groups", "{{groupCode}}"]}}, "response": []}]}, {"name": "Group Types", "item": [{"name": "List Group Types", "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/groups/types", "host": ["{{BASE_URL}}"], "path": ["groups", "types"]}}, "response": []}]}, {"name": "Group Top Up", "item": [{"name": "Top Up Intiate", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"amount\": 400,\n    \"remarks\":\"hello\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/groups/{{groupCode}}/top-up/initiate", "host": ["{{BASE_URL}}"], "path": ["groups", "{{groupCode}}", "top-up", "initiate"]}}, "response": []}, {"name": "Top Up Confirm", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"token\": \"{{groupTopUpToken}}\",\n    \"pin\":\"hello\",\n    \"confirmPin\":\"hello\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/groups/{{groupCode}}/top-up/confirm", "host": ["{{BASE_URL}}"], "path": ["groups", "{{groupCode}}", "top-up", "confirm"]}}, "response": []}]}, {"name": "Group Withdrawal", "item": [{"name": "Group Withdrawal Initiate", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"memberCustomerId\": \"fsj\",\n    \"amount\": 50,\n    \"remarks\":\"hello\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/groups/{{groupCode}}/withdrawal/initiate", "host": ["{{BASE_URL}}"], "path": ["groups", "{{groupCode}}", "withdrawal", "initiate"]}}, "response": []}, {"name": "Group Withdrawal Confirm", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"token\": \"{{groupTopUpToken}}\",\n    \"pin\":\"hello\",\n    \"confirmPin\":\"hello\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/groups/{{groupCode}}/withdrawal/confirm", "host": ["{{BASE_URL}}"], "path": ["groups", "{{groupCode}}", "withdrawal", "confirm"]}}, "response": []}, {"name": "<PERSON><PERSON>wal Approval Initiate", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"requestRefId\": \"fsj\",\n    \"approvalStatus\": \"APPROVED\",\n    \"requesterId\":\"hello\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/groups/{{groupCode}}/withdrawal/approve/initiate", "host": ["{{BASE_URL}}"], "path": ["groups", "{{groupCode}}", "withdrawal", "approve", "initiate"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> Approval Confirm", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"token\": \"{{groupTopUpToken}}\",\n    \"pin\":\"hello\",\n    \"confirmPin\":\"hello\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/groups/{{groupCode}}/withdrawal/approve/confirm", "host": ["{{BASE_URL}}"], "path": ["groups", "{{groupCode}}", "withdrawal", "approve", "confirm"]}}, "response": []}]}, {"name": "Member Invitations", "item": [{"name": "Invitation Initiate", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"customerAccountNo\": \"123\",\n    \"customerPhoneNumber\": \"123\",    \n    \"roleCode\": \"123\",\n    \"customerId\": \"123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/groups/{{groupCode}}/members/invite/initiate", "host": ["{{BASE_URL}}"], "path": ["groups", "{{groupCode}}", "members", "invite", "initiate"]}}, "response": []}, {"name": "Invitation Confirm", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"token\": \"{{groupTopUpToken}}\",\n    \"pin\":\"hello\",\n    \"confirmPin\":\"hello\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/groups/{{groupCode}}/members/invite/confirm", "host": ["{{BASE_URL}}"], "path": ["groups", "{{groupCode}}", "members", "invite", "confirm"]}}, "response": []}, {"name": "Approve/Reject Invitation", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"customerId\": \"{{groupTopUpToken}}\",\n    \"status\":\"APPROVED\",\n    \"pin\":\"hello\",\n    \"confirmPin\":\"hello\",\n    \"invitationReferenceId\": \"ok\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/groups/members/invite/process", "host": ["{{BASE_URL}}"], "path": ["groups", "members", "invite", "process"]}}, "response": []}, {"name": "Get Invitations", "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/groups/{{groupCode}}/invitations", "host": ["{{BASE_URL}}"], "path": ["groups", "{{groupCode}}", "invitations"]}}, "response": []}]}, {"name": "Remove Member", "item": [{"name": "Remove Member Initiate", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"memberCustomerId\": \"fsj\",\n    \"roleCode\": \"AUTHORISER\",\n    \"remarks\":\"hello\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/groups/{{groupCode}}/members/remove/initiate", "host": ["{{BASE_URL}}"], "path": ["groups", "{{groupCode}}", "members", "remove", "initiate"]}}, "response": []}, {"name": "Remove Member Confirm", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"token\": \"{{groupTopUpToken}}\",\n    \"pin\":\"hello\",\n    \"confirmPin\":\"hello\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/groups/{{groupCode}}/top-up/confirm", "host": ["{{BASE_URL}}"], "path": ["groups", "{{groupCode}}", "top-up", "confirm"]}}, "response": []}]}, {"name": "Update Role", "item": [{"name": "Update Role Initiate", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"memberCustomerNo\": \"fsj\",\n    \"roleCode\": \"AUTHORISER\",\n    \"remarks\":\"hello\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/groups/{{groupCode}}/members/role/initiate", "host": ["{{BASE_URL}}"], "path": ["groups", "{{groupCode}}", "members", "role", "initiate"]}}, "response": []}, {"name": "Update Role Confirm", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"token\": \"{{groupTopUpToken}}\",\n    \"pin\":\"hello\",\n    \"confirmPin\":\"hello\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/groups/{{groupCode}}/members/role/confirm", "host": ["{{BASE_URL}}"], "path": ["groups", "{{groupCode}}", "members", "role", "confirm"]}}, "response": []}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}, {"name": "Notifications", "item": [{"name": "Get Notifications", "request": {"method": "GET", "header": []}, "response": []}]}, {"name": "Storage", "item": [{"name": "Upload File", "event": [{"listen": "test", "script": {"exec": ["const data = pm.response.json().data", "", "if (data.id) {", "    pm.environment.set(\"fileId\", data.id);", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "postman-cloud:///1f01754c-f33d-4e20-8a5f-fd072fa8bdae"}, {"key": "isPublic", "value": "true", "type": "text"}]}, "url": {"raw": "{{BASE_URL}}/files", "host": ["{{BASE_URL}}"], "path": ["files"]}}, "response": []}, {"name": "Get File", "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/files/{{fileId}}", "host": ["{{BASE_URL}}"], "path": ["files", "{{fileId}}"]}}, "response": []}, {"name": "Get File URL", "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/files/{{fileId}}/url", "host": ["{{BASE_URL}}"], "path": ["files", "{{fileId}}", "url"]}}, "response": []}, {"name": "Get FIles", "event": [{"listen": "test", "script": {"exec": ["const data = pm.response.json().data", "", "if (data[0].id) {", "    pm.environment.set(\"fileId\", data[0].id);", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/files", "host": ["{{BASE_URL}}"], "path": ["files"]}}, "response": []}, {"name": "Delete File", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{BASE_URL}}/files/{{fileId}}", "host": ["{{BASE_URL}}"], "path": ["files", "{{fileId}}"]}}, "response": []}], "description": "# Storage API Documentation\n\nThis document provides details about the files/storage API endpoints.\n\n## Authentication\n\nAll endpoints in this module require authentication using a JWT token.\n\n**Headers:**\n\n```\n{\n  \"Authorization\": \"Bearer [token]\",\n  \"Content-Type\": \"application/json\"\n}\n\n ```\n\n## Endpoints\n\n### Upload File\n\n**URL:** `POST /files`\n\n**Description:** Upload a file to the storage service.\n\n**Request:**\n\n- This endpoint uses `multipart/form-data` for file uploading\n    \n- File should be sent with the field name `file`\n    \n\n**Form Fields:**\n\n| Field | Type | Required | Description |\n| --- | --- | --- | --- |\n| file | File | Yes | The file to upload |\n| name | string | No | Optional name for the file |\n| type | string | No | File type (defaults to \"OTHER\") |\n| notes | string | No | Additional notes about the file |\n| isPublic | boolean | No | Whether the file should be public (defaults to false) |\n\n**Response:**\n\n```\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"550e8400-e29b-41d4-a716-446655440000\",\n    \"name\": \"example.pdf\",\n    \"type\": \"DOCUMENT\",\n    \"url\": \"https://storage-url.com/example.pdf\",\n    \"key\": \"users/123/example.pdf\",\n    \"fileName\": \"example.pdf\",\n    \"contentType\": \"application/pdf\",\n    \"size\": 1024,\n    \"userId\": \"123\",\n    \"createdAt\": \"2023-04-01T14:30:00Z\",\n    \"updatedAt\": \"2023-04-01T14:30:00Z\",\n    \"status\": \"PENDING\"\n  },\n  \"message\": \"File uploaded successfully\"\n}\n\n ```\n\n### Get Files\n\n**URL:** `GET /files`\n\n**Description:** Retrieve a list of files for the authenticated user.\n\n**Query Parameters:**\n\n| Parameter | Type | Required | Description |\n| --- | --- | --- | --- |\n| page | number | No | Page number (default: 1) |\n| limit | number | No | Number of items per page (default: 10) |\n| isPublic | boolean | No | If true, includes public files in the response |\n| name | string | No | Filter files by name (case insensitive, partial match) |\n| type | string | No | Filter files by type (e.g., DOCUMENT, IMAGE, VIDEO, AUDIO, OTHER) |\n| status | string | No | Filter files by status |\n| createdAfter | date | No | Filter files created after this date (format: ISO 8601) |\n| createdBefore | date | No | Filter files created before this date (format: ISO 8601) |\n\n**Example Requests:**\n\n```\nGET /files?page=1&limit=20\nGET /files?isPublic=true&type=DOCUMENT\nGET /files?name=report&createdAfter=2023-01-01T00:00:00Z\n\n ```\n\n**Response:**\n\n```\n{\n  \"success\": true,\n  \"data\": {\n    \"data\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440000\",\n        \"name\": \"example.pdf\",\n        \"type\": \"DOCUMENT\",\n        \"url\": \"https://storage-url.com/example.pdf\",\n        \"key\": \"users/123/example.pdf\",\n        \"fileName\": \"example.pdf\",\n        \"contentType\": \"application/pdf\",\n        \"size\": 1024,\n        \"userId\": \"123\",\n        \"createdAt\": \"2023-04-01T14:30:00Z\",\n        \"updatedAt\": \"2023-04-01T14:30:00Z\",\n        \"status\": \"PENDING\"\n      }\n    ],\n    \"pagination\": {\n      \"page\": 1,\n      \"limit\": 10,\n      \"total\": 50,\n      \"totalPages\": 5\n    }\n  },\n  \"message\": \"Files retrieved successfully\"\n}\n\n ```\n\n### Get File\n\n**URL:** `GET /files/:id`\n\n**Description:** Retrieve a specific file by its ID.\n\n**Path Parameters:**\n\n| Parameter | Type | Description |\n| --- | --- | --- |\n| id | string | The ID of the file to retrieve |\n\n**Response:**\n\n```\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"550e8400-e29b-41d4-a716-446655440000\",\n    \"name\": \"example.pdf\",\n    \"type\": \"DOCUMENT\",\n    \"url\": \"https://storage-url.com/example.pdf\",\n    \"key\": \"users/123/example.pdf\",\n    \"fileName\": \"example.pdf\",\n    \"contentType\": \"application/pdf\",\n    \"size\": 1024,\n    \"userId\": \"123\",\n    \"createdAt\": \"2023-04-01T14:30:00Z\",\n    \"updatedAt\": \"2023-04-01T14:30:00Z\",\n    \"status\": \"PENDING\"\n  },\n  \"message\": \"File retrieved successfully\"\n}\n\n ```\n\n### Delete File\n\n**URL:** `DELETE /files/:id`\n\n**Description:** Delete a file.\n\n**Path Parameters:**\n\n| Parameter | Type | Description |\n| --- | --- | --- |\n| id | string | The ID of the file to delete |\n\n**Response:**\n\n```\n{\n  \"success\": true,\n  \"message\": \"File deleted successfully\"\n}\n\n ```\n\n### Get File URL\n\n**URL:** `GET /files/:id/url`\n\n**Description:** Get a signed URL for accessing a file.\n\n**Path Parameters:**\n\n| Parameter | Type | Description |\n| --- | --- | --- |\n| id | string | The ID of the file |\n\n**Query Parameters:**\n\n| Parameter | Type | Required | Description |\n| --- | --- | --- | --- |\n| expires | number | No | URL expiration time in seconds (default: 60) |\n\n**Response:**\n\n```\n{\n  \"success\": true,\n  \"data\": {\n    \"url\": \"https://storage-url.com/signed-url-for-file-access\"\n  },\n  \"message\": \"File URL generated successfully\"\n}\n\n ```\n\n## Error Responses\n\n**File Not Found:**\n\n```\n{\n  \"success\": false,\n  \"error\": {\n    \"statusCode\": 404,\n    \"message\": \"File not found\"\n  },\n  \"message\": \"File not found\"\n}\n\n ```\n\n**Bad Request:**\n\n```\n{\n  \"success\": false,\n  \"error\": {\n    \"statusCode\": 400,\n    \"message\": \"No file provided\"\n  },\n  \"message\": \"No file provided\"\n}\n\n ```\n\n**Unauthorized:**\n\n```\n{\n  \"success\": false,\n  \"error\": {\n    \"statusCode\": 401,\n    \"message\": \"Unauthorized\"\n  },\n  \"message\": \"Unauthorized\"\n}\n\n ```\n\n## File Types\n\nThe API supports the following file types (from the FileType enum):\n\n- DOCUMENT\n    \n- IMAGE\n    \n- VIDEO\n    \n- AUDIO\n    \n- OTHER", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}, {"name": "Beneficiaries", "item": [{"name": "Get Beneficiaries", "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/beneficiaries", "host": ["{{BASE_URL}}"], "path": ["beneficiaries"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}], "auth": {"type": "apikey", "apikey": [{"key": "key", "value": "X-API-Key", "type": "string"}, {"key": "value", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set custom headers dynamically before the request is sent", "pm.request.headers.add({ key: \"x-device-id\", value: \"NS093\" });", ""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Log response headers for debugging", "console.log(pm.response.headers.all());", "", "// Extract the custom header value", "const pslToken = pm.response.headers.get(\"x-psl-authorization\");", "", "if (pslToken) {", "    pm.environment.set(\"pslToken\", pslToken);", "    pm.collectionVariables.set(\"pslToken\", pslToken);", "    console.log(\"Token saved:\", pslToken);", "} else {", "    console.log(\"x-psl-authorization header not found\");", "}", "", ""]}}], "variable": [{"key": "baseUrl", "value": "https://farming-simulator.pstmn.io"}, {"key": "pslToken", "value": "", "type": "string"}]}