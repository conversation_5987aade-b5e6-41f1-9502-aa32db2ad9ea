{"name": "integration-service", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/integration-service/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["--node-env=production"]}, "configurations": {"development": {"args": ["--node-env=development"]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "integration-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "integration-service:build:development"}, "production": {"buildTarget": "integration-service:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}