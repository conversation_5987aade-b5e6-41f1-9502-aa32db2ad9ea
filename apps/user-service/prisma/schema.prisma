// User Service Prisma Schema
// Manages user data and authentication

generator client {
  provider = "prisma-client-js"
  output   = "../../../node_modules/.prisma/user-client"
}

datasource db {
  provider = "postgresql"
  url      = env("USER_DATABASE_URL")
}

// ============================================================================
// USER MANAGEMENT
// ============================================================================

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING
}

// ============================================================================
// RBAC (Role-Based Access Control)
// ============================================================================

model Role {
  id            String   @id @db.VarChar(35)
  name          String   @unique @db.VarChar(50)
  description   String?  @db.Text
  tenantCode    String?  @map("tenant_code") @db.VarChar(50)
  isSystemRole  Boolean  @default(false) @map("is_system_role")
  createdAt     DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt     DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)

  // Relations
  userRoles       UserRole[]
  rolePermissions RolePermission[]
  roleAudits      RoleAudit[]

  @@index([name], map: "idx_roles_name")
  @@index([tenantCode], map: "idx_roles_tenant_code")
  @@index([isSystemRole], map: "idx_roles_system")
  @@map("roles")
}

model Permission {
  id          String   @id @db.VarChar(35)
  name        String   @unique @db.VarChar(100)
  description String?  @db.Text
  resource    String   @db.VarChar(50)
  action      String   @db.VarChar(50)
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamptz(6)

  // Relations
  rolePermissions RolePermission[]
  userPermissions UserPermission[]

  @@index([name], map: "idx_permissions_name")
  @@index([resource], map: "idx_permissions_resource")
  @@index([action], map: "idx_permissions_action")
  @@index([resource, action], map: "idx_permissions_resource_action")
  @@map("permissions")
}

model UserRole {
  id         String    @id @db.VarChar(35)
  userId     String    @map("user_id") @db.VarChar(35)
  roleId     String    @map("role_id") @db.VarChar(35)
  assignedBy String?   @map("assigned_by") @db.VarChar(35)
  assignedAt DateTime  @default(now()) @map("assigned_at") @db.Timestamptz(6)
  expiresAt  DateTime? @map("expires_at") @db.Timestamptz(6)

  // Relations
  user       User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role       Role @relation(fields: [roleId], references: [id], onDelete: Cascade)
  assignedByUser User? @relation("UserRoleAssignedBy", fields: [assignedBy], references: [id])

  @@unique([userId, roleId], map: "idx_user_roles_user_role_unique")
  @@index([userId], map: "idx_user_roles_user")
  @@index([roleId], map: "idx_user_roles_role")
  @@index([assignedBy], map: "idx_user_roles_assigned_by")
  @@index([expiresAt], map: "idx_user_roles_expires")
  @@map("user_roles")
}

model RolePermission {
  id           String @id @db.VarChar(35)
  roleId       String @map("role_id") @db.VarChar(35)
  permissionId String @map("permission_id") @db.VarChar(35)

  // Relations
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId], map: "idx_role_permissions_role_permission_unique")
  @@index([roleId], map: "idx_role_permissions_role")
  @@index([permissionId], map: "idx_role_permissions_permission")
  @@map("role_permissions")
}

model UserPermission {
  id           String    @id @db.VarChar(35)
  userId       String    @map("user_id") @db.VarChar(35)
  permissionId String    @map("permission_id") @db.VarChar(35)
  grantedBy    String?   @map("granted_by") @db.VarChar(35)
  grantedAt    DateTime  @default(now()) @map("granted_at") @db.Timestamptz(6)
  expiresAt    DateTime? @map("expires_at") @db.Timestamptz(6)

  // Relations
  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  grantedByUser User?    @relation("UserPermissionGrantedBy", fields: [grantedBy], references: [id])

  @@unique([userId, permissionId], map: "idx_user_permissions_user_permission_unique")
  @@index([userId], map: "idx_user_permissions_user")
  @@index([permissionId], map: "idx_user_permissions_permission")
  @@index([grantedBy], map: "idx_user_permissions_granted_by")
  @@index([expiresAt], map: "idx_user_permissions_expires")
  @@map("user_permissions")
}



model User {
  id                   String    @id @db.VarChar(35)
  tenantId             String?   @map("tenant_id") @db.VarChar(35)
  email                String    @db.VarChar(255)
  username             String?   @db.VarChar(255)
  passwordHash         String?   @map("password_hash") @db.VarChar(255)
  firstName            String    @map("first_name") @db.VarChar(100)
  lastName             String    @map("last_name") @db.VarChar(100)
  preferences          Json?     @default("{}")
  tenantCode           String?   @map("tenant_code") @db.VarChar(50)
  status               UserStatus @default(PENDING)
  isEmailVerified      Boolean   @default(false) @map("is_email_verified")
  lastLoginAt          DateTime? @map("last_login_at") @db.Timestamptz(6)
  lastLoginIp          String?   @map("last_login_ip") @db.VarChar(255)
  lastLoginUserAgent   String?   @map("last_login_user_agent") @db.VarChar(500)
  loginAttempts        Int       @default(0) @map("login_attempts")
  lockedUntil          DateTime? @map("locked_until") @db.Timestamptz(6)
  passwordChangedAt    DateTime? @map("password_changed_at") @db.Timestamptz(6)
  failedLoginAttempts  Int       @default(0) @map("failed_login_attempts")
  createdAt            DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt            DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  deletedAt            DateTime? @map("deleted_at") @db.Timestamptz(6)

  // Relations
  userRoles           UserRole[]
  userPermissions     UserPermission[]
  assignedUserRoles   UserRole[] @relation("UserRoleAssignedBy")
  grantedUserPermissions UserPermission[] @relation("UserPermissionGrantedBy")
  roleAudits          RoleAudit[]
  performedRoleAudits RoleAudit[] @relation("RoleAuditPerformedBy")

  @@unique([tenantId, email], map: "idx_users_tenant_email_unique")
  @@unique([email, tenantCode], map: "idx_users_email_tenant_code_unique")
  @@index([email], map: "idx_users_email")
  @@index([tenantCode], map: "idx_users_tenant_code")
  @@index([lastLoginAt], map: "idx_users_last_login")
  @@index([status], map: "idx_users_status")
  @@map("users")
}


model RoleAudit {
  id          String   @id @db.VarChar(35)
  userId      String   @map("user_id") @db.VarChar(35)
  roleId      String   @map("role_id") @db.VarChar(35)
  action      String   @db.VarChar(20) // 'ASSIGNED' or 'REMOVED'
  performedBy String?  @map("performed_by") @db.VarChar(35)
  reason      String?  @db.Text
  timestamp   DateTime @default(now()) @db.Timestamptz(6)
  expiresAt   DateTime? @map("expires_at") @db.Timestamptz(6) // For temporary assignments

  // Relations
  user         User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  role         Role  @relation(fields: [roleId], references: [id], onDelete: Cascade)
  performedByUser User? @relation("RoleAuditPerformedBy", fields: [performedBy], references: [id])

  @@index([userId], map: "idx_role_audit_user")
  @@index([roleId], map: "idx_role_audit_role")
  @@index([action], map: "idx_role_audit_action")
  @@index([timestamp], map: "idx_role_audit_timestamp")
  @@index([userId, timestamp], map: "idx_role_audit_user_timestamp")
  @@map("role_audit")
}
