/* eslint-disable @nx/enforce-module-boundaries */
import { generatePermissionId, generateRoleId, generateRolePermissionId } from '../../../../libs/common/src/lib/utils/cuid.util.js';
import { PrismaClient } from '../../../../node_modules/.prisma/user-client/index.js';

const prisma = new PrismaClient();

/**
 * RBAC Seed Data
 * Seeds initial roles, permissions, and role-permission mappings
 *
 * NOTE: This file uses string literals that correspond to the type-safe enums
 * defined in libs/common/src/lib/rbac/enums/. When the RBAC module is fully
 * integrated, these should be imported and used directly.
 */

// System Roles (corresponding to PlatformRole and TenantRole enums)
const SYSTEM_ROLES = [
  // Platform Roles
  {
    name: 'super_admin', // PlatformRole.SUPER_ADMIN
    description: 'Super Administrator with full system access across all tenants',
    isSystemRole: true,
  },
  {
    name: 'platform_administrator', // PlatformRole.PLATFORM_ADMINISTRATOR
    description: 'Platform administrator with cross-tenant management capabilities',
    isSystemRole: true,
  },

  // Tenant Roles
  {
    name: 'tenant_admin', // TenantRole.TENANT_ADMIN
    description: 'Full administrative access within the tenant',
    isSystemRole: false,
  },

  {
    name: 'user', // TenantRole.USER
    description: 'Regular user with access to assigned tasks and basic features',
    isSystemRole: false,
  },
  {
    name: 'viewer', // TenantRole.VIEWER
    description: 'Read-only access to limited data and reports',
    isSystemRole: false,
  },
];

// System Permissions (corresponding to Permission enums)
const SYSTEM_PERMISSIONS = [
  // Platform Permissions
  { name: 'platform:admin:access', description: 'Access to platform administration features', resource: 'platform', action: 'admin:access' },
  { name: 'platform:config:read', description: 'Read platform configuration', resource: 'platform', action: 'config:read' },
  { name: 'platform:config:write', description: 'Write platform configuration', resource: 'platform', action: 'config:write' },
  { name: 'tenant:create', description: 'Create new tenants', resource: 'tenant', action: 'create' },
  { name: 'tenant:read:all', description: 'Read all tenant information', resource: 'tenant', action: 'read:all' },
  { name: 'tenant:update:all', description: 'Update all tenant settings', resource: 'tenant', action: 'update:all' },
  { name: 'tenant:delete', description: 'Delete tenants', resource: 'tenant', action: 'delete' },
  { name: 'tenant:suspend', description: 'Suspend tenants', resource: 'tenant', action: 'suspend' },
  { name: 'tenant:activate', description: 'Activate tenants', resource: 'tenant', action: 'activate' },
  { name: 'tenant:onboarding:initiate', description: 'Initiate tenant onboarding', resource: 'tenant', action: 'onboarding:initiate' },
  { name: 'tenant:onboarding:manage', description: 'Manage tenant onboarding', resource: 'tenant', action: 'onboarding:manage' },
  { name: 'tenant:onboarding:approve', description: 'Approve tenant onboarding', resource: 'tenant', action: 'onboarding:approve' },
  { name: 'tenant:onboarding:view:all', description: 'View all tenant onboarding processes', resource: 'tenant', action: 'onboarding:view:all' },
  { name: 'platform:analytics:read', description: 'Read platform analytics', resource: 'platform', action: 'analytics:read' },
  { name: 'platform:reports:generate', description: 'Generate platform reports', resource: 'platform', action: 'reports:generate' },
  { name: 'system:health:read', description: 'Read system health status', resource: 'system', action: 'health:read' },
  { name: 'system:metrics:read', description: 'Read system metrics', resource: 'system', action: 'metrics:read' },
  { name: 'system:logs:read', description: 'Read system logs', resource: 'system', action: 'logs:read' },

  // User Management Permissions
  { name: 'user:create', description: 'Create new user accounts', resource: 'user', action: 'create' },
  { name: 'user:read', description: 'Read user information', resource: 'user', action: 'read' },
  { name: 'user:update', description: 'Update user information', resource: 'user', action: 'update' },
  { name: 'user:delete', description: 'Delete user accounts', resource: 'user', action: 'delete' },
  { name: 'user:list', description: 'List users in tenant', resource: 'user', action: 'list' },
  { name: 'user:activate', description: 'Activate user accounts', resource: 'user', action: 'activate' },
  { name: 'user:deactivate', description: 'Deactivate user accounts', resource: 'user', action: 'deactivate' },
  { name: 'user:suspend', description: 'Suspend user accounts', resource: 'user', action: 'suspend' },
  { name: 'user:unlock', description: 'Unlock user accounts', resource: 'user', action: 'unlock' },
  { name: 'user:reset:password', description: 'Reset user passwords', resource: 'user', action: 'reset:password' },
  { name: 'user:force:password:change', description: 'Force password change', resource: 'user', action: 'force:password:change' },
  { name: 'profile:read:own', description: 'Read own profile', resource: 'profile', action: 'read:own' },
  { name: 'profile:update:own', description: 'Update own profile', resource: 'profile', action: 'update:own' },
  { name: 'profile:read:all', description: 'Read all profiles', resource: 'profile', action: 'read:all' },
  { name: 'profile:update:all', description: 'Update all profiles', resource: 'profile', action: 'update:all' },

  // Role and Permission Management
  { name: 'role:create', description: 'Create new roles', resource: 'role', action: 'create' },
  { name: 'role:read', description: 'Read role information', resource: 'role', action: 'read' },
  { name: 'role:update', description: 'Update role information', resource: 'role', action: 'update' },
  { name: 'role:delete', description: 'Delete roles', resource: 'role', action: 'delete' },
  { name: 'role:list', description: 'List available roles', resource: 'role', action: 'list' },
  { name: 'role:assign', description: 'Assign roles to users', resource: 'role', action: 'assign' },
  { name: 'role:revoke', description: 'Revoke roles from users', resource: 'role', action: 'revoke' },
  { name: 'permission:create', description: 'Create new permissions', resource: 'permission', action: 'create' },
  { name: 'permission:read', description: 'Read permission information', resource: 'permission', action: 'read' },
  { name: 'permission:update', description: 'Update permission information', resource: 'permission', action: 'update' },
  { name: 'permission:delete', description: 'Delete permissions', resource: 'permission', action: 'delete' },
  { name: 'permission:list', description: 'List available permissions', resource: 'permission', action: 'list' },
  { name: 'permission:assign', description: 'Assign permissions to users', resource: 'permission', action: 'assign' },
  { name: 'permission:revoke', description: 'Revoke permissions from users', resource: 'permission', action: 'revoke' },

  // Tenant-specific Permissions
  { name: 'tenant:config:read', description: 'Read tenant configuration', resource: 'tenant', action: 'config:read' },
  { name: 'tenant:config:update', description: 'Update tenant configuration', resource: 'tenant', action: 'config:update' },
  { name: 'tenant:features:read', description: 'Read tenant features', resource: 'tenant', action: 'features:read' },
  { name: 'tenant:features:update', description: 'Update tenant features', resource: 'tenant', action: 'features:update' },
  { name: 'tenant:analytics:read', description: 'Read tenant analytics', resource: 'tenant', action: 'analytics:read' },
  { name: 'tenant:reports:generate', description: 'Generate tenant reports', resource: 'tenant', action: 'reports:generate' },
  { name: 'tenant:branding:read', description: 'Read tenant branding', resource: 'tenant', action: 'branding:read' },
  { name: 'tenant:branding:update', description: 'Update tenant branding', resource: 'tenant', action: 'branding:update' },

  // Audit and Monitoring Permissions
  { name: 'audit:read', description: 'Read audit logs', resource: 'audit', action: 'read' },
  { name: 'audit:export', description: 'Export audit logs', resource: 'audit', action: 'export' },
  { name: 'audit:search', description: 'Search audit logs', resource: 'audit', action: 'search' },
  { name: 'monitoring:read', description: 'Read monitoring data', resource: 'monitoring', action: 'read' },
  { name: 'monitoring:configure', description: 'Configure monitoring', resource: 'monitoring', action: 'configure' },
  { name: 'security:event:read', description: 'Read security events', resource: 'security', action: 'event:read' },
  { name: 'security:event:investigate', description: 'Investigate security events', resource: 'security', action: 'event:investigate' },

  // Session Management Permissions
  { name: 'session:read:own', description: 'Read own session information', resource: 'session', action: 'read:own' },
  { name: 'session:read:all', description: 'Read all session information', resource: 'session', action: 'read:all' },
  { name: 'session:terminate:own', description: 'Terminate own sessions', resource: 'session', action: 'terminate:own' },
  { name: 'session:terminate:all', description: 'Terminate all sessions', resource: 'session', action: 'terminate:all' },
  { name: 'session:manage', description: 'Manage user sessions', resource: 'session', action: 'manage' },
];

// Role-Permission Mappings (using new role and permission names)
const ROLE_PERMISSIONS = {
  // Platform Roles
  super_admin: [
    // Super admin gets ALL permissions
    ...SYSTEM_PERMISSIONS.map((p) => p.name),
  ],
  platform_administrator: [
    // Platform administration
    'platform:admin:access',
    'platform:config:read',
    'platform:config:write',
    // Tenant management
    'tenant:create',
    'tenant:read:all',
    'tenant:update:all',
    'tenant:suspend',
    'tenant:activate',
    // Cross-tenant analytics
    'platform:analytics:read',
    'platform:reports:generate',
    // System monitoring
    'system:health:read',
    'system:metrics:read',
    'system:logs:read',
    // User management (cross-tenant)
    'user:create',
    'user:read',
    'user:update',
    'user:delete',
    'user:list',
    'user:activate',
    'user:deactivate',
    'user:suspend',
    'user:unlock',
    'user:reset:password',
    // Role management
    'role:create',
    'role:read',
    'role:update',
    'role:delete',
    'role:list',
    'role:assign',
    'role:revoke',
    // Audit access
    'audit:read',
    'audit:export',
    'audit:search',
    // Session management
    'session:read:all',
    'session:terminate:all',
    'session:manage',
  ],

  // Tenant Roles
  tenant_admin: [
    // Tenant configuration
    'tenant:config:read',
    'tenant:config:update',
    'tenant:features:read',
    'tenant:features:update',
    'tenant:branding:read',
    'tenant:branding:update',
    // User management (within tenant)
    'user:create',
    'user:read',
    'user:update',
    'user:delete',
    'user:list',
    'user:activate',
    'user:deactivate',
    'user:suspend',
    'user:unlock',
    'user:reset:password',
    // Role management (within tenant)
    'role:read',
    'role:list',
    'role:assign',
    'role:revoke',
    // Audit access
    'audit:read',
    'audit:export',
    'audit:search',
    // Session management
    'session:read:all',
    'session:terminate:all',
    'session:manage',
  ],

  user: [
    // Basic profile access
    'profile:read:own',
    'profile:update:own',
    // Session management (own)
    'session:read:own',
    'session:terminate:own',
  ],
};

async function seedRBAC() {
  console.log('🌱 Starting RBAC seed...');

  try {
    // 1. Create Permissions
    console.log('📝 Creating permissions...');
    for (const permission of SYSTEM_PERMISSIONS) {
      await prisma.permission.upsert({
        where: { name: permission.name },
        update: {
          description: permission.description,
          resource: permission.resource,
          action: permission.action,
        },
        create: {
          id: generatePermissionId(),
          ...permission,
        },
      });
    }
    console.log(`✅ Created ${SYSTEM_PERMISSIONS.length} permissions`);

    // 2. Create Roles
    console.log('👥 Creating roles...');
    for (const role of SYSTEM_ROLES) {
      await prisma.role.upsert({
        where: { name: role.name },
        update: {
          description: role.description,
          isSystemRole: role.isSystemRole,
        },
        create: {
          id: generateRoleId(),
          ...role,
        },
      });
    }
    console.log(`✅ Created ${SYSTEM_ROLES.length} roles`);

    // 3. Assign Permissions to Roles
    console.log('🔗 Assigning permissions to roles...');
    for (const [roleName, permissionNames] of Object.entries(ROLE_PERMISSIONS)) {
      const role = await prisma.role.findUnique({ where: { name: roleName } });
      if (!role) {
        console.warn(`⚠️ Role ${roleName} not found, skipping permission assignment`);
        continue;
      }

      // Clear existing permissions for this role
      await prisma.rolePermission.deleteMany({
        where: { roleId: role.id },
      });

      // Add new permissions
      for (const permissionName of permissionNames) {
        const permission = await prisma.permission.findUnique({ where: { name: permissionName } });
        if (!permission) {
          console.warn(`⚠️ Permission ${permissionName} not found, skipping`);
          continue;
        }

        await prisma.rolePermission.create({
          data: {
            id: generateRolePermissionId(),
            roleId: role.id,
            permissionId: permission.id,
          },
        });
      }

      console.log(`✅ Assigned ${permissionNames.length} permissions to ${roleName}`);
    }

    console.log('🎉 RBAC seed completed successfully!');
  } catch (error) {
    console.error('❌ Error seeding RBAC data:', error);
    throw error;
  }
}

export { seedRBAC };

// Run seed if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedRBAC()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
