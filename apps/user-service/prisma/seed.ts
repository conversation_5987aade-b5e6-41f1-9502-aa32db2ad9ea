/* eslint-disable @nx/enforce-module-boundaries */
import { createId } from '@paralleldrive/cuid2';
import { PrismaClient, UserStatus } from '../../../node_modules/.prisma/user-client/index.js';

const prisma = new PrismaClient();

// Temporary CUID generation functions for seed
function generateRoleId(): string {
  return `rol_${createId()}`;
}

function generatePermissionId(): string {
  return `per_${createId()}`;
}

function generateUserRoleId(): string {
  return `uro_${createId()}`;
}

function generateRolePermissionId(): string {
  return `rpe_${createId()}`;
}

// Helper function to generate password hashes
async function generatePasswordHash(password: string): Promise<string> {
  const bcrypt = await import('bcryptjs');
  return bcrypt.hash(password, 12);
}

// System Roles
const SYSTEM_ROLES = [
  {
    name: 'super_admin',
    description: 'Super Administrator with full system access across all tenants',
    isSystemRole: true,
  },
  {
    name: 'admin',
    description: 'Administrator with full access within their tenant',
    isSystemRole: true,
  },
  {
    name: 'manager',
    description: 'Manager with team-level access and user management capabilities',
    isSystemRole: true,
  },
  {
    name: 'support',
    description: 'Support staff with limited administrative access for troubleshooting',
    isSystemRole: true,
  },
  {
    name: 'user',
    description: 'Regular user with basic access to their own data and features',
    isSystemRole: true,
  },
];

// System Permissions
const SYSTEM_PERMISSIONS = [
  // User Management - Self
  { name: 'user:read_own', description: 'Read own user information', resource: 'user', action: 'read_own' },
  { name: 'user:update_own', description: 'Update own user information', resource: 'user', action: 'update_own' },
  { name: 'user:delete_own', description: 'Delete own user account', resource: 'user', action: 'delete_own' },

  // User Management - Others
  { name: 'user:read', description: 'Read user information', resource: 'user', action: 'read' },
  { name: 'user:create', description: 'Create new users', resource: 'user', action: 'create' },
  { name: 'user:update', description: 'Update user information', resource: 'user', action: 'update' },
  { name: 'user:delete', description: 'Delete user accounts', resource: 'user', action: 'delete' },
  { name: 'user:list', description: 'List users in tenant', resource: 'user', action: 'list' },

  // Profile Management - Self
  { name: 'profile:read_own', description: 'Read own profile', resource: 'profile', action: 'read_own' },
  { name: 'profile:update_own', description: 'Update own profile', resource: 'profile', action: 'update_own' },

  // Profile Management - Others
  { name: 'profile:read', description: 'Read user profiles', resource: 'profile', action: 'read' },
  { name: 'profile:update', description: 'Update user profiles', resource: 'profile', action: 'update' },

  // Account Management - Self
  { name: 'account:change_own_password', description: 'Change own password', resource: 'account', action: 'change_own_password' },
  { name: 'account:deactivate_own', description: 'Deactivate own account', resource: 'account', action: 'deactivate_own' },

  // Account Management - Others
  { name: 'account:unlock', description: 'Unlock user accounts', resource: 'account', action: 'unlock' },
  { name: 'account:suspend', description: 'Suspend user accounts', resource: 'account', action: 'suspend' },
  { name: 'account:activate', description: 'Activate user accounts', resource: 'account', action: 'activate' },
  { name: 'account:reset_password', description: 'Reset user passwords', resource: 'account', action: 'reset_password' },

  // Session Management - Self
  { name: 'session:read_own', description: 'Read own sessions', resource: 'session', action: 'read_own' },
  { name: 'session:manage_own', description: 'Manage own sessions', resource: 'session', action: 'manage_own' },

  // Session Management - Others
  { name: 'session:read', description: 'Read user sessions', resource: 'session', action: 'read' },
  { name: 'session:manage', description: 'Manage user sessions', resource: 'session', action: 'manage' },

  // Role Management
  { name: 'role:read', description: 'Read role information', resource: 'role', action: 'read' },
  { name: 'role:create', description: 'Create new roles', resource: 'role', action: 'create' },
  { name: 'role:update', description: 'Update roles', resource: 'role', action: 'update' },
  { name: 'role:delete', description: 'Delete roles', resource: 'role', action: 'delete' },
  { name: 'role:assign', description: 'Assign roles to users', resource: 'role', action: 'assign' },
  { name: 'role:revoke', description: 'Revoke roles from users', resource: 'role', action: 'revoke' },

  // Permission Management
  { name: 'permission:read', description: 'Read permission information', resource: 'permission', action: 'read' },
  { name: 'permission:create', description: 'Create new permissions', resource: 'permission', action: 'create' },
  { name: 'permission:update', description: 'Update permissions', resource: 'permission', action: 'update' },
  { name: 'permission:delete', description: 'Delete permissions', resource: 'permission', action: 'delete' },
  { name: 'permission:assign', description: 'Assign permissions to roles', resource: 'permission', action: 'assign' },
  { name: 'permission:revoke', description: 'Revoke permissions from roles', resource: 'permission', action: 'revoke' },

  // Tenant Management
  { name: 'tenant:read_own', description: 'Read own tenant information', resource: 'tenant', action: 'read_own' },
  { name: 'tenant:update_own', description: 'Update own tenant information', resource: 'tenant', action: 'update_own' },
  { name: 'tenant:read', description: 'Read tenant information', resource: 'tenant', action: 'read' },
  { name: 'tenant:create', description: 'Create new tenants', resource: 'tenant', action: 'create' },
  { name: 'tenant:update', description: 'Update tenant information', resource: 'tenant', action: 'update' },
  { name: 'tenant:delete', description: 'Delete tenants', resource: 'tenant', action: 'delete' },

  // Audit Management
  { name: 'audit:read_own', description: 'Read own audit logs', resource: 'audit', action: 'read_own' },
  { name: 'audit:read', description: 'Read audit logs', resource: 'audit', action: 'read' },
  { name: 'audit:export', description: 'Export audit logs', resource: 'audit', action: 'export' },

  // System Administration
  { name: 'system:admin', description: 'System administration access', resource: 'system', action: 'admin' },
  { name: 'system:config', description: 'System configuration access', resource: 'system', action: 'config' },
  { name: 'system:monitor', description: 'System monitoring access', resource: 'system', action: 'monitor' },
  { name: 'system:backup', description: 'System backup access', resource: 'system', action: 'backup' },
  { name: 'system:restore', description: 'System restore access', resource: 'system', action: 'restore' },

  // Notification Management
  { name: 'notification:send', description: 'Send notifications', resource: 'notification', action: 'send' },
  { name: 'notification:read_own', description: 'Read own notifications', resource: 'notification', action: 'read_own' },
  { name: 'notification:read', description: 'Read notifications', resource: 'notification', action: 'read' },
  { name: 'notification:manage_templates', description: 'Manage notification templates', resource: 'notification', action: 'manage_templates' },
];

// Role-Permission Mappings
const ROLE_PERMISSIONS = {
  super_admin: [
    // Super admin gets all permissions
    ...SYSTEM_PERMISSIONS.map((p) => p.name),
  ],
  admin: [
    // User management
    'user:read',
    'user:create',
    'user:update',
    'user:delete',
    'user:list',
    'user:read_own',
    'user:update_own',

    // Profile management
    'profile:read',
    'profile:update',
    'profile:read_own',
    'profile:update_own',

    // Account management
    'account:unlock',
    'account:suspend',
    'account:activate',
    'account:reset_password',
    'account:change_own_password',
    'account:deactivate_own',

    // Session management
    'session:read',
    'session:manage',
    'session:read_own',
    'session:manage_own',

    // Role management
    'role:read',
    'role:create',
    'role:update',
    'role:delete',
    'role:assign',
    'role:revoke',

    // Permission management
    'permission:read',
    'permission:create',
    'permission:update',
    'permission:delete',
    'permission:assign',
    'permission:revoke',

    // Tenant management
    'tenant:read_own',
    'tenant:update_own',

    // Audit management
    'audit:read',
    'audit:export',

    // Notification management
    'notification:send',
    'notification:read',
    'notification:manage_templates',
  ],
  manager: [
    // User management (limited)
    'user:read',
    'user:create',
    'user:update',
    'user:list',
    'user:read_own',
    'user:update_own',

    // Profile management
    'profile:read',
    'profile:update',
    'profile:read_own',
    'profile:update_own',

    // Account management (limited)
    'account:unlock',
    'account:reset_password',
    'account:change_own_password',
    'account:deactivate_own',

    // Session management
    'session:read',
    'session:manage',
    'session:read_own',
    'session:manage_own',

    // Role management (limited)
    'role:read',
    'role:assign',

    // Permission management (read-only)
    'permission:read',

    // Tenant management
    'tenant:read_own',

    // Audit management
    'audit:read_own',

    // Notification management
    'notification:send',
    'notification:read_own',
  ],
  support: [
    // User management (read-only)
    'user:read',
    'user:list',
    'user:read_own',
    'user:update_own',

    // Profile management
    'profile:read',
    'profile:read_own',
    'profile:update_own',

    // Account management (limited)
    'account:unlock',
    'account:change_own_password',

    // Session management
    'session:read',
    'session:read_own',
    'session:manage_own',

    // Role management (read-only)
    'role:read',

    // Tenant management
    'tenant:read_own',

    // Audit management
    'audit:read',

    // System monitoring
    'system:monitor',

    // Notification management
    'notification:read',
  ],
  user: [
    // User management (own only)
    'user:read_own',
    'user:update_own',
    'user:delete_own',

    // Profile management (own only)
    'profile:read_own',
    'profile:update_own',

    // Account management (own only)
    'account:change_own_password',
    'account:deactivate_own',

    // Session management (own only)
    'session:read_own',
    'session:manage_own',

    // Tenant management (read own)
    'tenant:read_own',

    // Audit management (own only)
    'audit:read_own',

    // Notification management (own only)
    'notification:read_own',
  ],
};

async function seedRBAC() {
  console.log('🌱 Starting RBAC seed...');

  try {
    // 1. Create Permissions
    console.log('📝 Creating permissions...');
    for (const permission of SYSTEM_PERMISSIONS) {
      await prisma.permission.upsert({
        where: { name: permission.name },
        update: {
          description: permission.description,
          resource: permission.resource,
          action: permission.action,
        },
        create: {
          id: generatePermissionId(),
          ...permission,
        },
      });
    }
    console.log(`✅ Created ${SYSTEM_PERMISSIONS.length} permissions`);

    // 2. Create Roles
    console.log('👥 Creating roles...');
    for (const role of SYSTEM_ROLES) {
      await prisma.role.upsert({
        where: { name: role.name },
        update: {
          description: role.description,
          isSystemRole: role.isSystemRole,
        },
        create: {
          id: generateRoleId(),
          ...role,
        },
      });
    }
    console.log(`✅ Created ${SYSTEM_ROLES.length} roles`);

    // 3. Assign Permissions to Roles
    console.log('🔗 Assigning permissions to roles...');
    for (const [roleName, permissionNames] of Object.entries(ROLE_PERMISSIONS)) {
      const role = await prisma.role.findUnique({ where: { name: roleName } });
      if (!role) {
        console.warn(`⚠️ Role '${roleName}' not found, skipping permission assignment`);
        continue;
      }

      // Clear existing role permissions
      await prisma.rolePermission.deleteMany({
        where: { roleId: role.id },
      });

      // Assign new permissions
      for (const permissionName of permissionNames) {
        const permission = await prisma.permission.findUnique({ where: { name: permissionName } });
        if (!permission) {
          console.warn(`⚠️ Permission '${permissionName}' not found, skipping`);
          continue;
        }

        await prisma.rolePermission.create({
          data: {
            id: generateRolePermissionId(),
            roleId: role.id,
            permissionId: permission.id,
          },
        });
      }

      console.log(`✅ Assigned ${permissionNames.length} permissions to role '${roleName}'`);
    }

    console.log('🎉 RBAC seed completed successfully!');
  } catch (error) {
    console.error('❌ Error seeding RBAC data:', error);
    throw error;
  }
}

async function createRonnaUsers() {
  console.log('👥 Creating comprehensive Ronna Bank user set...');

  // Proper CUID format for Ronna Bank tenant
  const ronnaTenantId = 'ten_bzuebwg93hq2qkmmigli5ccz';

  // Generate password hashes for each user
  const superadminHash = await generatePasswordHash('superadmin123');
  const adminHash = await generatePasswordHash('admin123');
  const managerHash = await generatePasswordHash('manager123');
  const supportHash = await generatePasswordHash('support123');
  const userHash = await generatePasswordHash('user123');

  // Define all users with role-based configurations using proper CUID format
  const usersToCreate = [
    {
      id: 'usr_zxy6wrs36byld55jiymv6mlx',
      email: '<EMAIL>',
      passwordHash: superadminHash, // password: superadmin123
      firstName: 'Raymond',
      lastName: 'Tetteh',
      role: 'super_admin',
      status: UserStatus.ACTIVE,
      isEmailVerified: true,
      preferences: {
        dashboard_layout: 'executive',
        notification_preferences: {
          email: true,
          sms: true,
          whatsapp: true,
          in_app: true,
          push: true,
        },
        language: 'en',
        timezone: 'Africa/Accra',
        theme: 'dark',
        security_alerts: true,
      },
    },
    {
      id: 'usr_venjjfonshdukzl2eiw43h3e',
      email: '<EMAIL>',
      passwordHash: adminHash, // password: admin123
      firstName: 'Kwame',
      lastName: 'Asante',
      role: 'admin',
      status: UserStatus.ACTIVE,
      isEmailVerified: true,
      preferences: {
        dashboard_layout: 'admin',
        notification_preferences: {
          email: true,
          sms: true,
          whatsapp: true,
          in_app: true,
        },
        language: 'en',
        timezone: 'Africa/Accra',
        theme: 'light',
      },
    },
    {
      id: 'usr_f6gnr7cxcqcgmlbmswzcwa4s',
      email: '<EMAIL>',
      passwordHash: managerHash, // password: manager123
      firstName: 'Akosua',
      lastName: 'Mensah',
      role: 'manager',
      status: UserStatus.ACTIVE,
      isEmailVerified: true,
    },
    {
      id: 'usr_xgonycskymihc5ohsbxefecm',
      email: '<EMAIL>',
      passwordHash: supportHash, // password: support123
      firstName: 'Kofi',
      lastName: 'Boateng',
      role: 'support',
      status: UserStatus.ACTIVE,
      isEmailVerified: true,
    },
    {
      id: 'usr_b6zzfbdhttr9d6y9w3af9lot',
      email: '<EMAIL>',
      passwordHash: userHash, // password: user123
      firstName: 'Ama',
      lastName: 'Serwaa',
      role: 'user',
      status: UserStatus.ACTIVE,
      isEmailVerified: true,
    },
  ];

  const createdUsers: Array<{ user: any; roleName: string }> = [];

  try {
    // Create all users
    for (const userData of usersToCreate) {
      const user = await prisma.user.upsert({
        where: {
          email_tenantCode: {
            email: userData.email,
            tenantCode: 'ronna-bank',
          },
        },
        update: {},
        create: {
          id: userData.id,
          tenantId: ronnaTenantId,
          email: userData.email,
          passwordHash: userData.passwordHash,
          firstName: userData.firstName,
          lastName: userData.lastName,
          tenantCode: 'ronna-bank',
          status: userData.status,
          isEmailVerified: userData.isEmailVerified,
        },
      });

      createdUsers.push({ user, roleName: userData.role });
      console.log(`✅ Created ${userData.role} user: ${userData.email}`);
    }

    // Assign roles to all created users
    for (const { user, roleName } of createdUsers) {
      const role = await prisma.role.findUnique({ where: { name: roleName } });
      if (role) {
        await prisma.userRole.upsert({
          where: {
            userId_roleId: {
              userId: user.id,
              roleId: role.id,
            },
          },
          update: {},
          create: {
            id: generateUserRoleId(),
            userId: user.id,
            roleId: role.id,
            assignedAt: new Date(),
          },
        });
        console.log(`✅ Assigned ${roleName} role to ${user.email}`);
      } else {
        console.warn(`⚠️ Role ${roleName} not found for user ${user.email}`);
      }
    }

    console.log(`✅ Successfully created ${createdUsers.length} users with proper role assignments`);
  } catch (error) {
    console.error('❌ Error creating Ronna Bank users:', error.message);
    throw error;
  }
}

async function main() {
  console.log('🌱 Seeding User Service database...');

  // Seed RBAC data (roles and permissions)
  await seedRBAC();

  // Create Ronna Bank users with proper roles
  await createRonnaUsers();

  console.log('📝 User service database seeded successfully!');
  console.log('');
  console.log('👤 User Service Database Summary:');
  console.log('  - ✅ Complete RBAC system with roles and permissions');
  console.log('  - ✅ System roles: super_admin, admin, manager, support, user');
  console.log('  - ✅ 40+ granular permissions for fine-grained access control');
  console.log('  - ✅ Role-permission mappings configured');
  console.log('  - ✅ 5 comprehensive test users created for Ronna Bank');
  console.log('');
  console.log('🔐 Available Roles & Test Users:');
  console.log('  - super_admin: <EMAIL> (Nana Akufo-Addo) - Full system access');
  console.log('  - admin: <EMAIL> (Kwame Asante) - Full tenant access');
  console.log('  - manager: <EMAIL> (Akosua Mensah) - Team-level access');
  console.log('  - support: <EMAIL> (Kofi Boateng) - Read-only troubleshooting');
  console.log('  - user: <EMAIL> (Ama Serwaa) - Basic user access');
  console.log('');
  console.log('🔑 Test Credentials (all passwords follow pattern: {role}123):');
  console.log('  - <EMAIL> / superadmin123');
  console.log('  - <EMAIL> / admin123');
  console.log('  - <EMAIL> / manager123');
  console.log('  - <EMAIL> / support123');
  console.log('  - <EMAIL> / user123');
}

main()
  .catch((e) => {
    console.error('❌ User Service database seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
