-- CreateEnum
CREATE TYPE "UserStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING');

-- CreateTable
CREATE TABLE "roles" (
    "id" VARCHAR(35) NOT NULL,
    "name" VARCHAR(50) NOT NULL,
    "description" TEXT,
    "tenant_code" VARCHAR(50),
    "is_system_role" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "roles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "permissions" (
    "id" VARCHAR(35) NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "description" TEXT,
    "resource" VARCHAR(50) NOT NULL,
    "action" VARCHAR(50) NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "permissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_roles" (
    "id" VARCHAR(35) NOT NULL,
    "user_id" VARCHAR(35) NOT NULL,
    "role_id" VARCHAR(35) NOT NULL,
    "assigned_by" VARCHAR(35),
    "assigned_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expires_at" TIMESTAMPTZ(6),

    CONSTRAINT "user_roles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "role_permissions" (
    "id" VARCHAR(35) NOT NULL,
    "role_id" VARCHAR(35) NOT NULL,
    "permission_id" VARCHAR(35) NOT NULL,

    CONSTRAINT "role_permissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_permissions" (
    "id" VARCHAR(35) NOT NULL,
    "user_id" VARCHAR(35) NOT NULL,
    "permission_id" VARCHAR(35) NOT NULL,
    "granted_by" VARCHAR(35),
    "granted_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expires_at" TIMESTAMPTZ(6),

    CONSTRAINT "user_permissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users" (
    "id" VARCHAR(35) NOT NULL,
    "tenant_id" VARCHAR(35),
    "email" VARCHAR(255) NOT NULL,
    "username" VARCHAR(255),
    "password_hash" VARCHAR(255),
    "first_name" VARCHAR(100) NOT NULL,
    "last_name" VARCHAR(100) NOT NULL,
    "preferences" JSONB DEFAULT '{}',
    "tenant_code" VARCHAR(50),
    "status" "UserStatus" NOT NULL DEFAULT 'PENDING',
    "is_email_verified" BOOLEAN NOT NULL DEFAULT false,
    "last_login_at" TIMESTAMPTZ(6),
    "last_login_ip" VARCHAR(255),
    "last_login_user_agent" VARCHAR(500),
    "login_attempts" INTEGER NOT NULL DEFAULT 0,
    "locked_until" TIMESTAMPTZ(6),
    "password_changed_at" TIMESTAMPTZ(6),
    "failed_login_attempts" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ(6),

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "role_audit" (
    "id" VARCHAR(35) NOT NULL,
    "user_id" VARCHAR(35) NOT NULL,
    "role_id" VARCHAR(35) NOT NULL,
    "action" VARCHAR(20) NOT NULL,
    "performed_by" VARCHAR(35),
    "reason" TEXT,
    "timestamp" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expires_at" TIMESTAMPTZ(6),

    CONSTRAINT "role_audit_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "roles_name_key" ON "roles"("name");

-- CreateIndex
CREATE INDEX "idx_roles_name" ON "roles"("name");

-- CreateIndex
CREATE INDEX "idx_roles_tenant_code" ON "roles"("tenant_code");

-- CreateIndex
CREATE INDEX "idx_roles_system" ON "roles"("is_system_role");

-- CreateIndex
CREATE UNIQUE INDEX "permissions_name_key" ON "permissions"("name");

-- CreateIndex
CREATE INDEX "idx_permissions_name" ON "permissions"("name");

-- CreateIndex
CREATE INDEX "idx_permissions_resource" ON "permissions"("resource");

-- CreateIndex
CREATE INDEX "idx_permissions_action" ON "permissions"("action");

-- CreateIndex
CREATE INDEX "idx_permissions_resource_action" ON "permissions"("resource", "action");

-- CreateIndex
CREATE INDEX "idx_user_roles_user" ON "user_roles"("user_id");

-- CreateIndex
CREATE INDEX "idx_user_roles_role" ON "user_roles"("role_id");

-- CreateIndex
CREATE INDEX "idx_user_roles_assigned_by" ON "user_roles"("assigned_by");

-- CreateIndex
CREATE INDEX "idx_user_roles_expires" ON "user_roles"("expires_at");

-- CreateIndex
CREATE UNIQUE INDEX "idx_user_roles_user_role_unique" ON "user_roles"("user_id", "role_id");

-- CreateIndex
CREATE INDEX "idx_role_permissions_role" ON "role_permissions"("role_id");

-- CreateIndex
CREATE INDEX "idx_role_permissions_permission" ON "role_permissions"("permission_id");

-- CreateIndex
CREATE UNIQUE INDEX "idx_role_permissions_role_permission_unique" ON "role_permissions"("role_id", "permission_id");

-- CreateIndex
CREATE INDEX "idx_user_permissions_user" ON "user_permissions"("user_id");

-- CreateIndex
CREATE INDEX "idx_user_permissions_permission" ON "user_permissions"("permission_id");

-- CreateIndex
CREATE INDEX "idx_user_permissions_granted_by" ON "user_permissions"("granted_by");

-- CreateIndex
CREATE INDEX "idx_user_permissions_expires" ON "user_permissions"("expires_at");

-- CreateIndex
CREATE UNIQUE INDEX "idx_user_permissions_user_permission_unique" ON "user_permissions"("user_id", "permission_id");

-- CreateIndex
CREATE INDEX "idx_users_email" ON "users"("email");

-- CreateIndex
CREATE INDEX "idx_users_tenant_code" ON "users"("tenant_code");

-- CreateIndex
CREATE INDEX "idx_users_last_login" ON "users"("last_login_at");

-- CreateIndex
CREATE INDEX "idx_users_status" ON "users"("status");

-- CreateIndex
CREATE UNIQUE INDEX "idx_users_tenant_email_unique" ON "users"("tenant_id", "email");

-- CreateIndex
CREATE UNIQUE INDEX "idx_users_email_tenant_code_unique" ON "users"("email", "tenant_code");

-- CreateIndex
CREATE INDEX "idx_role_audit_user" ON "role_audit"("user_id");

-- CreateIndex
CREATE INDEX "idx_role_audit_role" ON "role_audit"("role_id");

-- CreateIndex
CREATE INDEX "idx_role_audit_action" ON "role_audit"("action");

-- CreateIndex
CREATE INDEX "idx_role_audit_timestamp" ON "role_audit"("timestamp");

-- CreateIndex
CREATE INDEX "idx_role_audit_user_timestamp" ON "role_audit"("user_id", "timestamp");

-- AddForeignKey
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_assigned_by_fkey" FOREIGN KEY ("assigned_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "permissions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_permissions" ADD CONSTRAINT "user_permissions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_permissions" ADD CONSTRAINT "user_permissions_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "permissions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_permissions" ADD CONSTRAINT "user_permissions_granted_by_fkey" FOREIGN KEY ("granted_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_audit" ADD CONSTRAINT "role_audit_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_audit" ADD CONSTRAINT "role_audit_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_audit" ADD CONSTRAINT "role_audit_performed_by_fkey" FOREIGN KEY ("performed_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
