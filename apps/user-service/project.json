{"name": "user-service", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/user-service/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "user-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "user-service:build:development"}, "production": {"buildTarget": "user-service:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}