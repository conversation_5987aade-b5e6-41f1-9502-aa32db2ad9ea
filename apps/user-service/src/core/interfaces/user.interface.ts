export interface CreateUserData {
  email: string;
  passwordHash: string;
  firstName: string;
  lastName: string;
  tenantCode: string;
  tenantId: string;
  roles?: string[];
  permissions?: string[];
}

export interface UpdateUserData {
  firstName?: string;
  lastName?: string;
  roles?: string[];
  status?: string;
}

export interface UserLoginInfo {
  userId: string;
  ipAddress: string;
  userAgent: string;
  timestamp?: Date;
}

export interface UserSearchCriteria {
  email?: string;
  tenantCode?: string;
  tenantId?: string;
  status?: string;
  roles?: string[];
}

export interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  tenantCode: string;
  roles: string[];
  permissions: string[];
  status: string;
  isEmailVerified: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}
