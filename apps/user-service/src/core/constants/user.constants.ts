export const USER_CONSTANTS = {
  // Default values
  DEFAULT_ROLE: 'user',
  DEFAULT_PERMISSIONS: ['read:profile'],
  
  // Limits
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION_MINUTES: 30,
  
  // Password policy
  PASSWORD_MIN_LENGTH: 8,
  PASSWORD_MAX_LENGTH: 128,
  
  // Validation patterns
  EMAIL_PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  NAME_PATTERN: /^[a-zA-Z\s'-]{1,50}$/,
  
  // Error codes
  ERROR_CODES: {
    USER_NOT_FOUND: 'USER_NOT_FOUND',
    USER_ALREADY_EXISTS: 'USER_ALREADY_EXISTS',
    INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
    ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
    ACCOUNT_INACTIVE: 'ACCOUNT_INACTIVE',
    EMAIL_NOT_VERIFIED: 'EMAIL_NOT_VERIFIED',
    INVALID_PASSWORD: 'INVALID_PASSWORD',
    PERMISSION_DENIED: 'PERMISSION_DENIED',
  },
  
  // Cache keys
  CACHE_KEYS: {
    USER_BY_ID: 'user:id:',
    USER_BY_EMAIL: 'user:email:',
    USER_PERMISSIONS: 'user:permissions:',
    USER_ROLES: 'user:roles:',
  },
  
  // Cache TTL (in seconds)
  CACHE_TTL: {
    USER_DATA: 300, // 5 minutes
    USER_PERMISSIONS: 600, // 10 minutes
    USER_ROLES: 600, // 10 minutes
  },
} as const;
