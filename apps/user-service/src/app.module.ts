import { Module } from '@nestjs/common';
import { CommonModule } from '@qeep/common';
import { UserModule } from './domains/user-management/user.module';

@Module({
  imports: [
    CommonModule.forRoot({
      enableTelemetry: false,
      enableGlobalTenantInterceptor: false,
      enableCircuitBreaker: false,
      enableRateLimiting: false,
      enableSecurityHeaders: false,
    }),
    UserModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
