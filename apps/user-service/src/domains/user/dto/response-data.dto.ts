/* eslint-disable @typescript-eslint/no-explicit-any */
import { PaginatedListDataDto } from '@qeep/common';
import { Expose } from 'class-transformer';

/**
 * User profile data DTO
 */
export class UserProfileDataDto {
  @Expose()
  id: string;

  @Expose()
  email: string;

  @Expose({ name: 'first_name' })
  firstName: string;

  @Expose({ name: 'last_name' })
  lastName: string;

  @Expose({ name: 'phone_number' })
  phoneNumber?: string;

  @Expose({ name: 'avatar_url' })
  avatarUrl?: string;

  @Expose()
  timezone?: string;

  @Expose()
  language?: string;

  @Expose()
  status: string;

  @Expose({ name: 'is_email_verified' })
  isEmailVerified: boolean;

  @Expose({ name: 'is_phone_verified' })
  isPhoneVerified?: boolean;

  @Expose({ name: 'tenant_code' })
  tenantCode: string;

  @Expose()
  roles: string[];

  @Expose()
  permissions: string[];

  @Expose()
  preferences?: Record<string, any>;

  @Expose()
  metadata?: Record<string, any>;

  @Expose({ name: 'created_at' })
  createdAt: Date;

  @Expose({ name: 'updated_at' })
  updatedAt: Date;

  @Expose({ name: 'last_login_at' })
  lastLoginAt?: Date;
}

/**
 * User creation response data DTO
 */
export class UserCreationDataDto {
  @Expose({ name: 'user_id' })
  userId: string;

  @Expose()
  email: string;

  @Expose({ name: 'first_name' })
  firstName: string;

  @Expose({ name: 'last_name' })
  lastName: string;

  @Expose()
  status: string;

  @Expose({ name: 'tenant_code' })
  tenantCode: string;

  @Expose({ name: 'invitation_email_sent' })
  invitationEmailSent: boolean;

  @Expose({ name: 'created_at' })
  createdAt: Date;
}

/**
 * User update response data DTO
 */
export class UserUpdateDataDto {
  @Expose({ name: 'user_id' })
  userId: string;

  @Expose({ name: 'updated_fields' })
  updatedFields: string[];

  @Expose({ name: 'updated_at' })
  updatedAt: Date;

  @Expose()
  user: UserProfileDataDto;
}

/**
 * User list response data DTO
 */
export class UserListDataDto extends PaginatedListDataDto<UserProfileDataDto> {
  @Expose()
  items: UserProfileDataDto[];
}

/**
 * User role assignment data DTO
 */
export class UserRoleDataDto {
  @Expose({ name: 'user_id' })
  userId: string;

  @Expose()
  roles: string[];

  @Expose({ name: 'assigned_at' })
  assignedAt: Date;

  @Expose({ name: 'expires_at' })
  expiresAt?: Date;
}

/**
 * User permission data DTO
 */
export class UserPermissionDataDto {
  @Expose({ name: 'user_id' })
  userId: string;

  @Expose()
  permissions: string[];

  @Expose({ name: 'effective_permissions' })
  effectivePermissions: string[];

  @Expose({ name: 'calculated_at' })
  calculatedAt: Date;
}

/**
 * User activity data DTO
 */
export class UserActivityDataDto {
  @Expose({ name: 'activity_id' })
  activityId: string;

  @Expose({ name: 'user_id' })
  userId: string;

  @Expose()
  type: string;

  @Expose()
  description: string;

  @Expose({ name: 'ip_address' })
  ipAddress?: string;

  @Expose({ name: 'user_agent' })
  userAgent?: string;

  @Expose()
  metadata?: Record<string, any>;

  @Expose({ name: 'created_at' })
  createdAt: Date;
}

/**
 * User activity list data DTO
 */
export class UserActivityListDataDto extends PaginatedListDataDto<UserActivityDataDto> {
  @Expose()
  items: UserActivityDataDto[];
}

/**
 * User preferences data DTO
 */
export class UserPreferencesDataDto {
  @Expose({ name: 'user_id' })
  userId: string;

  @Expose({ name: 'notification_preferences' })
  notificationPreferences: {
    email: boolean;
    sms: boolean;
    push: boolean;
    in_app: boolean;
  };

  @Expose({ name: 'display_preferences' })
  displayPreferences: {
    theme: string;
    language: string;
    timezone: string;
    date_format: string;
    time_format: string;
  };

  @Expose({ name: 'privacy_preferences' })
  privacyPreferences: {
    profile_visibility: string;
    activity_tracking: boolean;
    data_sharing: boolean;
  };

  @Expose({ name: 'custom_preferences' })
  customPreferences?: Record<string, any>;

  @Expose({ name: 'updated_at' })
  updatedAt: Date;
}

/**
 * User statistics data DTO
 */
export class UserStatsDataDto {
  @Expose({ name: 'total_users' })
  totalUsers: number;

  @Expose({ name: 'active_users' })
  activeUsers: number;

  @Expose({ name: 'inactive_users' })
  inactiveUsers: number;

  @Expose({ name: 'verified_users' })
  verifiedUsers: number;

  @Expose({ name: 'unverified_users' })
  unverifiedUsers: number;

  @Expose({ name: 'new_users_30d' })
  newUsers30d: number;

  @Expose({ name: 'active_users_30d' })
  activeUsers30d: number;

  @Expose({ name: 'calculated_at' })
  calculatedAt: Date;
}
