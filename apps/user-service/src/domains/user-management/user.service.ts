import { User, UserStatus } from '.prisma/user-client';
import { BadRequestException, ConflictException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { createId } from '@paralleldrive/cuid2';
import { TelemetryService, Traced } from '@qeep/common';

import { UserPrismaService } from '../../database/prisma.service';

// CUID generation functions
function generateUserId(): string {
  return `usr_${createId()}`;
}

function generateUserRoleId(): string {
  return `uro_${createId()}`;
}

function generateRoleAuditId(): string {
  return `rau_${createId()}`;
}

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(private readonly prisma: UserPrismaService, private readonly telemetryService: TelemetryService) {}

  @Traced('UserService.createUser')
  async createUser(
    email: string,
    passwordHash: string,
    firstName: string,
    lastName: string,
    tenantCode: string | null,
    tenantId: string | null,
    status?: UserStatus,
    isEmailVerified?: boolean,
  ): Promise<User> {
    this.logger.log(`Creating user with email: ${email}`);

    // Record telemetry event for user creation attempt
    this.telemetryService.recordEvent({
      name: 'user_creation_attempt',
      timestamp: new Date(),
      severity: 'info',
      attributes: {
        email,
        tenantCode,
      },
    });

    // Check if user already exists
    // If tenantCode is provided, check within that tenant; otherwise check globally
    const whereClause = tenantCode ? { email, tenantCode } : { email };

    const existingUser = await this.prisma.user.findFirst({
      where: whereClause,
    });

    if (existingUser) {
      // Record conflict metric
      this.telemetryService.incrementCounter('user_creation_conflicts_total', 1, {
        tenantCode: tenantCode || 'no_tenant',
        reason: 'email_exists',
      });

      const errorMessage = tenantCode ? 'User already exists with this email in the tenant' : 'User already exists with this email';
      const error = new ConflictException(errorMessage);
      (error as any).code = 'USER_ALREADY_EXISTS';
      throw error;
    }

    const savedUser = await this.prisma.user.create({
      data: {
        id: generateUserId(),
        email,
        passwordHash,
        firstName,
        lastName,
        tenantCode,
        tenantId,
        status: status || UserStatus.PENDING, // Default to PENDING for new signups
        isEmailVerified: isEmailVerified !== undefined ? isEmailVerified : false, // Default to false for new signups
        loginAttempts: 0,
      },
    });

    // Automatically assign the default "user" role to new users
    try {
      await this.assignRole(savedUser.id, 'user', null); // System assignment, no assignedBy
      this.logger.log(`Default 'user' role assigned to new user: ${savedUser.id}`);
    } catch (roleError) {
      this.logger.warn(`Failed to assign default role to user ${savedUser.id}: ${roleError.message}`);
      // Don't fail user creation if role assignment fails
    }

    // Record success metrics
    this.telemetryService.incrementCounter('users_created_total', 1, {
      tenantCode,
    });

    this.telemetryService.recordEvent({
      name: 'user_created_successfully',
      timestamp: new Date(),
      severity: 'info',
      attributes: {
        userId: savedUser.id,
        email,
        tenantCode,
      },
    });

    this.logger.log(`User created successfully with ID: ${savedUser.id}`);
    return savedUser;
  }

  @Traced('UserService.getUserById')
  async getUserById(userId: string): Promise<User> {
    this.logger.log(`Getting user by ID: ${userId}`);

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  @Traced('UserService.getUserByEmail')
  async getUserByEmail(email: string, tenantCode?: string): Promise<User> {
    const whereClause = tenantCode ? { email, tenantCode } : { email };

    const user = await this.prisma.user.findFirst({
      where: whereClause,
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  @Traced('UserService.updateUser')
  async updateUser(userId: string, firstName?: string, lastName?: string, status?: UserStatus, isEmailVerified?: boolean): Promise<User> {
    this.logger.log(`Updating user: ${userId} with status: ${status}`);

    this.logger.debug({ firstName, lastName, status, isEmailVerified });

    // Check if user exists first
    await this.getUserById(userId);

    const updateData: any = {};

    if (firstName !== undefined) {
      updateData.firstName = firstName;
    }

    if (lastName !== undefined) {
      updateData.lastName = lastName;
    }

    if (status !== undefined) {
      updateData.status = status;
    }

    if (isEmailVerified !== undefined) {
      updateData.isEmailVerified = isEmailVerified;
    }

    this.logger.log(`Executing Prisma update with data:`, updateData);
    const updatedUser = await this.prisma.user.update({
      where: { id: userId },
      data: updateData,
    });

    this.logger.log(`User updated successfully: ${userId}, new status: ${updatedUser.status}, isEmailVerified: ${updatedUser.isEmailVerified}`);
    return updatedUser;
  }

  @Traced('UserService.updateUserLoginInfo')
  async updateUserLoginInfo(userId: string, ipAddress: string, userAgent: string): Promise<void> {
    this.logger.log(`Updating login info for user: ${userId}`);

    await this.prisma.user.update({
      where: { id: userId },
      data: {
        lastLoginAt: new Date(),
        lastLoginIp: ipAddress,
        lastLoginUserAgent: userAgent,
        loginAttempts: 0, // Reset login attempts on successful login
      },
    });

    this.logger.log(`Login info updated for user: ${userId}`);
  }

  @Traced('UserService.incrementLoginAttempts')
  async incrementLoginAttempts(userId: string): Promise<void> {
    this.logger.log(`Incrementing login attempts for user: ${userId}`);

    const user = await this.getUserById(userId);
    const newLoginAttempts = user.loginAttempts + 1;

    const updateData: any = {
      loginAttempts: newLoginAttempts,
    };

    // Lock user after 5 failed attempts for 30 minutes
    if (newLoginAttempts >= 5) {
      updateData.lockedUntil = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
      this.logger.warn(`User locked due to too many login attempts: ${userId}`);
    }

    await this.prisma.user.update({
      where: { id: userId },
      data: updateData,
    });
  }

  @Traced('UserService.updatePassword')
  async updatePassword(userId: string, passwordHash: string): Promise<void> {
    this.logger.log(`Updating password for user: ${userId}`);

    // Check if user exists first
    await this.getUserById(userId);

    // Update password hash
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        passwordHash,
        updatedAt: new Date(),
      },
    });

    this.logger.log(`Password updated successfully for user: ${userId}`);
  }

  @Traced('UserService.unlockUser')
  async unlockUser(userId: string): Promise<void> {
    this.logger.log(`Unlocking user: ${userId}`);

    await this.prisma.user.update({
      where: { id: userId },
      data: {
        loginAttempts: 0,
        lockedUntil: null,
      },
    });

    this.logger.log(`User unlocked: ${userId}`);
  }

  // ============================================================================
  // RBAC METHODS
  // ============================================================================

  @Traced('UserService.getUserRoles')
  async getUserRoles(userId: string): Promise<string[]> {
    this.logger.log(`Getting roles for user: ${userId}`);

    const userRoles = await this.prisma.userRole.findMany({
      where: {
        userId,
        OR: [
          { expiresAt: null }, // No expiration
          { expiresAt: { gt: new Date() } }, // Not expired
        ],
      },
      include: {
        role: true,
      },
    });

    const roles = userRoles.map((ur) => ur.role.name);
    this.logger.debug(`User ${userId} has roles: ${roles.join(', ')}`);
    return roles;
  }

  @Traced('UserService.getUserRolesWithDetails')
  async getUserRolesWithDetails(userId: string): Promise<any[]> {
    this.logger.log(`Getting detailed roles for user: ${userId}`);

    const userRoles = await this.prisma.userRole.findMany({
      where: {
        userId,
        OR: [
          { expiresAt: null }, // No expiration
          { expiresAt: { gt: new Date() } }, // Not expired
        ],
      },
      include: {
        role: true,
      },
    });

    this.logger.debug(`User ${userId} has ${userRoles.length} roles`);
    return userRoles;
  }

  @Traced('UserService.getUserPermissions')
  async getUserPermissions(userId: string): Promise<string[]> {
    this.logger.log(`Getting permissions for user: ${userId}`);

    // Get permissions from roles
    const rolePermissions = await this.prisma.$queryRaw<{ name: string }[]>`
      SELECT DISTINCT p.name
      FROM permissions p
      JOIN role_permissions rp ON p.id = rp.permission_id
      JOIN user_roles ur ON rp.role_id = ur.role_id
      WHERE ur.user_id = ${userId}::varchar
      AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
    `;

    // Get direct user permissions
    const directPermissions = await this.prisma.userPermission.findMany({
      where: {
        userId,
        OR: [
          { expiresAt: null }, // No expiration
          { expiresAt: { gt: new Date() } }, // Not expired
        ],
      },
      include: {
        permission: true,
      },
    });

    // Combine and deduplicate permissions
    const allPermissions = [...rolePermissions.map((p) => p.name), ...directPermissions.map((up) => up.permission.name)];

    const uniquePermissions = [...new Set(allPermissions)];
    this.logger.debug(`User ${userId} has permissions: ${uniquePermissions.join(', ')}`);
    return uniquePermissions;
  }

  @Traced('UserService.assignRole')
  async assignRole(userId: string, roleName: string, assignedBy?: string, expiresAt?: Date): Promise<void> {
    this.logger.log(`Assigning role ${roleName} to user: ${userId}`);

    // Check if user exists
    await this.getUserById(userId);

    // Find role
    const role = await this.prisma.role.findUnique({
      where: { name: roleName },
    });

    if (!role) {
      throw new NotFoundException(`Role '${roleName}' not found`);
    }

    // Check if role is already assigned
    const existingUserRole = await this.prisma.userRole.findUnique({
      where: {
        userId_roleId: {
          userId,
          roleId: role.id,
        },
      },
    });

    if (existingUserRole) {
      this.logger.warn(`Role ${roleName} already assigned to user ${userId}`);
      return;
    }

    // Assign role
    await this.prisma.userRole.create({
      data: {
        id: generateUserRoleId(),
        userId,
        roleId: role.id,
        assignedBy,
        expiresAt,
      },
    });

    // Create audit log entry
    await this.prisma.roleAudit.create({
      data: {
        id: generateRoleAuditId(),
        userId,
        roleId: role.id,
        action: 'ASSIGNED',
        performedBy: assignedBy,
        expiresAt,
        reason: `Role '${roleName}' assigned to user`,
      },
    });

    this.logger.log(`Role ${roleName} assigned to user ${userId}`);
  }

  @Traced('UserService.removeRole')
  async removeRole(userId: string, roleName: string): Promise<void> {
    this.logger.log(`Removing role ${roleName} from user: ${userId}`);

    // Find role
    const role = await this.prisma.role.findUnique({
      where: { name: roleName },
    });

    if (!role) {
      throw new NotFoundException(`Role '${roleName}' not found`);
    }

    // Remove role assignment
    const deletedUserRole = await this.prisma.userRole.deleteMany({
      where: {
        userId,
        roleId: role.id,
      },
    });

    if (deletedUserRole.count === 0) {
      this.logger.warn(`Role ${roleName} was not assigned to user ${userId}`);
    } else {
      // Create audit log entry for successful removal
      await this.prisma.roleAudit.create({
        data: {
          id: generateRoleAuditId(),
          userId,
          roleId: role.id,
          action: 'REMOVED',
          performedBy: null, // Could be enhanced to accept removedBy parameter
          reason: `Role '${roleName}' removed from user`,
        },
      });

      this.logger.log(`Role ${roleName} removed from user ${userId}`);
    }
  }

  @Traced('UserService.hasRole')
  async hasRole(userId: string, roleName: string): Promise<boolean> {
    const userRoles = await this.getUserRoles(userId);
    return userRoles.includes(roleName);
  }

  @Traced('UserService.hasPermission')
  async hasPermission(userId: string, permissionName: string): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId);
    return userPermissions.includes(permissionName);
  }

  @Traced('UserService.getUserWithRolesAndPermissions')
  async getUserWithRolesAndPermissions(userId: string): Promise<User & { roles: string[]; permissions: string[] }> {
    const user = await this.getUserById(userId);
    const roles = await this.getUserRoles(userId);
    const permissions = await this.getUserPermissions(userId);

    return {
      ...user,
      roles,
      permissions,
    };
  }

  // ============================================================================
  // USER DELETION METHODS
  // ============================================================================

  @Traced('UserService.softDeleteUser')
  async softDeleteUser(userId: string, deletedBy?: string): Promise<void> {
    this.logger.log(`Soft deleting user: ${userId}`);

    // Check if user exists first
    await this.getUserById(userId);

    // Soft delete the user (RBAC data remains for audit)
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        deletedAt: new Date(),
        status: UserStatus.INACTIVE, // Mark as inactive
        updatedAt: new Date(),
      },
    });

    // Record telemetry event
    this.telemetryService.recordEvent({
      name: 'user_soft_deleted',
      timestamp: new Date(),
      severity: 'info',
      attributes: {
        userId,
        deletedBy: deletedBy || 'unknown',
      },
    });

    this.logger.log(`User soft deleted successfully: ${userId}`);
  }

  @Traced('UserService.hardDeleteUser')
  async hardDeleteUser(userId: string, deletedBy?: string): Promise<void> {
    this.logger.log(`Hard deleting user: ${userId}`);

    // Check if user exists first
    const user = await this.getUserById(userId);

    // Record telemetry event before deletion
    this.telemetryService.recordEvent({
      name: 'user_hard_deleted',
      timestamp: new Date(),
      severity: 'warn',
      attributes: {
        userId,
        email: user.email,
        deletedBy: deletedBy || 'unknown',
      },
    });

    // Hard delete the user (CASCADE will automatically clean up RBAC data)
    await this.prisma.user.delete({
      where: { id: userId },
    });

    this.logger.log(`User hard deleted successfully: ${userId}`);
  }

  @Traced('UserService.restoreUser')
  async restoreUser(userId: string, restoredBy?: string): Promise<void> {
    this.logger.log(`Restoring soft deleted user: ${userId}`);

    // Find the soft deleted user
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.deletedAt) {
      throw new BadRequestException('User is not deleted');
    }

    // Restore the user
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        deletedAt: null,
        status: UserStatus.ACTIVE, // Restore to active status
        updatedAt: new Date(),
      },
    });

    // Record telemetry event
    this.telemetryService.recordEvent({
      name: 'user_restored',
      timestamp: new Date(),
      severity: 'info',
      attributes: {
        userId,
        restoredBy: restoredBy || 'unknown',
      },
    });

    this.logger.log(`User restored successfully: ${userId}`);
  }

  @Traced('UserService.getDeletedUsers')
  async getDeletedUsers(limit = 50, offset = 0): Promise<User[]> {
    this.logger.log(`Getting deleted users with limit: ${limit}, offset: ${offset}`);

    const deletedUsers = await this.prisma.user.findMany({
      where: {
        deletedAt: {
          not: null,
        },
      },
      orderBy: {
        deletedAt: 'desc',
      },
      take: limit,
      skip: offset,
    });

    this.logger.log(`Found ${deletedUsers.length} deleted users`);
    return deletedUsers;
  }
}
