import { Test, TestingModule } from '@nestjs/testing';
import { UserGrpcController } from './user.grpc.controller';
import { UserService } from './user.service';

describe('UserGrpcController', () => {
  let controller: UserGrpcController;
  let service: UserService;

  const mockUserService = {
    createUser: jest.fn(),
    findUserById: jest.fn(),
    findUserByEmail: jest.fn(),
    updateUser: jest.fn(),
    deleteUser: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserGrpcController],
      providers: [
        {
          provide: UserService,
          useValue: mockUserService,
        },
      ],
    }).compile();

    controller = module.get<UserGrpcController>(UserGrpcController);
    service = module.get<UserService>(UserService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createUser', () => {
    it('should create a new user via gRPC', async () => {
      const userData = {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        password: 'password123',
      };

      const createdUser = { id: '123', ...userData };
      mockUserService.createUser.mockResolvedValue(createdUser);

      const result = await controller.createUser(userData);

      expect(result).toEqual(createdUser);
      expect(mockUserService.createUser).toHaveBeenCalledWith(
        userData.email,
        expect.any(String), // hashed password
        userData.firstName,
        userData.lastName,
        expect.any(String), // tenant
        expect.any(String)  // tenantId
      );
    });
  });

  describe('findUserById', () => {
    it('should find user by id via gRPC', async () => {
      const userId = '123';
      const user = { id: userId, email: '<EMAIL>' };

      mockUserService.findUserById.mockResolvedValue(user);

      const result = await controller.findUserById({ id: userId });

      expect(result).toEqual(user);
      expect(mockUserService.findUserById).toHaveBeenCalledWith(userId);
    });
  });

  describe('findUserByEmail', () => {
    it('should find user by email via gRPC', async () => {
      const email = '<EMAIL>';
      const user = { id: '123', email };

      mockUserService.findUserByEmail.mockResolvedValue(user);

      const result = await controller.findUserByEmail({ email });

      expect(result).toEqual(user);
      expect(mockUserService.findUserByEmail).toHaveBeenCalledWith(email);
    });
  });
});
