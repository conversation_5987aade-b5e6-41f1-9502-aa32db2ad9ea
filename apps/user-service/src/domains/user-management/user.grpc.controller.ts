import { <PERSON>, Logger } from '@nestjs/common';
import { TelemetryService, Traced } from '@qeep/common';
import {
  CreateUserRequest,
  CreateUserResponse,
  GetDeletedUsersRequest,
  GetDeletedUsersResponse,
  GetUserByEmailRequest,
  GetUserByEmailResponse,
  GetUserRequest,
  GetUserResponse,
  HardDeleteUserRequest,
  HardDeleteUserResponse,
  RestoreUserRequest,
  RestoreUserResponse,
  SoftDeleteUserRequest,
  SoftDeleteUserResponse,
  UpdatePasswordRequest,
  UpdatePasswordResponse,
  UpdateUserLoginInfoRequest,
  UpdateUserLoginInfoResponse,
  UpdateUserRequest,
  UpdateUserResponse,
  UserUser,
  UserUserStatus,
} from '@qeep/proto';

// Additional role management imports
import type { GetUserRolesRequest, GetUserRolesResponse, Role } from '@qeep/proto';

import { UserStatus } from '.prisma/user-client';
import { GrpcMethod } from '@nestjs/microservices';
import { UserService } from './user.service';

@Controller()
export class UserGrpcController {
  private readonly logger = new Logger(UserGrpcController.name);

  constructor(private readonly userService: UserService, private readonly telemetryService: TelemetryService) {}

  @GrpcMethod('UserService', 'CreateUser')
  @Traced('UserGrpcController.createUser')
  async createUser(request: CreateUserRequest): Promise<CreateUserResponse> {
    this.logger.log(`gRPC CreateUser request for email: ${request.email}`);

    try {
      // Handle tenant assignment - null during signup, assigned during onboarding
      const tenantCode = request.tenantCode || null;
      const tenantId = tenantCode ? '' : null; // TODO: Get tenant ID from tenant service

      const user = await this.userService.createUser(
        request.email,
        request.passwordHash,
        request.firstName,
        request.lastName,
        tenantCode,
        tenantId,
        this.mapProtoStatusToUserStatus(request.status),
        request.isEmailVerified,
      );

      return {
        success: true,
        user: this.mapUserToProto(user),
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`CreateUser failed: ${error.message}`, error.stack);

      // Use specific error codes when available
      const errorCode = (error as any).code || 'USER_CREATION_FAILED';

      return {
        success: false,
        user: undefined,
        error: {
          code: errorCode,
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }

  @GrpcMethod('UserService', 'GetUser')
  async getUser(request: GetUserRequest): Promise<GetUserResponse> {
    this.logger.log(`gRPC GetUser request for ID: ${request.userId}`);

    try {
      const user = await this.userService.getUserById(request.userId);

      return {
        success: true,
        user: this.mapUserToProto(user),
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`GetUser failed: ${error.message}`, error.stack);
      return {
        success: false,
        user: undefined,
        error: {
          code: 'USER_NOT_FOUND',
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }

  @GrpcMethod('UserService', 'GetUserByEmail')
  async getUserByEmail(request: GetUserByEmailRequest): Promise<GetUserByEmailResponse> {
    this.logger.log(`gRPC GetUserByEmail request for email: ${request.email} for tenant: ${request.tenantCode || 'none'}`);

    try {
      const user = await this.userService.getUserByEmail(request.email, request.tenantCode || undefined);

      return {
        success: true,
        user: this.mapUserToProto(user),
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`GetUserByEmail failed: ${error.message}`, error.stack);
      return {
        success: false,
        user: undefined,
        error: {
          code: 'USER_NOT_FOUND',
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }

  @GrpcMethod('UserService', 'UpdateUser')
  async updateUser(request: UpdateUserRequest): Promise<UpdateUserResponse> {
    this.logger.log(`gRPC UpdateUser request for ID: ${request.userId}`);
    this.logger.log({ request });

    try {
      const user = await this.userService.updateUser(request.userId, request.firstName, request.lastName, this.mapProtoStatusToEntity(request.status), request.isEmailVerified);

      return {
        success: true,
        user: this.mapUserToProto(user),
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`UpdateUser failed: ${error.message}`, error.stack);
      return {
        success: false,
        user: undefined,
        error: {
          code: 'USER_UPDATE_FAILED',
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }

  @GrpcMethod('UserService', 'UpdateUserLoginInfo')
  async updateUserLoginInfo(request: UpdateUserLoginInfoRequest): Promise<UpdateUserLoginInfoResponse> {
    this.logger.log(`gRPC UpdateUserLoginInfo request for ID: ${request.userId}`);

    try {
      await this.userService.updateUserLoginInfo(request.userId, request.ipAddress, request.userAgent);

      return {
        success: true,
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`UpdateUserLoginInfo failed: ${error.message}`, error.stack);
      return {
        success: false,
        error: {
          code: 'USER_LOGIN_INFO_UPDATE_FAILED',
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }

  @GrpcMethod('UserService', 'UpdatePassword')
  async updatePassword(request: UpdatePasswordRequest): Promise<UpdatePasswordResponse> {
    this.logger.log(`gRPC UpdatePassword request for ID: ${request.userId}`);

    try {
      await this.userService.updatePassword(request.userId, request.passwordHash);

      return {
        success: true,
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`UpdatePassword failed: ${error.message}`, error.stack);
      return {
        success: false,
        error: {
          code: 'PASSWORD_UPDATE_FAILED',
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }

  private mapUserToProto(user: any): UserUser {
    return {
      id: user.id,
      email: user.email,
      passwordHash: user.passwordHash,
      firstName: user.firstName,
      lastName: user.lastName,
      tenantCode: user.tenantCode,
      status: this.mapEntityStatusToProto(user.status),
      isEmailVerified: user.isEmailVerified,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      lastLoginAt: user.lastLoginAt,
      lastLoginIp: user.lastLoginIp || '',
      lastLoginUserAgent: user.lastLoginUserAgent || '',
      loginAttempts: user.loginAttempts,
      lockedUntil: user.lockedUntil,
    };
  }

  private mapEntityStatusToProto(status: any): UserUserStatus {
    switch (status) {
      case 'ACTIVE':
        return UserUserStatus.USER_STATUS_ACTIVE;
      case 'INACTIVE':
        return UserUserStatus.USER_STATUS_INACTIVE;
      case 'SUSPENDED':
        return UserUserStatus.USER_STATUS_SUSPENDED;
      case 'PENDING':
        return UserUserStatus.USER_STATUS_PENDING;
      default:
        return UserUserStatus.USER_STATUS_UNKNOWN;
    }
  }

  private mapProtoStatusToEntity(status: UserUserStatus | number): UserStatus {
    // Handle both enum constants and numeric values
    const statusValue = typeof status === 'number' ? status : this.getStatusNumber(status);

    switch (statusValue) {
      case 1: // USER_STATUS_ACTIVE
        return UserStatus.ACTIVE;
      case 2: // USER_STATUS_INACTIVE
        return UserStatus.INACTIVE;
      case 3: // USER_STATUS_SUSPENDED
        return UserStatus.SUSPENDED;
      case 4: // USER_STATUS_PENDING
        return UserStatus.PENDING;
      case 0: // USER_STATUS_UNKNOWN
      default:
        return UserStatus.PENDING;
    }
  }

  private getStatusNumber(status: UserUserStatus): number {
    switch (status) {
      case UserUserStatus.USER_STATUS_UNKNOWN:
        return 0;
      case UserUserStatus.USER_STATUS_ACTIVE:
        return 1;
      case UserUserStatus.USER_STATUS_INACTIVE:
        return 2;
      case UserUserStatus.USER_STATUS_SUSPENDED:
        return 3;
      case UserUserStatus.USER_STATUS_PENDING:
        return 4;
      default:
        return 0;
    }
  }

  private mapProtoStatusToUserStatus(protoStatus?: UserUserStatus | number): UserStatus | undefined {
    if (!protoStatus || protoStatus === UserUserStatus.USER_STATUS_UNKNOWN || protoStatus === 0) {
      return undefined; // Let service use default
    }

    // Handle both enum constants and numeric values
    const statusValue = typeof protoStatus === 'number' ? protoStatus : this.getStatusNumber(protoStatus);

    switch (statusValue) {
      case 1: // USER_STATUS_ACTIVE
        return UserStatus.ACTIVE;
      case 2: // USER_STATUS_INACTIVE
        return UserStatus.INACTIVE;
      case 3: // USER_STATUS_SUSPENDED
        return UserStatus.SUSPENDED;
      case 4: // USER_STATUS_PENDING
        return UserStatus.PENDING;
      default:
        return undefined;
    }
  }

  // ============================================================================
  // USER DELETION GRPC METHODS
  // ============================================================================

  @GrpcMethod('UserService', 'SoftDeleteUser')
  async softDeleteUser(request: SoftDeleteUserRequest): Promise<SoftDeleteUserResponse> {
    this.logger.log(`gRPC SoftDeleteUser request for ID: ${request.userId}`);

    try {
      await this.userService.softDeleteUser(request.userId, request.deletedBy);

      return {
        success: true,
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`SoftDeleteUser failed: ${error.message}`, error.stack);
      return {
        success: false,
        error: {
          code: 'USER_SOFT_DELETE_FAILED',
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }

  @GrpcMethod('UserService', 'HardDeleteUser')
  async hardDeleteUser(request: HardDeleteUserRequest): Promise<HardDeleteUserResponse> {
    this.logger.log(`gRPC HardDeleteUser request for ID: ${request.userId}`);

    try {
      await this.userService.hardDeleteUser(request.userId, request.deletedBy);

      return {
        success: true,
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`HardDeleteUser failed: ${error.message}`, error.stack);
      return {
        success: false,
        error: {
          code: 'USER_HARD_DELETE_FAILED',
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }

  @GrpcMethod('UserService', 'RestoreUser')
  async restoreUser(request: RestoreUserRequest): Promise<RestoreUserResponse> {
    this.logger.log(`gRPC RestoreUser request for ID: ${request.userId}`);

    try {
      await this.userService.restoreUser(request.userId, request.restoredBy);

      // Get the restored user to return in response
      const user = await this.userService.getUserById(request.userId);

      return {
        success: true,
        user: this.mapUserToProto(user),
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`RestoreUser failed: ${error.message}`, error.stack);
      return {
        success: false,
        user: undefined,
        error: {
          code: 'USER_RESTORE_FAILED',
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }

  @GrpcMethod('UserService', 'GetDeletedUsers')
  async getDeletedUsers(request: GetDeletedUsersRequest): Promise<GetDeletedUsersResponse> {
    this.logger.log(`gRPC GetDeletedUsers request with limit: ${request.limit}, offset: ${request.offset}`);

    try {
      const limit = request.limit || 50;
      const offset = request.offset || 0;

      const deletedUsers = await this.userService.getDeletedUsers(limit, offset);

      return {
        success: true,
        users: deletedUsers.map((user) => this.mapUserToProto(user)),
        totalCount: deletedUsers.length,
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`GetDeletedUsers failed: ${error.message}`, error.stack);
      return {
        success: false,
        users: [],
        totalCount: 0,
        error: {
          code: 'GET_DELETED_USERS_FAILED',
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }

  // ============================================================================
  // ROLE MANAGEMENT GRPC METHODS
  // ============================================================================

  @GrpcMethod('UserService', 'GetUserRoles')
  async getUserRoles(request: GetUserRolesRequest): Promise<GetUserRolesResponse> {
    this.logger.log(`gRPC GetUserRoles request for user ID: ${request.userId}`);

    try {
      const userRoles = await this.userService.getUserRolesWithDetails(request.userId);

      const roles: Role[] = userRoles.map((userRole) => ({
        id: userRole.role.id,
        name: userRole.role.name,
        description: userRole.role.description,
        isSystemRole: userRole.role.isSystemRole,
        createdAt: userRole.role.createdAt,
        updatedAt: userRole.role.updatedAt,
      }));

      return {
        success: true,
        roles,
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`GetUserRoles failed: ${error.message}`, error.stack);
      return {
        success: false,
        roles: [],
        error: {
          code: 'GET_USER_ROLES_FAILED',
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }

  @GrpcMethod('UserService', 'GetUserPermissions')
  async getUserPermissions(request: any): Promise<any> {
    this.logger.log(`gRPC GetUserPermissions request for user ID: ${request.userId}`);

    try {
      const permissions = await this.userService.getUserPermissions(request.userId);

      return {
        success: true,
        permissions,
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`GetUserPermissions failed: ${error.message}`, error.stack);
      return {
        success: false,
        permissions: [],
        error: {
          code: 'GET_USER_PERMISSIONS_FAILED',
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }
}
