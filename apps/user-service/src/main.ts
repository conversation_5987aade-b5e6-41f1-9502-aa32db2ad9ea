/**
 * Qeep User Service
 * Handles user profile management, roles, and permissions
 */

import { Logger, ValidationPipe, VersioningType } from '@nestjs/common';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';

import { NestFactory } from '@nestjs/core';
import { ConfigService, ProtoConfigService } from '@qeep/common';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  const protoConfigService = app.get(ProtoConfigService);

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // Global prefix for all routes
  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);

  // Enable API versioning
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
    prefix: 'v',
  });

  // Set up gRPC microservice
  const grpcPort = configService.getServicePort('user-service-grpc');
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.GRPC,
    options: {
      package: 'user',
      protoPath: protoConfigService.getProtoPath('user', 'user.proto'),
      url: `0.0.0.0:${grpcPort}`,
    },
  });

  // Start all microservices
  await app.startAllMicroservices();

  // Use specific port for User Service HTTP
  const port = configService.getServicePort('user-service');
  const host = configService.getServiceHost('user-service');

  await app.listen(port, host);

  Logger.log(`👤 User Service HTTP is running on: http://${host}:${port}/${globalPrefix}`);
  Logger.log(`🔌 User Service gRPC is running on: ${host}:${grpcPort}`);
  Logger.log(`📚 Environment: ${configService.getNodeEnv()}`);
  Logger.log(`🔧 Log Level: ${configService.getLogLevel()}`);
}

bootstrap();
