import { USER_CONSTANTS } from '../../core/constants/user.constants';
import { UserStatus } from '../../core/enums/user-status.enum';

export class UserUtils {
  /**
   * Validate email format
   */
  static isValidEmail(email: string): boolean {
    return USER_CONSTANTS.EMAIL_PATTERN.test(email);
  }

  /**
   * Validate name format
   */
  static isValidName(name: string): boolean {
    return USER_CONSTANTS.NAME_PATTERN.test(name);
  }

  /**
   * Get default permissions for a role
   *
   * @deprecated This method is deprecated. Use the centralized RBAC system from @qeep/common instead.
   * Role-permission mappings are now handled by the RBAC module.
   */
  static getDefaultPermissionsForRole(): string[] {
    // This method is deprecated and should not be used.
    // Role-permission mappings are now handled by the centralized RBAC system.
    console.warn('UserUtils.getDefaultPermissionsForRole is deprecated. Use RBAC module from @qeep/common instead.');
    return [];
  }

  /**
   * Check if user status is active
   */
  static isActiveStatus(status: UserStatus): boolean {
    return status === UserStatus.ACTIVE;
  }

  /**
   * Check if user can login
   */
  static canLogin(status: UserStatus, isEmailVerified: boolean, lockedUntil?: Date): boolean {
    if (!this.isActiveStatus(status)) {
      return false;
    }

    if (!isEmailVerified) {
      return false;
    }

    if (lockedUntil && lockedUntil > new Date()) {
      return false;
    }

    return true;
  }

  /**
   * Generate cache key for user data
   */
  static generateCacheKey(type: keyof typeof USER_CONSTANTS.CACHE_KEYS, identifier: string): string {
    return `${USER_CONSTANTS.CACHE_KEYS[type]}${identifier}`;
  }

  /**
   * Sanitize user data for public consumption (remove sensitive fields)
   */
  static sanitizeUserData(user: Record<string, unknown>): Record<string, unknown> {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { passwordHash, ...sanitizedUser } = user;
    return sanitizedUser;
  }

  /**
   * Check if user has specific permission
   *
   * @deprecated This method is deprecated. Use RolePermissionUtils from @qeep/common instead.
   */
  static hasPermission(userPermissions: string[], requiredPermission: string): boolean {
    console.warn('UserUtils.hasPermission is deprecated. Use RolePermissionUtils from @qeep/common instead.');
    return userPermissions.includes(requiredPermission);
  }

  /**
   * Check if user has any of the specified roles
   *
   * @deprecated This method is deprecated. Use RBACUtils from @qeep/common instead.
   */
  static hasAnyRole(userRoles: string[], requiredRoles: string[]): boolean {
    console.warn('UserUtils.hasAnyRole is deprecated. Use RBACUtils from @qeep/common instead.');
    return requiredRoles.some((role) => userRoles.includes(role));
  }
}
