import { CanActivate, ExecutionContext, ForbiddenException, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Permission, RolePermissionUtils } from '@qeep/common';

export const PERMISSIONS_KEY = 'permissions';

/**
 * @deprecated This guard is deprecated. Use the centralized RBAC guards from @qeep/common instead.
 * Import RbacGuard and use the type-safe decorators like @RequirePermissions() or @RequireRoles().
 */
@Injectable()
export class UserPermissionGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    console.warn('UserPermissionGuard is deprecated. Use RbacGuard from @qeep/common instead.');

    const requiredPermissions = this.reflector.getAllAndOverride<Permission[]>(PERMISSIONS_KEY, [context.getHandler(), context.getClass()]);

    if (!requiredPermissions) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    // Use the centralized RBAC system
    const hasPermission = requiredPermissions.some((permission) => RolePermissionUtils.userHasPermission(user.roles || [], permission));

    if (!hasPermission) {
      throw new ForbiddenException('Insufficient permissions');
    }

    return true;
  }
}
