import { Test, TestingModule } from '@nestjs/testing';
import { UserPrismaService } from './prisma.service';

describe('UserPrismaService', () => {
  let service: UserPrismaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [UserPrismaService],
    }).compile();

    service = module.get<UserPrismaService>(UserPrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should extend PrismaClient', () => {
    expect(service).toHaveProperty('user');
    expect(service).toHaveProperty('$connect');
    expect(service).toHaveProperty('$disconnect');
  });

  describe('connection management', () => {
    it('should have connect method', () => {
      expect(typeof service.onModuleInit).toBe('function');
    });

    it('should have disconnect method', () => {
      expect(typeof service.onModuleDestroy).toBe('function');
    });
  });

  describe('database operations', () => {
    it('should have user model available', () => {
      expect(service.user).toBeDefined();
      expect(typeof service.user.findFirst).toBe('function');
      expect(typeof service.user.create).toBe('function');
      expect(typeof service.user.update).toBe('function');
      expect(typeof service.user.delete).toBe('function');
    });
  });
});
