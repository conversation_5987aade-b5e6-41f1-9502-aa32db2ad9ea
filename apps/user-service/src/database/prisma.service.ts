import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/user-client';
import { BasePrismaService } from '@qeep/common';

@Injectable()
export class UserPrismaService extends BasePrismaService {
  protected prismaClient: PrismaClient;

  constructor() {
    super();
    this.prismaClient = new PrismaClient({
      // log: ['query', 'info', 'warn', 'error'],
      errorFormat: 'colorless',
    });
  }

  // Expose Prisma client methods
  get user() {
    return this.prismaClient.user;
  }

  // RBAC models
  get role() {
    return this.prismaClient.role;
  }

  get permission() {
    return this.prismaClient.permission;
  }

  get userRole() {
    return this.prismaClient.userRole;
  }

  get rolePermission() {
    return this.prismaClient.rolePermission;
  }

  get userPermission() {
    return this.prismaClient.userPermission;
  }

  get roleAudit() {
    return this.prismaClient.roleAudit;
  }

  get $queryRaw() {
    return this.prismaClient.$queryRaw.bind(this.prismaClient);
  }

  get $transaction() {
    return this.prismaClient.$transaction.bind(this.prismaClient);
  }
}
