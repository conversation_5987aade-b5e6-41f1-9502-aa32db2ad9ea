import { Injectable } from '@nestjs/common';
import { ConfigService } from '@qeep/common';

@Injectable()
export class UserConfigService {
  constructor(private readonly configService: ConfigService) {}

  // Database configuration
  get databaseUrl(): string {
    return this.configService.getOrThrow('USER_DATABASE_URL');
  }

  // Service configuration
  get port(): number {
    return this.configService.getServicePort('user-service');
  }

  get grpcPort(): number {
    return this.configService.getGrpcServicePort('user-service');
  }

  get host(): string {
    return this.configService.getServiceHost('user-service');
  }

  // User-specific configuration
  get defaultUserRole(): string {
    return this.configService.get('DEFAULT_USER_ROLE') || 'user';
  }

  get defaultUserPermissions(): string[] {
    const permissions = this.configService.get('DEFAULT_USER_PERMISSIONS') || 'read:profile';
    return permissions.split(',').map((p: string) => p.trim());
  }

  get maxLoginAttempts(): number {
    return parseInt(this.configService.get('MAX_LOGIN_ATTEMPTS') || '5', 10);
  }

  get lockoutDurationMinutes(): number {
    return parseInt(this.configService.get('LOCKOUT_DURATION_MINUTES') || '30', 10);
  }

  // Password policy
  get passwordMinLength(): number {
    return parseInt(this.configService.get('PASSWORD_MIN_LENGTH') || '8', 10);
  }

  get passwordRequireUppercase(): boolean {
    return this.configService.get('PASSWORD_REQUIRE_UPPERCASE') === 'true';
  }

  get passwordRequireLowercase(): boolean {
    return this.configService.get('PASSWORD_REQUIRE_LOWERCASE') === 'true';
  }

  get passwordRequireNumbers(): boolean {
    return this.configService.get('PASSWORD_REQUIRE_NUMBERS') === 'true';
  }

  get passwordRequireSpecialChars(): boolean {
    return this.configService.get('PASSWORD_REQUIRE_SPECIAL_CHARS') === 'true';
  }
}
