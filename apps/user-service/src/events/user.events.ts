export interface UserCreatedEvent {
  userId: string;
  email: string;
  tenantCode: string;
  tenantId: string;
  roles: string[];
  timestamp: Date;
}

export interface UserUpdatedEvent {
  userId: string;
  email: string;
  tenantCode: string;
  changes: {
    firstName?: string;
    lastName?: string;
    roles?: string[];
    status?: string;
  };
  timestamp: Date;
}

export interface UserLoginEvent {
  userId: string;
  email: string;
  tenantCode: string;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
}

export interface UserStatusChangedEvent {
  userId: string;
  email: string;
  tenantCode: string;
  oldStatus: string;
  newStatus: string;
  timestamp: Date;
}

export interface UserLockedEvent {
  userId: string;
  email: string;
  tenantCode: string;
  reason: string;
  lockedUntil: Date;
  timestamp: Date;
}

export interface UserUnlockedEvent {
  userId: string;
  email: string;
  tenantCode: string;
  timestamp: Date;
}

export const USER_EVENTS = {
  USER_CREATED: 'user.created',
  USER_UPDATED: 'user.updated',
  USER_LOGIN: 'user.login',
  USER_STATUS_CHANGED: 'user.status.changed',
  USER_LOCKED: 'user.locked',
  USER_UNLOCKED: 'user.unlocked',
} as const;
