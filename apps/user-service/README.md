# User Service

The User Service manages user profiles, roles, permissions, and authentication workflows in the Qeep Financial Transaction Monitoring System.

## 📁 Folder Structure

The service follows the new standardized folder structure:

```bash
user-service/
├── src/
│   ├── main.ts                    # Application entry point
│   ├── app/                       # Main application module
│   │   ├── app.module.ts          # Root module
│   │   ├── app.controller.ts      # Health check endpoints
│   │   └── app.service.ts         # Basic app service
│   │
│   ├── modules/                   # Feature modules
│   │   └── user-management/       # User management module
│   │       ├── user.service.ts    # Business logic
│   │       ├── user.grpc.controller.ts # gRPC endpoints
│   │       ├── user.module.ts     # Module definition
│   │       └── user.service.spec.ts # Unit tests
│   │
│   ├── config/                    # Configuration management
│   │   └── user.config.ts         # User service configuration
│   │
│   ├── core/                      # Core business logic & shared types
│   │   ├── interfaces/            # TypeScript interfaces
│   │   │   └── user.interface.ts  # User-related interfaces
│   │   ├── enums/                 # Business enums
│   │   │   └── user-status.enum.ts # User status, roles, permissions
│   │   └── constants/             # Application constants
│   │       └── user.constants.ts  # User-related constants
│   │
│   ├── database/                  # Data access layer
│   │   └── prisma.service.ts      # Prisma database service
│   │
│   ├── common/                    # Common utilities
│   │   └── utils/                 # Utility functions
│   │       └── user.utils.ts      # User-related utilities
│   │
│   ├── guards/                    # Authorization & access control
│   │   └── user-permission.guard.ts # Permission-based guard
│   │
│   ├── middleware/                # Request/response processing
│   │   └── tenant-context.middleware.ts # Tenant context middleware
│   │
│   └── events/                    # Event-driven architecture
│       └── user.events.ts         # User-related events
│
├── prisma/                        # Database schema and migrations
│   └── schema.prisma              # Prisma schema
├── Dockerfile                     # Container configuration
└── project.json                   # NX project configuration
```

## 🔧 Core Responsibilities

- **User Profile Management**: Create, update, and manage user profiles
- **Role & Permission Management**: Assign and manage user roles and permissions
- **Authentication Support**: Provide user data for authentication workflows
- **Multi-tenant Support**: Manage users across different tenant organizations
- **User Status Management**: Handle user activation, suspension, and verification

## 🚀 Key Features

- **gRPC API**: High-performance service-to-service communication
- **Multi-tenant Architecture**: Proper tenant isolation and context
- **Role-based Access Control**: Flexible permission system
- **User Lifecycle Management**: Complete user management workflow
- **Event-driven Architecture**: Publishes user-related events
- **Comprehensive Validation**: Input validation and business rule enforcement

## 📋 API Endpoints

### gRPC Methods

- `CreateUser`: Create a new user account
- `GetUser`: Retrieve user by ID
- `GetUserByEmail`: Retrieve user by email and tenant
- `UpdateUser`: Update user information
- `UpdateUserLoginInfo`: Update user login information

## 🔐 Security Features

- **Permission Guards**: Method-level permission checking
- **Tenant Context**: Automatic tenant isolation
- **Input Validation**: Comprehensive data validation
- **Audit Logging**: Track user operations and changes

## 🧪 Testing

Run unit tests:
```bash
npx nx test user-service
```

Run integration tests:
```bash
npx nx test:integration user-service
```

## 🔧 Configuration

The service uses environment variables for configuration:

- `USER_DATABASE_URL`: PostgreSQL connection string
- `USER_SERVICE_PORT`: HTTP port (default: 3002)
- `USER_SERVICE_GRPC_PORT`: gRPC port (default: 3013)
- `DEFAULT_USER_ROLE`: Default role for new users
- `MAX_LOGIN_ATTEMPTS`: Maximum failed login attempts
- `LOCKOUT_DURATION_MINUTES`: Account lockout duration

## 📊 Database Schema

The service uses PostgreSQL with Prisma ORM. Key tables:

- `users`: User profiles and authentication data
- `user_sessions`: User session management

## 🔄 Events Published

- `user.created`: When a new user is created
- `user.updated`: When user information is updated
- `user.login`: When user logs in successfully
- `user.status.changed`: When user status changes
- `user.locked`: When user account is locked
- `user.unlocked`: When user account is unlocked

## 🏗️ Architecture Notes

This service follows the new standardized folder structure with:

- **Separation of Concerns**: Clear separation between modules, config, core logic, etc.
- **Scalable Organization**: Easy to add new modules and features
- **Testable Design**: Clear dependencies and interfaces for testing
- **Event-driven**: Publishes events for other services to consume
- **Multi-tenant Ready**: Built-in tenant context and isolation
