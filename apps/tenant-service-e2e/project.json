{"name": "tenant-service-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["tenant-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/tenant-service-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["tenant-service:build", "tenant-service:serve"]}}}