{"name": "surveillance-service", "$schema": "../..node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/surveillance-service/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["--node-env=production"]}, "configurations": {"development": {"args": ["--node-env=development"]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "surveillance-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "surveillance-service:build:development"}, "production": {"buildTarget": "surveillance-service:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}