{"name": "transaction-service-e2e", "$schema": "../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["transaction-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "transaction-service-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["transaction-service:build", "transaction-service:serve"]}}}