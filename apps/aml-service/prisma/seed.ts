import { createId } from '@paralleldrive/cuid2';
import { PrismaClient } from '../../../node_modules/.prisma/aml-client/index.js';

const prisma = new PrismaClient();

// CUID generation functions for seed
function generateRiskRuleId(): string {
  return `rru_${createId()}`;
}

function generateAmlConfigurationId(): string {
  return `acf_${createId()}`;
}

/**
 * AML Service Database Seeding
 *
 * Seeds the AML database with initial risk rules, configurations,
 * and sample data for development and testing.
 */
async function main() {
  console.log('🌱 Starting AML service database seeding...');

  // Default tenant for development
  const defaultTenantId = 'tnt_development_default';

  try {
    // 1. Seed Risk Rules
    console.log('📋 Seeding risk rules...');
    await seedRiskRules(defaultTenantId);

    // 2. Seed AML Configurations
    console.log('⚙️ Seeding AML configurations...');
    await seedAMLConfigurations(defaultTenantId);

    console.log('✅ AML service database seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error seeding AML database:', error);
    throw error;
  }
}

/**
 * Seed default risk rules
 */
async function seedRiskRules(tenantId: string) {
  const riskRules = [
    {
      id: generateRiskRuleId(),
      tenantId,
      name: 'High Amount Transaction',
      description: 'Flag transactions above $100,000',
      ruleType: 'AMOUNT_THRESHOLD',
      severity: 'HIGH',
      status: 'ACTIVE',
      threshold: 100000,
      parameters: { currency: 'USD', timeWindow: 24 },
      conditions: { minAmount: 100000, excludeInternalTransfers: false },
      version: 1,
      isActive: true,
    },
    {
      id: generateRiskRuleId(),
      tenantId,
      name: 'Critical Amount Transaction',
      description: 'Block transactions above $1,000,000',
      ruleType: 'AMOUNT_THRESHOLD',
      severity: 'CRITICAL',
      status: 'ACTIVE',
      threshold: 1000000,
      parameters: { currency: 'USD', requireManualReview: true },
      conditions: { minAmount: 1000000, blockTransaction: true },
      version: 1,
      isActive: true,
    },
    {
      id: generateRiskRuleId(),
      tenantId,
      name: 'High Velocity Check',
      description: 'Flag customers with more than 10 transactions in 24 hours',
      ruleType: 'VELOCITY_CHECK',
      severity: 'MEDIUM',
      status: 'ACTIVE',
      threshold: 10,
      parameters: { timeWindow: 24, maxTransactions: 10, maxAmount: 500000 },
      conditions: { includeFailedTransactions: false, countByCustomer: true },
      version: 1,
      isActive: true,
    },
  ];

  for (const rule of riskRules) {
    await prisma.riskRule.upsert({
      where: { id: rule.id },
      update: rule as any,
      create: rule as any,
    });
  }

  console.log(`✅ Created ${riskRules.length} risk rules`);
}

/**
 * Seed AML configurations
 */
async function seedAMLConfigurations(tenantId: string) {
  const configurations = [
    {
      id: generateAmlConfigurationId(),
      tenantId,
      key: 'RISK_THRESHOLDS',
      value: {
        lowRisk: { min: 0, max: 25 },
        mediumRisk: { min: 26, max: 50 },
        highRisk: { min: 51, max: 75 },
        criticalRisk: { min: 76, max: 100 },
      },
      description: 'Risk score thresholds for categorization',
      isActive: true,
    },
    {
      id: generateAmlConfigurationId(),
      tenantId,
      key: 'SYSTEM_SETTINGS',
      value: {
        enableRealTimeEvaluation: true,
        batchProcessingEnabled: true,
        maxConcurrentEvaluations: 100,
        evaluationTimeoutMs: 30000,
        enableAuditLogging: true,
        logLevel: 'INFO',
        enableMetrics: true,
        healthCheckIntervalMs: 60000,
      },
      description: 'System performance and monitoring settings',
      isActive: true,
    },
  ];

  for (const config of configurations) {
    await prisma.aMLConfiguration.upsert({
      where: { id: config.id },
      update: config,
      create: config,
    });
  }

  console.log(`✅ Created ${configurations.length} AML configurations`);
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
