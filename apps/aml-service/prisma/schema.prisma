generator client {
  provider = "prisma-client-js"
  output   = "../../../node_modules/.prisma/aml-client"
}

datasource db {
  provider = "postgresql"
  url      = env("AML_DATABASE_URL")
}

// AML Service Prisma Schema
// Manages risk rules, evaluations, alerts, and audit trails

enum RiskLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum TransactionStatus {
  APPROVED
  PENDING_REVIEW
  FLAGGED
  BLOCKED
  REJECTED
}

enum AlertType {
  AMOUNT_THRESHOLD
  VELOCITY_CHECK
  TIME_ANOMALY
  GEOGRAPHIC_ANOMALY
  PATTERN_DETECTION
  SANCTIONS_MATCH
  PEP_MATCH
  ADVERSE_MEDIA
  COMPLIANCE
  CUSTOMER_RISK
  UNKNOWN
}

enum AlertSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum RiskRuleType {
  AMOUNT_THRESHOLD
  VELOCITY_CHECK
  TIME_ANOMALY
  GEOGRAPHIC_ANOMALY
  PATTERN_DETECTION
  SANCTIONS_SCREENING
  PEP_SCREENING
  ADVERSE_MEDIA_SCREENING
  COMPLIANCE_CHECK
  CUSTOMER_RISK_ASSESSMENT
  CUSTOM
}

enum CustomerRiskCategory {
  LOW_RISK
  MEDIUM_RISK
  HIGH_RISK
  PROHIBITED
}

enum KycStatus {
  NOT_STARTED
  IN_PROGRESS
  PENDING_REVIEW
  VERIFIED
  REJECTED
  EXPIRED
}

enum CustomerType {
  INDIVIDUAL
  BUSINESS
  GOVERNMENT
  NON_PROFIT
  FINANCIAL_INSTITUTION
}

enum AccountType {
  CHECKING
  SAVINGS
  BUSINESS
  INVESTMENT
  TRUST
  ESCROW
}

enum PatternType {
  ROUND_NUMBER
  STRUCTURED_TRANSACTION
  RAPID_SUCCESSION
  UNUSUAL_TIMING
  CROSS_BORDER
  ESCALATING_AMOUNTS
  CONSISTENT_ROUND_NUMBERS
  WEEKEND_BUSINESS
}

enum SanctionsListType {
  OFAC_SDN
  OFAC_CONSOLIDATED
  EU_SANCTIONS
  UN_SANCTIONS
  UK_SANCTIONS
  CUSTOM
}

enum SanctionsMatchType {
  EXACT
  FUZZY
  PHONETIC
  ALIAS
}

enum PepCategory {
  DOMESTIC_PEP
  FOREIGN_PEP
  INTERNATIONAL_ORGANIZATION
  FAMILY_MEMBER
  CLOSE_ASSOCIATE
}

enum AuditActionType {
  RULE_CREATED
  RULE_UPDATED
  RULE_DELETED
  RULE_ACTIVATED
  RULE_DEACTIVATED
  EVALUATION_PERFORMED
  ALERT_GENERATED
  ALERT_RESOLVED
  CUSTOMER_RISK_UPDATED
  CONFIGURATION_CHANGED
  SANCTIONS_LIST_UPDATED
  SYSTEM_MAINTENANCE
}

enum ConfigurationType {
  RISK_THRESHOLDS
  ALERT_SETTINGS
  SANCTIONS_SETTINGS
  REPORTING_SETTINGS
  SYSTEM_SETTINGS
  INTEGRATION_SETTINGS
}

enum ReviewStatus {
  PENDING
  IN_REVIEW
  APPROVED
  REJECTED
  ESCALATED
  CLOSED
}

enum RetentionPeriod {
  ONE_YEAR
  THREE_YEARS
  FIVE_YEARS
  SEVEN_YEARS
  TEN_YEARS
  INDEFINITE
}

enum MonitoringFrequency {
  REAL_TIME
  DAILY
  WEEKLY
  MONTHLY
  QUARTERLY
  ANNUALLY
}

enum RuleExecutionStatus {
  SUCCESS
  FAILED
  SKIPPED
  TIMEOUT
  ERROR
}

enum RuleStatus {
  ACTIVE
  INACTIVE
  DRAFT
  ARCHIVED
}

// ============================================================================
// RISK RULES MANAGEMENT
// ============================================================================

model RiskRule {
  id          String     @id @default(cuid()) @db.VarChar(35)
  tenantId    String     @map("tenant_id") @db.VarChar(35)
  name        String     @db.VarChar(255)
  description String?    @db.Text
  ruleType    RiskRuleType  @map("rule_type")
  severity    AlertSeverity
  status      RuleStatus @default(ACTIVE)
  
  // Rule configuration
  threshold   Float?
  parameters  Json?      @db.JsonB
  conditions  Json?      @db.JsonB
  
  // Metadata
  version     Int        @default(1)
  isActive    Boolean    @default(true) @map("is_active")
  createdBy   String?    @map("created_by") @db.VarChar(35)
  updatedBy   String?    @map("updated_by") @db.VarChar(35)
  createdAt   DateTime   @default(now()) @map("created_at")
  updatedAt   DateTime   @updatedAt @map("updated_at")

  // Relations
  evaluations RiskEvaluation[]
  alerts      RiskAlert[]

  @@index([tenantId])
  @@index([ruleType])
  @@index([status])
  @@index([isActive])
  @@map("risk_rules")
}

// ============================================================================
// RISK EVALUATIONS
// ============================================================================

model RiskEvaluation {
  id                String            @id @default(cuid()) @db.VarChar(35)
  tenantId          String            @map("tenant_id") @db.VarChar(35)
  transactionId     String?           @map("transaction_id") @db.VarChar(35)
  customerId        String?           @map("customer_id") @db.VarChar(35)
  
  // Evaluation results
  riskScore         Float             @map("risk_score")
  riskLevel         RiskLevel         @map("risk_level")
  recommendedStatus TransactionStatus @map("recommended_status")
  
  // Evaluation context
  evaluationType    String            @map("evaluation_type") @db.VarChar(50) // 'transaction', 'customer', 'periodic'
  evaluationModel   String?           @map("evaluation_model") @db.VarChar(100)
  riskFactors       Json?             @map("risk_factors") @db.JsonB
  
  // Processing metadata
  processingTime    Int               @map("processing_time") // milliseconds
  evaluatedAt       DateTime          @default(now()) @map("evaluated_at")
  
  // Relations
  alerts            RiskAlert[]
  auditLogs         AuditLog[]
  riskRules         RiskRule[] 


  @@index([tenantId])
  @@index([transactionId])
  @@index([customerId])
  @@index([riskLevel])
  @@index([evaluatedAt])
  @@map("risk_evaluations")

}

// ============================================================================
// RISK ALERTS
// ============================================================================

model RiskAlert {
  id               String        @id @default(cuid()) @db.VarChar(35)
  evaluationId     String        @map("evaluation_id") @db.VarChar(35)
  ruleId           String?       @map("rule_id") @db.VarChar(35)
  
  // Alert details
  alertType        AlertType     @map("alert_type")
  severity         AlertSeverity
  message          String        @db.Text
  confidence       Float?        // 0.0 to 1.0
  
  // Alert metadata
  details          Json?         @db.JsonB
  createdAt        DateTime      @default(now()) @map("created_at")

  // Relations
  evaluation       RiskEvaluation @relation(fields: [evaluationId], references: [id], onDelete: Cascade)
  rule             RiskRule?      @relation(fields: [ruleId], references: [id])

  @@index([evaluationId])
  @@index([ruleId])
  @@index([alertType])
  @@index([severity])
  @@map("risk_alerts")
}

// ============================================================================
// CUSTOMER RISK PROFILES
// ============================================================================

model CustomerRiskProfile {
  id                String               @id @default(cuid()) @db.VarChar(35)
  tenantId          String               @map("tenant_id") @db.VarChar(35)
  customerId        String               @unique @map("customer_id") @db.VarChar(35)
  
  // Risk assessment
  riskCategory      CustomerRiskCategory @map("risk_category")
  overallRiskScore  Float                @map("overall_risk_score")
  riskReason        String?              @map("risk_reason") @db.Text
  riskFactors       Json?                @map("risk_factors") @db.JsonB
  
  // Profile metadata
  lastEvaluated     DateTime             @map("last_evaluated")
  nextReviewDate    DateTime?            @map("next_review_date")
  createdAt         DateTime             @default(now()) @map("created_at")
  updatedAt         DateTime             @updatedAt @map("updated_at")

  @@index([tenantId])
  @@index([customerId])
  @@index([riskCategory])
  @@index([lastEvaluated])
  @@map("customer_risk_profiles")
}

// ============================================================================
// AUDIT LOGS
// ============================================================================

model AuditLog {
  id             String         @id @default(cuid()) @db.VarChar(35)
  tenantId       String         @map("tenant_id") @db.VarChar(35)
  evaluationId   String?        @map("evaluation_id") @db.VarChar(35)
  
  // Audit details
  action         String         @db.VarChar(100)
  entityType     String         @map("entity_type") @db.VarChar(50)
  entityId       String?        @map("entity_id") @db.VarChar(35)
  
  // Context
  userId         String?        @map("user_id") @db.VarChar(35)
  ipAddress      String?        @map("ip_address") @db.VarChar(45)
  userAgent      String?        @map("user_agent") @db.Text
  
  // Data
  oldValues      Json?          @map("old_values") @db.JsonB
  newValues      Json?          @map("new_values") @db.JsonB
  metadata       Json?          @db.JsonB
  
  // Timestamp
  createdAt      DateTime       @default(now()) @map("created_at")

  // Relations
  evaluation     RiskEvaluation? @relation(fields: [evaluationId], references: [id])

  @@index([tenantId])
  @@index([evaluationId])
  @@index([action])
  @@index([entityType])
  @@index([createdAt])
  @@map("audit_logs")
}

// ============================================================================
// SYSTEM CONFIGURATION
// ============================================================================

model AMLConfiguration {
  id          String   @id @default(cuid()) @db.VarChar(35)
  tenantId    String   @map("tenant_id") @db.VarChar(35)
  key         String   @db.VarChar(100)
  value       Json     @db.JsonB
  description String?  @db.Text
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@unique([tenantId, key])
  @@index([tenantId])
  @@index([key])
  @@map("aml_configurations")
}
