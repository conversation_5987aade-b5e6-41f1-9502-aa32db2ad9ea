import { CanActivate, ExecutionContext, Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@qeep/common';

/**
 * API Key Authentication Guard for AML Service
 * 
 * Validates API keys and extracts tenant information for REST endpoints.
 * This guard ensures that only authorized tenants can access AML functionality.
 */
@Injectable()
export class ApiKeyAuthGuard implements CanActivate {
  private readonly logger = new Logger(ApiKeyAuthGuard.name);

  constructor(private readonly configService: ConfigService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const apiKey = this.extractApiKey(request);

    if (!apiKey) {
      this.logger.warn('API key missing from request');
      throw new UnauthorizedException('API key is required');
    }

    try {
      // Validate API key and extract tenant ID
      const tenantId = await this.validateApiKey(apiKey);
      
      if (!tenantId) {
        this.logger.warn(`Invalid API key provided: ${apiKey.substring(0, 8)}...`);
        throw new UnauthorizedException('Invalid API key');
      }

      // Attach tenant ID to request for use in controllers
      request.tenantId = tenantId;
      
      this.logger.debug(`API key validated for tenant: ${tenantId}`);
      return true;

    } catch (error) {
      this.logger.error('API key validation failed:', error);
      throw new UnauthorizedException('API key validation failed');
    }
  }

  /**
   * Extract API key from request headers
   */
  private extractApiKey(request: any): string | null {
    // Check Authorization header (Bearer token)
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Check X-API-Key header
    const apiKeyHeader = request.headers['x-api-key'];
    if (apiKeyHeader) {
      return apiKeyHeader;
    }

    return null;
  }

  /**
   * Validate API key and return tenant ID
   * 
   * In production, this would validate against a database or external service.
   * For development, we use a simple mapping.
   */
  private async validateApiKey(apiKey: string): Promise<string | null> {
    // Development/testing API keys
    const developmentKeys = {
      'aml_dev_key_tenant_1': 'tnt_development_default',
      'aml_dev_key_tenant_2': 'tnt_development_secondary',
      'aml_test_key': 'tnt_test_default',
    };

    // Check development keys first
    if (developmentKeys[apiKey]) {
      return developmentKeys[apiKey];
    }

    // In production, validate against tenant service or database
    // For now, return null for unknown keys
    return null;
  }
}
