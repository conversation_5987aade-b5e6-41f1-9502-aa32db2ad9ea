import { Injectable, Logger } from '@nestjs/common';
import { AML } from '@qeep/contracts';

@Injectable()
export class PatternDetectionService implements AML.Interfaces.IPatternDetectionEngine {
  private readonly logger = new Logger(PatternDetectionService.name);

  /**
   * Detect suspicious patterns in transaction
   */
  async detectPatterns(
    tenantId: string,
    transaction: AML.DTOs.TransactionContextDto,
    historicalData?: any[]
  ): Promise<AML.Interfaces.ITransactionPattern[]> {
    const patterns: AML.Interfaces.ITransactionPattern[] = [];

    try {
      // 1. Round number pattern detection
      const roundNumberPattern = this.detectRoundNumbers(transaction);
      if (roundNumberPattern) {
        patterns.push(roundNumberPattern);
      }

      // 2. Unusual timing pattern
      const timingPattern = this.detectUnusualTiming(transaction);
      if (timingPattern) {
        patterns.push(timingPattern);
      }

      // 3. Structured transaction pattern
      const structuredPattern = this.detectStructuredTransactions(transaction);
      if (structuredPattern) {
        patterns.push(structuredPattern);
      }

      // 4. Rapid succession pattern
      const rapidPattern = this.detectRapidSuccession(transaction, historicalData);
      if (rapidPattern) {
        patterns.push(rapidPattern);
      }

      // 5. Cross-border pattern
      const crossBorderPattern = this.detectCrossBorderPattern(transaction);
      if (crossBorderPattern) {
        patterns.push(crossBorderPattern);
      }

      this.logger.debug(`Detected ${patterns.length} patterns for transaction ${transaction.transactionId}`);
      return patterns;

    } catch (error) {
      this.logger.error(`Pattern detection failed for transaction ${transaction.transactionId}:`, error);
      return [];
    }
  }

  /**
   * Analyze velocity patterns
   */
  async analyzeVelocity(
    tenantId: string,
    customerId: string,
    timeWindow: number
  ): Promise<{
    transactionCount: number;
    totalAmount: number;
    averageAmount: number;
    riskScore: number;
  }> {
    // Mock implementation - in real scenario, this would query transaction history
    // For demonstration, return mock data based on customer ID pattern
    
    const mockTransactionCount = this.getMockTransactionCount(customerId);
    const mockTotalAmount = this.getMockTotalAmount(customerId);
    const averageAmount = mockTransactionCount > 0 ? mockTotalAmount / mockTransactionCount : 0;
    
    // Calculate risk score based on velocity
    let riskScore = 0;
    
    if (mockTransactionCount > 50) {
      riskScore += 40;
    } else if (mockTransactionCount > 20) {
      riskScore += 20;
    } else if (mockTransactionCount > 10) {
      riskScore += 10;
    }

    if (mockTotalAmount > 1000000) {
      riskScore += 30;
    } else if (mockTotalAmount > 500000) {
      riskScore += 15;
    }

    return {
      transactionCount: mockTransactionCount,
      totalAmount: mockTotalAmount,
      averageAmount,
      riskScore: Math.min(riskScore, 100),
    };
  }

  /**
   * Check for geographic anomalies
   */
  async checkGeographicAnomalies(
    tenantId: string,
    customerId: string,
    currentLocation: string
  ): Promise<{
    isAnomalous: boolean;
    riskScore: number;
    reason?: string;
  }> {
    // Mock implementation - in real scenario, this would analyze historical locations
    const highRiskCountries = ['CN', 'RU', 'IR', 'KP', 'AF', 'SY'];
    const mediumRiskCountries = ['PK', 'BD', 'MM', 'LK'];

    if (highRiskCountries.includes(currentLocation)) {
      return {
        isAnomalous: true,
        riskScore: 70,
        reason: 'Transaction from high-risk jurisdiction',
      };
    }

    if (mediumRiskCountries.includes(currentLocation)) {
      return {
        isAnomalous: true,
        riskScore: 35,
        reason: 'Transaction from medium-risk jurisdiction',
      };
    }

    // Check for unusual location changes (mock)
    const isUnusualLocation = this.isUnusualLocationForCustomer(customerId, currentLocation);
    if (isUnusualLocation) {
      return {
        isAnomalous: true,
        riskScore: 25,
        reason: 'Transaction from unusual location for customer',
      };
    }

    return {
      isAnomalous: false,
      riskScore: 0,
    };
  }

  // ============================================================================
  // PRIVATE PATTERN DETECTION METHODS
  // ============================================================================

  private detectRoundNumbers(transaction: AML.DTOs.TransactionContextDto): AML.Interfaces.ITransactionPattern | null {
    const amount = transaction.amount;
    
    // Check if amount is a round number (ends in multiple zeros)
    if (amount >= 1000 && amount % 1000 === 0) {
      const confidence = amount >= 10000 ? 0.8 : 0.6;
      const riskScore = amount >= 50000 ? 25 : 15;

      return {
        patternType: 'round_number',
        confidence,
        description: `Transaction amount is a round number: $${amount.toLocaleString()}`,
        riskScore,
        metadata: {
          amount,
          roundingFactor: this.getRoundingFactor(amount),
        },
      };
    }

    return null;
  }

  private detectUnusualTiming(transaction: AML.DTOs.TransactionContextDto): AML.Interfaces.ITransactionPattern | null {
    const transactionDate = new Date(transaction.timestamp);
    const hour = transactionDate.getHours();
    const dayOfWeek = transactionDate.getDay(); // 0 = Sunday, 6 = Saturday

    // Check for unusual hours (late night/early morning)
    if (hour < 6 || hour > 22) {
      return {
        patternType: 'unusual_timing',
        confidence: 0.7,
        description: `Transaction at unusual hour: ${hour}:00`,
        riskScore: 15,
        metadata: {
          hour,
          dayOfWeek,
          timestamp: transaction.timestamp,
        },
      };
    }

    // Check for weekend transactions (if business account)
    if ((dayOfWeek === 0 || dayOfWeek === 6) && transaction.metadata?.accountType === 'business') {
      return {
        patternType: 'weekend_business',
        confidence: 0.6,
        description: 'Business transaction on weekend',
        riskScore: 10,
        metadata: {
          dayOfWeek,
          accountType: transaction.metadata.accountType,
        },
      };
    }

    return null;
  }

  private detectStructuredTransactions(transaction: AML.DTOs.TransactionContextDto): AML.Interfaces.ITransactionPattern | null {
    const amount = transaction.amount;
    
    // Check for amounts just below reporting thresholds
    const reportingThresholds = [10000, 5000, 3000];
    
    for (const threshold of reportingThresholds) {
      const margin = threshold * 0.05; // 5% margin
      if (amount > (threshold - margin) && amount < threshold) {
        return {
          patternType: 'structured_transaction',
          confidence: 0.85,
          description: `Transaction amount $${amount.toLocaleString()} appears structured to avoid $${threshold.toLocaleString()} threshold`,
          riskScore: 40,
          metadata: {
            amount,
            threshold,
            margin,
            difference: threshold - amount,
          },
        };
      }
    }

    return null;
  }

  private detectRapidSuccession(
    transaction: AML.DTOs.TransactionContextDto,
    historicalData?: any[]
  ): AML.Interfaces.ITransactionPattern | null {
    // Mock implementation - in real scenario, analyze recent transactions
    if (!historicalData || historicalData.length === 0) {
      return null;
    }

    // Check for multiple transactions in short time period
    const recentTransactions = historicalData.filter(tx => {
      const timeDiff = new Date(transaction.timestamp).getTime() - new Date(tx.timestamp).getTime();
      return timeDiff < 3600000; // 1 hour
    });

    if (recentTransactions.length >= 3) {
      return {
        patternType: 'rapid_succession',
        confidence: 0.75,
        description: `${recentTransactions.length + 1} transactions within 1 hour`,
        riskScore: 30,
        metadata: {
          transactionCount: recentTransactions.length + 1,
          timeWindow: '1 hour',
        },
      };
    }

    return null;
  }

  private detectCrossBorderPattern(transaction: AML.DTOs.TransactionContextDto): AML.Interfaces.ITransactionPattern | null {
    // Check if transaction involves multiple countries
    const countries = [
      transaction.countryCode,
      transaction.metadata?.senderCountry,
      transaction.metadata?.recipientCountry,
    ].filter(Boolean);

    const uniqueCountries = [...new Set(countries)];
    
    if (uniqueCountries.length > 1) {
      const riskScore = uniqueCountries.length > 2 ? 20 : 10;
      
      return {
        patternType: 'cross_border',
        confidence: 0.8,
        description: `Cross-border transaction involving ${uniqueCountries.length} countries`,
        riskScore,
        metadata: {
          countries: uniqueCountries,
          countryCount: uniqueCountries.length,
        },
      };
    }

    return null;
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  private getRoundingFactor(amount: number): number {
    if (amount % 100000 === 0) return 100000;
    if (amount % 50000 === 0) return 50000;
    if (amount % 10000 === 0) return 10000;
    if (amount % 5000 === 0) return 5000;
    if (amount % 1000 === 0) return 1000;
    return 1;
  }

  private getMockTransactionCount(customerId: string): number {
    // Generate mock data based on customer ID hash
    const hash = this.simpleHash(customerId);
    return Math.floor((hash % 100) / 2); // 0-49 transactions
  }

  private getMockTotalAmount(customerId: string): number {
    // Generate mock data based on customer ID hash
    const hash = this.simpleHash(customerId);
    return (hash % 1000000) + 10000; // $10K - $1M
  }

  private isUnusualLocationForCustomer(customerId: string, location: string): boolean {
    // Mock implementation - in real scenario, check against customer's historical locations
    const hash = this.simpleHash(customerId + location);
    return hash % 10 === 0; // 10% chance of unusual location
  }

  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }
}
