import { Injectable, Logger } from '@nestjs/common';
import { AML } from '@qeep/contracts';

@Injectable()
export class HistoricalAnalysisService implements AML.Interfaces.IHistoricalAnalysisEngine {
  private readonly logger = new Logger(HistoricalAnalysisService.name);

  /**
   * Analyze customer transaction history
   */
  async analyzeCustomerHistory(
    tenantId: string,
    customerId: string,
    timeWindow: number
  ): Promise<{
    totalTransactions: number;
    totalAmount: number;
    averageAmount: number;
    patterns: AML.Interfaces.ITransactionPattern[];
    riskScore: number;
  }> {
    try {
      // Mock implementation - in production, this would query transaction database
      const mockHistory = this.generateMockHistory(customerId, timeWindow);
      
      const totalTransactions = mockHistory.transactions.length;
      const totalAmount = mockHistory.transactions.reduce((sum, tx) => sum + tx.amount, 0);
      const averageAmount = totalTransactions > 0 ? totalAmount / totalTransactions : 0;

      // Analyze patterns in historical data
      const patterns = this.analyzeHistoricalPatterns(mockHistory.transactions);
      
      // Calculate risk score based on historical behavior
      const riskScore = this.calculateHistoricalRiskScore(mockHistory, patterns);

      this.logger.debug(
        `Historical analysis for customer ${customerId}: ` +
        `${totalTransactions} transactions, $${totalAmount.toLocaleString()} total, risk score: ${riskScore}`
      );

      return {
        totalTransactions,
        totalAmount,
        averageAmount,
        patterns,
        riskScore,
      };

    } catch (error) {
      this.logger.error(`Historical analysis failed for customer ${customerId}:`, error);
      return {
        totalTransactions: 0,
        totalAmount: 0,
        averageAmount: 0,
        patterns: [],
        riskScore: 0,
      };
    }
  }

  /**
   * Compare transaction against historical patterns
   */
  async compareToHistory(
    tenantId: string,
    transaction: AML.DTOs.TransactionContextDto,
    customerId: string
  ): Promise<{
    isAnomalous: boolean;
    deviationScore: number;
    reasons: string[];
  }> {
    try {
      // Get customer's historical profile
      const history = await this.analyzeCustomerHistory(tenantId, customerId, 90); // 90 days
      
      if (history.totalTransactions === 0) {
        // No history available - new customer is inherently riskier
        return {
          isAnomalous: true,
          deviationScore: 25,
          reasons: ['No transaction history available (new customer)'],
        };
      }

      const reasons: string[] = [];
      let deviationScore = 0;

      // 1. Amount deviation analysis
      const amountDeviation = this.analyzeAmountDeviation(transaction, history);
      if (amountDeviation.isAnomalous) {
        deviationScore += amountDeviation.score;
        reasons.push(amountDeviation.reason);
      }

      // 2. Frequency deviation analysis
      const frequencyDeviation = this.analyzeFrequencyDeviation(transaction, history);
      if (frequencyDeviation.isAnomalous) {
        deviationScore += frequencyDeviation.score;
        reasons.push(frequencyDeviation.reason);
      }

      // 3. Timing pattern deviation
      const timingDeviation = this.analyzeTimingDeviation(transaction, history);
      if (timingDeviation.isAnomalous) {
        deviationScore += timingDeviation.score;
        reasons.push(timingDeviation.reason);
      }

      // 4. Geographic deviation
      const geoDeviation = this.analyzeGeographicDeviation(transaction, history);
      if (geoDeviation.isAnomalous) {
        deviationScore += geoDeviation.score;
        reasons.push(geoDeviation.reason);
      }

      const isAnomalous = deviationScore > 20 || reasons.length > 0;

      this.logger.debug(
        `Historical comparison for transaction ${transaction.transactionId}: ` +
        `anomalous=${isAnomalous}, score=${deviationScore}, reasons=${reasons.length}`
      );

      return {
        isAnomalous,
        deviationScore: Math.min(deviationScore, 100),
        reasons,
      };

    } catch (error) {
      this.logger.error(`Historical comparison failed for transaction ${transaction.transactionId}:`, error);
      return {
        isAnomalous: false,
        deviationScore: 0,
        reasons: [],
      };
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  private generateMockHistory(customerId: string, timeWindow: number): {
    transactions: Array<{
      amount: number;
      timestamp: string;
      countryCode?: string;
      hour: number;
    }>;
    profile: {
      averageAmount: number;
      typicalHours: number[];
      commonCountries: string[];
      transactionFrequency: number; // transactions per day
    };
  } {
    // Generate deterministic mock data based on customer ID
    const hash = this.simpleHash(customerId);
    const transactionCount = Math.floor((hash % 50) + 10); // 10-59 transactions
    
    const transactions = [];
    const baseAmount = (hash % 10000) + 1000; // $1K-$11K base
    const commonCountries = ['US', 'CA', 'GB', 'DE'];
    const typicalHours = [9, 10, 11, 12, 13, 14, 15, 16, 17];

    for (let i = 0; i < transactionCount; i++) {
      const dayOffset = Math.floor(Math.random() * timeWindow);
      const hour = typicalHours[Math.floor(Math.random() * typicalHours.length)];
      const amount = baseAmount * (0.5 + Math.random() * 1.5); // ±50% variation
      
      const timestamp = new Date();
      timestamp.setDate(timestamp.getDate() - dayOffset);
      timestamp.setHours(hour, Math.floor(Math.random() * 60), 0, 0);

      transactions.push({
        amount: Math.round(amount),
        timestamp: timestamp.toISOString(),
        countryCode: commonCountries[Math.floor(Math.random() * commonCountries.length)],
        hour,
      });
    }

    const averageAmount = transactions.reduce((sum, tx) => sum + tx.amount, 0) / transactions.length;
    const transactionFrequency = transactionCount / timeWindow;

    return {
      transactions,
      profile: {
        averageAmount,
        typicalHours,
        commonCountries,
        transactionFrequency,
      },
    };
  }

  private analyzeHistoricalPatterns(transactions: any[]): AML.Interfaces.ITransactionPattern[] {
    const patterns: AML.Interfaces.ITransactionPattern[] = [];

    // Analyze for consistent round numbers
    const roundNumbers = transactions.filter(tx => tx.amount % 1000 === 0);
    if (roundNumbers.length > transactions.length * 0.7) {
      patterns.push({
        patternType: 'consistent_round_numbers',
        confidence: 0.8,
        description: 'Customer consistently uses round number amounts',
        riskScore: 15,
        metadata: {
          roundNumberPercentage: (roundNumbers.length / transactions.length) * 100,
        },
      });
    }

    // Analyze for escalating amounts
    const sortedByDate = transactions.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
    const isEscalating = this.detectEscalatingPattern(sortedByDate);
    if (isEscalating) {
      patterns.push({
        patternType: 'escalating_amounts',
        confidence: 0.75,
        description: 'Transaction amounts show escalating pattern',
        riskScore: 20,
        metadata: {
          trend: 'increasing',
        },
      });
    }

    return patterns;
  }

  private calculateHistoricalRiskScore(history: any, patterns: AML.Interfaces.ITransactionPattern[]): number {
    let riskScore = 0;

    // High frequency risk
    if (history.profile.transactionFrequency > 5) {
      riskScore += 20;
    } else if (history.profile.transactionFrequency > 2) {
      riskScore += 10;
    }

    // High average amount risk
    if (history.profile.averageAmount > 50000) {
      riskScore += 25;
    } else if (history.profile.averageAmount > 20000) {
      riskScore += 15;
    }

    // Pattern-based risk
    riskScore += patterns.reduce((sum, pattern) => sum + pattern.riskScore, 0);

    return Math.min(riskScore, 100);
  }

  private analyzeAmountDeviation(
    transaction: AML.DTOs.TransactionContextDto,
    history: any
  ): { isAnomalous: boolean; score: number; reason: string } {
    const { averageAmount } = history;
    const deviation = Math.abs(transaction.amount - averageAmount) / averageAmount;

    if (deviation > 5.0) { // 500% deviation
      return {
        isAnomalous: true,
        score: 40,
        reason: `Transaction amount $${transaction.amount.toLocaleString()} deviates significantly from average $${averageAmount.toLocaleString()}`,
      };
    } else if (deviation > 2.0) { // 200% deviation
      return {
        isAnomalous: true,
        score: 25,
        reason: `Transaction amount is ${Math.round(deviation * 100)}% above average`,
      };
    }

    return { isAnomalous: false, score: 0, reason: '' };
  }

  private analyzeFrequencyDeviation(
    transaction: AML.DTOs.TransactionContextDto,
    history: any
  ): { isAnomalous: boolean; score: number; reason: string } {
    // Mock frequency analysis - in production, this would analyze recent transaction frequency
    const expectedDaysBetween = 1 / history.profile.transactionFrequency;
    
    if (expectedDaysBetween > 7) {
      // Customer typically transacts less than weekly
      return {
        isAnomalous: true,
        score: 15,
        reason: 'Transaction frequency higher than typical pattern',
      };
    }

    return { isAnomalous: false, score: 0, reason: '' };
  }

  private analyzeTimingDeviation(
    transaction: AML.DTOs.TransactionContextDto,
    history: any
  ): { isAnomalous: boolean; score: number; reason: string } {
    const transactionHour = new Date(transaction.timestamp).getHours();
    const { typicalHours } = history.profile;

    if (!typicalHours.includes(transactionHour)) {
      return {
        isAnomalous: true,
        score: 20,
        reason: `Transaction at ${transactionHour}:00 is outside typical hours (${typicalHours.join(', ')})`,
      };
    }

    return { isAnomalous: false, score: 0, reason: '' };
  }

  private analyzeGeographicDeviation(
    transaction: AML.DTOs.TransactionContextDto,
    history: any
  ): { isAnomalous: boolean; score: number; reason: string } {
    if (!transaction.countryCode) {
      return { isAnomalous: false, score: 0, reason: '' };
    }

    const { commonCountries } = history.profile;
    
    if (!commonCountries.includes(transaction.countryCode)) {
      return {
        isAnomalous: true,
        score: 25,
        reason: `Transaction from ${transaction.countryCode} is outside typical countries (${commonCountries.join(', ')})`,
      };
    }

    return { isAnomalous: false, score: 0, reason: '' };
  }

  private detectEscalatingPattern(sortedTransactions: any[]): boolean {
    if (sortedTransactions.length < 5) return false;

    let increasingCount = 0;
    for (let i = 1; i < sortedTransactions.length; i++) {
      if (sortedTransactions[i].amount > sortedTransactions[i - 1].amount) {
        increasingCount++;
      }
    }

    // If more than 70% of transactions are increasing, consider it escalating
    return increasingCount / (sortedTransactions.length - 1) > 0.7;
  }

  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }
}
