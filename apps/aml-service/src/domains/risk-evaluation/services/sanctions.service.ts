import { Injectable, Logger } from '@nestjs/common';
import { AML } from '@qeep/contracts';

@Injectable()
export class SanctionsService implements AML.Interfaces.ISanctionsEngine {
  private readonly logger = new Logger(SanctionsService.name);

  // Mock sanctions lists - in production, these would be loaded from external sources
  private readonly sanctionedEntities = [
    { name: '<PERSON>', type: 'individual', list: 'OFAC SDN', country: 'IR' },
    { name: 'Evil Corp', type: 'entity', list: 'EU Sanctions', country: 'RU' },
    { name: 'Bad Bank', type: 'entity', list: 'UN Sanctions', country: 'KP' },
  ];

  private readonly pepList = [
    { name: 'Political Person', position: 'Minister', country: 'XX' },
    { name: 'Government Official', position: 'Ambassador', country: 'YY' },
  ];

  /**
   * Check customer against sanctions lists
   */
  async checkSanctions(customer: AML.DTOs.CustomerInfoDto): Promise<AML.Interfaces.ISanctionsMatch[]> {
    const matches: AML.Interfaces.ISanctionsMatch[] = [];

    try {
      // Check individual name
      if (customer.firstName && customer.lastName) {
        const fullName = `${customer.firstName} ${customer.lastName}`.toLowerCase();
        const nameMatches = this.findNameMatches(fullName, 'individual');
        matches.push(...nameMatches);
      }

      // Check business name
      if (customer.businessName) {
        const businessMatches = this.findNameMatches(customer.businessName.toLowerCase(), 'entity');
        matches.push(...businessMatches);
      }

      // Check by nationality/country
      if (customer.nationality || customer.countryOfResidence) {
        const countryMatches = this.findCountryMatches(customer);
        matches.push(...countryMatches);
      }

      this.logger.debug(`Sanctions check for customer ${customer.customerId}: ${matches.length} matches found`);
      return matches;
    } catch (error) {
      this.logger.error(`Sanctions check failed for customer ${customer.customerId}:`, error);
      return [];
    }
  }

  /**
   * Check for PEP (Politically Exposed Person) status
   */
  async checkPEP(customer: AML.DTOs.CustomerInfoDto): Promise<{
    isPEP: boolean;
    confidence: number;
    details?: Record<string, any>;
  }> {
    try {
      // Check if already flagged as PEP
      if (customer.isPoliticallyExposed) {
        return {
          isPEP: true,
          confidence: 1.0,
          details: {
            source: 'customer_data',
            flagged: true,
          },
        };
      }

      // Check against PEP database
      if (customer.firstName && customer.lastName) {
        const fullName = `${customer.firstName} ${customer.lastName}`.toLowerCase();
        const pepMatch = this.findPEPMatch(fullName);

        if (pepMatch) {
          return {
            isPEP: true,
            confidence: 0.85,
            details: {
              source: 'pep_database',
              match: pepMatch,
            },
          };
        }
      }

      // Check business PEP connections
      if (customer.businessName) {
        const businessPEP = this.checkBusinessPEPConnections(customer.businessName);
        if (businessPEP.isPEP) {
          return businessPEP;
        }
      }

      return {
        isPEP: false,
        confidence: 0.95,
      };
    } catch (error) {
      this.logger.error(`PEP check failed for customer ${customer.customerId}:`, error);
      return {
        isPEP: false,
        confidence: 0.5,
      };
    }
  }

  /**
   * Check adverse media
   */
  async checkAdverseMedia(customer: AML.DTOs.CustomerInfoDto): Promise<{
    hasAdverseMedia: boolean;
    riskScore: number;
    articles?: Array<{
      title: string;
      source: string;
      date: string;
      relevance: number;
    }>;
  }> {
    try {
      const articles = await this.searchAdverseMedia(customer);

      if (articles.length === 0) {
        return {
          hasAdverseMedia: false,
          riskScore: 0,
        };
      }

      // Calculate risk score based on articles
      const riskScore = this.calculateAdverseMediaRisk(articles);

      return {
        hasAdverseMedia: true,
        riskScore,
        articles,
      };
    } catch (error) {
      this.logger.error(`Adverse media check failed for customer ${customer.customerId}:`, error);
      return {
        hasAdverseMedia: false,
        riskScore: 0,
      };
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  private findNameMatches(name: string, type: 'individual' | 'entity'): AML.Interfaces.ISanctionsMatch[] {
    const matches: AML.Interfaces.ISanctionsMatch[] = [];

    for (const entity of this.sanctionedEntities) {
      if (entity.type === type) {
        const similarity = this.calculateNameSimilarity(name, entity.name.toLowerCase());

        if (similarity >= 0.8) {
          matches.push({
            listName: entity.list,
            matchType: similarity >= 0.95 ? 'exact' : 'fuzzy',
            confidence: similarity,
            matchedFields: ['name'],
            details: {
              sanctionedName: entity.name,
              customerName: name,
              similarity,
              country: entity.country,
            },
          });
        }
      }
    }

    return matches;
  }

  private findCountryMatches(customer: AML.DTOs.CustomerInfoDto): AML.Interfaces.ISanctionsMatch[] {
    const matches: AML.Interfaces.ISanctionsMatch[] = [];
    const highRiskCountries = ['IR', 'KP', 'SY', 'CU'];

    const customerCountries = [customer.nationality, customer.countryOfResidence].filter(Boolean);

    for (const country of customerCountries) {
      if (highRiskCountries.includes(country!)) {
        matches.push({
          listName: 'High Risk Countries',
          matchType: 'exact',
          confidence: 1.0,
          matchedFields: ['country'],
          details: {
            country,
            riskLevel: 'high',
          },
        });
      }
    }

    return matches;
  }

  private findPEPMatch(name: string): any {
    for (const pep of this.pepList) {
      const similarity = this.calculateNameSimilarity(name, pep.name.toLowerCase());
      if (similarity >= 0.8) {
        return {
          name: pep.name,
          position: pep.position,
          country: pep.country,
          similarity,
        };
      }
    }
    return null;
  }

  private checkBusinessPEPConnections(businessName: string): {
    isPEP: boolean;
    confidence: number;
    details?: Record<string, any>;
  } {
    // Mock implementation - check if business has PEP connections
    const pepKeywords = ['government', 'ministry', 'state', 'public', 'official'];
    const lowerBusinessName = businessName.toLowerCase();

    for (const keyword of pepKeywords) {
      if (lowerBusinessName.includes(keyword)) {
        return {
          isPEP: true,
          confidence: 0.7,
          details: {
            source: 'business_name_analysis',
            keyword,
            businessName,
          },
        };
      }
    }

    return {
      isPEP: false,
      confidence: 0.8,
    };
  }

  private async searchAdverseMedia(customer: AML.DTOs.CustomerInfoDto): Promise<
    Array<{
      title: string;
      source: string;
      date: string;
      relevance: number;
    }>
  > {
    // Mock implementation - in production, this would search news databases
    const mockArticles = [];

    // Simulate finding adverse media for certain names
    const searchTerms = [customer.firstName && customer.lastName ? `${customer.firstName} ${customer.lastName}` : '', customer.businessName || ''].filter(Boolean);

    for (const term of searchTerms) {
      if (term.toLowerCase().includes('bad') || term.toLowerCase().includes('evil')) {
        mockArticles.push({
          title: `Investigation into ${term} reveals financial irregularities`,
          source: 'Financial Times',
          date: '2024-01-15',
          relevance: 0.85,
        });
      }
    }

    return mockArticles;
  }

  private calculateAdverseMediaRisk(articles: Array<{ relevance: number }>): number {
    if (articles.length === 0) return 0;

    // Calculate weighted risk based on article relevance and count
    const totalRelevance = articles.reduce((sum, article) => sum + article.relevance, 0);
    const averageRelevance = totalRelevance / articles.length;

    // Base risk from average relevance (0-50 points)
    let riskScore = averageRelevance * 50;

    // Additional risk from article count (0-30 points)
    const countRisk = Math.min(articles.length * 5, 30);
    riskScore += countRisk;

    // High relevance articles increase risk (0-20 points)
    const highRelevanceArticles = articles.filter((article) => article.relevance > 0.7);

    if (highRelevanceArticles.length > 0) {
      riskScore += Math.min(highRelevanceArticles.length * 10, 20);
    }

    return Math.min(Math.round(riskScore), 100);
  }

  private calculateNameSimilarity(name1: string, name2: string): number {
    // Simple Levenshtein distance-based similarity
    const distance = this.levenshteinDistance(name1, name2);
    const maxLength = Math.max(name1.length, name2.length);

    if (maxLength === 0) return 1.0;

    return 1 - distance / maxLength;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(matrix[i - 1][j - 1] + 1, matrix[i][j - 1] + 1, matrix[i - 1][j] + 1);
        }
      }
    }

    return matrix[str2.length][str1.length];
  }
}
