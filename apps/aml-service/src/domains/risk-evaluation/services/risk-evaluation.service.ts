/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable, Logger } from '@nestjs/common';
import { AlertSeverity, AlertType, RiskLevel, TransactionStatus } from '@prisma/aml-client';
import { AML } from '@qeep/contracts';
import { CustomerRiskProfileRepository } from '../../../repositories/customer-risk-profile.repository';
import { RiskAlertRepository } from '../../../repositories/risk-alert.repository';
import { RiskEvaluationRepository } from '../../../repositories/risk-evaluation.repository';
import { HistoricalAnalysisService } from './historical-analysis.service';
import { PatternDetectionService } from './pattern-detection.service';
import { RiskRulesService } from './risk-rules.service';
import { SanctionsService } from './sanctions.service';

@Injectable()
export class RiskEvaluationService implements AML.Interfaces.IRiskEvaluationEngine {
  private readonly logger = new Logger(RiskEvaluationService.name);

  constructor(
    private readonly riskEvaluationRepository: RiskEvaluationRepository,
    private readonly riskAlertRepository: RiskAlertRepository,
    private readonly customerRiskProfileRepository: CustomerRiskProfileRepository,
    private readonly riskRulesService: RiskRulesService,
    private readonly patternDetectionService: PatternDetectionService,
    private readonly sanctionsService: SanctionsService,
    private readonly historicalAnalysisService: HistoricalAnalysisService,
  ) {}

  /**
   * Evaluate transaction risk
   */
  async evaluateTransactionRisk(
    tenantId: string,
    transaction: AML.DTOs.TransactionContextDto,
    fromAccount: AML.DTOs.AccountInfoDto,
    toAccount: AML.DTOs.AccountInfoDto,
    fromCustomer: AML.DTOs.CustomerInfoDto,
    toCustomer: AML.DTOs.CustomerInfoDto,
    options: {
      includeHistoricalAnalysis?: boolean;
      includePatternDetection?: boolean;
    } = {},
  ): Promise<AML.DTOs.RiskEvaluationDto> {
    const startTime = Date.now();
    const evaluationId = `eval_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    this.logger.log(`Starting risk evaluation for transaction ${transaction.transactionId}`);

    try {
      // Initialize risk assessment
      let totalRiskScore = 0;
      const alerts: AML.DTOs.RiskAlertDto[] = [];
      const riskFactors: Record<string, number> = {};

      // 1. Rule-based evaluation
      const ruleAlerts = await this.riskRulesService.evaluateRules(tenantId, transaction, { fromAccount, toAccount, fromCustomer, toCustomer });
      alerts.push(...ruleAlerts);

      const ruleRiskScore = this.calculateRuleRiskScore(ruleAlerts);
      totalRiskScore += ruleRiskScore;
      riskFactors.rules = ruleRiskScore;

      // 2. Sanctions and watchlist checks
      const [fromSanctionsMatches, toSanctionsMatches] = await Promise.all([this.sanctionsService.checkSanctions(fromCustomer), this.sanctionsService.checkSanctions(toCustomer)]);

      if (fromSanctionsMatches.length > 0 || toSanctionsMatches.length > 0) {
        const sanctionsRiskScore = 90; // High risk for sanctions matches
        totalRiskScore += sanctionsRiskScore;
        riskFactors.sanctions = sanctionsRiskScore;

        alerts.push({
          type: AlertType.SANCTIONS_MATCH,
          severity: AlertSeverity.CRITICAL,
          message: 'Customer matches sanctions list',
          confidence: 0.95,
          details: {
            fromMatches: fromSanctionsMatches,
            toMatches: toSanctionsMatches,
          },
        });
      }

      // 3. PEP checks
      const [fromPEP, toPEP] = await Promise.all([this.sanctionsService.checkPEP(fromCustomer), this.sanctionsService.checkPEP(toCustomer)]);

      if (fromPEP.isPEP || toPEP.isPEP) {
        const pepRiskScore = 30;
        totalRiskScore += pepRiskScore;
        riskFactors.pep = pepRiskScore;

        alerts.push({
          type: AlertType.PEP_MATCH,
          severity: AlertSeverity.MEDIUM,
          message: 'Customer is politically exposed person',
          confidence: Math.max(fromPEP.confidence, toPEP.confidence),
        });
      }

      // 4. Historical analysis (if enabled)
      if (options.includeHistoricalAnalysis) {
        const historicalAnalysis = await this.historicalAnalysisService.compareToHistory(tenantId, transaction, fromCustomer.customerId);

        if (historicalAnalysis.isAnomalous) {
          const historicalRiskScore = historicalAnalysis.deviationScore;
          totalRiskScore += historicalRiskScore;
          riskFactors.historical = historicalRiskScore;

          alerts.push({
            type: AlertType.PATTERN_DETECTION,
            severity: this.getSeverityFromScore(historicalRiskScore),
            message: `Transaction deviates from historical patterns: ${historicalAnalysis.reasons.join(', ')}`,
            confidence: 0.75,
          });
        }
      }

      // 5. Pattern detection (if enabled)
      if (options.includePatternDetection) {
        const patterns = await this.patternDetectionService.detectPatterns(tenantId, transaction);

        for (const pattern of patterns) {
          totalRiskScore += pattern.riskScore;
          riskFactors[`pattern_${pattern.patternType}`] = pattern.riskScore;

          alerts.push({
            type: AlertType.PATTERN_DETECTION,
            severity: this.getSeverityFromScore(pattern.riskScore),
            message: pattern.description,
            confidence: pattern.confidence,
            details: pattern.metadata,
          });
        }
      }

      // 6. Geographic risk assessment
      const geoRisk = await this.assessGeographicRisk(transaction, fromCustomer, toCustomer);
      if (geoRisk.riskScore > 0) {
        totalRiskScore += geoRisk.riskScore;
        riskFactors.geographic = geoRisk.riskScore;

        alerts.push({
          type: AlertType.GEOGRAPHIC_ANOMALY,
          severity: this.getSeverityFromScore(geoRisk.riskScore),
          message: geoRisk.reason || 'Geographic risk detected',
          confidence: 0.7,
        });
      }

      // 7. Velocity checks
      const velocityRisk = await this.assessVelocityRisk(tenantId, fromCustomer.customerId, transaction);
      if (velocityRisk.riskScore > 0) {
        totalRiskScore += velocityRisk.riskScore;
        riskFactors.velocity = velocityRisk.riskScore;

        alerts.push({
          type: AlertType.VELOCITY_CHECK,
          severity: this.getSeverityFromScore(velocityRisk.riskScore),
          message: `High transaction velocity detected: ${velocityRisk.details}`,
          confidence: 0.8,
        });
      }

      // Normalize risk score (cap at 100)
      const finalRiskScore = Math.min(totalRiskScore, 100);
      const riskLevel = this.getRiskLevel(finalRiskScore);
      const recommendedStatus = this.getRecommendedStatus(finalRiskScore, alerts);

      const processingTime = Date.now() - startTime;

      // Store evaluation in database
      const evaluation = await this.riskEvaluationRepository.create({
        tenantId,
        transactionId: transaction.transactionId,
        customerId: fromCustomer.customerId,
        riskScore: finalRiskScore,
        riskLevel,
        recommendedStatus,
        evaluationType: 'transaction',
        evaluationModel: 'qeep-aml-v1',
        riskFactors,
        processingTime,
      });

      // Store alerts
      if (alerts.length > 0) {
        await this.riskAlertRepository.createMany(
          alerts.map((alert) => ({
            evaluationId: evaluation.id,
            alertType: alert.type,
            severity: alert.severity,
            message: alert.message,
            confidence: alert.confidence,
            details: alert.details,
          })),
        );
      }

      this.logger.log(
        `Risk evaluation completed for transaction ${transaction.transactionId}: ` +
          `score=${finalRiskScore}, level=${riskLevel}, status=${recommendedStatus}, time=${processingTime}ms`,
      );

      return {
        riskScore: finalRiskScore,
        riskLevel,
        recommendedStatus,
        alerts,
        evaluationId: evaluation.id,
        evaluatedAt: evaluation.evaluatedAt.toISOString(),
        evaluationModel: 'qeep-aml-v1',
        riskFactors,
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(`Risk evaluation failed for transaction ${transaction.transactionId}:`, error);

      // Return safe fallback evaluation
      return this.createFallbackEvaluation(evaluationId, processingTime, error);
    }
  }

  /**
   * Evaluate customer risk
   */
  async evaluateCustomerRisk(
    tenantId: string,
    customer: AML.DTOs.CustomerInfoDto,
    accounts: AML.DTOs.AccountInfoDto[],
    options: {
      includeTransactionHistory?: boolean;
    } = {},
  ): Promise<AML.DTOs.CustomerRiskProfileDto> {
    this.logger.log(`Evaluating customer risk for ${customer.customerId}`);

    try {
      let totalRiskScore = 0;
      const riskFactors: Record<string, number> = {};
      const alerts: AML.DTOs.RiskAlertDto[] = [];

      // 1. Base customer risk assessment
      const baseRisk = this.assessBaseCustomerRisk(customer);
      totalRiskScore += baseRisk.score;
      riskFactors.base = baseRisk.score;

      // 2. Sanctions and PEP checks
      const [sanctionsMatches, pepCheck] = await Promise.all([this.sanctionsService.checkSanctions(customer), this.sanctionsService.checkPEP(customer)]);

      if (sanctionsMatches.length > 0) {
        totalRiskScore += 90;
        riskFactors.sanctions = 90;
      }

      if (pepCheck.isPEP) {
        totalRiskScore += 30;
        riskFactors.pep = 30;
      }

      // 3. Account risk assessment
      const accountRisk = this.assessAccountRisk(accounts);
      totalRiskScore += accountRisk.score;
      riskFactors.accounts = accountRisk.score;

      // 4. Transaction history analysis (if enabled)
      if (options.includeTransactionHistory) {
        const historyAnalysis = await this.historicalAnalysisService.analyzeCustomerHistory(
          tenantId,
          customer.customerId,
          90, // 90 days
        );

        totalRiskScore += historyAnalysis.riskScore;
        riskFactors.history = historyAnalysis.riskScore;
      }

      const finalRiskScore = Math.min(totalRiskScore, 100);
      const riskCategory = this.getCustomerRiskCategory(finalRiskScore);

      // Update or create customer risk profile
      await this.customerRiskProfileRepository.upsert(
        customer.customerId,
        {
          tenantId,
          customerId: customer.customerId,
          riskCategory,
          overallRiskScore: finalRiskScore,
          riskFactors,
          nextReviewDate: this.calculateNextReviewDate(riskCategory),
        },
        {
          riskCategory,
          overallRiskScore: finalRiskScore,
          riskFactors,
          nextReviewDate: this.calculateNextReviewDate(riskCategory),
        },
      );

      return {
        customerId: customer.customerId,
        riskCategory,
        overallRiskScore: finalRiskScore,
        recentAlerts: alerts,
        lastEvaluated: new Date().toISOString(),
        riskFactors,
        riskReason: this.generateRiskReason(riskFactors),
      };
    } catch (error) {
      this.logger.error(`Customer risk evaluation failed for ${customer.customerId}:`, error);
      throw error;
    }
  }

  /**
   * Get customer risk profile
   */
  async getCustomerRiskProfile(
    tenantId: string,
    customerId: string,
    options: {
      includeRecentAlerts?: boolean;
      historyDays?: number;
    } = {},
  ): Promise<AML.DTOs.CustomerRiskProfileDto | null> {
    const profile = await this.customerRiskProfileRepository.findByCustomerId(customerId);

    if (!profile) {
      return null;
    }

    let recentAlerts: AML.DTOs.RiskAlertDto[] = [];

    if (options.includeRecentAlerts) {
      const hours = (options.historyDays || 30) * 24;
      const alerts = await this.riskAlertRepository.findRecentByTenant(tenantId, hours, 50);

      recentAlerts = alerts
        .filter((alert) => alert.evaluation.customerId === customerId)
        .map((alert) => ({
          type: alert.alertType,
          severity: alert.severity,
          message: alert.message,
          confidence: alert.confidence,
          details: alert.details as Record<string, any>,
        }));
    }

    return {
      customerId: profile.customerId,
      riskCategory: profile.riskCategory,
      overallRiskScore: profile.overallRiskScore,
      recentAlerts,
      lastEvaluated: profile.lastEvaluated.toISOString(),
      riskFactors: profile.riskFactors as Record<string, number>,
      riskReason: profile.riskReason,
    };
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  private calculateRuleRiskScore(alerts: AML.DTOs.RiskAlertDto[]): number {
    return alerts.reduce((total, alert) => {
      switch (alert.severity) {
        case AlertSeverity.CRITICAL:
          return total + 40;
        case AlertSeverity.HIGH:
          return total + 25;
        case AlertSeverity.MEDIUM:
          return total + 15;
        case AlertSeverity.LOW:
          return total + 5;
        default:
          return total;
      }
    }, 0);
  }

  private getSeverityFromScore(score: number): AlertSeverity {
    if (score >= 70) return AlertSeverity.CRITICAL;
    if (score >= 50) return AlertSeverity.HIGH;
    if (score >= 25) return AlertSeverity.MEDIUM;
    return AlertSeverity.LOW;
  }

  private getRiskLevel(score: number): RiskLevel {
    if (score >= 80) return RiskLevel.CRITICAL;
    if (score >= 60) return RiskLevel.HIGH;
    if (score >= 30) return RiskLevel.MEDIUM;
    return RiskLevel.LOW;
  }

  private getRecommendedStatus(score: number, alerts: AML.DTOs.RiskAlertDto[]): TransactionStatus {
    const hasCriticalAlert = alerts.some((alert) => alert.severity === AlertSeverity.CRITICAL);

    if (hasCriticalAlert || score >= 80) {
      return TransactionStatus.BLOCKED;
    }
    if (score >= 40) {
      return TransactionStatus.FLAGGED;
    }
    if (score >= 20) {
      return TransactionStatus.PENDING_REVIEW;
    }
    return TransactionStatus.APPROVED;
  }

  private async assessGeographicRisk(
    transaction: AML.DTOs.TransactionContextDto,
    fromCustomer: AML.DTOs.CustomerInfoDto,
    toCustomer: AML.DTOs.CustomerInfoDto,
  ): Promise<{ riskScore: number; reason?: string }> {
    const highRiskCountries = ['CN', 'RU', 'IR', 'KP', 'AF', 'SY'];
    const mediumRiskCountries = ['PK', 'BD', 'MM', 'LK'];

    let riskScore = 0;
    let reason = '';

    // Check transaction country
    if (transaction.countryCode && highRiskCountries.includes(transaction.countryCode)) {
      riskScore += 30;
      reason = 'Transaction from high-risk jurisdiction';
    } else if (transaction.countryCode && mediumRiskCountries.includes(transaction.countryCode)) {
      riskScore += 15;
      reason = 'Transaction from medium-risk jurisdiction';
    }

    // Check customer countries
    if (fromCustomer.countryOfResidence && highRiskCountries.includes(fromCustomer.countryOfResidence)) {
      riskScore += 20;
      reason += (reason ? '; ' : '') + 'Sender from high-risk country';
    }

    if (toCustomer.countryOfResidence && highRiskCountries.includes(toCustomer.countryOfResidence)) {
      riskScore += 20;
      reason += (reason ? '; ' : '') + 'Recipient from high-risk country';
    }

    return { riskScore, reason: reason || undefined };
  }

  private async assessVelocityRisk(tenantId: string, customerId: string, transaction: AML.DTOs.TransactionContextDto): Promise<{ riskScore: number; details: string }> {
    try {
      const velocityAnalysis = await this.patternDetectionService.analyzeVelocity(
        tenantId,
        customerId,
        24, // 24 hours
      );

      let riskScore = 0;
      const details: string[] = [];

      // Check transaction count
      if (velocityAnalysis.transactionCount > 20) {
        riskScore += 30;
        details.push(`${velocityAnalysis.transactionCount} transactions in 24h`);
      } else if (velocityAnalysis.transactionCount > 10) {
        riskScore += 15;
        details.push(`${velocityAnalysis.transactionCount} transactions in 24h`);
      }

      // Check total amount
      if (velocityAnalysis.totalAmount > 500000) {
        riskScore += 25;
        details.push(`$${velocityAnalysis.totalAmount.toLocaleString()} total in 24h`);
      } else if (velocityAnalysis.totalAmount > 100000) {
        riskScore += 10;
        details.push(`$${velocityAnalysis.totalAmount.toLocaleString()} total in 24h`);
      }

      return { riskScore, details: details.join(', ') };
    } catch (error) {
      this.logger.warn(`Velocity assessment failed for customer ${customerId}:`, error);
      return { riskScore: 0, details: 'Velocity check unavailable' };
    }
  }

  private assessBaseCustomerRisk(customer: AML.DTOs.CustomerInfoDto): { score: number } {
    let score = 0;

    // Customer type risk
    if (customer.customerType === 'business') {
      score += 5; // Businesses have slightly higher base risk
    }

    // KYC status risk
    if (customer.kycStatus !== 'verified') {
      score += 20;
    }

    // Age of KYC
    if (customer.lastKycUpdate) {
      const kycAge = Date.now() - new Date(customer.lastKycUpdate).getTime();
      const daysOld = kycAge / (1000 * 60 * 60 * 24);

      if (daysOld > 365) {
        score += 15; // KYC older than 1 year
      } else if (daysOld > 180) {
        score += 5; // KYC older than 6 months
      }
    }

    return { score };
  }

  private assessAccountRisk(accounts: AML.DTOs.AccountInfoDto[]): { score: number } {
    let score = 0;

    for (const account of accounts) {
      // Account status risk
      if (account.accountStatus !== 'active') {
        score += 10;
      }

      // Account age risk (new accounts are riskier)
      if (account.accountOpenDate) {
        const accountAge = Date.now() - new Date(account.accountOpenDate).getTime();
        const daysOld = accountAge / (1000 * 60 * 60 * 24);

        if (daysOld < 30) {
          score += 15; // Very new account
        } else if (daysOld < 90) {
          score += 5; // Relatively new account
        }
      }
    }

    return { score: Math.min(score, 50) }; // Cap account risk
  }

  private getCustomerRiskCategory(score: number): any {
    if (score >= 80) return 'PROHIBITED';
    if (score >= 60) return 'HIGH';
    if (score >= 30) return 'MEDIUM';
    return 'LOW';
  }

  private calculateNextReviewDate(riskCategory: string): Date {
    const now = new Date();

    switch (riskCategory) {
      case 'PROHIBITED':
        return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days
      case 'HIGH':
        return new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000); // 90 days
      case 'MEDIUM':
        return new Date(now.getTime() + 180 * 24 * 60 * 60 * 1000); // 180 days
      default:
        return new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000); // 365 days
    }
  }

  private generateRiskReason(riskFactors: Record<string, number>): string {
    const reasons: string[] = [];

    Object.entries(riskFactors).forEach(([factor, score]) => {
      if (score > 20) {
        switch (factor) {
          case 'rules':
            reasons.push('Rule violations detected');
            break;
          case 'sanctions':
            reasons.push('Sanctions list match');
            break;
          case 'pep':
            reasons.push('Politically exposed person');
            break;
          case 'historical':
            reasons.push('Unusual transaction pattern');
            break;
          case 'geographic':
            reasons.push('High-risk jurisdiction');
            break;
          case 'velocity':
            reasons.push('High transaction velocity');
            break;
          default:
            if (factor.startsWith('pattern_')) {
              reasons.push('Suspicious pattern detected');
            }
        }
      }
    });

    return reasons.length > 0 ? reasons.join('; ') : 'Standard risk assessment';
  }

  private createFallbackEvaluation(evaluationId: string, processingTime: number, error: any): AML.DTOs.RiskEvaluationDto {
    return {
      riskScore: 50, // Medium risk as fallback
      riskLevel: RiskLevel.MEDIUM,
      recommendedStatus: TransactionStatus.PENDING_REVIEW,
      alerts: [
        {
          type: AlertType.COMPLIANCE,
          severity: AlertSeverity.MEDIUM,
          message: `Risk evaluation service error: ${error.message}`,
          confidence: 0.5,
        },
      ],
      evaluationId,
      evaluatedAt: new Date().toISOString(),
      evaluationModel: 'fallback',
      riskFactors: { error: 50 },
    };
  }
}
