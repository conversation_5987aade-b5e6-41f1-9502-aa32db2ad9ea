import { Injectable, Logger } from '@nestjs/common';
import { AlertType, AlertSeverity } from '@prisma/aml-client';
import { AML } from '@qeep/contracts';
import { RiskRuleRepository } from '../../../repositories/risk-rule.repository';

@Injectable()
export class RiskRulesService implements AML.Interfaces.IRiskRulesEngine {
  private readonly logger = new Logger(RiskRulesService.name);

  constructor(private readonly riskRuleRepository: RiskRuleRepository) {}

  /**
   * Get active rules for tenant
   */
  async getActiveRules(tenantId: string): Promise<AML.Interfaces.IRiskRule[]> {
    const rules = await this.riskRuleRepository.findActiveByTenant(tenantId);
    
    return rules.map(rule => ({
      id: rule.id,
      tenantId: rule.tenantId,
      name: rule.name,
      description: rule.description,
      ruleType: rule.ruleType,
      severity: rule.severity,
      threshold: rule.threshold,
      parameters: rule.parameters as Record<string, any>,
      conditions: rule.conditions as Record<string, any>,
      isActive: rule.isActive,
    }));
  }

  /**
   * Evaluate rules against transaction
   */
  async evaluateRules(
    tenantId: string,
    transaction: AML.DTOs.TransactionContextDto,
    context: {
      fromAccount: AML.DTOs.AccountInfoDto;
      toAccount: AML.DTOs.AccountInfoDto;
      fromCustomer: AML.DTOs.CustomerInfoDto;
      toCustomer: AML.DTOs.CustomerInfoDto;
    }
  ): Promise<AML.DTOs.RiskAlertDto[]> {
    const rules = await this.getActiveRules(tenantId);
    const alerts: AML.DTOs.RiskAlertDto[] = [];

    for (const rule of rules) {
      try {
        const alert = await this.evaluateRule(rule, transaction, context);
        if (alert) {
          alerts.push(alert);
        }
      } catch (error) {
        this.logger.warn(`Failed to evaluate rule ${rule.id}:`, error);
      }
    }

    return alerts;
  }

  /**
   * Update rules
   */
  async updateRules(tenantId: string, rules: Partial<AML.Interfaces.IRiskRule>[]): Promise<number> {
    const updates = rules.map(rule => ({
      id: rule.id!,
      data: {
        name: rule.name,
        description: rule.description,
        severity: rule.severity as AlertSeverity,
        threshold: rule.threshold,
        parameters: rule.parameters,
        conditions: rule.conditions,
        isActive: rule.isActive,
      },
    }));

    return this.riskRuleRepository.updateMany(tenantId, updates);
  }

  /**
   * Validate rule configuration
   */
  async validateRule(rule: Partial<AML.Interfaces.IRiskRule>): Promise<string[]> {
    const errors: string[] = [];

    if (!rule.name || rule.name.trim().length === 0) {
      errors.push('Rule name is required');
    }

    if (!rule.ruleType) {
      errors.push('Rule type is required');
    }

    if (!rule.severity) {
      errors.push('Rule severity is required');
    }

    // Validate rule-specific parameters
    switch (rule.ruleType) {
      case AlertType.AMOUNT_THRESHOLD:
        if (!rule.threshold || rule.threshold <= 0) {
          errors.push('Amount threshold must be greater than 0');
        }
        break;

      case AlertType.VELOCITY_CHECK:
        if (!rule.parameters?.timeWindow || rule.parameters.timeWindow <= 0) {
          errors.push('Velocity check requires a valid time window');
        }
        if (!rule.parameters?.maxTransactions || rule.parameters.maxTransactions <= 0) {
          errors.push('Velocity check requires maximum transaction count');
        }
        break;

      case AlertType.TIME_ANOMALY:
        if (!rule.parameters?.allowedHours || !Array.isArray(rule.parameters.allowedHours)) {
          errors.push('Time anomaly rule requires allowed hours array');
        }
        break;

      case AlertType.GEOGRAPHIC_ANOMALY:
        if (!rule.parameters?.restrictedCountries || !Array.isArray(rule.parameters.restrictedCountries)) {
          errors.push('Geographic anomaly rule requires restricted countries list');
        }
        break;
    }

    return errors;
  }

  /**
   * Evaluate a single rule against transaction
   */
  private async evaluateRule(
    rule: AML.Interfaces.IRiskRule,
    transaction: AML.DTOs.TransactionContextDto,
    context: {
      fromAccount: AML.DTOs.AccountInfoDto;
      toAccount: AML.DTOs.AccountInfoDto;
      fromCustomer: AML.DTOs.CustomerInfoDto;
      toCustomer: AML.DTOs.CustomerInfoDto;
    }
  ): Promise<AML.DTOs.RiskAlertDto | null> {
    switch (rule.ruleType) {
      case AlertType.AMOUNT_THRESHOLD:
        return this.evaluateAmountThreshold(rule, transaction);

      case AlertType.TIME_ANOMALY:
        return this.evaluateTimeAnomaly(rule, transaction);

      case AlertType.GEOGRAPHIC_ANOMALY:
        return this.evaluateGeographicAnomaly(rule, transaction, context);

      case AlertType.VELOCITY_CHECK:
        return this.evaluateVelocityCheck(rule, transaction, context);

      case AlertType.COMPLIANCE:
        return this.evaluateComplianceRule(rule, transaction, context);

      default:
        this.logger.warn(`Unknown rule type: ${rule.ruleType}`);
        return null;
    }
  }

  private evaluateAmountThreshold(
    rule: AML.Interfaces.IRiskRule,
    transaction: AML.DTOs.TransactionContextDto
  ): AML.DTOs.RiskAlertDto | null {
    if (!rule.threshold) return null;

    if (transaction.amount >= rule.threshold) {
      return {
        type: AlertType.AMOUNT_THRESHOLD,
        severity: rule.severity as AlertSeverity,
        message: `Transaction amount $${transaction.amount.toLocaleString()} exceeds threshold $${rule.threshold.toLocaleString()}`,
        ruleId: rule.id,
        ruleName: rule.name,
        confidence: 0.95,
        details: {
          amount: transaction.amount,
          threshold: rule.threshold,
          currency: transaction.currency,
        },
      };
    }

    return null;
  }

  private evaluateTimeAnomaly(
    rule: AML.Interfaces.IRiskRule,
    transaction: AML.DTOs.TransactionContextDto
  ): AML.DTOs.RiskAlertDto | null {
    const allowedHours = rule.parameters?.allowedHours as number[] || [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22];
    const transactionHour = new Date(transaction.timestamp).getHours();

    if (!allowedHours.includes(transactionHour)) {
      return {
        type: AlertType.TIME_ANOMALY,
        severity: rule.severity as AlertSeverity,
        message: `Transaction occurred at ${transactionHour}:00, outside allowed hours`,
        ruleId: rule.id,
        ruleName: rule.name,
        confidence: 0.80,
        details: {
          transactionHour,
          allowedHours,
          timestamp: transaction.timestamp,
        },
      };
    }

    return null;
  }

  private evaluateGeographicAnomaly(
    rule: AML.Interfaces.IRiskRule,
    transaction: AML.DTOs.TransactionContextDto,
    context: {
      fromCustomer: AML.DTOs.CustomerInfoDto;
      toCustomer: AML.DTOs.CustomerInfoDto;
    }
  ): AML.DTOs.RiskAlertDto | null {
    const restrictedCountries = rule.parameters?.restrictedCountries as string[] || [];
    const highRiskCountries = rule.parameters?.highRiskCountries as string[] || ['CN', 'RU', 'IR', 'KP'];

    // Check transaction country
    if (transaction.countryCode && restrictedCountries.includes(transaction.countryCode)) {
      return {
        type: AlertType.GEOGRAPHIC_ANOMALY,
        severity: AlertSeverity.CRITICAL,
        message: `Transaction from restricted country: ${transaction.countryCode}`,
        ruleId: rule.id,
        ruleName: rule.name,
        confidence: 0.95,
        details: {
          country: transaction.countryCode,
          type: 'restricted',
        },
      };
    }

    // Check high-risk countries
    const riskCountries = [
      transaction.countryCode,
      context.fromCustomer.countryOfResidence,
      context.toCustomer.countryOfResidence,
    ].filter(Boolean);

    for (const country of riskCountries) {
      if (highRiskCountries.includes(country!)) {
        return {
          type: AlertType.GEOGRAPHIC_ANOMALY,
          severity: rule.severity as AlertSeverity,
          message: `Transaction involves high-risk country: ${country}`,
          ruleId: rule.id,
          ruleName: rule.name,
          confidence: 0.85,
          details: {
            country,
            type: 'high-risk',
          },
        };
      }
    }

    return null;
  }

  private async evaluateVelocityCheck(
    rule: AML.Interfaces.IRiskRule,
    transaction: AML.DTOs.TransactionContextDto,
    context: {
      fromCustomer: AML.DTOs.CustomerInfoDto;
    }
  ): Promise<AML.DTOs.RiskAlertDto | null> {
    // This would typically query transaction history
    // For now, return null as this requires historical data access
    // In a real implementation, this would check transaction velocity
    
    const timeWindow = rule.parameters?.timeWindow as number || 24; // hours
    const maxTransactions = rule.parameters?.maxTransactions as number || 10;
    const maxAmount = rule.parameters?.maxAmount as number;

    // Mock velocity check - in real implementation, query transaction history
    // const recentTransactions = await this.getRecentTransactions(context.fromCustomer.customerId, timeWindow);
    
    // For now, return null (no velocity violation)
    return null;
  }

  private evaluateComplianceRule(
    rule: AML.Interfaces.IRiskRule,
    transaction: AML.DTOs.TransactionContextDto,
    context: {
      fromCustomer: AML.DTOs.CustomerInfoDto;
      toCustomer: AML.DTOs.CustomerInfoDto;
    }
  ): AML.DTOs.RiskAlertDto | null {
    // Check for basic compliance issues
    const issues: string[] = [];

    // Check customer KYC status
    if (context.fromCustomer.kycStatus !== 'verified') {
      issues.push('Sender KYC not verified');
    }

    if (context.toCustomer.kycStatus !== 'verified') {
      issues.push('Recipient KYC not verified');
    }

    // Check for sanctions flags
    if (context.fromCustomer.isSanctioned || context.toCustomer.isSanctioned) {
      issues.push('Customer on sanctions list');
    }

    if (issues.length > 0) {
      return {
        type: AlertType.COMPLIANCE,
        severity: rule.severity as AlertSeverity,
        message: `Compliance issues detected: ${issues.join(', ')}`,
        ruleId: rule.id,
        ruleName: rule.name,
        confidence: 0.90,
        details: {
          issues,
        },
      };
    }

    return null;
  }
}
