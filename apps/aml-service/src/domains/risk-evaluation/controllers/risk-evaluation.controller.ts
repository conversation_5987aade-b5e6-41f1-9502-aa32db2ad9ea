import { Controller, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { AML } from '@qeep/contracts';
import {
  EvaluateCustomerRiskRequest,
  EvaluateCustomerRiskResponse,
  EvaluateTransactionRiskRequest,
  EvaluateTransactionRiskResponse,
  GetAMLStatusRequest,
  GetAMLStatusResponse,
  GetRiskProfileRequest,
  GetRiskProfileResponse,
  UpdateRiskRulesRequest,
  UpdateRiskRulesResponse,
} from '@qeep/proto';
import { RiskEvaluationService } from '../services/risk-evaluation.service';
import { RiskRulesService } from '../services/risk-rules.service';

/**
 * Risk Evaluation gRPC Controller
 *
 * Provides gRPC endpoints for AML risk evaluation functionality.
 * Works alongside the REST controller using the same service layer.
 */
@Controller()
export class RiskEvaluationGrpcController {
  private readonly logger = new Logger(RiskEvaluationGrpcController.name);

  constructor(private readonly riskEvaluationService: RiskEvaluationService, private readonly riskRulesService: RiskRulesService) {}

  @GrpcMethod('AMLService', 'EvaluateTransactionRisk')
  async evaluateTransactionRisk(request: EvaluateTransactionRiskRequest): Promise<EvaluateTransactionRiskResponse> {
    const startTime = Date.now();
    const requestId = request.metadata?.requestId || `req_${Date.now()}`;

    this.logger.log(`Processing transaction risk evaluation request ${requestId}`);

    try {
      // Transform proto request to internal DTOs
      const transactionDto = AML.DTOs.AMLDtoTransforms.transformProtoToInternal<AML.DTOs.TransactionContextDto>(request.transaction);
      const fromAccountDto = AML.DTOs.AMLDtoTransforms.transformProtoToInternal<AML.DTOs.AccountInfoDto>(request.fromAccount);
      const toAccountDto = AML.DTOs.AMLDtoTransforms.transformProtoToInternal<AML.DTOs.AccountInfoDto>(request.toAccount);
      const fromCustomerDto = AML.DTOs.AMLDtoTransforms.transformProtoToInternal<AML.DTOs.CustomerInfoDto>(request.fromCustomer);
      const toCustomerDto = AML.DTOs.AMLDtoTransforms.transformProtoToInternal<AML.DTOs.CustomerInfoDto>(request.toCustomer);

      // Perform risk evaluation
      const riskEvaluation = await this.riskEvaluationService.evaluateTransactionRisk(
        request.tenantId,
        transactionDto,
        fromAccountDto,
        toAccountDto,
        fromCustomerDto,
        toCustomerDto,
        {
          includeHistoricalAnalysis: request.includeHistoricalAnalysis,
          includePatternDetection: request.includePatternDetection,
        },
      );

      const processingTime = Date.now() - startTime;

      // Transform response back to proto format
      const response: EvaluateTransactionRiskResponse = {
        success: true,
        riskEvaluation: AML.DTOs.AMLDtoTransforms.transformInternalToProto(riskEvaluation),
        message: 'Risk evaluation completed successfully',
        metadata: this.createResponseMetadata(requestId, processingTime),
      };

      this.logger.log(
        `Transaction risk evaluation completed for ${requestId}: ` + `score=${riskEvaluation.riskScore}, status=${riskEvaluation.recommendedStatus}, time=${processingTime}ms`,
      );

      return response;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(`Transaction risk evaluation failed for ${requestId}:`, error);

      return {
        success: false,
        message: 'Risk evaluation failed',
        error: {
          code: 'EVALUATION_ERROR',
          message: error.message,
          details: error.stack,
        },
        metadata: this.createResponseMetadata(requestId, processingTime),
      };
    }
  }

  @GrpcMethod('AMLService', 'EvaluateCustomerRisk')
  async evaluateCustomerRisk(request: EvaluateCustomerRiskRequest): Promise<EvaluateCustomerRiskResponse> {
    const startTime = Date.now();
    const requestId = request.metadata?.requestId || `req_${Date.now()}`;

    this.logger.log(`Processing customer risk evaluation request ${requestId}`);

    try {
      // Transform proto request to internal DTOs
      const customerDto = AML.DTOs.AMLDtoTransforms.transformProtoToInternal<AML.DTOs.CustomerInfoDto>(request.customer);
      const accountsDto = request.accounts.map((account) => AML.DTOs.AMLDtoTransforms.transformProtoToInternal<AML.DTOs.AccountInfoDto>(account));

      // Perform customer risk evaluation
      const riskProfile = await this.riskEvaluationService.evaluateCustomerRisk(request.tenantId, customerDto, accountsDto, {
        includeTransactionHistory: request.includeTransactionHistory,
      });

      const processingTime = Date.now() - startTime;

      // Transform response back to proto format
      const response: EvaluateCustomerRiskResponse = {
        success: true,
        riskProfile: AML.DTOs.AMLDtoTransforms.transformInternalToProto(riskProfile),
        message: 'Customer risk evaluation completed successfully',
        metadata: this.createResponseMetadata(requestId, processingTime),
      };

      this.logger.log(
        `Customer risk evaluation completed for ${requestId}: ` + `category=${riskProfile.riskCategory}, score=${riskProfile.overallRiskScore}, time=${processingTime}ms`,
      );

      return response;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(`Customer risk evaluation failed for ${requestId}:`, error);

      return {
        success: false,
        message: 'Customer risk evaluation failed',
        error: {
          code: 'CUSTOMER_EVALUATION_ERROR',
          message: error.message,
          details: error.stack,
        },
        metadata: this.createResponseMetadata(requestId, processingTime),
      };
    }
  }

  @GrpcMethod('AMLService', 'GetRiskProfile')
  async getRiskProfile(request: GetRiskProfileRequest): Promise<GetRiskProfileResponse> {
    const startTime = Date.now();
    const requestId = request.metadata?.requestId || `req_${Date.now()}`;

    this.logger.log(`Processing get risk profile request ${requestId}`);

    try {
      // Get customer risk profile
      const riskProfile = await this.riskEvaluationService.getCustomerRiskProfile(request.tenantId, request.customerId, {
        includeRecentAlerts: request.includeRecentAlerts,
        historyDays: request.historyDays,
      });

      const processingTime = Date.now() - startTime;

      if (!riskProfile) {
        return {
          success: false,
          message: 'Risk profile not found',
          error: {
            code: 'PROFILE_NOT_FOUND',
            message: `No risk profile found for customer ${request.customerId}`,
          },
          metadata: this.createResponseMetadata(requestId, processingTime),
        };
      }

      // Transform response back to proto format
      const response: GetRiskProfileResponse = {
        success: true,
        riskProfile: AML.DTOs.AMLDtoTransforms.transformInternalToProto(riskProfile),
        message: 'Risk profile retrieved successfully',
        metadata: this.createResponseMetadata(requestId, processingTime),
      };

      this.logger.log(`Risk profile retrieved for ${requestId}: customer=${request.customerId}, time=${processingTime}ms`);

      return response;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(`Get risk profile failed for ${requestId}:`, error);

      return {
        success: false,
        message: 'Failed to retrieve risk profile',
        error: {
          code: 'PROFILE_RETRIEVAL_ERROR',
          message: error.message,
          details: error.stack,
        },
        metadata: this.createResponseMetadata(requestId, processingTime),
      };
    }
  }

  @GrpcMethod('AMLService', 'UpdateRiskRules')
  async updateRiskRules(request: UpdateRiskRulesRequest): Promise<UpdateRiskRulesResponse> {
    const startTime = Date.now();
    const requestId = request.metadata?.requestId || `req_${Date.now()}`;

    this.logger.log(`Processing update risk rules request ${requestId}`);

    try {
      // Validate rules if requested
      if (request.validateOnly) {
        const validationErrors: string[] = [];

        for (const rule of request.rules) {
          const errors = await this.riskRulesService.validateRule(rule);
          validationErrors.push(...errors);
        }

        const processingTime = Date.now() - startTime;

        return {
          success: validationErrors.length === 0,
          rulesUpdated: 0,
          validationErrors,
          message: validationErrors.length === 0 ? 'Rules validation passed' : 'Rules validation failed',
          metadata: this.createResponseMetadata(requestId, processingTime),
        };
      }

      // Update rules
      const rulesUpdated = await this.riskRulesService.updateRules(request.tenantId, request.rules);
      const processingTime = Date.now() - startTime;

      const response: UpdateRiskRulesResponse = {
        success: true,
        rulesUpdated,
        validationErrors: [],
        message: `Successfully updated ${rulesUpdated} rules`,
        metadata: this.createResponseMetadata(requestId, processingTime),
      };

      this.logger.log(`Risk rules updated for ${requestId}: ${rulesUpdated} rules, time=${processingTime}ms`);

      return response;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(`Update risk rules failed for ${requestId}:`, error);

      return {
        success: false,
        rulesUpdated: 0,
        validationErrors: [],
        message: 'Failed to update risk rules',
        error: {
          code: 'RULES_UPDATE_ERROR',
          message: error.message,
          details: error.stack,
        },
        metadata: this.createResponseMetadata(requestId, processingTime),
      };
    }
  }

  @GrpcMethod('AMLService', 'GetAMLStatus')
  async getAMLStatus(request: GetAMLStatusRequest): Promise<GetAMLStatusResponse> {
    const startTime = Date.now();
    const requestId = request.metadata?.requestId || `req_${Date.now()}`;

    this.logger.log(`Processing get AML status request ${requestId}`);

    try {
      // Get service statistics
      const activeRules = await this.riskRulesService.getActiveRules(request.tenantId);
      const processingTime = Date.now() - startTime;

      // Mock some statistics - in production, these would come from monitoring systems
      const status = {
        isHealthy: true,
        version: '1.0.0',
        activeRules: activeRules.length,
        evaluationsToday: Math.floor(Math.random() * 1000) + 100, // Mock data
        averageProcessingTime: 150, // Mock data
        lastRuleUpdate: new Date(),
      };

      const response: GetAMLStatusResponse = {
        success: true,
        status,
        message: 'AML service status retrieved successfully',
        metadata: this.createResponseMetadata(requestId, processingTime),
      };

      this.logger.log(`AML status retrieved for ${requestId}: healthy=${status.isHealthy}, time=${processingTime}ms`);

      return response;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(`Get AML status failed for ${requestId}:`, error);

      return {
        success: false,
        message: 'Failed to retrieve AML status',
        error: {
          code: 'STATUS_ERROR',
          message: error.message,
          details: error.stack,
        },
        metadata: this.createResponseMetadata(requestId, processingTime),
      };
    }
  }

  /**
   * Create response metadata for gRPC responses
   */
  private createResponseMetadata(requestId: string, processingTime: number) {
    return {
      requestId,
      timestamp: new Date(),
      processingTime,
      version: '1.0.0',
      correlationId: requestId,
    };
  }
}
