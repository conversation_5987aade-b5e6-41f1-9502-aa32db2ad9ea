import { Body, Controller, Get, Logger, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ResponseUtil, StandardApiResponse, TenantId, ZodValidationPipe } from '@qeep/common';
import { AML } from '@qeep/contracts';
import { ApiKeyAuthGuard } from '../../../guards/api-key-auth.guard';
import { RiskEvaluationService } from '../services/risk-evaluation.service';
import { RiskRulesService } from '../services/risk-rules.service';

/**
 * Risk Evaluation REST Controller
 * 
 * Provides REST API endpoints for AML risk evaluation functionality.
 * Works alongside the gRPC controller using the same service layer.
 */
@Controller('risk-evaluation')
@UseGuards(ApiKeyAuthGuard)
export class RiskEvaluationRestController {
  private readonly logger = new Logger(RiskEvaluationRestController.name);

  constructor(
    private readonly riskEvaluationService: RiskEvaluationService,
    private readonly riskRulesService: RiskRulesService,
  ) {}

  /**
   * Evaluate transaction risk
   */
  @Post('transactions/evaluate')
  async evaluateTransactionRisk(
    @Body(new ZodValidationPipe(AML.Schemas.EvaluateTransactionRiskRequestSchema))
    request: AML.DTOs.EvaluateTransactionRiskRequestDto,
    @TenantId() tenantId: string,
  ): Promise<StandardApiResponse<AML.DTOs.TransactionRiskEvaluationDto>> {
    const requestId = `rest_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.logger.log(`REST: Evaluating transaction risk for ${request.transaction.transactionId} (${requestId})`);

    try {
      const result = await this.riskEvaluationService.evaluateTransactionRisk(
        tenantId,
        request.transaction,
        request.fromAccount,
        request.toAccount,
        request.fromCustomer,
        request.toCustomer,
        {
          includeHistoricalAnalysis: request.includeHistoricalAnalysis,
          includePatternDetection: request.includePatternDetection,
        }
      );

      this.logger.log(
        `REST: Transaction risk evaluation completed for ${request.transaction.transactionId}: ` +
        `score=${result.riskScore}, status=${result.recommendedStatus}`
      );

      return ResponseUtil.success(result, 'Transaction risk evaluation completed successfully');

    } catch (error) {
      this.logger.error(`REST: Transaction risk evaluation failed for ${request.transaction.transactionId}:`, error);
      return ResponseUtil.error(
        error instanceof Error ? error.message : 'Risk evaluation failed',
        500,
        'RISK_EVALUATION_ERROR',
        { transactionId: request.transaction.transactionId }
      );
    }
  }

  /**
   * Evaluate customer risk
   */
  @Post('customers/evaluate')
  async evaluateCustomerRisk(
    @Body(new ZodValidationPipe(AML.Schemas.EvaluateCustomerRiskRequestSchema))
    request: AML.DTOs.EvaluateCustomerRiskRequestDto,
    @TenantId() tenantId: string,
  ): Promise<StandardApiResponse<AML.DTOs.CustomerRiskProfileDto>> {
    const requestId = `rest_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.logger.log(`REST: Evaluating customer risk for ${request.customer.customerId} (${requestId})`);

    try {
      const result = await this.riskEvaluationService.evaluateCustomerRisk(
        tenantId,
        request.customer,
        request.accounts,
        {
          includeTransactionHistory: request.includeTransactionHistory,
        }
      );

      this.logger.log(
        `REST: Customer risk evaluation completed for ${request.customer.customerId}: ` +
        `category=${result.riskCategory}, score=${result.overallRiskScore}`
      );

      return ResponseUtil.success(result, 'Customer risk evaluation completed successfully');

    } catch (error) {
      this.logger.error(`REST: Customer risk evaluation failed for ${request.customer.customerId}:`, error);
      return ResponseUtil.error(
        error instanceof Error ? error.message : 'Customer risk evaluation failed',
        500,
        'CUSTOMER_RISK_EVALUATION_ERROR',
        { customerId: request.customer.customerId }
      );
    }
  }

  /**
   * Get customer risk profile
   */
  @Get('customers/:customerId/risk-profile')
  async getRiskProfile(
    @Param('customerId') customerId: string,
    @Query('includeRecentAlerts') includeRecentAlerts?: boolean,
    @Query('historyDays') historyDays?: number,
    @TenantId() tenantId: string,
  ): Promise<StandardApiResponse<AML.DTOs.CustomerRiskProfileDto | null>> {
    this.logger.log(`REST: Getting risk profile for customer ${customerId}`);

    try {
      const result = await this.riskEvaluationService.getCustomerRiskProfile(
        tenantId,
        customerId,
        {
          includeRecentAlerts: includeRecentAlerts === true,
          historyDays: historyDays ? parseInt(historyDays.toString()) : undefined,
        }
      );

      if (!result) {
        return ResponseUtil.notFound(`Risk profile not found for customer ${customerId}`);
      }

      return ResponseUtil.success(result, 'Risk profile retrieved successfully');

    } catch (error) {
      this.logger.error(`REST: Failed to get risk profile for customer ${customerId}:`, error);
      return ResponseUtil.error(
        error instanceof Error ? error.message : 'Failed to retrieve risk profile',
        500,
        'RISK_PROFILE_ERROR',
        { customerId }
      );
    }
  }

  /**
   * Get active risk rules
   */
  @Get('rules')
  async getRiskRules(
    @TenantId() tenantId: string,
  ): Promise<StandardApiResponse<AML.Interfaces.IRiskRule[]>> {
    this.logger.log(`REST: Getting active risk rules for tenant ${tenantId}`);

    try {
      const rules = await this.riskRulesService.getActiveRules(tenantId);
      return ResponseUtil.success(rules, `Retrieved ${rules.length} active risk rules`);

    } catch (error) {
      this.logger.error(`REST: Failed to get risk rules for tenant ${tenantId}:`, error);
      return ResponseUtil.error(
        error instanceof Error ? error.message : 'Failed to retrieve risk rules',
        500,
        'RISK_RULES_ERROR'
      );
    }
  }

  /**
   * Update risk rules
   */
  @Put('rules')
  async updateRiskRules(
    @Body(new ZodValidationPipe(AML.Schemas.UpdateRiskRulesRequestSchema))
    request: AML.DTOs.UpdateRiskRulesRequestDto,
    @TenantId() tenantId: string,
  ): Promise<StandardApiResponse<{ rulesUpdated: number; validationErrors?: string[] }>> {
    this.logger.log(`REST: Updating ${request.rules.length} risk rules for tenant ${tenantId}`);

    try {
      // Validate rules if requested
      if (request.validateOnly) {
        const validationErrors: string[] = [];
        
        for (const rule of request.rules) {
          const errors = await this.riskRulesService.validateRule(rule);
          validationErrors.push(...errors);
        }

        return ResponseUtil.success(
          { rulesUpdated: 0, validationErrors },
          validationErrors.length === 0 ? 'Rules validation passed' : 'Rules validation failed'
        );
      }

      // Update rules
      const rulesUpdated = await this.riskRulesService.updateRules(tenantId, request.rules);
      
      return ResponseUtil.success(
        { rulesUpdated },
        `Successfully updated ${rulesUpdated} risk rules`
      );

    } catch (error) {
      this.logger.error(`REST: Failed to update risk rules for tenant ${tenantId}:`, error);
      return ResponseUtil.error(
        error instanceof Error ? error.message : 'Failed to update risk rules',
        500,
        'RISK_RULES_UPDATE_ERROR'
      );
    }
  }

  /**
   * Get AML service status
   */
  @Get('status')
  async getAMLStatus(
    @TenantId() tenantId: string,
  ): Promise<StandardApiResponse<{
    isHealthy: boolean;
    version: string;
    activeRules: number;
    evaluationsToday: number;
    averageProcessingTime: number;
    lastRuleUpdate: string;
  }>> {
    this.logger.log(`REST: Getting AML service status for tenant ${tenantId}`);

    try {
      // Get service statistics
      const activeRules = await this.riskRulesService.getActiveRules(tenantId);

      // Mock some statistics - in production, these would come from monitoring systems
      const status = {
        isHealthy: true,
        version: '1.0.0',
        activeRules: activeRules.length,
        evaluationsToday: Math.floor(Math.random() * 1000) + 100, // Mock data
        averageProcessingTime: 150, // Mock data
        lastRuleUpdate: new Date().toISOString(),
      };

      return ResponseUtil.success(status, 'AML service status retrieved successfully');

    } catch (error) {
      this.logger.error(`REST: Failed to get AML service status for tenant ${tenantId}:`, error);
      return ResponseUtil.error(
        error instanceof Error ? error.message : 'Failed to retrieve AML service status',
        500,
        'AML_STATUS_ERROR'
      );
    }
  }

  /**
   * Health check endpoint
   */
  @Get('health')
  async healthCheck(): Promise<StandardApiResponse<{ status: string; timestamp: string }>> {
    return ResponseUtil.success(
      {
        status: 'healthy',
        timestamp: new Date().toISOString(),
      },
      'AML service is healthy'
    );
  }
}
