import { Module } from '@nestjs/common';
import { AMLPrismaService } from '../../database/prisma.service';
import { CustomerRiskProfileRepository } from '../../repositories/customer-risk-profile.repository';
import { RiskAlertRepository } from '../../repositories/risk-alert.repository';
import { RiskEvaluationRepository } from '../../repositories/risk-evaluation.repository';
import { RiskRuleRepository } from '../../repositories/risk-rule.repository';
import { RiskEvaluationRestController } from './controllers/risk-evaluation-rest.controller';
import { RiskEvaluationGrpcController } from './controllers/risk-evaluation.controller';
import { HistoricalAnalysisService } from './services/historical-analysis.service';
import { PatternDetectionService } from './services/pattern-detection.service';
import { RiskEvaluationService } from './services/risk-evaluation.service';
import { RiskRulesService } from './services/risk-rules.service';
import { SanctionsService } from './services/sanctions.service';

/**
 * Risk Evaluation Module
 *
 * Handles all AML risk evaluation functionality including:
 * - Transaction risk assessment
 * - Customer risk profiling
 * - Rule-based evaluation
 * - Pattern detection
 * - Sanctions screening
 * - Historical analysis
 */
@Module({
  controllers: [RiskEvaluationGrpcController, RiskEvaluationRestController],
  providers: [
    // Core services
    RiskEvaluationService,
    RiskRulesService,
    PatternDetectionService,
    SanctionsService,
    HistoricalAnalysisService,

    // Database services
    AMLPrismaService,

    // Repositories
    RiskRuleRepository,
    RiskEvaluationRepository,
    RiskAlertRepository,
    CustomerRiskProfileRepository,
  ],
  exports: [RiskEvaluationService, RiskRulesService, PatternDetectionService, SanctionsService, HistoricalAnalysisService],
})
export class RiskEvaluationModule {}
