import { Injectable } from '@nestjs/common';
import { RiskAlert, AlertType, AlertSeverity } from '@prisma/aml-client';
import { generateId } from '@qeep/common';
import { AMLPrismaService } from '../database/prisma.service';

export interface CreateRiskAlertData {
  evaluationId: string;
  ruleId?: string;
  alertType: AlertType;
  severity: AlertSeverity;
  message: string;
  confidence?: number;
  details?: Record<string, any>;
}

@Injectable()
export class RiskAlertRepository {
  constructor(private readonly prisma: AMLPrismaService) {}

  /**
   * Create a new risk alert
   */
  async create(data: CreateRiskAlertData): Promise<RiskAlert> {
    return this.prisma.riskAlert.create({
      data: {
        id: generateId('alert'),
        ...data,
      },
      include: {
        rule: true,
        evaluation: true,
      },
    });
  }

  /**
   * Create multiple alerts
   */
  async createMany(alerts: CreateRiskAlertData[]): Promise<number> {
    const alertsWithIds = alerts.map(alert => ({
      id: generateId('alert'),
      ...alert,
    }));

    const result = await this.prisma.riskAlert.createMany({
      data: alertsWithIds,
    });

    return result.count;
  }

  /**
   * Find alert by ID
   */
  async findById(id: string): Promise<RiskAlert | null> {
    return this.prisma.riskAlert.findUnique({
      where: { id },
      include: {
        rule: true,
        evaluation: true,
      },
    });
  }

  /**
   * Find alerts by evaluation ID
   */
  async findByEvaluationId(evaluationId: string): Promise<RiskAlert[]> {
    return this.prisma.riskAlert.findMany({
      where: { evaluationId },
      include: {
        rule: true,
      },
      orderBy: [
        { severity: 'desc' },
        { createdAt: 'desc' },
      ],
    });
  }

  /**
   * Find alerts by rule ID
   */
  async findByRuleId(ruleId: string, limit: number = 100): Promise<RiskAlert[]> {
    return this.prisma.riskAlert.findMany({
      where: { ruleId },
      include: {
        rule: true,
        evaluation: true,
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
    });
  }

  /**
   * Find recent alerts for a tenant
   */
  async findRecentByTenant(
    tenantId: string,
    hours: number = 24,
    limit: number = 100
  ): Promise<RiskAlert[]> {
    const since = new Date();
    since.setHours(since.getHours() - hours);

    return this.prisma.riskAlert.findMany({
      where: {
        evaluation: {
          tenantId,
        },
        createdAt: {
          gte: since,
        },
      },
      include: {
        rule: true,
        evaluation: true,
      },
      orderBy: [
        { severity: 'desc' },
        { createdAt: 'desc' },
      ],
      take: limit,
    });
  }

  /**
   * Find high-severity alerts
   */
  async findHighSeverity(
    tenantId: string,
    severities: AlertSeverity[] = [AlertSeverity.HIGH, AlertSeverity.CRITICAL],
    limit: number = 50
  ): Promise<RiskAlert[]> {
    return this.prisma.riskAlert.findMany({
      where: {
        evaluation: {
          tenantId,
        },
        severity: {
          in: severities,
        },
      },
      include: {
        rule: true,
        evaluation: true,
      },
      orderBy: [
        { severity: 'desc' },
        { createdAt: 'desc' },
      ],
      take: limit,
    });
  }

  /**
   * Find alerts by type
   */
  async findByType(
    tenantId: string,
    alertType: AlertType,
    fromDate?: Date,
    toDate?: Date,
    limit: number = 100
  ): Promise<RiskAlert[]> {
    const whereClause: any = {
      evaluation: {
        tenantId,
      },
      alertType,
    };

    if (fromDate || toDate) {
      whereClause.createdAt = {};
      if (fromDate) whereClause.createdAt.gte = fromDate;
      if (toDate) whereClause.createdAt.lte = toDate;
    }

    return this.prisma.riskAlert.findMany({
      where: whereClause,
      include: {
        rule: true,
        evaluation: true,
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
    });
  }

  /**
   * Get alert statistics
   */
  async getStatistics(
    tenantId: string,
    fromDate?: Date,
    toDate?: Date
  ): Promise<{
    total: number;
    byType: Record<string, number>;
    bySeverity: Record<string, number>;
    byRule: Record<string, number>;
    averageConfidence: number;
  }> {
    const whereClause: any = {
      evaluation: {
        tenantId,
      },
    };

    if (fromDate || toDate) {
      whereClause.createdAt = {};
      if (fromDate) whereClause.createdAt.gte = fromDate;
      if (toDate) whereClause.createdAt.lte = toDate;
    }

    const [
      total,
      byType,
      bySeverity,
      byRule,
      averageConfidence,
    ] = await Promise.all([
      this.prisma.riskAlert.count({ where: whereClause }),
      this.prisma.riskAlert.groupBy({
        by: ['alertType'],
        where: whereClause,
        _count: true,
      }),
      this.prisma.riskAlert.groupBy({
        by: ['severity'],
        where: whereClause,
        _count: true,
      }),
      this.prisma.riskAlert.groupBy({
        by: ['ruleId'],
        where: whereClause,
        _count: true,
      }),
      this.prisma.riskAlert.aggregate({
        where: whereClause,
        _avg: {
          confidence: true,
        },
      }),
    ]);

    return {
      total,
      byType: byType.reduce((acc, item) => {
        acc[item.alertType] = item._count;
        return acc;
      }, {} as Record<string, number>),
      bySeverity: bySeverity.reduce((acc, item) => {
        acc[item.severity] = item._count;
        return acc;
      }, {} as Record<string, number>),
      byRule: byRule.reduce((acc, item) => {
        if (item.ruleId) {
          acc[item.ruleId] = item._count;
        }
        return acc;
      }, {} as Record<string, number>),
      averageConfidence: averageConfidence._avg.confidence || 0,
    };
  }

  /**
   * Get daily alert counts
   */
  async getDailyAlertCounts(
    tenantId: string,
    days: number = 30
  ): Promise<Array<{ date: string; count: number; highSeverityCount: number }>> {
    const fromDate = new Date();
    fromDate.setDate(fromDate.getDate() - days);

    const result = await this.prisma.$queryRaw<Array<{
      date: string;
      count: bigint;
      high_severity_count: bigint;
    }>>`
      SELECT 
        DATE(ra.created_at) as date,
        COUNT(*) as count,
        COUNT(CASE WHEN ra.severity IN ('HIGH', 'CRITICAL') THEN 1 END) as high_severity_count
      FROM risk_alerts ra
      INNER JOIN risk_evaluations re ON ra.evaluation_id = re.id
      WHERE re.tenant_id = ${tenantId}
        AND ra.created_at >= ${fromDate}
      GROUP BY DATE(ra.created_at)
      ORDER BY date DESC
    `;

    return result.map(row => ({
      date: row.date,
      count: Number(row.count),
      highSeverityCount: Number(row.high_severity_count),
    }));
  }

  /**
   * Find alerts that triggered multiple times
   */
  async findFrequentAlerts(
    tenantId: string,
    minOccurrences: number = 5,
    days: number = 7
  ): Promise<Array<{
    ruleId: string;
    alertType: AlertType;
    count: number;
    ruleName?: string;
  }>> {
    const fromDate = new Date();
    fromDate.setDate(fromDate.getDate() - days);

    const result = await this.prisma.riskAlert.groupBy({
      by: ['ruleId', 'alertType'],
      where: {
        evaluation: {
          tenantId,
        },
        createdAt: {
          gte: fromDate,
        },
        ruleId: {
          not: null,
        },
      },
      _count: true,
      having: {
        ruleId: {
          _count: {
            gte: minOccurrences,
          },
        },
      },
      orderBy: {
        _count: {
          ruleId: 'desc',
        },
      },
    });

    // Get rule names
    const ruleIds = result.map(item => item.ruleId).filter(Boolean) as string[];
    const rules = await this.prisma.riskRule.findMany({
      where: {
        id: {
          in: ruleIds,
        },
      },
      select: {
        id: true,
        name: true,
      },
    });

    const ruleMap = new Map(rules.map(rule => [rule.id, rule.name]));

    return result.map(item => ({
      ruleId: item.ruleId!,
      alertType: item.alertType,
      count: item._count,
      ruleName: ruleMap.get(item.ruleId!),
    }));
  }

  /**
   * Delete old alerts (cleanup)
   */
  async deleteOldAlerts(tenantId: string, olderThanDays: number = 365): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const result = await this.prisma.riskAlert.deleteMany({
      where: {
        evaluation: {
          tenantId,
        },
        createdAt: {
          lt: cutoffDate,
        },
      },
    });

    return result.count;
  }
}
