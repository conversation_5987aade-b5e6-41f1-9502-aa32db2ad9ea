import { Injectable } from '@nestjs/common';
import { AlertSeverity, AlertType, RiskRule, RuleStatus } from '@prisma/aml-client';
import { generateCuid } from '@qeep/common';
import { AMLPrismaService } from '../database/prisma.service';

export interface CreateRiskRuleData {
  tenantId: string;
  name: string;
  description?: string;
  ruleType: AlertType;
  severity: AlertSeverity;
  threshold?: number;
  parameters?: Record<string, any>;
  conditions?: Record<string, any>;
  createdBy?: string;
}

export interface UpdateRiskRuleData {
  name?: string;
  description?: string;
  severity?: AlertSeverity;
  threshold?: number;
  parameters?: Record<string, any>;
  conditions?: Record<string, any>;
  status?: RuleStatus;
  isActive?: boolean;
  updatedBy?: string;
}

@Injectable()
export class RiskRuleRepository {
  constructor(private readonly prisma: AMLPrismaService) {}

  /**
   * Create a new risk rule
   */
  async create(data: CreateRiskRuleData): Promise<RiskRule> {
    return this.prisma.riskRule.create({
      data: {
        id: generateCuid('rsk_'),
        ...data,
      },
    });
  }

  /**
   * Find risk rule by ID
   */
  async findById(id: string): Promise<RiskRule | null> {
    return this.prisma.riskRule.findUnique({
      where: { id },
    });
  }

  /**
   * Find all active rules for a tenant
   */
  async findActiveByTenant(tenantId: string): Promise<RiskRule[]> {
    return this.prisma.riskRule.findMany({
      where: {
        tenantId,
        isActive: true,
        status: RuleStatus.ACTIVE,
      },
      orderBy: [{ severity: 'desc' }, { createdAt: 'asc' }],
    });
  }

  /**
   * Find rules by type for a tenant
   */
  async findByTypeAndTenant(tenantId: string, ruleType: AlertType): Promise<RiskRule[]> {
    return this.prisma.riskRule.findMany({
      where: {
        tenantId,
        ruleType,
        isActive: true,
        status: RuleStatus.ACTIVE,
      },
      orderBy: { threshold: 'asc' },
    });
  }

  /**
   * Update a risk rule
   */
  async update(id: string, data: UpdateRiskRuleData): Promise<RiskRule> {
    return this.prisma.riskRule.update({
      where: { id },
      data: {
        ...data,
        version: {
          increment: 1,
        },
      },
    });
  }

  /**
   * Update multiple rules
   */
  async updateMany(tenantId: string, updates: Array<{ id: string; data: UpdateRiskRuleData }>): Promise<number> {
    const updatePromises = updates.map(({ id, data }) =>
      this.prisma.riskRule.update({
        where: { id },
        data: {
          ...data,
          version: {
            increment: 1,
          },
        },
      }),
    );

    const results = await Promise.allSettled(updatePromises);
    return results.filter((result) => result.status === 'fulfilled').length;
  }

  /**
   * Soft delete a risk rule
   */
  async softDelete(id: string, deletedBy?: string): Promise<RiskRule> {
    return this.prisma.riskRule.update({
      where: { id },
      data: {
        isActive: false,
        status: RuleStatus.ARCHIVED,
        updatedBy: deletedBy,
      },
    });
  }

  /**
   * Get rules count by tenant
   */
  async countByTenant(tenantId: string): Promise<number> {
    return this.prisma.riskRule.count({
      where: {
        tenantId,
        isActive: true,
      },
    });
  }

  /**
   * Get rules statistics
   */
  async getStatistics(tenantId: string): Promise<{
    total: number;
    active: number;
    inactive: number;
    byType: Record<string, number>;
    bySeverity: Record<string, number>;
  }> {
    const [total, active, inactive, byType, bySeverity] = await Promise.all([
      this.prisma.riskRule.count({ where: { tenantId } }),
      this.prisma.riskRule.count({ where: { tenantId, isActive: true } }),
      this.prisma.riskRule.count({ where: { tenantId, isActive: false } }),
      this.prisma.riskRule.groupBy({
        by: ['ruleType'],
        where: { tenantId, isActive: true },
        _count: true,
      }),
      this.prisma.riskRule.groupBy({
        by: ['severity'],
        where: { tenantId, isActive: true },
        _count: true,
      }),
    ]);

    return {
      total,
      active,
      inactive,
      byType: byType.reduce((acc, item) => {
        acc[item.ruleType] = item._count;
        return acc;
      }, {} as Record<string, number>),
      bySeverity: bySeverity.reduce((acc, item) => {
        acc[item.severity] = item._count;
        return acc;
      }, {} as Record<string, number>),
    };
  }

  /**
   * Find rules that need review (old versions)
   */
  async findRulesNeedingReview(tenantId: string, daysOld = 90): Promise<RiskRule[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    return this.prisma.riskRule.findMany({
      where: {
        tenantId,
        isActive: true,
        updatedAt: {
          lt: cutoffDate,
        },
      },
      orderBy: { updatedAt: 'asc' },
    });
  }

  /**
   * Bulk create rules
   */
  async createMany(rules: CreateRiskRuleData[]): Promise<number> {
    const rulesWithIds = rules.map((rule) => ({
      id: generateCuid('rsk_'),
      ...rule,
    }));

    const result = await this.prisma.riskRule.createMany({
      data: rulesWithIds,
      skipDuplicates: true,
    });

    return result.count;
  }
}
