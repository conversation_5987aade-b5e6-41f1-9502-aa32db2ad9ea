import { Injectable } from '@nestjs/common';
import { CustomerRiskCategory, CustomerRiskProfile } from '@prisma/aml-client';
import { generateCuid } from '@qeep/common';
import { AMLPrismaService } from '../database/prisma.service';

export interface CreateCustomerRiskProfileData {
  tenantId: string;
  customerId: string;
  riskCategory: CustomerRiskCategory;
  overallRiskScore: number;
  riskReason?: string;
  riskFactors?: Record<string, any>;
  nextReviewDate?: Date;
}

export interface UpdateCustomerRiskProfileData {
  riskCategory?: CustomerRiskCategory;
  overallRiskScore?: number;
  riskReason?: string;
  riskFactors?: Record<string, any>;
  lastEvaluated?: Date;
  nextReviewDate?: Date;
}

@Injectable()
export class CustomerRiskProfileRepository {
  constructor(private readonly prisma: AMLPrismaService) {}

  /**
   * Create a new customer risk profile
   */
  async create(data: CreateCustomerRiskProfileData): Promise<CustomerRiskProfile> {
    return this.prisma.customerRiskProfile.create({
      data: {
        id: generateCuid('crp_'),
        ...data,
        lastEvaluated: new Date(),
      },
    });
  }

  /**
   * Find customer risk profile by customer ID
   */
  async findByCustomerId(customerId: string): Promise<CustomerRiskProfile | null> {
    return this.prisma.customerRiskProfile.findUnique({
      where: { customerId },
    });
  }

  /**
   * Find customer risk profile by ID
   */
  async findById(id: string): Promise<CustomerRiskProfile | null> {
    return this.prisma.customerRiskProfile.findUnique({
      where: { id },
    });
  }

  /**
   * Find profiles by tenant and risk category
   */
  async findByTenantAndCategory(tenantId: string, riskCategory: CustomerRiskCategory, limit = 100, offset = 0): Promise<CustomerRiskProfile[]> {
    return this.prisma.customerRiskProfile.findMany({
      where: {
        tenantId,
        riskCategory,
      },
      orderBy: [{ overallRiskScore: 'desc' }, { lastEvaluated: 'desc' }],
      take: limit,
      skip: offset,
    });
  }

  /**
   * Find high-risk profiles
   */
  async findHighRisk(tenantId: string, minRiskScore = 70, limit = 50): Promise<CustomerRiskProfile[]> {
    return this.prisma.customerRiskProfile.findMany({
      where: {
        tenantId,
        overallRiskScore: {
          gte: minRiskScore,
        },
        riskCategory: {
          in: [CustomerRiskCategory.HIGH, CustomerRiskCategory.PROHIBITED],
        },
      },
      orderBy: [{ overallRiskScore: 'desc' }, { lastEvaluated: 'desc' }],
      take: limit,
    });
  }

  /**
   * Find profiles needing review
   */
  async findNeedingReview(tenantId: string, beforeDate?: Date): Promise<CustomerRiskProfile[]> {
    const reviewDate = beforeDate || new Date();

    return this.prisma.customerRiskProfile.findMany({
      where: {
        tenantId,
        OR: [
          {
            nextReviewDate: {
              lte: reviewDate,
            },
          },
          {
            nextReviewDate: null,
            lastEvaluated: {
              lt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // 90 days ago
            },
          },
        ],
      },
      orderBy: [{ riskCategory: 'desc' }, { overallRiskScore: 'desc' }, { lastEvaluated: 'asc' }],
    });
  }

  /**
   * Update customer risk profile
   */
  async update(customerId: string, data: UpdateCustomerRiskProfileData): Promise<CustomerRiskProfile> {
    return this.prisma.customerRiskProfile.update({
      where: { customerId },
      data: {
        ...data,
        lastEvaluated: new Date(),
      },
    });
  }

  /**
   * Upsert customer risk profile
   */
  async upsert(customerId: string, createData: CreateCustomerRiskProfileData, updateData: UpdateCustomerRiskProfileData): Promise<CustomerRiskProfile> {
    return this.prisma.customerRiskProfile.upsert({
      where: { customerId },
      create: {
        id: generateCuid('crp_'),
        ...createData,
        lastEvaluated: new Date(),
      },
      update: {
        ...updateData,
        lastEvaluated: new Date(),
      },
    });
  }

  /**
   * Delete customer risk profile
   */
  async delete(customerId: string): Promise<CustomerRiskProfile> {
    return this.prisma.customerRiskProfile.delete({
      where: { customerId },
    });
  }

  /**
   * Get risk profile statistics for a tenant
   */
  async getStatistics(tenantId: string): Promise<{
    total: number;
    byCategory: Record<string, number>;
    averageRiskScore: number;
    needingReview: number;
    recentlyUpdated: number;
  }> {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const reviewDate = new Date();

    const [total, byCategory, averageRiskScore, needingReview, recentlyUpdated] = await Promise.all([
      this.prisma.customerRiskProfile.count({ where: { tenantId } }),
      this.prisma.customerRiskProfile.groupBy({
        by: ['riskCategory'],
        where: { tenantId },
        _count: true,
      }),
      this.prisma.customerRiskProfile.aggregate({
        where: { tenantId },
        _avg: {
          overallRiskScore: true,
        },
      }),
      this.prisma.customerRiskProfile.count({
        where: {
          tenantId,
          OR: [
            {
              nextReviewDate: {
                lte: reviewDate,
              },
            },
            {
              nextReviewDate: null,
              lastEvaluated: {
                lt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
              },
            },
          ],
        },
      }),
      this.prisma.customerRiskProfile.count({
        where: {
          tenantId,
          lastEvaluated: {
            gte: thirtyDaysAgo,
          },
        },
      }),
    ]);

    return {
      total,
      byCategory: byCategory.reduce((acc, item) => {
        acc[item.riskCategory] = item._count;
        return acc;
      }, {} as Record<string, number>),
      averageRiskScore: averageRiskScore._avg.overallRiskScore || 0,
      needingReview,
      recentlyUpdated,
    };
  }

  /**
   * Get risk score distribution
   */
  async getRiskScoreDistribution(tenantId: string): Promise<Array<{ range: string; count: number }>> {
    const result = await this.prisma.$queryRaw<
      Array<{
        range: string;
        count: bigint;
      }>
    >`
      SELECT 
        CASE 
          WHEN overall_risk_score < 20 THEN '0-19'
          WHEN overall_risk_score < 40 THEN '20-39'
          WHEN overall_risk_score < 60 THEN '40-59'
          WHEN overall_risk_score < 80 THEN '60-79'
          ELSE '80-100'
        END as range,
        COUNT(*) as count
      FROM customer_risk_profiles 
      WHERE tenant_id = ${tenantId}
      GROUP BY range
      ORDER BY range
    `;

    return result.map((row) => ({
      range: row.range,
      count: Number(row.count),
    }));
  }

  /**
   * Find profiles by risk score range
   */
  async findByRiskScoreRange(tenantId: string, minScore: number, maxScore: number, limit = 100): Promise<CustomerRiskProfile[]> {
    return this.prisma.customerRiskProfile.findMany({
      where: {
        tenantId,
        overallRiskScore: {
          gte: minScore,
          lte: maxScore,
        },
      },
      orderBy: { overallRiskScore: 'desc' },
      take: limit,
    });
  }

  /**
   * Bulk update review dates
   */
  async bulkUpdateReviewDates(tenantId: string, customerIds: string[], nextReviewDate: Date): Promise<number> {
    const result = await this.prisma.customerRiskProfile.updateMany({
      where: {
        tenantId,
        customerId: {
          in: customerIds,
        },
      },
      data: {
        nextReviewDate,
      },
    });

    return result.count;
  }

  /**
   * Get profiles with recent risk changes
   */
  async findWithRecentRiskChanges(tenantId: string, days = 7): Promise<CustomerRiskProfile[]> {
    const since = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

    return this.prisma.customerRiskProfile.findMany({
      where: {
        tenantId,
        lastEvaluated: {
          gte: since,
        },
      },
      orderBy: { lastEvaluated: 'desc' },
    });
  }
}
