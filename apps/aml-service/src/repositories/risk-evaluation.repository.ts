import { Injectable } from '@nestjs/common';
import { RiskEvaluation, RiskLevel, TransactionStatus } from '@prisma/aml-client';
import { generateId } from '@qeep/common';
import { AMLPrismaService } from '../database/prisma.service';

export interface CreateRiskEvaluationData {
  tenantId: string;
  transactionId?: string;
  customerId?: string;
  riskScore: number;
  riskLevel: RiskLevel;
  recommendedStatus: TransactionStatus;
  evaluationType: string;
  evaluationModel?: string;
  riskFactors?: Record<string, any>;
  processingTime: number;
}

export interface UpdateRiskEvaluationData {
  riskScore?: number;
  riskLevel?: RiskLevel;
  recommendedStatus?: TransactionStatus;
  riskFactors?: Record<string, any>;
}

@Injectable()
export class RiskEvaluationRepository {
  constructor(private readonly prisma: AMLPrismaService) {}

  /**
   * Create a new risk evaluation
   */
  async create(data: CreateRiskEvaluationData): Promise<RiskEvaluation> {
    return this.prisma.riskEvaluation.create({
      data: {
        id: generateId('eval'),
        ...data,
      },
      include: {
        alerts: true,
      },
    });
  }

  /**
   * Find risk evaluation by ID
   */
  async findById(id: string): Promise<RiskEvaluation | null> {
    return this.prisma.riskEvaluation.findUnique({
      where: { id },
      include: {
        alerts: {
          include: {
            rule: true,
          },
        },
      },
    });
  }

  /**
   * Find evaluations by transaction ID
   */
  async findByTransactionId(transactionId: string): Promise<RiskEvaluation[]> {
    return this.prisma.riskEvaluation.findMany({
      where: { transactionId },
      include: {
        alerts: {
          include: {
            rule: true,
          },
        },
      },
      orderBy: { evaluatedAt: 'desc' },
    });
  }

  /**
   * Find evaluations by customer ID
   */
  async findByCustomerId(
    customerId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<RiskEvaluation[]> {
    return this.prisma.riskEvaluation.findMany({
      where: { customerId },
      include: {
        alerts: {
          include: {
            rule: true,
          },
        },
      },
      orderBy: { evaluatedAt: 'desc' },
      take: limit,
      skip: offset,
    });
  }

  /**
   * Find recent evaluations for a tenant
   */
  async findRecentByTenant(
    tenantId: string,
    hours: number = 24,
    limit: number = 100
  ): Promise<RiskEvaluation[]> {
    const since = new Date();
    since.setHours(since.getHours() - hours);

    return this.prisma.riskEvaluation.findMany({
      where: {
        tenantId,
        evaluatedAt: {
          gte: since,
        },
      },
      include: {
        alerts: true,
      },
      orderBy: { evaluatedAt: 'desc' },
      take: limit,
    });
  }

  /**
   * Find high-risk evaluations
   */
  async findHighRisk(
    tenantId: string,
    minRiskScore: number = 70,
    limit: number = 50
  ): Promise<RiskEvaluation[]> {
    return this.prisma.riskEvaluation.findMany({
      where: {
        tenantId,
        riskScore: {
          gte: minRiskScore,
        },
        riskLevel: {
          in: [RiskLevel.HIGH, RiskLevel.CRITICAL],
        },
      },
      include: {
        alerts: {
          include: {
            rule: true,
          },
        },
      },
      orderBy: [
        { riskScore: 'desc' },
        { evaluatedAt: 'desc' },
      ],
      take: limit,
    });
  }

  /**
   * Update a risk evaluation
   */
  async update(id: string, data: UpdateRiskEvaluationData): Promise<RiskEvaluation> {
    return this.prisma.riskEvaluation.update({
      where: { id },
      data,
      include: {
        alerts: true,
      },
    });
  }

  /**
   * Get evaluation statistics for a tenant
   */
  async getStatistics(
    tenantId: string,
    fromDate?: Date,
    toDate?: Date
  ): Promise<{
    total: number;
    byRiskLevel: Record<string, number>;
    byStatus: Record<string, number>;
    averageRiskScore: number;
    averageProcessingTime: number;
    totalAlerts: number;
  }> {
    const whereClause: any = { tenantId };
    
    if (fromDate || toDate) {
      whereClause.evaluatedAt = {};
      if (fromDate) whereClause.evaluatedAt.gte = fromDate;
      if (toDate) whereClause.evaluatedAt.lte = toDate;
    }

    const [
      total,
      byRiskLevel,
      byStatus,
      averages,
      totalAlerts,
    ] = await Promise.all([
      this.prisma.riskEvaluation.count({ where: whereClause }),
      this.prisma.riskEvaluation.groupBy({
        by: ['riskLevel'],
        where: whereClause,
        _count: true,
      }),
      this.prisma.riskEvaluation.groupBy({
        by: ['recommendedStatus'],
        where: whereClause,
        _count: true,
      }),
      this.prisma.riskEvaluation.aggregate({
        where: whereClause,
        _avg: {
          riskScore: true,
          processingTime: true,
        },
      }),
      this.prisma.riskAlert.count({
        where: {
          evaluation: whereClause,
        },
      }),
    ]);

    return {
      total,
      byRiskLevel: byRiskLevel.reduce((acc, item) => {
        acc[item.riskLevel] = item._count;
        return acc;
      }, {} as Record<string, number>),
      byStatus: byStatus.reduce((acc, item) => {
        acc[item.recommendedStatus] = item._count;
        return acc;
      }, {} as Record<string, number>),
      averageRiskScore: averages._avg.riskScore || 0,
      averageProcessingTime: averages._avg.processingTime || 0,
      totalAlerts,
    };
  }

  /**
   * Get daily evaluation counts
   */
  async getDailyEvaluationCounts(
    tenantId: string,
    days: number = 30
  ): Promise<Array<{ date: string; count: number; averageRiskScore: number }>> {
    const fromDate = new Date();
    fromDate.setDate(fromDate.getDate() - days);

    const result = await this.prisma.$queryRaw<Array<{
      date: string;
      count: bigint;
      avg_risk_score: number;
    }>>`
      SELECT 
        DATE(evaluated_at) as date,
        COUNT(*) as count,
        AVG(risk_score) as avg_risk_score
      FROM risk_evaluations 
      WHERE tenant_id = ${tenantId}
        AND evaluated_at >= ${fromDate}
      GROUP BY DATE(evaluated_at)
      ORDER BY date DESC
    `;

    return result.map(row => ({
      date: row.date,
      count: Number(row.count),
      averageRiskScore: row.avg_risk_score || 0,
    }));
  }

  /**
   * Delete old evaluations (cleanup)
   */
  async deleteOldEvaluations(tenantId: string, olderThanDays: number = 365): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const result = await this.prisma.riskEvaluation.deleteMany({
      where: {
        tenantId,
        evaluatedAt: {
          lt: cutoffDate,
        },
      },
    });

    return result.count;
  }

  /**
   * Find evaluations needing review
   */
  async findEvaluationsNeedingReview(
    tenantId: string,
    riskThreshold: number = 80
  ): Promise<RiskEvaluation[]> {
    return this.prisma.riskEvaluation.findMany({
      where: {
        tenantId,
        riskScore: {
          gte: riskThreshold,
        },
        recommendedStatus: TransactionStatus.PENDING_REVIEW,
      },
      include: {
        alerts: {
          include: {
            rule: true,
          },
        },
      },
      orderBy: [
        { riskScore: 'desc' },
        { evaluatedAt: 'asc' },
      ],
    });
  }
}
