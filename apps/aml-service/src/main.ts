/**
 * AML Service - Anti-Money Laundering Risk Evaluation Service
 *
 * Provides gRPC-based risk evaluation services for transaction monitoring,
 * customer risk assessment, and compliance screening.
 */

import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { ProtoConfigService } from '@qeep/common';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('AMLService');

  try {
    // Create the NestJS application
    const app = await NestFactory.create(AppModule);

    // Get proto configuration
    const protoConfigService = app.get(ProtoConfigService);
    const protoPath = protoConfigService.getProtoPath('aml', 'aml.proto');

    // Configure gRPC microservice
    const grpcPort = process.env.AML_GRPC_PORT || '3010';
    const grpcHost = process.env.AML_GRPC_HOST || '0.0.0.0';

    app.connectMicroservice<MicroserviceOptions>({
      transport: Transport.GRPC,
      options: {
        package: 'aml',
        protoPath,
        url: `${grpcHost}:${grpcPort}`,
        loader: {
          keepCase: true,
          longs: String,
          enums: String,
          defaults: true,
          oneofs: true,
        },
      },
    });

    // Start all microservices
    await app.startAllMicroservices();

    // Optionally start HTTP server for health checks
    const httpPort = process.env.AML_HTTP_PORT || '3011';
    await app.listen(httpPort);

    logger.log(`🚀 AML gRPC Service is running on: ${grpcHost}:${grpcPort}`);
    logger.log(`🏥 AML HTTP Health Service is running on: http://localhost:${httpPort}`);
    logger.log(`📋 Proto file: ${protoPath}`);
  } catch (error) {
    logger.error('Failed to start AML service:', error);
    process.exit(1);
  }
}

bootstrap();
