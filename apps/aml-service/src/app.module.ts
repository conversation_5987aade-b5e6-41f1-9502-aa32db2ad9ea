import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CommonModule } from '@qeep/common';
import { RiskEvaluationModule } from './domains/risk-evaluation/risk-evaluation.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    CommonModule,
    RiskEvaluationModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
