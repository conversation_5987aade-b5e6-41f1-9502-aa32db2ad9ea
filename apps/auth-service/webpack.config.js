const { NxAppWebpackPlugin } = require('@nx/webpack/app-plugin');
const { join } = require('path');

module.exports = {
  target: 'node',
  output: {
    path: join(__dirname, '../../dist/apps/auth-service'),
  },
  externalsPresets: { node: true },
  externals: [
    // Explicitly exclude Node.js built-in modules
    function ({ request }, callback) {
      if (/^(crypto|fs|path|os|util|stream|events|buffer|url|querystring|http|https|net|tls|zlib|child_process|cluster|dgram|dns|domain|readline|repl|string_decoder|timers|tty|vm|worker_threads|async_hooks|perf_hooks|inspector|trace_events|v8|wasi)$/.test(request)) {
        return callback(null, 'commonjs ' + request);
      }
      callback();
    },
  ],
  plugins: [
    new NxAppWebpackPlugin({
      target: 'node',
      compiler: 'tsc',
      main: './src/main.ts',
      tsConfig: './tsconfig.app.json',
      assets: ['./src/assets'],
      optimization: false,
      outputHashing: 'none',
      generatePackageJson: true,
    }),
  ],
};
