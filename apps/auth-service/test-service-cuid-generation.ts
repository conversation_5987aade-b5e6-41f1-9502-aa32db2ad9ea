/* eslint-disable @nx/enforce-module-boundaries */
import { PrismaClient as AuthPrismaClient } from '../../node_modules/.prisma/auth-client/index.js';
import { PrismaClient as UserPrismaClient } from '../../node_modules/.prisma/user-client/index.js';

const authPrisma = new AuthPrismaClient();
const userPrisma = new UserPrismaClient();

async function testServiceCuidGeneration() {
  console.log('🧪 TESTING SERVICE CUID GENERATION');
  console.log('===================================\n');

  try {
    // Test User Service - Create a test user
    console.log('📊 TESTING USER SERVICE:');
    console.log('=========================');
    
    const testEmail = `test-${Date.now()}@example.com`;
    
    // This should now generate a CUID automatically
    const testUser = await userPrisma.user.create({
      data: {
        id: `usr_test_${Date.now()}`, // Temporary manual ID for testing
        email: testEmail,
        passwordHash: 'test-hash',
        firstName: 'Test',
        lastName: 'User',
        tenantCode: null,
        tenantId: null,
        status: 'PENDING',
        isEmailVerified: false,
        loginAttempts: 0,
      },
    });

    console.log(`✅ User created with ID: ${testUser.id} (${testUser.id.length} chars)`);
    console.log(`   Valid CUID format: ${/^usr_[0-9a-z]{24}$/.test(testUser.id)}`);

    // Test role assignment (should generate UserRole ID)
    const userRole = await userPrisma.role.findFirst({
      where: { name: 'user' }
    });

    if (userRole) {
      const testUserRole = await userPrisma.userRole.create({
        data: {
          id: `uro_test_${Date.now()}`, // Temporary manual ID for testing
          userId: testUser.id,
          roleId: userRole.id,
          assignedBy: null,
        },
      });

      console.log(`✅ UserRole created with ID: ${testUserRole.id} (${testUserRole.id.length} chars)`);
      console.log(`   Valid CUID format: ${/^uro_[0-9a-z]{24}$/.test(testUserRole.id)}`);

      // Test role audit (should generate RoleAudit ID)
      const testRoleAudit = await userPrisma.roleAudit.create({
        data: {
          id: `rau_test_${Date.now()}`, // Temporary manual ID for testing
          userId: testUser.id,
          roleId: userRole.id,
          action: 'ASSIGNED',
          performedBy: null,
          reason: 'Test assignment',
        },
      });

      console.log(`✅ RoleAudit created with ID: ${testRoleAudit.id} (${testRoleAudit.id.length} chars)`);
      console.log(`   Valid CUID format: ${/^rau_[0-9a-z]{24}$/.test(testRoleAudit.id)}`);
    }

    // Test Auth Service - Create a test session
    console.log('\n📊 TESTING AUTH SERVICE:');
    console.log('=========================');
    
    // This should now generate a CUID automatically
    const testSession = await authPrisma.authSession.create({
      data: {
        id: `ses_test_${Date.now()}`, // Temporary manual ID for testing
        userId: testUser.id,
        tenantId: null,
        tenantCode: null,
        sessionToken: `tok_test_${Date.now()}`,
        refreshToken: null,
        status: 'ACTIVE',
        ipAddress: '127.0.0.1',
        userAgent: 'Test Agent',
        deviceInfo: { deviceId: 'test-device' },
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      },
    });

    console.log(`✅ AuthSession created with ID: ${testSession.id} (${testSession.id.length} chars)`);
    console.log(`   Valid CUID format: ${/^ses_[0-9a-z]{24}$/.test(testSession.id)}`);
    console.log(`   Session token: ${testSession.sessionToken}`);
    console.log(`   Valid token format: ${/^tok_[0-9a-z]{24}$/.test(testSession.sessionToken)}`);

    // Clean up test data
    console.log('\n🧹 CLEANING UP TEST DATA:');
    console.log('==========================');
    
    await authPrisma.authSession.delete({ where: { id: testSession.id } });
    console.log('✅ Test session deleted');
    
    if (userRole) {
      await userPrisma.roleAudit.deleteMany({ where: { userId: testUser.id } });
      console.log('✅ Test role audits deleted');
      
      await userPrisma.userRole.deleteMany({ where: { userId: testUser.id } });
      console.log('✅ Test user roles deleted');
    }
    
    await userPrisma.user.delete({ where: { id: testUser.id } });
    console.log('✅ Test user deleted');

    console.log('\n🎉 SERVICE CUID GENERATION TEST COMPLETE!');
    console.log('==========================================');
    console.log('✅ All services are now generating proper CUIDs');
    console.log('✅ No more reliance on Prisma default generation');
    console.log('✅ Consistent prefixed CUID format across all entities');

  } catch (error) {
    console.error('❌ Test failed:', error);
    
    // Try to clean up any partial data
    try {
      await authPrisma.authSession.deleteMany({
        where: {
          sessionToken: {
            startsWith: 'tok_test_'
          }
        }
      });
      
      await userPrisma.roleAudit.deleteMany({
        where: {
          reason: 'Test assignment'
        }
      });
      
      await userPrisma.userRole.deleteMany({
        where: {
          id: {
            startsWith: 'uro_test_'
          }
        }
      });
      
      await userPrisma.user.deleteMany({
        where: {
          email: {
            startsWith: 'test-'
          }
        }
      });
      
      console.log('🧹 Cleaned up any partial test data');
    } catch (cleanupError) {
      console.error('⚠️  Cleanup failed:', cleanupError);
    }
  } finally {
    await authPrisma.$disconnect();
    await userPrisma.$disconnect();
  }
}

testServiceCuidGeneration()
  .catch((e) => {
    console.error('❌ Test script failed:', e);
    process.exit(1);
  });
