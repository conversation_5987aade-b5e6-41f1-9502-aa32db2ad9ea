import { CommonModule, ConfigModule, TenantExtractionSource } from '@qeep/common';

import { Module } from '@nestjs/common';
import { AccountManagementModule } from './domains/account-management/account-management.module';
import { AuthenticationModule } from './domains/authentication/authentication.module';
import { DeviceManagementModule } from './domains/device-management/device-management.module';
import { HealthModule } from './domains/health/health.module';
import { MfaModule } from './domains/mfa/mfa.module';
import { PasswordManagementModule } from './domains/password-management/password-management.module';
import { SecuritySettingsModule } from './domains/security-settings/security-settings.module';
import { SessionModule } from './domains/session-management/session.module';

@Module({
  imports: [
    CommonModule.forRoot({
      enableSecurityHeaders: false,
      enableTelemetry: false,
      enableGlobalTenantInterceptor: true,
      enableCircuitBreaker: false,
      enableRateLimiting: false, // Explicitly disabled
      enableCaseTransform: true, // Explicitly enable case transformation
      enableResponseFormat: true, // Explicitly enable response formatting
      tenantConfig: {
        enabled: true,
        extractionSources: [TenantExtractionSource.HEADER],
        tenantCodeHeader: 'x-tenant-code',
        tenantIdHeader: 'x-tenant-id',
        requireTenant: false, // Optional for auth endpoints
        validateTenant: false, // Temporarily disable validation to debug
        enableDebugLogging: false, // Keep debug logging enabled
      },
    }),
    ConfigModule.forRoot(),
    AuthenticationModule,
    AccountManagementModule,
    SessionModule,
    PasswordManagementModule,
    SecuritySettingsModule,
    HealthModule,
    DeviceManagementModule,
    MfaModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
