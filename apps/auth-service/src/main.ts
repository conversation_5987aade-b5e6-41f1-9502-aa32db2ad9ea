/**
 * Qeep Authentication Service
 * Handles user authentication, authorization, and JWT token management
 */

import { Logger, VersioningType } from '@nestjs/common';

import { NestFactory } from '@nestjs/core';
import { Transport } from '@nestjs/microservices';
import { ConfigService, ProtoConfigService } from '@qeep/common';
import { AppModule } from './app.module';

async function bootstrap() {
  // Create HTTP application
  const app = await NestFactory.create(AppModule);

  // Dynamically import ConfigService from lazy-loaded common library
  const configService = app.get(ConfigService);
  const protoConfigService = app.get(ProtoConfigService);

  // Global prefix for all routes
  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);

  // Enable API versioning
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
    prefix: 'v',
  });

  // Use specific port for Auth Service HTTP
  const httpPort = configService.getServicePort('auth-service');
  const host = configService.getServiceHost('auth-service');

  // Create gRPC microservice
  const grpcPort = configService.getServicePort('auth-service-grpc');

  // Connect gRPC microservice with multiple packages
  app.connectMicroservice({
    transport: Transport.GRPC,
    options: {
      url: configService.getServiceUrl('auth-service-grpc'),
      package: ['auth', 'common'],
      protoPath: [protoConfigService.getProtoPath('auth', 'auth.proto'), protoConfigService.getProtoPath('common', 'health.proto')],
      includeDirs: [protoConfigService.getProtoRootPath()],
      loader: protoConfigService.getLoaderOptions(),
    },
  });

  // Start microservices
  await app.startAllMicroservices();

  // Start HTTP server
  await app.listen(httpPort);

  Logger.log(`🔐 Auth Service HTTP is running on: http://${host}:${httpPort}/${globalPrefix}`);
  Logger.log(`🔗 Auth Service gRPC is running on: ${host}:${grpcPort}`);
  Logger.log(`📚 Environment: ${configService.getNodeEnv()}`);
  Logger.log(`🔧 Log Level: ${configService.getLogLevel()}`);
}

bootstrap();
