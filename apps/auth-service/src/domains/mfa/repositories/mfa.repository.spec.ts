import { Test, TestingModule } from '@nestjs/testing';
import { MfaMethod, MfaStatus, PrismaClient } from '@prisma/auth-client';
import { MfaRepository } from './mfa.repository';

// Mock PrismaClient
jest.mock('@prisma/auth-client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    mfaConfiguration: {
      upsert: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      deleteMany: jest.fn(),
    },
    mfaChallenge: {
      create: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      deleteMany: jest.fn(),
    },
  })),
  MfaMethod: {
    TOTP: 'TOTP',
    SMS: 'SMS',
    EMAIL: 'EMAIL',
  },
  MfaStatus: {
    ACTIVE: 'ACTIVE',
    INACTIVE: 'INACTIVE',
    LOCKED: 'LOCKED',
    EXPIRED: 'EXPIRED',
  },
}));

describe('MfaRepository', () => {
  let repository: MfaRepository;
  let prisma: jest.Mocked<PrismaClient>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [MfaRepository],
    }).compile();

    repository = module.get<MfaRepository>(MfaRepository);
    prisma = (repository as any).prisma;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('upsertMfaConfiguration', () => {
    it('should create new MFA configuration', async () => {
      const mockConfig = {
        id: 'mfa_123',
        userId: 'user_123',
        method: MfaMethod.TOTP,
        secret: 'encrypted_secret',
        isEnabled: true,
        status: MfaStatus.ACTIVE,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prisma.mfaConfiguration.upsert.mockResolvedValue(mockConfig);

      const result = await repository.upsertMfaConfiguration({
        userId: 'user_123',
        method: MfaMethod.TOTP,
        secret: 'test_secret',
        backupCodes: ['code1', 'code2'],
        isEnabled: true,
      });

      expect(prisma.mfaConfiguration.upsert).toHaveBeenCalledWith({
        where: { userId: 'user_123' },
        update: expect.objectContaining({
          method: MfaMethod.TOTP,
          isEnabled: true,
          status: MfaStatus.ACTIVE,
        }),
        create: expect.objectContaining({
          userId: 'user_123',
          method: MfaMethod.TOTP,
          isEnabled: true,
          status: MfaStatus.ACTIVE,
        }),
      });

      expect(result).toEqual(mockConfig);
    });

    it('should update existing MFA configuration', async () => {
      const mockConfig = {
        id: 'mfa_123',
        userId: 'user_123',
        method: MfaMethod.SMS,
        phoneNumber: '+1234567890',
        isEnabled: false,
        status: MfaStatus.INACTIVE,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prisma.mfaConfiguration.upsert.mockResolvedValue(mockConfig);

      const result = await repository.upsertMfaConfiguration({
        userId: 'user_123',
        method: MfaMethod.SMS,
        phoneNumber: '+1234567890',
        isEnabled: false,
      });

      expect(result).toEqual(mockConfig);
    });
  });

  describe('getMfaConfiguration', () => {
    it('should return MFA configuration for existing user', async () => {
      const mockConfig = {
        id: 'mfa_123',
        userId: 'user_123',
        method: MfaMethod.TOTP,
        secret: 'encrypted_secret',
        backupCodes: ['encrypted_code1', 'encrypted_code2'],
        isEnabled: true,
        status: MfaStatus.ACTIVE,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prisma.mfaConfiguration.findUnique.mockResolvedValue(mockConfig);

      const result = await repository.getMfaConfiguration('user_123');

      expect(prisma.mfaConfiguration.findUnique).toHaveBeenCalledWith({
        where: { userId: 'user_123' },
      });

      expect(result).toBeDefined();
      expect(result?.userId).toBe('user_123');
    });

    it('should return null for non-existing user', async () => {
      prisma.mfaConfiguration.findUnique.mockResolvedValue(null);

      const result = await repository.getMfaConfiguration('non_existing_user');

      expect(result).toBeNull();
    });
  });

  describe('getMfaUserData', () => {
    it('should convert database config to MfaUserData interface', async () => {
      const mockConfig = {
        id: 'mfa_123',
        userId: 'user_123',
        method: MfaMethod.TOTP,
        secret: 'encrypted_secret',
        backupCodes: ['encrypted_code1', 'encrypted_code2'],
        isEnabled: true,
        status: MfaStatus.ACTIVE,
        failedAttempts: 0,
        lastUsedAt: new Date(),
        setupAt: new Date(),
        lockedUntil: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prisma.mfaConfiguration.findUnique.mockResolvedValue(mockConfig);

      const result = await repository.getMfaUserData('user_123');

      expect(result).toBeDefined();
      expect(result?.userId).toBe('user_123');
      expect(result?.method).toBe(MfaMethod.TOTP);
      expect(result?.isEnabled).toBe(true);
      expect(result?.backupCodes).toHaveLength(2);
    });
  });

  describe('updateLastUsed', () => {
    it('should update last used timestamp and reset failed attempts', async () => {
      const mockUpdatedConfig = {
        id: 'mfa_123',
        userId: 'user_123',
        lastUsedAt: new Date(),
        failedAttempts: 0,
      };

      prisma.mfaConfiguration.update.mockResolvedValue(mockUpdatedConfig as any);

      await repository.updateLastUsed('user_123');

      expect(prisma.mfaConfiguration.update).toHaveBeenCalledWith({
        where: { userId: 'user_123' },
        data: {
          lastUsedAt: expect.any(Date),
          failedAttempts: 0,
        },
      });
    });
  });

  describe('incrementFailedAttempts', () => {
    it('should increment failed attempts without locking', async () => {
      const mockConfig = {
        id: 'mfa_123',
        userId: 'user_123',
        failedAttempts: 2,
        status: MfaStatus.ACTIVE,
      };

      const mockUpdatedConfig = {
        ...mockConfig,
        failedAttempts: 3,
        status: MfaStatus.ACTIVE,
        lockedUntil: null,
      };

      prisma.mfaConfiguration.findUnique.mockResolvedValue(mockConfig as any);
      prisma.mfaConfiguration.update.mockResolvedValue(mockUpdatedConfig as any);

      const result = await repository.incrementFailedAttempts('user_123');

      expect(result.isLocked).toBe(false);
      expect(result.lockedUntil).toBeUndefined();
    });

    it('should lock account after max failed attempts', async () => {
      const mockConfig = {
        id: 'mfa_123',
        userId: 'user_123',
        failedAttempts: 4,
        status: MfaStatus.ACTIVE,
      };

      const lockedUntil = new Date(Date.now() + 15 * 60 * 1000);
      const mockUpdatedConfig = {
        ...mockConfig,
        failedAttempts: 5,
        status: MfaStatus.LOCKED,
        lockedAt: new Date(),
        lockedUntil,
      };

      prisma.mfaConfiguration.findUnique.mockResolvedValue(mockConfig as any);
      prisma.mfaConfiguration.update.mockResolvedValue(mockUpdatedConfig as any);

      const result = await repository.incrementFailedAttempts('user_123');

      expect(result.isLocked).toBe(true);
      expect(result.lockedUntil).toEqual(lockedUntil);
    });
  });

  describe('createChallenge', () => {
    it('should create MFA challenge', async () => {
      const mockChallenge = {
        id: 'challenge_123',
        userId: 'user_123',
        challengeId: 'challenge_uuid',
        method: MfaMethod.SMS,
        code: 'encrypted_code',
        expiresAt: new Date(),
        createdAt: new Date(),
      };

      prisma.mfaChallenge.create.mockResolvedValue(mockChallenge as any);

      const result = await repository.createChallenge({
        userId: 'user_123',
        challengeId: 'challenge_uuid',
        method: MfaMethod.SMS,
        code: '123456',
        expiresAt: new Date(),
      });

      expect(prisma.mfaChallenge.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: 'user_123',
          challengeId: 'challenge_uuid',
          method: MfaMethod.SMS,
        }),
      });

      expect(result).toEqual(mockChallenge);
    });
  });

  describe('cleanupExpiredChallenges', () => {
    it('should delete expired challenges', async () => {
      const mockResult = { count: 5 };
      prisma.mfaChallenge.deleteMany.mockResolvedValue(mockResult);

      const result = await repository.cleanupExpiredChallenges();

      expect(prisma.mfaChallenge.deleteMany).toHaveBeenCalledWith({
        where: {
          expiresAt: {
            lt: expect.any(Date),
          },
        },
      });

      expect(result).toBe(5);
    });
  });

  describe('error handling', () => {
    it('should handle database errors gracefully', async () => {
      const dbError = new Error('Database connection failed');
      prisma.mfaConfiguration.findUnique.mockRejectedValue(dbError);

      await expect(repository.getMfaConfiguration('user_123')).rejects.toThrow('Database connection failed');
    });

    it('should handle encryption/decryption errors', async () => {
      const mockConfig = {
        id: 'mfa_123',
        userId: 'user_123',
        secret: 'invalid_encrypted_data',
        backupCodes: ['invalid_encrypted_code'],
      };

      prisma.mfaConfiguration.findUnique.mockResolvedValue(mockConfig as any);

      // This should handle decryption errors gracefully
      await expect(repository.getMfaConfiguration('user_123')).rejects.toThrow();
    });
  });
});
