import { Injectable, Logger } from '@nestjs/common';
import { MfaStatus, PrismaClient, MfaMethod as PrismaMfaMethod } from '@prisma/auth-client';
import { generateMfaChallengeId, generateMfaConfigurationId } from '@qeep/common';
import { MfaMethod as ContractMfaMethod, MfaUserData } from '@qeep/contracts';

// Type mapping between contract and Prisma enums
const MfaMethodMapping: Partial<Record<ContractMfaMethod, PrismaMfaMethod>> = {
  [ContractMfaMethod.TOTP]: PrismaMfaMethod.TOTP,
  [ContractMfaMethod.SMS]: PrismaMfaMethod.SMS,
  [ContractMfaMethod.EMAIL]: PrismaMfaMethod.EMAIL,
  // [ContractMfaMethod.HARDWARE_KEY]: PrismaMfaMethod.HARDWARE_KEY, // TODO: Add when Prisma client is updated
};

// Helper function to convert contract enum to Prisma enum
function toPrismaMfaMethod(method: ContractMfaMethod): PrismaMfaMethod {
  const mapped = MfaMethodMapping[method];
  if (!mapped) {
    throw new Error(`Unsupported MFA method: ${method}`);
  }
  return mapped;
}

// Helper function to convert Prisma enum to contract enum
function toContractMfaMethod(method: PrismaMfaMethod): ContractMfaMethod {
  return method as ContractMfaMethod; // Safe cast since enums have same values
}

/**
 * MFA Repository
 * Handles all database operations for MFA configurations and challenges
 */
@Injectable()
export class MfaRepository {
  private readonly logger = new Logger(MfaRepository.name);
  private readonly prisma = new PrismaClient();

  /**
   * Create or update MFA configuration
   */
  async upsertMfaConfiguration(data: {
    userId: string;
    tenantId?: string;
    tenantCode?: string;
    method: ContractMfaMethod;
    secret?: string;
    phoneNumber?: string;
    email?: string;
    backupCodes?: string[];
    isEnabled: boolean;
  }) {
    try {
      const encryptedSecret = data.secret ? this.encryptData(data.secret) : null;
      const encryptedBackupCodes = data.backupCodes ? data.backupCodes.map((code) => this.encryptData(code)) : null;

      return await this.prisma.mfaConfiguration.upsert({
        where: { userId: data.userId },
        update: {
          method: toPrismaMfaMethod(data.method),
          secret: encryptedSecret,
          phoneNumber: data.phoneNumber,
          email: data.email,
          backupCodes: encryptedBackupCodes,
          isEnabled: data.isEnabled,
          status: data.isEnabled ? MfaStatus.ACTIVE : MfaStatus.INACTIVE,
          setupAt: data.isEnabled ? new Date() : undefined,
          updatedAt: new Date(),
        },
        create: {
          id: this.generateMfaConfigId(),
          userId: data.userId,
          tenantId: data.tenantId,
          tenantCode: data.tenantCode,
          method: toPrismaMfaMethod(data.method),
          secret: encryptedSecret,
          phoneNumber: data.phoneNumber,
          email: data.email,
          backupCodes: encryptedBackupCodes,
          isEnabled: data.isEnabled,
          status: data.isEnabled ? MfaStatus.ACTIVE : MfaStatus.INACTIVE,
          setupAt: data.isEnabled ? new Date() : undefined,
        },
      });
    } catch (error) {
      this.logger.error(`Failed to upsert MFA configuration for user ${data.userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get MFA configuration by user ID
   */
  async getMfaConfiguration(userId: string) {
    try {
      const config = await this.prisma.mfaConfiguration.findUnique({
        where: { userId },
      });

      if (!config) {
        return null;
      }

      // Decrypt sensitive data
      return {
        ...config,
        secret: config.secret ? this.decryptData(config.secret) : null,
        backupCodes: config.backupCodes ? (config.backupCodes as string[]).map((code) => this.decryptData(code)) : [],
      };
    } catch (error) {
      this.logger.error(`Failed to get MFA configuration for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Convert database MFA configuration to MfaUserData interface
   */
  async getMfaUserData(userId: string): Promise<MfaUserData | null> {
    try {
      const config = await this.getMfaConfiguration(userId);
      if (!config) {
        return null;
      }

      return {
        userId: config.userId,
        method: toContractMfaMethod(config.method),
        secret: config.secret,
        phoneNumber: config.phoneNumber,
        email: config.email,
        backupCodes: config.backupCodes || [],
        isEnabled: config.isEnabled,
        lastUsed: config.lastUsedAt,
        setupAt: config.setupAt,
        failedAttempts: config.failedAttempts,
        lockedUntil: config.lockedUntil,
      };
    } catch (error) {
      this.logger.error(`Failed to get MFA user data for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update MFA last used timestamp
   */
  async updateLastUsed(userId: string) {
    try {
      await this.prisma.mfaConfiguration.update({
        where: { userId },
        data: {
          lastUsedAt: new Date(),
          failedAttempts: 0, // Reset failed attempts on successful use
        },
      });
    } catch (error) {
      this.logger.error(`Failed to update last used for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Increment failed attempts and lock if necessary
   */
  async incrementFailedAttempts(userId: string): Promise<{ isLocked: boolean; lockedUntil?: Date }> {
    try {
      const config = await this.prisma.mfaConfiguration.findUnique({
        where: { userId },
      });

      if (!config) {
        throw new Error('MFA configuration not found');
      }

      const newFailedAttempts = config.failedAttempts + 1;
      const maxAttempts = 5; // Configurable
      const lockoutDuration = 15 * 60 * 1000; // 15 minutes

      let updateData: {
        failedAttempts: number;
        status?: MfaStatus;
        lockedAt?: Date;
        lockedUntil?: Date;
      } = {
        failedAttempts: newFailedAttempts,
      };

      if (newFailedAttempts >= maxAttempts) {
        const lockedUntil = new Date(Date.now() + lockoutDuration);
        updateData = {
          ...updateData,
          status: MfaStatus.LOCKED,
          lockedAt: new Date(),
          lockedUntil,
        };
      }

      const updated = await this.prisma.mfaConfiguration.update({
        where: { userId },
        data: updateData,
      });

      return {
        isLocked: updated.status === MfaStatus.LOCKED,
        lockedUntil: updated.lockedUntil,
      };
    } catch (error) {
      this.logger.error(`Failed to increment failed attempts for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Remove used backup code
   */
  async removeBackupCode(userId: string, usedCode: string) {
    try {
      const config = await this.getMfaConfiguration(userId);
      if (!config || !config.backupCodes) {
        return;
      }

      const updatedCodes = config.backupCodes.filter((code) => code !== usedCode);
      const encryptedCodes = updatedCodes.map((code) => this.encryptData(code));

      await this.prisma.mfaConfiguration.update({
        where: { userId },
        data: {
          backupCodes: encryptedCodes,
        },
      });
    } catch (error) {
      this.logger.error(`Failed to remove backup code for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Disable MFA for user
   */
  async disableMfa(userId: string) {
    try {
      await this.prisma.mfaConfiguration.update({
        where: { userId },
        data: {
          isEnabled: false,
          status: MfaStatus.INACTIVE,
          secret: null,
          backupCodes: null,
          failedAttempts: 0,
          lockedAt: null,
          lockedUntil: null,
        },
      });
    } catch (error) {
      this.logger.error(`Failed to disable MFA for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create MFA challenge
   */
  async createChallenge(data: { userId: string; challengeId: string; method: ContractMfaMethod; code?: string; expiresAt: Date }) {
    try {
      const encryptedCode = data.code ? this.encryptData(data.code) : null;

      return await this.prisma.mfaChallenge.create({
        data: {
          id: this.generateMfaChallengeId(),
          userId: data.userId,
          challengeId: data.challengeId,
          method: toPrismaMfaMethod(data.method),
          code: encryptedCode,
          expiresAt: data.expiresAt,
        },
      });
    } catch (error) {
      this.logger.error(`Failed to create MFA challenge: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get MFA challenge
   */
  async getChallenge(challengeId: string) {
    try {
      const challenge = await this.prisma.mfaChallenge.findUnique({
        where: { challengeId },
      });

      if (!challenge) {
        return null;
      }

      return {
        ...challenge,
        code: challenge.code ? this.decryptData(challenge.code) : null,
      };
    } catch (error) {
      this.logger.error(`Failed to get MFA challenge ${challengeId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Mark challenge as used
   */
  async markChallengeAsUsed(challengeId: string) {
    try {
      await this.prisma.mfaChallenge.update({
        where: { challengeId },
        data: {
          isUsed: true,
          usedAt: new Date(),
        },
      });
    } catch (error) {
      this.logger.error(`Failed to mark challenge as used ${challengeId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Clean up expired challenges
   */
  async cleanupExpiredChallenges() {
    try {
      const result = await this.prisma.mfaChallenge.deleteMany({
        where: {
          expiresAt: {
            lt: new Date(),
          },
        },
      });

      this.logger.log(`Cleaned up ${result.count} expired MFA challenges`);
      return result.count;
    } catch (error) {
      this.logger.error(`Failed to cleanup expired challenges: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate unique MFA configuration ID
   */
  private generateMfaConfigId(): string {
    return generateMfaConfigurationId();
  }

  /**
   * Generate unique MFA challenge ID
   */
  private generateMfaChallengeId(): string {
    return generateMfaChallengeId();
  }

  /**
   * Encrypt sensitive data
   */
  private encryptData(data: string): string {
    // Simple base64 encoding - in production, use proper encryption with key management
    // This is just for demonstration purposes
    return Buffer.from(data, 'utf8').toString('base64');
  }

  /**
   * Decrypt sensitive data
   */
  private decryptData(encryptedData: string): string {
    try {
      // Simple base64 decoding - in production, use proper decryption
      return Buffer.from(encryptedData, 'base64').toString('utf8');
    } catch (error) {
      this.logger.error(`Failed to decrypt data: ${error.message}`);
      throw error;
    }
  }
}
