import { Body, Controller, Get, Logger, Post, UseGuards } from '@nestjs/common';
import { JwtAuthGuard, JwtUserId, ResponseUtil, SnakeToCamelPipe, StandardApiResponse, ZodValidationPipe } from '@qeep/common';
import {
  MfaChallengeRequestDto,
  MfaChallengeRequestSchema,
  MfaChallengeResponseDto,
  MfaDisableRequestDto,
  MfaDisableRequestSchema,
  MfaSetupRequestDto,
  MfaSetupRequestSchema,
  MfaSetupResponseDto,
  MfaStatusResponseDto,
  MfaVerificationRequestDto,
  MfaVerificationRequestSchema,
  MfaVerificationResponseDto,
  RegenerateBackupCodesRequestDto,
  RegenerateBackupCodesRequestSchema,
  RegenerateBackupCodesResponseDto,
  VerifyMfaChallengeRequestDto,
  VerifyMfaChallengeRequestSchema,
  VerifyMfaChallengeResponseDto,
} from '@qeep/contracts';
import { MfaService } from '../services/mfa.service';

@Controller('auth/mfa')
export class MfaController {
  private readonly logger = new Logger(MfaController.name);

  constructor(private readonly mfaService: MfaService) {}

  @Post('setup')
  @UseGuards(JwtAuthGuard)
  async setupMFA(
    @Body(new SnakeToCamelPipe(), new ZodValidationPipe(MfaSetupRequestSchema)) setupMfaDto: MfaSetupRequestDto,
    @JwtUserId() userId: string,
  ): Promise<StandardApiResponse<MfaSetupResponseDto>> {
    this.logger.log(`Setup MFA request for user: ${userId}, method: ${setupMfaDto.method}`);

    try {
      const result = await this.mfaService.setupMfa(userId, setupMfaDto);

      return ResponseUtil.success(result, 'MFA setup initiated successfully');
    } catch (error) {
      this.logger.error(`Setup MFA failed for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  @Post('verify-setup')
  @UseGuards(JwtAuthGuard)
  async verifyMfaSetup(
    @Body(new SnakeToCamelPipe(), new ZodValidationPipe(MfaVerificationRequestSchema)) verifyMfaSetupDto: MfaVerificationRequestDto,
    @JwtUserId() userId: string,
  ): Promise<StandardApiResponse<MfaVerificationResponseDto>> {
    this.logger.log(`Verify MFA setup for user: ${userId}`);

    try {
      const result = await this.mfaService.verifyMfaSetup(userId, verifyMfaSetupDto);
      return ResponseUtil.success(result, 'MFA enabled successfully');
    } catch (error) {
      this.logger.error(`Verify MFA setup failed for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  @Post('challenge')
  async createMfaChallenge(
    @Body(new SnakeToCamelPipe(), new ZodValidationPipe(MfaChallengeRequestSchema)) mfaChallengeDto: MfaChallengeRequestDto,
  ): Promise<StandardApiResponse<MfaChallengeResponseDto>> {
    this.logger.log(`Creating MFA challenge for method: ${mfaChallengeDto.method}, session token: ${mfaChallengeDto.sessionToken}`);

    try {
      const result = await this.mfaService.createMfaChallengeFromSession(mfaChallengeDto.sessionToken, mfaChallengeDto.method);

      return ResponseUtil.success(result, 'MFA challenge created successfully');
    } catch (error) {
      this.logger.error(`Create MFA challenge failed: ${error.message}`);
      throw error;
    }
  }

  @Post('verify')
  async verifyMfaChallenge(
    @Body(new SnakeToCamelPipe(), new ZodValidationPipe(VerifyMfaChallengeRequestSchema)) verifyMfaChallengeDto: VerifyMfaChallengeRequestDto,
  ): Promise<StandardApiResponse<VerifyMfaChallengeResponseDto>> {
    this.logger.log(`Verify MFA challenge: ${verifyMfaChallengeDto.challengeId}`);

    try {
      const authResult = await this.mfaService.verifyMfaChallengeWithTokens(verifyMfaChallengeDto);

      return ResponseUtil.success(authResult, 'MFA verification successful - user authenticated');
    } catch (error) {
      this.logger.error(`Verify MFA challenge failed: ${error.message}`);
      throw error;
    }
  }

  @Post('disable')
  @UseGuards(JwtAuthGuard)
  async disableMFA(
    @Body(new SnakeToCamelPipe(), new ZodValidationPipe(MfaDisableRequestSchema)) disableMfaDto: MfaDisableRequestDto,
    @JwtUserId() userId: string,
  ): Promise<StandardApiResponse<{ mfaEnabled: boolean }>> {
    this.logger.log(`Disable MFA request for user: ${userId}`);

    try {
      const result = await this.mfaService.disableMfa(userId, disableMfaDto);
      return ResponseUtil.success(result, 'MFA disabled successfully');
    } catch (error) {
      this.logger.error(`Disable MFA failed for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  @Get('status')
  @UseGuards(JwtAuthGuard)
  async getMfaStatus(@JwtUserId() userId: string): Promise<StandardApiResponse<MfaStatusResponseDto>> {
    this.logger.log(`Get MFA status for user: ${userId}`);

    try {
      const result = await this.mfaService.getMfaStatus(userId);
      return ResponseUtil.success(result, 'MFA status retrieved successfully');
    } catch (error) {
      this.logger.error(`Get MFA status failed for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  @Post('regenerate-backup-codes')
  @UseGuards(JwtAuthGuard)
  async regenerateBackupCodes(
    @Body(new ZodValidationPipe(RegenerateBackupCodesRequestSchema)) regenerateBackupCodesDto: RegenerateBackupCodesRequestDto,
    @JwtUserId() userId: string,
  ): Promise<StandardApiResponse<RegenerateBackupCodesResponseDto>> {
    this.logger.log(`Regenerate backup codes for user: ${userId}`);

    try {
      const result = await this.mfaService.regenerateBackupCodes(userId, regenerateBackupCodesDto);
      return ResponseUtil.success(result, 'Backup codes regenerated successfully');
    } catch (error) {
      this.logger.error(`Regenerate backup codes failed for user ${userId}: ${error.message}`);
      throw error;
    }
  }
}
