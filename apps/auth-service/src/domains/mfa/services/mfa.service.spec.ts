import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException } from '@nestjs/common';
import { ConfigService } from '@qeep/common';
import { MfaService } from './mfa.service';
import { MfaRepository } from '../repositories/mfa.repository';
import { MfaMethod } from '@qeep/contracts';
import { Redis } from 'ioredis';

// Mock dependencies
jest.mock('ioredis');
jest.mock('speakeasy');
jest.mock('qrcode');

const mockRedis = {
  get: jest.fn(),
  set: jest.fn(),
  setex: jest.fn(),
  del: jest.fn(),
  quit: jest.fn(),
};

const mockMfaRepository = {
  upsertMfaConfiguration: jest.fn(),
  getMfaUserData: jest.fn(),
  getMfaConfiguration: jest.fn(),
  updateLastUsed: jest.fn(),
  incrementFailedAttempts: jest.fn(),
  removeBackupCode: jest.fn(),
  disableMfa: jest.fn(),
  createChallenge: jest.fn(),
  getChallenge: jest.fn(),
  markChallengeAsUsed: jest.fn(),
  cleanupExpiredChallenges: jest.fn(),
};

const mockConfigService = {
  getRedisConfig: jest.fn().mockReturnValue({
    host: 'localhost',
    port: 6379,
    password: 'test_password',
  }),
};

describe('MfaService', () => {
  let service: MfaService;
  let repository: jest.Mocked<MfaRepository>;
  let redis: jest.Mocked<Redis>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MfaService,
        {
          provide: MfaRepository,
          useValue: mockMfaRepository,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<MfaService>(MfaService);
    repository = module.get(MfaRepository);
    redis = (service as any).redis;

    // Mock Redis methods
    Object.assign(redis, mockRedis);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('setupMfa', () => {
    it('should setup TOTP MFA successfully', async () => {
      const setupData = {
        method: MfaMethod.TOTP,
      };

      const mockSecret = 'JBSWY3DPEHPK3PXP';
      const mockQrCode = 'data:image/png;base64,mock_qr_code';
      const mockBackupCodes = ['CODE1', 'CODE2', 'CODE3'];

      // Mock speakeasy
      const speakeasy = require('speakeasy');
      speakeasy.generateSecret.mockReturnValue({
        base32: mockSecret,
        otpauth_url: 'otpauth://totp/test',
      });

      // Mock QRCode
      const QRCode = require('qrcode');
      QRCode.toDataURL.mockResolvedValue(mockQrCode);

      // Mock generateBackupCodes
      jest.spyOn(service as any, 'generateBackupCodes').mockReturnValue(mockBackupCodes);

      redis.setex.mockResolvedValue('OK');

      const result = await service.setupMfa('user_123', setupData);

      expect(result).toEqual({
        method: MfaMethod.TOTP,
        secret: mockSecret,
        qrCode: mockQrCode,
        backupCodes: mockBackupCodes,
      });

      expect(redis.setex).toHaveBeenCalledWith(
        'mfa_setup:user_123',
        600,
        expect.stringContaining('user_123')
      );
    });

    it('should setup SMS MFA successfully', async () => {
      const setupData = {
        method: MfaMethod.SMS,
        phoneNumber: '+1234567890',
      };

      const mockBackupCodes = ['CODE1', 'CODE2', 'CODE3'];
      jest.spyOn(service as any, 'generateBackupCodes').mockReturnValue(mockBackupCodes);

      redis.setex.mockResolvedValue('OK');

      const result = await service.setupMfa('user_123', setupData);

      expect(result).toEqual({
        method: MfaMethod.SMS,
        backupCodes: mockBackupCodes,
      });
    });
  });

  describe('verifyMfaSetup', () => {
    it('should verify TOTP setup successfully', async () => {
      const verifyData = {
        method: MfaMethod.TOTP,
        code: '123456',
      };

      const tempData = {
        userId: 'user_123',
        method: MfaMethod.TOTP,
        secret: 'JBSWY3DPEHPK3PXP',
        backupCodes: ['CODE1', 'CODE2'],
        isEnabled: false,
      };

      redis.get.mockResolvedValue(JSON.stringify(tempData));
      redis.set.mockResolvedValue('OK');
      redis.del.mockResolvedValue(1);

      // Mock speakeasy verification
      const speakeasy = require('speakeasy');
      speakeasy.totp.verify.mockReturnValue(true);

      repository.upsertMfaConfiguration.mockResolvedValue({} as any);

      const result = await service.verifyMfaSetup('user_123', verifyData);

      expect(result).toEqual({ verified: true });
      expect(repository.upsertMfaConfiguration).toHaveBeenCalledWith({
        userId: 'user_123',
        method: MfaMethod.TOTP,
        secret: 'JBSWY3DPEHPK3PXP',
        phoneNumber: undefined,
        email: undefined,
        backupCodes: ['CODE1', 'CODE2'],
        isEnabled: true,
      });
    });

    it('should throw error for invalid TOTP code', async () => {
      const verifyData = {
        method: MfaMethod.TOTP,
        code: '000000',
      };

      const tempData = {
        userId: 'user_123',
        method: MfaMethod.TOTP,
        secret: 'JBSWY3DPEHPK3PXP',
        isEnabled: false,
      };

      redis.get.mockResolvedValue(JSON.stringify(tempData));

      // Mock speakeasy verification failure
      const speakeasy = require('speakeasy');
      speakeasy.totp.verify.mockReturnValue(false);

      await expect(service.verifyMfaSetup('user_123', verifyData)).rejects.toThrow(BadRequestException);
    });

    it('should throw error when no setup in progress', async () => {
      const verifyData = {
        method: MfaMethod.TOTP,
        code: '123456',
      };

      redis.get.mockResolvedValue(null);

      await expect(service.verifyMfaSetup('user_123', verifyData)).rejects.toThrow(BadRequestException);
    });
  });

  describe('createMfaChallenge', () => {
    it('should create TOTP challenge successfully', async () => {
      const mockMfaData = {
        userId: 'user_123',
        method: MfaMethod.TOTP,
        secret: 'JBSWY3DPEHPK3PXP',
        isEnabled: true,
        backupCodes: ['CODE1', 'CODE2'],
      };

      repository.getMfaUserData.mockResolvedValue(mockMfaData);
      repository.createChallenge.mockResolvedValue({} as any);
      redis.setex.mockResolvedValue('OK');

      const result = await service.createMfaChallenge('user_123');

      expect(result).toEqual({
        challengeId: expect.any(String),
        method: MfaMethod.TOTP,
        expiresAt: expect.any(String),
        instructions: expect.stringContaining('TOTP'),
      });

      expect(repository.createChallenge).toHaveBeenCalled();
    });

    it('should create SMS challenge successfully', async () => {
      const mockMfaData = {
        userId: 'user_123',
        method: MfaMethod.SMS,
        phoneNumber: '+1234567890',
        isEnabled: true,
        backupCodes: ['CODE1', 'CODE2'],
      };

      repository.getMfaUserData.mockResolvedValue(mockMfaData);
      repository.createChallenge.mockResolvedValue({} as any);
      redis.setex.mockResolvedValue('OK');

      jest.spyOn(service as any, 'generateSmsCode').mockReturnValue('123456');
      jest.spyOn(service as any, 'maskPhoneNumber').mockReturnValue('***-***-7890');

      const result = await service.createMfaChallenge('user_123');

      expect(result).toEqual({
        challengeId: expect.any(String),
        method: MfaMethod.SMS,
        expiresAt: expect.any(String),
        instructions: expect.stringContaining('SMS'),
      });
    });

    it('should throw error when MFA not enabled', async () => {
      repository.getMfaUserData.mockResolvedValue(null);

      await expect(service.createMfaChallenge('user_123')).rejects.toThrow(BadRequestException);
    });
  });

  describe('verifyMfaChallenge', () => {
    it('should verify TOTP challenge successfully', async () => {
      const verifyData = {
        challengeId: 'challenge_123',
        code: '123456',
      };

      const mockMeta = {
        userId: 'user_123',
        method: MfaMethod.TOTP,
      };

      const mockMfaData = {
        userId: 'user_123',
        method: MfaMethod.TOTP,
        secret: 'JBSWY3DPEHPK3PXP',
        isEnabled: true,
        backupCodes: ['CODE1', 'CODE2'],
      };

      redis.get.mockResolvedValue(JSON.stringify(mockMeta));
      repository.getMfaUserData.mockResolvedValue(mockMfaData);
      repository.updateLastUsed.mockResolvedValue(undefined);
      redis.set.mockResolvedValue('OK');
      redis.del.mockResolvedValue(1);

      // Mock speakeasy verification
      const speakeasy = require('speakeasy');
      speakeasy.totp.verify.mockReturnValue(true);

      const result = await service.verifyMfaChallenge(verifyData);

      expect(result).toEqual({
        userId: 'user_123',
        isValid: true,
      });

      expect(repository.updateLastUsed).toHaveBeenCalledWith('user_123');
    });

    it('should verify backup code successfully', async () => {
      const verifyData = {
        challengeId: 'challenge_123',
        backupCode: 'CODE1',
      };

      const mockMeta = {
        userId: 'user_123',
        method: MfaMethod.TOTP,
      };

      const mockMfaData = {
        userId: 'user_123',
        method: MfaMethod.TOTP,
        secret: 'JBSWY3DPEHPK3PXP',
        isEnabled: true,
        backupCodes: ['CODE1', 'CODE2'],
      };

      redis.get.mockResolvedValue(JSON.stringify(mockMeta));
      repository.getMfaUserData.mockResolvedValue(mockMfaData);
      repository.removeBackupCode.mockResolvedValue(undefined);
      repository.updateLastUsed.mockResolvedValue(undefined);
      redis.set.mockResolvedValue('OK');
      redis.del.mockResolvedValue(1);

      const result = await service.verifyMfaChallenge(verifyData);

      expect(result).toEqual({
        userId: 'user_123',
        isValid: true,
      });

      expect(repository.removeBackupCode).toHaveBeenCalledWith('user_123', 'CODE1');
    });

    it('should return false for invalid code', async () => {
      const verifyData = {
        challengeId: 'challenge_123',
        code: '000000',
      };

      const mockMeta = {
        userId: 'user_123',
        method: MfaMethod.TOTP,
      };

      const mockMfaData = {
        userId: 'user_123',
        method: MfaMethod.TOTP,
        secret: 'JBSWY3DPEHPK3PXP',
        isEnabled: true,
        backupCodes: ['CODE1', 'CODE2'],
      };

      redis.get.mockResolvedValue(JSON.stringify(mockMeta));
      repository.getMfaUserData.mockResolvedValue(mockMfaData);

      // Mock speakeasy verification failure
      const speakeasy = require('speakeasy');
      speakeasy.totp.verify.mockReturnValue(false);

      const result = await service.verifyMfaChallenge(verifyData);

      expect(result).toEqual({
        userId: 'user_123',
        isValid: false,
      });
    });
  });

  describe('disableMfa', () => {
    it('should disable MFA successfully', async () => {
      const disableData = {
        password: 'user_password',
      };

      const mockMfaData = {
        userId: 'user_123',
        method: MfaMethod.TOTP,
        isEnabled: true,
        backupCodes: ['CODE1', 'CODE2'],
      };

      repository.getMfaUserData.mockResolvedValue(mockMfaData);
      repository.disableMfa.mockResolvedValue(undefined);
      redis.del.mockResolvedValue(1);

      const result = await service.disableMfa('user_123', disableData);

      expect(result).toEqual({ mfaEnabled: false });
      expect(repository.disableMfa).toHaveBeenCalledWith('user_123');
    });
  });
});
