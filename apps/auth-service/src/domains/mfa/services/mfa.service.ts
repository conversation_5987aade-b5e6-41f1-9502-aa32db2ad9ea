/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { BadRequestException, Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { ConfigService } from '@qeep/common';
import type { VerifyMfaChallengeResponseDto } from '@qeep/contracts/auth';
import {
  MfaChallengeResponseDto,
  MfaDisableRequestDto,
  MfaMethod,
  MfaSetupRequestDto,
  MfaSetupResponseDto,
  MfaStatusResponseDto,
  MfaVerificationRequestDto,
  MfaVerificationResponseDto,
  RegenerateBackupCodesRequestDto,
  RegenerateBackupCodesResponseDto,
  VerifyMfaChallengeRequestDto,
} from '@qeep/contracts/auth';
import type { MfaUserData } from '@qeep/contracts/auth/interfaces';
import { UserServiceClient } from '@qeep/proto';
import * as crypto from 'crypto';
import { Redis } from 'ioredis';
import * as jwt from 'jsonwebtoken';
import * as QRCode from 'qrcode';
import { firstValueFrom } from 'rxjs';
import * as speakeasy from 'speakeasy';
import { v4 as uuidv4 } from 'uuid';
import { SessionService } from '../../session-management/services/session.service';
import { MfaRepository } from '../repositories/mfa.repository';

@Injectable()
export class MfaService implements OnModuleInit {
  private readonly logger = new Logger(MfaService.name);
  private readonly redis: Redis;
  private userServiceClient: UserServiceClient;

  constructor(
    @Inject('USER_PACKAGE') private readonly userGrpcClient: ClientGrpc,
    private readonly configService: ConfigService,
    private readonly mfaRepository: MfaRepository,
    private readonly sessionService: SessionService,
  ) {
    // Initialize Redis connection
    const redisConfig = this.configService.getRedisConfig();
    this.redis = new Redis({
      host: redisConfig.host,
      port: redisConfig.port,
      password: redisConfig.password,
      maxRetriesPerRequest: 3,
    });
  }

  onModuleInit() {
    this.userServiceClient = this.userGrpcClient.getService<UserServiceClient>('UserService');
  }

  /**
   * Setup MFA for a user
   */
  async setupMfa(userId: string, setupData: MfaSetupRequestDto): Promise<MfaSetupResponseDto> {
    this.logger.log(`Setting up MFA for user: ${userId}, method: ${setupData.method}`);

    try {
      let secret: string | undefined;
      let qrCode: string | undefined;

      if (setupData.method === MfaMethod.TOTP) {
        // Generate TOTP secret
        const totpSecret = speakeasy.generateSecret({
          name: `Qeep (${userId})`,
          issuer: 'Qeep',
          length: 32,
        });

        secret = totpSecret.base32;

        // Generate QR code
        const otpauthUrl = totpSecret.otpauth_url;
        qrCode = await QRCode.toDataURL(otpauthUrl);
      }

      // Generate backup codes
      const backupCodes = this.generateBackupCodes();

      // Store temporary MFA setup data (not enabled yet)
      const tempMfaData: Partial<MfaUserData> = {
        userId,
        method: setupData.method,
        secret,
        phoneNumber: setupData.phoneNumber,
        email: setupData.email,
        backupCodes,
        isEnabled: false,
      };

      await this.redis.setex(
        `mfa_setup:${userId}`,
        600, // 10 minutes
        JSON.stringify(tempMfaData),
      );

      return {
        method: setupData.method,
        secret,
        qrCode,
        backupCodes,
      };
    } catch (error) {
      this.logger.error(`MFA setup failed for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Verify MFA setup and enable it
   */
  async verifyMfaSetup(userId: string, verifyData: MfaVerificationRequestDto): Promise<MfaVerificationResponseDto> {
    this.logger.log(`Verifying MFA setup for user: ${userId}`);

    try {
      // Get temporary setup data
      const tempDataStr = await this.redis.get(`mfa_setup:${userId}`);
      if (!tempDataStr) {
        throw new BadRequestException('No MFA setup in progress');
      }

      const tempData: Partial<MfaUserData> = JSON.parse(tempDataStr);

      if (tempData.method !== verifyData.method) {
        throw new BadRequestException('MFA method mismatch');
      }

      let isValid = false;

      if (verifyData.method === MfaMethod.TOTP) {
        // Verify TOTP code
        isValid = speakeasy.totp.verify({
          secret: tempData.secret!,
          encoding: 'base32',
          token: verifyData.code,
          window: 2, // Allow 2 time steps tolerance
        });
      } else if (verifyData.method === MfaMethod.SMS || verifyData.method === MfaMethod.EMAIL) {
        // For SMS/EMAIL, verify against stored challenge code
        const challengeCode = await this.redis.get(`mfa_challenge:${userId}`);
        isValid = challengeCode === verifyData.code;
      }

      if (!isValid) {
        throw new BadRequestException('Invalid MFA code');
      }

      // Enable MFA
      const mfaData: MfaUserData = {
        ...(tempData as MfaUserData),
        isEnabled: true,
      };

      // Save to PostgreSQL
      await this.mfaRepository.upsertMfaConfiguration({
        userId,
        method: tempData.method,
        secret: tempData.secret,
        phoneNumber: tempData.phoneNumber,
        email: tempData.email,
        backupCodes: mfaData.backupCodes,
        isEnabled: true,
      });

      // Clean up setup data
      await this.redis.del(`mfa_setup:${userId}`);

      this.logger.log(`MFA enabled for user: ${userId}`);

      return {
        verified: true,
      };
    } catch (error) {
      this.logger.error(`MFA verification failed for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create MFA challenge during login
   */
  async createMfaChallenge(userId: string): Promise<MfaChallengeResponseDto> {
    this.logger.log(`Creating MFA challenge for user: ${userId}`);

    try {
      const mfaData = await this.getMfaData(userId);
      if (!mfaData || !mfaData.isEnabled) {
        throw new BadRequestException('MFA not enabled for user');
      }

      const challengeId = uuidv4();
      let maskedContact: string | undefined;

      if (mfaData.method === MfaMethod.SMS) {
        // Send SMS code (mock implementation)
        const code = this.generateSmsCode();
        await this.redis.setex(`mfa_challenge:${challengeId}`, 300, code); // 5 minutes
        maskedContact = this.maskPhoneNumber(mfaData.phoneNumber!);

        // TODO: Integrate with SMS service
        this.logger.log(`SMS code sent to ${maskedContact}: ${code}`);
      } else if (mfaData.method === MfaMethod.EMAIL) {
        // Send email code (mock implementation)
        const code = this.generateEmailCode();
        await this.redis.setex(`mfa_challenge:${challengeId}`, 300, code); // 5 minutes
        maskedContact = this.maskEmail(mfaData.email!);

        // TODO: Integrate with email service
        this.logger.log(`Email code sent to ${maskedContact}: ${code}`);
      }

      // Store challenge in PostgreSQL
      const expiresAt = new Date(Date.now() + 300000); // 5 minutes
      const challengeCode = mfaData.method === MfaMethod.SMS || mfaData.method === MfaMethod.EMAIL ? await this.redis.get(`mfa_challenge:${challengeId}`) : undefined;

      await this.mfaRepository.createChallenge({
        userId,
        challengeId,
        method: mfaData.method,
        code: challengeCode,
        expiresAt,
      });

      // Store challenge metadata in Redis for temporary access
      await this.redis.setex(`mfa_challenge_meta:${challengeId}`, 300, JSON.stringify({ userId, method: mfaData.method }));

      return {
        challengeId,
        method: mfaData.method,
        expiresAt: new Date(Date.now() + 300000).toISOString(),
        instructions: `Enter the verification code from your ${mfaData.method} device`,
      };
    } catch (error) {
      this.logger.error(`MFA challenge creation failed for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Verify MFA challenge during login
   */
  async verifyMfaChallenge(verifyData: VerifyMfaChallengeRequestDto): Promise<{ userId: string; isValid: boolean }> {
    this.logger.log(`Verifying MFA challenge: ${verifyData.challengeId}`);

    try {
      // Get challenge metadata
      const metaStr = await this.redis.get(`mfa_challenge_meta:${verifyData.challengeId}`);
      if (!metaStr) {
        throw new BadRequestException('Invalid or expired challenge');
      }

      const meta = JSON.parse(metaStr);
      const mfaData = await this.getMfaData(meta.userId);

      if (!mfaData || !mfaData.isEnabled) {
        throw new BadRequestException('MFA not enabled');
      }

      let isValid = false;

      if (verifyData.backupCode) {
        // Verify backup code
        isValid = mfaData.backupCodes.includes(verifyData.backupCode);
        if (isValid) {
          // Remove used backup code from PostgreSQL
          await this.mfaRepository.removeBackupCode(meta.userId, verifyData.backupCode);
        }
      } else if (meta.method === MfaMethod.TOTP) {
        // Verify TOTP code
        isValid = speakeasy.totp.verify({
          secret: mfaData.secret!,
          encoding: 'base32',
          token: verifyData.code,
          window: 2,
        });
      } else {
        // Verify SMS/Email code
        const challengeCode = await this.redis.get(`mfa_challenge:${verifyData.challengeId}`);
        isValid = challengeCode === verifyData.code;
      }

      if (isValid) {
        // Update last used timestamp in PostgreSQL
        await this.mfaRepository.updateLastUsed(meta.userId);

        // Clean up challenge
        await this.redis.del(`mfa_challenge:${verifyData.challengeId}`);
        await this.redis.del(`mfa_challenge_meta:${verifyData.challengeId}`);
      }

      return { userId: meta.userId, isValid };
    } catch (error) {
      this.logger.error(`MFA challenge verification failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Disable MFA for a user
   */
  async disableMfa(userId: string, disableData: MfaDisableRequestDto): Promise<{ mfaEnabled: boolean }> {
    this.logger.log(`Disabling MFA for user: ${userId}`);

    try {
      const mfaData = await this.getMfaData(userId);
      if (!mfaData || !mfaData.isEnabled) {
        throw new BadRequestException('MFA not enabled');
      }

      // If backup code is provided, verify it
      if (disableData.backupCode) {
        const backupCodeIndex = mfaData.backupCodes.indexOf(disableData.backupCode);
        if (backupCodeIndex === -1) {
          throw new BadRequestException('Invalid backup code');
        }
        // Remove the used backup code
        mfaData.backupCodes.splice(backupCodeIndex, 1);
      }

      // Disable MFA in PostgreSQL
      await this.mfaRepository.disableMfa(userId);

      this.logger.log(`MFA disabled for user: ${userId}`);

      return { mfaEnabled: false };
    } catch (error) {
      this.logger.error(`MFA disable failed for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get MFA status for a user
   */
  async getMfaStatus(userId: string): Promise<MfaStatusResponseDto> {
    try {
      const mfaData = await this.getMfaData(userId);

      if (!mfaData || !mfaData.isEnabled) {
        return {
          enabled: false,
          methods: [],
          backup_codes_remaining: 0,
        };
      }

      return {
        enabled: true,
        methods: [mfaData.method],
        primaryMethod: mfaData.method,
        backup_codes_remaining: mfaData.backupCodes.length,
      };
    } catch (error) {
      this.logger.error(`Get MFA status failed for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Regenerate backup codes
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async regenerateBackupCodes(userId: string, _regenerateData: RegenerateBackupCodesRequestDto): Promise<RegenerateBackupCodesResponseDto> {
    this.logger.log(`Regenerating backup codes for user: ${userId}`);

    try {
      const mfaData = await this.getMfaData(userId);
      if (!mfaData || !mfaData.isEnabled) {
        throw new BadRequestException('MFA not enabled');
      }

      // Password verification is handled by the controller/guard
      // No additional MFA verification needed for regenerating backup codes
      // Generate new backup codes
      const newBackupCodes = this.generateBackupCodes();

      // Update PostgreSQL
      await this.mfaRepository.upsertMfaConfiguration({
        userId,
        method: mfaData.method,
        secret: mfaData.secret,
        phoneNumber: mfaData.phoneNumber,
        email: mfaData.email,
        backupCodes: newBackupCodes,
        isEnabled: mfaData.isEnabled,
      });

      return {
        backupCodes: newBackupCodes,
        generatedAt: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`Regenerate backup codes failed for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  // Helper methods
  private async getMfaData(userId: string): Promise<MfaUserData | null> {
    try {
      return await this.mfaRepository.getMfaUserData(userId);
    } catch (error) {
      this.logger.error(`Failed to get MFA data for user ${userId}: ${error.message}`);
      return null;
    }
  }

  private generateBackupCodes(): string[] {
    const codes: string[] = [];
    for (let i = 0; i < 10; i++) {
      codes.push(crypto.randomBytes(4).toString('hex').toUpperCase());
    }
    return codes;
  }

  private generateSmsCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  private generateEmailCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  private maskPhoneNumber(phone: string): string {
    if (phone.length < 4) return phone;
    return phone.slice(0, -4).replace(/\d/g, '*') + phone.slice(-4);
  }

  private maskEmail(email: string): string {
    const [local, domain] = email.split('@');
    if (local.length <= 2) return email;
    return local.slice(0, 2) + '*'.repeat(local.length - 2) + '@' + domain;
  }

  /**
   * Create MFA challenge using session token
   * @param sessionToken - The session token from the request (format: tok_...)
   * @param method - The MFA method to challenge
   * @returns Promise<MfaChallengeResponseDto> - The challenge response
   */
  async createMfaChallengeFromSession(sessionToken: string, method: MfaMethod): Promise<MfaChallengeResponseDto> {
    this.logger.log(`Creating MFA challenge from session token: ${sessionToken} for method: ${method}`);

    try {
      // Extract user ID from session token
      const userId = await this.getUserIdFromSessionToken(sessionToken);

      // Create the challenge using the existing method
      return await this.createMfaChallenge(userId);
    } catch (error) {
      this.logger.error(`MFA challenge creation from session failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Verify MFA challenge and generate tokens
   * @param verifyData - The verification request data
   * @returns Promise<VerifyMfaChallengeResponseDto> - The verification response with tokens
   */
  async verifyMfaChallengeWithTokens(verifyData: VerifyMfaChallengeRequestDto): Promise<VerifyMfaChallengeResponseDto> {
    this.logger.log(`Verifying MFA challenge with token generation: ${verifyData.challengeId}`);

    try {
      // Verify the challenge using existing method
      const verificationResult = await this.verifyMfaChallenge(verifyData);

      if (!verificationResult.isValid) {
        throw new BadRequestException('Invalid MFA code');
      }

      // Get user information via gRPC
      const userResponse = await firstValueFrom(
        this.userServiceClient.getUser({
          userId: verificationResult.userId,
          metadata: {
            requestId: `mfa-verify-${Date.now()}`,
            sourceIp: '127.0.0.1',
            userAgent: 'MfaService/1.0',
            timestamp: new Date(),
          },
        }),
      );

      if (!userResponse || !userResponse.success || !userResponse.user) {
        throw new BadRequestException('User not found');
      }

      const user = userResponse.user;

      // Generate JWT tokens
      const tokens = await this.generateTokens(user.id, user.email, user.tenantCode || null);

      return {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        expiresIn: 3600, // 1 hour
        user: {
          id: user.id,
          email: user.email,
          emailVerified: user.isEmailVerified,
          tenantCode: user.tenantCode || null,
        },
      };
    } catch (error) {
      this.logger.error(`MFA challenge verification with tokens failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Extract user ID from session token
   * @param sessionToken - The session token from the request (format: tok_...)
   * @returns Promise<string> - The user ID associated with the session
   * @throws BadRequestException if session is invalid or expired
   */
  private async getUserIdFromSessionToken(sessionToken: string): Promise<string> {
    try {
      // Get session information from session service
      const session = await this.sessionService.getSessionByToken({ sessionToken });

      if (!session) {
        throw new Error('Session not found or expired');
      }

      if (session.status !== 'ACTIVE') {
        throw new Error('Session is not active');
      }

      // Check if session is expired
      if (session.expiresAt && new Date(session.expiresAt) < new Date()) {
        throw new Error('Session has expired');
      }

      return session.userId;
    } catch (error) {
      this.logger.error(`Failed to extract user ID from session token ${sessionToken}: ${error.message}`);
      throw new BadRequestException('Invalid or expired session');
    }
  }

  /**
   * Generate JWT tokens for authenticated user
   * @param userId - The user ID
   * @param email - User's email
   * @param tenantCode - User's tenant code
   * @returns Object containing access token, refresh token, and expiration
   */
  private async generateTokens(userId: string, email: string, tenantCode: string | null) {
    const now = Math.floor(Date.now() / 1000);

    const payload = {
      sub: userId, // Standard JWT subject claim
      userId, // Custom claim for backward compatibility
      email,
      tenantCode,
      iss: 'qeep-auth-service', // Issuer
      aud: 'qeep-api', // Audience
      iat: now, // Issued at time
    };

    // For refresh token, add a unique identifier to ensure uniqueness
    const refreshPayload = {
      ...payload,
      jti: `${userId}-${now}-${Math.random().toString(36).substring(2)}`, // Unique JWT ID
    };

    const jwtConfig = this.configService.getJwtConfig();

    // Add kid (key ID) to header for JWKS compatibility
    const accessToken = (jwt.sign as any)(payload, jwtConfig.secret, {
      expiresIn: jwtConfig.expiresIn,
      algorithm: 'HS256',
      header: {
        kid: 'qeep-key-1', // Key identifier
        typ: 'JWT',
        alg: 'HS256',
      },
    });

    const refreshToken = (jwt.sign as any)(refreshPayload, jwtConfig.refreshSecret, {
      expiresIn: jwtConfig.refreshExpiresIn,
      algorithm: 'HS256',
      header: {
        kid: 'qeep-refresh-key-1',
        typ: 'JWT',
        alg: 'HS256',
      },
    });

    return { accessToken, refreshToken };
  }
}
