# 🔐 TOTP (Time-based One-Time Password) Flow Explained

## **Overview**

TOTP is a time-synchronized authentication method that generates 6-digit codes that change every 30 seconds. It's based on the RFC 6238 standard and uses a shared secret between the server and the user's authenticator app.

---

## **🔄 Complete TOTP Flow**

### **Phase 1: MFA Setup (One-time)**

#### **1. User Initiates TOTP Setup**

```http
POST /auth/mfa/setup
{
  "method": "TOTP"
}
```

#### **2. Server Generates TOTP Secret**

```typescript
// Server generates a cryptographically secure secret
const totpSecret = speakeasy.generateSecret({
  name: `Qeep (${userId})`,        // Account identifier
  issuer: 'Qeep',                  // Service name
  length: 32,                      // 32-byte secret (256 bits)
});

// Extract the Base32-encoded secret
secret = totpSecret.base32;

// Generate QR code containing the otpauth:// URL
const otpauthUrl = totpSecret.otpauth_url;
qrCode = await QRCode.toDataURL(otpauthUrl);
```

**The `otpauth://` URL format:**

```
otpauth://totp/Qeep:user123?secret=JBSWY3DPEHPK3PXP&issuer=Qeep
```

#### **3. Server Response**

```json
{
  "method": "TOTP",
  "secret": "JBSWY3DPEHPK3PXP",
  "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "backupCodes": ["A1B2C3D4", "E5F6G7H8", ...]
}
```

#### **4. User Scans QR Code**

- User opens authenticator app (Google Authenticator, Authy, etc.)
- Scans the QR code or manually enters the secret
- App starts generating 6-digit codes every 30 seconds

#### **5. User Verifies Setup**

```http
POST /auth/mfa/verify-setup
{
  "method": "TOTP",
  "code": "123456"
}
```

#### **6. Server Validates Setup Code**

```typescript
// Server verifies the TOTP code
isValid = speakeasy.totp.verify({
  secret: tempData.secret!,     // The shared secret
  encoding: 'base32',           // Secret encoding format
  token: verifyData.code,       // User's 6-digit code
  window: 2,                    // Allow ±2 time steps (±60 seconds tolerance)
});
```

#### **7. MFA Enabled & Stored**

If verification succeeds:

- Secret and backup codes are encrypted and stored in PostgreSQL
- MFA is marked as enabled for the user
- Temporary setup data is cleaned up

---

### **Phase 2: Authentication Flow (Every Login)**

#### **1. User Completes Primary Authentication**

- User provides username/password
- Server validates credentials
- Instead of issuing tokens, server requires MFA

#### **2. Server Creates MFA Challenge**

```http
POST /auth/mfa/challenge
{
  "loginToken": "temporary-login-token"
}
```

For TOTP, **no code is sent** - the challenge just indicates that TOTP verification is required:

```typescript
// For TOTP, no code generation - just create challenge record
const challengeId = uuidv4();

// Store challenge in PostgreSQL (no code for TOTP)
await this.mfaRepository.createChallenge({
  userId,
  challengeId,
  method: MfaMethod.TOTP,
  code: undefined,              // No server-generated code for TOTP
  expiresAt: new Date(Date.now() + 300000), // 5 minutes
});
```

#### **3. Server Response**

```json
{
  "challengeId": "challenge-uuid-123",
  "method": "TOTP",
  "expiresAt": "2024-07-17T21:30:00.000Z",
  "instructions": "Enter the verification code from your TOTP device"
}
```

#### **4. User Provides TOTP Code**

- User opens their authenticator app
- Reads the current 6-digit code (e.g., "456789")
- Submits the code:

```http
POST /auth/mfa/verify-challenge
{
  "challengeId": "challenge-uuid-123",
  "code": "456789"
}
```

#### **5. Server Verifies TOTP Code**

```typescript
// Server verifies the TOTP code against stored secret
isValid = speakeasy.totp.verify({
  secret: mfaData.secret!,      // User's stored secret (decrypted)
  encoding: 'base32',           // Secret encoding
  token: verifyData.code,       // User's submitted code
  window: 2,                    // ±60 seconds tolerance
});
```

#### **6. Authentication Complete**

If verification succeeds:

- Server updates last used timestamp
- Issues access and refresh tokens
- User is fully authenticated

---

## **🔧 Technical Details**

### **Time Synchronization**

- **Time Steps**: 30-second intervals
- **Window Tolerance**: ±2 steps (±60 seconds) to account for clock drift
- **Algorithm**: HMAC-SHA1 with time-based counter

### **Secret Management**

- **Generation**: 32-byte cryptographically secure random secret
- **Encoding**: Base32 for compatibility with authenticator apps
- **Storage**: Encrypted in PostgreSQL database
- **Sharing**: Via QR code containing `otpauth://` URL

### **Code Generation Algorithm**

```
TOTP = HOTP(K, T)
where:
- K = shared secret key
- T = current time step (Unix time ÷ 30)
- HOTP = HMAC-based One-Time Password
```

### **Backup Codes**

```typescript
// Generate 10 backup codes (8-character hex strings)
private generateBackupCodes(): string[] {
  const codes: string[] = [];
  for (let i = 0; i < 10; i++) {
    codes.push(crypto.randomBytes(4).toString('hex').toUpperCase());
  }
  return codes; // ["A1B2C3D4", "E5F6G7H8", ...]
}
```

- **Purpose**: Recovery when TOTP device is unavailable
- **Format**: 8-character hexadecimal strings
- **Usage**: One-time use, automatically removed after verification
- **Storage**: Encrypted array in PostgreSQL

---

## **🔒 Security Features**

### **Time-Based Security**

- **Limited Validity**: Each code valid for only 30 seconds
- **No Replay**: Used codes cannot be reused
- **Clock Tolerance**: ±60 seconds to handle minor time differences

### **Secret Protection**

- **Encryption**: Secrets encrypted at rest in database
- **No Transmission**: Secret never sent over network after setup
- **Secure Generation**: Cryptographically secure random generation

### **Challenge Protection**

- **Expiration**: Challenges expire after 5 minutes
- **Single Use**: Each challenge can only be verified once
- **User Binding**: Challenges tied to specific user sessions

---

## **📱 User Experience**

### **Setup Flow**

1. User scans QR code with authenticator app
2. App immediately starts showing 6-digit codes
3. User enters current code to verify setup
4. Backup codes provided for recovery

### **Login Flow**

1. User enters username/password
2. System prompts for TOTP code
3. User opens authenticator app
4. User enters current 6-digit code
5. Access granted immediately

### **Recovery Flow**

- If TOTP device unavailable, user can use backup codes
- Each backup code works once
- New backup codes can be generated when authenticated

---

## **🔄 Sequence Diagram**

```mermaid
sequenceDiagram
    participant U as User
    participant C as Client App
    participant S as Auth Server
    participant A as Authenticator App
    participant DB as PostgreSQL

    Note over U,DB: Phase 1: MFA Setup (One-time)
    
    U->>C: Request MFA setup
    C->>S: POST /auth/mfa/setup {"method": "TOTP"}
    S->>S: Generate TOTP secret & QR code
    S->>DB: Store temporary setup data
    S->>C: Return secret, QR code, backup codes
    C->>U: Display QR code
    U->>A: Scan QR code
    A->>A: Start generating codes
    A->>U: Show 6-digit code
    U->>C: Enter verification code
    C->>S: POST /auth/mfa/verify-setup {"code": "123456"}
    S->>S: Verify TOTP code
    S->>DB: Store encrypted secret & enable MFA
    S->>C: Setup confirmed
    
    Note over U,DB: Phase 2: Authentication (Every Login)
    
    U->>C: Login with username/password
    C->>S: POST /auth/login
    S->>S: Validate credentials
    S->>C: Require MFA challenge
    C->>S: POST /auth/mfa/challenge
    S->>DB: Create challenge record
    S->>C: Return challenge ID
    C->>U: Prompt for TOTP code
    U->>A: Check current code
    A->>U: Show 6-digit code
    U->>C: Enter TOTP code
    C->>S: POST /auth/mfa/verify-challenge
    S->>DB: Get user's secret
    S->>S: Verify TOTP code
    S->>DB: Update last used timestamp
    S->>C: Return access tokens
    C->>U: Login successful
```

This TOTP implementation provides **strong security** with **excellent user experience**, following industry standards and best practices! 🔐
