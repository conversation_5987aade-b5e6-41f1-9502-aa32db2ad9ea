# MFA (Multi-Factor Authentication) Domain

## Overview

This domain implements a PostgreSQL-first MFA system with Redis caching for the authentication service. It supports TOTP, SMS, EMAIL, and HARDWARE_KEY authentication methods.

## Architecture

### PostgreSQL-First Approach

- **Primary Storage**: PostgreSQL for persistent MFA configurations
- **Cache Layer**: Redis for temporary challenges and session data
- **Data Encryption**: Sensitive data (secrets, backup codes) are encrypted at rest
- **Type Safety**: Full TypeScript support with proper enum mapping

### Database Schema

#### MFA Configurations Table

```sql
CREATE TABLE mfa_configurations (
  id VARCHAR(35) PRIMARY KEY,
  user_id VARCHAR(35) UNIQUE NOT NULL,
  tenant_id VARCHAR(35),
  tenant_code VARCHAR(50),
  method MfaMethod NOT NULL,
  secret VARCHAR(255), -- Encrypted TOTP secret
  phone_number VARCHAR(20), -- For SMS
  email VARCHAR(255), -- For EMAIL
  backup_codes JSONB, -- Encrypted backup codes array
  is_enabled BOOLEAN DEFAULT false,
  status MfaStatus DEFAULT 'ACTIVE',
  failed_attempts INTEGER DEFAULT 0,
  locked_at TIMESTAMPTZ,
  locked_until TIMESTAMPTZ,
  last_used_at TIMESTAMPTZ,
  setup_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### MFA Challenges Table

```sql
CREATE TABLE mfa_challenges (
  id VARCHAR(35) PRIMARY KEY,
  user_id VARCHAR(35) NOT NULL,
  challenge_id VARCHAR(255) UNIQUE NOT NULL,
  method MfaMethod NOT NULL,
  code VARCHAR(10), -- Encrypted challenge code for SMS/EMAIL
  attempts INTEGER DEFAULT 0,
  max_attempts INTEGER DEFAULT 3,
  is_used BOOLEAN DEFAULT false,
  used_at TIMESTAMPTZ,
  expires_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Components

### Repository Layer (`MfaRepository`)

- **Purpose**: Data access layer for MFA operations
- **Features**:
  - CRUD operations for MFA configurations
  - Challenge management
  - Data encryption/decryption
  - Type-safe enum conversion
  - Automatic cleanup of expired data

### Service Layer (`MfaService`)

- **Purpose**: Business logic for MFA operations
- **Features**:
  - MFA setup and verification
  - Challenge creation and validation
  - Backup code management
  - Integration with TOTP libraries
  - PostgreSQL-first data access

### Controller Layer (`MfaController`)

- **Purpose**: HTTP API endpoints for MFA operations
- **Features**:
  - RESTful API design
  - Zod schema validation
  - Proper error handling
  - JWT authentication integration

## API Endpoints

### Setup MFA

```http
POST /auth/mfa/setup
Content-Type: application/json

{
  "method": "TOTP",
  "phoneNumber": "+1234567890", // For SMS
  "email": "<EMAIL>"   // For EMAIL
}
```

### Verify MFA Setup

```http
POST /auth/mfa/verify-setup
Content-Type: application/json

{
  "method": "TOTP",
  "code": "123456"
}
```

### Create MFA Challenge

```http
POST /auth/mfa/challenge
Content-Type: application/json

{
  "loginToken": "jwt-token"
}
```

### Verify MFA Challenge

```http
POST /auth/mfa/verify-challenge
Content-Type: application/json

{
  "challengeId": "challenge-uuid",
  "code": "123456",
  "backupCode": "BACKUP123" // Alternative to code
}
```

### Get MFA Status

```http
GET /auth/mfa/status
Authorization: Bearer jwt-token
```

### Disable MFA

```http
POST /auth/mfa/disable
Content-Type: application/json

{
  "password": "user-password"
}
```

### Regenerate Backup Codes

```http
POST /auth/mfa/regenerate-backup-codes
Content-Type: application/json

{
  "password": "user-password"
}
```

## Testing

### Unit Tests

- Repository layer tests with mocked Prisma client
- Service layer tests with mocked dependencies
- Comprehensive error handling scenarios

### Integration Tests

- End-to-end API testing
- Database integration testing
- Redis integration testing

### Running Tests

```bash
# Unit tests
npm test mfa.repository.spec.ts
npm test mfa.service.spec.ts

# Integration tests
npm test mfa.controller.e2e.spec.ts
```

## Security Features

### Data Protection

- **Encryption**: Sensitive data encrypted at rest
- **Backup Codes**: One-time use with automatic removal
- **Rate Limiting**: Failed attempt tracking and account lockout
- **Secure Storage**: PostgreSQL with proper indexing

### Authentication Flow

1. User initiates MFA setup
2. System generates secure secrets/codes
3. User verifies setup with test code
4. MFA is enabled and stored securely
5. During login, challenge is created
6. User provides verification code
7. System validates and grants access

## Configuration

### Environment Variables

```bash
# Database
AUTH_DATABASE_URL=postgresql://user:pass@host:port/db

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=password

# MFA Settings
MFA_TOTP_ISSUER=YourApp
MFA_BACKUP_CODES_COUNT=10
MFA_MAX_FAILED_ATTEMPTS=5
MFA_LOCKOUT_DURATION=900 # 15 minutes
```

## Monitoring and Maintenance

### Database Cleanup

```typescript
// Clean up expired challenges periodically
const cleanedCount = await mfaRepository.cleanupExpiredChallenges();
console.log(`Cleaned up ${cleanedCount} expired challenges`);
```

## Future Enhancements

1. **Hardware Key Support**: Full FIDO2/WebAuthn integration
2. **Push Notifications**: Mobile app-based authentication
3. **Biometric Authentication**: Fingerprint/face recognition
4. **Risk-Based Authentication**: Adaptive MFA based on context
5. **Audit Logging**: Comprehensive MFA event tracking
