/* eslint-disable @typescript-eslint/no-explicit-any */
import { Inject, Injectable, Logger, OnModuleInit, UnauthorizedException } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { ConfigService } from '@qeep/common';
import { UserServiceClient } from '@qeep/proto';
import Redis from 'ioredis';
import { firstValueFrom } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class SecuritySettingsService implements OnModuleInit {
  private readonly logger = new Logger(SecuritySettingsService.name);
  private userService: UserServiceClient;
  private redis: Redis;

  constructor(@Inject('USER_PACKAGE') private userClient: ClientGrpc, private configService: ConfigService) {}

  onModuleInit() {
    this.userService = this.userClient.getService<UserServiceClient>('UserService');

    // Initialize Redis
    const redisConfig = this.configService.getRedisConfig();
    this.redis = new Redis({
      host: redisConfig.host,
      port: redisConfig.port,
      password: redisConfig.password,
      maxRetriesPerRequest: 3,
    });
  }

  async getSecuritySettings(userId: string): Promise<any> {
    this.logger.log(`Getting security settings for user: ${userId}`);

    try {
      // Get user data
      const userResponse = await firstValueFrom(
        this.userService.getUser({
          userId,
          metadata: {
            requestId: uuidv4(),
            sourceIp: '',
            userAgent: '',
            timestamp: new Date(),
          },
        }),
      );

      if (!userResponse.success || !userResponse.user) {
        throw new UnauthorizedException('User not found');
      }

      const user = userResponse.user;

      // Get active sessions count (real data)
      // const sessions = await this.getUserSessions(userId);

      // Check if account is currently locked
      const isCurrentlyLocked = user.lockedUntil ? new Date(user.lockedUntil) > new Date() : false;

      // Get failed login attempts from Redis (more current than DB)
      const lockoutKey = `lockout:${user.email}:${user.tenantCode}`;
      const lockoutData = await this.redis.get(lockoutKey);
      let currentFailedAttempts = user.loginAttempts || 0;

      if (lockoutData) {
        const lockout = JSON.parse(lockoutData);
        currentFailedAttempts = lockout.attempts || 0;
      }

      // Check MFA status (placeholder for now)
      const mfaEnabled = false; // TODO: Implement MFA status check when MFA is implemented

      // Get security notification preference (placeholder)
      const securityNotificationsEnabled = true; // TODO: Add user preference storage

      return {
        mfaEnabled,
        // activeSessions: sessions.length,
        lastPasswordChange: null, // TODO: Add passwordChangedAt field to user model
        isLocked: isCurrentlyLocked,
        failedLoginAttempts: currentFailedAttempts,
        lastLogin: user.lastLoginAt,
        lastLoginIp: user.lastLoginIp,
        securityNotificationsEnabled,
      };
    } catch (error) {
      this.logger.error(`Failed to get security settings for user ${userId}: ${error.message}`);
      throw error;
    }
  }
}
