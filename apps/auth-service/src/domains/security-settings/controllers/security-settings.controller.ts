/* eslint-disable @typescript-eslint/no-explicit-any */
import { <PERSON>, Get, Logger, UseGuards } from '@nestjs/common';
import { ConfigService, JwtAuthGuard, JwtUserId } from '@qeep/common';
import { SecuritySettingsService } from '../services/security-settings.service';

@Controller('auth')
export class SecuritySettingsController {
  private readonly logger = new Logger(SecuritySettingsController.name);

  constructor(private readonly authenticationService: SecuritySettingsService, private readonly configService: ConfigService) {}

  @Get('security-settings')
  @UseGuards(JwtAuthGuard)
  async getSecuritySettings(@JwtUserId() userId: string): Promise<{ success: boolean; message: string; data: any }> {
    this.logger.log(`Get security settings request for user: ${userId}`);

    try {
      const settings = await this.authenticationService.getSecuritySettings(userId);

      return {
        success: true,
        message: 'Security settings retrieved successfully',
        data: settings,
      };
    } catch (error) {
      this.logger.error(`Get security settings failed: ${error.message}`);
      throw error;
    }
  }
}
