import { Modu<PERSON> } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigService, ProtoConfigService } from '@qeep/common';
import { AuthEventPublisher } from '../../events/auth-event-publisher.service';
import { AccountManagementController } from './controllers/account-management.controller';
import { AccountManagementService } from './services/account-management.service';
@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: 'USER_PACKAGE',
        imports: [],
        useFactory: (configService: ConfigService, protoConfigService: ProtoConfigService) => ({
          transport: Transport.GRPC,
          options: {
            package: 'user',
            protoPath: protoConfigService.getProtoPath('user', 'user.proto'),
            url: configService.getServiceUrl('user-service-grpc'),
            loader: protoConfigService.getLoaderOptions(),
            defaults: true,
            oneofs: true,
          },
        }),
        inject: [ConfigService, ProtoConfigService],
      },
      {
        name: 'TENANT_PACKAGE',
        imports: [],
        useFactory: (configService: ConfigService, protoConfigService: ProtoConfigService) => ({
          transport: Transport.GRPC,
          options: {
            package: 'tenant',
            protoPath: protoConfigService.getProtoPath('tenant', 'tenant.proto'),
            url: configService.getServiceUrl('tenant-service-grpc'),
            loader: protoConfigService.getLoaderOptions(),
            defaults: true,
            oneofs: true,
          },
        }),
        inject: [ConfigService, ProtoConfigService],
      },
      {
        name: 'NOTIFICATION_PACKAGE',
        imports: [],
        useFactory: (configService: ConfigService, protoConfigService: ProtoConfigService) => ({
          transport: Transport.GRPC,
          options: {
            package: 'notification',
            protoPath: protoConfigService.getProtoPath('notification', 'notification.proto'),
            url: configService.getServiceUrl('notification-service-grpc'),
            loader: protoConfigService.getLoaderOptions(),
            defaults: true,
            oneofs: true,
          },
        }),
        inject: [ConfigService, ProtoConfigService],
      },
    ]),
  ],
  controllers: [AccountManagementController],
  providers: [AccountManagementService, AuthEventPublisher],
  exports: [AccountManagementService],
})
export class AccountManagementModule {}
