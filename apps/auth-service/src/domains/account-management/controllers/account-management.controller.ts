/* eslint-disable @typescript-eslint/no-explicit-any */
import { BadRequestException, Body, Controller, ForbiddenException, Get, Logger, NotFoundException, Post, Query, Request, UseGuards } from '@nestjs/common';
import {
  ClientIp,
  JwtAuthGuard,
  JwtUserId,
  PlatformRole,
  RbacGuard,
  RequirePermissions,
  RequireRoles,
  ResponseUtil,
  SnakeToCamelPipe,
  StandardApiResponse,
  TenantId,
  TenantRole,
  UserAgent,
  UserPermission,
  ZodValidationPipe,
} from '@qeep/common';
import {
  CheckEmailAvailabilityRequestDto,
  CheckEmailAvailabilityRequestSchema,
  CheckEmailAvailabilityResponseDto,
  EmailVerificationRequestSchema,
  EmailVerificationResponseDto,
  ResendVerificationRequestSchema,
  SignupInternalDto,
  SignupRequestDto,
  SignupRequestSchema,
  SignupResponseDto,
  UnlockAccountRequestDto,
  UnlockAccountResponseDto,
  UnlockUserAccountRequestSchema,
  UserProfileResponseDto,
} from '@qeep/contracts/auth';
import { AccountManagementService } from '../services/account-management.service';

@Controller('auth')
export class AccountManagementController {
  private readonly logger = new Logger(AccountManagementController.name);

  constructor(private readonly accountManagementService: AccountManagementService) {}

  /** Signup */
  @Post('signup')
  async signup(
    @Body(new SnakeToCamelPipe(), new ZodValidationPipe(SignupRequestSchema)) signupDto: SignupRequestDto,
    @Request() req: Request,
    @ClientIp() ipAddress: string,
    @TenantId() tenantId?: string,
  ): Promise<StandardApiResponse<SignupResponseDto>> {
    try {
      const serviceRequest: SignupInternalDto = {
        email: signupDto.email,
        password: signupDto.password,
        firstName: signupDto.firstName,
        lastName: signupDto.lastName,
        tenantId: tenantId || signupDto.tenantId,
        acceptTerms: signupDto.acceptTerms,
        acceptPrivacy: signupDto.acceptPrivacy,
        marketingConsent: signupDto.marketingConsent,
        ipAddress: ipAddress,
        userAgent: req.headers['user-agent'],
        deviceId: req.headers['x-device-id'],
      };

      const signupResult = await this.accountManagementService.signup(serviceRequest);

      return ResponseUtil.created(signupResult, 'Account created successfully. Please check your email to verify your account.');
    } catch (error) {
      this.logger.error(`Signup failed: ${error.message}`);
      throw error;
    }
  }

  /** Check Email Availability */
  @Post('check-email')
  async checkEmail(
    @Body(new ZodValidationPipe(CheckEmailAvailabilityRequestSchema)) checkEmailDto: CheckEmailAvailabilityRequestDto,
  ): Promise<StandardApiResponse<CheckEmailAvailabilityResponseDto>> {
    try {
      const result = await this.accountManagementService.checkEmailAvailability(checkEmailDto);

      return ResponseUtil.success(result, 'Email availability check result', 200);
    } catch (error) {
      this.logger.error(`Email check failed: ${error.message}`);
      throw error;
    }
  }

  /** Verify Email */
  @Get('verify-email')
  async verifyEmail(@Query() verifyEmailQueryParams: any, @Request() req: Request, @ClientIp() ipAddress: string): Promise<StandardApiResponse<EmailVerificationResponseDto>> {
    this.logger.log(`Email verification request`);

    try {
      // Validate query parameters using Zod schema
      const queryDto = EmailVerificationRequestSchema.parse(verifyEmailQueryParams);

      // Extract IP address and user agent from request
      const userAgent = req.headers['user-agent'] || '';

      await this.accountManagementService.verifyEmail({
        token: queryDto.token,
        ipAddress: ipAddress,
        userAgent,
      });

      // Create response directly using DTO interface
      const response: EmailVerificationResponseDto = {
        message: 'Email verified successfully',
        verifiedAt: new Date().toISOString(),
        canLogin: true,
      };

      return ResponseUtil.success(response, 'Email verified successfully. You are now logged in.', 200);
    } catch (error) {
      this.logger.error(`Email verification failed: ${error.message}`);
      throw error;
    }
  }

  /** Resend Email Verification */
  @Post('resend-verification')
  async resendVerification(@Body(new ZodValidationPipe(ResendVerificationRequestSchema)) resendDto: any): Promise<StandardApiResponse<null>> {
    this.logger.log(`Resend verification request received`);

    try {
      const result = await this.accountManagementService.resendVerification({ email: resendDto.email });

      return ResponseUtil.success(null, result.message, 200);
    } catch (error) {
      this.logger.error(`Resend verification failed: ${error.message}`);
      throw error;
    }
  }

  /** Get current user profile with enhanced security */
  @Get('me')
  @UseGuards(JwtAuthGuard)
  async getCurrentUser(@JwtUserId() userId: string, @ClientIp() clientIp: string, @UserAgent() userAgent: string): Promise<StandardApiResponse<UserProfileResponseDto>> {
    try {
      const getUserResult = await this.accountManagementService.getCurrentUser(userId);

      return ResponseUtil.success(getUserResult, 'User retrieved successfully');
    } catch (error) {
      // Enhanced error logging for security monitoring
      this.logger.error(`Profile access failed for user: ${userId} from IP: ${clientIp}, User-Agent: ${userAgent}, Error: ${error.message}`);

      // Don't expose internal error details in production
      if (error.status === 404) {
        throw new NotFoundException('User not found');
      } else if (error.status === 403) {
        throw new ForbiddenException('Access forbidden');
      } else if (error.status === 400) {
        throw new BadRequestException('Invalid request');
      }

      throw error;
    }
  }

  @Post('unlock')
  @UseGuards(JwtAuthGuard, RbacGuard)
  @RequireRoles(TenantRole.TENANT_ADMIN, PlatformRole.SUPER_ADMIN)
  @RequirePermissions(UserPermission.USER_UNLOCK)
  async unlockAccount(
    @Body(new ZodValidationPipe(UnlockUserAccountRequestSchema)) unlockDto: UnlockAccountRequestDto,
    @JwtUserId() adminUserId: string,
  ): Promise<StandardApiResponse<UnlockAccountResponseDto>> {
    try {
      // Call service directly with validated data
      await this.accountManagementService.unlockUserAccount(unlockDto, adminUserId);

      // Create external response
      const externalResult: UnlockAccountResponseDto = {
        message: 'Account unlocked successfully',
        unlockedAt: new Date().toISOString(),
        userId: unlockDto.userId,
      };

      return ResponseUtil.success(externalResult, 'Account unlocked successfully');
    } catch (error) {
      this.logger.error(`Unlock account failed: ${error.message}`);
      throw error;
    }
  }
}
