/* eslint-disable @typescript-eslint/no-explicit-any */
import { BadRequestException, ConflictException, ForbiddenException, Inject, Injectable, Logger, NotFoundException, OnModuleInit } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { ConfigService, Traced } from '@qeep/common';
import {
  AccountManagementAuditAction,
  AuditResult,
  AuthStatus,
  CheckEmailAvailabilityRequestDto,
  CheckEmailAvailabilityResponseDto,
  EmailTriggerType,
  EmailVerificationAuditAction,
  OnboardingStep,
  SignupAuditAction,
  SignupInternalDto,
  SignupResponseDto,
  UserProfileResponseDto,
} from '@qeep/contracts';
import { NotificationServiceClient, UserServiceClient, UserUserStatus } from '@qeep/proto';
import * as bcrypt from 'bcryptjs';
import Redis from 'ioredis';
import * as jwt from 'jsonwebtoken';
import { firstValueFrom } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';
import { AUTH_CONSTANTS } from '../../../core/constants/auth.constants';
import { AuthEventPublisher } from '../../../events/auth-event-publisher.service';
import { AuthEventFactory } from '../../../events/auth.events';

@Injectable()
export class AccountManagementService implements OnModuleInit {
  private readonly logger = new Logger(AccountManagementService.name);
  private userService: UserServiceClient;
  private notificationService: NotificationServiceClient;
  private redis: Redis;

  constructor(
    @Inject('USER_PACKAGE') private readonly userClient: ClientGrpc,
    @Inject('NOTIFICATION_PACKAGE') private readonly notificationClient: ClientGrpc,
    private readonly configService: ConfigService,
    private readonly authEventPublisher: AuthEventPublisher,
  ) {}

  onModuleInit() {
    this.userService = this.userClient.getService<UserServiceClient>('UserService');
    this.notificationService = this.notificationClient.getService<NotificationServiceClient>('NotificationService');

    // Initialize Redis
    const redisConfig = this.configService.getRedisConfig();
    this.redis = new Redis({
      host: redisConfig.host,
      port: redisConfig.port,
      password: redisConfig.password,
      maxRetriesPerRequest: 3,
    });
  }

  /**
   * Create a new user account
   */
  @Traced('AccountManagementService.signup')
  async signup(dto: SignupInternalDto): Promise<SignupResponseDto> {
    this.logger.log(`Signup request for email: ${dto.email}`);

    // Publish audit event for signup initiation
    await this.authEventPublisher.publishAuditEvent(
      AuthEventFactory.createAuditEvent({
        action: SignupAuditAction.USER_SIGNUP_INITIATED,
        email: dto.email,
        result: AuditResult.PENDING,
      }),
    );

    try {
      // 1. Check if user already exists
      const existingUserResponse = await firstValueFrom(
        this.userService.getUserByEmail({
          email: dto.email,
        }),
      );

      if (existingUserResponse.user) {
        this.logger.warn(`Signup attempt with existing email: ${dto.email}`);
        throw new ConflictException('User with this email already exists');
      }

      // 2. Hash password before creating user
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(dto.password, saltRounds);

      // 3. Create user via gRPC
      const createUserResponse = await firstValueFrom(
        this.userService.createUser({
          email: dto.email,
          passwordHash,
          firstName: dto.firstName,
          lastName: dto.lastName,
          tenantCode: null, // No tenant assigned during signup
          status: UserUserStatus.USER_STATUS_PENDING,
          isEmailVerified: false,
        }),
      );

      if (!createUserResponse.success) {
        this.logger.error(`Failed to create user for email: ${dto.email}`);
        throw new BadRequestException('Failed to create user');
      }

      const user = createUserResponse.user;

      // Publish user registered audit event
      await this.authEventPublisher.publishAuditEvent(
        AuthEventFactory.createAuditEvent({
          action: SignupAuditAction.USER_SIGNUP_COMPLETED,
          userId: user.id,
          email: user.email,
          tenantCode: user.tenantCode,
          result: AuditResult.SUCCESS,
          ip: dto.ipAddress,
          userAgent: dto.userAgent,

          // Target information
          targetType: 'USER',
          targetId: user.id,
          targetName: `${user.firstName} ${user.lastName}`,

          // Change tracking - new user creation
          newValues: {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            status: AuthStatus.PENDING,
            isEmailVerified: false,
            tenantCode: user.tenantCode,
            acceptMarketingEmails: dto.marketingConsent,
          },

          additionalData: {
            firstName: user.firstName,
            lastName: user.lastName,
            status: AuthStatus.PENDING,
            acceptMarketingEmails: dto.marketingConsent,
          },
        }),
      );

      // 4. Generate verification token and send email
      let verificationEmailSent = false;
      try {
        // Generate simple verification token (not JWT)
        const { v4: uuidv4 } = require('uuid');
        const verificationToken = uuidv4();

        // Store verification token in Redis with 24 hour expiration
        const redisKey = `${AUTH_CONSTANTS.REDIS_KEYS.VERIFICATION}${verificationToken}`;
        await this.redis.setex(redisKey, AUTH_CONSTANTS.VERIFICATION_TOKEN_EXPIRY, user.id);

        // Send verification email via notification service
        const baseUrl = this.configService.get('FRONTEND_URL') || AUTH_CONSTANTS.DEFAULT_FRONTEND_URL;
        const verificationUrl = `${baseUrl}/verify-email?token=${verificationToken}`;

        const emailRequest = {
          recipientEmail: user.email,
          recipientName: user.firstName,
          subject: 'Welcome to Qeep - Verify Your Email',
          templateSlug: 'email-verification',
          templateData: {
            firstName: user.firstName,
            verificationUrl,
            verificationToken,
          },
          tenantCode: null, // No default tenant - null is null
          metadata: {
            requestId: uuidv4(),
            userId: user.id,
            tenantCode: null,
            timestamp: new Date(),
          },
        };

        const emailResponse = await firstValueFrom(this.notificationService.sendEmail(emailRequest));

        verificationEmailSent = emailResponse.success;
        this.logger.log(`Verification email sent successfully to ${user.email}`);

        // Publish email verification initiated audit event
        if (verificationEmailSent) {
          await this.authEventPublisher.publishAuditEvent(
            AuthEventFactory.createAuditEvent({
              action: EmailVerificationAuditAction.EMAIL_VERIFICATION_INITIATED,
              userId: user.id,
              email: user.email,
              tenantCode: user.tenantCode,
              result: AuditResult.SUCCESS,
              ip: dto.ipAddress,
              userAgent: dto.userAgent,

              // Target information
              targetType: 'EMAIL_VERIFICATION',
              targetId: user.email,
              targetName: `Email verification for ${user.email}`,

              additionalData: {
                verificationToken: verificationToken.substring(0, 8), // First 8 chars for tracking
                expiresAt: new Date(Date.now() + AUTH_CONSTANTS.VERIFICATION_TOKEN_EXPIRY * 1000).toISOString(),
              },
            }),
          );
        }
      } catch (emailError) {
        this.logger.warn(`Failed to send verification email: ${emailError.message}`);
        // Don't fail signup if email sending fails
        verificationEmailSent = false;
      }

      // Publish user onboarding started audit event
      await this.authEventPublisher.publishAuditEvent(
        AuthEventFactory.createAuditEvent({
          action: AccountManagementAuditAction.USER_ONBOARDING_STARTED,
          userId: user.id,
          email: user.email,
          tenantCode: user.tenantCode,
          result: AuditResult.SUCCESS,
          ip: dto.ipAddress,
          userAgent: dto.userAgent,

          // Target information
          targetType: 'USER_ONBOARDING',
          targetId: user.id,
          targetName: `Onboarding for ${user.firstName} ${user.lastName}`,

          additionalData: {
            nextSteps: [OnboardingStep.EMAIL_VERIFICATION, OnboardingStep.PROFILE_COMPLETION],
          },
        }),
      );

      // Publish audit event for successful signup
      await this.authEventPublisher.publishAuditEvent(
        AuthEventFactory.createAuditEvent({
          action: SignupAuditAction.USER_SIGNUP_COMPLETED,
          userId: user.id,
          email: dto.email,
          result: AuditResult.SUCCESS,
        }),
      );

      return {
        userId: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        status: user.status,
        requiresEmailVerification: true,
        nextStep: OnboardingStep.EMAIL_VERIFICATION,
        verificationEmailSentAt: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`Signup failed for ${dto.email}: ${error.message}`);

      // Publish audit event for failed signup
      await this.authEventPublisher.publishAuditEvent(
        AuthEventFactory.createAuditEvent({
          action: SignupAuditAction.USER_SIGNUP_FAILED,
          email: dto.email,
          result: AuditResult.FAILURE,
          errorMessage: error.message,
        }),
      );

      throw error;
    }
  }

  /**
   * Check if email is available for registration
   */
  @Traced('AccountManagementService.checkEmailAvailability')
  async checkEmailAvailability(dto: CheckEmailAvailabilityRequestDto): Promise<CheckEmailAvailabilityResponseDto> {
    this.logger.log(`Checking email availability for: ${dto.email}`);

    // Publish audit event for email availability check
    await this.authEventPublisher.publishAuditEvent(
      AuthEventFactory.createAuditEvent({
        action: AccountManagementAuditAction.EMAIL_AVAILABILITY_CHECK,
        email: dto.email,
        result: AuditResult.PENDING,
        ip: AUTH_CONSTANTS.UNKNOWN_VALUE,
        userAgent: AUTH_CONSTANTS.UNKNOWN_VALUE,
      }),
    );

    try {
      // Check if user exists with this email
      const userResponse = await firstValueFrom(
        this.userService.getUserByEmail({
          email: dto.email,
        }),
      );

      const exists = userResponse.success && userResponse.user;
      this.logger.log(`Email availability check for ${dto.email}: ${exists ? 'exists' : 'available'}`);

      // Publish successful audit event
      await this.authEventPublisher.publishAuditEvent(
        AuthEventFactory.createAuditEvent({
          action: AccountManagementAuditAction.EMAIL_AVAILABILITY_CHECK,
          email: dto.email,
          result: AuditResult.SUCCESS,
          ip: AUTH_CONSTANTS.UNKNOWN_VALUE,
          userAgent: AUTH_CONSTANTS.UNKNOWN_VALUE,
          additionalData: {
            emailExists: !!exists,
            checkResult: exists ? 'exists' : 'available',
          },
        }),
      );

      return {
        available: !!exists,
      };
    } catch (error) {
      this.logger.error(`Email availability check failed for ${dto.email}: ${error.message}`);

      // Publish failed audit event
      await this.authEventPublisher.publishAuditEvent(
        AuthEventFactory.createAuditEvent({
          action: AccountManagementAuditAction.EMAIL_AVAILABILITY_CHECK,
          email: dto.email,
          result: AuditResult.FAILURE,
          ip: AUTH_CONSTANTS.UNKNOWN_VALUE,
          userAgent: AUTH_CONSTANTS.UNKNOWN_VALUE,
          errorMessage: error.message,
          additionalData: {
            securityResponse: true, // Indicates we're returning a security-safe response
          },
        }),
      );

      // For security, don't reveal if the check failed due to system error
      // Return as if email doesn't exist
      return {
        available: false,
      };
    }
  }

  /**
   * Get current user profile data with enhanced security practices
   */
  @Traced('AccountManagementService.getCurrentUser')
  async getCurrentUser(userId: string): Promise<UserProfileResponseDto> {
    this.logger.log(`Getting current user data for user: ${userId}`);

    // Publish audit event for profile access attempt
    await this.authEventPublisher.publishAuditEvent(
      AuthEventFactory.createAuditEvent({
        action: AccountManagementAuditAction.USER_PROFILE_ACCESS,
        userId: userId,
        result: AuditResult.PENDING,
        ip: AUTH_CONSTANTS.UNKNOWN_VALUE,
        userAgent: AUTH_CONSTANTS.UNKNOWN_VALUE,

        // Target information
        targetType: 'USER_PROFILE',
        targetId: userId,
        targetName: 'User Profile Access',
      }),
    );

    try {
      // Validate userId format for security
      if (!userId || typeof userId !== 'string' || userId.trim().length === 0) {
        this.logger.warn(`Invalid user ID provided: ${userId}`);

        // Publish audit event for invalid user ID
        await this.authEventPublisher.publishAuditEvent(
          AuthEventFactory.createAuditEvent({
            action: AccountManagementAuditAction.USER_PROFILE_ACCESS,
            userId: userId,
            result: AuditResult.FAILURE,
            ip: AUTH_CONSTANTS.UNKNOWN_VALUE,
            userAgent: AUTH_CONSTANTS.UNKNOWN_VALUE,
            errorCode: 'INVALID_USER_ID',
            errorMessage: 'Invalid user ID',
          }),
        );

        throw new BadRequestException('Invalid user ID');
      }

      // Get comprehensive user data from user service
      const userResponse = await firstValueFrom(
        this.userService.getUser({
          userId: userId.trim(),
        }),
      );

      if (!userResponse.user) {
        this.logger.warn(`User not found for ID: ${userId}`);
        throw new NotFoundException('User not found');
      }

      const user = userResponse.user;

      // Check if user account is in a valid state for profile access
      if (!user.id || !user.email) {
        this.logger.error(`Invalid user data structure for user: ${userId}`);
        throw new BadRequestException('Invalid user data');
      }

      // Security check: Ensure user account is not in a forbidden state
      if (user.status === UserUserStatus.USER_STATUS_SUSPENDED) {
        this.logger.warn(`Attempt to access suspended user profile: ${userId}`);

        // Publish audit event for suspended account access attempt
        await this.authEventPublisher.publishAuditEvent(
          AuthEventFactory.createAuditEvent({
            action: AccountManagementAuditAction.USER_PROFILE_ACCESS,
            userId: userId,
            email: user.email,
            tenantCode: user.tenantCode,
            result: AuditResult.FAILURE,
            ip: AUTH_CONSTANTS.UNKNOWN_VALUE,
            userAgent: AUTH_CONSTANTS.UNKNOWN_VALUE,
            errorCode: 'ACCOUNT_SUSPENDED',
            errorMessage: 'Account suspended',
            additionalData: {
              accountStatus: this.mapUserStatusToString(user.status),
            },
          }),
        );

        throw new ForbiddenException('Account suspended');
      }

      if (user.status === UserUserStatus.USER_STATUS_INACTIVE) {
        this.logger.warn(`Attempt to access inactive user profile: ${userId}`);

        // Publish audit event for inactive account access attempt
        await this.authEventPublisher.publishAuditEvent(
          AuthEventFactory.createAuditEvent({
            action: AccountManagementAuditAction.USER_PROFILE_ACCESS,
            userId: userId,
            email: user.email,
            tenantCode: user.tenantCode,
            result: AuditResult.FAILURE,
            ip: AUTH_CONSTANTS.UNKNOWN_VALUE,
            userAgent: AUTH_CONSTANTS.UNKNOWN_VALUE,
            errorCode: 'ACCOUNT_INACTIVE',
            errorMessage: 'Account inactive',
            additionalData: {
              accountStatus: this.mapUserStatusToString(user.status),
            },
          }),
        );

        throw new ForbiddenException('Account inactive');
      }

      // Security logging for successful profile access
      this.logger.log(`Profile accessed successfully for user: ${userId}, email: ${user.email}`);

      // Publish successful audit event
      await this.authEventPublisher.publishAuditEvent(
        AuthEventFactory.createAuditEvent({
          action: AccountManagementAuditAction.USER_PROFILE_ACCESS,
          userId: userId,
          email: user.email,
          tenantCode: user.tenantCode,
          result: AuditResult.SUCCESS,
          ip: AUTH_CONSTANTS.UNKNOWN_VALUE,
          userAgent: AUTH_CONSTANTS.UNKNOWN_VALUE,

          // Target information
          targetType: 'USER_PROFILE',
          targetId: userId,
          targetName: `${user.firstName} ${user.lastName} Profile`,

          additionalData: {
            accountStatus: this.mapUserStatusToString(user.status),
            isEmailVerified: user.isEmailVerified,
            lastLoginAt: user.lastLoginAt ? new Date(user.lastLoginAt).toISOString() : null,
          },
        }),
      );

      return {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        status: this.mapUserStatusToString(user.status),
        isEmailVerified: user.isEmailVerified,
        roles: [],
        tenant: user.tenantCode
          ? {
              id: '', // TODO: Get from tenant service
              code: user.tenantCode,
              name: '', // TODO: Get from tenant service
            }
          : undefined,
        lastLoginAt: user.lastLoginAt ? new Date(user.lastLoginAt).toISOString() : null,
        lastLoginIp: user.lastLoginIp,
        lastLoginUserAgent: user.lastLoginUserAgent,
        loginAttempts: user.loginAttempts,
        lockedUntil: user.lockedUntil ? new Date(user.lockedUntil).toISOString() : null,
        passwordChangedAt: null,
        createdAt: user.createdAt.toISOString(),
        updatedAt: user.updatedAt.toISOString(),
      };
    } catch (error) {
      // Enhanced error logging for security monitoring
      if (error instanceof NotFoundException || error instanceof ForbiddenException || error instanceof BadRequestException) {
        // These are expected errors, log at appropriate level
        this.logger.warn(`Profile access denied for user ${userId}: ${error.message}`);
      } else {
        // Unexpected errors should be logged as errors
        this.logger.error(`Get current user failed for user ${userId}: ${error.message}`, error.stack);
      }
      throw error;
    }
  }

  /**
   * Unlock a user account (admin operation)
   */
  @Traced('AccountManagementService.unlockUserAccount')
  async unlockUserAccount(unlockData: { userId: string; reason?: string }, adminUserId: string): Promise<void> {
    this.logger.log(`Unlocking account for user: ${unlockData.userId} by admin: ${adminUserId}`);

    // Publish audit event for account unlock attempt
    await this.authEventPublisher.publishAuditEvent(
      AuthEventFactory.createAuditEvent({
        action: AccountManagementAuditAction.ACCOUNT_UNLOCK_INITIATED,
        userId: adminUserId, // The admin performing the action
        result: AuditResult.PENDING,
        ip: AUTH_CONSTANTS.UNKNOWN_VALUE,
        userAgent: AUTH_CONSTANTS.UNKNOWN_VALUE,

        // Target information - the user being unlocked
        targetType: 'USER_ACCOUNT',
        targetId: unlockData.userId,
        targetName: `Account unlock for user ${unlockData.userId}`,

        additionalData: {
          adminUserId: adminUserId,
          reason: unlockData.reason || 'No reason provided',
        },
      }),
    );

    try {
      // 1. Verify the user exists
      const userResponse = await firstValueFrom(
        this.userService.getUser({
          userId: unlockData.userId,
        }),
      );

      if (!userResponse.user) {
        // Publish audit event for user not found
        await this.authEventPublisher.publishAuditEvent(
          AuthEventFactory.createAuditEvent({
            action: AccountManagementAuditAction.ACCOUNT_UNLOCK_FAILED,
            userId: unlockData.userId,
            result: AuditResult.FAILURE,
            ip: AUTH_CONSTANTS.UNKNOWN_VALUE,
            userAgent: AUTH_CONSTANTS.UNKNOWN_VALUE,
            errorCode: 'USER_NOT_FOUND',
            errorMessage: 'User not found',
            additionalData: {
              adminUserId: adminUserId,
              reason: unlockData.reason || 'No reason provided',
            },
          }),
        );

        throw new NotFoundException('User not found');
      }

      // 2. Unlock the account via user service
      await firstValueFrom(
        this.userService.updateUser({
          userId: unlockData.userId,
          firstName: userResponse.user.firstName,
          lastName: userResponse.user.lastName,
          status: UserUserStatus.USER_STATUS_ACTIVE,
          isEmailVerified: userResponse.user.isEmailVerified,
        }),
      );

      this.logger.log(`Account unlocked successfully for user: ${unlockData.userId}`);

      // Publish successful audit event
      await this.authEventPublisher.publishAuditEvent(
        AuthEventFactory.createAuditEvent({
          action: AccountManagementAuditAction.ACCOUNT_UNLOCK_COMPLETED,
          userId: adminUserId, // The admin performing the action
          email: userResponse.user.email,
          tenantCode: userResponse.user.tenantCode,
          result: AuditResult.SUCCESS,
          ip: AUTH_CONSTANTS.UNKNOWN_VALUE,
          userAgent: AUTH_CONSTANTS.UNKNOWN_VALUE,

          // Target information - the user being unlocked
          targetType: 'USER_ACCOUNT',
          targetId: unlockData.userId,
          targetName: `${userResponse.user.firstName} ${userResponse.user.lastName}`,

          // Change tracking - account status change
          oldValues: {
            status: this.mapUserStatusToString(userResponse.user.status),
            lockedUntil: userResponse.user.lockedUntil,
          },
          newValues: {
            status: AuthStatus.ACTIVE,
            lockedUntil: null,
          },

          additionalData: {
            adminUserId: adminUserId,
            reason: unlockData.reason || 'No reason provided',
            previousStatus: this.mapUserStatusToString(userResponse.user.status),
            newStatus: AuthStatus.ACTIVE,
          },
        }),
      );

      // Method returns void - controller will create the response
    } catch (error) {
      this.logger.error(`Unlock account failed for user ${unlockData.userId}: ${error.message}`);

      // Publish failed audit event (if not already published)
      if (!(error instanceof NotFoundException)) {
        await this.authEventPublisher.publishAuditEvent(
          AuthEventFactory.createAuditEvent({
            action: AccountManagementAuditAction.ACCOUNT_UNLOCK_FAILED,
            userId: unlockData.userId,
            result: AuditResult.FAILURE,
            ip: AUTH_CONSTANTS.UNKNOWN_VALUE,
            userAgent: AUTH_CONSTANTS.UNKNOWN_VALUE,
            errorMessage: error.message,
            additionalData: {
              adminUserId: adminUserId,
              reason: unlockData.reason || 'No reason provided',
            },
          }),
        );
      }

      throw error;
    }
  }

  /**
   * Verify user email with verification token
   */
  @Traced('AccountManagementService.verifyEmail')
  async verifyEmail(verificationData: { token: string; ipAddress?: string; userAgent?: string }): Promise<{
    accessToken: string;
    refreshToken: string;
    user: {
      id: string;
      email: string;
      firstName: string;
      lastName: string;
      status: string;
      emailVerified: boolean;
      roles?: string[];
      tenantCode?: string;
      tenant?: {
        id: string;
        code: string;
        name: string;
      };
    };
  }> {
    this.logger.log(`Email verification request for token: ${verificationData.token}`);

    try {
      // 1. Get user ID from Redis using verification token
      const userId = await this.redis.get(`verification:${verificationData.token}`);
      this.logger.log(`Retrieved userId from Redis: ${userId}`);

      // If token not found, check if it might be for an already verified user
      if (!userId) {
        // Try to find if this token was used before by checking if any active user exists
        // This is a fallback check for better error messaging
        this.logger.log('Token not found in Redis, checking if user might be already verified');
        throw new BadRequestException('Invalid or expired verification token');
      }

      // 2. Update user status to active and verified (email verification)
      // First get the user to get current values
      this.logger.log(`Getting current user data for userId: ${userId}`);
      const currentUserResponse = await firstValueFrom(
        this.userService.getUser({
          userId,
        }),
      );

      if (!currentUserResponse.success || !currentUserResponse.user) {
        this.logger.error(`User not found for userId: ${userId}`);
        throw new BadRequestException('User not found');
      }

      const currentUser = currentUserResponse.user;
      this.logger.log(`Current user status: ${currentUser.status}, isEmailVerified: ${currentUser.isEmailVerified}`);

      // Check if user is already verified
      if (currentUser.isEmailVerified && currentUser.status === UserUserStatus.USER_STATUS_ACTIVE) {
        this.logger.warn(`Verification attempted for already verified user: ${userId}`);
        throw new BadRequestException('Account is already verified');
      }

      this.logger.log(`Updating user status to ACTIVE and isEmailVerified to true`);
      const updateResponse = await firstValueFrom(
        this.userService.updateUser({
          userId,
          firstName: currentUser.firstName,
          lastName: currentUser.lastName,
          status: UserUserStatus.USER_STATUS_ACTIVE, // Activate user
          isEmailVerified: true, // Set verified flag
        }),
      );

      this.logger.log(`Update response success: ${updateResponse.success}`);
      if (!updateResponse.success) {
        this.logger.error(`Failed to update user: ${JSON.stringify(updateResponse)}`);
        throw new BadRequestException('Failed to verify email');
      }

      // 3. Remove verification token from Redis
      await this.redis.del(`verification:${verificationData.token}`);

      // 4. Get updated user details
      const userResponse = await firstValueFrom(
        this.userService.getUser({
          userId,
        }),
      );

      if (!userResponse.success || !userResponse.user) {
        throw new BadRequestException('User not found');
      }

      const user = userResponse.user;

      // 5. Generate tokens for automatic login (no tenant required for email verification)
      const tokens = await this.generateTokens(user.id, user.email, user.tenantCode || null);

      // 6. Store refresh token
      await this.storeRefreshToken(user.id, tokens.refreshToken);

      // 7. Update last login info (since user is automatically logged in after verification)
      await firstValueFrom(
        this.userService.updateUserLoginInfo({
          userId: user.id,
          ipAddress: verificationData.ipAddress || AUTH_CONSTANTS.UNKNOWN_VALUE,
          userAgent: verificationData.userAgent || AUTH_CONSTANTS.UNKNOWN_VALUE,
        }),
      );

      // 8. Publish email verification completed audit event
      await this.authEventPublisher.publishAuditEvent(
        AuthEventFactory.createAuditEvent({
          action: EmailVerificationAuditAction.EMAIL_VERIFICATION_COMPLETED,
          userId: user.id,
          email: user.email,
          tenantCode: user.tenantCode,
          result: AuditResult.SUCCESS,
          ip: verificationData.ipAddress,
          userAgent: verificationData.userAgent,

          // Target information
          targetType: 'EMAIL_VERIFICATION',
          targetId: user.email,
          targetName: `Email verification for ${user.firstName} ${user.lastName}`,

          // Change tracking - email verification status change
          oldValues: {
            isEmailVerified: false,
            status: user.status,
          },
          newValues: {
            isEmailVerified: true,
            status: AuthStatus.ACTIVE,
          },

          additionalData: {
            verificationToken: verificationData.token.substring(0, 8), // First 8 chars for tracking
            completedAt: new Date().toISOString(),
          },
        }),
      );

      // 9. Publish welcome email requested audit event (async processing)
      await this.authEventPublisher.publishAuditEvent(
        AuthEventFactory.createAuditEvent({
          action: AccountManagementAuditAction.WELCOME_EMAIL_REQUESTED,
          userId: user.id,
          email: user.email,
          tenantCode: user.tenantCode,
          result: AuditResult.SUCCESS,
          ip: verificationData.ipAddress,
          userAgent: verificationData.userAgent,
          additionalData: {
            firstName: user.firstName,
            triggerType: EmailTriggerType.POST_VERIFICATION,
          },
        }),
      );

      this.logger.log(`Email verified successfully for user: ${user.id}`);

      return {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          status: user.status,
          emailVerified: user.isEmailVerified,
          roles: [], // Will be populated by role service if needed
          tenantCode: user.tenantCode,
          tenant: undefined, // TODO: Populate from tenant service if needed
        },
      };
    } catch (error) {
      this.logger.error(`Email verification failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Resend verification email
   */
  @Traced('AccountManagementService.resendVerification')
  async resendVerification(resendData: { email: string }): Promise<{ message: string }> {
    this.logger.log(`Resend verification request for email: ${resendData.email}`);

    // Publish audit event for resend verification attempt
    await this.authEventPublisher.publishAuditEvent(
      AuthEventFactory.createAuditEvent({
        action: EmailVerificationAuditAction.EMAIL_VERIFICATION_RESEND_REQUESTED,
        email: resendData.email,
        result: AuditResult.PENDING,
        ip: AUTH_CONSTANTS.UNKNOWN_VALUE,
        userAgent: AUTH_CONSTANTS.UNKNOWN_VALUE,
      }),
    );

    try {
      // 1. Check if user exists and is not verified
      const userResponse = await firstValueFrom(
        this.userService.getUserByEmail({
          email: resendData.email,
        }),
      );

      if (!userResponse.success || !userResponse.user) {
        // Don't reveal if email exists for security - return success anyway
        this.logger.warn(`Resend verification requested for non-existent email: ${resendData.email}`);

        // Publish audit event for non-existent email (security response)
        await this.authEventPublisher.publishAuditEvent(
          AuthEventFactory.createAuditEvent({
            action: EmailVerificationAuditAction.EMAIL_VERIFICATION_RESEND_REQUESTED,
            email: resendData.email,
            result: AuditResult.SUCCESS,
            ip: AUTH_CONSTANTS.UNKNOWN_VALUE,
            userAgent: AUTH_CONSTANTS.UNKNOWN_VALUE,
            additionalData: {
              securityResponse: true,
              reason: 'Email not found',
            },
          }),
        );

        return {
          message: 'If an unverified account exists with this email, a new verification email has been sent.',
        };
      }

      const user = userResponse.user;

      // 2. Check if user is already verified
      if (user.status === UserUserStatus.USER_STATUS_ACTIVE) {
        this.logger.warn(`Resend verification requested for already verified email: ${resendData.email}`);

        // Publish audit event for already verified email (security response)
        await this.authEventPublisher.publishAuditEvent(
          AuthEventFactory.createAuditEvent({
            action: EmailVerificationAuditAction.EMAIL_VERIFICATION_RESEND_REQUESTED,
            userId: user.id,
            email: resendData.email,
            tenantCode: user.tenantCode,
            result: AuditResult.SUCCESS,
            ip: AUTH_CONSTANTS.UNKNOWN_VALUE,
            userAgent: AUTH_CONSTANTS.UNKNOWN_VALUE,
            additionalData: {
              securityResponse: true,
              reason: 'Email already verified',
              accountStatus: this.mapUserStatusToString(user.status),
            },
          }),
        );

        return {
          message: 'If an unverified account exists with this email, a new verification email has been sent.',
        };
      }

      // 3. Generate new verification token
      const verificationToken = uuidv4();

      // 4. Store verification token in Redis with 48-hour expiry
      await this.redis.setex(`verification:${verificationToken}`, 48 * 60 * 60, user.id);

      // 5. Send verification email
      const emailSent = await this.sendVerificationEmail(user.email, user.firstName, verificationToken);

      if (emailSent) {
        this.logger.log(`Verification email resent successfully for: ${resendData.email}`);

        // Publish successful audit event
        await this.authEventPublisher.publishAuditEvent(
          AuthEventFactory.createAuditEvent({
            action: EmailVerificationAuditAction.EMAIL_VERIFICATION_RESEND_COMPLETED,
            userId: user.id,
            email: resendData.email,
            tenantCode: user.tenantCode,
            result: AuditResult.SUCCESS,
            ip: AUTH_CONSTANTS.UNKNOWN_VALUE,
            userAgent: AUTH_CONSTANTS.UNKNOWN_VALUE,
            additionalData: {
              verificationToken: verificationToken.substring(0, 8), // First 8 chars for tracking
              expiresAt: new Date(Date.now() + 48 * 60 * 60 * 1000).toISOString(),
            },
          }),
        );
      } else {
        this.logger.warn(`Verification email failed to send for: ${resendData.email}`);

        // Publish failed audit event for email sending failure
        await this.authEventPublisher.publishAuditEvent(
          AuthEventFactory.createAuditEvent({
            action: EmailVerificationAuditAction.EMAIL_VERIFICATION_RESEND_FAILED,
            userId: user.id,
            email: resendData.email,
            tenantCode: user.tenantCode,
            result: AuditResult.FAILURE,
            ip: AUTH_CONSTANTS.UNKNOWN_VALUE,
            userAgent: AUTH_CONSTANTS.UNKNOWN_VALUE,
            errorMessage: 'Failed to send verification email',
            additionalData: {
              verificationToken: verificationToken.substring(0, 8), // First 8 chars for tracking
            },
          }),
        );
      }

      return {
        message: 'If an unverified account exists with this email, a new verification email has been sent.',
      };
    } catch (error) {
      this.logger.error(`Resend verification failed for ${resendData.email}: ${error.message}`);

      // Publish failed audit event
      await this.authEventPublisher.publishAuditEvent(
        AuthEventFactory.createAuditEvent({
          action: EmailVerificationAuditAction.EMAIL_VERIFICATION_RESEND_FAILED,
          email: resendData.email,
          result: AuditResult.FAILURE,
          ip: AUTH_CONSTANTS.UNKNOWN_VALUE,
          userAgent: AUTH_CONSTANTS.UNKNOWN_VALUE,
          errorMessage: error.message,
          additionalData: {
            securityResponse: true,
          },
        }),
      );

      // Don't throw error to avoid revealing information about email existence
      return {
        message: 'If an unverified account exists with this email, a new verification email has been sent.',
      };
    }
  }

  /**
   * Generate JWT tokens for user authentication
   */
  @Traced('AccountManagementService.generateTokens')
  private async generateTokens(userId: string, email: string, tenantCode: string | null) {
    const now = Math.floor(Date.now() / 1000);

    const payload = {
      sub: userId, // Standard JWT subject claim
      email,
      tenantCode,
      iss: 'qeep-auth-service', // Issuer
      aud: 'qeep-api', // Audience
      iat: now, // Issued at time
    };

    // For refresh token, add a unique identifier to ensure uniqueness
    const refreshPayload = {
      ...payload,
      jti: `${userId}-${now}-${Math.random().toString(36).substring(2)}`, // Unique JWT ID
    };

    const jwtConfig = this.configService.getJwtConfig();

    // Add kid (key ID) to header for JWKS compatibility
    const accessToken = (jwt.sign as (payload: any, secret: string, options: any) => string)(payload, jwtConfig.secret, {
      expiresIn: jwtConfig.expiresIn,
      algorithm: 'HS256',
      header: {
        kid: 'qeep-key-1', // Key identifier
        typ: 'JWT',
        alg: 'HS256',
      },
    });

    const refreshToken = (jwt.sign as (payload: any, secret: string, options: any) => string)(refreshPayload, jwtConfig.refreshSecret, {
      expiresIn: jwtConfig.refreshExpiresIn,
      algorithm: 'HS256',
      header: {
        kid: 'qeep-refresh-key-1',
        typ: 'JWT',
        alg: 'HS256',
      },
    });

    return { accessToken, refreshToken };
  }

  /**
   * Store refresh token in Redis
   */
  @Traced('AccountManagementService.storeRefreshToken')
  private async storeRefreshToken(userId: string, refreshToken: string) {
    // Store refresh token in Redis with 7 days expiration
    await this.redis.setex(`refresh_token:${userId}`, 604800, refreshToken);
  }

  /**
   * Send verification email to user
   */
  @Traced('AccountManagementService.sendVerificationEmail')
  private async sendVerificationEmail(email: string, firstName: string, verificationToken: string): Promise<boolean> {
    try {
      // Create verification URL
      const baseUrl = this.configService.get('FRONTEND_URL') || AUTH_CONSTANTS.DEFAULT_FRONTEND_URL;
      const verificationUrl = `${baseUrl}/verify-email?token=${verificationToken}`;

      // Send email using notification service
      const emailRequest = {
        recipientEmail: email,
        recipientName: firstName,
        subject: 'Welcome to Qeep - Verify Your Email',
        templateSlug: 'email-verification',
        templateData: {
          firstName,
          verificationUrl,
          verificationToken,
        },
        tenantCode: null, // No default tenant - null is null
        metadata: {
          requestId: uuidv4(),
          userId: '',
          tenantCode: null,
          timestamp: new Date(),
        },
      };

      const response = await firstValueFrom(this.notificationService.sendEmail(emailRequest));

      if (response.success) {
        this.logger.log(`Verification email sent successfully to ${email}`);
        return true;
      } else {
        this.logger.error(`Failed to send verification email to ${email}: ${response.error?.message}`);
        return false;
      }
    } catch (error) {
      this.logger.error(`Error sending verification email to ${email}: ${error.message}`);
      // Don't throw error to prevent signup failure due to email issues
      return false;
    }
  }

  /**
   * Map UserUserStatus enum to AuthStatus
   */
  private mapUserStatusToString(status: UserUserStatus): string {
    switch (status) {
      case UserUserStatus.USER_STATUS_ACTIVE:
        return AuthStatus.ACTIVE;
      case UserUserStatus.USER_STATUS_PENDING:
        return AuthStatus.PENDING;
      case UserUserStatus.USER_STATUS_SUSPENDED:
        return AuthStatus.SUSPENDED;
      case UserUserStatus.USER_STATUS_INACTIVE:
        return AuthStatus.INACTIVE;
      case UserUserStatus.USER_STATUS_UNKNOWN:
        return AuthStatus.DELETED; // Map unknown to deleted for consistency
      default:
        return AuthStatus.DELETED;
    }
  }
}
