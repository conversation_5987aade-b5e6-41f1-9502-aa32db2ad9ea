export enum CredentialDeviceType {
  SINGLE_DEVICE = 'singleDevice',
  MULTI_DEVICE = 'multiDevice',
}

export enum ChallengeType {
  REGISTRATION = 'registration',
  AUTHENTICATION = 'authentication',
}

export enum UserStatus {
  PENDING_VERIFICATION = 'PENDING_VERIFICATION',
  ACTIVE = 'ACTIVE',
  SUSPENDED = 'SUSPENDED',
  DEACTIVATED = 'DEACTIVATED',
}

export enum SessionStatus {
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  REVOKED = 'REVOKED',
}

export enum AuthenticatorTransport {
  USB = 'usb',
  BLE = 'ble',
  NFC = 'nfc',
  INTERNAL = 'internal',
}

export enum TokenType {
  ACCESS = 'access',
  REFRESH = 'refresh',
  TEMPORARY = 'temporary',
  RESET = 'reset',
  VERIFICATION = 'verification',
}
