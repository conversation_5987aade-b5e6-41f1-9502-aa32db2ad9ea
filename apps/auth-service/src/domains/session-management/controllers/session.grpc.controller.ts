import { <PERSON>, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { Traced } from '@qeep/common';
import {
  CreateUserSessionRequest,
  CreateUserSessionResponse,
  GetUserSessionsRequest,
  GetUserSessionsResponse,
  TerminateAllUserSessionsRequest,
  TerminateAllUserSessionsResponse,
  TerminateUserSessionRequest,
  TerminateUserSessionResponse,
} from '@qeep/proto';
import { SessionService } from '../services/session.service';

@Controller()
export class SessionGrpcController {
  private readonly logger = new Logger(SessionGrpcController.name);

  constructor(private readonly sessionService: SessionService) {}

  @GrpcMethod('AuthService', 'CreateUserSession')
  @Traced('SessionGrpcController.createUserSession')
  async createUserSession(request: CreateUserSessionRequest): Promise<CreateUserSessionResponse> {
    this.logger.log(`gRPC CreateUserSession request for user: ${request.userId}`);

    try {
      const session = await this.sessionService.createUserSession({
        userId: request.userId,
        sessionToken: request.sessionToken,
        deviceId: request.deviceId,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        expiresAt: request.expiresAt,
      });

      return {
        success: true,
        session: this.mapSessionToProto(session),
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`CreateUserSession failed: ${error.message}`, error.stack);
      return {
        success: false,
        session: undefined,
        error: {
          code: 'SESSION_CREATION_FAILED',
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }

  @GrpcMethod('AuthService', 'GetUserSessions')
  @Traced('SessionGrpcController.getUserSessions')
  async getUserSessions(request: GetUserSessionsRequest): Promise<GetUserSessionsResponse> {
    this.logger.log(`gRPC GetUserSessions request for user: ${request.userId}`);

    try {
      const sessions = await this.sessionService.getUserSessions({
        userId: request.userId,
        activeOnly: request.activeOnly,
      });

      return {
        success: true,
        sessions: sessions.map((session) => this.mapSessionToProto(session)),
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`GetUserSessions failed: ${error.message}`, error.stack);
      return {
        success: false,
        sessions: [],
        error: {
          code: 'SESSION_RETRIEVAL_FAILED',
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }

  @GrpcMethod('AuthService', 'TerminateUserSession')
  @Traced('SessionGrpcController.terminateUserSession')
  async terminateUserSession(request: TerminateUserSessionRequest): Promise<TerminateUserSessionResponse> {
    this.logger.log(`gRPC TerminateUserSession request for user: ${request.userId}, session: ${request.sessionId}`);

    try {
      await this.sessionService.terminateUserSession({
        userId: request.userId,
        sessionId: request.sessionId,
      });

      return {
        success: true,
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`TerminateUserSession failed: ${error.message}`, error.stack);
      return {
        success: false,
        error: {
          code: 'SESSION_TERMINATION_FAILED',
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }

  @GrpcMethod('AuthService', 'TerminateAllUserSessions')
  @Traced('SessionGrpcController.terminateAllUserSessions')
  async terminateAllUserSessions(request: TerminateAllUserSessionsRequest): Promise<TerminateAllUserSessionsResponse> {
    this.logger.log(`gRPC TerminateAllUserSessions request for user: ${request.userId}`);

    try {
      const terminatedCount = await this.sessionService.terminateAllUserSessions({ userId: request.userId });

      return {
        success: true,
        sessionsTerminated: terminatedCount,
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`TerminateAllUserSessions failed: ${error.message}`, error.stack);
      return {
        success: false,
        sessionsTerminated: 0,
        error: {
          code: 'SESSION_TERMINATION_FAILED',
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }

  private mapSessionToProto(session: any): any {
    return {
      id: session.id,
      userId: session.userId,
      tenantId: session.tenantId || '', // Handle optional tenantId
      sessionToken: session.sessionToken,
      deviceId: (session.deviceInfo?.deviceId as string) || '',
      ipAddress: session.ipAddress || '',
      userAgent: session.userAgent || '',
      isActive: session.status === 'ACTIVE',
      expiresAt: session.expiresAt,
      createdAt: session.createdAt,
      updatedAt: session.lastAccessedAt || session.createdAt,
    };
  }
}
