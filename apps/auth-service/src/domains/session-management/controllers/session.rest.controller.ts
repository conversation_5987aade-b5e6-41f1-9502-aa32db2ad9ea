/* eslint-disable @typescript-eslint/no-explicit-any */
import { BadRequestException, Body, Controller, Delete, Get, HttpCode, HttpStatus, Logger, NotFoundException, Param, ParseUUIDPipe, UseGuards } from '@nestjs/common';
import { JwtAuthGuard, JwtUserId, ResponseUtil, StandardApiResponse, Traced } from '@qeep/common';
import { Auth } from '@qeep/contracts';
import { ZodError } from 'zod';
import { SessionService } from '../services/session.service';

@Controller('sessions')
@UseGuards(JwtAuthGuard)
export class SessionRestController {
  private readonly logger = new Logger(SessionRestController.name);

  constructor(private readonly sessionService: SessionService) {}

  /**
   * Get user's active sessions
   * GET /sessions
   */
  @Get()
  @Traced('SessionRestController.getUserSessions')
  async getUserSessions(@JwtUserId() userId: string): Promise<StandardApiResponse<any>> {
    this.logger.log(`Get sessions request for user: ${userId}`);

    try {
      // Get user's sessions
      const sessions = await this.sessionService.getUserSessions({
        userId,
        activeOnly: true,
      });

      // Transform to external format
      const activeCount = sessions.filter((s) => s.status === 'ACTIVE').length;

      this.logger.log(`Retrieved ${sessions.length} sessions for user: ${userId}`);
      return ResponseUtil.success(
        {
          sessions,
          totalCount: sessions.length,
          activeCount,
        },
        'Sessions retrieved successfully',
        200,
      );
    } catch (error) {
      this.logger.error(`Get sessions failed for user ${userId}: ${error.message}`, error.stack);
      throw new BadRequestException({
        message: 'Failed to retrieve sessions',
        error: error.message,
      });
    }
  }

  /**
   * Get current session details
   * GET /sessions/current
   */
  @Get('current')
  @Traced('SessionRestController.getCurrentSession')
  async getCurrentSession(@JwtUserId() userId: string): Promise<StandardApiResponse<any>> {
    this.logger.log(`Get current session request for user: ${userId}`);

    try {
      // Get user's sessions to find the current one
      const sessions = await this.sessionService.getUserSessions({
        userId,
        activeOnly: true,
      });

      if (sessions.length === 0) {
        throw new NotFoundException('No active sessions found');
      }

      // For now, return the most recent session as current
      // In a real implementation, you'd identify the current session by token
      const currentSession = sessions[0];

      this.logger.log(`Retrieved current session for user: ${userId}`);
      return ResponseUtil.success(currentSession, 'Current session retrieved successfully', 200);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Get current session failed for user ${userId}: ${error.message}`, error.stack);
      throw new BadRequestException({
        message: 'Failed to retrieve current session',
        error: error.message,
      });
    }
  }

  /**
   * Terminate a specific session
   * DELETE /sessions/:sessionId
   */
  @Delete(':sessionId')
  @HttpCode(HttpStatus.OK)
  @Traced('SessionRestController.terminateSession')
  async terminateSession(@Param('sessionId', ParseUUIDPipe) sessionId: string, @Body() requestBody: any, @JwtUserId() userId: string): Promise<StandardApiResponse<any>> {
    this.logger.log(`Terminate session request for session: ${sessionId}, user: ${userId}`);

    try {
      // Validate request body
      const validatedRequest = Auth.Schemas.TerminateSessionRequestSchema.parse(requestBody);

      // Terminate the specific session
      await this.sessionService.terminateUserSession({
        userId,
        sessionId,
      });

      this.logger.log(`Session ${sessionId} terminated for user: ${userId}`);
      return ResponseUtil.success({}, 'Session terminated successfully', 200);
    } catch (error) {
      if (error instanceof ZodError) {
        this.logger.error(`Terminate session validation failed: ${JSON.stringify(error.issues)}`);
        throw new BadRequestException({
          message: 'Validation failed',
          errors: error.issues.map((err: any) => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        });
      }

      this.logger.error(`Terminate session failed for session ${sessionId}, user ${userId}: ${error.message}`, error.stack);
      throw new BadRequestException({
        message: 'Failed to terminate session',
        error: error.message,
      });
    }
  }

  /**
   * Terminate all user sessions
   * DELETE /sessions
   */
  @Delete()
  @HttpCode(HttpStatus.OK)
  @Traced('SessionRestController.terminateAllSessions')
  async terminateAllSessions(@Body() requestBody: any, @JwtUserId() userId: string): Promise<StandardApiResponse<any>> {
    this.logger.log(`Terminate all sessions request for user: ${userId}`);

    try {
      // Validate request body
      const validatedRequest = Auth.Schemas.TerminateAllSessionsRequestSchema.parse(requestBody);

      // Terminate all user sessions
      const terminatedCount = await this.sessionService.terminateAllUserSessions({
        userId,
      });

      this.logger.log(`${terminatedCount} sessions terminated for user: ${userId}`);
      return ResponseUtil.success(terminatedCount, 'All sessions terminated successfully', 200);
    } catch (error) {
      if (error instanceof ZodError) {
        this.logger.error(`Terminate all sessions validation failed: ${JSON.stringify(error.issues)}`);
        throw new BadRequestException({
          message: 'Validation failed',
          errors: error.issues.map((err: any) => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        });
      }

      this.logger.error(`Terminate all sessions failed for user ${userId}: ${error.message}`, error.stack);
      throw new BadRequestException({
        message: 'Failed to terminate all sessions',
        error: error.message,
      });
    }
  }
}
