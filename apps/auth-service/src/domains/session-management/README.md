# Session Management REST API

This module provides REST endpoints for managing user sessions in the auth service.

## Endpoints

### 1. Get User Sessions
**GET** `/sessions`

Retrieves all active sessions for the authenticated user.

**Headers:**
```
Authorization: Bearer <access_token>
```

**Response:**
```json
{
  "success": true,
  "status_code": 200,
  "message": "Sessions retrieved successfully",
  "data": {
    "sessions": [
      {
        "id": "uuid",
        "user_id": "uuid",
        "tenant_id": "uuid",
        "tenant_code": "string",
        "status": "ACTIVE",
        "ip_address": "***********",
        "user_agent": "Mozilla/5.0...",
        "device_info": {
          "deviceId": "device123"
        },
        "location": null,
        "created_at": "2023-01-01T00:00:00.000Z",
        "last_accessed_at": "2023-01-01T00:00:00.000Z",
        "expires_at": "2023-01-08T00:00:00.000Z",
        "revoked_at": null,
        "revoked_by": null,
        "revoked_reason": null
      }
    ],
    "total_count": 1,
    "active_count": 1
  },
  "meta": {
    "timestamp": "2023-01-01T00:00:00.000Z",
    "request_id": "uuid"
  }
}
```

### 2. Get Current Session
**GET** `/sessions/current`

Retrieves details of the current session (the one used to make the request).

**Headers:**
```
Authorization: Bearer <access_token>
```

**Response:**
```json
{
  "success": true,
  "status_code": 200,
  "message": "Current session retrieved successfully",
  "data": {
    "session": {
      "id": "uuid",
      "user_id": "uuid",
      "tenant_id": "uuid",
      "tenant_code": "string",
      "status": "ACTIVE",
      "ip_address": "***********",
      "user_agent": "Mozilla/5.0...",
      "device_info": {
        "deviceId": "device123"
      },
      "location": null,
      "created_at": "2023-01-01T00:00:00.000Z",
      "last_accessed_at": "2023-01-01T00:00:00.000Z",
      "expires_at": "2023-01-08T00:00:00.000Z",
      "revoked_at": null,
      "revoked_by": null,
      "revoked_reason": null
    },
    "is_current": true
  },
  "meta": {
    "timestamp": "2023-01-01T00:00:00.000Z",
    "request_id": "uuid"
  }
}
```

### 3. Terminate Specific Session
**DELETE** `/sessions/:sessionId`

Terminates a specific session by ID.

**Headers:**
```
Authorization: Bearer <access_token>
```

**Request Body:**
```json
{
  "reason": "User requested session termination"
}
```

**Response:**
```json
{
  "success": true,
  "status_code": 200,
  "message": "Session terminated successfully",
  "data": {
    "session_id": "uuid",
    "terminated_at": "2023-01-01T00:00:00.000Z",
    "reason": "User requested session termination"
  },
  "meta": {
    "timestamp": "2023-01-01T00:00:00.000Z",
    "request_id": "uuid"
  }
}
```

### 4. Terminate All Sessions
**DELETE** `/sessions`

Terminates all sessions for the authenticated user.

**Headers:**
```
Authorization: Bearer <access_token>
```

**Request Body:**
```json
{
  "reason": "User requested termination of all sessions",
  "exclude_current": false
}
```

**Response:**
```json
{
  "success": true,
  "status_code": 200,
  "message": "All sessions terminated successfully",
  "data": {
    "terminated_count": 3,
    "terminated_at": "2023-01-01T00:00:00.000Z",
    "reason": "User requested termination of all sessions"
  },
  "meta": {
    "timestamp": "2023-01-01T00:00:00.000Z",
    "request_id": "uuid"
  }
}
```

## Error Responses

All endpoints follow the standard error response format:

```json
{
  "success": false,
  "status_code": 400,
  "message": "Error message",
  "error": {
    "message": "Detailed error message",
    "timestamp": "2023-01-01T00:00:00.000Z"
  },
  "meta": {
    "timestamp": "2023-01-01T00:00:00.000Z",
    "request_id": "uuid"
  }
}
```

## Security

- All endpoints require authentication via JWT token
- Users can only access and manage their own sessions
- Session termination is logged for audit purposes
- Proper validation is applied to all request bodies

## Usage Examples

### Using cURL

```bash
# Get all sessions
curl -X GET http://localhost:3000/sessions \
  -H "Authorization: Bearer <access_token>"

# Get current session
curl -X GET http://localhost:3000/sessions/current \
  -H "Authorization: Bearer <access_token>"

# Terminate specific session
curl -X DELETE http://localhost:3000/sessions/session-uuid \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{"reason": "Suspicious activity detected"}'

# Terminate all sessions
curl -X DELETE http://localhost:3000/sessions \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{"reason": "Security precaution", "exclude_current": false}'
```

### Using JavaScript/TypeScript

```typescript
import { Auth } from '@qeep/contracts';

// Get all sessions
const sessionsResponse = await fetch('/sessions', {
  headers: {
    'Authorization': `Bearer ${accessToken}`
  }
});
const sessions: Auth.DTOs.GetUserSessionsResponseDto = await sessionsResponse.json();

// Terminate specific session
const terminateResponse = await fetch(`/sessions/${sessionId}`, {
  method: 'DELETE',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    reason: 'User requested termination'
  })
});
```

## Integration

The session management REST controller is automatically included when the `SessionModule` is imported in your application module. It works alongside the existing gRPC session controller and uses the same underlying `SessionService`.
