/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable, Logger } from '@nestjs/common';
import { createId } from '@paralleldrive/cuid2';
import { TelemetryService, Traced } from '@qeep/common';
import { Auth } from '@qeep/contracts';
import { AuthPrismaService } from '../../../database/prisma.service';

// CUID generation function
function generateAuthSessionId(): string {
  return `ses_${createId()}`;
}

@Injectable()
export class SessionService {
  private readonly logger = new Logger(SessionService.name);

  constructor(private readonly prisma: AuthPrismaService, private readonly telemetryService: TelemetryService) {}

  @Traced('SessionService.createUserSession')
  async createUserSession(request: any): Promise<Auth.DTOs.SessionInternalDto> {
    // Record telemetry event for session creation
    this.telemetryService.recordEvent({
      name: 'user_session_creation_attempt',
      timestamp: new Date(),
      severity: 'info',
      attributes: {
        userId: request.userId,
        deviceId: request.deviceId || 'unknown',
        hasIpAddress: request.ipAddress ? 'true' : 'false',
        hasUserAgent: request.userAgent ? 'true' : 'false',
      },
    });

    try {
      // Validate and truncate data to fit database constraints
      const truncatedSessionToken = request.sessionToken?.substring(0, 255);
      const truncatedDeviceId = request.deviceId?.substring(0, 100) || null;
      const truncatedUserAgent = request.userAgent?.substring(0, 1000) || null; // Reasonable limit for user agent

      const session = await this.prisma.authSession.create({
        data: {
          id: generateAuthSessionId(),
          userId: request.userId,
          tenantId: null,
          tenantCode: null,
          sessionToken: truncatedSessionToken,
          refreshToken: null, // Will be set separately if needed
          status: 'ACTIVE',
          ipAddress: this.ensureStringOrNull(request.ipAddress),
          userAgent: truncatedUserAgent,
          deviceInfo: truncatedDeviceId ? { deviceId: truncatedDeviceId } : null,
          expiresAt: request.expiresAt || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days default
        },
      });

      // Record successful session creation
      this.telemetryService.recordEvent({
        name: 'user_session_created',
        timestamp: new Date(),
        severity: 'info',
        attributes: {
          userId: request.userId,
          sessionId: session.id,
          deviceId: request.deviceId || 'unknown',
        },
      });

      this.logger.log(`Session created for user: ${request.userId}, sessionId: ${session.id}`);

      // Transform Prisma session to contracts DTO format
      return this.transformSessionToDto(session);
    } catch (error) {
      // Record failed session creation
      this.telemetryService.recordEvent({
        name: 'user_session_creation_failed',
        timestamp: new Date(),
        severity: 'error',
        attributes: {
          userId: request.userId,
          error: error.message,
          errorCode: error.code || 'unknown',
        },
      });

      this.logger.error(`Failed to create session for user ${request.userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Traced('SessionService.getUserSessions')
  async getUserSessions(request: any): Promise<Auth.DTOs.SessionInternalDto[]> {
    this.logger.log(`Getting sessions for user: ${request.userId}, activeOnly: ${request.activeOnly}`);

    try {
      const whereClause: any = { userId: request.userId };

      if (request.activeOnly) {
        whereClause.status = 'ACTIVE';
        whereClause.expiresAt = { gt: new Date() };
      }

      const sessions = await this.prisma.authSession.findMany({
        where: whereClause,
        orderBy: { createdAt: 'desc' },
      });

      // Record telemetry for session retrieval
      this.telemetryService.recordEvent({
        name: 'user_sessions_retrieved',
        timestamp: new Date(),
        severity: 'info',
        attributes: {
          userId: request.userId,
          activeOnly: request.activeOnly.toString(),
          sessionCount: sessions.length.toString(),
        },
      });

      this.logger.log(`Found ${sessions.length} sessions for user: ${request.userId}`);

      // Transform Prisma sessions to contracts DTO format
      return sessions.map((session) => this.transformSessionToDto(session));
    } catch (error) {
      this.logger.error(`Failed to get sessions for user ${request.userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Traced('SessionService.getSessionByToken')
  async getSessionByToken(request: any): Promise<Auth.DTOs.SessionInternalDto | null> {
    this.logger.log(`Getting session by token`);

    try {
      const session = await this.prisma.authSession.findUnique({
        where: {
          sessionToken: request.sessionToken,
          status: 'ACTIVE',
          expiresAt: { gt: new Date() },
        },
      });

      // Transform Prisma session to contracts DTO format if session exists
      if (!session) {
        return null;
      }

      return this.transformSessionToDto(session);
    } catch (error) {
      this.logger.error(`Failed to get session by token: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Traced('SessionService.updateSessionActivity')
  async updateSessionActivity(request: any): Promise<void> {
    this.logger.log(`Updating session activity for token`);

    try {
      await this.prisma.authSession.update({
        where: { sessionToken: request.sessionToken },
        data: {
          lastAccessedAt: new Date(),
        },
      });

      this.logger.log(`Session activity updated for token`);
    } catch (error) {
      this.logger.error(`Failed to update session activity: ${error.message}`, error.stack);
      // Don't throw error for activity updates to avoid breaking the main flow
    }
  }

  @Traced('SessionService.terminateUserSession')
  async terminateUserSession(request: any): Promise<void> {
    this.logger.log(`Terminating session: ${request.sessionId} for user: ${request.userId}`);

    try {
      // Verify the session belongs to the user
      const session = await this.prisma.authSession.findFirst({
        where: {
          id: request.sessionId,
          userId: request.userId,
        },
      });

      if (!session) {
        throw new Error('Session not found or does not belong to user');
      }

      // Mark session as revoked
      await this.prisma.authSession.update({
        where: { id: request.sessionId },
        data: {
          status: 'REVOKED',
          revokedAt: new Date(),
          revokedReason: 'User initiated logout',
        },
      });

      // Record telemetry for session termination
      this.telemetryService.recordEvent({
        name: 'user_session_terminated',
        timestamp: new Date(),
        severity: 'info',
        attributes: {
          userId: request.userId,
          sessionId: request.sessionId,
          terminationType: 'individual',
        },
      });

      this.logger.log(`Session terminated: ${request.sessionId} for user: ${request.userId}`);
    } catch (error) {
      this.logger.error(`Failed to terminate session ${request.sessionId} for user ${request.userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Traced('SessionService.terminateSessionByToken')
  async terminateSessionByToken(request: any): Promise<void> {
    this.logger.log(`Terminating session by token`);

    try {
      const truncatedSessionToken = request.sessionToken?.substring(0, 255);

      // First, get the count of sessions to be updated for telemetry
      const sessionsToRevoke = await this.prisma.authSession.count({
        where: {
          sessionToken: truncatedSessionToken,
          status: 'ACTIVE',
        },
      });

      // Revoke the session instead of deleting to maintain audit trail
      const result = await this.prisma.authSession.updateMany({
        where: {
          sessionToken: truncatedSessionToken,
          status: 'ACTIVE',
        },
        data: {
          status: 'REVOKED',
          revokedAt: new Date(),
          revokedReason: 'Token-based logout',
        },
      });

      // Record telemetry for session termination
      this.telemetryService.recordEvent({
        name: 'user_session_terminated_by_token',
        timestamp: new Date(),
        severity: 'info',
        attributes: {
          sessionsTerminated: sessionsToRevoke.toString(),
          terminationType: 'by_token_revoked',
        },
      });

      this.logger.log(`Deleted ${result.count} session(s) by token`);
    } catch (error) {
      this.logger.error(`Failed to terminate session by token: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Traced('SessionService.terminateUserSessionsByDevice')
  async terminateUserSessionsByDevice(request: any): Promise<number> {
    this.logger.log(`Terminating sessions for user: ${request.userId} on device: ${request.deviceId}`);

    try {
      // First, get the count of sessions to be revoked for telemetry
      const sessionsToRevoke = await this.prisma.authSession.count({
        where: {
          userId: request.userId,
          deviceInfo: { path: ['deviceId'], equals: request.deviceId },
          status: 'ACTIVE',
        },
      });

      // Revoke sessions for the specific device to maintain audit trail
      const result = await this.prisma.authSession.updateMany({
        where: {
          userId: request.userId,
          deviceInfo: { path: ['deviceId'], equals: request.deviceId },
          status: 'ACTIVE',
        },
        data: {
          status: 'REVOKED',
          revokedAt: new Date(),
          revokedReason: 'Device-based logout',
        },
      });

      // Record telemetry for device-specific session termination
      this.telemetryService.recordEvent({
        name: 'user_sessions_terminated_by_device',
        timestamp: new Date(),
        severity: 'info',
        attributes: {
          userId: request.userId,
          deviceId: request.deviceId,
          sessionsTerminated: sessionsToRevoke.toString(),
          terminationType: 'by_device_revoked',
        },
      });

      this.logger.log(`Deleted ${result.count} sessions for user: ${request.userId} on device: ${request.deviceId}`);
      return result.count;
    } catch (error) {
      this.logger.error(`Failed to terminate sessions for user ${request.userId} on device ${request.deviceId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Traced('SessionService.terminateAllUserSessions')
  async terminateAllUserSessions(request: any): Promise<number> {
    this.logger.log(`Terminating all sessions for user: ${request.userId}`);

    try {
      // First, get the count of sessions to be revoked for telemetry
      const sessionsToRevoke = await this.prisma.authSession.count({
        where: {
          userId: request.userId,
          status: 'ACTIVE',
        },
      });

      // Delete all active sessions for the user to avoid unique constraint issues
      const result = await this.prisma.authSession.deleteMany({
        where: {
          userId: request.userId,
          status: 'ACTIVE',
        },
      });

      // Record telemetry for all sessions termination
      this.telemetryService.recordEvent({
        name: 'user_all_sessions_terminated',
        timestamp: new Date(),
        severity: 'info',
        attributes: {
          userId: request.userId,
          sessionsTerminated: sessionsToRevoke.toString(),
          terminationType: 'all_sessions_deleted',
        },
      });

      this.logger.log(`Deleted ${result.count} sessions for user: ${request.userId}`);
      return result.count;
    } catch (error) {
      this.logger.error(`Failed to terminate all sessions for user ${request.userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Traced('SessionService.cleanupExpiredSessions')
  async cleanupExpiredSessions(): Promise<number> {
    this.logger.log('Cleaning up expired sessions');

    try {
      // Mark expired sessions as expired instead of deleting
      const result = await this.prisma.authSession.updateMany({
        where: {
          expiresAt: { lt: new Date() },
          status: { not: 'EXPIRED' },
        },
        data: {
          status: 'EXPIRED',
        },
      });

      // Record telemetry for cleanup
      this.telemetryService.recordEvent({
        name: 'expired_sessions_cleaned',
        timestamp: new Date(),
        severity: 'info',
        attributes: {
          sessionsDeleted: result.count.toString(),
        },
      });

      this.logger.log(`Cleaned up ${result.count} expired sessions`);
      return result.count;
    } catch (error) {
      this.logger.error(`Failed to cleanup expired sessions: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Transform Prisma session object to contracts DTO format
   */
  private transformSessionToDto(session: any): any {
    return {
      id: session.id,
      userId: session.userId,
      tenantId: session.tenantId,
      tenantCode: session.tenantCode,
      sessionToken: session.sessionToken,
      refreshToken: session.refreshToken,
      status: session.status,
      ipAddress: session.ipAddress,
      userAgent: session.userAgent,
      deviceInfo: session.deviceInfo as Record<any, unknown>,
      location: session.location as Record<any, unknown>,
      createdAt: session.createdAt,
      lastAccessedAt: session.lastAccessedAt,
      expiresAt: session.expiresAt,
      revokedAt: session.revokedAt,
      revokedBy: session.revokedBy,
      revokedReason: session.revokedReason,
    };
  }

  /**
   * Ensure a value is a string or null, not an object
   */
  private ensureStringOrNull(value: any): string | null {
    if (value === null || value === undefined) {
      return null;
    }

    if (typeof value === 'string') {
      return value;
    }

    if (typeof value === 'object') {
      // If it's an object, try to extract a meaningful string value
      if (value.toString && typeof value.toString === 'function') {
        const stringValue = value.toString();
        // Avoid "[object Object]" strings
        if (stringValue !== '[object Object]') {
          return stringValue;
        }
      }

      // If it's an object with an ip property, extract it
      if (value.ip && typeof value.ip === 'string') {
        return value.ip;
      }

      // If it's an object with an address property, extract it
      if (value.address && typeof value.address === 'string') {
        return value.address;
      }

      // Log the problematic object for debugging
      this.logger.warn(`Received object instead of string for IP address: ${JSON.stringify(value)}`);
      return null;
    }

    // Convert other types to string
    return String(value);
  }
}
