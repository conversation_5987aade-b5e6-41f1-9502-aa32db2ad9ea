import { Module } from '@nestjs/common';
import { CommonModule } from '@qeep/common';
import { AuthPrismaService } from '../../database/prisma.service';
import { SessionGrpcController } from './controllers/session.grpc.controller';
import { SessionRestController } from './controllers/session.rest.controller';
import { SessionService } from './services/session.service';

@Module({
  imports: [CommonModule.forRoot()],
  controllers: [SessionGrpcController, SessionRestController],
  providers: [SessionService, AuthPrismaService],
  exports: [SessionService],
})
export class SessionModule {}
