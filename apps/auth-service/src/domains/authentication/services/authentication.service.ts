/* eslint-disable @typescript-eslint/no-explicit-any */
import { BadRequestException, Inject, Injectable, Logger, OnModuleInit, UnauthorizedException } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { createId } from '@paralleldrive/cuid2';
import { ConfigService, Traced } from '@qeep/common';
import { Auth, LoginInternalDto, LoginResponseDto, TokenRefreshRequestDto, TokenRefreshResponseDto } from '@qeep/contracts';
import { UserServiceClient, UserUserStatus } from '@qeep/proto';
import * as bcrypt from 'bcryptjs';
import Redis from 'ioredis';
import * as jwt from 'jsonwebtoken';
import { firstValueFrom } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';
import { AUTH_CONSTANTS } from '../../../core/constants/auth.constants';
import { AuthEventPublisher } from '../../../events/auth-event-publisher.service';
import { AuthEventFactory } from '../../../events/auth.events';
import { SessionService } from '../../session-management/services/session.service';
import { MfaService } from '../../mfa/services/mfa.service';

// CUID generation function for session tokens
function generateSessionToken(): string {
  return `tok_${createId()}`;
}

@Injectable()
export class AuthenticationService implements OnModuleInit {
  private readonly logger = new Logger(AuthenticationService.name);
  private userService: UserServiceClient;
  private redis: Redis;

  constructor(
    @Inject('USER_PACKAGE') private userClient: ClientGrpc,
    private configService: ConfigService,
    private readonly authEventPublisher: AuthEventPublisher,
    private readonly sessionService: SessionService,
    private readonly mfaService: MfaService,
  ) {}

  onModuleInit() {
    this.userService = this.userClient.getService<UserServiceClient>('UserService');

    // Initialize Redis
    const redisConfig = this.configService.getRedisConfig();
    this.redis = new Redis({
      host: redisConfig.host,
      port: redisConfig.port,
      password: redisConfig.password,
      maxRetriesPerRequest: 3,
    });
  }

  /** Login user */
  @Traced('AuthenticationService.login')
  async login(dto: LoginInternalDto): Promise<LoginResponseDto> {
    await this.publishLoginInitiatedEvent(dto);

    try {
      // 1. Validate login prerequisites
      await this.validateAccountLockout(dto);
      const user = await this.validateUserCredentials(dto);
      await this.validateUserStatus(user, dto);
      await this.validateEmailVerification(user, dto);

      // 2. Process successful login
      await this.clearAccountLockout(dto);
      const tokens = await this.generateTokens(user.id, user.email, dto.tenantId);
      const sessionData = await this.setupUserSession(user, dto, tokens);
      const { userRoles, userPermissions } = await this.fetchUserRolesAndPermissions(user, dto);
      
      // Check if user has MFA enabled
      const mfaStatus = await this.mfaService.getMfaStatus(user.id);
      const requiresMfa = mfaStatus?.enabled || false;
      const mfaMethods = requiresMfa && mfaStatus?.primaryMethod ? [mfaStatus.primaryMethod] : mfaStatus?.methods || [];

      // 3. Complete login process
      await this.publishLoginSuccessEvent(user, dto, tokens);

      return this.buildLoginResponse(
        user, 
        tokens, 
        userRoles, 
        userPermissions, 
        dto, 
        sessionData, 
        requiresMfa, 
        mfaMethods
      );
    } catch (error) {
      this.logger.error(`Login failed for ${dto.email}: ${error.message}`);

      // Publish failed login audit event
      await this.authEventPublisher.publishAuditEvent(
        AuthEventFactory.createAuditEvent({
          action: Auth.Enums.LoginAuditAction.USER_LOGIN_FAILED,
          email: dto.email,
          tenantCode: dto.tenantId,
          result: Auth.Enums.AuditResult.FAILURE,
          ip: dto.ipAddress,
          userAgent: dto.userAgent,
          errorCode: (error as any).code || 'UNKNOWN_ERROR',
          errorMessage: error.message,
          additionalData: {
            deviceId: dto.deviceId,
          },
        }),
      );

      throw error;
    }
  }

  /** Refresh authentication tokens */
  @Traced('AuthenticationService.refreshToken')
  async refreshToken(dto: TokenRefreshRequestDto): Promise<TokenRefreshResponseDto> {
    try {
      const { refreshToken } = dto;

      // 1. Verify refresh token
      const jwtConfig = this.configService.getJwtConfig();
      const decoded = jwt.verify(refreshToken, jwtConfig.refreshSecret) as any;

      // 2. Check if refresh token exists in Redis
      const storedToken = await this.redis.get(`refresh_token:${decoded.userId}`);
      if (!storedToken || storedToken !== refreshToken) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      // 3. Get user details
      const userResponse = await firstValueFrom(
        this.userService.getUser({
          userId: decoded.userId,
        }),
      );

      if (!userResponse.success || !userResponse.user) {
        throw new UnauthorizedException('User not found');
      }

      const user = userResponse.user;

      // 4. Check if user is still active
      if (user.status !== UserUserStatus.USER_STATUS_ACTIVE) {
        throw new UnauthorizedException('Account is not active');
      }

      // 5. Generate new tokens
      const tokens = await this.generateTokens(user.id, user.email, decoded.tenantCode);

      // 6. Store new refresh token
      await this.storeRefreshToken(user.id, tokens.refreshToken);

      return {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        expiresIn: 3600, // 1 hour
        tokenType: 'Bearer',
      };
    } catch (error) {
      this.logger.error(`Token refresh failed: ${error.message}`);
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  /** Logout user and invalidate session */
  @Traced('AuthenticationService.logout')
  async logout(userId: string, accessToken?: string, refreshToken?: string): Promise<void> {
    try {
      const promises: Promise<any>[] = [];

      // 1. Add access token to blacklist if provided
      if (accessToken) {
        const decoded = jwt.decode(accessToken) as any;
        if (decoded && decoded.exp) {
          const currentTime = Math.floor(Date.now() / 1000);
          const ttl = decoded.exp - currentTime;
          // Only blacklist if token hasn't expired yet (ttl > 0)
          if (ttl > 0) {
            promises.push(this.redis.setex(`blacklist:${accessToken}`, ttl, 'true'));
          } else {
            this.logger.debug(`Skipping blacklist for expired token (exp: ${decoded.exp}, now: ${currentTime})`);
          }
        }
      }

      // 2. Remove specific refresh token if provided, otherwise remove all user refresh tokens
      if (refreshToken) {
        // Verify the refresh token belongs to this user before removing
        try {
          const jwtConfig = this.configService.getJwtConfig();
          const decoded = jwt.verify(refreshToken, jwtConfig.refreshSecret) as any;
          if (decoded.userId === userId) {
            promises.push(this.redis.del(`refresh_token:${userId}`));
          }
        } catch (error) {
          this.logger.warn(`Invalid refresh token provided during logout for user ${userId}: ${error.message}`);
        }
      } else {
        // Remove all refresh tokens for this user
        promises.push(this.redis.del(`refresh_token:${userId}`));
      }

      // 3. Clear any user sessions (if session management is implemented)
      promises.push(this.redis.del(`user_sessions:${userId}`));

      // Execute Redis operations first
      await Promise.all(promises);

      // 4. Delete all sessions from the database for this user
      // This ensures complete cleanup and prevents unique constraint issues on next login
      try {
        const terminatedCount = await this.sessionService.terminateAllUserSessions({ userId });
        this.logger.log(`Successfully deleted ${terminatedCount} sessions for user: ${userId}`);
      } catch (error) {
        this.logger.error(`Failed to delete sessions during logout for user ${userId}: ${error.message}`);
        // Don't throw error to avoid breaking logout flow
      }

      this.logger.log(`User logged out successfully: ${userId}`);
    } catch (error) {
      this.logger.error(`Logout failed for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /** Revoke a specific refresh token */
  @Traced('AuthenticationService.revokeToken')
  async revokeToken(userId: string, refreshToken: string): Promise<void> {
    try {
      const jwtConfig = this.configService.getJwtConfig();

      // Verify the refresh token belongs to this user
      const decoded = jwt.verify(refreshToken, jwtConfig.refreshSecret) as any;
      if (decoded.userId !== userId) {
        throw new UnauthorizedException('Token does not belong to the authenticated user');
      }

      // Remove the specific refresh token
      await this.redis.del(`refresh_token:${userId}`);

      this.logger.log(`Refresh token revoked for user: ${userId}`);
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      this.logger.error(`Token revocation failed for user ${userId}: ${error.message}`);
      throw new BadRequestException('Invalid refresh token');
    }
  }

  /** Revoke all tokens for a user */
  @Traced('AuthenticationService.revokeAllTokens')
  async revokeAllTokens(userId: string): Promise<{ revokedTokensCount: number }> {
    try {
      const promises: Promise<any>[] = [];
      let revokedCount = 0;

      // 1. Remove all refresh tokens for this user
      const refreshTokenKey = `refresh_token:${userId}`;
      const refreshTokenExists = await this.redis.exists(refreshTokenKey);
      if (refreshTokenExists) {
        promises.push(this.redis.del(refreshTokenKey));
        revokedCount++;
      }

      // 2. Clear all user sessions
      const sessionKey = `user_sessions:${userId}`;
      const sessionExists = await this.redis.exists(sessionKey);
      if (sessionExists) {
        promises.push(this.redis.del(sessionKey));
      }

      // 3. Add user to force-logout list (this will invalidate all existing access tokens)
      const forceLogoutKey = `force_logout:${userId}`;
      const currentTime = Math.floor(Date.now() / 1000);
      promises.push(this.redis.setex(forceLogoutKey, 86400, currentTime.toString())); // 24 hours

      await Promise.all(promises);

      this.logger.log(`All tokens revoked for user: ${userId}, count: ${revokedCount}`);
      return { revokedTokensCount: revokedCount };
    } catch (error) {
      this.logger.error(`Revoke all tokens failed for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  @Traced('AuthenticationService.generateTokens')
  private async generateTokens(userId: string, email: string, tenantCode: string | null) {
    const now = Math.floor(Date.now() / 1000);

    const payload = {
      sub: userId, // Standard JWT subject claim
      userId, // Custom claim for backward compatibility
      email,
      tenantCode,
      iss: 'qeep-auth-service', // Issuer
      aud: 'qeep-api', // Audience
      iat: now, // Issued at time
    };

    // For refresh token, add a unique identifier to ensure uniqueness
    const refreshPayload = {
      ...payload,
      jti: `${userId}-${now}-${Math.random().toString(36).substring(2)}`, // Unique JWT ID
    };

    const jwtConfig = this.configService.getJwtConfig();

    // Add kid (key ID) to header for JWKS compatibility
    const accessToken = (jwt.sign as any)(payload, jwtConfig.secret, {
      expiresIn: jwtConfig.expiresIn,
      algorithm: 'HS256',
      header: {
        kid: 'qeep-key-1', // Key identifier
        typ: 'JWT',
        alg: 'HS256',
      },
    });

    const refreshToken = (jwt.sign as any)(refreshPayload, jwtConfig.refreshSecret, {
      expiresIn: jwtConfig.refreshExpiresIn,
      algorithm: 'HS256',
      header: {
        kid: 'qeep-refresh-key-1',
        typ: 'JWT',
        alg: 'HS256',
      },
    });

    return { accessToken, refreshToken };
  }

  @Traced('AuthenticationService.storeRefreshToken')
  private async storeRefreshToken(userId: string, refreshToken: string) {
    // Store refresh token in Redis with 7 days expiration
    await this.redis.setex(`refresh_token:${userId}`, 604800, refreshToken);
  }

  @Traced('AuthenticationService.handleFailedLogin')
  private async handleFailedLogin(email: string, tenantCode: string) {
    const lockoutKey = `lockout:${email}:${tenantCode}`;
    const attemptsKey = `attempts:${email}:${tenantCode}`;

    // Increment failed attempts
    const attempts = await this.redis.incr(attemptsKey);
    await this.redis.expire(attemptsKey, 900); // 15 minutes

    // Lock account after 5 failed attempts
    if (attempts >= 5) {
      const lockoutData = {
        lockedUntil: Date.now() + 900000, // 15 minutes
        attempts,
      };
      await this.redis.setex(lockoutKey, 900, JSON.stringify(lockoutData));
      await this.redis.del(attemptsKey);
    }
  }

  private mapUserStatusToString(status: UserUserStatus): Auth.Enums.AuthStatus {
    switch (status) {
      case UserUserStatus.USER_STATUS_ACTIVE:
        return Auth.Enums.AuthStatus.ACTIVE;
      case UserUserStatus.USER_STATUS_PENDING:
        return Auth.Enums.AuthStatus.PENDING;
      case UserUserStatus.USER_STATUS_SUSPENDED:
        return Auth.Enums.AuthStatus.SUSPENDED;
      case UserUserStatus.USER_STATUS_INACTIVE:
        return Auth.Enums.AuthStatus.INACTIVE;
      default:
        return Auth.Enums.AuthStatus.DELETED;
    }
  }

  @Traced('AuthenticationService.publishLoginInitiatedEvent')
  private async publishLoginInitiatedEvent(dto: LoginInternalDto): Promise<void> {
    await this.authEventPublisher.publishAuditEvent(
      AuthEventFactory.createAuditEvent({
        action: Auth.Enums.LoginAuditAction.USER_LOGIN_INITIATED,
        email: dto.email,
        tenantCode: dto.tenantId,
        result: Auth.Enums.AuditResult.PENDING,
        ip: dto.ipAddress,
        userAgent: dto.userAgent,

        // Target information
        targetType: 'USER_SESSION',
        targetId: dto.email,
        targetName: `Login attempt for ${dto.email}`,
      }),
    );
  }

  @Traced('AuthenticationService.validateAccountLockout')
  private async validateAccountLockout(dto: LoginInternalDto): Promise<void> {
    // Skip in dev mode
    if (this.configService.isDevelopment()) {
      return;
    }

    const lockoutKey = `lockout:${dto.email}:${dto.tenantId}`;
    const lockoutData = await this.redis.get(lockoutKey);

    if (lockoutData) {
      const lockout = JSON.parse(lockoutData);
      if (lockout.lockedUntil > Date.now()) {
        // Publish account locked audit event
        await this.authEventPublisher.publishAuditEvent(
          AuthEventFactory.createAuditEvent({
            action: Auth.Enums.LoginAuditAction.ACCOUNT_LOCKED,
            email: dto.email,
            tenantCode: dto.tenantId,
            result: Auth.Enums.AuditResult.FAILURE,
            ip: dto.ipAddress,
            userAgent: dto.userAgent,
            errorCode: Auth.Enums.AuthErrorCode.ACCOUNT_LOCKED,
            errorMessage: 'Account temporarily locked due to multiple failed login attempts',
            additionalData: {
              lockedUntil: new Date(lockout.lockedUntil).toISOString(),
              deviceId: dto.deviceId,
            },
          }),
        );

        const error = new UnauthorizedException('Account temporarily locked due to multiple failed login attempts');
        (error as any).code = Auth.Enums.AuthErrorCode.ACCOUNT_LOCKED;
        throw error;
      }
    }
  }

  @Traced('AuthenticationService.validateUserCredentials')
  private async validateUserCredentials(dto: LoginInternalDto): Promise<any> {
    // Get user by email and tenant
    const userResponse = await firstValueFrom(
      this.userService.getUserByEmail({
        email: dto.email,
        tenantCode: dto.tenantId,
      }),
    );

    if (!userResponse.success || !userResponse.user) {
      await this.handleFailedLogin(dto.email, dto.tenantId);
      const error = new UnauthorizedException('Invalid credentials');
      (error as any).code = Auth.Enums.AuthErrorCode.INVALID_CREDENTIALS;
      throw error;
    }

    const user = userResponse.user;

    // Verify password
    const isPasswordValid = await bcrypt.compare(dto.password, user.passwordHash);
    if (!isPasswordValid) {
      await this.handleFailedLogin(dto.email, dto.tenantId);
      const error = new UnauthorizedException('Invalid credentials');
      (error as any).code = Auth.Enums.AuthErrorCode.INVALID_CREDENTIALS;
      throw error;
    }

    return user;
  }

  @Traced('AuthenticationService.validateUserStatus')
  private async validateUserStatus(user: any, dto: LoginInternalDto): Promise<void> {
    if (user.status !== UserUserStatus.USER_STATUS_ACTIVE) {
      // Publish login blocked audit event
      await this.authEventPublisher.publishAuditEvent(
        AuthEventFactory.createAuditEvent({
          action: Auth.Enums.LoginAuditAction.LOGIN_BLOCKED,
          userId: user.id,
          email: dto.email,
          tenantCode: dto.tenantId,
          result: Auth.Enums.AuditResult.FAILURE,
          ip: dto.ipAddress,
          userAgent: dto.userAgent,
          errorCode: Auth.Enums.AuthErrorCode.ACCOUNT_INACTIVE,
          errorMessage: 'Account is not active',
          additionalData: {
            accountStatus: this.mapUserStatusToString(user.status),
            deviceId: dto.deviceId,
          },
        }),
      );

      const error = new UnauthorizedException('Account is not active');
      (error as any).code = Auth.Enums.AuthErrorCode.ACCOUNT_INACTIVE;
      throw error;
    }
  }

  @Traced('AuthenticationService.validateEmailVerification')
  private async validateEmailVerification(user: any, dto: LoginInternalDto): Promise<void> {
    if (!user.isEmailVerified) {
      // Publish login blocked audit event for unverified email
      await this.authEventPublisher.publishAuditEvent(
        AuthEventFactory.createAuditEvent({
          action: Auth.Enums.LoginAuditAction.LOGIN_BLOCKED,
          userId: user.id,
          email: dto.email,
          tenantCode: dto.tenantId,
          result: Auth.Enums.AuditResult.FAILURE,
          ip: dto.ipAddress,
          userAgent: dto.userAgent,
          errorCode: Auth.Enums.AuthErrorCode.EMAIL_NOT_VERIFIED,
          errorMessage: 'Email not verified. Please check your email and verify your account.',
          additionalData: {
            emailVerified: false,
            deviceId: dto.deviceId,
          },
        }),
      );

      const error = new UnauthorizedException('Email not verified. Please check your email and verify your account.');
      (error as any).code = Auth.Enums.AuthErrorCode.EMAIL_NOT_VERIFIED;
      throw error;
    }
  }

  @Traced('AuthenticationService.clearAccountLockout')
  private async clearAccountLockout(dto: LoginInternalDto): Promise<void> {
    const lockoutKey = `lockout:${dto.email}:${dto.tenantId}`;
    await this.redis.del(lockoutKey);
  }

  @Traced('AuthenticationService.setupUserSession')
  private async setupUserSession(user: any, dto: LoginInternalDto, tokens: any): Promise<any> {
    // Invalidate existing sessions for this user (optional)
    // This prevents multiple active sessions and resolves unique constraint issues
    try {
      const terminatedCount = await this.sessionService.terminateAllUserSessions({ userId: user.id });
      this.logger.log(`Terminated ${terminatedCount} existing sessions for user ${user.id}`);
    } catch (error) {
      // Don't fail login if session cleanup fails
      this.logger.warn(`Failed to terminate existing sessions for user ${user.id}: ${error.message}`);
    }

    // Generate a unique session token for this login
    const sessionToken = generateSessionToken();

    // Create new session using local session service
    const sessionData = await this.sessionService.createUserSession({
      userId: user.id,
      sessionToken,
      deviceId: dto.deviceId || AUTH_CONSTANTS.UNKNOWN_VALUE,
      ipAddress: dto.ipAddress || AUTH_CONSTANTS.UNKNOWN_VALUE,
      userAgent: dto.userAgent || AUTH_CONSTANTS.UNKNOWN_VALUE,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    });

    // Store refresh token in Redis for quick access
    await this.storeRefreshToken(user.id, tokens.refreshToken);

    // Update last login
    await firstValueFrom(
      this.userService.updateUserLoginInfo({
        userId: user.id,
        ipAddress: dto.ipAddress || AUTH_CONSTANTS.UNKNOWN_VALUE,
        userAgent: dto.userAgent || AUTH_CONSTANTS.UNKNOWN_VALUE,
      }),
    );

    return sessionData;
  }

  @Traced('AuthenticationService.fetchUserRolesAndPermissions')
  private async fetchUserRolesAndPermissions(user: any, dto: LoginInternalDto): Promise<{ userRoles: string[]; userPermissions: string[] }> {
    let userRoles: string[] = [];
    let userPermissions: string[] = [];

    try {
      // Fetch user roles
      const rolesResponse = await firstValueFrom(
        this.userService.getUserRoles({
          userId: user.id,
          metadata: {
            requestId: uuidv4(),
            sourceIp: dto.ipAddress || '',
            userAgent: dto.userAgent || '',
            timestamp: new Date(),
          },
        }),
      );

      if (rolesResponse.success && rolesResponse.roles) {
        userRoles = rolesResponse.roles.map((role) => role.name);
      }
    } catch (rolesError) {
      this.logger.warn(`Failed to fetch user roles for ${user.id}: ${rolesError.message}`);
      // Don't fail login if role fetching fails, just log the warning
    }

    try {
      // Fetch user permissions
      const permissionsResponse = await firstValueFrom(
        this.userService.getUserPermissions({
          userId: user.id,
          metadata: {
            requestId: uuidv4(),
            sourceIp: dto.ipAddress || '',
            userAgent: dto.userAgent || '',
            timestamp: new Date(),
          },
        }),
      );

      if (permissionsResponse.success && permissionsResponse.permissions) {
        userPermissions = permissionsResponse.permissions;
      }
    } catch (permissionsError) {
      this.logger.warn(`Failed to fetch user permissions for ${user.id}: ${permissionsError.message}`);
      // Don't fail login if permission fetching fails, just log the warning
    }

    return { userRoles, userPermissions };
  }

  @Traced('AuthenticationService.publishLoginSuccessEvent')
  private async publishLoginSuccessEvent(user: any, dto: LoginInternalDto, tokens: any): Promise<void> {
    this.logger.log(`Login successful for user: ${user.id}`);

    // Publish successful login audit event
    await this.authEventPublisher.publishAuditEvent(
      AuthEventFactory.createAuditEvent({
        action: Auth.Enums.LoginAuditAction.USER_LOGIN_COMPLETED,
        userId: user.id,
        email: dto.email,
        tenantCode: dto.tenantId,
        result: Auth.Enums.AuditResult.SUCCESS,
        ip: dto.ipAddress,
        userAgent: dto.userAgent,

        // Target information
        targetType: 'USER_SESSION',
        targetId: user.id,
        targetName: `${user.firstName} ${user.lastName} Login`,

        // Change tracking - login status change
        oldValues: {
          lastLoginAt: user.lastLoginAt ? new Date(user.lastLoginAt).toISOString() : null,
          lastLoginIp: user.lastLoginIp,
          lastLoginUserAgent: user.lastLoginUserAgent,
          loginAttempts: user.loginAttempts || 0,
        },
        newValues: {
          lastLoginAt: new Date().toISOString(),
          lastLoginIp: dto.ipAddress,
          lastLoginUserAgent: dto.userAgent,
          loginAttempts: 0, // Reset on successful login
        },

        additionalData: {
          sessionId: tokens.refreshToken.substring(0, 8), // First 8 chars for tracking
          deviceId: dto.deviceId,
        },
      }),
    );
  }

  @Traced('AuthenticationService.buildLoginResponse')
  private buildLoginResponse(
    user: any, 
    tokens: any, 
    userRoles: string[], 
    userPermissions: string[], 
    dto: LoginInternalDto, 
    sessionData: any, 
    requiresMfa = false, 
    mfaMethods: Auth.Enums.MfaMethod[] = []
  ): LoginResponseDto {
    return {
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      expiresIn: 3600, // 1 hour
      tokenType: 'Bearer',
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        status: this.mapUserStatusToString(user.status),
        isEmailVerified: user.isEmailVerified,
        roles: userRoles,
        permissions: userPermissions,
        tenant: dto.tenantId
          ? {
              id: dto.tenantId,
              code: '', // TODO: Get from tenant service
              name: '', // TODO: Get from tenant service
            }
          : undefined,
        lastLoginAt: user.lastLoginAt ? user.lastLoginAt.toISOString() : null,
        profileCompletion: undefined, // TODO: Calculate profile completion
        nextStep: undefined, // TODO: Determine next onboarding step
      },
      session: {
        id: sessionData.id,
        status: Auth.Enums.SessionStatus.ACTIVE,
        createdAt: sessionData.createdAt.toISOString(),
        expiresAt: sessionData.expiresAt.toISOString(),
        lastActivityAt: sessionData.lastAccessedAt ? sessionData.lastAccessedAt.toISOString() : sessionData.createdAt.toISOString(),
        sessionToken: sessionData.sessionToken,
        device: sessionData.deviceInfo?.deviceId ? { id: sessionData.deviceInfo.deviceId } : undefined,
        ipAddress: sessionData.ipAddress,
        userAgent: sessionData.userAgent,
      },
      requiresMfa, // Use actual MFA status
      mfaMethods // Use available MFA methods
    };
  }
}
