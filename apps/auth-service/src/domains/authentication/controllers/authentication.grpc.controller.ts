/* eslint-disable @typescript-eslint/no-explicit-any */
import { Controller, Logger } from '@nestjs/common';
import { LoginRequest, LoginResponse, RefreshTokenRequest, RefreshTokenResponse } from '@qeep/proto';

import { GrpcMethod } from '@nestjs/microservices';
import { AuthenticationService } from '../services/authentication.service';

@Controller()
export class AuthenticationGrpcController {
  private readonly logger = new Logger(AuthenticationGrpcController.name);

  constructor(private readonly authenticationService: AuthenticationService) {}

  @GrpcMethod('AuthService', 'Login')
  async login(request: LoginRequest): Promise<LoginResponse> {
    this.logger.log(`gRPC Login request for email: ${request.email}`);

    try {
      // Extract IP and user agent from gRPC metadata if available
      const ipAddress = request.metadata?.sourceIp || 'grpc-unknown';
      const userAgent = request.metadata?.userAgent || 'grpc-client';

      const result = await this.authenticationService.login({
        email: request.email,
        password: request.password,
        rememberMe: false, // Default to false for gRPC requests
        tenantId: request.tenantCode,
        ipAddress: ipAddress,
        userAgent,
      });

      return {
        success: true,
        accessToken: result.accessToken,
        refreshToken: result.refreshToken,
        expiresIn: result.expiresIn.toString(),
        user: {
          id: result.user.id,
          email: result.user.email,
          firstName: '',
          lastName: '',
          isEmailVerified: false,
          status: 1 as any, // USER_STATUS_ACTIVE
          createdAt: new Date(), // TODO: Add these fields to the service response
          updatedAt: new Date(),
        },
      };
    } catch (error) {
      this.logger.error(`gRPC Login failed: ${error.message}`);
      return {
        success: false,
        accessToken: '',
        refreshToken: '',
        expiresIn: '0',
        user: null,
      };
    }
  }

  @GrpcMethod('AuthService', 'RefreshToken')
  async refreshToken(request: RefreshTokenRequest): Promise<RefreshTokenResponse> {
    this.logger.log(`gRPC RefreshToken request`);

    try {
      const result = await this.authenticationService.refreshToken(request);

      return {
        success: true,
        accessToken: result.accessToken,
        refreshToken: result.refreshToken,
        expiresIn: result.expiresIn.toString(),
      };
    } catch (error) {
      this.logger.error(`gRPC RefreshToken failed: ${error.message}`);
      return {
        success: false,
        accessToken: '',
        refreshToken: '',
        expiresIn: '0',
      };
    }
  }

  @GrpcMethod('AuthService', 'Logout')
  async logout(request: { userId: string; token?: string; deviceId?: string }): Promise<{ success: boolean; message: string }> {
    this.logger.log(`gRPC Logout request for user: ${request.userId}`);

    try {
      await this.authenticationService.logout(request.userId, request.token);

      return {
        success: true,
        message: 'Logout successful',
      };
    } catch (error) {
      this.logger.error(`gRPC Logout failed: ${error.message}`);
      return {
        success: false,
        message: error.message,
      };
    }
  }
}
