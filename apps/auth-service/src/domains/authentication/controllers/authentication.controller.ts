/* eslint-disable @typescript-eslint/no-explicit-any */
import { BadRequestException, Body, Controller, HttpCode, HttpStatus, Logger, Post, Request, UseGuards } from '@nestjs/common';
import { ClientIp, ConfigService, JwtAuthGuard, JwtUserId, ResponseUtil, StandardApiResponse, TenantId, Traced, UserAgent, ZodValidationPipe } from '@qeep/common';
import {
  Auth,
  LoginRequestDto,
  LoginRequestSchema,
  LoginResponseDto,
  LogoutRequestDto,
  LogoutResponseDto,
  RevokeAllTokensResponseDto,
  RevokeTokenRequestDto,
  RevokeTokenRequestSchema,
  RevokeTokenResponseDto,
  TokenRefreshRequestDto,
  TokenRefreshRequestSchema,
} from '@qeep/contracts';
import * as jwt from 'jsonwebtoken';
import { AuthenticationService } from '../services/authentication.service';

@Controller('auth')
export class AuthenticationRestController {
  private readonly logger = new Logger(AuthenticationRestController.name);

  constructor(private readonly authenticationService: AuthenticationService, private readonly configService: ConfigService) {}

  @Post('login')
  @Traced('AuthenticationRestController.login')
  async login(
    @Body(new ZodValidationPipe(LoginRequestSchema)) loginDto: LoginRequestDto,
    @ClientIp() ipAddress: string,
    @UserAgent() userAgent: string,
    @TenantId() tenantId?: string,
  ): Promise<StandardApiResponse<LoginResponseDto>> {
    try {
      const loginResult = await this.authenticationService.login({
        ...loginDto,
        tenantId: tenantId || loginDto.tenantId,
        ipAddress,
        userAgent,
      });

      return ResponseUtil.success(loginResult, 'Login successful', HttpStatus.OK);
    } catch (error) {
      this.logger.error(`Login failed: ${error.message}`);
      throw error;
    }
  }

  @Post('refresh-token')
  @Traced('AuthenticationRestController.refreshToken')
  async refreshToken(
    @Body(new ZodValidationPipe(TokenRefreshRequestSchema)) refreshTokenDto: TokenRefreshRequestDto,
  ): Promise<StandardApiResponse<Auth.DTOs.TokenRefreshResponseDto>> {
    try {
      const refreshTokenResult = await this.authenticationService.refreshToken(refreshTokenDto);

      return ResponseUtil.success(refreshTokenResult, 'Token refreshed successfully');
    } catch (error) {
      this.logger.error(`Token refresh failed: ${error.message}`);
      throw error;
    }
  }

  @Post('logout')
  @HttpCode(HttpStatus.OK)
  @Traced('AuthenticationRestController.logout')
  async logout(@Body(new ZodValidationPipe(Auth.Schemas.LogoutRequestSchema)) logoutDto: LogoutRequestDto, @Request() req: any): Promise<StandardApiResponse<LogoutResponseDto>> {
    const accessToken = req.headers.authorization?.replace('Bearer ', '');

    try {
      // Extract user ID from the refresh token instead of requiring JWT validation
      let userId: string | null = null;

      if (logoutDto.refreshToken) {
        try {
          const jwtConfig = this.configService.getJwtConfig();
          const decoded = jwt.verify(logoutDto.refreshToken, jwtConfig.refreshSecret) as any;
          userId = decoded.userId;
        } catch (error) {
          this.logger.warn(`Invalid refresh token provided during logout: ${error.message}`);
        }
      }

      // If we can't get user ID from refresh token, try from access token
      if (!userId && accessToken) {
        try {
          const jwtConfig = this.configService.getJwtConfig();
          const decoded = jwt.verify(accessToken, jwtConfig.secret) as any;
          userId = decoded.userId;
        } catch (error) {
          this.logger.warn(`Invalid access token provided during logout: ${error.message}`);
        }
      }

      if (userId) {
        this.logger.log(`Proceeding with logout for user: ${userId}`);
        await this.authenticationService.logout(userId, accessToken, logoutDto.refreshToken);
      } else {
        this.logger.warn('Logout attempted without valid user identification');
      }

      // Create response directly using DTO interface (camelCase)
      const response: Auth.DTOs.LogoutResponseDto = {
        loggedOutAt: new Date().toISOString(),
      };

      return ResponseUtil.success(response, 'Logged out successfully');
    } catch (error) {
      this.logger.error(`Logout failed: ${error.message}`);
      throw new BadRequestException('Logout failed');
    }
  }

  @Post('revoke-token')
  @UseGuards(JwtAuthGuard)
  @Traced('AuthenticationRestController.revokeToken')
  async revokeToken(
    @Body(new ZodValidationPipe(RevokeTokenRequestSchema)) revokeTokenDto: RevokeTokenRequestDto,
    @JwtUserId() userId: string,
  ): Promise<StandardApiResponse<RevokeTokenResponseDto>> {
    try {
      await this.authenticationService.revokeToken(userId, revokeTokenDto.refreshToken);

      // Create response directly using DTO interface (camelCase)
      const response: RevokeTokenResponseDto = {
        revokedAt: new Date().toISOString(),
        tokenId: revokeTokenDto.refreshToken.substring(0, 8), // First 8 chars for tracking
      };

      return ResponseUtil.success(response, 'Token revoked successfully');
    } catch (error) {
      this.logger.error(`Token revocation failed for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  @Post('revoke-all-tokens')
  @UseGuards(JwtAuthGuard)
  @Traced('AuthenticationRestController.revokeAllTokens')
  async revokeAllTokens(@JwtUserId() userId: string): Promise<StandardApiResponse<RevokeAllTokensResponseDto>> {
    this.logger.log(`Revoke all tokens request for user: ${userId}`);

    try {
      const result = await this.authenticationService.revokeAllTokens(userId);

      // Create response directly using DTO interface (camelCase)
      const response: RevokeAllTokensResponseDto = {
        revokedTokensCount: result.revokedTokensCount,
        revokedAt: new Date().toISOString(),
      };

      return ResponseUtil.success(response, 'All tokens revoked successfully');
    } catch (error) {
      this.logger.error(`Revoke all tokens failed for user ${userId}: ${error.message}`);
      throw new BadRequestException('Failed to revoke all tokens');
    }
  }
}
