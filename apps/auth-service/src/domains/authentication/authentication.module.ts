import { ClientsModule, Transport } from '@nestjs/microservices';

import { Module } from '@nestjs/common';
import { ConfigService, ProtoConfigService } from '@qeep/common';
import { AuthEventPublisher } from '../../events/auth-event-publisher.service';
import { MfaModule } from '../mfa/mfa.module';
import { SessionModule } from '../session-management/session.module';
import { AuthenticationRestController } from './controllers/authentication.controller';
import { AuthenticationGrpcController } from './controllers/authentication.grpc.controller';
import { AuthenticationService } from './services/authentication.service';

@Module({
  imports: [
    SessionModule,
    MfaModule,
    ClientsModule.registerAsync([
      {
        name: 'USER_PACKAGE',
        imports: [],
        useFactory: (configService: ConfigService, protoConfig: ProtoConfigService) => ({
          transport: Transport.GRPC,
          options: {
            package: 'user',
            protoPath: protoConfig.getProtoPath('user', 'user.proto'),
            url: configService.getServiceUrl('user-service-grpc'),
            loader: protoConfig.getLoaderOptions(),
            defaults: true,
            oneofs: true,
          },
        }),
        inject: [ConfigService, ProtoConfigService],
      },
      {
        name: 'TENANT_PACKAGE',
        imports: [],
        useFactory: (configService: ConfigService, protoConfig: ProtoConfigService) => ({
          transport: Transport.GRPC,
          options: {
            package: 'tenant',
            protoPath: protoConfig.getProtoPath('tenant', 'tenant.proto'),
            url: configService.getServiceUrl('tenant-service-grpc'),
            loader: protoConfig.getLoaderOptions(),
            defaults: true,
            oneofs: true,
          },
        }),
        inject: [ConfigService, ProtoConfigService],
      },
      {
        name: 'NOTIFICATION_PACKAGE',
        imports: [],
        useFactory: (configService: ConfigService, protoConfig: ProtoConfigService) => ({
          transport: Transport.GRPC,
          options: {
            package: 'notification',
            protoPath: protoConfig.getProtoPath('notification', 'notification.proto'),
            url: configService.getServiceUrl('notification-service-grpc'),
            loader: protoConfig.getLoaderOptions(),
            defaults: true,
            oneofs: true,
          },
        }),
        inject: [ConfigService, ProtoConfigService],
      },
    ]),
  ],
  controllers: [AuthenticationGrpcController, AuthenticationRestController],
  providers: [AuthenticationService, AuthEventPublisher],
  exports: [AuthenticationService],
})
export class AuthenticationModule {}
