import { Controller, Get, HttpStatus, Res } from '@nestjs/common';
import { HealthStatus, LivenessStatus, ReadinessStatus } from '@qeep/proto';
import { Response } from 'express';
import { HealthService } from '../services/health.service';

@Controller('health')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  // General health endpoint - Returns overall health status of the Auth Service
  @Get()
  async getHealth(@Res() res: Response): Promise<void> {
    try {
      const healthResponse = await this.healthService.getHealth();
      const statusCode = this.getHttpStatusCode(healthResponse.status);
      res.status(statusCode).json(healthResponse);
    } catch (error) {
      res.status(HttpStatus.SERVICE_UNAVAILABLE).json({
        status: HealthStatus.HEALTH_STATUS_UNHEALTHY,
        message: 'Health check failed',
        error: error.message,
      });
    }
  }

  // Readiness probe endpoint - Indicates if the service is ready to accept traffic
  @Get('ready')
  async getReadiness(@Res() res: Response): Promise<void> {
    try {
      const readinessResponse = await this.healthService.getReadiness();
      const statusCode = readinessResponse.status === ReadinessStatus.READINESS_STATUS_READY ? HttpStatus.OK : HttpStatus.SERVICE_UNAVAILABLE;
      res.status(statusCode).json(readinessResponse);
    } catch (error) {
      res.status(HttpStatus.SERVICE_UNAVAILABLE).json({
        status: 'not_ready',
        timestamp: new Date(),
        message: 'Readiness check failed',
        error: error.message,
      });
    }
  }

  // Liveness probe endpoint - Indicates if the service is alive
  @Get('live')
  async getLiveness(@Res() res: Response): Promise<void> {
    try {
      const livenessResponse = await this.healthService.getLiveness();
      const statusCode = livenessResponse.status === LivenessStatus.LIVENESS_STATUS_ALIVE ? HttpStatus.OK : HttpStatus.SERVICE_UNAVAILABLE;
      res.status(statusCode).json(livenessResponse);
    } catch (error) {
      res.status(HttpStatus.SERVICE_UNAVAILABLE).json({
        status: LivenessStatus.LIVENESS_STATUS_DEAD,
        timestamp: new Date(),
        message: 'Liveness check failed',
        error: error.message,
      });
    }
  }

  // Detailed health endpoint - Returns comprehensive health information
  @Get('detailed')
  async getDetailedHealth(@Res() res: Response): Promise<void> {
    try {
      const detailedHealthResponse = await this.healthService.getDetailedHealth();
      const statusCode = this.getHttpStatusCode(detailedHealthResponse.status);
      res.status(statusCode).json(detailedHealthResponse);
    } catch (error) {
      res.status(HttpStatus.SERVICE_UNAVAILABLE).json({
        status: HealthStatus.HEALTH_STATUS_UNHEALTHY,
        message: 'Detailed health check failed',
        error: error.message,
      });
    }
  }

  // Convert health status to HTTP status code
  private getHttpStatusCode(healthStatus: HealthStatus): number {
    switch (healthStatus) {
      case HealthStatus.HEALTH_STATUS_HEALTHY:
        return HttpStatus.OK;
      case HealthStatus.HEALTH_STATUS_DEGRADED:
        return HttpStatus.OK; // Still accepting traffic but with warnings
      case HealthStatus.HEALTH_STATUS_UNHEALTHY:
        return HttpStatus.SERVICE_UNAVAILABLE;
      default:
        return HttpStatus.SERVICE_UNAVAILABLE;
    }
  }
}
