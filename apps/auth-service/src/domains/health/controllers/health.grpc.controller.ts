import { <PERSON>, Logger } from '@nestjs/common';
import {
  CommonRequestMetadata,
  CommonResponseMetadata,
  DetailedHealthCheckResponse,
  HealthCheckRequest,
  HealthCheckResponse,
  HealthStatus,
  LivenessCheckResponse,
  LivenessStatus,
  ReadinessCheckResponse,
  ReadinessStatus,
  ServiceHealth,
  SystemInfo,
} from '@qeep/proto';

import { GrpcMethod } from '@nestjs/microservices';
import { HealthService } from '../services/health.service';

@Controller()
export class HealthGrpcController {
  private readonly logger = new Logger(HealthGrpcController.name);

  constructor(private readonly healthService: HealthService) {}

  // Basic health check via gRPC
  @GrpcMethod('HealthService', 'Check')
  async check(request: HealthCheckRequest): Promise<HealthCheckResponse> {
    const startTime = Date.now();

    try {
      this.logger.debug('Received gRPC health check request', { service: request.service });

      const healthResponse = await this.healthService.getHealth();

      return {
        status: this.mapHealthStatusToGrpc(healthResponse.status),
        system: this.mapSystemInfoToGrpc(healthResponse.system),
        message: healthResponse.message || '',
        metadata: this.buildResponseMetadata(request.metadata, startTime),
      };
    } catch (error) {
      this.logger.error('gRPC health check failed', error);
      throw error;
    }
  }

  // Readiness check via gRPC
  @GrpcMethod('HealthService', 'CheckReadiness')
  async checkReadiness(request: HealthCheckRequest): Promise<ReadinessCheckResponse> {
    const startTime = Date.now();

    try {
      this.logger.debug('Received gRPC readiness check request', { service: request.service });

      const readinessResponse = await this.healthService.getReadiness();

      return {
        status: readinessResponse.status,
        timestamp: readinessResponse.timestamp,
        checks: readinessResponse.checks,
        message: readinessResponse.message,
        metadata: this.buildResponseMetadata(request.metadata, startTime),
      };
    } catch (error) {
      this.logger.error('gRPC readiness check failed', error);
      return {
        status: ReadinessStatus.READINESS_STATUS_NOT_READY,
        timestamp: new Date(),
        checks: {},
        message: 'Readiness check failed',
        metadata: this.buildResponseMetadata(request.metadata, startTime),
      };
    }
  }

  // Liveness check via gRPC
  @GrpcMethod('HealthService', 'CheckLiveness')
  async checkLiveness(request: HealthCheckRequest): Promise<LivenessCheckResponse> {
    const startTime = Date.now();

    try {
      this.logger.debug('Received gRPC liveness check request', { service: request.service });

      const livenessResponse = await this.healthService.getLiveness();

      return {
        status: livenessResponse.status,
        timestamp: livenessResponse.timestamp,
        uptime: livenessResponse.uptime,
        memoryUsage: livenessResponse.memoryUsage,
        message: livenessResponse.message,
        metadata: this.buildResponseMetadata(request.metadata, startTime),
      };
    } catch (error) {
      this.logger.error('gRPC liveness check failed', error);
      return {
        status: LivenessStatus.LIVENESS_STATUS_DEAD,
        timestamp: new Date(),
        uptime: '0',
        memoryUsage: { used: 0, total: 0, percentage: 0 },
        message: 'Liveness check failed',
        metadata: this.buildResponseMetadata(request.metadata, startTime),
      };
    }
  }

  // Detailed health check via gRPC
  @GrpcMethod('HealthService', 'CheckDetailed')
  async checkDetailed(request: HealthCheckRequest): Promise<DetailedHealthCheckResponse> {
    const startTime = Date.now();

    try {
      this.logger.debug('Received gRPC detailed health check request', { service: request.service });

      const detailedResponse = await this.healthService.getDetailedHealth();

      return {
        status: this.mapHealthStatusToGrpc(detailedResponse.status),
        system: this.mapSystemInfoToGrpc(detailedResponse.system),
        dependencies: detailedResponse.dependencies.map((dep) => this.mapServiceHealthToGrpc(dep)),
        checks: detailedResponse.checks,
        message: detailedResponse.message,
        metadata: this.buildResponseMetadata(request.metadata, startTime),
      };
    } catch (error) {
      this.logger.error('gRPC detailed health check failed', error);
      return {
        status: HealthStatus.HEALTH_STATUS_UNHEALTHY,
        system: this.getDefaultSystemInfo(),
        dependencies: [],
        checks: {},
        message: 'Detailed health check failed',
        metadata: this.buildResponseMetadata(request.metadata, startTime),
      };
    }
  }

  // Map internal health status to gRPC health status
  private mapHealthStatusToGrpc(status: HealthStatus): HealthStatus {
    return status; // Direct mapping since they're the same enum
  }

  // Map internal system info to gRPC system info
  private mapSystemInfoToGrpc(systemInfo: SystemInfo): SystemInfo {
    return {
      service: systemInfo.service,
      version: systemInfo.version,
      environment: systemInfo.environment,
      uptime: systemInfo.uptime,
      timestamp: systemInfo.timestamp,
      nodeVersion: systemInfo.nodeVersion,
      memoryUsage: systemInfo.memoryUsage,
      cpuUsage: systemInfo.cpuUsage,
    };
  }

  // Map internal service health to gRPC service health
  private mapServiceHealthToGrpc(serviceHealth: ServiceHealth): ServiceHealth {
    return {
      name: serviceHealth.name,
      status: serviceHealth.status,
      responseTime: serviceHealth.responseTime,
      lastChecked: serviceHealth.lastChecked,
      error: serviceHealth.error || '',
      url: serviceHealth.url || '',
    };
  }

  // Build response metadata
  private buildResponseMetadata(requestMetadata: CommonRequestMetadata | undefined, startTime: number): CommonResponseMetadata {
    return {
      requestId: requestMetadata?.requestId || '',
      timestamp: new Date(),
      processingTime: Date.now() - startTime,
    };
  }

  // Get default system info for error cases
  private getDefaultSystemInfo(): SystemInfo {
    return {
      service: 'auth-service',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'unknown',
      uptime: '0',
      timestamp: new Date(),
      nodeVersion: process.version,
      memoryUsage: { used: 0, total: 0, percentage: 0 },
      cpuUsage: 0,
    };
  }
}
