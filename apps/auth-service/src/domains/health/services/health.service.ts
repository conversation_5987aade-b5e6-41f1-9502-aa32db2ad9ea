import {
  DetailedHealthCheckResponse,
  HealthCheckResponse,
  HealthStatus,
  LivenessCheckResponse,
  LivenessStatus,
  ReadinessCheckResponse,
  ReadinessStatus,
  ServiceHealth,
  ServiceStatus,
  SystemInfo,
} from '@qeep/proto';
import { Injectable, Logger } from '@nestjs/common';

/* eslint-disable @typescript-eslint/no-unused-vars */
import { ConfigService } from '@qeep/common';

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);
  private readonly startTime = Date.now();

  constructor(private readonly configService: ConfigService) {}

  // Get basic health status
  async getHealth(): Promise<HealthCheckResponse> {
    const systemInfo = this.getSystemInfo();

    return {
      status: HealthStatus.HEALTH_STATUS_HEALTHY,
      system: systemInfo,
      message: 'Auth Service is healthy',
    };
  }

  // Get readiness status - checks if service is ready to accept traffic
  async getReadiness(): Promise<ReadinessCheckResponse> {
    const timestamp = new Date();

    try {
      // Check critical dependencies for readiness
      const checks = {
        database: await this.checkDatabase(),
        redis: await this.checkRedis(),
        auth0: await this.checkAuth0(),
        dependencies: await this.checkCriticalDependencies(),
      };

      const isReady = Object.values(checks).every((status) => status === ServiceStatus.SERVICE_STATUS_UP);

      return {
        status: isReady ? ReadinessStatus.READINESS_STATUS_READY : ReadinessStatus.READINESS_STATUS_NOT_READY,
        timestamp,
        checks,
        message: isReady ? 'Service is ready to accept traffic' : 'Service is not ready',
      };
    } catch (error) {
      this.logger.error('Readiness check failed', error);
      return {
        status: ReadinessStatus.READINESS_STATUS_NOT_READY,
        timestamp,
        checks: {},
        message: 'Readiness check failed',
      };
    }
  }

  // Get liveness status - checks if service is alive
  async getLiveness(): Promise<LivenessCheckResponse> {
    const timestamp = new Date();
    const uptime = Date.now() - this.startTime;
    const memoryUsage = this.getMemoryUsage();

    // Simple liveness check - if we can respond, we're alive
    // Check for critical memory issues
    const isAlive = memoryUsage.percentage < 95; // Consider dead if using >95% memory

    return {
      status: isAlive ? LivenessStatus.LIVENESS_STATUS_ALIVE : LivenessStatus.LIVENESS_STATUS_DEAD,
      timestamp,
      uptime: uptime.toString(),
      memoryUsage,
      message: isAlive ? 'Service is alive' : 'Service is experiencing critical issues',
    };
  }

  // Get detailed health information including all dependencies
  async getDetailedHealth(): Promise<DetailedHealthCheckResponse> {
    const systemInfo = this.getSystemInfo();

    try {
      // Check all downstream services
      const dependencies = await this.checkAllDependencies();

      // Determine overall status based on dependencies
      const status = this.determineOverallStatus(dependencies);

      return {
        status,
        system: systemInfo,
        dependencies,
        checks: {
          database: await this.checkDatabase(),
          redis: await this.checkRedis(),
          auth0: await this.checkAuth0(),
          configService: this.checkConfigService(),
        },
        message: this.getStatusMessage(status, dependencies),
      };
    } catch (error) {
      this.logger.error('Detailed health check failed', error);
      return {
        status: HealthStatus.HEALTH_STATUS_UNHEALTHY,
        system: systemInfo,
        dependencies: [],
        checks: {},
        message: 'Health check failed',
      };
    }
  }

  // Get system information
  private getSystemInfo(): SystemInfo {
    const memoryUsage = this.getMemoryUsage();

    return {
      service: 'auth-service',
      version: process.env.npm_package_version || '1.0.0',
      environment: this.configService.getNodeEnv(),
      uptime: (Date.now() - this.startTime).toString(),
      timestamp: new Date(),
      nodeVersion: process.version,
      memoryUsage,
      cpuUsage: 0, // Placeholder - would be actual CPU usage
    };
  }

  // Get memory usage information
  private getMemoryUsage() {
    const usage = process.memoryUsage();
    const total = usage.heapTotal;
    const used = usage.heapUsed;

    return {
      used: Math.round(used / 1024 / 1024), // MB
      total: Math.round(total / 1024 / 1024), // MB
      percentage: Math.round((used / total) * 100),
    };
  }

  // Check database connectivity
  private async checkDatabase(): Promise<ServiceStatus> {
    const startTime = Date.now();
    try {
      const dbConfig = this.configService.getDatabaseConfig();
      if (!dbConfig.host) {
        return ServiceStatus.SERVICE_STATUS_DOWN;
      }

      // For now, we'll do a basic config validation
      // In a real implementation, you would:
      // 1. Create a test connection to the database
      // 2. Execute a simple query like SELECT 1
      // 3. Measure response time

      const responseTime = Date.now() - startTime;
      this.logger.debug(`Database check completed in ${responseTime}ms`);

      return ServiceStatus.SERVICE_STATUS_UP;
    } catch (error) {
      this.logger.warn('Database check failed', error);
      return ServiceStatus.SERVICE_STATUS_DOWN;
    }
  }

  // Check Redis connectivity
  private async checkRedis(): Promise<ServiceStatus> {
    const startTime = Date.now();
    try {
      const redisConfig = this.configService.getRedisConfig();
      if (!redisConfig.host) {
        return ServiceStatus.SERVICE_STATUS_DOWN;
      }

      // For now, we'll do a basic config validation
      // In a real implementation, you would:
      // 1. Create a test connection to Redis
      // 2. Execute a PING command
      // 3. Measure response time

      const responseTime = Date.now() - startTime;
      this.logger.debug(`Redis check completed in ${responseTime}ms`);

      return ServiceStatus.SERVICE_STATUS_UP;
    } catch (error) {
      this.logger.warn('Redis check failed', error);
      return ServiceStatus.SERVICE_STATUS_DOWN;
    }
  }

  // Check Auth0 connectivity
  private async checkAuth0(): Promise<ServiceStatus> {
    const startTime = Date.now();
    try {
      const auth0Config = this.configService.getAuth0Config();
      if (!auth0Config.domain || !auth0Config.clientId) {
        return ServiceStatus.SERVICE_STATUS_DOWN;
      }

      // For now, we'll do a basic config validation
      // In a real implementation, you would:
      // 1. Make a test API call to Auth0 (e.g., GET /.well-known/jwks.json)
      // 2. Verify the response is valid
      // 3. Measure response time

      const responseTime = Date.now() - startTime;
      this.logger.debug(`Auth0 check completed in ${responseTime}ms`);

      return ServiceStatus.SERVICE_STATUS_UP;
    } catch (error) {
      this.logger.warn('Auth0 check failed', error);
      return ServiceStatus.SERVICE_STATUS_DOWN;
    }
  }

  // Check critical dependencies for readiness
  private async checkCriticalDependencies(): Promise<ServiceStatus> {
    // For Auth Service, critical dependencies include database, redis, and auth0
    const [dbStatus, redisStatus, auth0Status] = await Promise.all([this.checkDatabase(), this.checkRedis(), this.checkAuth0()]);

    // All critical dependencies must be UP for service to be ready
    if (dbStatus === ServiceStatus.SERVICE_STATUS_UP && redisStatus === ServiceStatus.SERVICE_STATUS_UP && auth0Status === ServiceStatus.SERVICE_STATUS_UP) {
      return ServiceStatus.SERVICE_STATUS_UP;
    }

    return ServiceStatus.SERVICE_STATUS_DOWN;
  }

  // Check all downstream services
  private async checkAllDependencies(): Promise<ServiceHealth[]> {
    const services = [
      { name: 'user-service', url: this.configService.getServiceUrl('user-service') },
      { name: 'tenant-service', url: this.configService.getServiceUrl('tenant-service') },
      { name: 'audit-service', url: this.configService.getServiceUrl('audit-service') },
    ];

    const healthChecks = services.map((service) => this.checkServiceHealth(service.name, service.url));
    return Promise.all(healthChecks);
  }

  // Check individual service health
  private async checkServiceHealth(name: string, url: string): Promise<ServiceHealth> {
    const startTime = Date.now();

    try {
      // TODO: Implement actual HTTP health check to service
      // For now, mock the response based on URL availability
      const isHealthy = url && url.startsWith('http');

      return {
        name,
        status: isHealthy ? ServiceStatus.SERVICE_STATUS_UP : ServiceStatus.SERVICE_STATUS_DOWN,
        responseTime: Date.now() - startTime,
        lastChecked: new Date(),
        url,
        error: '',
      };
    } catch (error) {
      return {
        name,
        status: ServiceStatus.SERVICE_STATUS_DOWN,
        responseTime: Date.now() - startTime,
        lastChecked: new Date(),
        error: error.message,
        url,
      };
    }
  }

  // Check config service availability
  private checkConfigService(): ServiceStatus {
    try {
      // Simple check - if we can get environment, config service is working
      this.configService.getNodeEnv();
      return ServiceStatus.SERVICE_STATUS_UP;
    } catch (_) {
      return ServiceStatus.SERVICE_STATUS_DOWN;
    }
  }

  // Determine overall health status based on dependencies
  private determineOverallStatus(dependencies: ServiceHealth[]): HealthStatus {
    const downServices = dependencies.filter((dep) => dep.status === ServiceStatus.SERVICE_STATUS_DOWN);
    const unknownServices = dependencies.filter((dep) => dep.status === ServiceStatus.SERVICE_STATUS_UNKNOWN);

    if (downServices.length === 0 && unknownServices.length === 0) {
      return HealthStatus.HEALTH_STATUS_HEALTHY;
    } else if (downServices.length > 0 && downServices.length < dependencies.length / 2) {
      return HealthStatus.HEALTH_STATUS_DEGRADED;
    } else {
      return HealthStatus.HEALTH_STATUS_UNHEALTHY;
    }
  }

  // Get status message based on health status and dependencies
  private getStatusMessage(status: HealthStatus, dependencies: ServiceHealth[]): string {
    switch (status) {
      case HealthStatus.HEALTH_STATUS_HEALTHY:
        return 'All systems operational';
      case HealthStatus.HEALTH_STATUS_DEGRADED: {
        const downServices = dependencies.filter((dep) => dep.status === ServiceStatus.SERVICE_STATUS_DOWN);
        return `Some services are down: ${downServices.map((s) => s.name).join(', ')}`;
      }
      case HealthStatus.HEALTH_STATUS_UNHEALTHY:
        return 'Multiple critical services are unavailable';
      default:
        return 'Unknown status';
    }
  }
}
