import { Module } from '@nestjs/common';
import { HealthGrpcController } from './controllers/health.grpc.controller';
import { HealthController } from './controllers/health.rest.controller';
import { HealthService } from './services/health.service';

@Module({
  imports: [],
  controllers: [HealthController, HealthGrpcController],
  providers: [HealthService],
  exports: [HealthService],
})
export class HealthModule {}
