import { Injectable, Logger } from '@nestjs/common';
import { AuthPrismaService } from '../../../database/prisma.service';
import { DeviceStatus, TrustedDeviceEntity } from '../entities/trusted-device.entity';

/**
 * Repository for managing trusted device records in the database
 */
@Injectable()
export class DeviceRepository {
  private readonly logger = new Logger(DeviceRepository.name);

  constructor(private readonly prisma: AuthPrismaService) {}

  /**
   * Find trusted devices by user ID
   * @param userId User ID to find devices for
   * @returns List of trusted devices
   */
  async findByUserId(userId: string): Promise<TrustedDeviceEntity[]> {
    const devices = await this.prisma.trustedDevice.findMany({
      where: {
        userId,
        status: DeviceStatus.ACTIVE,
      },
      orderBy: {
        lastUsedAt: 'desc',
      },
    });

    return devices as unknown as TrustedDeviceEntity[];
  }

  /**
   * Count trusted devices for a user
   * @param userId User ID to count devices for
   * @returns Total count of trusted devices for the user
   */
  async countByUserId(userId: string): Promise<number> {
    return this.prisma.trustedDevice.count({
      where: {
        userId,
        status: DeviceStatus.ACTIVE,
      },
    });
  }

  /**
   * Count trusted devices for a user with filters
   * @param userId User ID to count devices for
   * @param filters Filters to apply (status, deviceType, deviceName, etc.)
   * @returns Total count of trusted devices for the user matching filters
   */
  async countByUserIdWithFilters(
    userId: string,
    filters: {
      status?: DeviceStatus;
      deviceType?: string;
      deviceName?: string;
      browser?: string;
      operatingSystem?: string;
    },
  ): Promise<number> {
    // Start with base where clause
    const where: Record<string, unknown> = { userId };

    // Apply filters if they are defined
    if (filters.status) where.status = filters.status;
    if (filters.deviceType) where.deviceType = { contains: filters.deviceType, mode: 'insensitive' };
    if (filters.deviceName) where.deviceName = { contains: filters.deviceName, mode: 'insensitive' };
    if (filters.browser) where.browser = { contains: filters.browser, mode: 'insensitive' };
    if (filters.operatingSystem) where.operatingSystem = { contains: filters.operatingSystem, mode: 'insensitive' };

    return this.prisma.trustedDevice.count({ where });
  }

  /**
   * Find trusted devices by user ID with pagination
   * @param userId User ID to find devices for
   * @param options Pagination options (offset, limit)
   * @returns Paginated list of trusted devices
   */
  async findByUserIdWithPagination(userId: string, options: { offset: number; limit: number }): Promise<TrustedDeviceEntity[]> {
    const devices = await this.prisma.trustedDevice.findMany({
      where: {
        userId,
        status: DeviceStatus.ACTIVE,
      },
      orderBy: {
        lastUsedAt: 'desc',
      },
      skip: options.offset,
      take: options.limit,
    });

    return devices as unknown as TrustedDeviceEntity[];
  }

  /**
   * Find trusted devices by user ID with pagination and filters
   * @param userId User ID to find devices for
   * @param pagination Pagination options (offset, limit)
   * @param filters Filters to apply (status, deviceType, deviceName, etc.)
   * @param sort Sorting options (field, order)
   * @returns Paginated and filtered list of trusted devices
   */
  async findByUserIdWithFiltersAndPagination(
    userId: string,
    pagination: { offset: number; limit: number },
    filters: {
      status?: DeviceStatus;
      deviceType?: string;
      deviceName?: string;
      browser?: string;
      operatingSystem?: string;
    },
    sort?: { field: string; order: 'asc' | 'desc' },
  ): Promise<TrustedDeviceEntity[]> {
    // Start with base where clause
    const where: Record<string, unknown> = { userId };

    // Apply filters if they are defined
    if (filters.status) where.status = filters.status;
    if (filters.deviceType) where.deviceType = { contains: filters.deviceType, mode: 'insensitive' };
    if (filters.deviceName) where.deviceName = { contains: filters.deviceName, mode: 'insensitive' };
    if (filters.browser) where.browser = { contains: filters.browser, mode: 'insensitive' };
    if (filters.operatingSystem) where.operatingSystem = { contains: filters.operatingSystem, mode: 'insensitive' };

    // Determine sort field and order (default to lastUsedAt desc if not specified)
    const orderBy = {};
    const sortField = sort?.field || 'lastUsedAt';
    const sortOrder = sort?.order || 'desc';
    orderBy[sortField] = sortOrder;

    const devices = await this.prisma.trustedDevice.findMany({
      where,
      orderBy,
      skip: pagination.offset,
      take: pagination.limit,
    });

    return devices as unknown as TrustedDeviceEntity[];
  }

  /**
   * Find trusted device by device ID
   * @param deviceId Device ID to find
   * @returns The trusted device if found, null otherwise
   */
  async findById(deviceId: string): Promise<TrustedDeviceEntity | null> {
    const device = await this.prisma.trustedDevice.findUnique({
      where: {
        id: deviceId,
      },
    });

    return device as unknown as TrustedDeviceEntity;
  }

  /**
   * Find trusted device by user and device ID
   * @param userId User ID
   * @param deviceId Device ID
   * @returns The trusted device if found, null otherwise
   */
  async findByDeviceId(userId: string, deviceId: string): Promise<TrustedDeviceEntity | null> {
    const device = await this.prisma.trustedDevice.findFirst({
      where: {
        userId,
        deviceId,
        status: DeviceStatus.ACTIVE,
      },
    });

    return device as unknown as TrustedDeviceEntity;
  }

  /**
   * Create a new trusted device
   * @param data Trusted device data to create
   * @returns The created trusted device
   */
  async create(data: Omit<TrustedDeviceEntity, 'updatedAt'>): Promise<TrustedDeviceEntity> {
    const device = await this.prisma.trustedDevice.create({
      data,
    });

    return device as unknown as TrustedDeviceEntity;
  }

  /**
   * Update an existing trusted device
   * @param id Device ID to update
   * @param data Updated trusted device data
   * @returns The updated trusted device
   */
  async update(id: string, data: Partial<TrustedDeviceEntity>): Promise<TrustedDeviceEntity> {
    const device = await this.prisma.trustedDevice.update({
      where: {
        id,
      },
      data: {
        ...data,
        updatedAt: new Date(),
      },
    });

    return device as unknown as TrustedDeviceEntity;
  }

  /**
   * Deactivate all trusted devices for a user
   * @param userId User ID whose devices to deactivate
   * @returns Count of deactivated devices
   */
  async deactivateAll(userId: string): Promise<number> {
    const result = await this.prisma.trustedDevice.updateMany({
      where: {
        userId,
        status: DeviceStatus.ACTIVE,
      },
      data: {
        status: DeviceStatus.INACTIVE,
        updatedAt: new Date(),
      },
    });

    return result.count;
  }
}
