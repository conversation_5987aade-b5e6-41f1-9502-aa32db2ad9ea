import { Body, Controller, Delete, Get, Logger, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import { JwtAuthGuard, JwtUserId, ResponseUtil, SnakeToCamelPipe, StandardApiResponse, ZodValidationPipe } from '@qeep/common';
import {
  // Import DTO types
  CheckDeviceTrustDto,
  // Import schemas for validation
  CheckDeviceTrustSchema,
  DeviceTrustStatusDto,
  ListTrustedDevicesQueryDto,
  ListTrustedDevicesQuerySchema,
  RegisterTrustedDeviceDto,
  RegisterTrustedDeviceSchema,
  RevokeAllTrustedDevicesResponseDto,
  RevokeTrustedDeviceResponseDto,
  TrustedDeviceDto,
  UpdateTrustedDeviceDto,
  UpdateTrustedDeviceSchema,
} from '@qeep/contracts/auth';
import { Shared } from '@qeep/contracts/shared';
import { DeviceManagementService } from '../services/device-management.service';

@Controller('auth/devices')
export class DeviceManagementController {
  private readonly logger = new Logger(DeviceManagementController.name);

  constructor(private readonly deviceService: DeviceManagementService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  async listTrustedDevices(
    @JwtUserId() userId: string,
    @Query(new SnakeToCamelPipe(), new ZodValidationPipe(ListTrustedDevicesQuerySchema))
    query: ListTrustedDevicesQueryDto,
  ): Promise<
    StandardApiResponse<{
      devices: TrustedDeviceDto[];
      pagination: Shared.DTOs.PaginationMeta;
    }>
  > {
    this.logger.log(`List trusted devices for user: ${userId} with query: ${JSON.stringify(query)}`);

    const result = await this.deviceService.getTrustedDevices(userId, query);

    return ResponseUtil.success(
      {
        devices: result.devices,
        pagination: result.pagination,
      },
      'Trusted devices retrieved',
    );
  }

  @Post('trust')
  @UseGuards(JwtAuthGuard)
  async trustDevice(
    @JwtUserId() userId: string,
    @Body(SnakeToCamelPipe, new ZodValidationPipe(RegisterTrustedDeviceSchema))
    dto: RegisterTrustedDeviceDto,
  ): Promise<StandardApiResponse<TrustedDeviceDto>> {
    this.logger.log(`Trust device request for user: ${userId}`);
    const device = await this.deviceService.registerTrustedDevice(userId, dto);
    return ResponseUtil.success(device, 'Device trusted successfully');
  }

  @Put(':deviceId')
  @UseGuards(JwtAuthGuard)
  async updateTrustedDevice(
    @JwtUserId() userId: string,
    @Param('deviceId') deviceId: string,
    @Body(SnakeToCamelPipe, new ZodValidationPipe(UpdateTrustedDeviceSchema))
    dto: UpdateTrustedDeviceDto,
  ): Promise<StandardApiResponse<TrustedDeviceDto>> {
    this.logger.log(`Update trusted device: ${deviceId} for user: ${userId}`);
    const device = await this.deviceService.updateTrustedDevice(userId, deviceId, dto);
    return ResponseUtil.success(device, 'Trusted device updated');
  }

  @Post('check')
  @UseGuards(JwtAuthGuard)
  async checkDeviceTrust(
    @JwtUserId() userId: string,
    @Body(SnakeToCamelPipe, new ZodValidationPipe(CheckDeviceTrustSchema))
    dto: CheckDeviceTrustDto,
  ): Promise<StandardApiResponse<DeviceTrustStatusDto>> {
    this.logger.log(`Check device trust for user: ${userId}`);
    const trustStatus = await this.deviceService.isDeviceTrusted(userId, dto.deviceInfo);
    return ResponseUtil.success(trustStatus, 'Device trust status checked');
  }

  @Delete(':deviceId')
  @UseGuards(JwtAuthGuard)
  async revokeTrustedDevice(@JwtUserId() userId: string, @Param('deviceId') deviceId: string): Promise<StandardApiResponse<RevokeTrustedDeviceResponseDto>> {
    this.logger.log(`Revoke trusted device: ${deviceId} for user: ${userId}`);
    const result = await this.deviceService.revokeTrustedDevice(userId, deviceId);
    return ResponseUtil.success(result, 'Trusted device revoked');
  }

  @Delete()
  @UseGuards(JwtAuthGuard)
  async revokeAllTrustedDevices(@JwtUserId() userId: string): Promise<StandardApiResponse<RevokeAllTrustedDevicesResponseDto>> {
    this.logger.log(`Revoke all trusted devices for user: ${userId}`);
    const result = await this.deviceService.revokeAllTrustedDevices(userId);
    return ResponseUtil.success(result, 'All trusted devices revoked');
  }
}
