import { Modu<PERSON> } from '@nestjs/common';
import { AuthPrismaService } from '../../database/prisma.service';
import { DeviceManagementController } from './controllers/device-management.controller';
import { DeviceRepository } from './repositories/device.repository';
import { DeviceManagementService } from './services/device-management.service';

@Module({
  imports: [],
  controllers: [DeviceManagementController],
  providers: [DeviceManagementService, DeviceRepository, AuthPrismaService],
  exports: [DeviceManagementService],
})
export class DeviceManagementModule {}
