import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { generateDeviceFingerprintId, generateTrustedDeviceId } from '@qeep/common';
import { DeviceInfoDto, DeviceTrustStatusDto, ListTrustedDevicesQueryDto, RegisterTrustedDeviceDto, TrustedDeviceDto } from '@qeep/contracts/auth';
import { Shared } from '@qeep/contracts/shared';
import { DeviceStatus, TrustedDeviceEntity } from '../entities/trusted-device.entity';
import { DeviceRepository } from '../repositories/device.repository';

/**
 * Service to manage trusted devices for users
 */
@Injectable()
export class DeviceManagementService {
  private readonly logger = new Logger(DeviceManagementService.name);

  constructor(private readonly deviceRepository: DeviceRepository) {}

  /**
   * Generate a device ID from device information
   */
  generateDeviceId(deviceInfo: DeviceInfoDto): string {
    // Create a simplified fingerprint from available device info
    const fingerprint = [deviceInfo.userAgent || '', deviceInfo.platform || '', deviceInfo.screenResolution || '', deviceInfo.language || ''].join('|');

    // Use the CUID utility for consistent ID generation with proper prefixes
    // This uses the fp_ prefix for device fingerprinting
    return generateDeviceFingerprintId(fingerprint);
  }

  /**
   * Get all trusted devices for a user with pagination and filtering
   * @param userId The user ID to get trusted devices for
   * @param options Pagination and filtering options
   * @returns Paginated list of trusted devices and pagination metadata
   */
  async getTrustedDevices(
    userId: string,
    options: ListTrustedDevicesQueryDto,
  ): Promise<{
    devices: TrustedDeviceDto[];
    pagination: Shared.DTOs.PaginationMeta;
  }> {
    // Default pagination values
    const page = options.page || 1;
    const limit = options.limit || 10;
    const offset = (page - 1) * limit;

    // Prepare filter criteria
    const filters: {
      status?: DeviceStatus;
      deviceType?: string;
      deviceName?: string;
      browser?: string;
      operatingSystem?: string;
    } = {
      status: options.status,
      deviceType: options.deviceType as string,
      deviceName: options.deviceName as string,
      browser: options.browser as string,
      operatingSystem: options.operatingSystem as string,
    };

    // Prepare sorting
    const sortOptions: { field: string; order: 'asc' | 'desc' } = {
      field: (options.sortBy as string) || 'lastUsedAt',
      order: (options.sortOrder as 'asc' | 'desc') || 'desc',
    };

    // Get total count with filters
    const totalCount = await this.deviceRepository.countByUserIdWithFilters(userId, filters);

    // Get devices with pagination and filters
    const devices = await this.deviceRepository.findByUserIdWithFiltersAndPagination(userId, { offset, limit }, filters, sortOptions);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const hasMore = page < totalPages;

    // Convert entities to DTOs
    const deviceDtos: TrustedDeviceDto[] = devices.map((device) => ({
      id: device.id,
      deviceId: device.deviceId,
      status: device.status,
      trustedAt: device.trustedAt,
      deviceName: device.deviceName,
      deviceType: device.deviceType,
      operatingSystem: device.operatingSystem,
      browser: device.browser,
      lastUsedAt: device.lastUsedAt,
      expiresAt: device.expiresAt,
    }));

    return {
      devices: deviceDtos,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore,
        page,
        totalPages,
      },
    };
  }

  /**
   * Get a specific trusted device by ID
   */
  async getTrustedDevice(userId: string, deviceId: string): Promise<TrustedDeviceDto | null> {
    // Get device from repository by ID
    const device = await this.deviceRepository.findById(deviceId);

    // Ensure the device belongs to the requesting user for security
    if (!device || device.userId !== userId) {
      return null;
    }

    // Convert entity to DTO
    return {
      id: device.id,
      deviceId: device.deviceId,
      status: device.status,
      trustedAt: device.trustedAt,
      deviceName: device.deviceName,
      deviceType: device.deviceType,
      operatingSystem: device.operatingSystem,
      browser: device.browser,
      lastUsedAt: device.lastUsedAt,
      expiresAt: device.expiresAt,
    };
  }

  /**
   * Register a new trusted device for a user
   */
  async registerTrustedDevice(userId: string, dto: RegisterTrustedDeviceDto): Promise<TrustedDeviceDto> {
    const deviceId = this.generateDeviceId(dto.deviceInfo);

    // Check if device already exists
    const existingDevice = await this.deviceRepository.findByDeviceId(userId, deviceId);
    if (existingDevice) {
      return this.updateTrustedDevice(userId, existingDevice.id, {
        deviceName: dto.deviceName,
        lastUsedAt: new Date(),
      });
    }

    // Calculate expiration date (90 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 90);

    // Extract browser and OS from user agent if available
    const userAgent = dto.deviceInfo.userAgent || '';
    const browser = this.extractBrowserInfo(userAgent);
    const operatingSystem = this.extractOSInfo(userAgent);

    // Create new trusted device
    const entity = await this.deviceRepository.create({
      id: generateTrustedDeviceId(),
      userId,
      deviceId,
      deviceName: dto.deviceName || 'Unknown Device',
      deviceType: dto.deviceType || 'unknown',
      operatingSystem,
      browser,
      status: DeviceStatus.ACTIVE,
      ipAddress: dto.deviceInfo.ipAddress,
      location: dto.deviceInfo.timezone ? { timezone: dto.deviceInfo.timezone } : undefined,
      trustedAt: new Date(),
      lastUsedAt: new Date(),
      expiresAt,
      createdAt: new Date(),
    });

    // Convert entity to DTO
    return {
      id: entity.id,
      deviceId: entity.deviceId,
      status: entity.status,
      trustedAt: entity.trustedAt,
      deviceName: entity.deviceName,
      deviceType: entity.deviceType,
      operatingSystem: entity.operatingSystem,
      browser: entity.browser,
      lastUsedAt: entity.lastUsedAt,
      expiresAt: entity.expiresAt,
    };
  }

  /**
   * Update an existing trusted device
   */
  async updateTrustedDevice(userId: string, deviceId: string, dto: Partial<TrustedDeviceEntity>): Promise<TrustedDeviceDto> {
    const device = await this.getTrustedDevice(userId, deviceId);

    if (!device) {
      throw new NotFoundException(`Trusted device ${deviceId} not found`);
    }

    const updated = await this.deviceRepository.update(deviceId, dto);

    // Convert entity to DTO
    return {
      id: updated.id,
      deviceId: updated.deviceId,
      status: updated.status,
      trustedAt: updated.trustedAt,
      deviceName: updated.deviceName,
      deviceType: updated.deviceType,
      operatingSystem: updated.operatingSystem,
      browser: updated.browser,
      lastUsedAt: updated.lastUsedAt,
      expiresAt: updated.expiresAt,
    };
  }

  /**
   * Revoke a trusted device
   * @param userId User ID who owns the device
   * @param deviceId Device ID to revoke
   * @returns Response with device ID and revocation timestamp
   */
  async revokeTrustedDevice(userId: string, deviceId: string): Promise<{ deviceId: string; revokedAt: Date }> {
    const device = await this.getTrustedDevice(userId, deviceId);

    if (!device) {
      throw new NotFoundException(`Trusted device ${deviceId} not found`);
    }

    const revokedAt = new Date();

    await this.deviceRepository.update(deviceId, {
      status: DeviceStatus.INACTIVE,
      updatedAt: revokedAt,
    });

    return {
      deviceId,
      revokedAt,
    };
  }

  /**
   * Revoke all trusted devices for a user
   * @param userId User ID whose devices to revoke
   * @returns Response with count of revoked devices and revocation timestamp
   */
  async revokeAllTrustedDevices(userId: string): Promise<{ revokedCount: number; revokedAt: Date }> {
    const revokedAt = new Date();
    const count = await this.deviceRepository.deactivateAll(userId);

    return {
      revokedCount: count || 0,
      revokedAt,
    };
  }

  /**
   * Check if a device is trusted for a user
   */
  async isDeviceTrusted(userId: string, deviceInfo: DeviceInfoDto): Promise<DeviceTrustStatusDto> {
    const deviceId = this.generateDeviceId(deviceInfo);
    const device = await this.deviceRepository.findByDeviceId(userId, deviceId);

    if (!device || device.status !== DeviceStatus.ACTIVE) {
      return { isTrusted: false };
    }

    // Check if device trust has expired
    if (device.expiresAt && new Date() > device.expiresAt) {
      await this.deviceRepository.update(device.id, { status: DeviceStatus.INACTIVE });
      return { isTrusted: false };
    }

    // Update last used timestamp
    await this.deviceRepository.update(device.id, {
      lastUsedAt: new Date(),
    });

    return {
      isTrusted: true,
      deviceId: device.id,
    };
  }

  /**
   * Extract browser information from user agent
   */
  private extractBrowserInfo(userAgent: string): string {
    // Simple extraction logic - in production use a proper user-agent parser
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    if (userAgent.includes('MSIE') || userAgent.includes('Trident/')) return 'Internet Explorer';
    return 'Unknown';
  }

  /**
   * Extract OS information from user agent
   */
  private extractOSInfo(userAgent: string): string {
    // Simple extraction logic - in production use a proper user-agent parser
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS') || userAgent.includes('iPhone') || userAgent.includes('iPad')) return 'iOS';
    return 'Unknown';
  }
}
