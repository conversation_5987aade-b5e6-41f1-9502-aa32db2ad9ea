/**
 * Trusted Device entity representing the TrustedDevice model in the Prisma schema
 */
export enum DeviceStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  BLOCKED = 'BLOCKED',
  SUSPICIOUS = 'SUSPICIOUS',
}

/**
 * Trusted device entity based on the actual Prisma schema
 */
export interface TrustedDeviceEntity {
  /**
   * Unique identifier for the trusted device
   */
  id: string;

  /**
   * User ID that owns this trusted device
   */
  userId: string;

  /**
   * Tenant ID (optional)
   */
  tenantId?: string;

  /**
   * Tenant code (optional)
   */
  tenantCode?: string;

  /**
   * Unique device identifier
   */
  deviceId: string;

  /**
   * User-friendly name for the device
   */
  deviceName?: string;

  /**
   * Type of device (mobile, desktop, tablet)
   */
  deviceType?: string;

  /**
   * Operating system of the device
   */
  operatingSystem?: string;

  /**
   * Browser used on the device
   */
  browser?: string;

  /**
   * Status of the trusted device
   */
  status: DeviceStatus;

  /**
   * IP address when device was trusted
   */
  ipAddress?: string;

  /**
   * Location information (JSON)
   */
  location?: Record<string, any>;

  /**
   * When the device was last used
   */
  lastUsedAt?: Date;

  /**
   * When the device was added as trusted
   */
  trustedAt: Date;

  /**
   * When the device trust expires
   */
  expiresAt?: Date;

  /**
   * When the device record was created
   */
  createdAt: Date;

  /**
   * When the device record was last updated
   */
  updatedAt: Date;
}
