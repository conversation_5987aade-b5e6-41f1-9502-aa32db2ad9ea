/* eslint-disable @typescript-eslint/no-explicit-any */
import { BadRequestException, Body, Controller, Logger, Post, UseGuards } from '@nestjs/common';
import { JwtAuthGuard, JwtUserId, ResponseUtil, StandardApiResponse, TenantId } from '@qeep/common';
import { Auth } from '@qeep/contracts';
import { ZodError } from 'zod';
import { PasswordManagementService } from '../services/password-management.service';

@Controller('auth')
export class PasswordManagementController {
  private readonly logger = new Logger(PasswordManagementController.name);

  constructor(private readonly authenticationService: PasswordManagementService) {}

  @Post('forgot-password')
  async forgotPassword(@Body() requestBody: any, @TenantId() tenantId?: string): Promise<StandardApiResponse<any>> {
    try {
      // Validate request using Zod schema
      const forgotPasswordDto = Auth.Schemas.ForgotPasswordRequestSchema.parse({
        ...requestBody,
        tenant_code: tenantId,
      });

      // Transform external request to internal format
      const internalRequest = {
        email: forgotPasswordDto.email,
        tenantId: tenantId,
      };

      // Call service
      const result = await this.authenticationService.forgotPassword(internalRequest);

      return ResponseUtil.success({}, result.message);
    } catch (error) {
      if (error instanceof ZodError) {
        this.logger.error(`Forgot password validation failed: ${JSON.stringify(error.issues)}`);
        throw new BadRequestException({
          message: 'Validation failed',
          errors: error.issues.map((err) => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        });
      }

      this.logger.error(`Forgot password failed: ${error.message}`);

      return ResponseUtil.success({}, 'If an account exists with this email, a password reset link has been sent.');
    }
  }

  @Post('reset-password')
  async resetPassword(@Body() requestBody: any): Promise<StandardApiResponse<any>> {
    this.logger.log('Reset password request');

    try {
      // Validate request using Zod schema
      const resetPasswordDto = Auth.Schemas.ResetPasswordRequestSchema.parse(requestBody);

      // Transform external request to internal format
      const internalRequest = {
        token: resetPasswordDto.token,
        newPassword: resetPasswordDto.newPassword,
      };

      // Call service
      const result = await this.authenticationService.resetPassword(internalRequest);

      return ResponseUtil.success(result, result.message);
    } catch (error) {
      if (error instanceof ZodError) {
        this.logger.error(`Reset password validation failed: ${JSON.stringify(error.issues)}`);
        throw new BadRequestException({
          message: 'Validation failed',
          errors: error.issues.map((err) => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        });
      }
      this.logger.error(`Reset password failed: ${error.message}`);
      throw error;
    }
  }

  @Post('change-password')
  @UseGuards(JwtAuthGuard)
  async changePassword(@Body() requestBody: any, @JwtUserId() userId: string): Promise<StandardApiResponse<any>> {
    this.logger.log(`Change password request for user: ${userId}`);

    try {
      // Validate request using Zod schema
      const changePasswordDto = Auth.Schemas.ChangePasswordRequestSchema.parse(requestBody);

      // Transform external request to internal format
      const internalRequest = {
        userId,
        currentPassword: changePasswordDto.currentPassword,
        newPassword: changePasswordDto.newPassword,
      };

      // Call service
      const result = await this.authenticationService.changePassword(internalRequest);

      return ResponseUtil.success(result, result.message);
    } catch (error) {
      if (error instanceof ZodError) {
        this.logger.error(`Change password validation failed: ${JSON.stringify(error.issues)}`);
        throw new BadRequestException({
          message: 'Validation failed',
          errors: error.issues.map((err) => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        });
      }
      this.logger.error(`Change password failed: ${error.message}`);
      throw error;
    }
  }
}
