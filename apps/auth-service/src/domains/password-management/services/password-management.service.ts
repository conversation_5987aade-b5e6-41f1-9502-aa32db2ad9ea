/* eslint-disable @typescript-eslint/no-explicit-any */
import { BadRequestException, Inject, Injectable, Logger, OnModuleInit, UnauthorizedException } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { ConfigService } from '@qeep/common';
import { NotificationServiceClient, UserServiceClient, UserUserStatus } from '@qeep/proto';
import * as bcrypt from 'bcryptjs';
import Redis from 'ioredis';
import * as jwt from 'jsonwebtoken';
import { firstValueFrom } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';
import { AUTH_CONSTANTS } from '../../../core/constants/auth.constants';

@Injectable()
export class PasswordManagementService implements OnModuleInit {
  private readonly logger = new Logger(PasswordManagementService.name);
  private userService: UserServiceClient;
  private notificationService: NotificationServiceClient;
  private redis: Redis;

  constructor(
    @Inject('USER_PACKAGE') private userClient: ClientGrpc,
    @Inject('NOTIFICATION_PACKAGE') private notificationClient: ClientGrpc,
    private configService: ConfigService,
  ) {}

  onModuleInit() {
    this.userService = this.userClient.getService<UserServiceClient>('UserService');
    this.notificationService = this.notificationClient.getService<NotificationServiceClient>('NotificationService');

    // Initialize Redis
    const redisConfig = this.configService.getRedisConfig();
    this.redis = new Redis({
      host: redisConfig.host,
      port: redisConfig.port,
      password: redisConfig.password,
      maxRetriesPerRequest: 3,
    });
  }

  async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const isBlacklisted = await this.redis.exists(`blacklist:${token}`);
      return isBlacklisted === 1;
    } catch (error) {
      this.logger.error(`Error checking token blacklist: ${error.message}`);
      return false;
    }
  }

  async isUserForcedLogout(userId: string, tokenIssuedAt: number): Promise<boolean> {
    try {
      const forceLogoutTime = await this.redis.get(`force_logout:${userId}`);
      if (!forceLogoutTime) {
        return false;
      }

      const forceLogoutTimestamp = parseInt(forceLogoutTime, 10);
      return tokenIssuedAt < forceLogoutTimestamp;
    } catch (error) {
      this.logger.error(`Error checking force logout: ${error.message}`);
      return false;
    }
  }

  private async generateTokens(userId: string, email: string, tenantCode: string | null) {
    const now = Math.floor(Date.now() / 1000);

    const payload = {
      sub: userId, // Standard JWT subject claim
      userId, // Custom claim for backward compatibility
      email,
      tenantCode,
      iss: 'qeep-auth-service', // Issuer
      aud: 'qeep-api', // Audience
      iat: now, // Issued at time
    };

    // For refresh token, add a unique identifier to ensure uniqueness
    const refreshPayload = {
      ...payload,
      jti: `${userId}-${now}-${Math.random().toString(36).substring(2)}`, // Unique JWT ID
    };

    const jwtConfig = this.configService.getJwtConfig();

    // Add kid (key ID) to header for JWKS compatibility
    const accessToken = (jwt.sign as any)(payload, jwtConfig.secret, {
      expiresIn: jwtConfig.expiresIn,
      algorithm: 'HS256',
      header: {
        kid: 'qeep-key-1', // Key identifier
        typ: 'JWT',
        alg: 'HS256',
      },
    });

    const refreshToken = (jwt.sign as any)(refreshPayload, jwtConfig.refreshSecret, {
      expiresIn: jwtConfig.refreshExpiresIn,
      algorithm: 'HS256',
      header: {
        kid: 'qeep-refresh-key-1',
        typ: 'JWT',
        alg: 'HS256',
      },
    });

    return { accessToken, refreshToken };
  }

  async forgotPassword(request: any): Promise<any> {
    this.logger.log(`Forgot password request for email: ${request.email}`);

    try {
      // 1. Check if user exists (but don't reveal if they don't for security)
      let user;
      try {
        const userResponse = await firstValueFrom(
          this.userService.getUserByEmail({
            email: request.email,
            tenantCode: request.tenantCode || null,
          }),
        );

        if (!userResponse.success || !userResponse.user) {
          // Don't reveal that user doesn't exist
          this.logger.log(`User not found for forgot password: ${request.email}`);
          return {
            message: 'If an account exists with this email, a password reset link has been sent.',
          };
        }

        user = userResponse.user;
      } catch (error) {
        this.logger.error(`User lookup failed for forgot password: ${request.email}`, error);

        // Don't reveal that user doesn't exist
        return {
          message: 'If an account exists with this email, a password reset link has been sent.',
        };
      }

      // 2. Check if user account is active
      if (user.status !== UserUserStatus.USER_STATUS_ACTIVE && user.status !== UserUserStatus.USER_STATUS_PENDING) {
        this.logger.log(`Password reset attempted for inactive user: ${request.email}`);
        return {
          message: 'If an account exists with this email, a password reset link has been sent.',
        };
      }

      // 3. Generate password reset token
      const resetToken = uuidv4();

      this.logger.log({ resetToken });

      // 4. Store reset token in Redis with 1 hour expiration
      const resetKey = `${AUTH_CONSTANTS.REDIS_KEYS.PASSWORD_RESET}${resetToken}`;
      await this.redis.setex(
        resetKey,
        AUTH_CONSTANTS.PASSWORD_RESET_TOKEN_EXPIRY,
        JSON.stringify({
          userId: user.id,
          email: user.email,
          tenantCode: request.tenantCode || null,
          createdAt: Date.now(),
        }),
      );

      // 5. Send password reset email
      await this.sendPasswordResetEmail(user.email, user.firstName, resetToken);

      this.logger.log(`Password reset email sent successfully for: ${request.email}`);

      return {
        message: 'If an account exists with this email, a password reset link has been sent.',
      };
    } catch (error) {
      this.logger.error(`Forgot password failed for ${request.email}: ${error.message}`);

      // Don't reveal internal errors to the user
      return {
        message: 'If an account exists with this email, a password reset link has been sent.',
      };
    }
  }

  async resetPassword(request: any): Promise<any> {
    this.logger.log('Reset password request');

    try {
      // 1. Validate and retrieve reset token from Redis
      const resetKey = `${AUTH_CONSTANTS.REDIS_KEYS.PASSWORD_RESET}${request.token}`;
      const resetData = await this.redis.get(resetKey);

      if (!resetData) {
        throw new BadRequestException('Invalid or expired reset token');
      }

      const { userId, email } = JSON.parse(resetData);

      // 2. Get user to verify they still exist and are active
      const userResponse = await firstValueFrom(
        this.userService.getUser({
          userId,
          metadata: {
            requestId: uuidv4(),
            sourceIp: '127.0.0.1',
            userAgent: 'auth-service',
            timestamp: new Date(),
          },
        }),
      );

      if (!userResponse.success || !userResponse.user) {
        throw new BadRequestException('User not found');
      }

      const user = userResponse.user;

      // 3. Check if user account is still active
      if (user.status !== UserUserStatus.USER_STATUS_ACTIVE && user.status !== UserUserStatus.USER_STATUS_PENDING) {
        throw new BadRequestException('Account is not active');
      }

      // 4. Hash the new password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(request.newPassword, saltRounds);

      // 5. Update user password using the new updatePassword gRPC method
      const updatePasswordResponse = await firstValueFrom(
        this.userService.updatePassword({
          userId,
          passwordHash: hashedPassword,
          metadata: {
            requestId: uuidv4(),
            sourceIp: '127.0.0.1',
            userAgent: 'auth-service',
            timestamp: new Date(),
          },
        }),
      );

      if (!updatePasswordResponse.success) {
        this.logger.error(`Failed to update password for user: ${userId}`, updatePasswordResponse.error);
        throw new BadRequestException('Failed to reset password');
      }

      this.logger.log(`Password reset completed successfully for user: ${userId}`);

      // 6. Delete the reset token from Redis
      await this.redis.del(resetKey);

      // 7. Invalidate all existing refresh tokens for this user
      const refreshTokenKey = `refresh_token:${userId}`;
      await this.redis.del(refreshTokenKey);

      this.logger.log(`Password reset successfully for user: ${email}`);

      return {
        success: true,
        message: 'Password reset successfully. Please log in with your new password.',
      };
    } catch (error) {
      this.logger.error(`Reset password error:`, error);
      throw error;
    }
  }

  async changePassword(request: any): Promise<any> {
    this.logger.log(`Change password request for user: ${request.userId}`);

    try {
      // 1. Get user details
      const userResponse = await firstValueFrom(
        this.userService.getUser({
          userId: request.userId,
          metadata: {
            requestId: uuidv4(),
            sourceIp: '127.0.0.1',
            userAgent: 'auth-service',
            timestamp: new Date(),
          },
        }),
      );

      if (!userResponse.success || !userResponse.user) {
        throw new BadRequestException('User not found');
      }

      const user = userResponse.user;

      // 2. Check if user account is active
      if (user.status !== UserUserStatus.USER_STATUS_ACTIVE) {
        throw new BadRequestException('Account is not active');
      }

      // 3. Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(request.currentPassword, user.passwordHash);
      if (!isCurrentPasswordValid) {
        throw new BadRequestException('Current password is incorrect');
      }

      // 4. Check if new password is different from current password
      const isSamePassword = await bcrypt.compare(request.newPassword, user.passwordHash);
      if (isSamePassword) {
        throw new BadRequestException('New password must be different from current password');
      }

      // 5. Hash the new password
      const saltRounds = 12;
      const hashedNewPassword = await bcrypt.hash(request.newPassword, saltRounds);

      // 6. Update user password using the updatePassword gRPC method
      const updatePasswordResponse = await firstValueFrom(
        this.userService.updatePassword({
          userId: request.userId,
          passwordHash: hashedNewPassword,
          metadata: {
            requestId: uuidv4(),
            sourceIp: '127.0.0.1',
            userAgent: 'auth-service',
            timestamp: new Date(),
          },
        }),
      );

      if (!updatePasswordResponse.success) {
        this.logger.error(`Failed to update password for user: ${request.userId}`, updatePasswordResponse.error);
        throw new BadRequestException('Failed to change password');
      }

      // 7. Invalidate all existing refresh tokens for this user (force re-login on other devices)
      const refreshTokenKey = `refresh_token:${request.userId}`;
      await this.redis.del(refreshTokenKey);

      this.logger.log(`Password changed successfully for user: ${request.userId}`);

      return {
        success: true,
        message: 'Password changed successfully. Please log in again.',
      };
    } catch (error) {
      this.logger.error(`Change password error for user ${request.userId}:`, error);
      throw error;
    }
  }

  private async sendPasswordResetEmail(email: string, firstName: string, resetToken: string): Promise<void> {
    try {
      // Create password reset URL
      const baseUrl = this.configService.get('FRONTEND_URL') || 'http://localhost:3000';
      const resetUrl = `${baseUrl}/reset-password?token=${resetToken}`;

      // Send email using notification service
      const emailRequest = {
        recipientEmail: email,
        recipientName: firstName,
        subject: 'Qeep - Password Reset Request',
        templateSlug: 'password-reset-email',
        templateData: {
          firstName,
          resetUrl,
          resetToken,
          expiryHours: '1', // 1 hour expiry
          supportEmail: '<EMAIL>',
        },
        tenantCode: null, // No default tenant - null is null
        metadata: {
          requestId: uuidv4(),
          userId: '',
          tenantCode: null,
          timestamp: new Date(),
        },
      };

      const response = await firstValueFrom(this.notificationService.sendEmail(emailRequest));

      if (response.success) {
        this.logger.log(`Password reset email sent successfully to ${email}`);
      } else {
        this.logger.error(`Failed to send password reset email to ${email}: ${response.error?.message}`);
      }
    } catch (error) {
      this.logger.error(`Error sending password reset email to ${email}: ${error.message}`);
      // Don't throw error to prevent password reset failure due to email issues
    }
  }

  /**
   * Generate tokens for a user (used by MFA after verification)
   */
  async generateTokensForUser(request: any): Promise<any> {
    try {
      // Get user details
      const userResponse = await firstValueFrom(
        this.userService.getUser({
          userId: request.userId,
          metadata: {
            requestId: uuidv4(),
            sourceIp: '',
            userAgent: '',
            timestamp: new Date(),
          },
        }),
      );

      if (!userResponse.success || !userResponse.user) {
        throw new Error('User not found');
      }

      const user = userResponse.user;

      // Generate tokens
      const tokens = await this.generateTokens(user.id, user.email, user.tenantCode);

      return {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        expiresIn: 3600,
        user: {
          id: user.id,
          email: user.email,
          emailVerified: user.isEmailVerified,
          tenantCode: user.tenantCode,
        },
      };
    } catch (error) {
      this.logger.error(`Generate tokens for user failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Verify user password (used by MFA and other security operations)
   */
  async verifyUserPassword(request: any): Promise<boolean> {
    try {
      // Get user details
      const userResponse = await firstValueFrom(
        this.userService.getUser({
          userId: request.userId,
          metadata: {
            requestId: uuidv4(),
            sourceIp: '',
            userAgent: '',
            timestamp: new Date(),
          },
        }),
      );

      if (!userResponse.success || !userResponse.user) {
        throw new UnauthorizedException('User not found');
      }

      const user = userResponse.user;

      // Verify password
      const isPasswordValid = await bcrypt.compare(request.password, user.passwordHash);
      if (!isPasswordValid) {
        throw new UnauthorizedException('Invalid password');
      }

      return true;
    } catch (error) {
      this.logger.error(`Password verification failed for user ${request.userId}: ${error.message}`);
      throw error;
    }
  }
}
