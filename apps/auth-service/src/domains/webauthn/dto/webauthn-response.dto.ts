/**
 * Start WebAuthn registration response data DTO
 */
export class StartWebAuthnRegistrationDataDto {
  challenge: string;
  rp: {
    name: string;
    id: string;
  };
  user: {
    id: string;
    name: string;
    displayName: string;
  };
  pubKeyCredParams: Array<{
    type: string;
    alg: number;
  }>;
  timeout: number;
  excludeCredentials: Array<{
    id: string;
    type: string;
    transports: string[];
  }>;
  authenticatorSelection: {
    authenticatorAttachment?: string;
    userVerification: string;
    requireResidentKey: boolean;
  };
  attestation: string;
}

/**
 * Complete WebAuthn registration response data DTO
 */
export class CompleteWebAuthnRegistrationDataDto {
  credentialId: string;
  verified: boolean;
  authenticatorName: string;
}

/**
 * Start WebAuthn authentication response data DTO
 */
export class StartWebAuthnAuthenticationDataDto {
  challenge: string;
  timeout: number;
  rpId: string;
  allowCredentials: Array<{
    id: string;
    type: string;
    transports: string[];
  }>;
  userVerification: string;
}

/**
 * Complete WebAuthn authentication response data DTO
 */
export class CompleteWebAuthnAuthenticationDataDto {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  user: {
    id: string;
    email: string;
    emailVerified: boolean;
    tenantCode: string;
  };
}

/**
 * Passkey list item DTO
 */
export class PasskeyItemDto {
  id: string;
  name: string;
  createdAt: string;
  lastUsed?: string;
  deviceType?: string;
}

/**
 * List passkeys response data DTO
 */
export class ListPasskeysDataDto {
  passkeys: PasskeyItemDto[];
}

/**
 * Remove passkey response data DTO
 */
export class RemovePasskeyDataDto {
  data: null;
}

/**
 * Rename passkey response data DTO
 */
export class RenamePasskeyDataDto {
  data: null;
}
