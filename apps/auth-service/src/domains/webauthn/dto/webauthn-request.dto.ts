import { IsNotEmpty, <PERSON>Optional, IsString } from 'class-validator';

// WebAuthn Registration DTOs
export class StartWebAuthnRegistrationDto {
  @IsString({ message: 'Authenticator name must be a string' })
  @IsNotEmpty({ message: 'Authenticator name is required' })
  authenticatorName: string;
}

export class CompleteWebAuthnRegistrationDto {
  @IsString({ message: 'Challenge must be a string' })
  @IsNotEmpty({ message: 'Challenge is required' })
  challenge: string;

  @IsNotEmpty({ message: 'Credential is required' })
  credential: any; // WebAuthn credential object

  @IsString({ message: 'Authenticator name must be a string' })
  @IsNotEmpty({ message: 'Authenticator name is required' })
  authenticatorName: string;
}

// WebAuthn Authentication DTOs
export class StartWebAuthnAuthenticationDto {
  @IsString({ message: 'Login token must be a string' })
  @IsNotEmpty({ message: 'Login token is required' })
  loginToken: string;
}

export class CompleteWebAuthnAuthenticationDto {
  @IsString({ message: 'Challenge must be a string' })
  @IsNotEmpty({ message: 'Challenge is required' })
  challenge: string;

  @IsNotEmpty({ message: 'Credential is required' })
  credential: any; // WebAuthn credential object
}

// Passkey Management DTOs
export class RenamePasskeyDto {
  @IsString({ message: 'Name must be a string' })
  @IsNotEmpty({ message: 'Name is required' })
  name: string;
}
