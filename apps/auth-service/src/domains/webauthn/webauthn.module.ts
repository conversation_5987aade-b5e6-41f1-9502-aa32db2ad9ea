import { Module } from '@nestjs/common';
import { ConfigService } from '@qeep/common';
import { WebAuthnController } from './controllers/webauthn.controller';
import { WebAuthnService } from './services/webauthn.service';

@Module({
  imports: [],
  controllers: [WebAuthnController],
  providers: [
    WebAuthnService,
    ConfigService,
  ],
  exports: [WebAuthnService],
})
export class WebAuthnModule {}
