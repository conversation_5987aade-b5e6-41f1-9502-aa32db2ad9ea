import { Body, Controller, Delete, Get, Logger, Param, Post, Put, UseGuards } from '@nestjs/common';
import { JwtAuthGuard, JwtUserId, ResponseUtil, StandardApiResponse } from '@qeep/common';
import * as jwt from 'jsonwebtoken';
import { ConfigService } from '@qeep/common';
import {
  StartWebAuthnRegistrationDto,
  CompleteWebAuthnRegistrationDto,
  StartWebAuthnAuthenticationDto,
  CompleteWebAuthnAuthenticationDto,
  RenamePasskeyDto,
} from '../dto/webauthn-request.dto';
import {
  StartWebAuthnRegistrationDataDto,
  CompleteWebAuthnRegistrationDataDto,
  StartWebAuthnAuthenticationDataDto,
  CompleteWebAuthnAuthenticationDataDto,
  ListPasskeysDataDto,
  RemovePasskeyDataDto,
  RenamePasskeyDataDto,
} from '../dto/webauthn-response.dto';
import { WebAuthnService } from '../services/webauthn.service';

@Controller('webauthn')
export class WebAuthnController {
  private readonly logger = new Logger(WebAuthnController.name);

  constructor(
    private readonly webAuthnService: WebAuthnService,
    private readonly configService: ConfigService,
  ) {}

  @Post('registration/start')
  @UseGuards(JwtAuthGuard)
  async startWebAuthnRegistration(
    @Body() startRegistrationDto: StartWebAuthnRegistrationDto,
    @JwtUserId() userId: string
  ): Promise<StandardApiResponse<StartWebAuthnRegistrationDataDto>> {
    this.logger.log(`Start WebAuthn registration for user: ${userId}`);

    try {
      const result = await this.webAuthnService.startRegistration(userId, startRegistrationDto.authenticatorName);
      return ResponseUtil.success(result, 'WebAuthn registration started');
    } catch (error) {
      this.logger.error(`WebAuthn registration start failed for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  @Post('registration/complete')
  @UseGuards(JwtAuthGuard)
  async completeWebAuthnRegistration(
    @Body() completeRegistrationDto: CompleteWebAuthnRegistrationDto,
    @JwtUserId() userId: string
  ): Promise<StandardApiResponse<CompleteWebAuthnRegistrationDataDto>> {
    this.logger.log(`Complete WebAuthn registration for user: ${userId}`);

    try {
      const result = await this.webAuthnService.completeRegistration(
        userId,
        completeRegistrationDto.challenge,
        completeRegistrationDto.credential,
        completeRegistrationDto.authenticatorName
      );
      return ResponseUtil.success(result, 'WebAuthn credential registered successfully');
    } catch (error) {
      this.logger.error(`WebAuthn registration completion failed for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  @Post('authentication/start')
  async startWebAuthnAuthentication(
    @Body() startAuthenticationDto: StartWebAuthnAuthenticationDto
  ): Promise<StandardApiResponse<StartWebAuthnAuthenticationDataDto>> {
    this.logger.log('Start WebAuthn authentication');

    try {
      // Extract user ID from login token
      const jwtConfig = this.configService.getJwtConfig();
      const decoded = jwt.verify(startAuthenticationDto.loginToken, jwtConfig.secret) as any;
      const userId = decoded.userId;

      const result = await this.webAuthnService.startAuthentication(userId);
      return ResponseUtil.success(result, 'WebAuthn authentication challenge created');
    } catch (error) {
      this.logger.error(`WebAuthn authentication start failed: ${error.message}`);
      throw error;
    }
  }

  @Post('authentication/complete')
  async completeWebAuthnAuthentication(
    @Body() completeAuthenticationDto: CompleteWebAuthnAuthenticationDto
  ): Promise<StandardApiResponse<CompleteWebAuthnAuthenticationDataDto>> {
    this.logger.log(`Complete WebAuthn authentication for challenge: ${completeAuthenticationDto.challenge}`);

    try {
      const { userId, isValid } = await this.webAuthnService.completeAuthentication(
        completeAuthenticationDto.challenge,
        completeAuthenticationDto.credential
      );

      if (!isValid) {
        throw new Error('WebAuthn authentication failed');
      }

      // TODO: Generate final login tokens using authentication service
      // For now, return placeholder data
      const tokens = {
        accessToken: 'placeholder-access-token',
        refreshToken: 'placeholder-refresh-token',
        expiresIn: 3600,
        user: {
          id: userId,
          email: '<EMAIL>',
          emailVerified: true,
          tenantCode: 'default',
        },
      };

      return ResponseUtil.success(tokens, 'WebAuthn authentication successful');
    } catch (error) {
      this.logger.error(`WebAuthn authentication completion failed: ${error.message}`);
      throw error;
    }
  }

  @Get('passkeys')
  @UseGuards(JwtAuthGuard)
  async listPasskeys(@JwtUserId() userId: string): Promise<StandardApiResponse<ListPasskeysDataDto>> {
    this.logger.log(`List passkeys for user: ${userId}`);

    try {
      const result = await this.webAuthnService.listPasskeys(userId);
      return ResponseUtil.success(result, 'Passkeys retrieved successfully');
    } catch (error) {
      this.logger.error(`Failed to list passkeys for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  @Delete('passkeys/:credentialId')
  @UseGuards(JwtAuthGuard)
  async removePasskey(
    @Param('credentialId') credentialId: string,
    @JwtUserId() userId: string
  ): Promise<StandardApiResponse<RemovePasskeyDataDto>> {
    this.logger.log(`Remove passkey: ${credentialId} for user: ${userId}`);

    try {
      const result = await this.webAuthnService.removePasskey(userId, credentialId);
      return ResponseUtil.success(result, 'Passkey removed successfully');
    } catch (error) {
      this.logger.error(`Failed to remove passkey ${credentialId}: ${error.message}`);
      throw error;
    }
  }

  @Put('passkeys/:credentialId/rename')
  @UseGuards(JwtAuthGuard)
  async renamePasskey(
    @Param('credentialId') credentialId: string,
    @Body() renameDto: RenamePasskeyDto,
    @JwtUserId() userId: string
  ): Promise<StandardApiResponse<RenamePasskeyDataDto>> {
    this.logger.log(`Rename passkey: ${credentialId} to "${renameDto.name}" for user: ${userId}`);

    try {
      const result = await this.webAuthnService.renamePasskey(userId, credentialId, renameDto.name);
      return ResponseUtil.success(result, 'Passkey renamed successfully');
    } catch (error) {
      this.logger.error(`Failed to rename passkey ${credentialId}: ${error.message}`);
      throw error;
    }
  }
}
