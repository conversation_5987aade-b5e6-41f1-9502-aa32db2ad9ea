import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@qeep/common';
import { Redis } from 'ioredis';
import { v4 as uuidv4 } from 'uuid';
import { ChallengeType, CredentialDeviceType } from '../../shared';
import {
  CompleteWebAuthnRegistrationDataDto,
  ListPasskeysDataDto,
  RemovePasskeyDataDto,
  RenamePasskeyDataDto,
  StartWebAuthnAuthenticationDataDto,
  StartWebAuthnRegistrationDataDto,
} from '../dto/webauthn-response.dto';

interface WebAuthnChallenge {
  id: string;
  challenge: string;
  userId: string;
  type: ChallengeType;
  createdAt: Date;
  expiresAt: Date;
}

interface WebAuthnCredential {
  id: string;
  credentialId: string;
  userId: string;
  publicKey: string;
  counter: number;
  deviceType: CredentialDeviceType;
  backedUp: boolean;
  transports: string[];
  authenticatorName?: string;
  createdAt: Date;
  lastUsed?: Date;
}

@Injectable()
export class WebAuthnService {
  private readonly logger = new Logger(WebAuthnService.name);
  private readonly redis: Redis;
  private readonly rpName = 'Qeep';
  private readonly rpId: string;
  private readonly origin: string;

  constructor(private readonly configService: ConfigService) {
    // Initialize Redis connection
    const redisConfig = this.configService.getRedisConfig();
    this.redis = new Redis({
      host: redisConfig.host,
      port: redisConfig.port,
      password: redisConfig.password,
      maxRetriesPerRequest: 3,
    });

    // Configure WebAuthn settings
    this.rpId = process.env.WEBAUTHN_RP_ID || 'localhost';
    this.origin = process.env.WEBAUTHN_ORIGIN || 'http://localhost:3000';
  }

  /**
   * Start WebAuthn registration process
   */
  async startRegistration(userId: string, authenticatorName?: string): Promise<StartWebAuthnRegistrationDataDto> {
    this.logger.log(`Starting WebAuthn registration for user: ${userId} with authenticator: ${authenticatorName}`);

    try {
      // Generate a challenge
      const challenge = uuidv4();

      // Store challenge in Redis with expiration
      const challengeData: WebAuthnChallenge = {
        id: uuidv4(),
        challenge,
        userId,
        type: ChallengeType.REGISTRATION,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 60000), // 1 minute
      };

      await this.redis.setex(`webauthn:challenge:${challenge}`, 60, JSON.stringify(challengeData));

      this.logger.log(`WebAuthn registration challenge created for user: ${userId}`);

      // Return simplified registration options
      return {
        challenge,
        rp: {
          name: this.rpName,
          id: this.rpId,
        },
        user: {
          id: userId,
          name: `user-${userId}`,
          displayName: `User ${userId}`,
        },
        pubKeyCredParams: [
          { type: 'public-key', alg: -7 },
          { type: 'public-key', alg: -257 },
        ],
        timeout: 60000,
        excludeCredentials: [], // TODO: Get existing credentials
        authenticatorSelection: {
          authenticatorAttachment: 'platform',
          userVerification: 'preferred',
          requireResidentKey: false,
        },
        attestation: 'none',
      };
    } catch (error) {
      this.logger.error(`Failed to start WebAuthn registration for user ${userId}: ${error.message}`);
      throw new BadRequestException('Failed to start WebAuthn registration');
    }
  }

  /**
   * Complete WebAuthn registration process
   */
  async completeRegistration(userId: string, challenge: string, credential: any, authenticatorName?: string): Promise<CompleteWebAuthnRegistrationDataDto> {
    this.logger.log(`Completing WebAuthn registration for user: ${userId}`);

    try {
      // Retrieve and validate challenge
      const challengeKey = `webauthn:challenge:${challenge}`;
      const challengeData = await this.redis.get(challengeKey);

      if (!challengeData) {
        throw new BadRequestException('Invalid or expired challenge');
      }

      const parsedChallenge: WebAuthnChallenge = JSON.parse(challengeData);

      if (parsedChallenge.userId !== userId || parsedChallenge.type !== ChallengeType.REGISTRATION) {
        throw new BadRequestException('Challenge mismatch');
      }

      // TODO: Implement proper WebAuthn verification
      // For now, simulate successful verification
      const credentialId = uuidv4();

      // Store credential (placeholder)
      const credentialData: WebAuthnCredential = {
        id: uuidv4(),
        credentialId,
        userId,
        publicKey: 'placeholder-public-key',
        counter: 0,
        deviceType: CredentialDeviceType.SINGLE_DEVICE,
        backedUp: false,
        transports: ['internal'],
        authenticatorName: authenticatorName || 'Unnamed Device',
        createdAt: new Date(),
      };

      await this.storeCredential(credentialData);

      // Clean up challenge
      await this.redis.del(challengeKey);

      this.logger.log(`WebAuthn credential registered successfully for user: ${userId}`);

      return {
        credentialId: credentialData.credentialId,
        verified: true,
        authenticatorName: credentialData.authenticatorName,
      };
    } catch (error) {
      this.logger.error(`Failed to complete WebAuthn registration for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Start WebAuthn authentication process
   */
  async startAuthentication(userId: string): Promise<StartWebAuthnAuthenticationDataDto> {
    this.logger.log(`Starting WebAuthn authentication for user: ${userId}`);

    try {
      // Get user's credentials (placeholder)
      const userCredentials = await this.getUserCredentials(userId);

      if (userCredentials.length === 0) {
        throw new BadRequestException('No WebAuthn credentials found for user');
      }

      // Generate a challenge
      const challenge = uuidv4();

      // Store challenge
      const challengeData: WebAuthnChallenge = {
        id: uuidv4(),
        challenge,
        userId,
        type: ChallengeType.AUTHENTICATION,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 60000),
      };

      await this.redis.setex(`webauthn:challenge:${challenge}`, 60, JSON.stringify(challengeData));

      return {
        challenge,
        timeout: 60000,
        rpId: this.rpId,
        allowCredentials: [], // TODO: Map user credentials
        userVerification: 'preferred',
      };
    } catch (error) {
      this.logger.error(`Failed to start WebAuthn authentication for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Complete WebAuthn authentication process
   */
  async completeAuthentication(challenge: string, credential: any): Promise<{ userId: string; isValid: boolean }> {
    this.logger.log(`Completing WebAuthn authentication for challenge: ${challenge}`);

    try {
      // Retrieve challenge
      const challengeKey = `webauthn:challenge:${challenge}`;
      const challengeData = await this.redis.get(challengeKey);

      if (!challengeData) {
        throw new BadRequestException('Invalid or expired challenge');
      }

      const parsedChallenge: WebAuthnChallenge = JSON.parse(challengeData);

      if (parsedChallenge.type !== ChallengeType.AUTHENTICATION) {
        throw new BadRequestException('Invalid challenge type');
      }

      // TODO: Implement proper WebAuthn verification
      // For now, simulate successful authentication
      const userId = parsedChallenge.userId;

      // Clean up challenge
      await this.redis.del(challengeKey);

      this.logger.log(`WebAuthn authentication successful for user: ${userId}`);

      return {
        userId,
        isValid: true,
      };
    } catch (error) {
      this.logger.error(`Failed to complete WebAuthn authentication: ${error.message}`);
      throw error;
    }
  }

  /**
   * List user's passkeys
   */
  async listPasskeys(userId: string): Promise<ListPasskeysDataDto> {
    this.logger.log(`Listing passkeys for user: ${userId}`);

    try {
      const credentials = await this.getUserCredentials(userId);

      const passkeys = credentials.map((cred) => ({
        id: cred.credentialId,
        name: cred.authenticatorName || 'Unnamed Device',
        createdAt: cred.createdAt.toISOString(),
        lastUsed: cred.lastUsed?.toISOString(),
        deviceType: cred.deviceType,
      }));

      return { passkeys };
    } catch (error) {
      this.logger.error(`Failed to list passkeys for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Remove a passkey
   */
  async removePasskey(userId: string, credentialId: string): Promise<RemovePasskeyDataDto> {
    this.logger.log(`Removing passkey ${credentialId} for user: ${userId}`);

    try {
      const credential = await this.getCredentialById(credentialId);

      if (!credential || credential.userId !== userId) {
        throw new BadRequestException('Passkey not found');
      }

      await this.deleteCredential(credentialId);

      this.logger.log(`Passkey removed successfully: ${credentialId}`);

      return { data: null };
    } catch (error) {
      this.logger.error(`Failed to remove passkey ${credentialId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Rename a passkey
   */
  async renamePasskey(userId: string, credentialId: string, newName: string): Promise<RenamePasskeyDataDto> {
    this.logger.log(`Renaming passkey ${credentialId} to "${newName}" for user: ${userId}`);

    try {
      const credential = await this.getCredentialById(credentialId);

      if (!credential || credential.userId !== userId) {
        throw new BadRequestException('Passkey not found');
      }

      await this.updateCredentialName(credentialId, newName);

      this.logger.log(`Passkey renamed successfully: ${credentialId}`);

      return { data: null };
    } catch (error) {
      this.logger.error(`Failed to rename passkey ${credentialId}: ${error.message}`);
      throw error;
    }
  }

  // Private helper methods (placeholder implementations)
  private async getUserCredentials(userId: string): Promise<WebAuthnCredential[]> {
    // TODO: Implement database query to get user credentials
    return [];
  }

  private async getCredentialById(credentialId: string): Promise<WebAuthnCredential | null> {
    // TODO: Implement database query to get credential by ID
    return null;
  }

  private async storeCredential(credential: WebAuthnCredential): Promise<void> {
    // TODO: Implement database storage for credential
    this.logger.log(`Storing credential: ${credential.credentialId}`);
  }

  private async updateCredentialCounter(credentialId: string, newCounter: number): Promise<void> {
    // TODO: Implement database update for credential counter
    this.logger.log(`Updating credential counter: ${credentialId} -> ${newCounter}`);
  }

  private async updateCredentialLastUsed(credentialId: string): Promise<void> {
    // TODO: Implement database update for last used timestamp
    this.logger.log(`Updating credential last used: ${credentialId}`);
  }

  private async deleteCredential(credentialId: string): Promise<void> {
    // TODO: Implement database deletion for credential
    this.logger.log(`Deleting credential: ${credentialId}`);
  }

  private async updateCredentialName(credentialId: string, newName: string): Promise<void> {
    // TODO: Implement database update for credential name
    this.logger.log(`Updating credential name: ${credentialId} -> ${newName}`);
  }
}
