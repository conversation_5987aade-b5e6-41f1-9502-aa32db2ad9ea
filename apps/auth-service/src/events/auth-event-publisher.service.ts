import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@qeep/common';
import { Kafka, Producer } from 'kafkajs';
import { AuthAuditEvent } from './auth.events';

@Injectable()
export class AuthEventPublisher implements OnModuleInit {
  private readonly logger = new Logger(AuthEventPublisher.name);
  private kafka: Kafka;
  private producer: Producer;

  constructor(private readonly configService: ConfigService) {}

  async onModuleInit() {
    try {
      // Silence KafkaJS partitioner warning
      process.env.KAFKAJS_NO_PARTITIONER_WARNING = '1';

      // Initialize Kafka client
      const kafkaConfig = this.configService.getKafkaConfig();
      this.kafka = new Kafka({
        clientId: kafkaConfig.clientId,
        brokers: kafkaConfig.brokers,
        retry: {
          initialRetryTime: 100,
          retries: 8,
        },
      });

      // Create producer with proper configuration
      this.producer = this.kafka.producer({
        maxInFlightRequests: 5, // Increase for better performance
        idempotent: false, // Disable idempotent to avoid EoS warnings
        retry: {
          initialRetryTime: 100,
          retries: 5,
        },
        // Use legacy partitioner to maintain compatibility
        createPartitioner: () => {
          // Simple round-robin partitioner
          let partition = 0;
          return ({ partitionMetadata }) => {
            const numPartitions = partitionMetadata.length;
            const selectedPartition = partition % numPartitions;
            partition++;
            return selectedPartition;
          };
        },
      });

      // Connect producer
      await this.producer.connect();
      this.logger.log('Kafka producer connected successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Kafka producer:', error);
      // Don't throw error to prevent service startup failure
    }
  }

  async onModuleDestroy() {
    try {
      if (this.producer) {
        await this.producer.disconnect();
        this.logger.log('Kafka producer disconnected');
      }
    } catch (error) {
      this.logger.error('Error disconnecting Kafka producer:', error);
    }
  }

  /**
   * Publish audit event
   */
  async publishAuditEvent(event: AuthAuditEvent): Promise<void> {
    await this.publishEvent('auth.audit', event);
  }

  /**
   * Generic method to publish events to Kafka
   */
  private async publishEvent(topic: string, event: any): Promise<void> {
    try {
      if (!this.producer) {
        this.logger.warn(`Kafka producer not available, skipping event: ${topic}`);
        return;
      }

      // Ensure timestamp is valid and positive
      const eventTimestamp = event.timestamp instanceof Date ? event.timestamp.getTime() : Date.now();
      const validTimestamp = eventTimestamp > 0 ? eventTimestamp : Date.now();

      const message = {
        key: event.userId || event.email || 'unknown',
        value: JSON.stringify({
          ...event,
          eventId: event.eventId || this.generateEventId(),
          publishedAt: new Date().toISOString(),
        }),
        timestamp: validTimestamp.toString(),
      };

      await this.producer.send({
        topic,
        messages: [message],
      });

      this.logger.debug(`Published event to topic ${topic}:`, { eventId: message.value });
    } catch (error) {
      this.logger.error(`Failed to publish event to topic ${topic}:`, error);
      // Don't throw error to prevent login failure due to event publishing issues
    }
  }

  /**
   * Generate unique event ID
   */
  private generateEventId(): string {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }
}
