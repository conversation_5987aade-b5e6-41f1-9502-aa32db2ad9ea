/**
 * Authentication domain event definitions for Kafka integration
 */

export interface BaseAuthEvent {
  eventId?: string;
  timestamp: Date;
  source: string;
  version: string;
}

export interface AuthAuditEvent extends BaseAuthEvent {
  action: string;
  userId?: string;
  email?: string;
  tenantCode?: string | null;
  result: 'SUCCESS' | 'FAILURE' | 'PENDING';

  // Actor information (who performed the action)
  ip?: string;
  userAgent?: string;
  location?: Record<string, unknown>;

  // Target information (what was affected)
  targetType?: string;
  targetId?: string;
  targetName?: string;

  // Change tracking
  oldValues?: Record<string, unknown>;
  newValues?: Record<string, unknown>;

  // Error information
  errorCode?: string;
  errorMessage?: string;

  // Additional context
  additionalData?: Record<string, unknown>;
}

/**
 * Authentication event factory
 */
export class AuthEventFactory {
  private static readonly SOURCE = 'auth-service';
  private static readonly VERSION = '1.0.0';

  static createAuditEvent(data: {
    action: string;
    userId?: string;
    email?: string;
    tenantCode?: string | null;
    result: 'SUCCESS' | 'FAILURE' | 'PENDING';

    // Actor information
    ip?: string;
    userAgent?: string;
    location?: Record<string, unknown>;

    // Target information
    targetType?: string;
    targetId?: string;
    targetName?: string;

    // Change tracking
    oldValues?: Record<string, unknown>;
    newValues?: Record<string, unknown>;

    // Error information
    errorCode?: string;
    errorMessage?: string;

    // Additional context
    additionalData?: Record<string, unknown>;
  }): AuthAuditEvent {
    return {
      ...data,
      timestamp: new Date(),
      source: this.SOURCE,
      version: this.VERSION,
      ip: data.ip || 'unknown',
      userAgent: data.userAgent || 'unknown',
      location: data.location || null,
      targetType: data.targetType || null,
      targetId: data.targetId || null,
      targetName: data.targetName || null,
      oldValues: data.oldValues || null,
      newValues: data.newValues || null,
    };
  }
}
