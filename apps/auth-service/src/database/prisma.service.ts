import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/auth-client';
import { BasePrismaService } from '@qeep/common';

@Injectable()
export class AuthPrismaService extends BasePrismaService {
  protected prismaClient: PrismaClient;

  constructor() {
    super();
    this.prismaClient = new PrismaClient({
      log: ['error'],
      errorFormat: 'colorless',
    });
  }

  // Expose Prisma client methods for session management
  get authSession() {
    return this.prismaClient.authSession;
  }

  // Expose Prisma client methods for authentication events
  get authEvent() {
    return this.prismaClient.authEvent;
  }

  // Expose Prisma client methods for authentication tokens
  get authToken() {
    return this.prismaClient.authToken;
  }

  // Expose Prisma client methods for account security
  get accountSecurity() {
    return this.prismaClient.accountSecurity;
  }

  // Expose Prisma client methods for trusted devices
  get trustedDevice() {
    return this.prismaClient.trustedDevice;
  }

  // Expose raw query methods
  get $queryRaw() {
    return this.prismaClient.$queryRaw.bind(this.prismaClient);
  }

  get $executeRaw() {
    return this.prismaClient.$executeRaw.bind(this.prismaClient);
  }

  get $transaction() {
    return this.prismaClient.$transaction.bind(this.prismaClient);
  }
}
