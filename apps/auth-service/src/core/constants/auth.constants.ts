// Authentication service constants

export const AUTH_CONSTANTS = {
  // Token expiration times
  ACCESS_TOKEN_EXPIRY: '1h',
  REFRESH_TOKEN_EXPIRY: '7d',
  VERIFICATION_TOKEN_EXPIRY: 86400, // 24 hours in seconds
  PASSWORD_RESET_TOKEN_EXPIRY: 3600, // 1 hour in seconds

  // Account lockout settings
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 900, // 15 minutes in seconds
  FAILED_ATTEMPTS_WINDOW: 900, // 15 minutes in seconds

  // Password requirements
  MIN_PASSWORD_LENGTH: 8,
  MAX_PASSWORD_LENGTH: 128,

  // Organization settings
  ORGANIZATION_NAME: 'Qeep',
  SUPPORT_EMAIL: '<EMAIL>',
  DEFAULT_FRONTEND_URL: 'http://localhost:3000',

  // Default values
  UNKNOWN_VALUE: 'unknown',
  DEFAULT_CACHE_TTL: 30 * 24 * 60 * 60, // 30 days in seconds

  // Redis key prefixes
  REDIS_KEYS: {
    REFRESH_TOKEN: 'refresh_token:',
    VERIFICATION: 'verification:',
    PASSWORD_RESET: 'password_reset:',
    LOCKOUT: 'lockout:',
    ATTEMPTS: 'attempts:',
    WELCOME_EMAIL_SENT: 'welcome_email_sent:',
  },

  // Error codes
  ERROR_CODES: {
    INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
    ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
    ACCOUNT_INACTIVE: 'ACCOUNT_INACTIVE',
    EMAIL_NOT_VERIFIED: 'EMAIL_NOT_VERIFIED',
    TOKEN_EXPIRED: 'TOKEN_EXPIRED',
    TOKEN_INVALID: 'TOKEN_INVALID',
  },
} as const;
