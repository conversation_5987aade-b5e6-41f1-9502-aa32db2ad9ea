/* eslint-disable @typescript-eslint/no-explicit-any */
// eslint-disable-next-line @nx/enforce-module-boundaries
import { createId } from '@paralleldrive/cuid2';
import { PrismaClient } from '../../../node_modules/.prisma/auth-client/index.js';

const prisma = new PrismaClient();

// Temporary CUID generation functions for seed
function generateSecuritySettingsId(): string {
  return `sec_${createId()}`;
}

function generateAuthEventId(): string {
  return `aev_${createId()}`;
}

function generateTrustedDeviceId(): string {
  return `trd_${createId()}`;
}

async function createSecuritySettings() {
  console.log('🔐 Creating comprehensive security settings for all Ronna Bank users...');

  // Proper CUID format IDs to match user service
  const ronnaTenantId = 'ten_bzuebwg93hq2qkmmigli5ccz';

  // User ID constants (matching user service CUIDs)
  const adminUserId = 'usr_venjjfonshdukzl2eiw43h3e';
  const complianceUserId = 'usr_f6gnr7cxcqcgmlbmswzcwa4s'; // Using manager user for compliance device

  // Define all users with their security configurations (using proper CUID format)
  const userSecurityConfigs = [
    {
      userId: 'usr_zxy6wrs36byld55jiymv6mlx', // super_admin
      email: '<EMAIL>',
      role: 'super_admin',
      twoFactorEnabled: true,
      twoFactorSecret: 'JBSWY3DPEHPK3PXP',
      twoFactorBackupCodes: ['SA123456', 'SA789012', 'SA345678', 'SA901234', 'SA567890'],
      securityQuestions: [
        {
          question: "What is the name of Ghana's first president?",
          answer_hash: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO9G', // "nkrumah"
        },
        {
          question: 'What is the capital of the Ashanti Region?',
          answer_hash: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO9G', // "kumasi"
        },
      ],
      loginRestrictions: {
        allowed_ip_ranges: ['***********/24', '10.0.0.0/8', '**********/12'],
        blocked_countries: ['CN', 'RU', 'KP', 'IR'],
        require_device_verification: true,
        max_concurrent_sessions: 2,
      },
      passwordPolicy: {
        min_length: 12,
        require_uppercase: true,
        require_lowercase: true,
        require_numbers: true,
        require_special_chars: true,
        password_history: 10,
        max_age_days: 60,
      },
    },
    {
      userId: 'usr_venjjfonshdukzl2eiw43h3e', // admin
      email: '<EMAIL>',
      role: 'admin',
      twoFactorEnabled: true,
      twoFactorSecret: 'KBSWY3DPEHPK3PXQ',
      twoFactorBackupCodes: ['AD123456', 'AD789012', 'AD345678', 'AD901234'],
      securityQuestions: [
        {
          question: 'What was the name of your first school?',
          answer_hash: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO9G', // "presec"
        },
      ],
      loginRestrictions: {
        allowed_ip_ranges: ['***********/24', '10.0.0.0/8'],
        blocked_countries: ['CN', 'RU', 'KP'],
        require_device_verification: true,
        max_concurrent_sessions: 3,
      },
      passwordPolicy: {
        min_length: 10,
        require_uppercase: true,
        require_lowercase: true,
        require_numbers: true,
        require_special_chars: true,
        password_history: 8,
        max_age_days: 90,
      },
    },
    {
      userId: 'usr_f6gnr7cxcqcgmlbmswzcwa4s', // manager
      email: '<EMAIL>',
      role: 'manager',
      twoFactorEnabled: false,
      twoFactorSecret: null,
      twoFactorBackupCodes: null,
      securityQuestions: [
        {
          question: 'What is your favorite Ghanaian dish?',
          answer_hash: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO9G', // "jollof"
        },
      ],
      loginRestrictions: {
        allowed_ip_ranges: ['***********/24'],
        blocked_countries: ['CN', 'RU'],
        require_device_verification: false,
        max_concurrent_sessions: 5,
      },
      passwordPolicy: {
        min_length: 8,
        require_uppercase: true,
        require_lowercase: true,
        require_numbers: true,
        require_special_chars: false,
        password_history: 5,
        max_age_days: 120,
      },
    },
    {
      userId: 'usr_xgonycskymihc5ohsbxefecm', // support
      email: '<EMAIL>',
      role: 'support',
      twoFactorEnabled: false,
      twoFactorSecret: null,
      twoFactorBackupCodes: null,
      securityQuestions: [
        {
          question: 'What is the name of your hometown?',
          answer_hash: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO9G', // "tamale"
        },
      ],
      loginRestrictions: {
        allowed_ip_ranges: ['***********/24'],
        blocked_countries: [],
        require_device_verification: false,
        max_concurrent_sessions: 3,
      },
      passwordPolicy: {
        min_length: 8,
        require_uppercase: true,
        require_lowercase: true,
        require_numbers: false,
        require_special_chars: false,
        password_history: 3,
        max_age_days: 180,
      },
    },
    {
      userId: 'usr_b6zzfbdhttr9d6y9w3af9lot', // user
      email: '<EMAIL>',
      role: 'user',
      twoFactorEnabled: false,
      twoFactorSecret: null,
      twoFactorBackupCodes: null,
      securityQuestions: [],
      loginRestrictions: {
        allowed_ip_ranges: [],
        blocked_countries: [],
        require_device_verification: false,
        max_concurrent_sessions: 2,
      },
      passwordPolicy: {
        min_length: 6,
        require_uppercase: false,
        require_lowercase: true,
        require_numbers: false,
        require_special_chars: false,
        password_history: 1,
        max_age_days: 365,
      },
    },
  ];

  try {
    // Create account security settings for all users
    for (const config of userSecurityConfigs) {
      const accountSecurity = await prisma.accountSecurity.upsert({
        where: { userId: config.userId },
        update: {},
        create: {
          id: generateSecuritySettingsId(),
          userId: config.userId,
          tenantId: ronnaTenantId,
          tenantCode: 'ronna-bank',
          twoFactorEnabled: config.twoFactorEnabled,
          twoFactorSecret: config.twoFactorSecret,
          twoFactorBackupCodes: config.twoFactorBackupCodes || undefined,
          securityQuestions: config.securityQuestions,
          passwordChangedAt: new Date(),
        },
      });

      console.log(`✅ Created security settings for ${config.role} user: ${config.email}`);
    }

    // Create sample auth events for testing (using proper CUID format)
    const authEvents = [
      {
        userId: 'usr_zxy6wrs36byld55jiymv6mlx', // super_admin
        tenantId: ronnaTenantId,
        tenantCode: 'ronna-bank',
        eventType: 'LOGIN_SUCCESS' as any,
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        location: { country: 'Ghana', city: 'Accra', region: 'Greater Accra' },
        metadata: {
          login_method: 'password_mfa',
          session_duration: 3600,
          risk_score: 5,
          device_type: 'desktop',
        },
      },
      {
        userId: 'usr_venjjfonshdukzl2eiw43h3e', // admin
        tenantId: ronnaTenantId,
        tenantCode: 'ronna-bank',
        eventType: 'LOGIN_SUCCESS' as any,
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        location: { country: 'Ghana', city: 'Kumasi', region: 'Ashanti' },
        metadata: {
          login_method: 'password_mfa',
          mfa_method: 'totp',
          session_duration: 1800,
          risk_score: 5,
          device_type: 'desktop',
        },
      },
      {
        userId: 'usr_f6gnr7cxcqcgmlbmswzcwa4s', // manager
        tenantId: ronnaTenantId,
        tenantCode: 'ronna-bank',
        eventType: 'LOGIN_SUCCESS' as any,
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15',
        location: { country: 'Ghana', city: 'Tamale', region: 'Northern' },
        metadata: {
          login_method: 'password',
          session_duration: 2400,
          risk_score: 15,
          device_type: 'mobile',
        },
      },
    ];

    for (const eventData of authEvents) {
      await prisma.authEvent.create({
        data: {
          id: generateAuthEventId(),
          ...eventData,
        },
      });
    }

    console.log(`✅ Created ${authEvents.length} sample auth events`);

    // Create trusted devices
    const trustedDevices = [
      {
        userId: adminUserId,
        tenantId: ronnaTenantId,
        deviceId: 'fp_admin_device_001',
        deviceName: 'Admin MacBook Pro',
        deviceType: 'desktop',
        browser: 'Chrome',
        operatingSystem: 'macOS',
        ipAddress: '*************',
        location: {
          city: 'New York',
          state: 'NY',
          country: 'US',
          timezone: 'America/New_York',
        },
        lastUsedAt: new Date(),
      },
      {
        userId: complianceUserId,
        tenantId: ronnaTenantId,
        deviceId: 'fp_compliance_device_001',
        deviceName: 'Compliance Windows PC',
        deviceType: 'desktop',
        browser: 'Chrome',
        operatingSystem: 'Windows',
        ipAddress: '*************',
        location: {
          city: 'New York',
          state: 'NY',
          country: 'US',
          timezone: 'America/New_York',
        },
        lastUsedAt: new Date(),
      },
    ];

    for (const deviceData of trustedDevices) {
      await prisma.trustedDevice.create({
        data: {
          id: generateTrustedDeviceId(),
          ...deviceData,
        },
      });
    }

    console.log(`✅ Created ${trustedDevices.length} trusted devices`);
  } catch (error) {
    console.error('❌ Error creating security settings:', error.message);
    throw error;
  }
}

async function main() {
  console.log('🌱 Seeding Auth Service database...');

  // Create security settings and sample data
  await createSecuritySettings();

  console.log('📝 Auth service database seeded successfully!');
  console.log('');
  console.log('🔐 Auth Service Database Summary:');
  console.log('  - ✅ Comprehensive security settings for all 5 Ronna Bank users');
  console.log('  - ✅ Role-based security configurations:');
  console.log('    • super_admin: Maximum security (MFA required, strict policies)');
  console.log('    • admin: High security (MFA enabled, enhanced policies)');
  console.log('    • manager: Medium security (MFA optional, standard policies)');
  console.log('    • support: Basic security (MFA disabled, relaxed policies)');
  console.log('    • user: Minimal security (MFA disabled, basic policies)');
  console.log('  - ✅ Sample auth events for testing');
  console.log('  - ✅ Ghana-specific security questions and configurations');
  console.log('');
  console.log('🔑 Security Features by Role:');
  console.log('  - Multi-factor authentication (TOTP) for super_admin and admin');
  console.log('  - Role-appropriate password policies');
  console.log('  - IP-based access restrictions (stricter for higher roles)');
  console.log('  - Device verification requirements');
  console.log('  - Comprehensive audit logging');
  console.log('');
  console.log('� Test MFA Credentials:');
  console.log('  - <EMAIL>: JBSWY3DPEHPK3PXP');
  console.log('  - <EMAIL>: KBSWY3DPEHPK3PXQ');
}

main()
  .catch((e) => {
    console.error('❌ Auth Service database seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
