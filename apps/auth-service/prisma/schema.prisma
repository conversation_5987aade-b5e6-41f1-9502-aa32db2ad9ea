generator client {
  provider = "prisma-client-js"
  output   = "../../../node_modules/.prisma/auth-client"
}

datasource db {
  provider = "postgresql"
  url      = env("AUTH_DATABASE_URL")
}

// Auth Service Prisma Schema
// Manages authentication, sessions, and security-related data

enum SessionStatus {
  ACTIVE
  EXPIRED
  REVOKED
  SUSPENDED
}

enum AuthEventType {
  LOGIN_SUCCESS
  LOGIN_FAILED
  LOGOUT
  PASSWORD_RESET_REQUEST
  PASSWORD_RESET_SUCCESS
  EMAIL_VERIFICATION
  ACCOUNT_LOCKED
  ACCOUNT_UNLOCKED
  SESSION_EXPIRED
  SESSION_REVOKED
  TWO_FACTOR_ENABLED
  TWO_FACTOR_DISABLED
  TWO_FACTOR_SUCCESS
  TWO_FACTOR_FAILED
}

// ============================================================================
// SESSION MANAGEMENT
// ============================================================================

model AuthSession {
  id              String        @id @db.VarChar(35)
  userId          String        @map("user_id") @db.VarChar(35)
  tenantId        String?       @map("tenant_id") @db.VarChar(35)
  tenantCode      String?       @map("tenant_code") @db.VarChar(50)
  sessionToken    String        @unique @map("session_token") @db.VarChar(255)
  refreshToken    String?       @unique @map("refresh_token") @db.VarChar(255)
  status          SessionStatus @default(ACTIVE)
  ipAddress       String?       @map("ip_address") @db.VarChar(45)
  userAgent       String?       @map("user_agent") @db.Text
  deviceInfo      Json?         @map("device_info")
  location        Json?         @map("location") // Country, city, etc.
  createdAt       DateTime      @default(now()) @map("created_at") @db.Timestamptz(6)
  lastAccessedAt  DateTime      @default(now()) @map("last_accessed_at") @db.Timestamptz(6)
  expiresAt       DateTime      @map("expires_at") @db.Timestamptz(6)
  revokedAt       DateTime?     @map("revoked_at") @db.Timestamptz(6)
  revokedBy       String?       @map("revoked_by") @db.VarChar(35)
  revokedReason   String?       @map("revoked_reason") @db.VarChar(255)

  @@index([userId], map: "idx_auth_sessions_user_id")
  @@index([tenantId], map: "idx_auth_sessions_tenant_id")
  @@index([tenantCode], map: "idx_auth_sessions_tenant_code")
  @@index([sessionToken], map: "idx_auth_sessions_session_token")
  @@index([refreshToken], map: "idx_auth_sessions_refresh_token")
  @@index([status], map: "idx_auth_sessions_status")
  @@index([createdAt], map: "idx_auth_sessions_created_at")
  @@index([expiresAt], map: "idx_auth_sessions_expires_at")
  @@index([userId, status], map: "idx_auth_sessions_user_status")
  @@map("auth_sessions")
}



// ============================================================================
// AUTHENTICATION EVENTS & AUDIT
// ============================================================================

model AuthEvent {
  id          String        @id @db.VarChar(35)
  userId      String?       @map("user_id") @db.VarChar(35)
  tenantId    String?       @map("tenant_id") @db.VarChar(35)
  tenantCode  String?       @map("tenant_code") @db.VarChar(50)
  sessionId   String?       @map("session_id") @db.VarChar(35)
  eventType   AuthEventType @map("event_type")
  email       String?       @db.VarChar(255)
  ipAddress   String?       @map("ip_address") @db.VarChar(45)
  userAgent   String?       @map("user_agent") @db.Text
  deviceInfo  Json?         @map("device_info")
  location    Json?         @map("location")
  metadata    Json?         @map("metadata") // Additional event-specific data
  success     Boolean       @default(true)
  errorCode   String?       @map("error_code") @db.VarChar(50)
  errorMessage String?      @map("error_message") @db.Text
  timestamp   DateTime      @default(now()) @db.Timestamptz(6)

  @@index([userId], map: "idx_auth_events_user_id")
  @@index([tenantId], map: "idx_auth_events_tenant_id")
  @@index([tenantCode], map: "idx_auth_events_tenant_code")
  @@index([sessionId], map: "idx_auth_events_session_id")
  @@index([eventType], map: "idx_auth_events_event_type")
  @@index([email], map: "idx_auth_events_email")
  @@index([ipAddress], map: "idx_auth_events_ip_address")
  @@index([timestamp], map: "idx_auth_events_timestamp")
  @@index([success], map: "idx_auth_events_success")
  @@index([userId, eventType], map: "idx_auth_events_user_event_type")
  @@index([timestamp, eventType], map: "idx_auth_events_timestamp_event_type")
  @@map("auth_events")
}

// ============================================================================
// PASSWORD RESET & EMAIL VERIFICATION
// ============================================================================

enum TokenType {
  PASSWORD_RESET
  EMAIL_VERIFICATION
  TWO_FACTOR_SETUP
  ACCOUNT_ACTIVATION
}

enum TokenStatus {
  PENDING
  USED
  EXPIRED
  REVOKED
}

model AuthToken {
  id          String      @id @db.VarChar(35)
  userId      String      @map("user_id") @db.VarChar(35)
  tenantId    String?     @map("tenant_id") @db.VarChar(35)
  tenantCode  String?     @map("tenant_code") @db.VarChar(50)
  token       String      @unique @db.VarChar(255)
  tokenType   TokenType   @map("token_type")
  status      TokenStatus @default(PENDING)
  email       String      @db.VarChar(255)
  metadata    Json?       @map("metadata") // Additional token-specific data
  createdAt   DateTime    @default(now()) @map("created_at") @db.Timestamptz(6)
  expiresAt   DateTime    @map("expires_at") @db.Timestamptz(6)
  usedAt      DateTime?   @map("used_at") @db.Timestamptz(6)
  revokedAt   DateTime?   @map("revoked_at") @db.Timestamptz(6)

  @@index([userId], map: "idx_auth_tokens_user_id")
  @@index([tenantId], map: "idx_auth_tokens_tenant_id")
  @@index([tenantCode], map: "idx_auth_tokens_tenant_code")
  @@index([token], map: "idx_auth_tokens_token")
  @@index([tokenType], map: "idx_auth_tokens_token_type")
  @@index([status], map: "idx_auth_tokens_status")
  @@index([email], map: "idx_auth_tokens_email")
  @@index([createdAt], map: "idx_auth_tokens_created_at")
  @@index([expiresAt], map: "idx_auth_tokens_expires_at")
  @@index([userId, tokenType], map: "idx_auth_tokens_user_token_type")
  @@map("auth_tokens")
}

// ============================================================================
// ACCOUNT SECURITY & LOCKOUT
// ============================================================================

model AccountSecurity {
  id                    String    @id @db.VarChar(35)
  userId                String    @unique @map("user_id") @db.VarChar(35)
  tenantId              String?   @map("tenant_id") @db.VarChar(35)
  tenantCode            String?   @map("tenant_code") @db.VarChar(50)
  failedLoginAttempts   Int       @default(0) @map("failed_login_attempts")
  lastFailedLoginAt     DateTime? @map("last_failed_login_at") @db.Timestamptz(6)
  lockedAt              DateTime? @map("locked_at") @db.Timestamptz(6)
  lockedUntil           DateTime? @map("locked_until") @db.Timestamptz(6)
  lockoutReason         String?   @map("lockout_reason") @db.VarChar(255)
  passwordChangedAt     DateTime? @map("password_changed_at") @db.Timestamptz(6)
  twoFactorEnabled      Boolean   @default(false) @map("two_factor_enabled")
  twoFactorSecret       String?   @map("two_factor_secret") @db.VarChar(255)
  twoFactorBackupCodes  Json?     @map("two_factor_backup_codes")
  securityQuestions     Json?     @map("security_questions")
  lastSecurityUpdate    DateTime? @map("last_security_update") @db.Timestamptz(6)
  createdAt             DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt             DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)

  @@index([userId], map: "idx_account_security_user_id")
  @@index([tenantId], map: "idx_account_security_tenant_id")
  @@index([tenantCode], map: "idx_account_security_tenant_code")
  @@index([lockedAt], map: "idx_account_security_locked_at")
  @@index([lockedUntil], map: "idx_account_security_locked_until")
  @@index([twoFactorEnabled], map: "idx_account_security_two_factor_enabled")
  @@map("account_security")
}

// ============================================================================
// MULTI-FACTOR AUTHENTICATION
// ============================================================================

enum MfaMethod {
  TOTP
  SMS
  EMAIL
  HARDWARE_KEY
}

enum MfaStatus {
  ACTIVE
  INACTIVE
  LOCKED
  EXPIRED
}

model MfaConfiguration {
  id              String    @id @db.VarChar(35)
  userId          String    @unique @map("user_id") @db.VarChar(35)
  tenantId        String?   @map("tenant_id") @db.VarChar(35)
  tenantCode      String?   @map("tenant_code") @db.VarChar(50)
  method          MfaMethod
  secret          String?   @db.Text // TOTP secret (encrypted)
  phoneNumber     String?   @map("phone_number") @db.VarChar(20) // For SMS
  email           String?   @db.VarChar(255) // For EMAIL
  backupCodes     Json?     @map("backup_codes") // Array of backup codes (encrypted)
  isEnabled       Boolean   @default(false) @map("is_enabled")
  status          MfaStatus @default(ACTIVE)
  failedAttempts  Int       @default(0) @map("failed_attempts")
  lockedAt        DateTime? @map("locked_at") @db.Timestamptz(6)
  lockedUntil     DateTime? @map("locked_until") @db.Timestamptz(6)
  lastUsedAt      DateTime? @map("last_used_at") @db.Timestamptz(6)
  setupAt         DateTime? @map("setup_at") @db.Timestamptz(6)
  createdAt       DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt       DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)

  // Relations
  challenges MfaChallenge[]

  @@index([userId], map: "idx_mfa_configurations_user_id")
  @@index([tenantId], map: "idx_mfa_configurations_tenant_id")
  @@index([tenantCode], map: "idx_mfa_configurations_tenant_code")
  @@index([method], map: "idx_mfa_configurations_method")
  @@index([isEnabled], map: "idx_mfa_configurations_is_enabled")
  @@index([status], map: "idx_mfa_configurations_status")
  @@index([lockedUntil], map: "idx_mfa_configurations_locked_until")
  @@index([lastUsedAt], map: "idx_mfa_configurations_last_used_at")
  @@map("mfa_configurations")
}

model MfaChallenge {
  id            String    @id @db.VarChar(35)
  userId        String    @map("user_id") @db.VarChar(35)
  challengeId   String    @unique @map("challenge_id") @db.VarChar(255)
  method        MfaMethod
  code          String?   @db.VarChar(50) // For SMS/EMAIL (encrypted)
  attempts      Int       @default(0)
  maxAttempts   Int       @default(3) @map("max_attempts")
  isUsed        Boolean   @default(false) @map("is_used")
  usedAt        DateTime? @map("used_at") @db.Timestamptz(6)
  expiresAt     DateTime  @map("expires_at") @db.Timestamptz(6)
  createdAt     DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)

  // Relations
  mfaConfiguration MfaConfiguration @relation(fields: [userId], references: [userId], onDelete: Cascade)

  @@index([userId], map: "idx_mfa_challenges_user_id")
  @@index([challengeId], map: "idx_mfa_challenges_challenge_id")
  @@index([method], map: "idx_mfa_challenges_method")
  @@index([expiresAt], map: "idx_mfa_challenges_expires_at")
  @@index([isUsed], map: "idx_mfa_challenges_is_used")
  @@index([createdAt], map: "idx_mfa_challenges_created_at")
  @@map("mfa_challenges")
}

// ============================================================================
// DEVICE MANAGEMENT
// ============================================================================

enum DeviceStatus {
  ACTIVE
  INACTIVE
  BLOCKED
  SUSPICIOUS
}

model TrustedDevice {
  id              String       @id @db.VarChar(35)
  userId          String       @map("user_id") @db.VarChar(35)
  tenantId        String?      @map("tenant_id") @db.VarChar(35)
  tenantCode      String?      @map("tenant_code") @db.VarChar(50)
  deviceId        String       @unique @map("device_id") @db.VarChar(255)
  deviceName      String?      @map("device_name") @db.VarChar(255)
  deviceType      String?      @map("device_type") @db.VarChar(50) // mobile, desktop, tablet
  operatingSystem String?      @map("operating_system") @db.VarChar(100)
  browser         String?      @map("browser") @db.VarChar(100)
  status          DeviceStatus @default(ACTIVE)
  ipAddress       String?      @map("ip_address") @db.VarChar(45)
  location        Json?        @map("location")
  lastUsedAt      DateTime?    @map("last_used_at") @db.Timestamptz(6)
  trustedAt       DateTime     @default(now()) @map("trusted_at") @db.Timestamptz(6)
  expiresAt       DateTime?    @map("expires_at") @db.Timestamptz(6)
  createdAt       DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt       DateTime     @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)

  @@index([userId], map: "idx_trusted_devices_user_id")
  @@index([tenantId], map: "idx_trusted_devices_tenant_id")
  @@index([tenantCode], map: "idx_trusted_devices_tenant_code")
  @@index([deviceId], map: "idx_trusted_devices_device_id")
  @@index([status], map: "idx_trusted_devices_status")
  @@index([lastUsedAt], map: "idx_trusted_devices_last_used_at")
  @@index([userId, status], map: "idx_trusted_devices_user_status")
  @@map("trusted_devices")
}
