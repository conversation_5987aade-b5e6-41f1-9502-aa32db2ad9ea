/* eslint-disable @nx/enforce-module-boundaries */
import { PrismaClient as AuthPrismaClient } from '../../node_modules/.prisma/auth-client/index.js';
import { PrismaClient as UserPrismaClient } from '../../node_modules/.prisma/user-client/index.js';

const authPrisma = new AuthPrismaClient();
const userPrisma = new UserPrismaClient();

async function testDatabaseAccess() {
  console.log('🔍 TESTING DATABASE ACCESS AFTER PERMISSION FIX');
  console.log('=================================================\n');

  try {
    // Test User Service Database Access
    console.log('📊 TESTING USER SERVICE DATABASE ACCESS:');
    console.log('=========================================');
    
    const userCount = await userPrisma.user.count();
    console.log(`✅ User count: ${userCount}`);
    
    const sampleUser = await userPrisma.user.findFirst({
      select: { id: true, email: true }
    });
    
    if (sampleUser) {
      console.log(`✅ Sample user: ${sampleUser.email} (ID: ${sampleUser.id})`);
      console.log(`   ID format valid: ${/^usr_[0-9a-z]{24}$/.test(sampleUser.id)}`);
    }

    // Test Auth Service Database Access
    console.log('\n📊 TESTING AUTH SERVICE DATABASE ACCESS:');
    console.log('=========================================');
    
    const securityCount = await authPrisma.accountSecurity.count();
    console.log(`✅ Account security count: ${securityCount}`);
    
    const sampleSecurity = await authPrisma.accountSecurity.findFirst({
      select: { id: true, userId: true, twoFactorEnabled: true }
    });
    
    if (sampleSecurity) {
      console.log(`✅ Sample security: User ${sampleSecurity.userId} (ID: ${sampleSecurity.id})`);
      console.log(`   ID format valid: ${/^sec_[0-9a-z]{24}$/.test(sampleSecurity.id)}`);
      console.log(`   2FA enabled: ${sampleSecurity.twoFactorEnabled}`);
    }

    console.log('\n🎉 DATABASE ACCESS TEST SUCCESSFUL!');
    console.log('===================================');
    console.log('✅ Both services can access their databases');
    console.log('✅ All CUID formats are working correctly');
    console.log('✅ Database permissions are properly configured');
    console.log('✅ Services are ready for operation');

  } catch (error) {
    console.error('❌ Database access test failed:', error);
    
    if (error.message?.includes('permission denied')) {
      console.error('🔧 Database permission issue detected');
      console.error('   Please ensure the database user has proper permissions');
    }
  } finally {
    await authPrisma.$disconnect();
    await userPrisma.$disconnect();
  }
}

testDatabaseAccess()
  .catch((e) => {
    console.error('❌ Test script failed:', e);
    process.exit(1);
  });
