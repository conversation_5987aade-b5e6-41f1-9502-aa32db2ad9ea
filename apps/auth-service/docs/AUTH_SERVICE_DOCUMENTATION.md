# Auth Service Domain Architecture Documentation

## Overview

The Auth Service follows a Domain-Driven Design (DDD) approach with clear separation of concerns across multiple domains. Each domain encapsulates specific business logic and responsibilities related to authentication and authorization.

## Domain Structure

The auth service is organized into the following domains:

```json
apps/auth-service/src/domains/
├── account-management/     # User account lifecycle management
├── authentication/        # Core authentication flows
├── health/               # Service health monitoring
├── mfa/                  # Multi-factor authentication
├── password-management/  # Password operations
├── security-settings/    # User security preferences
├── session-management/   # Session lifecycle
├── shared/              # Common utilities and interfaces
├── token-management/     # Token operations
└── webauthn/            # WebAuthn/FIDO2 authentication
```

## Domain Details

### 1. Account Management Domain

**Path:** `src/domains/account-management/`

**Responsibilities:**

- User registration and signup
- Email availability checking
- Account profile management
- Account status management (unlock, disable)
- User profile retrieval

**Key Components:**

- **Controller:** `AccountManagementController`
- **Service:** `AccountManagementService`
- **Endpoints:**
  - `POST /auth/signup` - User registration
  - `GET /auth/check-email` - Email availability check
  - `GET /auth/me` - Get current user profile
  - `POST /auth/unlock-account` - Account unlock

**DTOs:**

- `SignupRequestDto` / `SignupResponseDto`
- `CheckEmailDto` / `CheckEmailDataDto`
- `MeDataDto`
- `UnlockAccountDto` / `UnlockAccountDataDto`

**External Dependencies:**

- User Service (gRPC)
- Tenant Service (gRPC)
- Notification Service (gRPC)

### 2. Authentication Domain

**Path:** `src/domains/authentication/`

**Responsibilities:**

- User login and logout
- Email verification
- Password reset flows
- Token refresh
- Session management
- Resend verification emails

**Key Components:**

- **Controllers:**
  - `AuthenticationRestController` (REST API)
  - `AuthenticationGrpcController` (gRPC)
- **Service:** `AuthenticationService`

**Endpoints:**

- `POST /auth/login` - User authentication
- `POST /auth/logout` - User logout
- `GET /auth/verify-email` - Email verification
- `POST /auth/resend-verification` - Resend verification email
- `POST /auth/refresh-token` - Token refresh
- `POST /auth/forgot-password` - Password reset request
- `POST /auth/reset-password` - Password reset
- `POST /auth/change-password` - Password change
- `POST /auth/revoke-token` - Token revocation
- `DELETE /auth/revoke-all-tokens` - Revoke all user tokens

**DTOs:**

- `LoginDto` / `LoginDataDto`
- `VerifyEmailDto` / `VerifyEmailDataDto`
- `ResendVerificationDto` / `ResendVerificationDataDto`
- `RefreshTokenDto` / `RefreshTokenDataDto`
- `ForgotPasswordDto` / `ForgotPasswordDataDto`
- `ResetPasswordDto` / `ResetPasswordDataDto`
- `ChangePasswordDto` / `ChangePasswordDataDto`

**Features:**

- JWT token management
- Redis-based session storage
- Rate limiting for verification emails
- Security logging and monitoring

### 3. Health Domain

**Path:** `src/domains/health/`

**Responsibilities:**

- Service health monitoring
- Readiness and liveness probes
- Dependency health checks
- System status reporting

**Key Components:**

- **Controllers:**
  - `HealthController` (REST)
  - `HealthGrpcController` (gRPC)
- **Service:** `HealthService`

**Endpoints:**

- `GET /health` - Overall health status
- `GET /health/ready` - Readiness probe
- `GET /health/live` - Liveness probe

### 4. Multi-Factor Authentication (MFA) Domain

**Path:** `src/domains/mfa/`

**Responsibilities:**

- MFA setup and configuration
- TOTP (Time-based One-Time Password) management
- Backup codes generation
- MFA verification
- MFA status management

**Key Components:**

- **Controller:** `MfaController`
- **Service:** `MfaService`

**Endpoints:**

- `POST /mfa/setup` - Initialize MFA setup
- `POST /mfa/verify-setup` - Complete MFA setup
- `POST /mfa/challenge` - Request MFA challenge
- `POST /mfa/verify-challenge` - Verify MFA challenge
- `GET /mfa/status` - Get MFA status
- `POST /mfa/disable` - Disable MFA
- `POST /mfa/regenerate-backup-codes` - Generate new backup codes

**DTOs:**

- `SetupMfaDto` / `SetupMfaDataDto`
- `VerifyMfaSetupDto` / `VerifyMfaSetupDataDto`
- `MfaChallengeDto` / `MfaChallengeDataDto`
- `VerifyMfaChallengeDto` / `VerifyMfaChallengeDataDto`

### 5. WebAuthn Domain

**Path:** `src/domains/webauthn/`

**Responsibilities:**

- FIDO2/WebAuthn authentication
- Passkey registration and management
- Biometric authentication
- Hardware security key support

**Key Components:**

- **Controller:** `WebAuthnController`
- **Service:** `WebAuthnService`

**Endpoints:**

- `POST /webauthn/register/start` - Start passkey registration
- `POST /webauthn/register/complete` - Complete passkey registration
- `POST /webauthn/authenticate/start` - Start WebAuthn authentication
- `POST /webauthn/authenticate/complete` - Complete WebAuthn authentication
- `GET /webauthn/passkeys` - List user passkeys
- `PUT /webauthn/passkeys/:id/rename` - Rename passkey
- `DELETE /webauthn/passkeys/:id` - Remove passkey

**DTOs:**

- `StartWebAuthnRegistrationDto` / `StartWebAuthnRegistrationDataDto`
- `CompleteWebAuthnRegistrationDto` / `CompleteWebAuthnRegistrationDataDto`
- `StartWebAuthnAuthenticationDto` / `StartWebAuthnAuthenticationDataDto`
- `CompleteWebAuthnAuthenticationDto` / `CompleteWebAuthnAuthenticationDataDto`

### 6. Password Management Domain

**Path:** `src/domains/password-management/`

**Status:** Placeholder domain (empty directories)

**Intended Responsibilities:**

- Password policy enforcement
- Password strength validation
- Password history management
- Password expiration handling

### 7. Security Settings Domain

**Path:** `src/domains/security-settings/`

**Status:** Placeholder domain (empty directories)

**Intended Responsibilities:**

- User security preferences
- Login notification settings
- Device management
- Security audit logs

### 8. Session Management Domain

**Path:** `src/domains/session-management/`

**Status:** Placeholder domain (empty directories)

**Intended Responsibilities:**

- Active session tracking
- Session termination
- Concurrent session limits
- Session analytics

### 9. Token Management Domain

**Path:** `src/domains/token-management/`

**Status:** Placeholder domain (empty directories)

**Intended Responsibilities:**

- Token lifecycle management
- Token blacklisting
- Token analytics
- API key management

### 10. Shared Domain

**Path:** `src/domains/shared/`

**Responsibilities:**

- Common constants and enums
- Shared interfaces
- Utility functions
- Cross-domain types

**Components:**

- `constants/enums.ts` - Shared enumerations
- `interfaces/` - Common interfaces
- `utils/` - Utility functions

## Domain Integration

### Module Dependencies

- **Authentication** depends on **Account Management**
- All domains can use **Shared** utilities
- **Health** domain is independent
- **MFA** and **WebAuthn** are independent authentication methods

### External Service Dependencies

- **User Service** (gRPC) - User data management
- **Tenant Service** (gRPC) - Multi-tenancy support
- **Notification Service** (gRPC) - Email notifications
- **Redis** - Session and token storage
- **PostgreSQL** - Persistent data storage

## Security Features

### Rate Limiting

- Resend verification emails: 3 requests per hour per email
- Login attempts: Configurable rate limiting
- Password reset: Rate limited per email

### Token Security

- JWT tokens with configurable expiration
- Refresh token rotation
- Token blacklisting support
- Secure token storage in Redis

### Data Protection

- Password hashing with bcrypt
- Sensitive data exclusion from API responses
- Secure session management
- CSRF protection

## API Standards

### Response Format

All endpoints follow the `StandardApiResponse` format:

```typescript
{
  success: boolean;
  status_code: number;
  message: string;
  data: T;
  meta: {
    timestamp: string;
    request_id: string;
  }
}
```

### Field Naming

- API requests/responses use `snake_case`
- Internal DTOs use `camelCase`
- Automatic case transformation between layers

### Error Handling

- Consistent error response format
- Security-conscious error messages
- Comprehensive logging
- Graceful degradation

## Future Enhancements

### Planned Domains

1. **Password Management** - Advanced password policies
2. **Security Settings** - User security preferences
3. **Session Management** - Advanced session control
4. **Token Management** - Comprehensive token lifecycle

### Scalability Considerations

- Microservice architecture ready
- Horizontal scaling support
- Caching strategies
- Database optimization

## Development Guidelines

### Adding New Domains

1. Create domain directory structure
2. Implement module, controller, service
3. Define DTOs and interfaces
4. Add to main app module
5. Write comprehensive tests
6. Update documentation

### Domain Boundaries

- Each domain should be self-contained
- Minimize cross-domain dependencies
- Use shared domain for common utilities
- Follow single responsibility principle

This documentation provides a comprehensive overview of the Auth Service domain architecture, enabling developers to understand the system structure and contribute effectively to the codebase.
