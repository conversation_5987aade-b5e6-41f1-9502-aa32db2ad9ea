/* eslint-disable @nx/enforce-module-boundaries */
import { CustomerStatus, CustomerType, KycStatus, PrismaClient, RiskLevel, AccountType, AccountStatus, AccountAccessLevel } from '../../../node_modules/.prisma/customer-client/index.js';
import { createId } from '@paralleldrive/cuid2';

// CUID generation functions for seeding with proper prefixes
function generateTenantId(): string {
  return `ten_${createId()}`.substring(0, 35);
}

function generateCustomerId(): string {
  return `cus_${createId()}`.substring(0, 35);
}

function generateAccountId(): string {
  return `acc_${createId()}`.substring(0, 35);
}

const prisma = new PrismaClient();

async function createSampleCustomers() {
  console.log('👥 Creating sample customers for Ronna Bank...');

  // Use a CUID for Ronna Bank tenant (matching Qeep platform conventions)
  const ronnaTenantId = generateTenantId();

  try {
    // Create individual customer - <PERSON><PERSON><PERSON>
    const kwameId = generateCustomerId();
    await prisma.customer.upsert({
      where: { id: kwameId },
      update: {},
      create: {
        id: kwameId,
        tenantId: ronnaTenantId,
        customerType: CustomerType.INDIVIDUAL,
        status: CustomerStatus.ACTIVE,
        riskLevel: RiskLevel.LOW,
        profileData: {
          personal_info: {
            first_name: 'Kwame',
            last_name: 'Nkrumah',
            date_of_birth: '1985-06-15',
            nationality: 'GH',
            occupation: 'Software Engineer',
          },
          contact_info: {
            email: '<EMAIL>',
            phone: '+233-24-123-4567',
            address: {
              street: 'East Legon, Boundary Road',
              city: 'Accra',
              region: 'Greater Accra',
              postal_code: 'GA-123-4567',
              country: 'GH',
            },
          },
          identification: {
            ghana_card: 'GHA-123456789-0',
            voters_id: 'VID-987654321',
          },
        },
        kycStatus: KycStatus.VERIFIED,
        kycData: {
          verification_date: '2024-01-15T10:00:00Z',
          verification_method: 'DOCUMENT_UPLOAD',
          documents_verified: ['DRIVERS_LICENSE', 'UTILITY_BILL'],
          verification_status: 'PASSED',
          risk_factors: [],
        },
        riskAssessment: {
          overall_risk_score: 25,
          risk_factors: {
            geographic_risk: 10,
            occupation_risk: 5,
            transaction_risk: 10,
          },
          assessment_date: '2024-01-15T10:00:00Z',
          next_review_date: '2025-01-15T10:00:00Z',
        },
      },
    });

    console.log('✅ Created individual customer: Kwame Nkrumah');

    // Create business customer - Ashanti Gold Mining Company
    const ashantiId = generateCustomerId();
    await prisma.customer.upsert({
      where: { id: ashantiId },
      update: {},
      create: {
        id: ashantiId,
        tenantId: ronnaTenantId,
        customerType: CustomerType.BUSINESS,
        status: CustomerStatus.ACTIVE,
        riskLevel: RiskLevel.MEDIUM,
        profileData: {
          business_info: {
            legal_name: 'Ashanti Gold Mining Company Limited',
            dba_name: 'Ashanti Gold',
            business_type: 'Limited Company',
            industry: 'MINING',
            incorporation_date: '2020-03-01',
            tin: 'TIN-C0123456789',
          },
          contact_info: {
            email: '<EMAIL>',
            phone: '+233-32-202-6789',
            address: {
              street: 'Obuasi Mining Complex',
              city: 'Obuasi',
              region: 'Ashanti',
              postal_code: 'AK-456-7890',
              country: 'GH',
            },
          },
          beneficial_owners: [
            {
              name: 'Akosua Frimpong',
              ownership_percentage: 60,
              title: 'CEO',
            },
            {
              name: 'Kofi Asante',
              ownership_percentage: 40,
              title: 'COO',
            },
          ],
        },
        kycStatus: KycStatus.VERIFIED,
        kycData: {
          verification_date: '2024-01-20T14:00:00Z',
          verification_method: 'ENHANCED_DUE_DILIGENCE',
          documents_verified: ['ARTICLES_OF_INCORPORATION', 'BENEFICIAL_OWNERSHIP'],
          verification_status: 'PASSED',
          risk_factors: ['HIGH_TRANSACTION_VOLUME'],
        },
        riskAssessment: {
          overall_risk_score: 55,
          risk_factors: {
            geographic_risk: 15,
            industry_risk: 20,
            transaction_risk: 20,
          },
          assessment_date: '2024-01-20T14:00:00Z',
          next_review_date: '2024-07-20T14:00:00Z',
        },
      },
    });

    console.log('✅ Created business customer: Ashanti Gold Mining Company');

    // Create additional test customers
    const additionalCustomers = [
      {
        customerType: CustomerType.INDIVIDUAL,
        status: CustomerStatus.ACTIVE,
        riskLevel: RiskLevel.HIGH,
        profileData: {
          personal_info: {
            first_name: 'Ama',
            last_name: 'Serwaa',
            date_of_birth: '1978-03-22',
            nationality: 'GH',
            occupation: 'Import/Export Business Owner',
          },
          contact_info: {
            email: '<EMAIL>',
            phone: '+233-20-789-0123',
            address: {
              street: 'Tema Industrial Area',
              city: 'Tema',
              region: 'Greater Accra',
              postal_code: 'GA-789-0123',
              country: 'GH',
            },
          },
        },
        kycStatus: KycStatus.VERIFIED,
        riskAssessment: {
          overall_risk_score: 75,
          risk_factors: {
            geographic_risk: 30,
            occupation_risk: 25,
            transaction_risk: 20,
          },
        },
      },
      {
        customerType: CustomerType.INDIVIDUAL,
        status: CustomerStatus.PENDING_VERIFICATION,
        riskLevel: RiskLevel.MEDIUM,
        profileData: {
          personal_info: {
            first_name: 'Kofi',
            last_name: 'Mensah',
            date_of_birth: '1990-11-08',
            nationality: 'GH',
            occupation: 'Financial Analyst',
          },
          contact_info: {
            email: '<EMAIL>',
            phone: '+233-26-321-0987',
            address: {
              street: 'Ridge Area, Liberation Road',
              city: 'Accra',
              region: 'Greater Accra',
              postal_code: 'GA-321-0987',
              country: 'GH',
            },
          },
        },
        kycStatus: KycStatus.IN_PROGRESS,
        riskAssessment: {
          overall_risk_score: 45,
          risk_factors: {
            geographic_risk: 15,
            occupation_risk: 15,
            transaction_risk: 15,
          },
        },
      },
    ];

    for (const customerData of additionalCustomers) {
      const customer = await prisma.customer.create({
        data: {
          id: generateCustomerId(),
          tenantId: ronnaTenantId,
          ...customerData,
        },
      });

      const profileData = customer.profileData as any;
      console.log(`✅ Created additional customer: ${profileData.personal_info?.first_name} ${profileData.personal_info?.last_name}`);
    }

    console.log(`✅ Created ${additionalCustomers.length + 2} sample customers for testing`);
  } catch (error) {
    console.error('❌ Error creating sample customers:', error.message);
    throw error;
  }
}

async function createSampleAccounts() {
  console.log('💳 Creating sample accounts for seed customers...');

  // Get all customers to create accounts for (tenant ID will be from existing customers)
  const customers = await prisma.customer.findMany({});

  try {

    for (const customer of customers) {
      const profileData = customer.profileData as { personal_info?: { first_name?: string; last_name?: string }; business_info?: { legal_name?: string } };
      const customerName =
        customer.customerType === CustomerType.INDIVIDUAL
          ? `${profileData.personal_info?.first_name} ${profileData.personal_info?.last_name}`
          : profileData.business_info?.legal_name || 'Business Customer';

      console.log(`💳 Creating accounts for: ${customerName}`);

      // Create accounts based on customer type and risk level
      if (customer.customerType === CustomerType.INDIVIDUAL) {
        await createIndividualAccounts(customer, customer.tenantId);
      } else {
        await createBusinessAccounts(customer, customer.tenantId);
      }
    }

    console.log('✅ All sample accounts created successfully!');
  } catch (error) {
    console.error('❌ Error creating sample accounts:', error.message);
    throw error;
  }
}

async function createIndividualAccounts(customer: { id: string; customerType: CustomerType; status: CustomerStatus; riskLevel: RiskLevel; profileData: any }, tenantId: string) {
  const profileData = customer.profileData as { personal_info?: { first_name?: string; last_name?: string } };
  const customerName = `${profileData.personal_info?.first_name} ${profileData.personal_info?.last_name}`;

  // Primary checking account for all individuals
  const checkingAccount = await prisma.account.create({
    data: {
      id: generateAccountId(),
      tenantId,
      customerId: customer.id,
      accountNumber: `CHK-${Math.random().toString().slice(2, 12)}`,
      accountType: AccountType.CHECKING,
      accountStatus: customer.status === CustomerStatus.ACTIVE ? AccountStatus.ACTIVE : AccountStatus.PENDING,
      currency: 'GHS',
      accessLevel: customer.riskLevel === RiskLevel.HIGH ? AccountAccessLevel.BLOCKED : AccountAccessLevel.FULL,
      balance: customer.riskLevel === RiskLevel.LOW ? 15000.0 : customer.riskLevel === RiskLevel.MEDIUM ? 8500.0 : 2500.0,
      availableBalance: customer.riskLevel === RiskLevel.LOW ? 14500.0 : customer.riskLevel === RiskLevel.MEDIUM ? 8000.0 : 2000.0,
      overdraftLimit: customer.riskLevel === RiskLevel.LOW ? 5000.0 : customer.riskLevel === RiskLevel.MEDIUM ? 2000.0 : 0.0,
      interestRate: 0.02,
      minimumBalance: 100.0,
      accountName: `${customerName} - Primary Checking`,
      description: 'Primary checking account for daily transactions',
      metadata: {
        account_purpose: 'PRIMARY_CHECKING',
        created_via: 'SEED_DATA',
        risk_level: customer.riskLevel,
      },
      isActive: customer.status === CustomerStatus.ACTIVE,
    },
  });

  console.log(`  ✅ Created checking account: ${checkingAccount.accountNumber}`);

  // Savings account for low and medium risk customers
  if (customer.riskLevel !== RiskLevel.HIGH) {
    const savingsAccount = await prisma.account.create({
      data: {
        id: generateAccountId(),
        tenantId,
        customerId: customer.id,
        accountNumber: `SAV-${Math.random().toString().slice(2, 12)}`,
        accountType: AccountType.SAVINGS,
        accountStatus: customer.status === CustomerStatus.ACTIVE ? AccountStatus.ACTIVE : AccountStatus.PENDING,
        currency: 'GHS',
        accessLevel: AccountAccessLevel.FULL,
        balance: customer.riskLevel === RiskLevel.LOW ? 25000.0 : 12000.0,
        availableBalance: customer.riskLevel === RiskLevel.LOW ? 25000.0 : 12000.0,
        overdraftLimit: 0.0,
        interestRate: 0.045,
        minimumBalance: 500.0,
        accountName: `${customerName} - Savings`,
        description: 'Savings account for long-term deposits',
        metadata: {
          account_purpose: 'SAVINGS',
          created_via: 'SEED_DATA',
          interest_compound_frequency: 'MONTHLY',
        },
        isActive: customer.status === CustomerStatus.ACTIVE,
      },
    });

    console.log(`  ✅ Created savings account: ${savingsAccount.accountNumber}`);
  }

  // Investment account for low risk customers only
  if (customer.riskLevel === RiskLevel.LOW) {
    const investmentAccount = await prisma.account.create({
      data: {
        id: generateAccountId(),
        tenantId,
        customerId: customer.id,
        accountNumber: `INV-${Math.random().toString().slice(2, 12)}`,
        accountType: AccountType.INVESTMENT,
        accountStatus: AccountStatus.ACTIVE,
        currency: 'GHS',
        accessLevel: AccountAccessLevel.FULL,
        balance: 50000.0,
        availableBalance: 45000.0,
        overdraftLimit: 0.0,
        interestRate: 0.08,
        minimumBalance: 5000.0,
        accountName: `${customerName} - Investment Portfolio`,
        description: 'Investment account for portfolio management',
        metadata: {
          account_purpose: 'INVESTMENT',
          created_via: 'SEED_DATA',
          investment_type: 'DIVERSIFIED_PORTFOLIO',
        },
        isActive: true,
      },
    });

    console.log(`  ✅ Created investment account: ${investmentAccount.accountNumber}`);
  }
}

async function createBusinessAccounts(customer: { id: string; customerType: CustomerType; status: CustomerStatus; riskLevel: RiskLevel; profileData: any }, tenantId: string) {
  const profileData = customer.profileData as { business_info?: { legal_name?: string; business_type?: string; industry?: string } };
  const businessName = profileData.business_info?.legal_name || 'Business Customer';

  // Primary business checking account
  const businessChecking = await prisma.account.create({
    data: {
      id: generateAccountId(),
      tenantId,
      customerId: customer.id,
      accountNumber: `BIZ-${Math.random().toString().slice(2, 12)}`,
      accountType: AccountType.BUSINESS,
      accountStatus: AccountStatus.ACTIVE,
      currency: 'GHS',
      accessLevel: AccountAccessLevel.FULL,
      balance: 150000.0,
      availableBalance: 140000.0,
      overdraftLimit: 25000.0,
      interestRate: 0.015,
      minimumBalance: 5000.0,
      accountName: `${businessName} - Operating Account`,
      description: 'Primary business operating account',
      metadata: {
        account_purpose: 'BUSINESS_OPERATIONS',
        created_via: 'SEED_DATA',
        business_type: profileData.business_info?.business_type,
        industry: profileData.business_info?.industry,
      },
      isActive: true,
    },
  });

  console.log(`  ✅ Created business account: ${businessChecking.accountNumber}`);

  // Business savings account
  const businessSavings = await prisma.account.create({
    data: {
      id: generateAccountId(),
      tenantId,
      customerId: customer.id,
      accountNumber: `BSV-${Math.random().toString().slice(2, 12)}`,
      accountType: AccountType.SAVINGS,
      accountStatus: AccountStatus.ACTIVE,
      currency: 'GHS',
      accessLevel: AccountAccessLevel.FULL,
      balance: 75000.0,
      availableBalance: 75000.0,
      overdraftLimit: 0.0,
      interestRate: 0.035,
      minimumBalance: 10000.0,
      accountName: `${businessName} - Reserve Fund`,
      description: 'Business reserve and emergency fund',
      metadata: {
        account_purpose: 'BUSINESS_RESERVES',
        created_via: 'SEED_DATA',
        reserve_type: 'EMERGENCY_FUND',
      },
      isActive: true,
    },
  });

  console.log(`  ✅ Created business savings: ${businessSavings.accountNumber}`);

  // Credit line for established businesses
  if (customer.riskLevel !== RiskLevel.HIGH) {
    const creditAccount = await prisma.account.create({
      data: {
        id: generateAccountId(),
        tenantId,
        customerId: customer.id,
        accountNumber: `CRD-${Math.random().toString().slice(2, 12)}`,
        accountType: AccountType.CREDIT,
        accountStatus: AccountStatus.ACTIVE,
        currency: 'GHS',
        accessLevel: AccountAccessLevel.FULL,
        balance: -5000.0, // Negative balance indicates credit usage
        availableBalance: 45000.0, // Available credit
        overdraftLimit: 50000.0, // Credit limit
        interestRate: 0.12,
        minimumBalance: -50000.0, // Maximum credit limit
        accountName: `${businessName} - Business Credit Line`,
        description: 'Business line of credit for working capital',
        metadata: {
          account_purpose: 'BUSINESS_CREDIT',
          created_via: 'SEED_DATA',
          credit_limit: 50000.0,
          credit_type: 'REVOLVING_CREDIT',
        },
        isActive: true,
      },
    });

    console.log(`  ✅ Created credit line: ${creditAccount.accountNumber}`);
  }
}

async function main() {
  console.log('🌱 Seeding Customer Service database...');

  // Create sample customers for Ronna Bank
  await createSampleCustomers();

  // Create sample accounts for the customers
  await createSampleAccounts();

  console.log('📝 Customer service database seeded successfully!');
  console.log('');
  console.log('👥 Customer Service Database Summary:');
  console.log('  - ✅ Sample customers created for Ronna Bank');
  console.log('  - ✅ Individual customers: Kwame Nkrumah, Ama Serwaa, Kofi Mensah');
  console.log('  - ✅ Business customers: Ashanti Gold Mining Company');
  console.log('  - ✅ Various risk levels: LOW, MEDIUM, HIGH');
  console.log('  - ✅ Different KYC statuses: VERIFIED, IN_PROGRESS');
  console.log('  - ✅ Sample accounts created for all customers');
  console.log('  - ✅ Ready for customer data ingestion testing');
  console.log('');
  console.log('🔍 Customer Risk Profiles:');
  console.log('  - Kwame Nkrumah: Low risk individual (score: 25)');
  console.log('  - Ashanti Gold Mining Company: Medium risk business (score: 55)');
  console.log('  - Ama Serwaa: High risk individual (score: 75)');
  console.log('  - Kofi Mensah: Medium risk individual (score: 45, pending verification)');
  console.log('');
  console.log('💳 Account Types Created:');
  console.log('  - Individual customers: Checking, Savings (low/medium risk), Investment (low risk only)');
  console.log('  - Business customers: Business Operating, Business Savings, Credit Line (medium risk)');
  console.log('  - High-risk customers: Limited account access and blocked access levels');
  console.log('');
  console.log('💡 Use these customers and accounts to test transaction workflows and account management features');
}

main()
  .catch((e) => {
    console.error('❌ Customer Service database seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
