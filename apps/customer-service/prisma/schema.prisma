// =============================================================================
// Customer Service Database Schema
// =============================================================================
// This schema defines the customer data model for the Qeep AML platform
// including customer profiles, KYC data, risk assessments, and relationships
// =============================================================================

generator client {
  provider = "prisma-client-js"
  output   = "../../../node_modules/.prisma/customer-client"
}

datasource db {
  provider = "postgresql"
  url      = env("CUSTOMER_DATABASE_URL")
}

// =============================================================================
// CUSTOMER CORE MODELS
// =============================================================================

model Customer {
  id           String      @id
  tenantId     String      @map("tenant_id")
  customerType CustomerType @map("customer_type")
  status       CustomerStatus
  riskLevel    RiskLevel   @map("risk_level")
  
  // Core profile data (encrypted JSON)
  profileData  Json        @map("profile_data")
  
  // KYC information
  kycStatus    KycStatus   @map("kyc_status")
  kycData      Json?       @map("kyc_data")
  
  // Risk assessment
  riskAssessment Json?     @map("risk_assessment")
  
  // Timestamps
  createdAt    DateTime    @default(now()) @map("created_at")
  updatedAt    DateTime    @updatedAt @map("updated_at")
  
  // Relationships
  documents    CustomerDocument[]
  riskScores   CustomerRiskScore[]
  relationships CustomerRelationship[] @relation("PrimaryCustomer")
  relatedTo    CustomerRelationship[] @relation("RelatedCustomer")
  alerts       CustomerAlert[]
  notes        CustomerNote[]
  
  // Account relationship
  accounts     Account[]
  
  @@map("customers")
  @@index([tenantId])
  @@index([tenantId, status])
  @@index([tenantId, riskLevel])
  @@index([tenantId, customerType])
  @@index([createdAt])
}

model Account {
  id               String             @id
  tenantId         String             @map("tenant_id")
  customerId       String             @map("customer_id")
  
  // Account identifiers
  accountNumber     String        @unique @map("account_number")
  accountType       AccountType   @map("account_type")
  accountStatus     AccountStatus @map("account_status")
  
  // Account configuration
  currency          String        // ISO currency code
  accessLevel       AccountAccessLevel @map("access_level")
  
  // Financial data
  balance           Decimal       @default(0) @db.Decimal(19, 4)
  availableBalance  Decimal       @default(0) @map("available_balance") @db.Decimal(19, 4)
  overdraftLimit    Decimal       @default(0) @map("overdraft_limit") @db.Decimal(19, 4)
  interestRate      Decimal       @default(0) @map("interest_rate") @db.Decimal(5, 4)
  minimumBalance    Decimal       @default(0) @map("minimum_balance") @db.Decimal(19, 4)
  
  // Account details
  accountName       String        @map("account_name")
  description       String?
  metadata          Json          @default("{}")
  
  // Status and activity
  isActive          Boolean       @default(true) @map("is_active")
  lastTransactionAt DateTime?     @map("last_transaction_at")
  
  // Timestamps
  createdAt         DateTime      @default(now()) @map("created_at")
  updatedAt         DateTime      @updatedAt @map("updated_at")
  
  // Relationships
  customer          Customer      @relation(fields: [customerId], references: [id], onDelete: Cascade)
  
  @@map("accounts")
  @@index([tenantId])
  @@index([customerId])
  @@index([tenantId, customerId])
  @@index([tenantId, accountType])
  @@index([tenantId, accountStatus])
  @@index([accountNumber])
  @@index([createdAt])
}

model CustomerDocument {
  id           String      @id
  customerId   String      @map("customer_id")
  tenantId     String      @map("tenant_id")
  
  documentType DocumentType @map("document_type")
  fileName     String      @map("file_name")
  fileSize     Int         @map("file_size")
  mimeType     String      @map("mime_type")
  
  // Document metadata
  metadata     Json?
  
  // Storage information
  storageKey   String      @map("storage_key")
  encryptionKey String?    @map("encryption_key")
  
  // Verification status
  verificationStatus VerificationStatus @map("verification_status")
  verifiedAt   DateTime?   @map("verified_at")
  verifiedBy   String?     @map("verified_by") @db.Uuid
  
  // Timestamps
  uploadedAt   DateTime    @default(now()) @map("uploaded_at")
  updatedAt    DateTime    @updatedAt @map("updated_at")
  
  // Relationships
  customer     Customer    @relation(fields: [customerId], references: [id], onDelete: Cascade)
  
  @@map("customer_documents")
  @@index([customerId])
  @@index([tenantId])
  @@index([documentType])
  @@index([verificationStatus])
}

model CustomerRiskScore {
  id           String      @id
  customerId   String      @map("customer_id")
  tenantId     String      @map("tenant_id")
  
  // Risk scoring
  overallScore Int         @map("overall_score")
  riskLevel    RiskLevel   @map("risk_level")
  
  // Risk factors breakdown
  riskFactors  Json        @map("risk_factors")
  
  // Assessment metadata
  assessmentDate DateTime  @map("assessment_date")
  assessmentMethod String  @map("assessment_method")
  assessedBy   String?     @map("assessed_by")
  
  // Review information
  nextReviewDate DateTime? @map("next_review_date")
  reviewReason String?     @map("review_reason")
  
  // Timestamps
  createdAt    DateTime    @default(now()) @map("created_at")
  updatedAt    DateTime    @updatedAt @map("updated_at")
  
  // Relationships
  customer     Customer    @relation(fields: [customerId], references: [id], onDelete: Cascade)
  
  @@map("customer_risk_scores")
  @@index([customerId])
  @@index([tenantId])
  @@index([riskLevel])
  @@index([assessmentDate])
  @@index([nextReviewDate])
}

model CustomerRelationship {
  id                  String      @id
  primaryCustomerId   String      @map("primary_customer_id")
  relatedCustomerId   String      @map("related_customer_id")
  tenantId            String      @map("tenant_id")
  
  relationshipType RelationshipType @map("relationship_type")
  relationshipData Json?       @map("relationship_data")
  
  // Status and validity
  status           RelationshipStatus
  validFrom        DateTime    @map("valid_from")
  validTo          DateTime?   @map("valid_to")
  
  // Timestamps
  createdAt        DateTime    @default(now()) @map("created_at")
  updatedAt        DateTime    @updatedAt @map("updated_at")
  
  // Relationships
  primaryCustomer  Customer    @relation("PrimaryCustomer", fields: [primaryCustomerId], references: [id], onDelete: Cascade)
  relatedCustomer  Customer    @relation("RelatedCustomer", fields: [relatedCustomerId], references: [id], onDelete: Cascade)
  
  @@map("customer_relationships")
  @@index([tenantId])
  @@index([primaryCustomerId])
  @@index([relatedCustomerId])
  @@index([relationshipType])
  @@index([status])
}

model CustomerAlert {
  id           String      @id
  customerId   String      @map("customer_id")
  tenantId     String      @map("tenant_id")
  
  alertType    AlertType   @map("alert_type")
  severity     AlertSeverity
  title        String
  description  String
  
  // Alert data
  alertData    Json?       @map("alert_data")
  
  // Status and resolution
  status       AlertStatus
  resolvedAt   DateTime?   @map("resolved_at")
  resolvedBy   String?     @map("resolved_by") @db.Uuid
  resolution   String?
  
  // Timestamps
  createdAt    DateTime    @default(now()) @map("created_at")
  updatedAt    DateTime    @updatedAt @map("updated_at")
  
  // Relationships
  customer     Customer    @relation(fields: [customerId], references: [id], onDelete: Cascade)
  
  @@map("customer_alerts")
  @@index([customerId])
  @@index([tenantId])
  @@index([alertType])
  @@index([severity])
  @@index([status])
  @@index([createdAt])
}

model CustomerNote {
  id           String      @id
  customerId   String      @map("customer_id")
  tenantId     String      @map("tenant_id")
  
  noteType     NoteType    @map("note_type")
  title        String
  content      String
  
  // Note metadata
  isConfidential Boolean   @default(false) @map("is_confidential")
  tags         String[]
  
  // Author information
  authorId     String      @map("author_id")
  authorName   String      @map("author_name")
  
  // Timestamps
  createdAt    DateTime    @default(now()) @map("created_at")
  updatedAt    DateTime    @updatedAt @map("updated_at")
  
  // Relationships
  customer     Customer    @relation(fields: [customerId], references: [id], onDelete: Cascade)
  
  @@map("customer_notes")
  @@index([customerId])
  @@index([tenantId])
  @@index([noteType])
  @@index([authorId])
  @@index([createdAt])
}

// =============================================================================
// ENUMS
// =============================================================================

enum CustomerType {
  INDIVIDUAL
  BUSINESS
  TRUST
  GOVERNMENT
  NON_PROFIT
  
  @@map("customer_type")
}

enum CustomerStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  CLOSED
  PENDING_VERIFICATION
  
  @@map("customer_status")
}

enum RiskLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
  
  @@map("risk_level")
}

enum KycStatus {
  PENDING
  IN_PROGRESS
  VERIFIED
  REJECTED
  EXPIRED
  REQUIRES_UPDATE
  
  @@map("kyc_status")
}

enum DocumentType {
  DRIVERS_LICENSE
  PASSPORT
  NATIONAL_ID
  UTILITY_BILL
  BANK_STATEMENT
  ARTICLES_OF_INCORPORATION
  BENEFICIAL_OWNERSHIP
  TAX_DOCUMENT
  OTHER
  
  @@map("document_type")
}

enum VerificationStatus {
  PENDING
  VERIFIED
  REJECTED
  EXPIRED
  
  @@map("verification_status")
}

enum RelationshipType {
  BENEFICIAL_OWNER
  AUTHORIZED_SIGNER
  POWER_OF_ATTORNEY
  SPOUSE
  BUSINESS_PARTNER
  SUBSIDIARY
  PARENT_COMPANY
  AFFILIATE
  OTHER
  
  @@map("relationship_type")
}

enum RelationshipStatus {
  ACTIVE
  INACTIVE
  TERMINATED
  
  @@map("relationship_status")
}

enum AlertType {
  KYC_EXPIRATION
  RISK_SCORE_CHANGE
  DOCUMENT_REQUIRED
  COMPLIANCE_REVIEW
  SANCTIONS_MATCH
  PEP_MATCH
  ADVERSE_MEDIA
  OTHER
  
  @@map("alert_type")
}

enum AlertSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
  
  @@map("alert_severity")
}

enum AlertStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  DISMISSED
  
  @@map("alert_status")
}

enum AccountType {
  CHECKING
  SAVINGS
  BUSINESS
  INVESTMENT
  CREDIT
  
  @@map("account_type")
}

enum AccountStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  CLOSED
  PENDING
  
  @@map("account_status")
}

enum AccountAccessLevel {
  FULL
  READ_only
  BLOCKED
  
  @@map("account_access_level")
}

enum NoteType {
  GENERAL
  KYC_NOTE
  RISK_ASSESSMENT
  INVESTIGATION
  COMPLIANCE
  CUSTOMER_SERVICE
  
  @@map("note_type")
}
