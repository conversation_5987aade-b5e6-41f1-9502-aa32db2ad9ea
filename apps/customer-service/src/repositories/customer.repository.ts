import { Injectable } from '@nestjs/common';
import {
  Customer,
  Prisma
} from '@prisma/customer-client';
import { CustomerPrismaService } from '../database/prisma.service';
import { BaseRepository, IBaseRepository } from './base.repository';

// Custom types for complex queries
export type CustomerWithIncludes = Prisma.CustomerGetPayload<{
  include: {
    documents: true;
    riskScores: true;
    alerts: true;
  };
}>;

export type CustomerWithDocuments = Prisma.CustomerGetPayload<{
  include: { documents: true };
}>;

export type CustomerSummary = Pick<Customer, 'id' | 'tenantId' | 'customerType' | 'status' | 'createdAt'>;

/**
 * Customer Repository Interface
 * Defines all customer-specific data access operations
 */
export interface ICustomerRepository extends IBaseRepository<Customer, Prisma.CustomerCreateInput, Prisma.CustomerUpdateInput> {
  // Tenant-based operations
  findByTenant(tenantId: string, options?: { limit?: number; offset?: number }): Promise<Customer[]>;
  countByTenant(tenantId: string): Promise<number>;
  
  // Search and filtering
  findByEmail(email: string, tenantId?: string): Promise<Customer | null>;
  findByCustomerType(customerType: string, tenantId?: string): Promise<Customer[]>;
  findByStatus(status: string, tenantId?: string): Promise<Customer[]>;
  searchCustomers(searchTerm: string, tenantId?: string): Promise<Customer[]>;
  
  // Complex queries with includes
  findByIdWithIncludes(id: string): Promise<CustomerWithIncludes | null>;
  findByIdWithDocuments(id: string): Promise<CustomerWithDocuments | null>;
  
  // Business logic queries
  findHighRiskCustomers(tenantId: string): Promise<Customer[]>;
  findCustomersRequiringKycUpdate(tenantId: string): Promise<Customer[]>;
  findRecentCustomers(tenantId: string, days: number): Promise<Customer[]>;
  
  // Bulk operations
  updateCustomerStatus(customerIds: string[], status: string): Promise<number>;
  getCustomerStatistics(tenantId: string): Promise<{
    total: number;
    byStatus: Record<string, number>;
    byType: Record<string, number>;
    byRiskLevel: Record<string, number>;
  }>;
}

/**
 * Customer Repository Implementation
 * Handles all customer entity data access operations
 */
@Injectable()
export class CustomerRepository
  extends BaseRepository<Customer, Prisma.CustomerCreateInput, Prisma.CustomerUpdateInput>
  implements ICustomerRepository {

  constructor(prisma: CustomerPrismaService) {
    super(prisma);
  }

  protected get model() {
    return this.prisma.customer;
  }

  /**
   * Find customers by tenant with pagination
   */
  async findByTenant(tenantId: string, options?: { limit?: number; offset?: number }): Promise<Customer[]> {
    try {
      return await this.model.findMany({
        where: { tenantId },
        take: options?.limit,
        skip: options?.offset,
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      throw new Error(`Failed to find customers by tenant ${tenantId}: ${error.message}`);
    }
  }

  /**
   * Count customers by tenant
   */
  async countByTenant(tenantId: string): Promise<number> {
    try {
      return await this.model.count({ where: { tenantId } });
    } catch (error) {
      throw new Error(`Failed to count customers by tenant ${tenantId}: ${error.message}`);
    }
  }

  /**
   * Find customer by email address
   */
  async findByEmail(email: string, tenantId?: string): Promise<Customer | null> {
    try {
      const where: any = {
        profileData: {
          path: ['email'],
          equals: email,
        },
      };
      
      if (tenantId) {
        where.tenantId = tenantId;
      }

      return await this.model.findFirst({ where });
    } catch (error) {
      throw new Error(`Failed to find customer by email ${email}: ${error.message}`);
    }
  }

  /**
   * Find customers by customer type
   */
  async findByCustomerType(customerType: string, tenantId?: string): Promise<Customer[]> {
    try {
      const where: any = { customerType };
      if (tenantId) where.tenantId = tenantId;

      return await this.model.findMany({ 
        where,
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      throw new Error(`Failed to find customers by type ${customerType}: ${error.message}`);
    }
  }

  /**
   * Find customers by status
   */
  async findByStatus(status: string, tenantId?: string): Promise<Customer[]> {
    try {
      const where: any = { status };
      if (tenantId) where.tenantId = tenantId;

      return await this.model.findMany({
        where,
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      throw new Error(`Failed to find customers by status ${status}: ${error.message}`);
    }
  }

  /**
   * Search customers by term (name, email, etc.)
   */
  async searchCustomers(searchTerm: string, tenantId?: string): Promise<Customer[]> {
    try {
      const where: any = {
        OR: [
          {
            profileData: {
              path: ['firstName'],
              string_contains: searchTerm,
            },
          },
          {
            profileData: {
              path: ['lastName'],
              string_contains: searchTerm,
            },
          },
          {
            profileData: {
              path: ['email'],
              string_contains: searchTerm,
            },
          },
          {
            profileData: {
              path: ['businessName'],
              string_contains: searchTerm,
            },
          },
        ],
      };

      if (tenantId) where.tenantId = tenantId;

      return await this.model.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: 50, // Limit search results
      });
    } catch (error) {
      throw new Error(`Failed to search customers with term ${searchTerm}: ${error.message}`);
    }
  }

  /**
   * Find customer by ID with all related data
   */
  async findByIdWithIncludes(id: string): Promise<CustomerWithIncludes | null> {
    try {
      return await this.model.findUnique({
        where: { id },
        include: {
          documents: true,
          riskScores: { orderBy: { createdAt: 'desc' } },
          alerts: true,
        },
      });
    } catch (error) {
      throw new Error(`Failed to find customer with includes ${id}: ${error.message}`);
    }
  }

  /**
   * Find customer by ID with documents only
   */
  async findByIdWithDocuments(id: string): Promise<CustomerWithDocuments | null> {
    try {
      return await this.model.findUnique({
        where: { id },
        include: { documents: true },
      });
    } catch (error) {
      throw new Error(`Failed to find customer with documents ${id}: ${error.message}`);
    }
  }

  /**
   * Find high-risk customers
   */
  async findHighRiskCustomers(tenantId: string): Promise<Customer[]> {
    try {
      return await this.model.findMany({
        where: {
          tenantId,
          riskLevel: { in: ['HIGH', 'CRITICAL'] },
        },
        orderBy: { updatedAt: 'desc' },
      });
    } catch (error) {
      throw new Error(`Failed to find high-risk customers for tenant ${tenantId}: ${error.message}`);
    }
  }

  /**
   * Find customers requiring KYC update
   */
  async findCustomersRequiringKycUpdate(tenantId: string): Promise<Customer[]> {
    try {
      return await this.model.findMany({
        where: {
          tenantId,
          kycStatus: { in: ['EXPIRED', 'REQUIRES_UPDATE'] },
        },
        orderBy: { updatedAt: 'asc' },
      });
    } catch (error) {
      throw new Error(`Failed to find customers requiring KYC update for tenant ${tenantId}: ${error.message}`);
    }
  }

  /**
   * Find recent customers within specified days
   */
  async findRecentCustomers(tenantId: string, days: number): Promise<Customer[]> {
    try {
      const since = new Date();
      since.setDate(since.getDate() - days);

      return await this.model.findMany({
        where: {
          tenantId,
          createdAt: { gte: since },
        },
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      throw new Error(`Failed to find recent customers for tenant ${tenantId}: ${error.message}`);
    }
  }

  /**
   * Bulk update customer status
   */
  async updateCustomerStatus(customerIds: string[], status: string): Promise<number> {
    try {
      const result = await this.model.updateMany({
        where: { id: { in: customerIds } },
        data: { status, updatedAt: new Date() },
      });
      return result.count;
    } catch (error) {
      throw new Error(`Failed to bulk update customer status: ${error.message}`);
    }
  }

  /**
   * Get customer statistics for a tenant
   */
  async getCustomerStatistics(tenantId: string): Promise<{
    total: number;
    byStatus: Record<string, number>;
    byType: Record<string, number>;
    byRiskLevel: Record<string, number>;
  }> {
    try {
      const [total, byStatus, byType, byRiskLevel] = await Promise.all([
        this.model.count({ where: { tenantId } }),
        this.model.groupBy({
          by: ['status'],
          where: { tenantId },
          _count: true,
        }),
        this.model.groupBy({
          by: ['customerType'],
          where: { tenantId },
          _count: true,
        }),
        this.model.groupBy({
          by: ['riskLevel'],
          where: { tenantId },
          _count: true,
        }),
      ]);

      return {
        total,
        byStatus: byStatus.reduce((acc, item) => ({ ...acc, [item.status]: item._count }), {}),
        byType: byType.reduce((acc, item) => ({ ...acc, [item.customerType]: item._count }), {}),
        byRiskLevel: byRiskLevel.reduce((acc, item) => ({ ...acc, [item.riskLevel]: item._count }), {}),
      };
    } catch (error) {
      throw new Error(`Failed to get customer statistics for tenant ${tenantId}: ${error.message}`);
    }
  }
}
}
