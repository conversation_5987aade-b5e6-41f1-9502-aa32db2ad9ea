import { Body, Controller, Delete, Get, Logger, Param, Patch, Post, Query, UseGuards, UseInterceptors } from '@nestjs/common';
import {
  CUID_PREFIXES,
  JwtAuthGuard,
  ParseCuidPipe,
  PlatformRole,
  RequireRoles,
  ResponseFormatInterceptor,
  ResponseUtil,
  SnakeToCamelPipe,
  StandardApiResponse,
  TenantRole,
  Traced,
  ZodValidationPipe,
} from '@qeep/common';
import {
  AccountResponseDto,
  CreateAccountRequestDto,
  CreateAccountResponseDto,
  FindAccountsRequestDto,
  FindAccountsResponseDto,
  UpdateAccountRequestDto,
  UpdateAccountResponseDto,
  ValidateAccountRequestDto,
  ValidateAccountResponseDto,
  createAccountSchema,
  findAccountsFiltersSchema,
  updateAccountSchema,
  validateAccountSchema,
} from '@qeep/contracts';
import { AccountsService } from '../services/accounts.service';

/**
 * Accounts Controller
 *
 * Handles HTTP endpoints for account management operations.
 * Provides account creation, updates, queries, and validation services.
 *
 * Key Responsibilities:
 * - Account creation and management for customers
 * - Account balance and status updates
 * - Account validation for transaction processing
 * - Account search and retrieval with filtering
 * - Integration with transaction service via gRPC
 */
@Controller('accounts')
@UseGuards(JwtAuthGuard)
@UseInterceptors(ResponseFormatInterceptor)
export class AccountsController {
  private readonly logger = new Logger(AccountsController.name);

  constructor(private readonly accountsService: AccountsService) {}

  /**
   * Create a new account
   * POST /api/v1/accounts
   *
   * Creates a new account for a customer with specified parameters.
   * Validates input data and ensures business rules compliance.
   *
   * @param createAccountDto Account creation data
   * @returns Created account data
   */
  @Post()
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR, PlatformRole.SUPER_ADMIN)
  @Traced('AccountsController.createAccount')
  async createAccount(
    @Body(new SnakeToCamelPipe(), new ZodValidationPipe(createAccountSchema)) createAccountDto: CreateAccountRequestDto,
  ): Promise<StandardApiResponse<CreateAccountResponseDto>> {
    this.logger.log(`Create account request for customer: ${createAccountDto.customerId}`);

    try {
      const account = await this.accountsService.createAccount(createAccountDto);

      this.logger.log(`Account created successfully with ID: ${account.id}`);

      return ResponseUtil.success(account, 'Account created successfully', 201);
    } catch (error) {
      this.logger.error(`Failed to create account: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all accounts with filtering and pagination
   * GET /api/v1/accounts
   *
   * Retrieves accounts with advanced filtering, searching, and pagination.
   * Supports filtering by customer, account type, status, currency, etc.
   *
   * @param query Query parameters for filtering and pagination
   * @returns Paginated list of accounts
   */
  @Get()
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR, PlatformRole.SUPER_ADMIN, TenantRole.TENANT_ADMIN)
  @Traced('AccountsController.findAccounts')
  async findAccounts(@Query(new ZodValidationPipe(findAccountsFiltersSchema)) query: FindAccountsRequestDto): Promise<StandardApiResponse<FindAccountsResponseDto>> {
    this.logger.log(`Find accounts request with query: ${JSON.stringify(query)}`);

    try {
      const result = await this.accountsService.findAccounts(query);

      return ResponseUtil.success(result, 'Accounts retrieved successfully', 200);
    } catch (error) {
      this.logger.error(`Failed to find accounts: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get account by ID
   * GET /api/v1/accounts/:id
   *
   * Retrieves a specific account by its unique identifier.
   * Includes full account details and metadata.
   *
   * @param params Account ID parameter
   * @returns Account data
   */
  @Get(':id')
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR, PlatformRole.SUPER_ADMIN, TenantRole.TENANT_ADMIN)
  @Traced('AccountsController.getAccountById')
  async getAccountById(@Param(new ParseCuidPipe(CUID_PREFIXES.ACCOUNT)) id: string): Promise<StandardApiResponse<AccountResponseDto>> {
    this.logger.log(`Get account by ID request: ${id}`);

    try {
      const account = await this.accountsService.getAccountById(id);

      return ResponseUtil.success(account, 'Account retrieved successfully', 200);
    } catch (error) {
      this.logger.error(`Failed to get account by ID: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update account
   * PATCH /api/v1/accounts/:id
   *
   * Updates an existing account with new data.
   * Supports partial updates for flexibility.
   *
   * @param params Account ID parameter
   * @param updateAccountDto Account update data
   * @returns Updated account data
   */
  @Patch(':id')
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR, PlatformRole.SUPER_ADMIN)
  @Traced('AccountsController.updateAccount')
  async updateAccount(
    @Param(new ParseCuidPipe(CUID_PREFIXES.ACCOUNT)) id: string,
    @Body(new SnakeToCamelPipe(), new ZodValidationPipe(updateAccountSchema)) updateAccountDto: UpdateAccountRequestDto,
  ): Promise<StandardApiResponse<UpdateAccountResponseDto>> {
    this.logger.log(`Update account request for ID: ${id}`);

    try {
      const account = await this.accountsService.updateAccount(id, updateAccountDto);

      this.logger.log(`Account updated successfully: ${id}`);

      return ResponseUtil.success(account, 'Account updated successfully', 200);
    } catch (error) {
      this.logger.error(`Failed to update account: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete account
   * DELETE /api/v1/accounts/:id
   *
   * Soft deletes an account by setting its status to CLOSED.
   * Maintains data integrity and audit trail.
   *
   * @param params Account ID parameter
   * @returns Success confirmation
   */
  @Delete(':id')
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR, PlatformRole.SUPER_ADMIN)
  @Traced('AccountsController.deleteAccount')
  async deleteAccount(@Param(new ParseCuidPipe(CUID_PREFIXES.ACCOUNT)) id: string): Promise<StandardApiResponse<{ message: string }>> {
    this.logger.log(`Delete account request for ID: ${id}`);

    try {
      await this.accountsService.deleteAccount(id);

      this.logger.log(`Account deleted successfully: ${id}`);

      return ResponseUtil.success({ message: 'Account deleted successfully' }, 'Account deleted successfully', 200);
    } catch (error) {
      this.logger.error(`Failed to delete account: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Validate account for transaction
   * POST /api/v1/accounts/validate
   *
   * Validates an account for transaction processing.
   * Used by transaction service to verify account status and capabilities.
   *
   * @param validateAccountDto Account validation data
   * @returns Validation result
   */
  @Post('validate')
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR, PlatformRole.SUPER_ADMIN)
  @Traced('AccountsController.validateAccount')
  async validateAccount(
    @Body(new SnakeToCamelPipe(), new ZodValidationPipe(validateAccountSchema)) validateAccountDto: ValidateAccountRequestDto,
  ): Promise<StandardApiResponse<ValidateAccountResponseDto>> {
    this.logger.log(`Validate account request for ID: ${validateAccountDto.accountId}`);

    try {
      const validation = await this.accountsService.validateAccount(validateAccountDto);

      return ResponseUtil.success(validation, 'Account validation completed', 200);
    } catch (error) {
      this.logger.error(`Failed to validate account: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get customer accounts
   * GET /api/v1/accounts/customer/:customerId
   *
   * Retrieves all accounts for a specific customer.
   * Includes pagination and filtering capabilities.
   *
   * @param customerId Customer ID parameter
   * @param query Query parameters for filtering and pagination
   * @returns Customer's accounts
   */
  @Get('customer/:customerId')
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR, PlatformRole.SUPER_ADMIN, TenantRole.TENANT_ADMIN)
  @Traced('AccountsController.getCustomerAccounts')
  async getCustomerAccounts(
    @Param('customerId') customerId: string,
    @Query(new SnakeToCamelPipe(), new ZodValidationPipe(findAccountsFiltersSchema)) query: FindAccountsRequestDto,
  ): Promise<StandardApiResponse<FindAccountsResponseDto>> {
    this.logger.log(`Get customer accounts request for customer: ${customerId}`);

    try {
      // Add customer ID to query filters
      const queryWithCustomer = { ...query, customerId };
      const result = await this.accountsService.findAccounts(queryWithCustomer);

      return ResponseUtil.success(result, 'Customer accounts retrieved successfully', 200);
    } catch (error) {
      this.logger.error(`Failed to get customer accounts: ${error.message}`, error.stack);
      throw error;
    }
  }
}
