import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigService, ProtoConfigService } from '@qeep/common';
import { CustomerPrismaService } from '../../database/prisma.service';
import { AccountRepository } from '../../repositories';
import { AccountsController } from './controllers/accounts.controller';
import { AccountsService } from './services/accounts.service';

/**
 * Accounts Module
 *
 * Handles account management operations including account creation, updates,
 * balance management, and transaction validation for the customer service.
 *
 * Key Responsibilities:
 * - Account creation and lifecycle management for customers
 * - Account balance tracking and updates from transaction processing
 * - Account validation for transaction authorization and risk assessment
 * - Account search and retrieval with advanced filtering capabilities
 * - Integration with transaction service for real-time balance updates
 * - Account status management and access control enforcement
 */
@Module({
  imports: [
    ClientsModule.registerAsync([
      // {
      //   name: 'TRANSACTION_PACKAGE',
      //   imports: [],
      //   useFactory: (configService: ConfigService, protoConfigService: ProtoConfigService) => ({
      //     transport: Transport.GRPC,
      //     options: {
      //       package: 'transaction',
      //       protoPath: protoConfigService.getProtoPath('transaction', 'transaction.proto'),
      //       url: configService.getServiceUrl('transaction-service-grpc'),
      //       loader: protoConfigService.getLoaderOptions(),
      //       channelOptions: protoConfigService.getChannelOptions(),
      //     },
      //   }),
      //   inject: [ConfigService, ProtoConfigService],
      // },
      {
        name: 'NOTIFICATION_PACKAGE',
        imports: [],
        useFactory: (configService: ConfigService, protoConfigService: ProtoConfigService) => ({
          transport: Transport.GRPC,
          options: {
            package: 'notification',
            protoPath: protoConfigService.getProtoPath('notification', 'notification.proto'),
            url: configService.getServiceUrl('notification-service-grpc'),
            loader: protoConfigService.getLoaderOptions(),
            channelOptions: protoConfigService.getChannelOptions(),
          },
        }),
        inject: [ConfigService, ProtoConfigService],
      },
      // {
      //   name: 'AML_PACKAGE',
      //   imports: [],
      //   useFactory: (configService: ConfigService, protoConfigService: ProtoConfigService) => ({
      //     transport: Transport.GRPC,
      //     options: {
      //       package: 'aml',
      //       protoPath: protoConfigService.getProtoPath('aml', 'aml.proto'),
      //       url: configService.getServiceUrl('aml-service-grpc'),
      //       loader: protoConfigService.getLoaderOptions(),
      //       channelOptions: protoConfigService.getChannelOptions(),
      //     },
      //   }),
      //   inject: [ConfigService, ProtoConfigService],
      // },
    ]),
  ],
  controllers: [AccountsController],
  providers: [
    AccountsService,
    CustomerPrismaService,
    AccountRepository,
    {
      provide: IAccountRepository,
      useClass: AccountRepository,
    },
  ],
  exports: [AccountsService, CustomerPrismaService, IAccountRepository],
})
export class AccountsModule {}
