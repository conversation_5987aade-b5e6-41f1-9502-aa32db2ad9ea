import { Injectable, Logger } from '@nestjs/common';
import { AccountAccessLevel, AccountStatus, AccountType, Prisma } from '@prisma/customer-client';
import { FindAccountsRequestDto } from '@qeep/contracts';
import { CustomerPrismaService } from '../../../database/prisma.service';

// Type for Prisma account with includes
type PrismaAccountWithIncludes = Prisma.AccountGetPayload<{
  include: {
    customer: {
      select: {
        id: true;
        customerType: true;
        status: true;
      };
    };
  };
}>;

// Type for account creation data
interface CreateAccountData {
  id: string;
  tenantId: string;
  customerId: string;
  accountNumber: string;
  accountType: AccountType;
  accountStatus: AccountStatus;
  currency: string;
  accessLevel: AccountAccessLevel;
  balance: number;
  availableBalance: number;
  overdraftLimit: number;
  interestRate: number;
  minimumBalance: number;
  accountName: string;
  description?: string;
  metadata: Prisma.InputJsonValue;
  isActive: boolean;
}

// Type for account update data
interface UpdateAccountData {
  accountName?: string;
  description?: string;
  accountStatus?: AccountStatus;
  accessLevel?: AccountAccessLevel;
  overdraftLimit?: number;
  interestRate?: number;
  minimumBalance?: number;
  metadata?: Prisma.InputJsonValue;
  isActive?: boolean;
}

/**
 * Accounts Repository
 *
 * Handles all database operations for the accounts domain.
 * Provides a clean abstraction layer between the service and database.
 *
 * Key Responsibilities:
 * - Account CRUD operations with proper error handling
 * - Complex queries with filtering, pagination, and sorting
 * - Database transaction management for account operations
 * - Balance updates and financial data management
 * - Relationship management with customers and other entities
 */
@Injectable()
export class AccountsRepository {
  private readonly logger = new Logger(AccountsRepository.name);

  constructor(private readonly prisma: CustomerPrismaService) {}

  /**
   * Create a new account
   */
  async createAccount(data: CreateAccountData): Promise<PrismaAccountWithIncludes> {
    this.logger.log(`Creating account for customer: ${data.customerId}`);

    return await this.prisma.account.create({
      data: {
        id: data.id,
        tenantId: data.tenantId,
        customerId: data.customerId,
        accountNumber: data.accountNumber,
        accountType: data.accountType,
        accountStatus: data.accountStatus,
        currency: data.currency,
        accessLevel: data.accessLevel,
        balance: data.balance,
        availableBalance: data.availableBalance,
        overdraftLimit: data.overdraftLimit,
        interestRate: data.interestRate,
        minimumBalance: data.minimumBalance,
        accountName: data.accountName,
        description: data.description,
        metadata: data.metadata,
        isActive: data.isActive,
      },
      include: {
        customer: {
          select: {
            id: true,
            customerType: true,
            status: true,
          },
        },
      },
    });
  }

  /**
   * Find accounts with filtering and pagination
   */
  async findAccounts(query: FindAccountsRequestDto, skip: number, take: number): Promise<{ accounts: PrismaAccountWithIncludes[]; total: number }> {
    this.logger.log(`Finding accounts with filters: ${JSON.stringify(query)}`);

    // Build where clause
    const where: Prisma.AccountWhereInput = {
      tenantId: query.tenantId,
    };

    if (query.customerId) {
      where.customerId = query.customerId;
    }

    if (query.accountType) {
      where.accountType = query.accountType;
    }

    if (query.accountStatus) {
      where.accountStatus = query.accountStatus;
    }

    if (query.currency) {
      where.currency = query.currency;
    }

    if (query.accessLevel) {
      where.accessLevel = query.accessLevel;
    }

    if (query.isActive !== undefined) {
      where.isActive = query.isActive;
    }

    if (query.minBalance !== undefined || query.maxBalance !== undefined) {
      where.balance = {};
      if (query.minBalance !== undefined) {
        where.balance.gte = query.minBalance;
      }
      if (query.maxBalance !== undefined) {
        where.balance.lte = query.maxBalance;
      }
    }

    // Execute queries in parallel
    const [accounts, total] = await Promise.all([
      this.prisma.account.findMany({
        where,
        skip,
        take,
        orderBy: { createdAt: 'desc' },
        include: {
          customer: {
            select: {
              id: true,
              customerType: true,
              status: true,
            },
          },
        },
      }),
      this.prisma.account.count({ where }),
    ]);

    return { accounts, total };
  }

  /**
   * Find account by ID
   */
  async findAccountById(accountId: string): Promise<PrismaAccountWithIncludes | null> {
    this.logger.log(`Finding account by ID: ${accountId}`);

    return await this.prisma.account.findUnique({
      where: { id: accountId },
      include: {
        customer: {
          select: {
            id: true,
            customerType: true,
            status: true,
          },
        },
      },
    });
  }

  /**
   * Find account by account number
   */
  async findAccountByNumber(accountNumber: string): Promise<PrismaAccountWithIncludes | null> {
    this.logger.log(`Finding account by number: ${accountNumber}`);

    return await this.prisma.account.findUnique({
      where: { accountNumber },
      include: {
        customer: {
          select: {
            id: true,
            customerType: true,
            status: true,
          },
        },
      },
    });
  }

  /**
   * Update account
   */
  async updateAccount(accountId: string, data: UpdateAccountData): Promise<PrismaAccountWithIncludes> {
    this.logger.log(`Updating account: ${accountId}`);

    return await this.prisma.account.update({
      where: { id: accountId },
      data,
      include: {
        customer: {
          select: {
            id: true,
            customerType: true,
            status: true,
          },
        },
      },
    });
  }

  /**
   * Update account balance and transaction timestamp
   */
  async updateAccountBalance(accountId: string, balance: number, availableBalance: number, lastTransactionAt: Date): Promise<void> {
    this.logger.log(`Updating account balance: ${accountId}`);

    await this.prisma.account.update({
      where: { id: accountId },
      data: {
        balance,
        availableBalance,
        lastTransactionAt,
      },
    });
  }

  /**
   * Soft delete account (set status to CLOSED)
   */
  async softDeleteAccount(accountId: string): Promise<void> {
    this.logger.log(`Soft deleting account: ${accountId}`);

    await this.prisma.account.update({
      where: { id: accountId },
      data: {
        accountStatus: AccountStatus.CLOSED,
        isActive: false,
      },
    });
  }

  /**
   * Check if customer exists and belongs to tenant
   */
  async findCustomerById(customerId: string): Promise<{ id: string; status: string; tenantId: string } | null> {
    this.logger.log(`Finding customer by ID: ${customerId}`);

    return await this.prisma.customer.findUnique({
      where: { id: customerId },
      select: { id: true, status: true, tenantId: true },
    });
  }

  /**
   * Check if account number is unique
   */
  async isAccountNumberUnique(accountNumber: string): Promise<boolean> {
    this.logger.log(`Checking if account number is unique: ${accountNumber}`);

    const existingAccount = await this.prisma.account.findUnique({
      where: { accountNumber },
      select: { id: true },
    });

    return !existingAccount;
  }

  /**
   * Get accounts count by customer
   */
  async getAccountsCountByCustomer(customerId: string): Promise<number> {
    this.logger.log(`Getting accounts count for customer: ${customerId}`);

    return await this.prisma.account.count({
      where: { customerId },
    });
  }

  /**
   * Get accounts by customer with pagination
   */
  async findAccountsByCustomer(customerId: string, skip: number, take: number): Promise<{ accounts: PrismaAccountWithIncludes[]; total: number }> {
    this.logger.log(`Finding accounts for customer: ${customerId}`);

    const [accounts, total] = await Promise.all([
      this.prisma.account.findMany({
        where: { customerId },
        skip,
        take,
        orderBy: { createdAt: 'desc' },
        include: {
          customer: {
            select: {
              id: true,
              customerType: true,
              status: true,
            },
          },
        },
      }),
      this.prisma.account.count({ where: { customerId } }),
    ]);

    return { accounts, total };
  }

  /**
   * Execute database transaction
   */
  async executeTransaction<T>(callback: (tx: Prisma.TransactionClient) => Promise<T>): Promise<T> {
    this.logger.log('Executing database transaction');

    return await this.prisma.$transaction(callback);
  }

  /**
   * Get account statistics for a tenant
   */
  async getAccountStatistics(tenantId: string): Promise<{
    totalAccounts: number;
    activeAccounts: number;
    totalBalance: number;
    accountsByType: Record<string, number>;
    accountsByStatus: Record<string, number>;
  }> {
    this.logger.log(`Getting account statistics for tenant: ${tenantId}`);

    const [totalAccounts, activeAccounts, balanceSum, accountsByType, accountsByStatus] = await Promise.all([
      this.prisma.account.count({ where: { tenantId } }),
      this.prisma.account.count({ where: { tenantId, isActive: true } }),
      this.prisma.account.aggregate({
        where: { tenantId },
        _sum: { balance: true },
      }),
      this.prisma.account.groupBy({
        by: ['accountType'],
        where: { tenantId },
        _count: { accountType: true },
      }),
      this.prisma.account.groupBy({
        by: ['accountStatus'],
        where: { tenantId },
        _count: { accountStatus: true },
      }),
    ]);

    const accountsByTypeMap = accountsByType.reduce((acc, item) => {
      acc[item.accountType] = item._count.accountType;
      return acc;
    }, {} as Record<string, number>);

    const accountsByStatusMap = accountsByStatus.reduce((acc, item) => {
      acc[item.accountStatus] = item._count.accountStatus;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalAccounts,
      activeAccounts,
      totalBalance: balanceSum._sum.balance?.toNumber() || 0,
      accountsByType: accountsByTypeMap,
      accountsByStatus: accountsByStatusMap,
    };
  }
}
