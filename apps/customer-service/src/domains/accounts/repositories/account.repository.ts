/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable } from '@nestjs/common';
import { Account, Prisma } from '@prisma/customer-client';
import { BaseRepository, IBaseRepository } from '@qeep/common';
import { CustomerPrismaService } from '../../../database/prisma.service';

// Custom types for complex queries
export type AccountWithCustomer = Prisma.AccountGetPayload<{
  include: { customer: true };
}>;

export type AccountSummary = Pick<Account, 'id' | 'tenantId' | 'customerId' | 'accountNumber' | 'accountType' | 'accountStatus' | 'balance'>;

export interface AccountFilters {
  tenantId?: string;
  customerId?: string;
  accountType?: string;
  accountStatus?: string;
  currency?: string;
  accessLevel?: string;
  isActive?: boolean;
  minBalance?: number;
  maxBalance?: number;
}

export interface AccountBalanceUpdate {
  balance: number;
  availableBalance: number;
  lastTransactionAt?: Date;
}

/**
 * Account Repository Interface
 * Defines all account-specific data access operations
 */
export interface IAccountRepository extends IBaseRepository<Account, Prisma.AccountCreateInput, Prisma.AccountUpdateInput> {
  // Customer-based operations
  findByCustomerId(customerId: string): Promise<Account[]>;
  findByCustomerIdAndType(customerId: string, accountType: string): Promise<Account[]>;
  countByCustomerId(customerId: string): Promise<number>;

  // Tenant-based operations
  findByTenant(tenantId: string, options?: { limit?: number; offset?: number }): Promise<Account[]>;
  countByTenant(tenantId: string): Promise<number>;

  // Account identification
  findByAccountNumber(accountNumber: string, tenantId?: string): Promise<Account | null>;
  generateUniqueAccountNumber(tenantId: string, accountType: string): Promise<string>;

  // Domain-focused search and filtering
  findAccountsMatchingCriteria(filters: AccountFilters, options?: { limit?: number; offset?: number }): Promise<Account[]>;
  countAccountsMatchingCriteria(filters: AccountFilters): Promise<number>;

  // Balance operations
  updateBalance(accountId: string, balanceUpdate: AccountBalanceUpdate): Promise<Account>;
  findAccountsWithLowBalance(tenantId: string, threshold: number): Promise<Account[]>;
  findAccountsWithHighBalance(tenantId: string, threshold: number): Promise<Account[]>;

  // Status and access operations
  updateAccountStatus(accountId: string, status: string): Promise<Account>;
  updateAccessLevel(accountId: string, accessLevel: string): Promise<Account>;
  activateAccount(accountId: string): Promise<Account>;
  deactivateAccount(accountId: string): Promise<Account>;

  // Complex queries
  findByIdWithCustomer(id: string): Promise<AccountWithCustomer | null>;
  findActiveAccountsByCustomer(customerId: string): Promise<Account[]>;
  findAccountsByType(accountType: string, tenantId?: string): Promise<Account[]>;

  // Validation operations
  validateAccountForTransaction(
    accountId: string,
    amount: number,
    operation: 'debit' | 'credit',
  ): Promise<{
    isValid: boolean;
    account: Account | null;
    reason?: string;
  }>;

  // Statistics and reporting
  getAccountStatistics(tenantId: string): Promise<{
    total: number;
    byType: Record<string, number>;
    byStatus: Record<string, number>;
    byCurrency: Record<string, number>;
    totalBalance: number;
    averageBalance: number;
  }>;
}

/**
 * Account Repository Implementation
 * Handles all account entity data access operations
 */
@Injectable()
export class AccountRepository extends BaseRepository<Account, Prisma.AccountCreateInput, Prisma.AccountUpdateInput> implements IAccountRepository {
  constructor(prisma: CustomerPrismaService) {
    super(prisma);
  }

  protected get model() {
    return this.prisma.account;
  }

  /**
   * Find all accounts for a specific customer
   */
  async findByCustomerId(customerId: string): Promise<Account[]> {
    try {
      return await this.model.findMany({
        where: { customerId },
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      throw new Error(`Failed to find accounts by customer ID ${customerId}: ${error.message}`);
    }
  }

  /**
   * Find accounts by customer ID and account type
   */
  async findByCustomerIdAndType(customerId: string, accountType: string): Promise<Account[]> {
    try {
      return await this.model.findMany({
        where: {
          customerId,
          accountType: accountType as Prisma.EnumAccountTypeFilter,
        },
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      throw new Error(`Failed to find accounts by customer ${customerId} and type ${accountType}: ${error.message}`);
    }
  }

  /**
   * Count accounts for a specific customer
   */
  async countByCustomerId(customerId: string): Promise<number> {
    try {
      return await this.model.count({ where: { customerId } });
    } catch (error) {
      throw new Error(`Failed to count accounts by customer ID ${customerId}: ${error.message}`);
    }
  }

  /**
   * Find accounts by tenant with pagination
   */
  async findByTenant(tenantId: string, options?: { limit?: number; offset?: number }): Promise<Account[]> {
    try {
      return await this.model.findMany({
        where: { tenantId },
        take: options?.limit,
        skip: options?.offset,
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      throw new Error(`Failed to find accounts by tenant ${tenantId}: ${error.message}`);
    }
  }

  /**
   * Count accounts by tenant
   */
  async countByTenant(tenantId: string): Promise<number> {
    try {
      return await this.model.count({ where: { tenantId } });
    } catch (error) {
      throw new Error(`Failed to count accounts by tenant ${tenantId}: ${error.message}`);
    }
  }

  /**
   * Find account by account number
   */
  async findByAccountNumber(accountNumber: string, tenantId?: string): Promise<Account | null> {
    try {
      const where: any = { accountNumber };
      if (tenantId) where.tenantId = tenantId;

      return await this.model.findFirst({ where });
    } catch (error) {
      throw new Error(`Failed to find account by number ${accountNumber}: ${error.message}`);
    }
  }

  /**
   * Generate unique account number for tenant and account type
   */
  async generateUniqueAccountNumber(tenantId: string, accountType: string): Promise<string> {
    try {
      const prefixes = {
        CHECKING: '10',
        SAVINGS: '20',
        BUSINESS: '30',
        INVESTMENT: '40',
        CREDIT: '50',
      };

      const prefix = prefixes[accountType] || '00';
      let attempts = 0;
      const maxAttempts = 10;

      while (attempts < maxAttempts) {
        const randomSuffix = Math.floor(Math.random() * 1000000)
          .toString()
          .padStart(6, '0');
        const accountNumber = `${prefix}${randomSuffix}`;

        const existing = await this.findByAccountNumber(accountNumber, tenantId);
        if (!existing) {
          return accountNumber;
        }

        attempts++;
      }

      throw new Error(`Failed to generate unique account number after ${maxAttempts} attempts`);
    } catch (error) {
      throw new Error(`Failed to generate account number: ${error.message}`);
    }
  }

  /**
   * Find accounts matching specified criteria
   */
  async findAccountsMatchingCriteria(filters: AccountFilters, options?: { limit?: number; offset?: number }): Promise<Account[]> {
    try {
      const where: any = {};

      if (filters.tenantId) where.tenantId = filters.tenantId;
      if (filters.customerId) where.customerId = filters.customerId;
      if (filters.accountType) where.accountType = filters.accountType;
      if (filters.accountStatus) where.accountStatus = filters.accountStatus;
      if (filters.currency) where.currency = filters.currency;
      if (filters.accessLevel) where.accessLevel = filters.accessLevel;
      if (filters.isActive !== undefined) where.isActive = filters.isActive;

      if (filters.minBalance !== undefined || filters.maxBalance !== undefined) {
        where.balance = {};
        if (filters.minBalance !== undefined) where.balance.gte = filters.minBalance;
        if (filters.maxBalance !== undefined) where.balance.lte = filters.maxBalance;
      }

      return await this.model.findMany({
        where,
        take: options?.limit,
        skip: options?.offset,
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      throw new Error(`Failed to find accounts with filters: ${error.message}`);
    }
  }

  /**
   * Count accounts matching specified criteria
   */
  async countAccountsMatchingCriteria(filters: AccountFilters): Promise<number> {
    try {
      const where: any = {};

      if (filters.tenantId) where.tenantId = filters.tenantId;
      if (filters.customerId) where.customerId = filters.customerId;
      if (filters.accountType) where.accountType = filters.accountType;
      if (filters.accountStatus) where.accountStatus = filters.accountStatus;
      if (filters.currency) where.currency = filters.currency;
      if (filters.accessLevel) where.accessLevel = filters.accessLevel;
      if (filters.isActive !== undefined) where.isActive = filters.isActive;

      if (filters.minBalance !== undefined || filters.maxBalance !== undefined) {
        where.balance = {};
        if (filters.minBalance !== undefined) where.balance.gte = filters.minBalance;
        if (filters.maxBalance !== undefined) where.balance.lte = filters.maxBalance;
      }

      return await this.model.count({ where });
    } catch (error) {
      throw new Error(`Failed to count accounts with filters: ${error.message}`);
    }
  }

  /**
   * Update account balance
   */
  async updateBalance(accountId: string, balanceUpdate: AccountBalanceUpdate): Promise<Account> {
    try {
      return await this.model.update({
        where: { id: accountId },
        data: {
          balance: balanceUpdate.balance,
          availableBalance: balanceUpdate.availableBalance,
          lastTransactionAt: balanceUpdate.lastTransactionAt || new Date(),
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      throw new Error(`Failed to update balance for account ${accountId}: ${error.message}`);
    }
  }

  /**
   * Find accounts with low balance
   */
  async findAccountsWithLowBalance(tenantId: string, threshold: number): Promise<Account[]> {
    try {
      return await this.model.findMany({
        where: {
          tenantId,
          balance: { lt: threshold },
          isActive: true,
        },
        orderBy: { balance: 'asc' },
      });
    } catch (error) {
      throw new Error(`Failed to find accounts with low balance for tenant ${tenantId}: ${error.message}`);
    }
  }

  /**
   * Find accounts with high balance
   */
  async findAccountsWithHighBalance(tenantId: string, threshold: number): Promise<Account[]> {
    try {
      return await this.model.findMany({
        where: {
          tenantId,
          balance: { gt: threshold },
          isActive: true,
        },
        orderBy: { balance: 'desc' },
      });
    } catch (error) {
      throw new Error(`Failed to find accounts with high balance for tenant ${tenantId}: ${error.message}`);
    }
  }

  /**
   * Update account status
   */
  async updateAccountStatus(accountId: string, status: string): Promise<Account> {
    try {
      return await this.model.update({
        where: { id: accountId },
        data: {
          accountStatus: status as any,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      throw new Error(`Failed to update account status for ${accountId}: ${error.message}`);
    }
  }

  /**
   * Update access level
   */
  async updateAccessLevel(accountId: string, accessLevel: string): Promise<Account> {
    try {
      return await this.model.update({
        where: { id: accountId },
        data: {
          accessLevel: accessLevel as any,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      throw new Error(`Failed to update access level for account ${accountId}: ${error.message}`);
    }
  }

  /**
   * Activate account
   */
  async activateAccount(accountId: string): Promise<Account> {
    try {
      return await this.model.update({
        where: { id: accountId },
        data: {
          isActive: true,
          accountStatus: 'ACTIVE',
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      throw new Error(`Failed to activate account ${accountId}: ${error.message}`);
    }
  }

  /**
   * Deactivate account
   */
  async deactivateAccount(accountId: string): Promise<Account> {
    try {
      return await this.model.update({
        where: { id: accountId },
        data: {
          isActive: false,
          accountStatus: 'INACTIVE',
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      throw new Error(`Failed to deactivate account ${accountId}: ${error.message}`);
    }
  }

  /**
   * Find account by ID with customer information
   */
  async findByIdWithCustomer(id: string): Promise<AccountWithCustomer | null> {
    try {
      return await this.model.findUnique({
        where: { id },
        include: { customer: true },
      });
    } catch (error) {
      throw new Error(`Failed to find account with customer ${id}: ${error.message}`);
    }
  }

  /**
   * Find active accounts for a customer
   */
  async findActiveAccountsByCustomer(customerId: string): Promise<Account[]> {
    try {
      return await this.model.findMany({
        where: {
          customerId,
          isActive: true,
          accountStatus: 'ACTIVE',
        },
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      throw new Error(`Failed to find active accounts for customer ${customerId}: ${error.message}`);
    }
  }

  /**
   * Find accounts by type
   */
  async findAccountsByType(accountType: string, tenantId?: string): Promise<Account[]> {
    try {
      const where: any = { accountType };
      if (tenantId) where.tenantId = tenantId;

      return await this.model.findMany({
        where,
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      throw new Error(`Failed to find accounts by type ${accountType}: ${error.message}`);
    }
  }

  /**
   * Validate account for transaction
   */
  async validateAccountForTransaction(
    accountId: string,
    amount: number,
    operation: 'debit' | 'credit',
  ): Promise<{
    isValid: boolean;
    account: Account | null;
    reason?: string;
  }> {
    try {
      const account = await this.findById(accountId);

      if (!account) {
        return { isValid: false, account: null, reason: 'Account not found' };
      }

      if (!account.isActive) {
        return { isValid: false, account, reason: 'Account is not active' };
      }

      if (account.accountStatus !== 'ACTIVE') {
        return { isValid: false, account, reason: `Account status is ${account.accountStatus}` };
      }

      if (operation === 'debit') {
        const availableBalance = Number(account.availableBalance);
        const currentBalance = Number(account.balance);
        const overdraftLimit = Number(account.overdraftLimit);

        if (availableBalance < amount) {
          return { isValid: false, account, reason: 'Insufficient available balance' };
        }

        const newBalance = currentBalance - amount;
        if (newBalance < -overdraftLimit) {
          return { isValid: false, account, reason: 'Transaction would exceed overdraft limit' };
        }
      }

      return { isValid: true, account };
    } catch (error) {
      throw new Error(`Failed to validate account for transaction: ${error.message}`);
    }
  }

  /**
   * Get account statistics for a tenant
   */
  async getAccountStatistics(tenantId: string): Promise<{
    total: number;
    byType: Record<string, number>;
    byStatus: Record<string, number>;
    byCurrency: Record<string, number>;
    totalBalance: number;
    averageBalance: number;
  }> {
    try {
      const [total, byType, byStatus, byCurrency, balanceStats] = await Promise.all([
        this.model.count({ where: { tenantId } }),
        this.model.groupBy({
          by: ['accountType'],
          where: { tenantId },
          _count: true,
        }),
        this.model.groupBy({
          by: ['accountStatus'],
          where: { tenantId },
          _count: true,
        }),
        this.model.groupBy({
          by: ['currency'],
          where: { tenantId },
          _count: true,
        }),
        this.model.aggregate({
          where: { tenantId },
          _sum: { balance: true },
          _avg: { balance: true },
        }),
      ]);

      return {
        total,
        byType: byType.reduce((acc, item) => ({ ...acc, [item.accountType]: item._count }), {}),
        byStatus: byStatus.reduce((acc, item) => ({ ...acc, [item.accountStatus]: item._count }), {}),
        byCurrency: byCurrency.reduce((acc, item) => ({ ...acc, [item.currency]: item._count }), {}),
        totalBalance: Number(balanceStats._sum.balance) || 0,
        averageBalance: Number(balanceStats._avg.balance) || 0,
      };
    } catch (error) {
      throw new Error(`Failed to get account statistics for tenant ${tenantId}: ${error.message}`);
    }
  }
}
