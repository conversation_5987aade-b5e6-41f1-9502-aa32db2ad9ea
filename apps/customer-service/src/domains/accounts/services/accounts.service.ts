/* eslint-disable @typescript-eslint/no-explicit-any */
import { BadRequestException, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { CUID_PREFIXES, generateCuid } from '@qeep/common';
import {
  AccountAccessLevel,
  AccountCurrency,
  AccountResponseDto,
  AccountStatus,
  AccountType,
  CreateAccountRequestDto,
  FindAccountsRequestDto,
  FindAccountsResponseDto,
  UpdateAccountRequestDto,
  ValidateAccountRequestDto,
  ValidateAccountResponseDto,
} from '@qeep/contracts/customer';
import { IAccountRepository } from '../repositories';

@Injectable()
export class AccountsService {
  private readonly logger = new Logger(AccountsService.name);

  constructor(@Inject('IAccountRepository') private readonly accountsRepository: IAccountRepository) {}

  /**
   * Transform Prisma account data to response DTO
   * Note: This transformation is necessary primarily for Decimal to number conversion
   */
  private transformAccountToResponse(account: any): AccountResponseDto {
    return {
      ...account,
      // Convert Prisma Decimal types to numbers for API response
      balance: Number(account.balance),
      availableBalance: Number(account.availableBalance),
      overdraftLimit: Number(account.overdraftLimit),
      interestRate: Number(account.interestRate),
      minimumBalance: Number(account.minimumBalance),
      // Type assertions for enums (minimal transformation)
      accountType: account.accountType as AccountType,
      accountStatus: account.accountStatus as AccountStatus,
      currency: account.currency as AccountCurrency,
      accessLevel: account.accessLevel as AccountAccessLevel,
    };
  }

  /**
   * Generate unique account number
   */
  private async generateAccountNumber(tenantId: string, accountType: AccountType): Promise<string> {
    // Generate account number based on tenant and type
    const typePrefix = this.getAccountTypePrefix(accountType);
    const timestamp = Date.now().toString().slice(-8);
    const random = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, '0');

    return `${typePrefix}${timestamp}${random}`;
  }

  /**
   * Get account type prefix for account number generation
   */
  private getAccountTypePrefix(accountType: AccountType): string {
    const prefixes = {
      [AccountType.CHECKING]: '10',
      [AccountType.SAVINGS]: '20',
      [AccountType.BUSINESS]: '30',
      [AccountType.INVESTMENT]: '40',
      [AccountType.CREDIT]: '50',
    };
    return prefixes[accountType] || '00';
  }

  /**
   * Create a new account
   */
  async createAccount(createAccountDto: CreateAccountRequestDto): Promise<AccountResponseDto> {
    this.logger.log(`Creating account for customer: ${createAccountDto.customerId}`);

    try {
      // Generate unique identifiers
      const accountId = generateCuid(CUID_PREFIXES.ACCOUNT);
      const accountNumber = await this.accountsRepository.generateUniqueAccountNumber(createAccountDto.tenantId, createAccountDto.accountType);

      // Create account
      const account = await this.accountsRepository.create({
        id: accountId,
        tenantId: createAccountDto.tenantId,
        customer: { connect: { id: createAccountDto.customerId } },
        accountNumber,
        accountType: createAccountDto.accountType as any,
        accountStatus: AccountStatus.ACTIVE as any,
        currency: createAccountDto.currency as any,
        accessLevel: AccountAccessLevel.FULL as any,
        balance: createAccountDto.initialBalance || 0,
        availableBalance: createAccountDto.initialBalance || 0,
        overdraftLimit: createAccountDto.overdraftLimit || 0,
        interestRate: createAccountDto.interestRate || 0,
        minimumBalance: createAccountDto.minimumBalance || 0,
        accountName: createAccountDto.accountName,
        description: createAccountDto.description,
        metadata: createAccountDto.metadata || {},
        isActive: true,
      });

      this.logger.log(`Account created successfully: ${account.id}`);

      return this.transformAccountToResponse(account);
    } catch (error) {
      this.logger.error(`Failed to create account: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Find accounts with filtering and pagination
   */
  async findAccounts(query: FindAccountsRequestDto): Promise<FindAccountsResponseDto> {
    this.logger.log(`Finding accounts with query: ${JSON.stringify(query)}`);

    try {
      const { page = 1, limit = 20 } = query;
      const skip = (page - 1) * limit;

      // Use repository to find accounts
      const accounts = await this.accountsRepository.findAccountsMatchingCriteria(query, { limit, offset: skip });
      const total = await this.accountsRepository.countAccountsMatchingCriteria(query);

      const transformedAccounts = accounts.map((account) => this.transformAccountToResponse(account));

      return {
        accounts: transformedAccounts,
        pagination: {
          total,
          page,
          limit,
          offset: skip,
          totalPages: Math.ceil(total / limit),
          hasMore: page * limit < total,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to find accounts: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get account by ID
   */
  async getAccountById(accountId: string): Promise<AccountResponseDto> {
    this.logger.log(`Getting account by ID: ${accountId}`);

    try {
      const account = await this.accountsRepository.findById(accountId);

      if (!account) {
        throw new NotFoundException(`Account with ID ${accountId} not found`);
      }

      return this.transformAccountToResponse(account);
    } catch (error) {
      this.logger.error(`Failed to get account by ID: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update account
   */
  async updateAccount(accountId: string, updateAccountDto: UpdateAccountRequestDto): Promise<AccountResponseDto> {
    this.logger.log(`Updating account: ${accountId}`);

    try {
      // Verify account exists
      const existingAccount = await this.accountsRepository.findById(accountId);

      if (!existingAccount) {
        throw new NotFoundException(`Account with ID ${accountId} not found`);
      }

      // Update account
      const account = await this.accountsRepository.update(accountId, updateAccountDto);

      this.logger.log(`Account updated successfully: ${accountId}`);

      return this.transformAccountToResponse(account);
    } catch (error) {
      this.logger.error(`Failed to update account: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete account (soft delete by setting status to CLOSED)
   */
  async deleteAccount(accountId: string): Promise<void> {
    this.logger.log(`Deleting account: ${accountId}`);

    try {
      // Verify account exists
      const existingAccount = await this.accountsRepository.findById(accountId);

      if (!existingAccount) {
        throw new NotFoundException(`Account with ID ${accountId} not found`);
      }

      // Check if account has balance
      if (Number(existingAccount.balance) !== 0) {
        throw new BadRequestException('Cannot delete account with non-zero balance');
      }

      // Soft delete by setting status to CLOSED
      await this.accountsRepository.deactivateAccount(accountId);

      this.logger.log(`Account deleted successfully: ${accountId}`);
    } catch (error) {
      this.logger.error(`Failed to delete account: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete account (soft delete by setting status to CLOSED)
   */
  async softDeleteAccount(accountId: string): Promise<void> {
    this.logger.log(`Soft deleting account: ${accountId}`);

    try {
      // Verify account exists
      const existingAccount = await this.accountsRepository.findById(accountId);

      if (!existingAccount) {
        throw new NotFoundException(`Account with ID ${accountId} not found`);
      }

      // Check if account has balance
      if (Number(existingAccount.balance) !== 0) {
        throw new BadRequestException('Cannot delete account with non-zero balance');
      }

      // Soft delete by setting status to CLOSED
      await this.accountsRepository.deactivateAccount(accountId);

      this.logger.log(`Account deleted successfully: ${accountId}`);
    } catch (error) {
      this.logger.error(`Failed to delete account: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Validate account for transaction processing
   */
  async validateAccount(validateAccountDto: ValidateAccountRequestDto): Promise<ValidateAccountResponseDto> {
    this.logger.log(`Validating account: ${validateAccountDto.accountId}`);

    try {
      const account = await this.accountsRepository.findById(validateAccountDto.accountId);

      if (!account) {
        return {
          isValid: false,
          accountStatus: AccountStatus.CLOSED,
          availableBalance: 0,
          currency: AccountCurrency.USD,
          accessLevel: AccountAccessLevel.BLOCKED,
          canDebit: false,
          canCredit: false,
          reason: 'Account not found',
        };
      }

      return {
        isValid: false,
        accountStatus: account.accountStatus as AccountStatus,
        availableBalance: account.availableBalance.toNumber(),
        currency: account.currency as AccountCurrency,
        accessLevel: account.accessLevel as AccountAccessLevel,
        canDebit: false,
        canCredit: false,
        reason: 'Account not found',
      };
    } catch (error) {
      this.logger.error(`Failed to validate account: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update account balance (used by transaction service)
   */
  async updateAccountBalance(accountId: string, balance: number, availableBalance: number, lastTransactionAt: Date): Promise<void> {
    this.logger.log(`Updating account balance: ${accountId}`);

    try {
      await this.accountsRepository.updateBalance(accountId, { balance, availableBalance, lastTransactionAt });

      this.logger.log(`Account balance updated successfully: ${accountId}`);
    } catch (error) {
      this.logger.error(`Failed to update account balance: ${error.message}`, error.stack);
      throw error;
    }
  }
}
