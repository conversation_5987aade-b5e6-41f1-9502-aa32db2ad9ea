import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { CUID_PREFIXES, generateCuid } from '@qeep/common';
import {
  AccountAccessLevel,
  AccountCurrency,
  AccountResponseDto,
  AccountStatus,
  AccountType,
  CreateAccountRequestDto,
  FindAccountsRequestDto,
  FindAccountsResponseDto,
  UpdateAccountRequestDto,
  ValidateAccountRequestDto,
  ValidateAccountResponseDto,
} from '@qeep/contracts';
import { AccountsRepository } from '../repositories/accounts.repository';

@Injectable()
export class AccountsService {
  private readonly logger = new Logger(AccountsService.name);

  constructor(private readonly accountsRepository: AccountsRepository) {}

  /**
   * Transform Prisma account data to response DTO
   */
  private transformAccountToResponse(account: any): AccountResponseDto {
    return {
      id: account.id,
      tenantId: account.tenantId,
      customerId: account.customerId,
      accountNumber: account.accountNumber,
      accountType: account.accountType as AccountType,
      accountStatus: account.accountStatus as AccountStatus,
      currency: account.currency as AccountCurrency,
      accessLevel: account.accessLevel as AccountAccessLevel,
      balance: account.balance.toNumber(),
      availableBalance: account.availableBalance.toNumber(),
      overdraftLimit: account.overdraftLimit.toNumber(),
      interestRate: account.interestRate.toNumber(),
      minimumBalance: account.minimumBalance.toNumber(),
      accountName: account.accountName,
      description: account.description,
      metadata: account.metadata as Record<string, unknown>,
      isActive: account.isActive,
      lastTransactionAt: account.lastTransactionAt,
      createdAt: account.createdAt,
      updatedAt: account.updatedAt,
    };
  }

  /**
   * Generate unique account number
   */
  private async generateAccountNumber(tenantId: string, accountType: AccountType): Promise<string> {
    // Generate account number based on tenant and type
    const typePrefix = this.getAccountTypePrefix(accountType);
    const timestamp = Date.now().toString().slice(-8);
    const random = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, '0');

    return `${typePrefix}${timestamp}${random}`;
  }

  /**
   * Get account type prefix for account number generation
   */
  private getAccountTypePrefix(accountType: AccountType): string {
    const prefixes = {
      [AccountType.CHECKING]: '10',
      [AccountType.SAVINGS]: '20',
      [AccountType.BUSINESS]: '30',
      [AccountType.INVESTMENT]: '40',
      [AccountType.CREDIT]: '50',
    };
    return prefixes[accountType] || '00';
  }

  /**
   * Create a new account
   */
  async createAccount(createAccountDto: CreateAccountRequestDto): Promise<AccountResponseDto> {
    this.logger.log(`Creating account for customer: ${createAccountDto.customerId}`);

    try {
      // Verify customer exists and is active
      const customer = await this.accountsRepository.findCustomerById(createAccountDto.customerId);

      if (!customer) {
        throw new NotFoundException(`Customer with ID ${createAccountDto.customerId} not found`);
      }

      if (customer.tenantId !== createAccountDto.tenantId) {
        throw new BadRequestException('Customer does not belong to the specified tenant');
      }

      // Generate unique identifiers
      const accountId = generateCuid(CUID_PREFIXES.ACCOUNT);
      const accountNumber = await this.generateAccountNumber(createAccountDto.tenantId, createAccountDto.accountType);

      // Create account
      const account = await this.accountsRepository.createAccount({
        id: accountId,
        tenantId: createAccountDto.tenantId,
        customerId: createAccountDto.customerId,
        accountNumber,
        accountType: createAccountDto.accountType,
        accountStatus: AccountStatus.ACTIVE,
        currency: createAccountDto.currency,
        accessLevel: AccountAccessLevel.FULL,
        balance: createAccountDto.initialBalance || 0,
        availableBalance: createAccountDto.initialBalance || 0,
        overdraftLimit: createAccountDto.overdraftLimit || 0,
        interestRate: createAccountDto.interestRate || 0,
        minimumBalance: createAccountDto.minimumBalance || 0,
        accountName: createAccountDto.accountName,
        description: createAccountDto.description,
        metadata: createAccountDto.metadata || {},
        isActive: true,
      });

      this.logger.log(`Account created successfully: ${account.id}`);

      return this.transformAccountToResponse(account);
    } catch (error) {
      this.logger.error(`Failed to create account: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Find accounts with filtering and pagination
   */
  async findAccounts(query: FindAccountsRequestDto): Promise<FindAccountsResponseDto> {
    this.logger.log(`Finding accounts with query: ${JSON.stringify(query)}`);

    try {
      const { page = 1, limit = 20 } = query;
      const skip = (page - 1) * limit;

      // Use repository to find accounts
      const { accounts, total } = await this.accountsRepository.findAccounts(query, skip, limit);

      const transformedAccounts = accounts.map((account) => this.transformAccountToResponse(account));

      return {
        accounts: transformedAccounts,
        pagination: {
          total,
          page,
          limit,
          offset: skip,
          totalPages: Math.ceil(total / limit),
          hasMore: page * limit < total,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to find accounts: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get account by ID
   */
  async getAccountById(accountId: string): Promise<AccountResponseDto> {
    this.logger.log(`Getting account by ID: ${accountId}`);

    try {
      const account = await this.accountsRepository.findAccountById(accountId);

      if (!account) {
        throw new NotFoundException(`Account with ID ${accountId} not found`);
      }

      return this.transformAccountToResponse(account);
    } catch (error) {
      this.logger.error(`Failed to get account by ID: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update account
   */
  async updateAccount(accountId: string, updateAccountDto: UpdateAccountRequestDto): Promise<AccountResponseDto> {
    this.logger.log(`Updating account: ${accountId}`);

    try {
      // Verify account exists
      const existingAccount = await this.accountsRepository.findAccountById(accountId);

      if (!existingAccount) {
        throw new NotFoundException(`Account with ID ${accountId} not found`);
      }

      // Update account
      const account = await this.accountsRepository.updateAccount(accountId, updateAccountDto);

      this.logger.log(`Account updated successfully: ${accountId}`);

      return this.transformAccountToResponse(account);
    } catch (error) {
      this.logger.error(`Failed to update account: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete account (soft delete by setting status to CLOSED)
   */
  async deleteAccount(accountId: string): Promise<void> {
    this.logger.log(`Deleting account: ${accountId}`);

    try {
      // Verify account exists
      const existingAccount = await this.accountsRepository.findAccountById(accountId);

      if (!existingAccount) {
        throw new NotFoundException(`Account with ID ${accountId} not found`);
      }

      // Check if account has balance
      if (existingAccount.balance.toNumber() !== 0) {
        throw new BadRequestException('Cannot delete account with non-zero balance');
      }

      // Soft delete by setting status to CLOSED
      await this.accountsRepository.softDeleteAccount(accountId);

      this.logger.log(`Account deleted successfully: ${accountId}`);
    } catch (error) {
      this.logger.error(`Failed to delete account: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete account (soft delete by setting status to CLOSED)
   */
  async softDeleteAccount(accountId: string): Promise<void> {
    this.logger.log(`Soft deleting account: ${accountId}`);

    try {
      // Verify account exists
      const existingAccount = await this.accountsRepository.findAccountById(accountId);

      if (!existingAccount) {
        throw new NotFoundException(`Account with ID ${accountId} not found`);
      }

      // Check if account has balance
      if (existingAccount.balance.toNumber() !== 0) {
        throw new BadRequestException('Cannot delete account with non-zero balance');
      }

      // Soft delete by setting status to CLOSED
      await this.accountsRepository.softDeleteAccount(accountId);

      this.logger.log(`Account deleted successfully: ${accountId}`);
    } catch (error) {
      this.logger.error(`Failed to delete account: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Validate account for transaction processing
   */
  async validateAccount(validateAccountDto: ValidateAccountRequestDto): Promise<ValidateAccountResponseDto> {
    this.logger.log(`Validating account: ${validateAccountDto.accountId}`);

    try {
      const account = await this.accountsRepository.findAccountById(validateAccountDto.accountId);

      if (!account) {
        return {
          isValid: false,
          accountStatus: AccountStatus.CLOSED,
          availableBalance: 0,
          currency: AccountCurrency.USD,
          accessLevel: AccountAccessLevel.BLOCKED,
          canDebit: false,
          canCredit: false,
          reason: 'Account not found',
        };
      }

      return {
        isValid: false,
        accountStatus: account.accountStatus as AccountStatus,
        availableBalance: account.availableBalance.toNumber(),
        currency: account.currency as AccountCurrency,
        accessLevel: account.accessLevel as AccountAccessLevel,
        canDebit: false,
        canCredit: false,
        reason: 'Account not found',
      };
    } catch (error) {
      this.logger.error(`Failed to validate account: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update account balance (used by transaction service)
   */
  async updateAccountBalance(accountId: string, balance: number, availableBalance: number, lastTransactionAt: Date): Promise<void> {
    this.logger.log(`Updating account balance: ${accountId}`);

    try {
      await this.accountsRepository.updateAccountBalance(accountId, balance, availableBalance, lastTransactionAt);

      this.logger.log(`Account balance updated successfully: ${accountId}`);
    } catch (error) {
      this.logger.error(`Failed to update account balance: ${error.message}`, error.stack);
      throw error;
    }
  }
}
