/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Prisma } from '@prisma/customer-client';
import {
  Customer,
  CustomerQueryOptions,
  CustomerSortField,
  CustomerStatus,
  CustomerEntityType as CustomerType,
  ICustomer,
  ICustomerKycData,
  CustomerKycStatus as KycStatus,
  CustomerRiskLevel as RiskLevel,
  Shared,
} from '@qeep/contracts';
import { CustomerPrismaService } from '../../../database/prisma.service';

// Type for Prisma customer with includes
type PrismaCustomerWithIncludes = Prisma.CustomerGetPayload<{
  include: {
    documents: true;
    riskScores: true;
    alerts: true;
  };
}>;

@Injectable()
export class CustomerManagementService {
  private readonly logger = new Logger(CustomerManagementService.name);

  constructor(private readonly prisma: CustomerPrismaService) {}

  /**
   * Transform Prisma customer data to contract interface
   */
  private transformCustomerToInterface(customer: PrismaCustomerWithIncludes): ICustomer {
    return {
      id: customer.id,
      tenantId: customer.tenantId,
      customerType: customer.customerType as CustomerType, // Type assertion to handle enum mismatch
      status: customer.status as CustomerStatus,
      riskLevel: customer.riskLevel as RiskLevel,
      kycStatus: customer.kycStatus as KycStatus,
      profileData: customer.profileData as any,
      kycData: customer.kycData as ICustomerKycData,
      riskAssessment: customer.riskAssessment as any,
      documents: customer.documents as any,
      riskScores: customer.riskScores as any,
      alerts: customer.alerts as any,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
      // Simplified transformations with type assertions
    };
  }

  /**
   * Get all customers with pagination and filtering (superadmin only)
   * @param options Query options for filtering and pagination
   * @returns Paginated list of customers
   */
  async getAllCustomers(options: CustomerQueryOptions = {}): Promise<{
    customers: ICustomer[];
    pagination: Shared.DTOs.PaginationMeta;
  }> {
    const { page = 1, limit = 10, search, status, customerType, riskLevel, kycStatus, tenantId, sortBy = 'created_at', sortOrder = 'desc' } = options;

    // Transform snake_case sortBy to CustomerSortField enum
    let transformedSortBy: CustomerSortField;
    try {
      transformedSortBy = sortBy ? Customer.Transformers.transformSortFieldFromSnakeCase(sortBy) : CustomerSortField.CREATED_AT;
    } catch (error) {
      this.logger.warn(`Invalid sort field: ${sortBy}, defaulting to createdAt. Error: ${error.message}`);
      transformedSortBy = CustomerSortField.CREATED_AT;
    }

    this.logger.log(`Getting all customers with options: ${JSON.stringify(options)}`);

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build where clause
    const where: Prisma.CustomerWhereInput = {};

    // Add tenant filter if provided (for tenant-specific queries)
    if (tenantId) {
      where.tenantId = tenantId;
    }

    // Add search filter
    if (search) {
      // Search in profile data (assuming it contains searchable fields)
      where.OR = [
        {
          profileData: {
            path: ['firstName'],
            string_contains: search,
          },
        },
        {
          profileData: {
            path: ['lastName'],
            string_contains: search,
          },
        },
        {
          profileData: {
            path: ['email'],
            string_contains: search,
          },
        },
        {
          profileData: {
            path: ['businessName'],
            string_contains: search,
          },
        },
      ];
    }

    // Add status filter
    if (status) {
      where.status = status;
    }

    // Add customer type filter
    if (customerType) {
      where.customerType = customerType;
    }

    // Add risk level filter
    if (riskLevel) {
      where.riskLevel = riskLevel;
    }

    // Add KYC status filter
    if (kycStatus) {
      where.kycStatus = kycStatus;
    }

    // Build order by clause using transformed sort field
    const orderBy: Prisma.CustomerOrderByWithRelationInput = {};
    if (transformedSortBy === CustomerSortField.CREATED_AT || transformedSortBy === CustomerSortField.UPDATED_AT) {
      orderBy[transformedSortBy] = sortOrder;
    } else {
      // Default to createdAt if invalid sortBy
      orderBy.createdAt = sortOrder;
    }

    try {
      // Get total count for pagination
      const total = await this.prisma.customer.count({ where });

      // Get customers with pagination
      const customers = await this.prisma.customer.findMany({
        where,
        skip,
        take: limit,
        orderBy,
        include: {
          documents: true,
          riskScores: {
            orderBy: {
              assessmentDate: 'desc',
            },
            take: 1, // Get latest risk score
          },
          alerts: {
            where: {
              status: {
                not: 'RESOLVED',
              },
            },
          },
        },
      });

      // Calculate pagination metadata
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      // const hasPrev = page > 1;

      this.logger.log(`Retrieved ${customers.length} customers out of ${total} total`);

      // Transform Prisma customers to contract interface
      const transformedCustomers = customers.map((customer) => this.transformCustomerToInterface(customer));

      return {
        customers: transformedCustomers,
        pagination: {
          total,
          limit,
          offset: skip,
          hasMore: hasNext,
          page,
          totalPages: totalPages,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get all customers: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get customer by ID
   * @param id Customer ID
   * @returns Customer details
   */
  async getCustomerById(id: string): Promise<ICustomer | null> {
    this.logger.log(`Getting customer by ID: ${id}`);

    try {
      const customer = await this.prisma.customer.findUnique({
        where: { id },
        include: {
          documents: true,
          riskScores: {
            orderBy: {
              assessmentDate: 'desc',
            },
          },
          relationships: {
            include: {
              relatedCustomer: {
                select: {
                  id: true,
                  profileData: true,
                  customerType: true,
                  status: true,
                },
              },
            },
          },
          relatedTo: {
            include: {
              primaryCustomer: {
                select: {
                  id: true,
                  profileData: true,
                  customerType: true,
                  status: true,
                },
              },
            },
          },
          alerts: {
            orderBy: {
              createdAt: 'desc',
            },
          },
          notes: {
            orderBy: {
              createdAt: 'desc',
            },
          },
        },
      });

      if (!customer) {
        throw new NotFoundException(`Customer with ID ${id} not found`);
      }

      this.logger.log(`Retrieved customer: ${id}`);
      return this.transformCustomerToInterface(customer);
    } catch (error) {
      this.logger.error(`Failed to get customer by ID ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get customer statistics
   * @returns Customer statistics
   */
  async getCustomerStats(): Promise<{
    totalCustomers: number;
    activeCustomers: number;
    pendingKyc: number;
    highRiskCustomers: number;
    recentCustomers: number;
    customersByType: Record<string, number>;
    customersByRiskLevel: Record<string, number>;
  }> {
    this.logger.log('Getting customer statistics');

    try {
      const [totalCustomers, activeCustomers, pendingKyc, highRiskCustomers, customersByType, customersByRiskLevel, recentCustomers] = await Promise.all([
        // Total customers
        this.prisma.customer.count(),

        // Active customers
        this.prisma.customer.count({
          where: { status: 'ACTIVE' },
        }),

        // Pending KYC
        this.prisma.customer.count({
          where: { kycStatus: { in: ['PENDING', 'IN_PROGRESS'] } },
        }),

        // High risk customers
        this.prisma.customer.count({
          where: { riskLevel: { in: ['HIGH', 'CRITICAL'] } },
        }),

        // Customers by type
        this.prisma.customer.groupBy({
          by: ['customerType'],
          _count: true,
        }),

        // Customers by risk level
        this.prisma.customer.groupBy({
          by: ['riskLevel'],
          _count: true,
        }),

        // Recent customers (last 30 days)
        this.prisma.customer.count({
          where: {
            createdAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            },
          },
        }),
      ]);

      const stats = {
        totalCustomers,
        activeCustomers,
        pendingKyc,
        highRiskCustomers,
        recentCustomers,
        customersByType: customersByType.reduce((acc: Record<string, number>, item: { customerType: string; _count: number }) => {
          acc[item.customerType] = item._count;
          return acc;
        }, {} as Record<string, number>),
        customersByRiskLevel: customersByRiskLevel.reduce((acc: Record<string, number>, item: { riskLevel: string; _count: number }) => {
          acc[item.riskLevel] = item._count;
          return acc;
        }, {} as Record<string, number>),
      };

      this.logger.log('Retrieved customer statistics');
      return stats;
    } catch (error) {
      this.logger.error(`Failed to get customer statistics: ${error.message}`, error.stack);
      throw error;
    }
  }
}
