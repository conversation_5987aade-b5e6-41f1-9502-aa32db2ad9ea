/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Customer } from '@prisma/customer-client';
import {
  CustomerQueryOptions,
  CustomerStatus,
  CustomerEntityType as CustomerType,
  ICustomer,
  ICustomerKycData,
  CustomerKycStatus as KycStatus,
  CustomerRiskLevel as RiskLevel,
  Shared,
} from '@qeep/contracts';
import { CustomerWithIncludes, ICustomerRepository } from '../../../repositories';

@Injectable()
export class CustomerManagementService {
  private readonly logger = new Logger(CustomerManagementService.name);

  constructor(private readonly customerRepository: ICustomerRepository) {}

  /**
   * Transform Prisma customer data to contract interface
   */
  private transformCustomerToInterface(customer: CustomerWithIncludes): ICustomer {
    return {
      id: customer.id,
      tenantId: customer.tenantId,
      customerType: customer.customerType as CustomerType, // Type assertion to handle enum mismatch
      status: customer.status as CustomerStatus,
      riskLevel: customer.riskLevel as RiskLevel,
      kycStatus: customer.kycStatus as KycStatus,
      profileData: customer.profileData as any,
      kycData: customer.kycData as ICustomerKycData,
      riskAssessment: customer.riskAssessment as any,
      documents: customer.documents as any,
      riskScores: customer.riskScores as any,
      alerts: customer.alerts as any,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
      // Simplified transformations with type assertions
    };
  }

  /**
   * Get all customers with pagination and filtering (superadmin only)
   * @param options Query options for filtering and pagination
   * @returns Paginated list of customers
   */
  async getAllCustomers(options: CustomerQueryOptions = {}): Promise<{
    customers: ICustomer[];
    pagination: Shared.DTOs.PaginationMeta;
  }> {
    const { page = 1, limit = 10, search, tenantId } = options;

    this.logger.log(`Getting all customers with options: ${JSON.stringify(options)}`);

    // Calculate pagination
    const skip = (page - 1) * limit;

    try {
      let customers: Customer[];
      let total: number;

      if (search) {
        // Use search functionality from repository
        customers = await this.customerRepository.searchCustomers(search, tenantId);
        total = customers.length; // For search, we'll use the actual results count

        // Apply pagination to search results
        const startIndex = skip;
        const endIndex = startIndex + limit;
        customers = customers.slice(startIndex, endIndex);
      } else if (tenantId) {
        // Get customers by tenant with pagination
        customers = await this.customerRepository.findByTenant(tenantId, { limit, offset: skip });
        total = await this.customerRepository.countByTenant(tenantId);
      } else {
        // Get all customers with pagination (superadmin only)
        customers = await this.customerRepository.findMany();
        total = await this.customerRepository.count();

        // Apply pagination
        const startIndex = skip;
        const endIndex = startIndex + limit;
        customers = customers.slice(startIndex, endIndex);
      }

      // Transform to interface format
      const transformedCustomers = customers.map((customer) => ({
        id: customer.id,
        tenantId: customer.tenantId,
        customerType: customer.customerType as CustomerType,
        status: customer.status as CustomerStatus,
        riskLevel: customer.riskLevel as RiskLevel,
        kycStatus: customer.kycStatus as KycStatus,
        profileData: customer.profileData as any,
        kycData: customer.kycData as ICustomerKycData,
        riskAssessment: customer.riskAssessment as any,
        documents: [],
        riskScores: [],
        alerts: [],
        createdAt: customer.createdAt,
        updatedAt: customer.updatedAt,
      }));

      // Calculate pagination metadata
      const totalPages = Math.ceil(total / limit);

      this.logger.log(`Retrieved ${customers.length} customers out of ${total} total`);

      return {
        customers: transformedCustomers,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          offset: skip,
          hasMore: page < totalPages,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get all customers: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get customer by ID
   * @param id Customer ID
   * @returns Customer details
   */
  async getCustomerById(id: string): Promise<ICustomer | null> {
    this.logger.log(`Getting customer by ID: ${id}`);

    try {
      const customer = await this.customerRepository.findByIdWithIncludes(id);

      if (!customer) {
        throw new NotFoundException(`Customer with ID ${id} not found`);
      }

      this.logger.log(`Retrieved customer: ${id}`);
      return this.transformCustomerToInterface(customer);
    } catch (error) {
      this.logger.error(`Failed to get customer by ID ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get customer statistics
   * @returns Customer statistics
   */
  async getCustomerStats(): Promise<{
    totalCustomers: number;
    activeCustomers: number;
    pendingKyc: number;
    highRiskCustomers: number;
    recentCustomers: number;
    customersByType: Record<string, number>;
    customersByRiskLevel: Record<string, number>;
  }> {
    this.logger.log('Getting customer statistics');

    try {
      // Use repository to get statistics
      const stats = await this.customerRepository.getCustomerStatistics('');

      // Get additional specific counts
      const [activeCustomers, pendingKyc, highRiskCustomers, recentCustomers] = await Promise.all([
        this.customerRepository.findByStatus('ACTIVE').then((customers) => customers.length),
        this.customerRepository.findCustomersRequiringKycUpdate('').then((customers) => customers.length),
        this.customerRepository.findHighRiskCustomers('').then((customers) => customers.length),
        this.customerRepository.findRecentCustomers('', 30).then((customers) => customers.length),
      ]);

      const result = {
        totalCustomers: stats.total,
        activeCustomers,
        pendingKyc,
        highRiskCustomers,
        recentCustomers,
        customersByType: stats.byType,
        customersByRiskLevel: stats.byRiskLevel,
      };

      this.logger.log('Retrieved customer statistics');
      return result;
    } catch (error) {
      this.logger.error(`Failed to get customer statistics: ${error.message}`, error.stack);
      throw error;
    }
  }
}
