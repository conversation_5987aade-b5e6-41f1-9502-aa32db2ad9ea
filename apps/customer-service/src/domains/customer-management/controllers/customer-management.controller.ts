import { <PERSON>, Get, Logger, Param, ParseUUI<PERSON>ipe, Query, UseGuards, UseInterceptors } from '@nestjs/common';
import { JwtAuthGuard, PlatformRole, RequireRoles, ResponseFormatInterceptor, ResponseUtil, SnakeToCamelPipe, StandardApiResponse, Traced, ZodValidationPipe } from '@qeep/common';
import { CustomerQueryDto, CustomerQuerySchema, ICustomer, Shared } from '@qeep/contracts';
import { CustomerManagementService } from '../services/customer-management.service';
/**
 * Customer Management Controller
 *
 * Handles HTTP endpoints for customer management operations.
 * Provides superadmin-only access to customer data across all tenants.
 */
@Controller('customers')
@UseGuards(JwtAuthGuard)
@UseInterceptors(ResponseFormatInterceptor)
export class CustomerManagementController {
  private readonly logger = new Logger(CustomerManagementController.name);

  constructor(private readonly customerManagementService: CustomerManagementService) {}

  /**
   * Get all customers (superadmin only)
   * GET /api/v1/customers
   *
   * This endpoint allows superadmins to view all customers across all tenants
   * with advanced filtering, searching, and pagination capabilities.
   *
   * @param query Query parameters for filtering and pagination
   * @returns Paginated list of customers
   */
  @Get()
  @RequireRoles(PlatformRole.SUPER_ADMIN)
  @Traced('CustomerManagementController.getAllCustomers')
  async getAllCustomers(@Query(new SnakeToCamelPipe(), new ZodValidationPipe(CustomerQuerySchema)) query: CustomerQueryDto): Promise<
    StandardApiResponse<{
      customers: ICustomer[];
      pagination: Shared.DTOs.PaginationMeta;
    }>
  > {
    this.logger.log(`Get all customers request with query: ${JSON.stringify(query)}`);

    try {
      const result = await this.customerManagementService.getAllCustomers(query);

      return ResponseUtil.success(result, 'Customers retrieved successfully', 200);
    } catch (error) {
      this.logger.error(`Failed to get all customers: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get customer by ID (superadmin only)
   * GET /api/v1/customers/:id
   *
   * This endpoint allows superadmins to view detailed information
   * about a specific customer including all related data.
   *
   * @param id Customer UUID
   * @returns Customer details
   */
  @Get(':id')
  @RequireRoles(PlatformRole.SUPER_ADMIN)
  @Traced('CustomerManagementController.getCustomerById')
  async getCustomerById(@Param('id', ParseUUIDPipe) id: string): Promise<StandardApiResponse<ICustomer>> {
    this.logger.log(`Get customer by ID request: ${id}`);

    try {
      const customer = await this.customerManagementService.getCustomerById(id);

      this.logger.log(`Retrieved customer ${id} successfully`);

      return ResponseUtil.success(customer, 'Customer retrieved successfully', 200);
    } catch (error) {
      this.logger.error(`Failed to get customer ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get customer statistics (superadmin only)
   * GET /api/v1/customers/stats
   *
   * This endpoint provides comprehensive statistics about customers
   * across all tenants for superadmin dashboard and reporting.
   *
   * @returns Customer statistics
   */
  @Get('stats/overview')
  @RequireRoles(PlatformRole.SUPER_ADMIN)
  @Traced('CustomerManagementController.getCustomerStats')
  async getCustomerStats(): Promise<
    StandardApiResponse<{
      totalCustomers: number;
      activeCustomers: number;
      pendingKyc: number;
      highRiskCustomers: number;
      recentCustomers: number;
      customersByType: Record<string, number>;
      customersByRiskLevel: Record<string, number>;
    }>
  > {
    this.logger.log('Get customer statistics request');

    try {
      const stats = await this.customerManagementService.getCustomerStats();

      this.logger.log('Retrieved customer statistics successfully');

      return ResponseUtil.success(stats, 'Customer statistics retrieved successfully', 200);
    } catch (error) {
      this.logger.error(`Failed to get customer statistics: ${error.message}`, error.stack);
      throw error;
    }
  }
}
