import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigService, ProtoConfigService } from '@qeep/common';
import { CustomerPrismaService } from '../../database/prisma.service';
import { CustomerRepository } from '../../repositories';
import { CustomerManagementController } from './controllers/customer-management.controller';
import { CustomerManagementGrpcController } from './controllers/customer-management.grpc.controller';
import { CustomerManagementService } from './services/customer-management.service';

/**
 * Customer Management Module
 *
 * Handles core customer data management and CRUD operations for customer records
 * including customer registration, profile updates, data validation,
 * customer search and retrieval, and data integrity management.
 *
 * Key Responsibilities:
 * - Customer registration and profile creation for new customer onboarding
 * - Profile updates and maintenance to keep customer information current
 * - Data validation and integrity checks to ensure data quality and consistency
 * - Customer search and retrieval with advanced filtering and query capabilities
 * - Data integrity management including deduplication and consistency validation
 */
@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: 'USER_PACKAGE',
        imports: [],
        useFactory: (configService: ConfigService, protoConfigService: ProtoConfigService) => ({
          transport: Transport.GRPC,
          options: {
            package: 'user',
            protoPath: protoConfigService.getProtoPath('user', 'user.proto'),
            url: configService.getServiceUrl('user-service-grpc'),
            loader: protoConfigService.getLoaderOptions(),
            channelOptions: protoConfigService.getChannelOptions(),
          },
        }),
        inject: [ConfigService, ProtoConfigService],
      },
      {
        name: 'NOTIFICATION_PACKAGE',
        imports: [],
        useFactory: (configService: ConfigService, protoConfigService: ProtoConfigService) => ({
          transport: Transport.GRPC,
          options: {
            package: 'notification',
            protoPath: protoConfigService.getProtoPath('notification', 'notification.proto'),
            url: configService.getServiceUrl('notification-service-grpc'),
            loader: protoConfigService.getLoaderOptions(),
            channelOptions: protoConfigService.getChannelOptions(),
          },
        }),
        inject: [ConfigService, ProtoConfigService],
      },
    ]),
  ],
  controllers: [CustomerManagementController, CustomerManagementGrpcController],
  providers: [
    CustomerManagementService,
    CustomerPrismaService,
    CustomerRepository,
    {
      provide: ICustomerRepository,
      useClass: CustomerRepository,
    },
  ],
  exports: [CustomerManagementService, CustomerPrismaService, ICustomerRepository],
})
export class CustomerManagementModule {}
