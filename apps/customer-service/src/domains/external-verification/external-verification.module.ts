import { Module } from '@nestjs/common';
import { ClientsModule } from '@nestjs/microservices';

/**
 * External Verification Module
 *
 * Handles integration with external verification services and data sources
 * including third-party verification, data source integration, verification orchestration,
 * result aggregation, and compliance validation.
 *
 * Key Responsibilities:
 * - Third-party verification through external identity verification services
 * - Data source integration with government databases, credit bureaus, and watchlists
 * - Verification orchestration to coordinate multiple verification processes
 * - Result aggregation for comprehensive verification results from multiple sources
 * - Compliance validation to ensure verification meets regulatory requirements
 */
@Module({
  imports: [ClientsModule.registerAsync([])],
  controllers: [],
  providers: [],
  exports: [],
})
export class ExternalVerificationModule {}
