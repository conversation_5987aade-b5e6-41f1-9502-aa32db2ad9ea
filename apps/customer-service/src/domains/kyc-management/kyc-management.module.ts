import { Module } from '@nestjs/common';
import { ClientsModule } from '@nestjs/microservices';

/**
 * KYC Management Module
 *
 * Handles comprehensive Know Your Customer processes including identity verification,
 * background screening, source of funds verification, KYC workflow management,
 * and documentation management.
 *
 * Key Responsibilities:
 * - Identity verification using government-issued documents and biometric verification
 * - Background screening including criminal history, sanctions, and PEP screening
 * - Source of funds verification for income sources and wealth origins
 * - KYC workflow orchestration from application to approval
 * - Complete KYC documentation management and verification results
 */
@Module({
  imports: [ClientsModule.registerAsync([])],
  controllers: [],
  providers: [],
  exports: [],
})
export class KycManagementModule {}
