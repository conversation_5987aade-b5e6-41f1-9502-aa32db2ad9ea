import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigService, ProtoConfigService } from '@qeep/common';

/**
 * Customer Lifecycle Module
 *
 * Handles complete management of customer relationships from onboarding through offboarding
 * including customer onboarding, profile management, relationship tracking,
 * periodic reviews, and customer offboarding.
 *
 * Key Responsibilities:
 * - Streamlined customer onboarding processes for acquiring new customer relationships
 * - Profile management to maintain accurate and current customer information
 * - Relationship tracking for customer history, interactions, and lifecycle events
 * - Periodic reviews of customer information and risk assessments
 * - Proper customer offboarding procedures and record maintenance
 */
@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: 'USER_PACKAGE',
        imports: [],
        useFactory: (configService: ConfigService, protoConfigService: ProtoConfigService) => ({
          transport: Transport.GRPC,
          options: {
            package: 'user',
            protoPath: protoConfigService.getProtoPath('user', 'user.proto'),
            url: configService.getServiceUrl('user-service-grpc'),
            loader: protoConfigService.getLoaderOptions(),
            channelOptions: protoConfigService.getChannelOptions(),
          },
        }),
        inject: [ConfigService, ProtoConfigService],
      },
      {
        name: 'NOTIFICATION_PACKAGE',
        imports: [],
        useFactory: (configService: ConfigService, protoConfigService: ProtoConfigService) => ({
          transport: Transport.GRPC,
          options: {
            package: 'notification',
            protoPath: protoConfigService.getProtoPath('notification', 'notification.proto'),
            url: configService.getServiceUrl('notification-service-grpc'),
            loader: protoConfigService.getLoaderOptions(),
            channelOptions: protoConfigService.getChannelOptions(),
          },
        }),
        inject: [ConfigService, ProtoConfigService],
      },
      // {
      //   name: 'AUDIT_PACKAGE',
      //   imports: [],
      //   useFactory: (configService: ConfigService, protoConfigService: ProtoConfigService) => ({
      //     transport: Transport.GRPC,
      //     options: {
      //       package: 'audit',
      //       protoPath: protoConfigService.getProtoPath('audit', 'audit.proto'),
      //       url: configService.getServiceUrl('audit-service-grpc'),
      //       loader: protoConfigService.getLoaderOptions(),
      //       channelOptions: protoConfigService.getChannelOptions(),
      //     },
      //   }),
      //   inject: [ConfigService, ProtoConfigService],
      // },
    ]),
  ],
  controllers: [],
  providers: [],
  exports: [],
})
export class CustomerLifecycleModule {}
