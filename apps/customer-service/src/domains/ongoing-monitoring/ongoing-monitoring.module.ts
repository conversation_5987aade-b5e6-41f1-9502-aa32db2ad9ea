import { Module } from '@nestjs/common';
import { ClientsModule } from '@nestjs/microservices';

/**
 * Ongoing Monitoring Module
 *
 * Handles continuous monitoring of customer profiles and risk factors for changes and updates
 * including profile change detection, risk trigger monitoring, regulatory updates,
 * automated reviews, and exception management.
 *
 * Key Responsibilities:
 * - Profile change detection to identify and process customer information updates
 * - Risk trigger monitoring for events requiring risk reassessment or enhanced monitoring
 * - Regulatory updates adaptation for changing regulatory requirements
 * - Automated reviews through intelligent systems for ongoing customer monitoring
 * - Exception management for unusual situations in customer monitoring processes
 */
@Module({
  imports: [ClientsModule.registerAsync([])],
  controllers: [],
  providers: [],
  exports: [],
})
export class OngoingMonitoringModule {}
