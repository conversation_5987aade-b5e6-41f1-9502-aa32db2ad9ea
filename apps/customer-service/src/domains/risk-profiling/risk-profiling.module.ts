import { Module } from '@nestjs/common';
import { ClientsModule } from '@nestjs/microservices';

/**
 * Risk Profiling Module
 *
 * Handles comprehensive customer risk assessment and ongoing risk management
 * including initial risk assessment, risk categorization, risk factor analysis,
 * enhanced due diligence, and risk mitigation controls.
 *
 * Key Responsibilities:
 * - Initial risk assessment with baseline risk scores for new customers
 * - Risk categorization into appropriate levels (low, medium, high) with controls
 * - Risk factor analysis covering geographic, occupational, transactional, and behavioral factors
 * - Enhanced due diligence implementation for high-risk customers
 * - Risk mitigation controls and monitoring based on customer risk levels
 */
@Module({
  imports: [ClientsModule.registerAsync([])],
  controllers: [],
  providers: [],
  exports: [],
})
export class RiskProfilingModule {}
