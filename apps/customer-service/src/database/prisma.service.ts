import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/customer-client';
import { BasePrismaService } from '@qeep/common';

@Injectable()
export class CustomerPrismaService extends BasePrismaService {
  protected prismaClient: PrismaClient;

  constructor() {
    super();
    this.prismaClient = new PrismaClient({
      log: ['error'],
      errorFormat: 'colorless',
    });
  }

  // Expose Prisma client methods for customer management
  get customer() {
    return this.prismaClient.customer;
  }

  get account() {
    return this.prismaClient.account;
  }

  get customerDocument() {
    return this.prismaClient.customerDocument;
  }

  get customerRiskScore() {
    return this.prismaClient.customerRiskScore;
  }

  get customerRelationship() {
    return this.prismaClient.customerRelationship;
  }

  get customerAlert() {
    return this.prismaClient.customerAlert;
  }

  get customerNote() {
    return this.prismaClient.customerNote;
  }

  // Expose raw query methods
  get $queryRaw() {
    return this.prismaClient.$queryRaw.bind(this.prismaClient);
  }

  get $transaction() {
    return this.prismaClient.$transaction.bind(this.prismaClient);
  }
}
