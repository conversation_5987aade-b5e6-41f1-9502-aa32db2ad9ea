/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import { Logger, VersioningType } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { CaseTransformValidationPipe, ConfigService, ProtoConfigService } from '@qeep/common';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  const protoConfigService = app.get(ProtoConfigService);

  // Enable global validation with case transformation
  app.useGlobalPipes(new CaseTransformValidationPipe());

  // Global prefix for all routes
  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);

  // Enable API versioning
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
    prefix: 'v',
  });

  // Set up gRPC microservice
  const grpcPort = configService.getServicePort('customer-service-grpc');
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.GRPC,
    options: {
      package: 'customer',
      protoPath: protoConfigService.getProtoPath('customer', 'customer.proto'),
      url: `0.0.0.0:${grpcPort}`,
      loader: protoConfigService.getLoaderOptions(),
    },
  });

  // Start all microservices
  await app.startAllMicroservices();

  const port = configService.getServicePort('customer-service');
  const host = configService.getServiceHost('customer-service');

  await app.listen(port);
  Logger.log(`🚀 HTTP Application is running on: http://${host}:${port}/${globalPrefix}`);
  Logger.log(`🚀 gRPC Application is running on: ${host}:${grpcPort}`);
}

bootstrap();
