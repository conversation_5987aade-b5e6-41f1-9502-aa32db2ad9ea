import { Module } from '@nestjs/common';
import { CommonModule } from '@qeep/common';
import { AccountsModule } from './domains/accounts/accounts.module';
import { CustomerAnalyticsModule } from './domains/customer-analytics/customer-analytics.module';
import { CustomerLifecycleModule } from './domains/customer-lifecycle/customer-lifecycle.module';
import { CustomerManagementModule } from './domains/customer-management/customer-management.module';
import { ExternalVerificationModule } from './domains/external-verification/external-verification.module';
import { KycManagementModule } from './domains/kyc-management/kyc-management.module';
import { OngoingMonitoringModule } from './domains/ongoing-monitoring/ongoing-monitoring.module';
import { RiskProfilingModule } from './domains/risk-profiling/risk-profiling.module';

@Module({
  imports: [
    CommonModule.forRoot({
      enableTelemetry: false,
      enableGlobalTenantInterceptor: false,
      enableCircuitBreaker: false,
      enableRateLimiting: false,
      enableSecurityHeaders: false,
    }),
    // Domain Modules
    AccountsModule,
    KycManagementModule,
    RiskProfilingModule,
    CustomerLifecycleModule,
    CustomerManagementModule,
    OngoingMonitoringModule,
    ExternalVerificationModule,
    CustomerAnalyticsModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
