generator client {
  provider = "prisma-client-js"
  output   = "../../../node_modules/.prisma/notification-client"
}

datasource db {
  provider = "postgresql"
  url      = env("NOTIFICATION_DATABASE_URL")
}

// ============================================================================
// NOTIFICATION TEMPLATES
// ============================================================================

enum TemplateType {
  EMAIL
  SMS
  IN_APP
  PUSH
}

enum TemplateCategory {
  WELCOME
  VERIFICATION
  PASSWORD_RESET
  NOTIFICATION
  MARKETING
  TRANSACTIONAL
  SYSTEM
}

enum TemplateStatus {
  ACTIVE
  INACTIVE
  DRAFT
  ARCHIVED
}

model NotificationTemplate {
  id          String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  slug        String         @unique @db.VarChar(255)
  name        String         @db.VarChar(255)
  description String?        @db.Text
  type        TemplateType   @default(EMAIL)
  category    TemplateCategory @default(NOTIFICATION)
  status      TemplateStatus @default(DRAFT)
  
  // Content fields
  subject     String?        @db.VarChar(500) // For email templates
  htmlContent String?        @map("html_content") @db.Text
  textContent String?        @map("text_content") @db.Text
  
  // Template configuration
  variables   Json?          @default("{}")  // Template variables and their types
  metadata    Json?          @default("{}")  // Additional metadata
  
  // Header/Footer relationships
  headerTemplateId String?   @map("header_template_id") @db.Uuid
  footerTemplateId String?   @map("footer_template_id") @db.Uuid
  headerTemplate   NotificationTemplate? @relation("HeaderTemplate", fields: [headerTemplateId], references: [id])
  footerTemplate   NotificationTemplate? @relation("FooterTemplate", fields: [footerTemplateId], references: [id])
  
  // Self-referencing relations for header/footer
  headerUsages     NotificationTemplate[] @relation("HeaderTemplate")
  footerUsages     NotificationTemplate[] @relation("FooterTemplate")
  
  // Multi-tenant support
  tenantId    String?        @map("tenant_id") @db.VarChar(255)
  
  // Audit fields
  createdBy   String?        @map("created_by") @db.VarChar(255)
  updatedBy   String?        @map("updated_by") @db.VarChar(255)
  version     Int            @default(1)
  
  createdAt   DateTime       @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt   DateTime       @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  
  // Relations
  notificationLogs NotificationLog[]
  
  @@index([type, category, status], map: "idx_templates_type_category_status")
  @@index([slug], map: "idx_templates_slug")
  @@index([tenantId], map: "idx_templates_tenant_id")
  @@map("notification_templates")
}

// ============================================================================
// NOTIFICATION LOGS
// ============================================================================

enum NotificationStatus {
  PENDING
  SENT
  DELIVERED
  FAILED
  BOUNCED
  CANCELLED

}

enum NotificationChannel {
  EMAIL
  SMS
  IN_APP
  PUSH

}

model NotificationLog {
  id               String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  
  // Template reference
  templateId       String              @map("template_id") @db.Uuid
  template         NotificationTemplate @relation(fields: [templateId], references: [id])
  
  // Notification details
  channel          NotificationChannel
  recipientId      String              @map("recipient_id") @db.VarChar(255)
  recipientAddress String              @map("recipient_address") @db.VarChar(500)
  subject          String?             @db.VarChar(500)
  content          String?             @db.Text
  
  // Status tracking
  status           NotificationStatus  @default(PENDING)
  provider         String?             @db.VarChar(255)
  externalMessageId String?            @map("external_message_id") @db.VarChar(255)
  
  // Template data
  variables        Json?               @default("{}")
  metadata         Json?               @default("{}")
  
  // Error handling
  errorMessage     String?             @map("error_message") @db.Text
  deliveryDetails  Json?               @map("delivery_details") @default("{}")
  
  // Timing
  sentAt           DateTime?           @map("sent_at") @db.Timestamptz(6)
  deliveredAt      DateTime?           @map("delivered_at") @db.Timestamptz(6)
  failedAt         DateTime?           @map("failed_at") @db.Timestamptz(6)
  
  // Retry logic
  retryCount       Int                 @default(0) @map("retry_count")
  nextRetryAt      DateTime?           @map("next_retry_at") @db.Timestamptz(6)
  
  // Multi-tenant support
  tenantId         String?             @map("tenant_id") @db.VarChar(255)
  triggeredBy      String?             @map("triggered_by") @db.VarChar(255)
  
  createdAt        DateTime            @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt        DateTime            @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  
  @@index([status, channel], map: "idx_logs_status_channel")
  @@index([recipientId, channel], map: "idx_logs_recipient_channel")
  @@index([templateId], map: "idx_logs_template_id")
  @@index([externalMessageId], map: "idx_logs_external_message_id")
  @@index([tenantId], map: "idx_logs_tenant_id")
  @@index([createdAt], map: "idx_logs_created_at")
  @@map("notification_logs")
}

// ============================================================================
// PROVIDER CONFIGURATIONS
// ============================================================================

enum ProviderType {
  EMAIL
  SMS
  PUSH

}

enum ProviderStatus {
  ACTIVE
  INACTIVE
  MAINTENANCE

}

model NotificationProvider {
  id           String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name         String         @db.VarChar(100)
  type         ProviderType
  status       ProviderStatus @default(ACTIVE)
  
  // Configuration
  config       Json           @default("{}")  // Provider-specific configuration
  credentials  Json           @default("{}")  // Encrypted credentials
  
  // Settings
  isDefault    Boolean        @default(false) @map("is_default")
  priority     Int            @default(1)     // For fallback ordering
  
  // Rate limiting
  rateLimit    Int?           @map("rate_limit")    // Requests per minute
  dailyLimit   Int?           @map("daily_limit")   // Daily send limit
  
  // Multi-tenant support
  tenantId     String?        @map("tenant_id") @db.VarChar(255)
  
  createdAt    DateTime       @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt    DateTime       @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  
  @@index([type, status], map: "idx_providers_type_status")
  @@index([tenantId], map: "idx_providers_tenant_id")
  @@map("notification_providers")
}
