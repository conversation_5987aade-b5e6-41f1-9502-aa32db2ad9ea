import { PrismaClient, TemplateCategory, TemplateStatus, TemplateType } from '../../../node_modules/.prisma/notification-client/index.js';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding notification templates...');

  // Create shared header template
  const headerTemplate = await prisma.notificationTemplate.upsert({
    where: { slug: 'email-header' },
    update: {},
    create: {
      slug: 'email-header',
      name: '<PERSON><PERSON> Header',
      description: 'Shared email header template',
      type: TemplateType.EMAIL,
      category: TemplateCategory.SYSTEM,
      status: TemplateStatus.ACTIVE,
      htmlContent: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>{{subject}}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #2563eb; color: white; padding: 20px; text-align: center; }
            .logo { font-size: 24px; font-weight: bold; }
            .content { padding: 30px 20px; background-color: #ffffff; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="logo">Qeep</div>
            </div>
            <div class="content">
      `,
      textContent: `
        =====================================
        Qeep
        =====================================
        
      `,
      variables: {},
      version: 1,
    },
  });

  // Create shared footer template
  const footerTemplate = await prisma.notificationTemplate.upsert({
    where: { slug: 'email-footer' },
    update: {},
    create: {
      slug: 'email-footer',
      name: 'Email Footer',
      description: 'Shared email footer template',
      type: TemplateType.EMAIL,
      category: TemplateCategory.SYSTEM,
      status: TemplateStatus.ACTIVE,
      htmlContent: `
            </div>
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #6b7280;">
              <p>© {{currentYear}} Qeep. All rights reserved.</p>
              <p>
                <a href="{{appUrl}}" style="color: #2563eb; text-decoration: none;">Visit Qeep</a> | 
                <a href="mailto:{{supportEmail}}" style="color: #2563eb; text-decoration: none;">Contact Support</a>
              </p>
              <p style="margin-top: 15px; font-size: 11px;">
                This email was sent to {{userEmail}}. If you no longer wish to receive these emails, 
                <a href="{{unsubscribeUrl}}" style="color: #6b7280;">unsubscribe here</a>.
              </p>
            </div>
          </div>
        </body>
        </html>
      `,
      textContent: `
        
        =====================================
        © {{currentYear}} Qeep. All rights reserved.
        
        Visit Qeep: {{appUrl}}
        Contact Support: {{supportEmail}}
        
        This email was sent to {{userEmail}}.
        =====================================
      `,
      variables: {
        currentYear: 'number',
        appUrl: 'string',
        supportEmail: 'string',
        userEmail: 'string',
        unsubscribeUrl: 'string',
      },
      version: 1,
    },
  });

  // Create welcome email template
  await prisma.notificationTemplate.upsert({
    where: { slug: 'welcome-email' },
    update: {},
    create: {
      slug: 'welcome-email',
      name: 'Welcome Email',
      description: 'Welcome email sent to new users',
      type: TemplateType.EMAIL,
      category: TemplateCategory.WELCOME,
      status: TemplateStatus.ACTIVE,
      subject: 'Welcome to {{organizationName}} - Get Started with Qeep',
      htmlContent: `
        <h1>Welcome to {{organizationName}}, {{userName}}! 🎉</h1>

        <p>Congratulations on successfully verifying your email address! We're excited to have you join the Qeep community. Your account is now active and you're ready to start managing your alerts and notifications.</p>

        <div style="background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #1e40af;">Next Steps for Account Setup</h3>
          <ul style="margin-bottom: 0;">
            <li><strong>Create Your Organization:</strong> <a href="{{organizationSetupUrl}}" style="color: #2563eb;">Set up your organization</a> to start collaborating with your team</li>
            <li><strong>Complete Your Profile:</strong> <a href="{{profileSetupUrl}}" style="color: #2563eb;">Finish your profile setup</a> with additional details</li>
            <li><strong>Configure Alert Channels:</strong> Set up your first notification channels and alert preferences</li>
            <li><strong>Explore Features:</strong> Discover our dashboard and powerful alerting capabilities</li>
            <li><strong>Invite Team Members:</strong> Add your colleagues to start collaborating</li>
          </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <a href="{{loginUrl}}" style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold; margin-right: 10px;">
            Access Your Dashboard
          </a>
          <a href="{{documentationUrl}}" style="background-color: #6b7280; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
            View Documentation
          </a>
        </div>

        <div style="background-color: #f9fafb; padding: 15px; border-radius: 6px; margin: 20px 0;">
          <h4 style="margin-top: 0; color: #374151;">Need Help Getting Started?</h4>
          <p style="margin-bottom: 0;">Check out our <a href="{{documentationUrl}}" style="color: #2563eb;">getting started guide</a> or contact our support team at <a href="mailto:{{supportEmail}}" style="color: #2563eb;">{{supportEmail}}</a>.</p>
        </div>

        <p>Welcome aboard!</p>
        <p><strong>The Qeep Team</strong></p>
      `,
      textContent: `
        Welcome to {{organizationName}}, {{userName}}!

        Congratulations on successfully verifying your email address! We're excited to have you join the Qeep community. Your account is now active and you're ready to start managing your alerts and notifications.

        Next Steps for Account Setup:
        - Create Your Organization: {{organizationSetupUrl}}
        - Complete Your Profile: {{profileSetupUrl}}
        - Configure Alert Channels: Set up your first notification channels
        - Explore Features: Discover our dashboard and alerting capabilities
        - Invite Team Members: Add colleagues to start collaborating

        Access Your Dashboard: {{loginUrl}}
        View Documentation: {{documentationUrl}}

        Need Help Getting Started?
        Check out our getting started guide: {{documentationUrl}}
        Or contact our support team: {{supportEmail}}

        Welcome aboard!
        The Qeep Team
      `,
      headerTemplateId: headerTemplate.id,
      footerTemplateId: footerTemplate.id,
      variables: {
        userName: 'string',
        userEmail: 'string',
        organizationName: 'string',
        loginUrl: 'string',
        organizationSetupUrl: 'string',
        profileSetupUrl: 'string',
        documentationUrl: 'string',
        supportEmail: 'string',
        currentYear: 'string',
      },
      version: 1,
    },
  });

  // Create password reset email template
  await prisma.notificationTemplate.upsert({
    where: { slug: 'password-reset-email' },
    update: {},
    create: {
      slug: 'password-reset-email',
      name: 'Password Reset Email',
      description: 'Password reset email template',
      type: TemplateType.EMAIL,
      category: TemplateCategory.PASSWORD_RESET,
      status: TemplateStatus.ACTIVE,
      subject: 'Reset Your Qeep Password',
      htmlContent: `
        <h1>Password Reset Request</h1>
        
        <p>We received a request to reset your Qeep password. If you didn't make this request, you can safely ignore this email.</p>
        
        <div style="background-color: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;">
          <p style="margin: 0;"><strong>Security Notice:</strong> This reset link will expire in {{expiryHours}} hours for your security.</p>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="{{resetUrl}}" style="background-color: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
            Reset Your Password
          </a>
        </div>
        
        <p>If the button above doesn't work, copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #6b7280; font-size: 14px;">{{resetUrl}}</p>
        
        <p>If you continue to have problems, please contact our support team at <a href="mailto:{{supportEmail}}">{{supportEmail}}</a>.</p>
      `,
      textContent: `
        Password Reset Request
        
        We received a request to reset your Qeep password. If you didn't make this request, you can safely ignore this email.
        
        Security Notice: This reset link will expire in {{expiryHours}} hours for your security.
        
        Reset your password: {{resetUrl}}
        
        If you continue to have problems, please contact our support team at {{supportEmail}}.
      `,
      headerTemplateId: headerTemplate.id,
      footerTemplateId: footerTemplate.id,
      variables: {
        resetUrl: 'string',
        resetToken: 'string',
        expiryHours: 'number',
        supportEmail: 'string',
      },
      version: 1,
    },
  });

  // Create email verification template
  await prisma.notificationTemplate.upsert({
    where: { slug: 'email-verification' },
    update: {},
    create: {
      slug: 'email-verification',
      name: 'Email Verification',
      description: 'Email verification template for new user registration',
      type: TemplateType.EMAIL,
      category: TemplateCategory.VERIFICATION,
      status: TemplateStatus.ACTIVE,
      subject: 'Verify Your Email Address - Welcome to Qeep',
      htmlContent: `
        <h1>Welcome to Qeep! 🎉</h1>

        <p>Thank you for signing up! To complete your registration and start using Qeep, please verify your email address.</p>

        <div style="background-color: #ecfdf5; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981;">
          <p style="margin: 0;"><strong>Action Required:</strong> Please verify your email within {{expiryHours}} hours.</p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <a href="{{verificationUrl}}" style="background-color: #16a34a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
            Verify Email Address
          </a>
        </div>

        <p>If the button above doesn't work, copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #6b7280; font-size: 14px;">{{verificationUrl}}</p>

        <p>If you didn't create an account with us, please ignore this email or contact our support team at <a href="mailto:{{supportEmail}}">{{supportEmail}}</a>.</p>
      `,
      textContent: `
        Welcome to Qeep!

        Thank you for signing up! To complete your registration and start using Qeep, please verify your email address.

        Action Required: Please verify your email within {{expiryHours}} hours.

        Verify your email: {{verificationUrl}}

        If you didn't create an account with us, please ignore this email or contact our support team at {{supportEmail}}.
      `,
      headerTemplateId: headerTemplate.id,
      footerTemplateId: footerTemplate.id,
      variables: {
        verificationUrl: 'string',
        verificationToken: 'string',
        expiryHours: 'number',
        supportEmail: 'string',
      },
      version: 1,
    },
  });

  // Create tenant invitation template
  await prisma.notificationTemplate.upsert({
    where: { slug: 'tenant-invitation' },
    update: {},
    create: {
      slug: 'tenant-invitation',
      name: 'Tenant Invitation',
      description: 'Invitation email for users to join a tenant organization',
      type: TemplateType.EMAIL,
      category: TemplateCategory.NOTIFICATION,
      status: TemplateStatus.ACTIVE,
      subject: "You're invited to join {{organizationName}} on Qeep",
      htmlContent: `
        <h1>You're Invited! 🎉</h1>

        <p><strong>{{inviterName}}</strong> has invited you to join <strong>{{organizationName}}</strong> on Qeep.</p>

        <p>Qeep helps teams manage alerts, notifications, and stay connected. Join {{organizationName}} to start collaborating with your team.</p>

        <div style="background-color: #eff6ff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3b82f6;">
          <p style="margin: 0;"><strong>Invitation Details:</strong></p>
          <p style="margin: 5px 0;">Organization: {{organizationName}}</p>
          <p style="margin: 5px 0;">Invited by: {{inviterName}}</p>
          <p style="margin: 5px 0;">Expires in: {{expiryDays}} days</p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <a href="{{invitationUrl}}" style="background-color: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
            Accept Invitation
          </a>
        </div>

        <p>If the button above doesn't work, copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #6b7280; font-size: 14px;">{{invitationUrl}}</p>

        <p>If you have any questions or need help getting started, please contact our support team at <a href="mailto:{{supportEmail}}">{{supportEmail}}</a>.</p>

        <p style="font-size: 14px; color: #6b7280; margin-top: 30px;">
          If you weren't expecting this invitation, you can safely ignore this email.
        </p>
      `,
      textContent: `
        You're Invited!

        {{inviterName}} has invited you to join {{organizationName}} on Qeep.

        Qeep helps teams manage alerts, notifications, and stay connected. Join {{organizationName}} to start collaborating with your team.

        Invitation Details:
        - Organization: {{organizationName}}
        - Invited by: {{inviterName}}
        - Expires in: {{expiryDays}} days

        Accept your invitation: {{invitationUrl}}

        If you have any questions or need help getting started, please contact our support team at {{supportEmail}}.

        If you weren't expecting this invitation, you can safely ignore this email.
      `,
      headerTemplateId: headerTemplate.id,
      footerTemplateId: footerTemplate.id,
      variables: {
        inviterName: 'string',
        organizationName: 'string',
        invitationUrl: 'string',
        invitationToken: 'string',
        expiryDays: 'number',
        supportEmail: 'string',
      },
      version: 1,
    },
  });

  console.log('✅ Notification templates seeded successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding notification templates:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
