export interface SMSOptions {
  to: string;
  from?: string;
  message: string;
  mediaUrls?: string[];
  metadata?: Record<string, any>;
}

export interface SMSDeliveryResult {
  success: boolean;
  messageId?: string;
  providerId?: string;
  error?: string;
  deliveryStatus?: 'sent' | 'delivered' | 'failed' | 'pending' | 'undelivered';
  timestamp: Date;
  cost?: number;
  metadata?: Record<string, any>;
}

export interface SMSProviderConfig {
  accountSid?: string;
  authToken?: string;
  apiKey?: string;
  fromNumber?: string;
  webhookSecret?: string;
  baseUrl?: string;
  timeout?: number;
  retryAttempts?: number;
}

export abstract class ISMSProvider {
  protected config: SMSProviderConfig;
  
  constructor(config: SMSProviderConfig) {
    this.config = config;
  }

  abstract sendSMS(options: SMSOptions): Promise<SMSDeliveryResult>;
  abstract verifyConfiguration(): Promise<boolean>;
  abstract getProviderName(): string;
  abstract handleWebhook?(payload: any, signature?: string): Promise<SMSDeliveryResult>;
  abstract getDeliveryStatus?(messageId: string): Promise<SMSDeliveryResult>;
}

export enum SMSProvider {
  TWILIO = 'twilio',
  AWS_SNS = 'aws_sns',
  VONAGE = 'vonage',
  MESSAGEBIRD = 'messagebird'
}
