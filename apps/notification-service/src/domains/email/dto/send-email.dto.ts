import { IsString, IsE<PERSON>, IsOptional, IsArray, ValidateNested, IsObject, IsEnum } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { EmailProvider } from '../providers/email-provider.interface';

export class EmailAttachmentDto {
  @IsString()
  filename: string;

  @IsString()
  content: string; // Base64 encoded content

  @IsOptional()
  @IsString()
  contentType?: string;
}

export class EmailAddressDto {
  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  name?: string;
}

export class SendEmailDto {
  @IsString()
  templateSlug: string;

  @ValidateNested()
  @Type(() => EmailAddressDto)
  to: EmailAddressDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => EmailAddressDto)
  from?: EmailAddressDto;

  @IsOptional()
  @IsObject()
  variables?: Record<string, any>;

  @IsOptional()
  @IsString()
  tenantId?: string;

  @IsOptional()
  @IsEnum(EmailProvider)
  provider?: EmailProvider;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EmailAttachmentDto)
  attachments?: EmailAttachmentDto[];

  @IsOptional()
  @ValidateNested()
  @Type(() => EmailAddressDto)
  replyTo?: EmailAddressDto;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EmailAddressDto)
  cc?: EmailAddressDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EmailAddressDto)
  bcc?: EmailAddressDto[];

  @IsOptional()
  @IsObject()
  headers?: Record<string, string>;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class SendBulkEmailDto {
  @IsString()
  templateSlug: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EmailAddressDto)
  recipients: EmailAddressDto[];

  @IsOptional()
  @ValidateNested()
  @Type(() => EmailAddressDto)
  from?: EmailAddressDto;

  @IsOptional()
  @IsObject()
  variables?: Record<string, any>;

  @IsOptional()
  @IsString()
  tenantId?: string;

  @IsOptional()
  @IsEnum(EmailProvider)
  provider?: EmailProvider;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
