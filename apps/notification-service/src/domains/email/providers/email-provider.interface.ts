export interface EmailAttachment {
  filename: string;
  content: Buffer | string;
  contentType?: string;
  encoding?: string;
}

export interface EmailAddress {
  email: string;
  name?: string;
}

export interface EmailOptions {
  to: EmailAddress | EmailAddress[];
  from: EmailAddress;
  subject: string;
  html?: string;
  text?: string;
  attachments?: EmailAttachment[];
  replyTo?: EmailAddress;
  cc?: EmailAddress | EmailAddress[];
  bcc?: EmailAddress | EmailAddress[];
  headers?: Record<string, string>;
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface EmailDeliveryResult {
  success: boolean;
  messageId?: string;
  providerId?: string;
  error?: string;
  deliveryStatus?: 'sent' | 'delivered' | 'bounced' | 'failed' | 'pending';
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface EmailProviderConfig {
  apiKey: string;
  fromEmail?: string;
  fromName?: string;
  webhookSecret?: string;
  baseUrl?: string;
  timeout?: number;
  retryAttempts?: number;
}

export abstract class IEmailProvider {
  protected config: EmailProviderConfig;

  constructor(config: EmailProviderConfig) {
    this.config = config;
  }

  abstract sendEmail(options: EmailOptions): Promise<EmailDeliveryResult>;
  abstract verifyConfiguration(): Promise<boolean>;
  abstract getProviderName(): string;
  abstract handleWebhook?(payload: any, signature?: string): Promise<EmailDeliveryResult>;
  abstract getDeliveryStatus?(messageId: string): Promise<EmailDeliveryResult>;
}

export enum EmailProvider {
  RESEND = 'resend',
  SENDGRID = 'sendgrid',
  MAILGUN = 'mailgun',
  SES = 'ses',
  SMTP = 'smtp',
}
