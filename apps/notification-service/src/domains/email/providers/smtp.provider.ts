import { Injectable, Logger } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>ddress, EmailDeliveryResult, EmailOptions, EmailProviderConfig, IEmailProvider } from './email-provider.interface';
import * as nodemailer from 'nodemailer';
import { Transporter } from 'nodemailer';

export interface SMTPProviderConfig extends EmailProviderConfig {
  host: string;
  port: number;
  secure: boolean;
  user?: string;
  password?: string;
  ignoreTLS?: boolean;
  requireTLS?: boolean;
}

@Injectable()
export class SMTPEmailProvider extends IEmailProvider {
  private readonly logger = new Logger(SMTPEmailProvider.name);
  private transporter: Transporter;
  private smtpConfig: SMTPProviderConfig;

  constructor(config: SMTPProviderConfig) {
    super(config);
    this.smtpConfig = config;
    this.initializeTransporter();
  }

  private initializeTransporter(): void {
    this.transporter = nodemailer.createTransport({
      host: this.smtpConfig.host,
      port: this.smtpConfig.port,
      secure: this.smtpConfig.secure,
      auth:
        this.smtpConfig.user && this.smtpConfig.password
          ? {
              user: this.smtpConfig.user,
              pass: this.smtpConfig.password,
            }
          : undefined,
      ignoreTLS: this.smtpConfig.ignoreTLS,
      requireTLS: this.smtpConfig.requireTLS,
      connectionTimeout: this.smtpConfig.timeout || 30000,
      greetingTimeout: this.smtpConfig.timeout || 30000,
      socketTimeout: this.smtpConfig.timeout || 30000,
    });
  }

  async sendEmail(options: EmailOptions): Promise<EmailDeliveryResult> {
    try {
      this.logger.log(`Sending email via SMTP to: ${this.formatRecipients(options.to)}`);

      const mailOptions = {
        from: this.formatEmailAddress(options.from),
        to: this.formatRecipients(options.to),
        cc: options.cc ? this.formatRecipients(options.cc) : undefined,
        bcc: options.bcc ? this.formatRecipients(options.bcc) : undefined,
        replyTo: options.replyTo ? this.formatEmailAddress(options.replyTo) : undefined,
        subject: options.subject,
        text: options.text,
        html: options.html,
        attachments: options.attachments?.map((attachment) => ({
          filename: attachment.filename,
          content: attachment.content,
          contentType: attachment.contentType,
          encoding: attachment.encoding,
        })),
        headers: options.headers,
      };

      const result = await this.transporter.sendMail(mailOptions);

      this.logger.log(`Email sent successfully via SMTP. Message ID: ${result.messageId}`);
      return {
        success: true,
        messageId: result.messageId,
        providerId: 'smtp',
        deliveryStatus: 'sent',
        timestamp: new Date(),
        metadata: {
          smtpResponse: result.response,
          envelope: result.envelope,
          accepted: result.accepted,
          rejected: result.rejected,
        },
      };
    } catch (error) {
      this.logger.error(`SMTP email provider error: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        timestamp: new Date(),
        metadata: {
          error: error.stack,
          smtpConfig: {
            host: this.smtpConfig.host,
            port: this.smtpConfig.port,
            secure: this.smtpConfig.secure,
          },
        },
      };
    }
  }

  async verifyConfiguration(): Promise<boolean> {
    try {
      this.logger.log('Verifying SMTP configuration...');
      await this.transporter.verify();
      this.logger.log('SMTP configuration verified successfully');
      return true;
    } catch (error) {
      this.logger.error(`SMTP configuration verification failed: ${error.message}`);
      return false;
    }
  }

  getProviderName(): string {
    return 'smtp';
  }

  async handleWebhook?(payload: any, signature?: string): Promise<EmailDeliveryResult> {
    // SMTP doesn't typically support webhooks, but this can be implemented
    // if using a service like Mailpit that provides webhook functionality
    this.logger.warn('SMTP provider does not support webhooks');
    throw new Error('SMTP provider does not support webhooks');
  }

  async getDeliveryStatus?(messageId: string): Promise<EmailDeliveryResult> {
    // SMTP doesn't provide delivery status tracking by default
    // This would need to be implemented with additional services
    this.logger.warn('SMTP provider does not support delivery status tracking');
    throw new Error('SMTP provider does not support delivery status tracking');
  }

  private formatEmailAddress(address: EmailAddress): string {
    return address.name ? `"${address.name}" <${address.email}>` : address.email;
  }

  private formatRecipients(recipients: EmailAddress | EmailAddress[]): string {
    if (Array.isArray(recipients)) {
      return recipients.map((recipient) => this.formatEmailAddress(recipient)).join(', ');
    }
    return this.formatEmailAddress(recipients);
  }

  // Additional SMTP-specific methods
  async testConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      return true;
    } catch (error) {
      this.logger.error(`SMTP connection test failed: ${error.message}`);
      return false;
    }
  }

  async closeConnection(): Promise<void> {
    if (this.transporter) {
      this.transporter.close();
      this.logger.log('SMTP connection closed');
    }
  }

  getConnectionInfo(): any {
    return {
      host: this.smtpConfig.host,
      port: this.smtpConfig.port,
      secure: this.smtpConfig.secure,
      hasAuth: !!(this.smtpConfig.user && this.smtpConfig.password),
    };
  }
}
