import { Injectable, Logger } from '@nestjs/common';
import { EmailProvider, EmailProviderConfig, IEmailProvider } from './email-provider.interface';

import { ConfigService } from '@qeep/common';
import { ResendEmailProvider } from './resend.provider';
import { SMTPEmailProvider, SMTPProviderConfig } from './smtp.provider';

@Injectable()
export class EmailProviderFactory {
  private readonly logger = new Logger(EmailProviderFactory.name);
  private providers: Map<EmailProvider, IEmailProvider> = new Map();

  constructor(private configService: ConfigService) {}

  async createProvider(providerType?: EmailProvider): Promise<IEmailProvider> {
    const provider = providerType || this.getDefaultProvider();

    // Return cached provider if available
    if (this.providers.has(provider)) {
      return this.providers.get(provider);
    }

    const config = this.getProviderConfig(provider);
    let emailProvider: IEmailProvider;

    switch (provider) {
      case EmailProvider.RESEND:
        emailProvider = new ResendEmailProvider(config);
        break;
      case EmailProvider.SENDGRID:
        // emailProvider = new SendGridEmailProvider(config);
        throw new Error('SendGrid provider not implemented yet');
      case EmailProvider.MAILGUN:
        // emailProvider = new MailgunEmailProvider(config);
        throw new Error('Mailgun provider not implemented yet');
      case EmailProvider.SES:
        // emailProvider = new SESEmailProvider(config);
        throw new Error('AWS SES provider not implemented yet');
      case EmailProvider.SMTP:
        emailProvider = new SMTPEmailProvider(config as SMTPProviderConfig);
        break;
      default:
        throw new Error(`Unsupported email provider: ${provider}`);
    }

    // Verify provider configuration
    const isValid = await emailProvider.verifyConfiguration();
    if (!isValid) {
      throw new Error(`Email provider ${provider} configuration is invalid`);
    }

    // Cache the provider
    this.providers.set(provider, emailProvider);
    this.logger.log(`Email provider ${provider} initialized successfully`);

    return emailProvider;
  }

  async getAvailableProviders(): Promise<EmailProvider[]> {
    const providers: EmailProvider[] = [];

    for (const provider of Object.values(EmailProvider)) {
      try {
        const config = this.getProviderConfig(provider);
        if (config.apiKey) {
          providers.push(provider);
        }
      } catch (error) {
        // Provider not configured, skip
      }
    }

    return providers;
  }

  private getDefaultProvider(): EmailProvider {
    const emailConfig = this.configService.getEmailConfig();
    return emailConfig.defaultProvider as EmailProvider;
  }

  private getProviderConfig(provider: EmailProvider): EmailProviderConfig {
    const emailConfig = this.configService.getEmailConfig();

    switch (provider) {
      case EmailProvider.RESEND:
        return {
          apiKey: emailConfig.resend.apiKey,
          fromEmail: emailConfig.resend.fromEmail,
          fromName: emailConfig.resend.fromName,
          webhookSecret: emailConfig.resend.webhookSecret,
          timeout: emailConfig.timeout,
          retryAttempts: emailConfig.retryAttempts,
        };
      case EmailProvider.SENDGRID:
        return {
          apiKey: emailConfig.sendgrid.apiKey,
          fromEmail: emailConfig.sendgrid.fromEmail,
          fromName: emailConfig.sendgrid.fromName,
          webhookSecret: emailConfig.sendgrid.webhookSecret,
          timeout: emailConfig.timeout,
          retryAttempts: emailConfig.retryAttempts,
        };
      case EmailProvider.MAILGUN:
        return {
          apiKey: emailConfig.mailgun.apiKey,
          fromEmail: emailConfig.mailgun.fromEmail,
          fromName: emailConfig.mailgun.fromName,
          baseUrl: emailConfig.mailgun.baseUrl,
          webhookSecret: emailConfig.mailgun.webhookSecret,
          timeout: emailConfig.timeout,
          retryAttempts: emailConfig.retryAttempts,
        };
      case EmailProvider.SES:
        return {
          apiKey: emailConfig.ses.accessKeyId,
          fromEmail: emailConfig.ses.fromEmail,
          fromName: emailConfig.ses.fromName,
          baseUrl: emailConfig.ses.region,
          timeout: emailConfig.timeout,
          retryAttempts: emailConfig.retryAttempts,
        };
      case EmailProvider.SMTP:
        return {
          apiKey: '', // Not used for SMTP
          host: emailConfig.smtp.host,
          port: emailConfig.smtp.port,
          secure: emailConfig.smtp.secure,
          user: emailConfig.smtp.user,
          password: emailConfig.smtp.password,
          fromEmail: emailConfig.smtp.fromEmail,
          fromName: emailConfig.smtp.fromName,
          timeout: emailConfig.timeout,
          retryAttempts: emailConfig.retryAttempts,
          ignoreTLS: emailConfig.smtp.ignoreTLS,
          requireTLS: emailConfig.smtp.requireTLS,
        } as SMTPProviderConfig;
      default:
        throw new Error(`Configuration not found for provider: ${provider}`);
    }
  }

  clearCache(): void {
    this.providers.clear();
    this.logger.log('Email provider cache cleared');
  }
}
