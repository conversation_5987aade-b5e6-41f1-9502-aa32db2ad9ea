import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ail<PERSON>ddress, EmailDeliveryResult, EmailOptions, EmailProviderConfig, IEmailProvider } from './email-provider.interface';

import { Resend } from 'resend';

@Injectable()
export class ResendEmailProvider extends IEmailProvider {
  private readonly logger = new Logger(ResendEmailProvider.name);
  private resend: Resend;

  constructor(config: EmailProviderConfig) {
    super(config);
    this.resend = new Resend(config.apiKey);
  }

  async sendEmail(options: EmailOptions): Promise<EmailDeliveryResult> {
    try {
      this.logger.log(`Sending email via Resend to: ${this.formatRecipients(options.to)}`);

      const resendOptions = {
        from: this.formatEmailAddress(options.from),
        to: this.formatRecipients(options.to),
        subject: options.subject,
        html: options.html,
        text: options.text,
        attachments: options.attachments?.map((att) => ({
          filename: att.filename,
          content: att.content,
          content_type: att.contentType,
        })),
        reply_to: options.replyTo ? this.formatEmailAddress(options.replyTo) : undefined,
        cc: options.cc ? this.formatRecipients(options.cc) : undefined,
        bcc: options.bcc ? this.formatRecipients(options.bcc) : undefined,
        headers: options.headers,
        tags: options.tags?.map((tag) => ({ name: tag, value: tag })),
      };

      const result = await this.resend.emails.send(resendOptions);

      if (result.error) {
        this.logger.error(`Resend email failed: ${result.error.message}`, result.error);
        return {
          success: false,
          error: result.error.message,
          timestamp: new Date(),
          metadata: { resendError: result.error },
        };
      }

      this.logger.log(`Email sent successfully via Resend. Message ID: ${result.data?.id}`);
      return {
        success: true,
        messageId: result.data?.id,
        providerId: 'resend',
        deliveryStatus: 'sent',
        timestamp: new Date(),
        metadata: { resendData: result.data },
      };
    } catch (error) {
      this.logger.error(`Resend email provider error: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        timestamp: new Date(),
        metadata: { error: error.stack },
      };
    }
  }

  async verifyConfiguration(): Promise<boolean> {
    try {
      // Test the API key by making a simple request
      const result = await this.resend.domains.list();
      this.logger.log(`Resend configuration verification result: ${result.error ? 'Failed' : 'Success'}`);
      return !result.error;
    } catch (error) {
      this.logger.error(`Resend configuration verification failed: ${error.message}`);
      return false;
    }
  }

  getProviderName(): string {
    return 'resend';
  }

  async handleWebhook(payload: any, signature?: string): Promise<EmailDeliveryResult> {
    // Implement Resend webhook handling
    try {
      // Verify webhook signature if provided
      if (signature && this.config.webhookSecret) {
        // Add signature verification logic here
      }

      return {
        success: true,
        messageId: payload.data?.email_id,
        deliveryStatus: this.mapResendEventToStatus(payload.type),
        timestamp: new Date(payload.created_at),
        metadata: payload,
      };
    } catch (error) {
      this.logger.error(`Resend webhook handling error: ${error.message}`);
      return {
        success: false,
        error: error.message,
        timestamp: new Date(),
      };
    }
  }

  async getDeliveryStatus(messageId: string): Promise<EmailDeliveryResult> {
    try {
      const result = await this.resend.emails.get(messageId);

      if (result.error) {
        return {
          success: false,
          error: result.error.message,
          timestamp: new Date(),
        };
      }

      return {
        success: true,
        messageId: result.data?.id,
        deliveryStatus: this.mapResendStatusToDeliveryStatus(result.data?.last_event),
        timestamp: new Date(),
        metadata: result.data,
      };
    } catch (error) {
      this.logger.error(`Failed to get delivery status from Resend: ${error.message}`);
      return {
        success: false,
        error: error.message,
        timestamp: new Date(),
      };
    }
  }

  private formatEmailAddress(address: EmailAddress): string {
    return address.name ? `${address.name} <${address.email}>` : address.email;
  }

  private formatRecipients(recipients: EmailAddress | EmailAddress[]): string[] {
    const recipientArray = Array.isArray(recipients) ? recipients : [recipients];
    return recipientArray.map((recipient) => this.formatEmailAddress(recipient));
  }

  private mapResendEventToStatus(eventType: string): EmailDeliveryResult['deliveryStatus'] {
    switch (eventType) {
      case 'email.sent':
        return 'sent';
      case 'email.delivered':
        return 'delivered';
      case 'email.bounced':
        return 'bounced';
      case 'email.complained':
      case 'email.delivery_delayed':
        return 'failed';
      default:
        return 'pending';
    }
  }

  private mapResendStatusToDeliveryStatus(status: string): EmailDeliveryResult['deliveryStatus'] {
    switch (status) {
      case 'sent':
        return 'sent';
      case 'delivered':
        return 'delivered';
      case 'bounced':
        return 'bounced';
      case 'complained':
        return 'failed';
      default:
        return 'pending';
    }
  }
}
