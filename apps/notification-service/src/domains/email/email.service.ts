/* eslint-disable @typescript-eslint/no-explicit-any */
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { NotificationChannel, NotificationStatus } from '@prisma/notification-client';
import { EmailAddress, EmailOptions } from './providers/email-provider.interface';

import { NotificationPrismaService } from '../../database/prisma.service';
import { EmailProviderFactory } from './providers/email-provider.factory';
import { TemplateManager } from './templates/template.manager';

export interface EmailSendResult {
  success: boolean;
  notificationId?: string;
  messageId?: string;
  error?: string;
  deliveryStatus?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(private prismaService: NotificationPrismaService, private emailProviderFactory: EmailProviderFactory, private templateManager: TemplateManager) {}

  async sendEmail(sendEmailDto: any): Promise<EmailSendResult> {
    try {
      this.logger.log(`Sending email using template: ${sendEmailDto.templateSlug}`);

      // Get and render template
      const renderedTemplate = await this.templateManager.renderTemplate(
        sendEmailDto.templateSlug,
        {
          variables: sendEmailDto.variables || {},
          includeHeader: true,
          includeFooter: true,
        },
        sendEmailDto.tenantId,
      );

      // Get email provider
      const provider = await this.emailProviderFactory.createProvider(sendEmailDto.provider);

      // Prepare email options
      const emailOptions: EmailOptions = {
        to: this.convertToEmailAddress(sendEmailDto.to),
        from: sendEmailDto.from
          ? this.convertToEmailAddress(sendEmailDto.from)
          : {
              email: '<EMAIL>',
              name: 'Qeep',
            },
        subject: renderedTemplate.subject || 'Notification from Qeep',
        html: renderedTemplate.htmlContent,
        text: renderedTemplate.textContent,
        replyTo: sendEmailDto.replyTo ? this.convertToEmailAddress(sendEmailDto.replyTo) : undefined,
        cc: sendEmailDto.cc?.map((addr) => this.convertToEmailAddress(addr)),
        bcc: sendEmailDto.bcc?.map((addr) => this.convertToEmailAddress(addr)),
        headers: sendEmailDto.headers,
        tags: sendEmailDto.tags,
        attachments: sendEmailDto.attachments?.map((att) => ({
          filename: att.filename,
          content: Buffer.from(att.content, 'base64'),
          contentType: att.contentType,
        })),
        metadata: sendEmailDto.metadata,
      };

      // Create notification log entry
      const notificationLog = await this.prismaService.notificationLog.create({
        data: {
          templateId: (await this.templateManager.getTemplate(sendEmailDto.templateSlug, sendEmailDto.tenantId)).id,
          channel: NotificationChannel.EMAIL,
          recipientId: sendEmailDto.to.email,
          recipientAddress: sendEmailDto.to.email,
          subject: emailOptions.subject,
          content: renderedTemplate.htmlContent || renderedTemplate.textContent,
          status: NotificationStatus.PENDING,
          provider: provider.getProviderName(),
          variables: sendEmailDto.variables || {},
          metadata: sendEmailDto.metadata || {},
          tenantId: sendEmailDto.tenantId,
        },
      });

      // Send email
      const deliveryResult = await provider.sendEmail(emailOptions);

      // Update notification log with result
      await this.prismaService.notificationLog.update({
        where: { id: notificationLog.id },
        data: {
          status: deliveryResult.success ? NotificationStatus.SENT : NotificationStatus.FAILED,
          externalMessageId: deliveryResult.messageId,
          errorMessage: deliveryResult.error,
          deliveryDetails: deliveryResult.metadata || {},
          sentAt: deliveryResult.success ? new Date() : undefined,
          failedAt: !deliveryResult.success ? new Date() : undefined,
        },
      });

      this.logger.log(`Email ${deliveryResult.success ? 'sent successfully' : 'failed'}: ${notificationLog.id}`);

      return {
        success: deliveryResult.success,
        notificationId: notificationLog.id,
        messageId: deliveryResult.messageId,
        error: deliveryResult.error,
        deliveryStatus: deliveryResult.deliveryStatus,
        timestamp: deliveryResult.timestamp,
        metadata: deliveryResult.metadata,
      };
    } catch (error) {
      this.logger.error(`Failed to send email: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to send email: ${error.message}`);
    }
  }

  async sendBulkEmail(sendBulkEmailDto: any): Promise<EmailSendResult[]> {
    const results: EmailSendResult[] = [];

    for (const recipient of sendBulkEmailDto.recipients) {
      try {
        const emailDto: any = {
          ...sendBulkEmailDto,
          to: recipient,
        };

        const result = await this.sendEmail(emailDto);
        results.push(result);
      } catch (error) {
        this.logger.error(`Failed to send bulk email to ${recipient.email}: ${error.message}`);
        results.push({
          success: false,
          error: error.message,
          timestamp: new Date(),
        });
      }
    }

    return results;
  }

  async getEmailStatus(notificationId: string): Promise<any> {
    const notification = await this.prismaService.notificationLog.findUnique({
      where: { id: notificationId },
      include: { template: true },
    });

    if (!notification) {
      throw new BadRequestException('Notification not found');
    }

    return {
      id: notification.id,
      status: notification.status,
      provider: notification.provider,
      recipientAddress: notification.recipientAddress,
      subject: notification.subject,
      sentAt: notification.sentAt,
      deliveredAt: notification.deliveredAt,
      failedAt: notification.failedAt,
      errorMessage: notification.errorMessage,
      retryCount: notification.retryCount,
      deliveryDetails: notification.deliveryDetails,
      template: {
        slug: notification.template.slug,
        name: notification.template.name,
      },
    };
  }

  async getEmailHistory(recipientId: string, tenantId?: string, limit = 50, offset = 0): Promise<any> {
    const notifications = await this.prismaService.notificationLog.findMany({
      where: {
        recipientId,
        channel: NotificationChannel.EMAIL,
        ...(tenantId && { tenantId }),
      },
      include: { template: true },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
    });

    const total = await this.prismaService.notificationLog.count({
      where: {
        recipientId,
        channel: NotificationChannel.EMAIL,
        ...(tenantId && { tenantId }),
      },
    });

    return {
      notifications: notifications.map((n) => ({
        id: n.id,
        templateSlug: n.template.slug,
        subject: n.subject,
        status: n.status,
        provider: n.provider,
        sentAt: n.sentAt,
        deliveredAt: n.deliveredAt,
        createdAt: n.createdAt,
      })),
      total,
      limit,
      offset,
    };
  }

  private convertToEmailAddress(dto: any): EmailAddress {
    return {
      email: dto.email,
      name: dto.name,
    };
  }
}
