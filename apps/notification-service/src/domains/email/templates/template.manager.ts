/* eslint-disable @typescript-eslint/no-explicit-any */
import * as Handlebars from 'handlebars';

import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { NotificationTemplate, Prisma, TemplateStatus, TemplateType } from '@prisma/notification-client';

import { NotificationPrismaService } from '../../../database/prisma.service';

type TemplateWithIncludes = Prisma.NotificationTemplateGetPayload<{
  include: {
    headerTemplate: true;
    footerTemplate: true;
  };
}>;

export interface TemplateRenderOptions {
  variables: Record<string, any>;
  includeHeader?: boolean;
  includeFooter?: boolean;
}

export interface RenderedTemplate {
  subject?: string;
  htmlContent?: string;
  textContent?: string;
  variables: Record<string, any>;
}

@Injectable()
export class TemplateManager {
  private readonly logger = new Logger(TemplateManager.name);
  private compiledTemplates: Map<string, Handlebars.TemplateDelegate> = new Map();

  constructor(private prismaService: NotificationPrismaService) {
    this.registerHelpers();
  }

  async getTemplate(slug: string, tenantId?: string): Promise<TemplateWithIncludes> {
    // First try to find tenant-specific template if tenantId is provided
    let template = null;

    if (tenantId) {
      template = await this.prismaService.notificationTemplate.findFirst({
        where: {
          slug,
          status: TemplateStatus.ACTIVE,
          tenantId,
        },
        include: {
          headerTemplate: true,
          footerTemplate: true,
        },
      });
    }

    // If no tenant-specific template found, fall back to global template
    if (!template) {
      template = await this.prismaService.notificationTemplate.findFirst({
        where: {
          slug,
          status: TemplateStatus.ACTIVE,
          tenantId: null, // Global template
        },
        include: {
          headerTemplate: true,
          footerTemplate: true,
        },
      });
    }

    if (!template) {
      throw new NotFoundException(`Template with slug '${slug}' not found`);
    }

    return template;
  }

  async renderTemplate(slug: string, options: TemplateRenderOptions, tenantId?: string): Promise<RenderedTemplate> {
    const template = await this.getTemplate(slug, tenantId);

    this.logger.log(`Rendering template: ${slug}`);

    const result: RenderedTemplate = {
      variables: options.variables,
    };

    // Render subject if it exists
    if (template.subject) {
      result.subject = this.renderContent(template.subject, options.variables);
    }

    // Render HTML content
    if (template.htmlContent) {
      let htmlContent = template.htmlContent;

      // Include header if specified and available
      if (options.includeHeader !== false && template.headerTemplate) {
        const headerHtml = this.renderContent(template.headerTemplate.htmlContent || '', options.variables);
        htmlContent = headerHtml + htmlContent;
      }

      // Include footer if specified and available
      if (options.includeFooter !== false && template.footerTemplate) {
        const footerHtml = this.renderContent(template.footerTemplate.htmlContent || '', options.variables);
        htmlContent = htmlContent + footerHtml;
      }

      result.htmlContent = this.renderContent(htmlContent, options.variables);
    }

    // Render text content
    if (template.textContent) {
      let textContent = template.textContent;

      // Include header if specified and available
      if (options.includeHeader !== false && template.headerTemplate) {
        const headerText = this.renderContent(template.headerTemplate.textContent || '', options.variables);
        textContent = headerText + '\n\n' + textContent;
      }

      // Include footer if specified and available
      if (options.includeFooter !== false && template.footerTemplate) {
        const footerText = this.renderContent(template.footerTemplate.textContent || '', options.variables);
        textContent = textContent + '\n\n' + footerText;
      }

      result.textContent = this.renderContent(textContent, options.variables);
    }

    return result;
  }

  async createTemplate(templateData: Partial<NotificationTemplate>): Promise<NotificationTemplate> {
    return await this.prismaService.notificationTemplate.create({
      data: templateData as any,
    });
  }

  async updateTemplate(id: string, templateData: Partial<NotificationTemplate>): Promise<NotificationTemplate> {
    const updatedTemplate = await this.prismaService.notificationTemplate.update({
      where: { id },
      data: templateData as any,
    });

    // Clear compiled template cache for this template
    this.clearTemplateCache(updatedTemplate.slug);

    return updatedTemplate;
  }

  async deleteTemplate(id: string): Promise<void> {
    const template = await this.prismaService.notificationTemplate.findUnique({ where: { id } });
    if (template) {
      this.clearTemplateCache(template.slug);
      await this.prismaService.notificationTemplate.delete({ where: { id } });
    }
  }

  async getTemplatesByType(type: TemplateType, tenantId?: string): Promise<NotificationTemplate[]> {
    return await this.prismaService.notificationTemplate.findMany({
      where: {
        type,
        status: TemplateStatus.ACTIVE,
        ...(tenantId && { tenantId }),
      },
    });
  }

  private renderContent(content: string, variables: Record<string, any>): string {
    try {
      const cacheKey = this.generateCacheKey(content);

      let compiledTemplate = this.compiledTemplates.get(cacheKey);
      if (!compiledTemplate) {
        compiledTemplate = Handlebars.compile(content);
        this.compiledTemplates.set(cacheKey, compiledTemplate);
      }

      return compiledTemplate(variables);
    } catch (error) {
      this.logger.error(`Template rendering error: ${error.message}`, error.stack);
      throw new Error(`Failed to render template: ${error.message}`);
    }
  }

  private generateCacheKey(content: string): string {
    // Simple hash function for caching compiled templates
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  private clearTemplateCache(slug?: string): void {
    if (slug) {
      // Clear specific template cache - would need to track slug to cache key mapping
      this.logger.log(`Clearing cache for template: ${slug}`);
    } else {
      // Clear all template cache
      this.compiledTemplates.clear();
      this.logger.log('Cleared all template cache');
    }
  }

  private registerHelpers(): void {
    // Register custom Handlebars helpers
    Handlebars.registerHelper('formatDate', (date: Date, format: string) => {
      if (!date) return '';
      // Simple date formatting - could use a library like moment.js or date-fns
      return date.toLocaleDateString();
    });

    Handlebars.registerHelper('uppercase', (str: string) => {
      return str ? str.toUpperCase() : '';
    });

    Handlebars.registerHelper('lowercase', (str: string) => {
      return str ? str.toLowerCase() : '';
    });

    Handlebars.registerHelper('eq', (a: any, b: any) => {
      return a === b;
    });

    Handlebars.registerHelper('ne', (a: any, b: any) => {
      return a !== b;
    });

    Handlebars.registerHelper('gt', (a: number, b: number) => {
      return a > b;
    });

    Handlebars.registerHelper('lt', (a: number, b: number) => {
      return a < b;
    });

    this.logger.log('Handlebars helpers registered');
  }
}
