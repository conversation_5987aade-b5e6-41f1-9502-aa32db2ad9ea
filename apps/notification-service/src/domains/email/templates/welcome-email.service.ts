/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable, Logger } from '@nestjs/common';
import { EmailService } from '../email.service';

export interface WelcomeEmailData {
  userEmail: string;
  userName: string;
  organizationName?: string;
  tenantId?: string;
  loginUrl?: string;
  supportEmail?: string;
  metadata?: Record<string, any>;
}

@Injectable()
export class WelcomeEmailService {
  private readonly logger = new Logger(WelcomeEmailService.name);

  constructor(private emailService: EmailService) {}

  async sendWelcomeEmail(welcomeData: WelcomeEmailData) {
    this.logger.log(`Sending welcome email to: ${welcomeData.userEmail}`);

    const sendEmailDto: any = {
      templateSlug: 'welcome-email',
      to: {
        email: welcomeData.userEmail,
        name: welcomeData.userName,
      },
      variables: {
        userName: welcomeData.userName,
        userEmail: welcomeData.userEmail,
        organizationName: welcomeData.organizationName || 'Qeep',
        loginUrl: welcomeData.loginUrl || 'https://app.qeep.com/login',
        supportEmail: welcomeData.supportEmail || '<EMAIL>',
        currentYear: new Date().getFullYear(),
        ...welcomeData.metadata,
      },
      tenantId: welcomeData.tenantId,
      tags: ['welcome', 'onboarding'],
      metadata: {
        type: 'welcome_email',
        userId: welcomeData.userEmail,
        organizationName: welcomeData.organizationName,
        ...welcomeData.metadata,
      },
    };

    try {
      const result = await this.emailService.sendEmail(sendEmailDto);

      if (result.success) {
        this.logger.log(`Welcome email sent successfully to ${welcomeData.userEmail}: ${result.notificationId}`);
      } else {
        this.logger.error(`Welcome email failed for ${welcomeData.userEmail}: ${result.error}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`Failed to send welcome email to ${welcomeData.userEmail}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async sendPasswordResetEmail(email: string, resetToken: string, tenantId?: string) {
    this.logger.log(`Sending password reset email to: ${email}`);

    const sendEmailDto: any = {
      templateSlug: 'password-reset-email',
      to: {
        email,
      },
      variables: {
        resetUrl: `https://app.qeep.com/reset-password?token=${resetToken}`,
        resetToken,
        expiryHours: 24,
        supportEmail: '<EMAIL>',
        currentYear: new Date().getFullYear(),
      },
      tenantId,
      tags: ['password-reset', 'security'],
      metadata: {
        type: 'password_reset_email',
        userId: email,
        resetToken,
      },
    };

    try {
      const result = await this.emailService.sendEmail(sendEmailDto);

      if (result.success) {
        this.logger.log(`Password reset email sent successfully to ${email}: ${result.notificationId}`);
      } else {
        this.logger.error(`Password reset email failed for ${email}: ${result.error}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`Failed to send password reset email to ${email}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async sendEmailVerificationEmail(email: string, verificationToken: string, tenantId?: string) {
    this.logger.log(`Sending email verification email to: ${email}`);

    const sendEmailDto: any = {
      templateSlug: 'email-verification',
      to: {
        email,
      },
      variables: {
        verificationUrl: `https://app.qeep.com/verify-email?token=${verificationToken}`,
        verificationToken,
        expiryHours: 48,
        supportEmail: '<EMAIL>',
        currentYear: new Date().getFullYear(),
      },
      tenantId,
      tags: ['email-verification', 'onboarding'],
      metadata: {
        type: 'email_verification',
        userId: email,
        verificationToken,
      },
    };

    try {
      const result = await this.emailService.sendEmail(sendEmailDto);

      if (result.success) {
        this.logger.log(`Email verification sent successfully to ${email}: ${result.notificationId}`);
      } else {
        this.logger.error(`Email verification failed for ${email}: ${result.error}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`Failed to send email verification to ${email}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async sendTenantInvitationEmail(email: string, inviterName: string, organizationName: string, invitationToken: string, tenantId?: string) {
    this.logger.log(`Sending tenant invitation email to: ${email}`);

    const sendEmailDto: any = {
      templateSlug: 'tenant-invitation',
      to: {
        email,
      },
      variables: {
        inviterName,
        organizationName,
        invitationUrl: `https://app.qeep.com/accept-invitation?token=${invitationToken}`,
        invitationToken,
        expiryDays: 7,
        supportEmail: '<EMAIL>',
        currentYear: new Date().getFullYear(),
      },
      tenantId,
      tags: ['invitation', 'tenant'],
      metadata: {
        type: 'tenant_invitation',
        inviteeEmail: email,
        inviterName,
        organizationName,
        invitationToken,
      },
    };

    try {
      const result = await this.emailService.sendEmail(sendEmailDto);

      if (result.success) {
        this.logger.log(`Tenant invitation sent successfully to ${email}: ${result.notificationId}`);
      } else {
        this.logger.error(`Tenant invitation failed for ${email}: ${result.error}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`Failed to send tenant invitation to ${email}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
