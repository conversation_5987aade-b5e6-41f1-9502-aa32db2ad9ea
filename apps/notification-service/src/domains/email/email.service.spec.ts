import { Test, TestingModule } from '@nestjs/testing';
import { EmailService } from './email.service';

describe('EmailService', () => {
  let service: EmailService;

  const mockEmailProvider = {
    sendEmail: jest.fn(),
    validateEmail: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailService,
        {
          provide: 'EMAIL_PROVIDER',
          useValue: mockEmailProvider,
        },
      ],
    }).compile();

    service = module.get<EmailService>(EmailService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendEmail', () => {
    it('should send an email successfully', async () => {
      const emailData = {
        to: '<EMAIL>',
        subject: 'Test Email',
        body: 'Test email body',
      };

      mockEmailProvider.sendEmail.mockResolvedValue({ success: true, messageId: '123' });

      const result = await service.sendEmail(emailData);

      expect(result.success).toBe(true);
      expect(mockEmailProvider.sendEmail).toHaveBeenCalledWith(emailData);
    });

    it('should handle email sending failure', async () => {
      const emailData = {
        to: '<EMAIL>',
        subject: 'Test Email',
        body: 'Test email body',
      };

      mockEmailProvider.sendEmail.mockRejectedValue(new Error('Email sending failed'));

      await expect(service.sendEmail(emailData)).rejects.toThrow('Email sending failed');
    });
  });

  describe('validateEmail', () => {
    it('should validate email address format', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      const invalidEmails = [
        'invalid-email',
        '@domain.com',
        'user@',
        '',
      ];

      validEmails.forEach(email => {
        expect(email).toMatch(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);
      });

      invalidEmails.forEach(email => {
        expect(email).not.toMatch(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);
      });
    });
  });
});
