import { <PERSON>, Post, Get, Body, Param, Query, ValidationPipe, Logger } from '@nestjs/common';
import { EmailService } from './email.service';
import { SendEmailDto, SendBulkEmailDto } from './dto/send-email.dto';

@Controller('notifications/email')
export class EmailController {
  private readonly logger = new Logger(EmailController.name);

  constructor(private emailService: EmailService) {}

  @Post('send')
  async sendEmail(@Body(ValidationPipe) sendEmailDto: SendEmailDto) {
    this.logger.log(`Received email send request for template: ${sendEmailDto.templateSlug}`);

    try {
      const result = await this.emailService.sendEmail(sendEmailDto);

      this.logger.log(`Email send ${result.success ? 'successful' : 'failed'}: ${result.notificationId}`);

      return {
        success: result.success,
        data: {
          notificationId: result.notificationId,
          messageId: result.messageId,
          status: result.deliveryStatus,
          timestamp: result.timestamp,
        },
        ...(result.error && { error: result.error }),
      };
    } catch (error) {
      this.logger.error(`Email send failed: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        timestamp: new Date(),
      };
    }
  }

  @Post('send-bulk')
  async sendBulkEmail(@Body(ValidationPipe) sendBulkEmailDto: SendBulkEmailDto) {
    this.logger.log(`Received bulk email send request for ${sendBulkEmailDto.recipients.length} recipients`);

    try {
      const results = await this.emailService.sendBulkEmail(sendBulkEmailDto);

      const successCount = results.filter((r) => r.success).length;
      const failureCount = results.length - successCount;

      this.logger.log(`Bulk email completed: ${successCount} successful, ${failureCount} failed`);

      return {
        success: true,
        data: {
          total: results.length,
          successful: successCount,
          failed: failureCount,
          results: results.map((r) => ({
            notificationId: r.notificationId,
            messageId: r.messageId,
            success: r.success,
            error: r.error,
            timestamp: r.timestamp,
          })),
        },
      };
    } catch (error) {
      this.logger.error(`Bulk email send failed: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        timestamp: new Date(),
      };
    }
  }

  @Get('status/:notificationId')
  async getEmailStatus(@Param('notificationId') notificationId: string) {
    this.logger.log(`Getting email status for notification: ${notificationId}`);

    try {
      const status = await this.emailService.getEmailStatus(notificationId);

      return {
        success: true,
        data: status,
      };
    } catch (error) {
      this.logger.error(`Failed to get email status: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  @Get('history/:recipientId')
  async getEmailHistory(@Param('recipientId') recipientId: string, @Query('tenantId') tenantId?: string, @Query('limit') limit?: string, @Query('offset') offset?: string) {
    this.logger.log(`Getting email history for recipient: ${recipientId}`);

    try {
      const history = await this.emailService.getEmailHistory(recipientId, tenantId, limit ? parseInt(limit, 10) : 50, offset ? parseInt(offset, 10) : 0);

      return {
        success: true,
        data: history,
      };
    } catch (error) {
      this.logger.error(`Failed to get email history: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  @Get('providers')
  async getAvailableProviders() {
    this.logger.log('Getting available email providers');

    try {
      // This would be implemented in the email provider factory
      return {
        success: true,
        data: {
          providers: ['resend'], // For now, just return resend
          default: 'resend',
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get providers: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}
