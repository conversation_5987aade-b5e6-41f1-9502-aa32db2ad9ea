import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ConfigService } from '@qeep/common';
import { NotificationPrismaService } from '../../database/prisma.service';
import { EmailController } from './email.controller';
import { EmailService } from './email.service';
import { EmailProviderFactory } from './providers/email-provider.factory';
import { TemplateManager } from './templates/template.manager';
import { WelcomeEmailService } from './templates/welcome-email.service';

@Module({
  imports: [ConfigModule],
  controllers: [EmailController],
  providers: [EmailService, EmailProviderFactory, TemplateManager, WelcomeEmailService, NotificationPrismaService, ConfigService],
  exports: [EmailService, WelcomeEmailService, TemplateManager],
})
export class EmailModule {}
