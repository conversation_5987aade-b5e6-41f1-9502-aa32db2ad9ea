import { <PERSON>, Logger } from '@nestjs/common';
import {
  CreateEmailTemplateRequest,
  CreateEmailTemplateResponse,
  GetEmailHistoryRequest,
  GetEmailHistoryResponse,
  GetEmailStatusRequest,
  GetEmailStatusResponse,
  GetEmailTemplateRequest,
  GetEmailTemplateResponse,
  NotificationServiceControllerMethods,
  SendBulkEmailRequest,
  SendBulkEmailResponse,
  SendEmailRequest,
  SendEmailResponse,
} from '@qeep/proto';

import { GrpcMethod } from '@nestjs/microservices';
import { EmailService } from '../email/email.service';
import { EmailQueueService } from './services/email-queue.service';

@Controller()
@NotificationServiceControllerMethods()
export class NotificationGrpcController {
  private readonly logger = new Logger(NotificationGrpcController.name);

  constructor(private readonly emailService: EmailService, private readonly emailQueueService: EmailQueueService) {}

  @GrpcMethod('NotificationService', 'sendEmail')
  async sendEmail(request: SendEmailRequest): Promise<SendEmailResponse> {
    this.logger.log(`gRPC sendEmail request for: ${request.recipientEmail}`);

    try {
      // Queue the email for processing instead of sending directly
      const emailJobData = {
        templateSlug: request.templateSlug,
        recipientEmail: request.recipientEmail,
        variables: request.templateData || {},
        tenantId: request.tenantCode || 'default',
        priority: 0, // Normal priority
      };

      const jobId = await this.emailQueueService.queueEmail(emailJobData);

      return {
        success: true,
        notificationId: jobId,
        message: 'Email queued successfully for processing',
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          version: '1.0',
        },
      };
    } catch (error) {
      this.logger.error(`gRPC sendEmail failed: ${error.message}`, error.stack);

      return {
        success: false,
        notificationId: '',
        message: 'Internal server error',
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          version: '1.0',
        },
      };
    }
  }

  @GrpcMethod('NotificationService', 'sendBulkEmail')
  async sendBulkEmail(request: SendBulkEmailRequest): Promise<SendBulkEmailResponse> {
    this.logger.log(`gRPC sendBulkEmail request for ${request.recipients?.length || 0} recipients`);

    try {
      // Convert gRPC request to internal DTO format
      const bulkEmailDto = {
        templateSlug: request.templateSlug,
        recipients:
          request.recipients?.map((recipient) => ({
            email: recipient.email,
            name: recipient.name,
          })) || [],
        variables: request.templateData || {},
        tenantId: request.tenantCode || 'default',
      };

      const results = await this.emailService.sendBulkEmail(bulkEmailDto);

      // Process results array
      const successCount = results.filter((r) => r.success).length;
      const notificationIds = results.filter((r) => r.notificationId).map((r) => r.notificationId!);
      const allSuccessful = results.length > 0 && successCount === results.length;

      return {
        success: allSuccessful,
        notificationIds,
        message: allSuccessful ? 'Bulk emails sent successfully' : `${successCount}/${results.length} emails sent successfully`,
        error: !allSuccessful
          ? {
              code: 'BULK_EMAIL_PARTIAL_FAILURE',
              message: `${results.length - successCount} emails failed to send`,
              details: '',
            }
          : undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          version: '1.0',
        },
      };
    } catch (error) {
      this.logger.error(`gRPC sendBulkEmail failed: ${error.message}`, error.stack);

      return {
        success: false,
        notificationIds: [],
        message: 'Internal server error',
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          version: '1.0',
        },
      };
    }
  }

  @GrpcMethod('NotificationService', 'getEmailStatus')
  async getEmailStatus(request: GetEmailStatusRequest): Promise<GetEmailStatusResponse> {
    this.logger.log(`gRPC getEmailStatus request for: ${request.notificationId}`);

    try {
      const status = await this.emailService.getEmailStatus(request.notificationId);

      return {
        success: true,
        status: {
          notificationId: status.notificationId,
          status: status.status,
          recipientEmail: status.recipientEmail,
          sentAt: status.sentAt,
          deliveredAt: status.deliveredAt,
          errorMessage: status.errorMessage,
        },
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          version: '1.0',
        },
      };
    } catch (error) {
      this.logger.error(`gRPC getEmailStatus failed: ${error.message}`, error.stack);

      return {
        success: false,
        status: undefined,
        error: {
          code: 'STATUS_FETCH_FAILED',
          message: error.message,
          details: error.stack || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          version: '1.0',
        },
      };
    }
  }

  @GrpcMethod('NotificationService', 'getEmailHistory')
  async getEmailHistory(request: GetEmailHistoryRequest): Promise<GetEmailHistoryResponse> {
    this.logger.log(`gRPC getEmailHistory request for recipient: ${request.recipientId}`);

    try {
      const history = await this.emailService.getEmailHistory(
        request.recipientId,
        undefined, // tenantId
        request.limit || 10,
        request.offset || 0,
      );

      return {
        success: true,
        notifications:
          history.notifications?.map((notification) => ({
            notificationId: notification.notificationId,
            recipientEmail: notification.recipientEmail,
            subject: notification.subject,
            status: notification.status,
            templateSlug: notification.templateSlug,
            createdAt: notification.createdAt,
            sentAt: notification.sentAt,
          })) || [],
        totalCount: history.totalCount || 0,
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          version: '1.0',
        },
      };
    } catch (error) {
      this.logger.error(`gRPC getEmailHistory failed: ${error.message}`, error.stack);

      return {
        success: false,
        notifications: [],
        totalCount: 0,
        error: {
          code: 'HISTORY_FETCH_FAILED',
          message: error.message,
          details: error.stack || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          version: '1.0',
        },
      };
    }
  }

  @GrpcMethod('NotificationService', 'getEmailTemplate')
  async getEmailTemplate(request: GetEmailTemplateRequest): Promise<GetEmailTemplateResponse> {
    this.logger.log(`gRPC getEmailTemplate request for: ${request.templateSlug}`);

    // TODO: Implement template service
    return {
      success: false,
      template: undefined,
      error: {
        code: 'NOT_IMPLEMENTED',
        message: 'Template service not yet implemented',
        details: '',
      },
      metadata: {
        requestId: request.metadata?.requestId || '',
        timestamp: new Date(),
        version: '1.0',
      },
    };
  }

  @GrpcMethod('NotificationService', 'createEmailTemplate')
  async createEmailTemplate(request: CreateEmailTemplateRequest): Promise<CreateEmailTemplateResponse> {
    this.logger.log(`gRPC createEmailTemplate request for: ${request.slug}`);

    // TODO: Implement template service
    return {
      success: false,
      template: undefined,
      error: {
        code: 'NOT_IMPLEMENTED',
        message: 'Template service not yet implemented',
        details: '',
      },
      metadata: {
        requestId: request.metadata?.requestId || '',
        timestamp: new Date(),
        version: '1.0',
      },
    };
  }
}
