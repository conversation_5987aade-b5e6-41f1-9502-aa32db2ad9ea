import { EmailModule } from '../email/email.module';
import { EmailQueueService } from './services/email-queue.service';
import { Module } from '@nestjs/common';
import { NotificationGrpcController } from './notification.grpc.controller';
import { QueueMonitoringController } from './controllers/queue-monitoring.controller';

@Module({
  imports: [EmailModule],
  controllers: [NotificationGrpcController, QueueMonitoringController],
  providers: [EmailQueueService],
  exports: [EmailQueueService],
})
export class NotificationModule {}
