import { PaginatedListDataDto, QueueStatsDataDto } from '@qeep/common';
import { Expose } from 'class-transformer';

/**
 * Notification data DTO
 */
export class NotificationDataDto {
  @Expose()
  id: string;

  @Expose()
  type: string;

  @Expose()
  template: string;

  @Expose()
  recipient: string;

  @Expose()
  subject: string;

  @Expose()
  status: string;

  @Expose({ name: 'tenant_code' })
  tenantCode?: string;

  @Expose({ name: 'user_id' })
  userId?: string;

  @Expose()
  metadata?: Record<string, any>;

  @Expose()
  attempts: number;

  @Expose({ name: 'last_error' })
  lastError?: string;

  @Expose({ name: 'created_at' })
  createdAt: Date;

  @Expose({ name: 'sent_at' })
  sentAt?: Date;

  @Expose({ name: 'delivered_at' })
  deliveredAt?: Date;

  @Expose({ name: 'opened_at' })
  openedAt?: Date;

  @Expose({ name: 'clicked_at' })
  clickedAt?: Date;
}

/**
 * Notification send response data DTO
 */
export class NotificationSendDataDto {
  @Expose({ name: 'notification_id' })
  notificationId: string;

  @Expose()
  status: string;

  @Expose({ name: 'queue_position' })
  queuePosition?: number;

  @Expose({ name: 'estimated_delivery_minutes' })
  estimatedDeliveryMinutes?: number;

  @Expose({ name: 'created_at' })
  createdAt: Date;
}

/**
 * Notification list response data DTO
 */
export class NotificationListDataDto extends PaginatedListDataDto<NotificationDataDto> {
  @Expose()
  items: NotificationDataDto[];
}

/**
 * Notification template data DTO
 */
export class NotificationTemplateDataDto {
  @Expose()
  id: string;

  @Expose()
  name: string;

  @Expose({ name: 'display_name' })
  displayName: string;

  @Expose()
  type: string;

  @Expose()
  subject: string;

  @Expose()
  content: string;

  @Expose({ name: 'plain_text' })
  plainText?: string;

  @Expose()
  variables: string[];

  @Expose()
  status: string;

  @Expose()
  category?: string;

  @Expose()
  metadata?: Record<string, any>;

  @Expose({ name: 'created_at' })
  createdAt: Date;

  @Expose({ name: 'updated_at' })
  updatedAt: Date;
}

/**
 * Notification template list data DTO
 */
export class NotificationTemplateListDataDto extends PaginatedListDataDto<NotificationTemplateDataDto> {
  @Expose()
  items: NotificationTemplateDataDto[];
}

/**
 * Notification statistics data DTO
 */
export class NotificationStatsDataDto {
  @Expose({ name: 'total_sent' })
  totalSent: number;

  @Expose({ name: 'total_delivered' })
  totalDelivered: number;

  @Expose({ name: 'total_failed' })
  totalFailed: number;

  @Expose({ name: 'total_queued' })
  totalQueued: number;

  @Expose({ name: 'delivery_rate' })
  deliveryRate: number;

  @Expose({ name: 'avg_delivery_time_minutes' })
  avgDeliveryTimeMinutes: number;

  @Expose({ name: 'stats_by_type' })
  statsByType: Record<
    string,
    {
      sent: number;
      delivered: number;
      failed: number;
      delivery_rate: number;
    }
  >;

  @Expose({ name: 'calculated_at' })
  calculatedAt: Date;
}

/**
 * Queue monitoring data DTO
 */
export class QueueMonitoringDataDto extends QueueStatsDataDto {
  @Expose({ name: 'queue_name' })
  queueName: string;

  @Expose({ name: 'health_status' })
  healthStatus: string;

  @Expose({ name: 'avg_processing_time_seconds' })
  avgProcessingTimeSeconds?: number;

  @Expose({ name: 'oldest_waiting_job_minutes' })
  oldestWaitingJobMinutes?: number;

  @Expose({ name: 'updated_at' })
  updatedAt: Date;
}

/**
 * Bulk notification send data DTO
 */
export class BulkNotificationDataDto {
  @Expose({ name: 'bulk_id' })
  bulkId: string;

  @Expose({ name: 'total_notifications' })
  totalNotifications: number;

  @Expose({ name: 'queued_count' })
  queuedCount: number;

  @Expose({ name: 'failed_count' })
  failedCount: number;

  @Expose({ name: 'estimated_completion_minutes' })
  estimatedCompletionMinutes: number;

  @Expose()
  failures?: Array<{
    recipient: string;
    error: string;
    details?: any;
  }>;

  @Expose({ name: 'created_at' })
  createdAt: Date;
}

/**
 * Notification delivery report data DTO
 */
export class NotificationDeliveryReportDataDto {
  @Expose({ name: 'report_id' })
  reportId: string;

  @Expose({ name: 'period_start' })
  periodStart: Date;

  @Expose({ name: 'period_end' })
  periodEnd: Date;

  @Expose({ name: 'total_notifications' })
  totalNotifications: number;

  @Expose({ name: 'successful_deliveries' })
  successfulDeliveries: number;

  @Expose({ name: 'failed_deliveries' })
  failedDeliveries: number;

  @Expose({ name: 'delivery_rate' })
  deliveryRate: number;

  @Expose({ name: 'daily_stats' })
  dailyStats: Array<{
    date: string;
    sent: number;
    delivered: number;
    failed: number;
    delivery_rate: number;
  }>;

  @Expose({ name: 'top_failure_reasons' })
  topFailureReasons: Array<{
    reason: string;
    count: number;
    percentage: number;
  }>;

  @Expose({ name: 'generated_at' })
  generatedAt: Date;
}
