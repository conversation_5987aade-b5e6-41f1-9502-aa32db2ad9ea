/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable, Lo<PERSON>, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import { Job, Queue, Worker } from 'bullmq';

import { EmailService } from '../../email/email.service';

export interface EmailJobData {
  templateSlug: string;
  recipientEmail: string;
  variables: Record<string, any>;
  tenantId?: string;
  priority?: number;
  delay?: number;
}

@Injectable()
export class EmailQueueService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(EmailQueueService.name);
  private emailQueue: Queue<EmailJobData>;
  private emailWorker: Worker<EmailJobData>;

  constructor(private readonly emailService: EmailService) {}

  async onModuleInit() {
    await this.initializeQueue();
    await this.initializeWorker();
    this.logger.log('Email queue service initialized');
  }

  async onModuleDestroy() {
    if (this.emailWorker) {
      await this.emailWorker.close();
    }
    if (this.emailQueue) {
      await this.emailQueue.close();
    }
    this.logger.log('Email queue service destroyed');
  }

  private async initializeQueue() {
    // Create a direct Redis connection for BullMQ
    const Redis = require('ioredis');
    const redisConnection = new Redis({
      host: 'localhost',
      port: 6379,
      password: 'redis_dev_password',
      maxRetriesPerRequest: null, // Required for BullMQ
      enableReadyCheck: false,
      lazyConnect: true,
    });

    this.emailQueue = new Queue<EmailJobData>('email-processing', {
      connection: redisConnection,
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        delay: 0, // No delay by default
      },
    });

    // Add queue event listeners
    this.emailQueue.on('error', (error) => {
      this.logger.error('Email queue error:', error);
    });

    this.emailQueue.on('waiting', (jobId) => {
      this.logger.debug(`Email job ${jobId} is waiting`);
    });

    // Note: Queue events are handled by the worker, not the queue itself
  }

  private async initializeWorker() {
    // Create a direct Redis connection for BullMQ worker
    const Redis = require('ioredis');
    const redisConnection = new Redis({
      host: 'localhost',
      port: 6379,
      password: 'redis_dev_password',
      maxRetriesPerRequest: null, // Required for BullMQ
      enableReadyCheck: false,
      lazyConnect: true,
    });

    this.emailWorker = new Worker<EmailJobData>(
      'email-processing',
      async (job: Job<EmailJobData>) => {
        return this.processEmailJob(job);
      },
      {
        connection: redisConnection,
        concurrency: 5, // Process up to 5 emails concurrently
      },
    );

    // Add worker event listeners
    this.emailWorker.on('error', (error) => {
      this.logger.error('Email worker error:', error);
    });

    this.emailWorker.on('completed', (job) => {
      this.logger.log(`Email worker completed job ${job.id}`);
    });

    this.emailWorker.on('failed', (job, err) => {
      this.logger.error(`Email worker failed job ${job?.id}:`, err);
    });
  }

  async queueEmail(emailData: EmailJobData): Promise<string> {
    try {
      const job = await this.emailQueue.add('send-email', emailData, {
        priority: emailData.priority || 0,
        delay: emailData.delay || 0,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      });

      this.logger.log(`Email queued successfully with job ID: ${job.id}`);
      return job.id as string;
    } catch (error) {
      this.logger.error('Failed to queue email:', error);
      throw error;
    }
  }

  private async processEmailJob(job: Job<EmailJobData>): Promise<void> {
    const { templateSlug, recipientEmail, variables, tenantId } = job.data;

    this.logger.log(`Processing email job ${job.id} for ${recipientEmail}`);

    try {
      // Convert to email service DTO format
      const emailDto = {
        templateSlug,
        to: {
          email: recipientEmail,
          name: '', // We don't have the name in the queue data
        },
        variables,
        tenantId: tenantId || 'default',
      };

      // Send email using the existing email service
      await this.emailService.sendEmail(emailDto);

      this.logger.log(`Email sent successfully for job ${job.id}`);
    } catch (error) {
      this.logger.error(`Failed to send email for job ${job.id}:`, error);

      // Update job progress with error info
      await job.updateProgress({
        error: error.message,
        timestamp: new Date().toISOString(),
      });

      throw error; // Re-throw to trigger retry mechanism
    }
  }

  // Queue management methods
  async getQueueStats() {
    const waiting = await this.emailQueue.getWaiting();
    const active = await this.emailQueue.getActive();
    const completed = await this.emailQueue.getCompleted();
    const failed = await this.emailQueue.getFailed();

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
    };
  }

  async pauseQueue() {
    await this.emailQueue.pause();
    this.logger.log('Email queue paused');
  }

  async resumeQueue() {
    await this.emailQueue.resume();
    this.logger.log('Email queue resumed');
  }

  async clearQueue() {
    await this.emailQueue.drain();
    this.logger.log('Email queue cleared');
  }

  async retryFailedJobs() {
    const failedJobs = await this.emailQueue.getFailed();
    for (const job of failedJobs) {
      await job.retry();
    }
    this.logger.log(`Retried ${failedJobs.length} failed jobs`);
  }
}
