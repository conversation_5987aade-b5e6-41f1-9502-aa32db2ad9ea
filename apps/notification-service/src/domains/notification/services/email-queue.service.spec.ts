import { Test, TestingModule } from '@nestjs/testing';
import { EmailQueueService } from './email-queue.service';

describe('EmailQueueService', () => {
  let service: EmailQueueService;

  const mockQueue = {
    add: jest.fn(),
    process: jest.fn(),
    getJobs: jest.fn(),
    clean: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailQueueService,
        {
          provide: 'EMAIL_QUEUE',
          useValue: mockQueue,
        },
      ],
    }).compile();

    service = module.get<EmailQueueService>(EmailQueueService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('addEmailToQueue', () => {
    it('should add email job to queue', async () => {
      const emailJob = {
        to: '<EMAIL>',
        subject: 'Test Email',
        body: 'Test email body',
        priority: 'normal',
      };

      mockQueue.add.mockResolvedValue({ id: 'job-123' });

      const result = await service.addEmailToQueue(emailJob);

      expect(result.id).toBe('job-123');
      expect(mockQueue.add).toHaveBeenCalledWith('send-email', emailJob);
    });
  });

  describe('getQueueStatus', () => {
    it('should return queue status', async () => {
      const mockJobs = [
        { id: '1', status: 'completed' },
        { id: '2', status: 'waiting' },
        { id: '3', status: 'active' },
      ];

      mockQueue.getJobs.mockResolvedValue(mockJobs);

      const result = await service.getQueueStatus();

      expect(result).toEqual({
        total: 3,
        completed: 1,
        waiting: 1,
        active: 1,
        failed: 0,
      });
    });
  });

  describe('processEmailJob', () => {
    it('should process email job successfully', async () => {
      const jobData = {
        to: '<EMAIL>',
        subject: 'Test Email',
        body: 'Test email body',
      };

      // Mock successful processing
      const result = await service.processEmailJob(jobData);

      expect(result).toBeDefined();
      // Add more specific assertions based on actual implementation
    });
  });
});
