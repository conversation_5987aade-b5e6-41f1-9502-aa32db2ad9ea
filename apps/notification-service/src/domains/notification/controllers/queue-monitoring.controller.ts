import { Controller, Get, Post, Delete, Logger } from '@nestjs/common';
import { EmailQueueService } from '../services/email-queue.service';

@Controller('queue')
export class QueueMonitoringController {
  private readonly logger = new Logger(QueueMonitoringController.name);

  constructor(private readonly emailQueueService: EmailQueueService) {}

  @Get('stats')
  async getQueueStats() {
    try {
      const stats = await this.emailQueueService.getQueueStats();
      return {
        success: true,
        data: stats,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get queue stats:', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Post('pause')
  async pauseQueue() {
    try {
      await this.emailQueueService.pauseQueue();
      return {
        success: true,
        message: 'Queue paused successfully',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to pause queue:', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Post('resume')
  async resumeQueue() {
    try {
      await this.emailQueueService.resumeQueue();
      return {
        success: true,
        message: 'Queue resumed successfully',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to resume queue:', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Post('retry-failed')
  async retryFailedJobs() {
    try {
      await this.emailQueueService.retryFailedJobs();
      return {
        success: true,
        message: 'Failed jobs retried successfully',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to retry failed jobs:', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Delete('clear')
  async clearQueue() {
    try {
      await this.emailQueueService.clearQueue();
      return {
        success: true,
        message: 'Queue cleared successfully',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to clear queue:', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}
