import { CommonModule, ConfigModule } from '@qeep/common';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { EmailModule } from './domains/email/email.module';
import { Module } from '@nestjs/common';
import { ConfigModule as NestConfigModule } from '@nestjs/config';
import { NotificationConfigService } from './config/notification.config';
import { NotificationModule } from './domains/notification/notification.module';
import { NotificationPrismaService } from './database/prisma.service';

@Module({
  imports: [
    CommonModule.forRoot({
      enableTelemetry: false,
      enableGlobalTenantInterceptor: false,
      enableCircuitBreaker: false,
      enableRateLimiting: false,
      enableSecurityHeaders: false,
    }),
    ConfigModule.forRoot(),
    NestConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.development', '.env'],
    }),
    EmailModule,
    NotificationModule,
  ],
  controllers: [AppController],
  providers: [AppService, NotificationPrismaService, NotificationConfigService],
})
export class AppModule {}
