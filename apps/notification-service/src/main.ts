/**
 * Qeep Notification Service
 * Handles multi-channel notification delivery (email, SMS, push, in-app)
 */

import { Logger, ValidationPipe, VersioningType } from '@nestjs/common';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';

import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@qeep/common';
import { join } from 'path';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // Global prefix for all routes
  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);

  // Enable API versioning
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
    prefix: 'v',
  });

  // Setup gRPC microservice
  const grpcUrl = configService.getServiceUrl('notification-service-grpc');
  const grpcOptions: MicroserviceOptions = {
    transport: Transport.GRPC,
    options: {
      package: 'notification',
      protoPath: join(process.cwd(), 'proto/notification/notification.proto'),
      url: grpcUrl,
      loader: {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
      },
    },
  };

  app.connectMicroservice(grpcOptions);

  // Use specific port for Notification Service
  const port = configService.getServicePort('notification-service');
  const host = configService.getServiceHost('notification-service');

  await app.startAllMicroservices();
  await app.listen(port, host);

  Logger.log(`📧 Notification Service is running on: http://${host}:${port}/${globalPrefix}`);
  Logger.log(`🔌 gRPC Microservice is running on: ${grpcUrl}`);
  Logger.log(`📚 Environment: ${configService.getNodeEnv()}`);
  Logger.log(`🔧 Log Level: ${configService.getLogLevel()}`);
}

bootstrap();
