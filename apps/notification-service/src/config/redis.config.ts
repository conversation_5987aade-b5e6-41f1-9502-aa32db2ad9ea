import { ConfigService } from '@nestjs/config';
import { Injectable } from '@nestjs/common';
import { Redis } from 'ioredis';

@Injectable()
export class RedisConfig {
  constructor(private configService: ConfigService) {}

  createRedisConnection(): Redis {
    const redisUrl = this.configService.get<string>('REDIS_URL');

    if (redisUrl) {
      return new Redis(redisUrl);
    }

    // Fallback to individual config values
    return new Redis({
      host: this.configService.get<string>('REDIS_HOST', 'localhost'),
      port: this.configService.get<number>('REDIS_PORT', 6379),
      password: this.configService.get<string>('REDIS_PASSWORD', 'redis_dev_password'),
      db: this.configService.get<number>('REDIS_DB', 0),
      enableReadyCheck: true,
      maxRetriesPerRequest: null, // Required for BullMQ
      lazyConnect: true,
      keepAlive: 30000,
      connectTimeout: 10000,
      commandTimeout: 5000,
    });
  }

  createBullMQConnection(): Redis {
    const redisUrl = this.configService.get<string>('REDIS_URL');

    if (redisUrl) {
      return new Redis(redisUrl, {
        maxRetriesPerRequest: null, // Required for BullMQ
        enableReadyCheck: false,
        lazyConnect: true,
      });
    }

    // Fallback to individual config values
    return new Redis({
      host: this.configService.get<string>('REDIS_HOST', 'localhost'),
      port: this.configService.get<number>('REDIS_PORT', 6379),
      password: this.configService.get<string>('REDIS_PASSWORD', 'redis_dev_password'),
      db: this.configService.get<number>('REDIS_DB', 0),
      maxRetriesPerRequest: null, // Required for BullMQ
      enableReadyCheck: false,
      lazyConnect: true,
      keepAlive: 30000,
      connectTimeout: 10000,
      commandTimeout: 5000,
    });
  }

  getQueueConfig() {
    return {
      connection: this.createBullMQConnection(),
      defaultJobOptions: {
        removeOnComplete: 100, // Keep last 100 completed jobs
        removeOnFail: 50, // Keep last 50 failed jobs
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    };
  }
}
