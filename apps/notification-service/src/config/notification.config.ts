import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class NotificationConfigService {
  constructor(private configService: ConfigService) {}

  // Database Configuration
  get databaseUrl(): string {
    return this.configService.get<string>('NOTIFICATION_DATABASE_URL', 'postgresql://postgres:password@localhost:5432/qeep_notifications');
  }

  // Email Provider Configuration
  get defaultEmailProvider(): string {
    return this.configService.get<string>('DEFAULT_EMAIL_PROVIDER', 'smtp');
  }

  get emailTimeout(): number {
    return this.configService.get<number>('EMAIL_TIMEOUT', 30000);
  }

  get emailRetryAttempts(): number {
    return this.configService.get<number>('EMAIL_RETRY_ATTEMPTS', 3);
  }

  // Resend Configuration
  get resendApiKey(): string {
    return this.configService.get<string>('RESEND_API_KEY', '');
  }

  get resendFromEmail(): string {
    return this.configService.get<string>('RESEND_FROM_EMAIL', '<EMAIL>');
  }

  get resendFromName(): string {
    return this.configService.get<string>('RESEND_FROM_NAME', 'Qeep');
  }

  get resendWebhookSecret(): string {
    return this.configService.get<string>('RESEND_WEBHOOK_SECRET', '');
  }

  // SendGrid Configuration
  get sendGridApiKey(): string {
    return this.configService.get<string>('SENDGRID_API_KEY', '');
  }

  get sendGridFromEmail(): string {
    return this.configService.get<string>('SENDGRID_FROM_EMAIL', '<EMAIL>');
  }

  get sendGridFromName(): string {
    return this.configService.get<string>('SENDGRID_FROM_NAME', 'Qeep');
  }

  get sendGridWebhookSecret(): string {
    return this.configService.get<string>('SENDGRID_WEBHOOK_SECRET', '');
  }

  // Mailgun Configuration
  get mailgunApiKey(): string {
    return this.configService.get<string>('MAILGUN_API_KEY', '');
  }

  get mailgunDomain(): string {
    return this.configService.get<string>('MAILGUN_DOMAIN', '');
  }

  get mailgunFromEmail(): string {
    return this.configService.get<string>('MAILGUN_FROM_EMAIL', '<EMAIL>');
  }

  get mailgunFromName(): string {
    return this.configService.get<string>('MAILGUN_FROM_NAME', 'Qeep');
  }

  get mailgunBaseUrl(): string {
    return this.configService.get<string>('MAILGUN_BASE_URL', 'https://api.mailgun.net');
  }

  get mailgunWebhookSecret(): string {
    return this.configService.get<string>('MAILGUN_WEBHOOK_SECRET', '');
  }

  // AWS SES Configuration
  get awsAccessKeyId(): string {
    return this.configService.get<string>('AWS_ACCESS_KEY_ID', '');
  }

  get awsSecretAccessKey(): string {
    return this.configService.get<string>('AWS_SECRET_ACCESS_KEY', '');
  }

  get awsRegion(): string {
    return this.configService.get<string>('AWS_REGION', 'us-east-1');
  }

  get sesFromEmail(): string {
    return this.configService.get<string>('SES_FROM_EMAIL', '<EMAIL>');
  }

  get sesFromName(): string {
    return this.configService.get<string>('SES_FROM_NAME', 'Qeep');
  }

  // SMTP Configuration
  get smtpHost(): string {
    return this.configService.get<string>('SMTP_HOST', 'localhost');
  }

  get smtpPort(): number {
    return this.configService.get<number>('SMTP_PORT', 1026);
  }

  get smtpSecure(): boolean {
    return this.configService.get<boolean>('SMTP_SECURE', false);
  }

  get smtpUser(): string {
    return this.configService.get<string>('SMTP_USER', '');
  }

  get smtpPassword(): string {
    return this.configService.get<string>('SMTP_PASSWORD', '');
  }

  get smtpFromEmail(): string {
    return this.configService.get<string>('SMTP_FROM_EMAIL', '<EMAIL>');
  }

  get smtpFromName(): string {
    return this.configService.get<string>('SMTP_FROM_NAME', 'Qeep Development');
  }

  get smtpIgnoreTLS(): boolean {
    return this.configService.get<boolean>('SMTP_IGNORE_TLS', false);
  }

  get smtpRequireTLS(): boolean {
    return this.configService.get<boolean>('SMTP_REQUIRE_TLS', false);
  }

  // SMS Provider Configuration
  get defaultSmsProvider(): string {
    return this.configService.get<string>('DEFAULT_SMS_PROVIDER', 'twilio');
  }

  get smsTimeout(): number {
    return this.configService.get<number>('SMS_TIMEOUT', 30000);
  }

  get smsRetryAttempts(): number {
    return this.configService.get<number>('SMS_RETRY_ATTEMPTS', 3);
  }

  // Twilio Configuration
  get twilioAccountSid(): string {
    return this.configService.get<string>('TWILIO_ACCOUNT_SID', '');
  }

  get twilioAuthToken(): string {
    return this.configService.get<string>('TWILIO_AUTH_TOKEN', '');
  }

  get twilioFromNumber(): string {
    return this.configService.get<string>('TWILIO_FROM_NUMBER', '');
  }

  get twilioWebhookSecret(): string {
    return this.configService.get<string>('TWILIO_WEBHOOK_SECRET', '');
  }

  // Application Configuration
  get appUrl(): string {
    return this.configService.get<string>('APP_URL', 'https://app.qeep.com');
  }

  get supportEmail(): string {
    return this.configService.get<string>('SUPPORT_EMAIL', '<EMAIL>');
  }

  // Service Configuration
  get port(): number {
    return this.configService.get<number>('NOTIFICATION_SERVICE_PORT', 3004);
  }

  get grpcPort(): number {
    return this.configService.get<number>('NOTIFICATION_SERVICE_GRPC_PORT', 3015);
  }

  get environment(): string {
    return this.configService.get<string>('NODE_ENV', 'development');
  }

  get logLevel(): string {
    return this.configService.get<string>('LOG_LEVEL', 'info');
  }

  // Rate Limiting Configuration
  get emailRateLimit(): number {
    return this.configService.get<number>('EMAIL_RATE_LIMIT', 100); // per minute
  }

  get emailDailyLimit(): number {
    return this.configService.get<number>('EMAIL_DAILY_LIMIT', 10000);
  }

  get smsRateLimit(): number {
    return this.configService.get<number>('SMS_RATE_LIMIT', 50); // per minute
  }

  get smsDailyLimit(): number {
    return this.configService.get<number>('SMS_DAILY_LIMIT', 1000);
  }

  // Template Configuration
  get templateCacheSize(): number {
    return this.configService.get<number>('TEMPLATE_CACHE_SIZE', 1000);
  }

  get templateCacheTtl(): number {
    return this.configService.get<number>('TEMPLATE_CACHE_TTL', 3600); // 1 hour in seconds
  }

  // Webhook Configuration
  get webhookRetryAttempts(): number {
    return this.configService.get<number>('WEBHOOK_RETRY_ATTEMPTS', 3);
  }

  get webhookRetryDelay(): number {
    return this.configService.get<number>('WEBHOOK_RETRY_DELAY', 5000); // 5 seconds
  }

  // Queue Configuration
  get redisUrl(): string {
    return this.configService.get<string>('REDIS_URL', 'redis://localhost:6379');
  }

  get queueConcurrency(): number {
    return this.configService.get<number>('QUEUE_CONCURRENCY', 10);
  }

  get queueRetryAttempts(): number {
    return this.configService.get<number>('QUEUE_RETRY_ATTEMPTS', 3);
  }

  get queueRetryDelay(): number {
    return this.configService.get<number>('QUEUE_RETRY_DELAY', 5000);
  }
}
