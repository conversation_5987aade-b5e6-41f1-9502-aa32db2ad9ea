import { Test, TestingModule } from '@nestjs/testing';
import { PrismaService } from './prisma.service';

describe('PrismaService', () => {
  let service: PrismaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PrismaService],
    }).compile();

    service = module.get<PrismaService>(PrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should extend PrismaClient', () => {
    expect(service).toHaveProperty('$connect');
    expect(service).toHaveProperty('$disconnect');
  });

  describe('connection management', () => {
    it('should have connect method', () => {
      expect(typeof service.onModuleInit).toBe('function');
    });

    it('should have disconnect method', () => {
      expect(typeof service.onModuleDestroy).toBe('function');
    });
  });

  describe('database operations', () => {
    it('should have notification models available', () => {
      // Add specific model checks based on notification service schema
      expect(typeof service.$connect).toBe('function');
      expect(typeof service.$disconnect).toBe('function');
    });
  });
});
