import { BasePrismaService } from '@qeep/common';
import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/notification-client';

@Injectable()
export class NotificationPrismaService extends BasePrismaService {
  protected prismaClient: PrismaClient;

  constructor() {
    super();
    this.prismaClient = new PrismaClient({
      log: ['error'],
      errorFormat: 'colorless',
    });
  }

  // Expose Prisma client methods for notification templates
  get notificationTemplate() {
    return this.prismaClient.notificationTemplate;
  }

  // Expose Prisma client methods for notification logs
  get notificationLog() {
    return this.prismaClient.notificationLog;
  }

  // Expose Prisma client methods for notification providers
  get notificationProvider() {
    return this.prismaClient.notificationProvider;
  }

  // Expose raw query methods
  get $queryRaw() {
    return this.prismaClient.$queryRaw.bind(this.prismaClient);
  }

  get $transaction() {
    return this.prismaClient.$transaction.bind(this.prismaClient);
  }

  get $executeRaw() {
    return this.prismaClient.$executeRaw.bind(this.prismaClient);
  }
}
