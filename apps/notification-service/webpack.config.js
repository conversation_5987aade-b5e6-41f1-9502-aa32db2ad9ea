const { NxAppWebpackPlugin } = require('@nx/webpack/app-plugin');
const { join } = require('path');

module.exports = {
  target: 'node',
  output: {
    path: join(__dirname, '../../dist/apps/notification-service'),
  },
  externalsPresets: { node: true },
  ignoreWarnings: [
    // Ignore source map warnings from Prisma client
    {
      module: /node_modules\/.prisma/,
      message: /Failed to parse source map/,
    },
    {
      module: /node_modules\/@prisma/,
      message: /Failed to parse source map/,
    },
  ],
  externals: [
    // Explicitly exclude Node.js built-in modules
    function ({ request }, callback) {
      if (
        /^(crypto|fs|path|os|util|stream|events|buffer|url|querystring|http|https|net|tls|zlib|child_process|cluster|dgram|dns|domain|readline|repl|string_decoder|timers|tty|vm|worker_threads|async_hooks|perf_hooks|inspector|trace_events|v8|wasi)$/.test(
          request,
        )
      ) {
        return callback(null, 'commonjs ' + request);
      }
      callback();
    },
  ],
  module: {
    rules: [
      {
        test: /\.js$/,
        enforce: 'pre',
        use: [
          {
            loader: 'source-map-loader',
            options: {
              filterSourceMappingUrl: (url, resourcePath) => {
                // Skip source map loading for Prisma client files
                if (resourcePath.includes('.prisma') || resourcePath.includes('@prisma')) {
                  return false;
                }
                return true;
              },
            },
          },
        ],
        exclude: [
          // Exclude Prisma client from source map processing to avoid warnings
          /node_modules\/.prisma/,
          /node_modules\/@prisma/,
        ],
      },
    ],
  },
  plugins: [
    new NxAppWebpackPlugin({
      target: 'node',
      compiler: 'tsc',
      main: './src/main.ts',
      tsConfig: './tsconfig.app.json',
      assets: ['./src/assets'],
      optimization: false,
      outputHashing: 'none',
      generatePackageJson: true,
    }),
  ],
};
