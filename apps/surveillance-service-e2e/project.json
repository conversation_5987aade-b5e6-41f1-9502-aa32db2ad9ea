{"name": "surveillance-service-e2e", "$schema": "../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["surveillance-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "surveillance-service-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["surveillance-service:build", "surveillance-service:serve"]}}}