{"name": "integration-service-e2e", "$schema": "../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["integration-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "integration-service-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["integration-service:build", "integration-service:serve"]}}}