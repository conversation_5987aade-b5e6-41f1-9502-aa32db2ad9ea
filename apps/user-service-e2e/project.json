{"name": "user-service-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["user-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/user-service-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["user-service:build", "user-service:serve"]}}}