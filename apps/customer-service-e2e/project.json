{"name": "customer-service-e2e", "$schema": "../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["customer-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "customer-service-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["customer-service:build", "customer-service:serve"]}}}