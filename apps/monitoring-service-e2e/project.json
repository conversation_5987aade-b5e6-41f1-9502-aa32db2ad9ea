{"name": "monitoring-service-e2e", "$schema": "../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["monitoring-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "monitoring-service-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["monitoring-service:build", "monitoring-service:serve"]}}}