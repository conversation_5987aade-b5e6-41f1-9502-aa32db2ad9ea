/* eslint-disable @typescript-eslint/no-explicit-any */

import { Injectable } from '@nestjs/common';
import { BasePrismaService } from '@qeep/common';

// Import the generated Prisma client directly
const { PrismaClient } = require('../../../../node_modules/.prisma/audit-client');

@Injectable()
export class PrismaService extends BasePrismaService {
  protected prismaClient: any;

  constructor() {
    super();
    this.prismaClient = new PrismaClient({
      log: ['error'],
      errorFormat: 'colorless',
    });
  }

  // Audit-specific methods
  get auditLog() {
    return this.prismaClient.auditLog;
  }

  get auditSummary() {
    return this.prismaClient.auditSummary;
  }

  get complianceReport() {
    return this.prismaClient.complianceReport;
  }
}
