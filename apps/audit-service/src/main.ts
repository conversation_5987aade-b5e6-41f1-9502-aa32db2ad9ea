/**
 * Qeep Audit Service
 * Handles comprehensive audit trails and compliance logging
 */

import { Logger, ValidationPipe, VersioningType } from '@nestjs/common';

import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@qeep/common';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // Global prefix for all routes
  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);

  // Enable API versioning
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
    prefix: 'v',
  });

  // Use specific port for Audit Service
  const port = configService.getServicePort('audit-service');
  const host = configService.getServiceHost('audit-service');

  await app.listen(port, host);

  Logger.log(`📋 Audit Service is running on: http://${host}:${port}/${globalPrefix}`);
  Logger.log(`📚 Environment: ${configService.getNodeEnv()}`);
  Logger.log(`🔧 Log Level: ${configService.getLogLevel()}`);
}

bootstrap();
