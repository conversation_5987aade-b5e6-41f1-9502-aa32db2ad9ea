import { Module } from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import { AuditController } from './controllers/audit.controller';
import { AuditLogService } from './services/audit-log.service';

@Module({
  imports: [],
  controllers: [AuditController],
  providers: [PrismaService, AuditLogService],
  exports: [AuditLogService],
})
export class AuditLoggingModule {}
