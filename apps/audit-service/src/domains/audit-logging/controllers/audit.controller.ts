import { Controller, Get, Logger, Query } from '@nestjs/common';
import { AuditLogResponseDto, AuditStatsResponseDto, GetAuditLogsDto, GetAuditStatsDto } from '../dto/audit-log.dto';
import { AuditLogService } from '../services/audit-log.service';

@Controller('audit')
export class AuditController {
  private readonly logger = new Logger(AuditController.name);

  constructor(private readonly auditLogService: AuditLogService) {}

  @Get('logs')
  async getAuditLogs(@Query() query: GetAuditLogsDto): Promise<AuditLogResponseDto> {
    const filters = {
      tenantCode: query.tenant_code,
      eventType: query.event_type,
      eventCategory: query.event_category,
      severity: query.severity,
      actorUserId: query.actor_user_id,
      startDate: query.start_date ? new Date(query.start_date) : undefined,
      endDate: query.end_date ? new Date(query.end_date) : undefined,
      limit: query.limit || 50,
      offset: query.offset || 0,
    };

    const result = await this.auditLogService.getAuditLogs(filters);

    return {
      success: true,
      data: result.logs,
      pagination: {
        total: result.total,
        limit: result.limit,
        offset: result.offset,
        hasMore: result.offset + result.limit < result.total,
      },
    };
  }

  @Get('stats')
  async getAuditStats(@Query() query: GetAuditStatsDto): Promise<AuditStatsResponseDto> {
    this.logger.log('Getting audit statistics', query);

    const filters = {
      tenantCode: query.tenant_code,
      startDate: query.start_date ? new Date(query.start_date) : undefined,
      endDate: query.end_date ? new Date(query.end_date) : undefined,
    };

    const stats = await this.auditLogService.getAuditStats(filters);

    return {
      success: true,
      data: stats,
    };
  }

  @Get('events/auth')
  async getAuthEvents(
    @Query('tenant_code') tenantCode?: string,
    @Query('start_date') startDate?: string,
    @Query('end_date') endDate?: string,
    @Query('limit') limit?: string,
    @Query('offset') offset?: string,
  ) {
    this.logger.log('Getting auth-specific audit events', {
      tenantCode,
      startDate,
      endDate,
      limit,
      offset,
    });

    const filters = {
      tenantCode,
      eventCategory: 'auth',
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      limit: limit ? parseInt(limit, 10) : undefined,
      offset: offset ? parseInt(offset, 10) : undefined,
    };

    const result = await this.auditLogService.getAuditLogs(filters);

    return {
      success: true,
      data: {
        logs: result.logs,
        pagination: {
          total: result.total,
          limit: result.limit,
          offset: result.offset,
          hasMore: result.offset + result.limit < result.total,
        },
      },
    };
  }

  @Get('events/user-registration')
  async getUserRegistrationEvents(
    @Query('tenant_code') tenantCode?: string,
    @Query('start_date') startDate?: string,
    @Query('end_date') endDate?: string,
    @Query('limit') limit?: string,
    @Query('offset') offset?: string,
  ) {
    this.logger.log('Getting user registration audit events', {
      tenantCode,
      startDate,
      endDate,
      limit,
      offset,
    });

    const filters = {
      tenantCode,
      eventType: 'USER_CREATED',
      eventCategory: 'auth',
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      limit: limit ? parseInt(limit, 10) : undefined,
      offset: offset ? parseInt(offset, 10) : undefined,
    };

    const result = await this.auditLogService.getAuditLogs(filters);

    return {
      success: true,
      data: {
        logs: result.logs,
        pagination: {
          total: result.total,
          limit: result.limit,
          offset: result.offset,
          hasMore: result.offset + result.limit < result.total,
        },
      },
    };
  }

  @Get('events/email-verification')
  async getEmailVerificationEvents(
    @Query('tenant_code') tenantCode?: string,
    @Query('start_date') startDate?: string,
    @Query('end_date') endDate?: string,
    @Query('limit') limit?: string,
    @Query('offset') offset?: string,
  ) {
    this.logger.log('Getting email verification audit events', {
      tenantCode,
      startDate,
      endDate,
      limit,
      offset,
    });

    const filters = {
      tenantCode,
      eventType: 'USER_EMAIL_VERIFIED',
      eventCategory: 'auth',
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      limit: limit ? parseInt(limit, 10) : undefined,
      offset: offset ? parseInt(offset, 10) : undefined,
    };

    const result = await this.auditLogService.getAuditLogs(filters);

    return {
      success: true,
      data: {
        logs: result.logs,
        pagination: {
          total: result.total,
          limit: result.limit,
          offset: result.offset,
          hasMore: result.offset + result.limit < result.total,
        },
      },
    };
  }
}
