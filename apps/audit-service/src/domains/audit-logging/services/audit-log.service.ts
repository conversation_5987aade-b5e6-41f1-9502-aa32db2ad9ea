/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable, Logger } from '@nestjs/common';
import { createId } from '@paralleldrive/cuid2';
import { PrismaService } from '../../../services/prisma.service';
import { CreateAuditLogData, IAuditLogService } from '../interfaces/audit-log.interface';

// CUID generation function for audit logs
function generateAuditLogId(): string {
  return `aul_${createId()}`;
}

@Injectable()
export class AuditLogService implements IAuditLogService {
  private readonly logger = new Logger(AuditLogService.name);

  constructor(private readonly prisma: PrismaService) {}

  async createAuditLog(data: CreateAuditLogData): Promise<void> {
    try {
      const auditLog = await this.prisma.auditLog.create({
        data: {
          id: generateAuditLogId(),
          eventType: data.eventType as any,
          eventCategory: data.eventCategory,
          severity: data.severity as any,
          status: 'PROCESSED',
          description: data.description,

          // Actor information
          actorUserId: data.actorUserId,
          actorEmail: data.actorUserEmail,
          actorType: data.actorUserId ? 'USER' : 'SYSTEM',
          actorIpAddress: data.ipAddress,
          actorUserAgent: data.userAgent,

          // Tenant information
          tenantCode: data.tenantCode,

          // Target information (resource)
          targetType: data.resourceType,
          targetId: data.resourceId,

          // Request context
          requestId: data.requestId,
          sessionId: data.sessionId,

          // Change details
          oldValues: data.changes?.before,
          newValues: data.changes?.after,

          // Additional data
          metadata: data.metadata || {},

          // System information
          serviceId: data.source,
          serviceVersion: '1.0.0',
          environment: process.env.NODE_ENV || 'development',

          // Timing
          timestamp: data.timestamp || new Date(),
          processedAt: new Date(),

          // Compliance flags (can be enhanced based on event type)
          complianceFlags: this.getComplianceFlags(data.eventType, data.eventCategory),
          retentionPolicy: this.getRetentionPolicy(data.eventCategory, data.severity),
        },
      });

      this.logger.debug(`Created audit log entry: ${auditLog.id}`, {
        eventType: data.eventType,
        eventCategory: data.eventCategory,
        severity: data.severity,
        actorUserId: data.actorUserId,
        tenantCode: data.tenantCode,
      });
    } catch (error) {
      this.logger.error('Failed to create audit log entry:', error, {
        eventType: data.eventType,
        eventCategory: data.eventCategory,
        actorUserId: data.actorUserId,
      });
      // Don't throw error to prevent event processing failure
    }
  }

  async getAuditLogs(filters: {
    tenantCode?: string;
    eventType?: string;
    eventCategory?: string;
    severity?: string;
    actorUserId?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  }) {
    const where: any = {};

    if (filters.tenantCode) {
      where.tenantCode = filters.tenantCode;
    }

    if (filters.eventType) {
      where.eventType = filters.eventType;
    }

    if (filters.eventCategory) {
      where.eventCategory = filters.eventCategory;
    }

    if (filters.severity) {
      where.severity = filters.severity;
    }

    if (filters.actorUserId) {
      where.actorUserId = filters.actorUserId;
    }

    if (filters.startDate || filters.endDate) {
      where.timestamp = {};
      if (filters.startDate) {
        where.timestamp.gte = filters.startDate;
      }
      if (filters.endDate) {
        where.timestamp.lte = filters.endDate;
      }
    }

    const [logs, total] = await Promise.all([
      this.prisma.auditLog.findMany({
        where,
        orderBy: { timestamp: 'desc' },
        take: filters.limit || 50,
        skip: filters.offset || 0,
      }),
      this.prisma.auditLog.count({ where }),
    ]);

    return {
      logs,
      total,
      limit: filters.limit || 50,
      offset: filters.offset || 0,
    };
  }

  async getAuditStats(filters: { tenantCode?: string; startDate?: Date; endDate?: Date }) {
    const where: any = {};

    if (filters.tenantCode) {
      where.tenantCode = filters.tenantCode;
    }

    if (filters.startDate || filters.endDate) {
      where.timestamp = {};
      if (filters.startDate) {
        where.timestamp.gte = filters.startDate;
      }
      if (filters.endDate) {
        where.timestamp.lte = filters.endDate;
      }
    }

    const [totalEntries, entries24h, entries7d, entries30d, statsByEventType, statsByCategory, statsBySeverity, statsBySource] = await Promise.all([
      this.prisma.auditLog.count({ where }),
      this.prisma.auditLog.count({
        where: {
          ...where,
          timestamp: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) },
        },
      }),
      this.prisma.auditLog.count({
        where: {
          ...where,
          timestamp: { gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) },
        },
      }),
      this.prisma.auditLog.count({
        where: {
          ...where,
          timestamp: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
        },
      }),
      this.prisma.auditLog.groupBy({
        by: ['eventType'],
        where,
        _count: { eventType: true },
      }),
      this.prisma.auditLog.groupBy({
        by: ['eventCategory'],
        where,
        _count: { eventCategory: true },
      }),
      this.prisma.auditLog.groupBy({
        by: ['severity'],
        where,
        _count: { severity: true },
      }),
      this.prisma.auditLog.groupBy({
        by: ['serviceId'],
        where,
        _count: { serviceId: true },
      }),
    ]);

    return {
      totalEntries,
      entries24h,
      entries7d,
      entries30d,
      statsByEventType: Object.fromEntries(statsByEventType.map((stat: any) => [stat.eventType, stat._count.eventType])),
      statsByCategory: Object.fromEntries(statsByCategory.map((stat: any) => [stat.eventCategory, stat._count.eventCategory])),
      statsBySeverity: Object.fromEntries(statsBySeverity.map((stat: any) => [stat.severity, stat._count.severity])),
      statsBySource: Object.fromEntries(statsBySource.map((stat: any) => [stat.serviceId || 'unknown', stat._count.serviceId])),
      calculatedAt: new Date(),
    };
  }

  private getComplianceFlags(eventType: string, eventCategory: string): string[] {
    const flags: string[] = [];

    // Add GDPR flag for user-related events
    if (eventCategory === 'auth' || eventType.includes('USER')) {
      flags.push('GDPR');
    }

    // Add SOX flag for financial or security events
    if (eventCategory === 'security' || eventType.includes('SECURITY')) {
      flags.push('SOX');
    }

    // Add HIPAA flag if dealing with health data (can be extended)
    if (eventCategory === 'health') {
      flags.push('HIPAA');
    }

    return flags;
  }

  private getRetentionPolicy(eventCategory: string, severity: string): string {
    // Define retention policies based on category and severity
    if (severity === 'CRITICAL' || severity === 'HIGH') {
      return 'LONG_TERM'; // 7 years
    }

    if (eventCategory === 'security' || eventCategory === 'auth') {
      return 'MEDIUM_TERM'; // 3 years
    }

    return 'SHORT_TERM'; // 1 year
  }
}
