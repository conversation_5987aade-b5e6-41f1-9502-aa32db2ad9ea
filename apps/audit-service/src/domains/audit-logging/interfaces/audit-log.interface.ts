/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * Interface for audit log creation data
 */
export interface CreateAuditLogData {
  eventType: string;
  eventCategory: string;
  severity: string;
  description: string;
  actorUserId?: string;
  actorUserEmail?: string;
  tenantCode?: string;
  source: string;
  ipAddress?: string;
  userAgent?: string;
  resourceType?: string;
  resourceId?: string;
  requestId?: string;
  sessionId?: string;
  metadata?: Record<string, any>;
  changes?: {
    before?: Record<string, any>;
    after?: Record<string, any>;
  };
  timestamp?: Date;
}

/**
 * Interface for audit log query filters
 */
export interface AuditLogFilters {
  tenantCode?: string;
  eventType?: string;
  eventCategory?: string;
  severity?: string;
  actorUserId?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

/**
 * Interface for audit log query results
 */
export interface AuditLogQueryResult {
  logs: any[];
  total: number;
  limit: number;
  offset: number;
}

/**
 * Interface for audit statistics
 */
export interface AuditStats {
  totalEntries: number;
  entries24h: number;
  entries7d: number;
  entries30d: number;
  statsByEventType: Record<string, number>;
  statsByCategory: Record<string, number>;
  statsBySeverity: Record<string, number>;
  statsBySource: Record<string, number>;
  calculatedAt: Date;
}

/**
 * Interface for audit log service operations
 */
export interface IAuditLogService {
  createAuditLog(data: CreateAuditLogData): Promise<void>;
  getAuditLogs(filters: AuditLogFilters): Promise<AuditLogQueryResult>;
  getAuditStats(filters: Partial<AuditLogFilters>): Promise<AuditStats>;
}
