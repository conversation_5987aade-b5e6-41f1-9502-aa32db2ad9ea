/* eslint-disable @typescript-eslint/no-explicit-any */
import { PaginatedListDataDto } from '@qeep/common';
import { Expose } from 'class-transformer';

/**
 * Audit log entry data DTO
 */
export class AuditLogDataDto {
  @Expose()
  id: string;

  @Expose({ name: 'event_type' })
  eventType: string;

  @Expose()
  category: string;

  @Expose()
  severity: string;

  @Expose()
  description: string;

  @Expose({ name: 'user_id' })
  userId?: string;

  @Expose({ name: 'user_email' })
  userEmail?: string;

  @Expose({ name: 'tenant_code' })
  tenantCode?: string;

  @Expose()
  source: string;

  @Expose({ name: 'resource_type' })
  resourceType?: string;

  @Expose({ name: 'resource_id' })
  resourceId?: string;

  @Expose({ name: 'ip_address' })
  ipAddress?: string;

  @Expose({ name: 'user_agent' })
  userAgent?: string;

  @Expose({ name: 'request_id' })
  requestId?: string;

  @Expose({ name: 'session_id' })
  sessionId?: string;

  @Expose()
  metadata?: Record<string, any>;

  @Expose()
  changes?: {
    before?: Record<string, any>;
    after?: Record<string, any>;
  };

  @Expose({ name: 'created_at' })
  createdAt: Date;
}

/**
 * Audit log list response data DTO
 */
export class AuditLogListDataDto extends PaginatedListDataDto<AuditLogDataDto> {
  @Expose()
  items: AuditLogDataDto[];
}

/**
 * Audit statistics data DTO
 */
export class AuditStatsDataDto {
  @Expose({ name: 'total_entries' })
  totalEntries: number;

  @Expose({ name: 'entries_24h' })
  entries24h: number;

  @Expose({ name: 'entries_7d' })
  entries7d: number;

  @Expose({ name: 'entries_30d' })
  entries30d: number;

  @Expose({ name: 'stats_by_event_type' })
  statsByEventType: Record<string, number>;

  @Expose({ name: 'stats_by_category' })
  statsByCategory: Record<string, number>;

  @Expose({ name: 'stats_by_severity' })
  statsBySeverity: Record<string, number>;

  @Expose({ name: 'stats_by_source' })
  statsBySource: Record<string, number>;

  @Expose({ name: 'top_users' })
  topUsers: Array<{
    user_id: string;
    user_email: string;
    entry_count: number;
  }>;

  @Expose({ name: 'calculated_at' })
  calculatedAt: Date;
}

/**
 * Audit report data DTO
 */
export class AuditReportDataDto {
  @Expose({ name: 'report_id' })
  reportId: string;

  @Expose()
  type: string;

  @Expose()
  title: string;

  @Expose({ name: 'period_start' })
  periodStart: Date;

  @Expose({ name: 'period_end' })
  periodEnd: Date;

  @Expose({ name: 'tenant_code' })
  tenantCode?: string;

  @Expose({ name: 'total_entries' })
  totalEntries: number;

  @Expose()
  summary: {
    total_events: number;
    unique_users: number;
    unique_ips: number;
    security_events: number;
    failed_logins: number;
    successful_logins: number;
  };

  @Expose()
  sections: Array<{
    title: string;
    description: string;
    data: any;
    chart_type?: string;
  }>;

  @Expose()
  format: string;

  @Expose({ name: 'file_url' })
  fileUrl?: string;

  @Expose({ name: 'generated_at' })
  generatedAt: Date;

  @Expose({ name: 'expires_at' })
  expiresAt?: Date;
}

/**
 * Audit search result data DTO
 */
export class AuditSearchDataDto {
  @Expose()
  query: string;

  @Expose()
  filters: {
    event_types?: string[];
    categories?: string[];
    severities?: string[];
    date_range?: {
      start: Date;
      end: Date;
    };
    user_ids?: string[];
    tenant_codes?: string[];
  };

  @Expose({ name: 'total_results' })
  totalResults: number;

  @Expose({ name: 'execution_time_ms' })
  executionTimeMs: number;

  @Expose()
  results: AuditLogDataDto[];

  @Expose()
  aggregations: {
    by_event_type: Record<string, number>;
    by_category: Record<string, number>;
    by_severity: Record<string, number>;
    by_user: Record<string, number>;
    by_date: Record<string, number>;
  };

  @Expose({ name: 'searched_at' })
  searchedAt: Date;
}

/**
 * Audit retention policy data DTO
 */
export class AuditRetentionDataDto {
  @Expose({ name: 'policy_id' })
  policyId: string;

  @Expose({ name: 'retention_days' })
  retentionDays: number;

  @Expose({ name: 'archive_after_days' })
  archiveAfterDays: number;

  @Expose({ name: 'auto_delete_enabled' })
  autoDeleteEnabled: boolean;

  @Expose({ name: 'compression_enabled' })
  compressionEnabled: boolean;

  @Expose({ name: 'storage_usage_gb' })
  storageUsageGb: number;

  @Expose({ name: 'archived_storage_gb' })
  archivedStorageGb: number;

  @Expose({ name: 'next_cleanup_at' })
  nextCleanupAt: Date;

  @Expose({ name: 'last_cleanup_at' })
  lastCleanupAt: Date;

  @Expose({ name: 'updated_at' })
  updatedAt: Date;
}

/**
 * Audit compliance report data DTO
 */
export class AuditComplianceDataDto {
  @Expose()
  framework: string;

  @Expose()
  status: string;

  @Expose()
  score: number;

  @Expose({ name: 'period_start' })
  periodStart: Date;

  @Expose({ name: 'period_end' })
  periodEnd: Date;

  @Expose()
  requirements: Array<{
    requirement_id: string;
    description: string;
    status: string;
    score: number;
    evidence_count: number;
  }>;

  @Expose()
  issues: Array<{
    issue_id: string;
    severity: string;
    description: string;
    recommendation: string;
    affected_entries: number;
  }>;

  @Expose({ name: 'assessed_at' })
  assessedAt: Date;
}
