/* eslint-disable @typescript-eslint/no-explicit-any */
import { Transform } from 'class-transformer';
import { IsDateString, IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';

/**
 * DTO for audit log query parameters
 */
export class GetAuditLogsDto {
  @IsOptional()
  @IsString()
  tenant_code?: string;

  @IsOptional()
  @IsString()
  event_type?: string;

  @IsOptional()
  @IsString()
  event_category?: string;

  @IsOptional()
  @IsEnum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'])
  severity?: string;

  @IsOptional()
  @IsString()
  actor_user_id?: string;

  @IsOptional()
  @IsDateString()
  start_date?: string;

  @IsOptional()
  @IsDateString()
  end_date?: string;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  limit?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  offset?: number;
}

/**
 * DTO for audit statistics query parameters
 */
export class GetAuditStatsDto {
  @IsOptional()
  @IsString()
  tenant_code?: string;

  @IsOptional()
  @IsDateString()
  start_date?: string;

  @IsOptional()
  @IsDateString()
  end_date?: string;
}

/**
 * DTO for audit log response
 */
export class AuditLogResponseDto {
  success: boolean;
  data: any[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

/**
 * DTO for audit statistics response
 */
export class AuditStatsResponseDto {
  success: boolean;
  data: {
    totalEntries: number;
    entries24h: number;
    entries7d: number;
    entries30d: number;
    statsByEventType: Record<string, number>;
    statsByCategory: Record<string, number>;
    statsBySeverity: Record<string, number>;
    statsBySource: Record<string, number>;
    calculatedAt: Date;
  };
}

/**
 * DTO for creating audit log entries (internal use)
 */
export class CreateAuditLogDto {
  @IsString()
  eventType: string;

  @IsString()
  eventCategory: string;

  @IsEnum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'])
  severity: string;

  @IsString()
  description: string;

  @IsOptional()
  @IsString()
  actorUserId?: string;

  @IsOptional()
  @IsString()
  actorUserEmail?: string;

  @IsOptional()
  @IsString()
  tenantCode?: string;

  @IsString()
  source: string;

  @IsOptional()
  @IsString()
  ipAddress?: string;

  @IsOptional()
  @IsString()
  userAgent?: string;

  @IsOptional()
  @IsString()
  resourceType?: string;

  @IsOptional()
  @IsString()
  resourceId?: string;

  @IsOptional()
  @IsString()
  requestId?: string;

  @IsOptional()
  @IsString()
  sessionId?: string;

  @IsOptional()
  metadata?: Record<string, any>;

  @IsOptional()
  changes?: {
    before?: Record<string, any>;
    after?: Record<string, any>;
  };

  @IsOptional()
  @IsDateString()
  timestamp?: Date;
}
