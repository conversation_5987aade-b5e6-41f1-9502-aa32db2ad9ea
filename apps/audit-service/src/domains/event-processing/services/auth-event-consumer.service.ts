/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable, Logger, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@qeep/common';
import { Consumer, EachMessagePayload, Kafka } from 'kafkajs';
import { AuditLogService } from '../../audit-logging/services/audit-log.service';

@Injectable()
export class AuthEventConsumer implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(AuthEventConsumer.name);
  private kafka: Kafka;
  private consumer: Consumer;

  constructor(private readonly configService: ConfigService, private readonly auditLogService: AuditLogService) {}

  async onModuleInit() {
    try {
      // Initialize Kafka client
      const kafkaConfig = this.configService.getKafkaConfig();
      this.kafka = new Kafka({
        clientId: `${kafkaConfig.clientId}-audit-consumer`,
        brokers: kafkaConfig.brokers,
        retry: {
          initialRetryTime: 100,
          retries: 8,
        },
      });

      // Create consumer
      this.consumer = this.kafka.consumer({
        groupId: `${kafkaConfig.groupId}-audit`,
        sessionTimeout: 30000,
        heartbeatInterval: 3000,
      });

      // Connect consumer
      await this.consumer.connect();
      this.logger.log('Kafka consumer connected successfully');

      // Subscribe to auth events
      await this.consumer.subscribe({
        topics: [
          'auth.user.registered',
          'auth.email.verification.initiated',
          'auth.email.verification.completed',
          'auth.welcome.email.requested',
          'auth.user.onboarding.started',
          'auth.audit',
        ],
        fromBeginning: false,
      });

      // Start consuming messages
      await this.consumer.run({
        eachMessage: async (payload: EachMessagePayload) => {
          await this.handleMessage(payload);
        },
      });

      this.logger.log('Auth event consumer started successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Kafka consumer:', error);
      // Don't throw error to prevent service startup failure
    }
  }

  async onModuleDestroy() {
    try {
      if (this.consumer) {
        await this.consumer.disconnect();
        this.logger.log('Kafka consumer disconnected');
      }
    } catch (error) {
      this.logger.error('Error disconnecting Kafka consumer:', error);
    }
  }

  private async handleMessage(payload: EachMessagePayload): Promise<void> {
    const { topic, partition, message } = payload;

    try {
      if (!message.value) {
        this.logger.warn(`Received empty message from topic ${topic}`);
        return;
      }

      const eventData = JSON.parse(message.value.toString());
      this.logger.debug(`Processing message from topic ${topic}:`, {
        partition,
        offset: message.offset,
        eventId: eventData.eventId,
      });

      // Route message to appropriate handler based on topic
      switch (topic) {
        case 'auth.user.registered':
          await this.handleUserRegisteredEvent(eventData);
          break;
        case 'auth.email.verification.initiated':
          await this.handleEmailVerificationInitiatedEvent(eventData);
          break;
        case 'auth.email.verification.completed':
          await this.handleEmailVerificationCompletedEvent(eventData);
          break;
        case 'auth.welcome.email.requested':
          await this.handleWelcomeEmailRequestedEvent(eventData);
          break;
        case 'auth.user.onboarding.started':
          await this.handleUserOnboardingStartedEvent(eventData);
          break;
        case 'auth.audit':
          await this.handleAuthAuditEvent(eventData);
          break;
        default:
          this.logger.warn(`Unknown topic: ${topic}`);
      }
    } catch (error) {
      this.logger.error(`Error processing message from topic ${topic}:`, error);
      // Don't throw error to prevent consumer from stopping
    }
  }

  private async handleUserRegisteredEvent(eventData: any): Promise<void> {
    await this.auditLogService.createAuditLog({
      eventType: 'USER_CREATED',
      eventCategory: 'auth',
      severity: 'MEDIUM',
      description: `User registered: ${eventData.email}`,
      actorUserId: eventData.userId,
      actorUserEmail: eventData.email,
      tenantCode: eventData.tenantCode,
      source: eventData.source || 'auth-service',
      metadata: {
        eventId: eventData.eventId,
        firstName: eventData.firstName,
        lastName: eventData.lastName,
        status: eventData.status,
        acceptMarketingEmails: eventData.metadata?.acceptMarketingEmails,
        registrationIp: eventData.metadata?.registrationIp,
        userAgent: eventData.metadata?.userAgent,
      },
      timestamp: new Date(eventData.timestamp),
    });

    this.logger.log(`Logged user registration event for: ${eventData.email}`);
  }

  private async handleEmailVerificationInitiatedEvent(eventData: any): Promise<void> {
    await this.auditLogService.createAuditLog({
      eventType: 'USER_EMAIL_VERIFIED',
      eventCategory: 'auth',
      severity: 'LOW',
      description: `Email verification initiated for: ${eventData.email}`,
      actorUserId: eventData.userId,
      actorUserEmail: eventData.email,
      source: eventData.source || 'auth-service',
      metadata: {
        eventId: eventData.eventId,
        verificationToken: eventData.verificationToken,
        expiresAt: eventData.expiresAt,
        resend: eventData.metadata?.resend,
        attemptNumber: eventData.metadata?.attemptNumber,
      },
      timestamp: new Date(eventData.timestamp),
    });

    this.logger.log(`Logged email verification initiation for: ${eventData.email}`);
  }

  private async handleEmailVerificationCompletedEvent(eventData: any): Promise<void> {
    await this.auditLogService.createAuditLog({
      eventType: 'USER_EMAIL_VERIFIED',
      eventCategory: 'auth',
      severity: 'MEDIUM',
      description: `Email verification completed for: ${eventData.email}`,
      actorUserId: eventData.userId,
      actorUserEmail: eventData.email,
      source: eventData.source || 'auth-service',
      ipAddress: eventData.metadata?.verificationIp,
      userAgent: eventData.metadata?.userAgent,
      metadata: {
        eventId: eventData.eventId,
        verificationToken: eventData.verificationToken,
        completedAt: eventData.completedAt,
      },
      timestamp: new Date(eventData.timestamp),
    });

    this.logger.log(`Logged email verification completion for: ${eventData.email}`);
  }

  private async handleWelcomeEmailRequestedEvent(eventData: any): Promise<void> {
    await this.auditLogService.createAuditLog({
      eventType: 'USER_UPDATED',
      eventCategory: 'auth',
      severity: 'LOW',
      description: `Welcome email requested for: ${eventData.email}`,
      actorUserId: eventData.userId,
      actorUserEmail: eventData.email,
      source: eventData.source || 'auth-service',
      metadata: {
        eventId: eventData.eventId,
        firstName: eventData.firstName,
        triggerType: eventData.metadata?.triggerType,
        templateData: eventData.metadata?.templateData,
      },
      timestamp: new Date(eventData.timestamp),
    });

    this.logger.log(`Logged welcome email request for: ${eventData.email}`);
  }

  private async handleUserOnboardingStartedEvent(eventData: any): Promise<void> {
    await this.auditLogService.createAuditLog({
      eventType: 'USER_UPDATED',
      eventCategory: 'auth',
      severity: 'LOW',
      description: `User onboarding started for: ${eventData.email}`,
      actorUserId: eventData.userId,
      actorUserEmail: eventData.email,
      tenantCode: eventData.tenantCode,
      source: eventData.source || 'auth-service',
      metadata: {
        eventId: eventData.eventId,
        nextSteps: eventData.nextSteps,
        onboardingFlow: eventData.metadata?.onboardingFlow,
        referralSource: eventData.metadata?.referralSource,
      },
      timestamp: new Date(eventData.timestamp),
    });

    this.logger.log(`Logged user onboarding start for: ${eventData.email}`);
  }

  private async handleAuthAuditEvent(eventData: any): Promise<void> {
    // Map auth audit events to audit log entries
    const eventTypeMap: Record<string, string> = {
      USER_SIGNUP_INITIATED: 'USER_CREATED',
      USER_SIGNUP_COMPLETED: 'USER_CREATED',
      USER_SIGNUP_FAILED: 'USER_CREATED',
    };

    const severityMap: Record<string, string> = {
      success: 'MEDIUM',
      failure: 'HIGH',
      pending: 'LOW',
    };

    await this.auditLogService.createAuditLog({
      eventType: eventTypeMap[eventData.action] || 'SYSTEM_CONFIGURATION_CHANGED',
      eventCategory: 'auth',
      severity: severityMap[eventData.result] || 'MEDIUM',
      description: `${eventData.action}: ${eventData.result}`,
      actorUserId: eventData.userId,
      actorUserEmail: eventData.email,
      tenantCode: eventData.tenantCode,
      source: eventData.source || 'auth-service',

      // Actor information - ensure string types
      ipAddress: this.ensureStringOrNull(eventData.ip || eventData.metadata?.ip),
      userAgent: this.ensureStringOrNull(eventData.userAgent || eventData.metadata?.userAgent),

      // Target information
      resourceType: eventData.targetType,
      resourceId: eventData.targetId,

      metadata: {
        eventId: eventData.eventId,
        action: eventData.action,
        result: eventData.result,
        errorCode: eventData.errorCode || eventData.metadata?.errorCode,
        errorMessage: eventData.errorMessage || eventData.metadata?.errorMessage,
        additionalData: eventData.additionalData || eventData.metadata?.additionalData,
        targetName: eventData.targetName,
        actorLocation: eventData.location,
      },

      // Change tracking
      changes:
        eventData.oldValues || eventData.newValues
          ? {
              before: eventData.oldValues,
              after: eventData.newValues,
            }
          : undefined,

      timestamp: new Date(eventData.timestamp),
    });

    this.logger.log(`Logged auth audit event: ${eventData.action} - ${eventData.result}`);
  }

  /**
   * Ensure a value is a string or null, not an object
   */
  private ensureStringOrNull(value: any): string | null {
    if (value === null || value === undefined) {
      return null;
    }

    if (typeof value === 'string') {
      return value;
    }

    if (typeof value === 'object') {
      // If it's an object, try to extract a meaningful string value
      if (value.toString && typeof value.toString === 'function') {
        const stringValue = value.toString();
        // Avoid "[object Object]" strings
        if (stringValue !== '[object Object]') {
          return stringValue;
        }
      }

      // If it's an object with an ip property, extract it
      if (value.ip && typeof value.ip === 'string') {
        return value.ip;
      }

      // If it's an object with an address property, extract it
      if (value.address && typeof value.address === 'string') {
        return value.address;
      }

      // Log the problematic object for debugging
      this.logger.warn(`Received object instead of string for IP/UserAgent: ${JSON.stringify(value)}`);
      return null;
    }

    // Convert other types to string
    return String(value);
  }
}
