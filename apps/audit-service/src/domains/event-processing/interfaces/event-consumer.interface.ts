import { EachMessagePayload } from 'kafkajs';

/**
 * Interface for raw event data from Kafka
 */
export interface RawEventData {
  eventId?: string;
  timestamp: Date;
  source: string;
  version: string;
  [key: string]: any;
}

/**
 * Interface for processed event data ready for audit logging
 */
export interface ProcessedEventData {
  eventType: string;
  eventCategory: string;
  severity: string;
  description: string;
  actorUserId?: string;
  actorUserEmail?: string;
  tenantCode?: string;
  source: string;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

/**
 * Interface for event processing context
 */
export interface EventProcessingContext {
  topic: string;
  partition: number;
  offset: string;
  timestamp: Date;
  correlationId?: string;
}

/**
 * Interface for event consumer operations
 */
export interface IEventConsumer {
  handleMessage(payload: EachMessagePayload): Promise<void>;
  processEvent(eventData: RawEventData, context: EventProcessingContext): Promise<ProcessedEventData>;
  routeEvent(processedEvent: ProcessedEventData): Promise<void>;
}

/**
 * Interface for event transformation rules
 */
export interface EventTransformationRule {
  eventType: string;
  targetCategory: string;
  targetSeverity: string;
  descriptionTemplate: string;
  metadataMapping?: Record<string, string>;
}

/**
 * Interface for event routing configuration
 */
export interface EventRoutingConfig {
  topic: string;
  eventTypes: string[];
  targetDomain: string;
  processingOptions?: {
    batchSize?: number;
    retryAttempts?: number;
    deadLetterQueue?: boolean;
  };
}
