import { Module } from '@nestjs/common';
import { CommonModule, ConfigModule } from '@qeep/common';
import { AppController } from './app/app.controller';
import { AppService } from './app/app.service';
import { AuditLoggingModule } from './domains/audit-logging/audit-logging.module';
import { EventProcessingModule } from './domains/event-processing/event-processing.module';

@Module({
  imports: [
    ConfigModule,
    CommonModule.forRoot({
      enableTelemetry: false,
      enableGlobalTenantInterceptor: false,
      enableCircuitBreaker: false,
      enableRateLimiting: false,
      enableSecurityHeaders: false,
    }),
    AuditLoggingModule,
    EventProcessingModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
