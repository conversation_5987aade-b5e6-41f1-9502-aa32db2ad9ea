// Audit Service Prisma Schema
// Manages comprehensive audit logs and compliance tracking

generator client {
  provider = "prisma-client-js"
  output   = "../../../node_modules/.prisma/audit-client"
}

datasource db {
  provider = "postgresql"
  url      = env("AUDIT_DATABASE_URL")
}

// ============================================================================
// AUDIT LOG MANAGEMENT
// ============================================================================

enum AuditEventType {
  // User Management
  USER_CREATED
  USER_UPDATED
  USER_DELETED
  USER_LOGIN
  USER_LOGOUT
  USER_PASSWORD_CHANGED
  USER_EMAIL_VERIFIED
  USER_ACCOUNT_LOCKED
  USER_ACCOUNT_UNLOCKED
  
  // Role & Permission Management
  ROLE_ASSIGNED
  ROLE_REMOVED
  PERMISSION_GRANTED
  PERMISSION_REVOKED
  
  // Tenant Management
  TENANT_CREATED
  TENANT_UPDATED
  TENANT_DELETED
  TENANT_SETTINGS_CHANGED
  
  // Data Access
  DATA_READ
  DATA_CREATED
  DATA_UPDATED
  DATA_DELETED
  DATA_EXPORTED
  DATA_IMPORTED
  
  // Security Events
  SECURITY_BREACH_DETECTED
  SUSPICIOUS_ACTIVITY
  FAILED_LOGIN_ATTEMPT
  UNAUTHORIZED_ACCESS_ATTEMPT
  
  // System Events
  SYSTEM_CONFIGURATION_CHANGED
  BACKUP_CREATED
  BACKUP_RESTORED
  MAINTENANCE_STARTED
  MAINTENANCE_COMPLETED
  
  // API Events
  API_KEY_CREATED
  API_KEY_REVOKED
  API_RATE_LIMIT_EXCEEDED
  API_UNAUTHORIZED_ACCESS
  
  // Compliance Events
  GDPR_DATA_REQUEST
  GDPR_DATA_DELETION
  COMPLIANCE_REPORT_GENERATED
  REGULATORY_SUBMISSION
}

enum AuditSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum AuditStatus {
  PENDING
  PROCESSED
  ARCHIVED
  FLAGGED
}

// ============================================================================
// COMPREHENSIVE AUDIT LOG
// ============================================================================

model AuditLog {
  id              String          @id @db.VarChar(35)
  tenantId        String?         @map("tenant_id") @db.VarChar(35)
  tenantCode      String?         @map("tenant_code") @db.VarChar(50)
  
  // Event Details
  eventType       AuditEventType  @map("event_type")
  eventCategory   String          @map("event_category") @db.VarChar(50) // auth, user, tenant, data, security, system, api, compliance
  severity        AuditSeverity   @default(LOW)
  status          AuditStatus     @default(PENDING)
  
  // Actor Information (who performed the action)
  actorUserId     String?         @map("actor_user_id") @db.VarChar(35)
  actorEmail      String?         @map("actor_email") @db.VarChar(255)
  actorType       String?         @map("actor_type") @db.VarChar(50) // user, system, api, service
  actorIpAddress  String?         @map("actor_ip_address") @db.VarChar(45)
  actorUserAgent  String?         @map("actor_user_agent") @db.Text
  actorLocation   Json?           @map("actor_location")
  
  // Target Information (what was affected)
  targetType      String?         @map("target_type") @db.VarChar(50) // user, tenant, role, permission, data, etc.
  targetId        String?         @map("target_id") @db.VarChar(255)
  targetName      String?         @map("target_name") @db.VarChar(255)
  
  // Change Details
  oldValues       Json?           @map("old_values") // Previous state
  newValues       Json?           @map("new_values") // New state
  changedFields   String[]        @map("changed_fields") // List of fields that changed
  
  // Context & Metadata
  description     String          @db.Text
  metadata        Json?           @map("metadata") // Additional context-specific data
  correlationId   String?         @map("correlation_id") @db.VarChar(255) // For tracking related events
  sessionId       String?         @map("session_id") @db.VarChar(35)
  requestId       String?         @map("request_id") @db.VarChar(255)
  
  // Compliance & Regulatory
  complianceFlags String[]        @map("compliance_flags") // GDPR, SOX, HIPAA, etc.
  retentionPolicy String?         @map("retention_policy") @db.VarChar(50)
  
  // Timing
  timestamp       DateTime        @default(now()) @db.Timestamptz(6)
  processedAt     DateTime?       @map("processed_at") @db.Timestamptz(6)
  archivedAt      DateTime?       @map("archived_at") @db.Timestamptz(6)
  
  // System Information
  serviceId       String?         @map("service_id") @db.VarChar(50) // Which service generated this log
  serviceVersion  String?         @map("service_version") @db.VarChar(20)
  environment     String?         @map("environment") @db.VarChar(20) // dev, staging, prod
  
  @@index([tenantId], map: "idx_audit_logs_tenant_id")
  @@index([tenantCode], map: "idx_audit_logs_tenant_code")
  @@index([eventType], map: "idx_audit_logs_event_type")
  @@index([eventCategory], map: "idx_audit_logs_event_category")
  @@index([severity], map: "idx_audit_logs_severity")
  @@index([status], map: "idx_audit_logs_status")
  @@index([actorUserId], map: "idx_audit_logs_actor_user_id")
  @@index([actorEmail], map: "idx_audit_logs_actor_email")
  @@index([actorType], map: "idx_audit_logs_actor_type")
  @@index([targetType], map: "idx_audit_logs_target_type")
  @@index([targetId], map: "idx_audit_logs_target_id")
  @@index([correlationId], map: "idx_audit_logs_correlation_id")
  @@index([sessionId], map: "idx_audit_logs_session_id")
  @@index([timestamp], map: "idx_audit_logs_timestamp")
  @@index([processedAt], map: "idx_audit_logs_processed_at")
  @@index([serviceId], map: "idx_audit_logs_service_id")
  @@index([environment], map: "idx_audit_logs_environment")
  @@index([tenantId, eventType], map: "idx_audit_logs_tenant_event_type")
  @@index([tenantId, timestamp], map: "idx_audit_logs_tenant_timestamp")
  @@index([actorUserId, timestamp], map: "idx_audit_logs_actor_timestamp")
  @@index([eventType, timestamp], map: "idx_audit_logs_event_type_timestamp")
  @@index([severity, timestamp], map: "idx_audit_logs_severity_timestamp")
  @@map("audit_logs")
}

// ============================================================================
// AUDIT TRAIL SUMMARIES
// ============================================================================

model AuditSummary {
  id              String    @id @db.VarChar(35)
  tenantId        String?   @map("tenant_id") @db.VarChar(35)
  tenantCode      String?   @map("tenant_code") @db.VarChar(50)
  
  // Summary Period
  periodType      String    @map("period_type") @db.VarChar(20) // daily, weekly, monthly
  periodStart     DateTime  @map("period_start") @db.Timestamptz(6)
  periodEnd       DateTime  @map("period_end") @db.Timestamptz(6)
  
  // Event Counts by Category
  authEvents      Int       @default(0) @map("auth_events")
  userEvents      Int       @default(0) @map("user_events")
  tenantEvents    Int       @default(0) @map("tenant_events")
  dataEvents      Int       @default(0) @map("data_events")
  securityEvents  Int       @default(0) @map("security_events")
  systemEvents    Int       @default(0) @map("system_events")
  apiEvents       Int       @default(0) @map("api_events")
  complianceEvents Int      @default(0) @map("compliance_events")
  
  // Severity Counts
  lowSeverity     Int       @default(0) @map("low_severity")
  mediumSeverity  Int       @default(0) @map("medium_severity")
  highSeverity    Int       @default(0) @map("high_severity")
  criticalSeverity Int      @default(0) @map("critical_severity")
  
  // Additional Metrics
  uniqueUsers     Int       @default(0) @map("unique_users")
  uniqueIpAddresses Int     @default(0) @map("unique_ip_addresses")
  failedAttempts  Int       @default(0) @map("failed_attempts")
  
  // Metadata
  metadata        Json?     @map("metadata")
  createdAt       DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  
  @@unique([tenantId, periodType, periodStart], map: "idx_audit_summary_unique_period")
  @@index([tenantId], map: "idx_audit_summary_tenant_id")
  @@index([tenantCode], map: "idx_audit_summary_tenant_code")
  @@index([periodType], map: "idx_audit_summary_period_type")
  @@index([periodStart], map: "idx_audit_summary_period_start")
  @@index([periodEnd], map: "idx_audit_summary_period_end")
  @@map("audit_summaries")
}

// ============================================================================
// COMPLIANCE REPORTS
// ============================================================================

enum ReportStatus {
  PENDING
  GENERATING
  COMPLETED
  FAILED
  ARCHIVED
}

enum ReportType {
  GDPR_COMPLIANCE
  SOX_COMPLIANCE
  HIPAA_COMPLIANCE
  SECURITY_AUDIT
  USER_ACTIVITY
  DATA_ACCESS
  CUSTOM
}

model ComplianceReport {
  id              String       @id @db.VarChar(35)
  tenantId        String?      @map("tenant_id") @db.VarChar(35)
  tenantCode      String?      @map("tenant_code") @db.VarChar(50)
  
  // Report Details
  reportType      ReportType   @map("report_type")
  reportName      String       @map("report_name") @db.VarChar(255)
  description     String?      @db.Text
  status          ReportStatus @default(PENDING)
  
  // Time Range
  startDate       DateTime     @map("start_date") @db.Timestamptz(6)
  endDate         DateTime     @map("end_date") @db.Timestamptz(6)
  
  // Filters & Parameters
  filters         Json?        @map("filters") // Report-specific filters
  parameters      Json?        @map("parameters") // Report configuration
  
  // Results
  recordCount     Int?         @map("record_count")
  fileSize        Int?         @map("file_size") // in bytes
  filePath        String?      @map("file_path") @db.VarChar(500)
  downloadUrl     String?      @map("download_url") @db.VarChar(500)
  
  // Metadata
  requestedBy     String?      @map("requested_by") @db.VarChar(35)
  requestedAt     DateTime     @default(now()) @map("requested_at") @db.Timestamptz(6)
  generatedAt     DateTime?    @map("generated_at") @db.Timestamptz(6)
  expiresAt       DateTime?    @map("expires_at") @db.Timestamptz(6)
  
  // Error Handling
  errorMessage    String?      @map("error_message") @db.Text
  retryCount      Int          @default(0) @map("retry_count")
  
  @@index([tenantId], map: "idx_compliance_reports_tenant_id")
  @@index([tenantCode], map: "idx_compliance_reports_tenant_code")
  @@index([reportType], map: "idx_compliance_reports_report_type")
  @@index([status], map: "idx_compliance_reports_status")
  @@index([requestedBy], map: "idx_compliance_reports_requested_by")
  @@index([requestedAt], map: "idx_compliance_reports_requested_at")
  @@index([startDate, endDate], map: "idx_compliance_reports_date_range")
  @@map("compliance_reports")
}
