import { createId } from '@paralleldrive/cuid2';
import { PrismaClient } from '../../../node_modules/.prisma/audit-client/index.js';

const prisma = new PrismaClient();

// CUID generation functions
function generateAuditLogId(): string {
  return `aul_${createId()}`;
}

function generateAuditSummaryId(): string {
  return `aus_${createId()}`;
}

function generateComplianceReportId(): string {
  return `cor_${createId()}`;
}

async function main() {
  console.log('🌱 Seeding Audit Service database...');

  // Create some sample audit logs to demonstrate the CUID functionality
  console.log('📝 Creating sample audit logs...');

  const sampleAuditLogs = [
    {
      id: generateAuditLogId(),
      eventType: 'USER_LOGIN' as any,
      eventCategory: 'auth',
      severity: 'LOW' as any,
      status: 'PROCESSED' as any,
      description: 'User successfully logged in',
      actorType: 'USER',
      serviceId: 'auth-service',
      serviceVersion: '1.0.0',
      environment: 'development',
      timestamp: new Date(),
      processedAt: new Date(),
      complianceFlags: ['GDPR'],
      retentionPolicy: 'standard',
    },
    {
      id: generateAuditLogId(),
      eventType: 'USER_CREATED' as any,
      eventCategory: 'auth',
      severity: 'MEDIUM' as any,
      status: 'PROCESSED' as any,
      description: 'New user account created',
      actorType: 'SYSTEM',
      serviceId: 'user-service',
      serviceVersion: '1.0.0',
      environment: 'development',
      timestamp: new Date(),
      processedAt: new Date(),
      complianceFlags: ['GDPR', 'SOX'],
      retentionPolicy: 'extended',
    },
    {
      id: generateAuditLogId(),
      eventType: 'SECURITY_BREACH_DETECTED' as any,
      eventCategory: 'security',
      severity: 'CRITICAL' as any,
      status: 'PROCESSED' as any,
      description: 'Potential security breach detected',
      actorType: 'SYSTEM',
      serviceId: 'monitoring-service',
      serviceVersion: '1.0.0',
      environment: 'development',
      timestamp: new Date(),
      processedAt: new Date(),
      complianceFlags: ['SOX', 'HIPAA'],
      retentionPolicy: 'permanent',
    },
  ];

  for (const auditLog of sampleAuditLogs) {
    await prisma.auditLog.create({ data: auditLog });
  }

  console.log(`✅ Created ${sampleAuditLogs.length} sample audit logs`);

  // Create a sample audit summary
  console.log('📊 Creating sample audit summary...');

  const auditSummary = await prisma.auditSummary.create({
    data: {
      id: generateAuditSummaryId(),
      periodType: 'daily',
      periodStart: new Date(Date.now() - 24 * 60 * 60 * 1000), // 24 hours ago
      periodEnd: new Date(),
      authEvents: 15,
      userEvents: 8,
      securityEvents: 2,
      systemEvents: 5,
      lowSeverity: 20,
      mediumSeverity: 8,
      highSeverity: 1,
      criticalSeverity: 1,
      uniqueUsers: 12,
      uniqueIpAddresses: 8,
      failedAttempts: 3,
      metadata: {
        generatedBy: 'audit-service',
        version: '1.0.0',
      },
    },
  });

  console.log(`✅ Created audit summary: ${auditSummary.id}`);

  // Create a sample compliance report
  console.log('📋 Creating sample compliance report...');

  const complianceReport = await prisma.complianceReport.create({
    data: {
      id: generateComplianceReportId(),
      reportType: 'GDPR_COMPLIANCE' as any,
      reportName: 'Monthly GDPR Compliance Report',
      description: 'Comprehensive GDPR compliance report for the current month',
      status: 'COMPLETED' as any,
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      endDate: new Date(),
      recordCount: 1250,
      fileSize: 2048576, // 2MB
      filePath: '/reports/gdpr/monthly-2025-07.pdf',
      requestedAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      generatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
      expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
      retryCount: 0,
      filters: {
        eventCategories: ['auth', 'user', 'data'],
        severityLevels: ['MEDIUM', 'HIGH', 'CRITICAL'],
      },
      parameters: {
        includePersonalData: false,
        anonymizeUserIds: true,
        format: 'PDF',
      },
    },
  });

  console.log(`✅ Created compliance report: ${complianceReport.id}`);

  console.log('');
  console.log('📊 Audit Service Database Summary:');
  console.log('  - 3 sample audit logs with CUID format (aul_...)');
  console.log('  - 1 sample audit summary with CUID format (aus_...)');
  console.log('  - 1 sample compliance report with CUID format (cor_...)');
  console.log('  - Ready to collect audit logs from all services');
  console.log('  - Supports comprehensive event tracking');
  console.log('  - Includes GDPR, SOX, and HIPAA compliance features');
  console.log('');
  console.log('🎉 All audit service entities now use prefixed CUID format!');
  console.log('💡 Note: Audit events will be generated by other services during normal operation');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding audit service database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
