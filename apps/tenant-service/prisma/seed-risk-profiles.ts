/* eslint-disable @nx/enforce-module-boundaries */

import { createId } from '@paralleldrive/cuid2';
import { PrismaClient } from '../../../node_modules/.prisma/tenant-client/index.js';

// CUID generation functions for risk profiles
function generateRiskProfileId(): string {
  return `rp_${createId()}`.substring(0, 35);
}

function generateRuleId(): string {
  return `rule_${createId()}`.substring(0, 35);
}

const prisma = new PrismaClient();

async function main() {
  console.log('🛡️  Seeding Risk Profile Management System...');

  // Get all existing tenants
  const tenants = await prisma.tenant.findMany({
    where: { deletedAt: null },
    select: { id: true, name: true }
  });

  if (tenants.length === 0) {
    console.log('❌ No tenants found. Please run tenant seeding first.');
    process.exit(1);
  }

  console.log(`📊 Found ${tenants.length} tenants to create risk profiles for`);

  for (const tenant of tenants) {
    console.log(`\n🏢 Creating risk profiles for tenant: ${tenant.name}`);
    
    // Standard Risk Profile (LOW risk customers)
    const standardProfile = await prisma.riskProfile.upsert({
      where: {
        tenantId_name: {
          tenantId: tenant.id,
          name: 'Standard Risk Profile'
        }
      },
      update: {
        description: 'Default profile for customers with LOW risk level. Applies standard monitoring and compliance rules.',
        isActive: true,
        metadata: {
          auto_assignment_criteria: {
            customer_risk_level: 'LOW'
          },
          system_profile_type: 'STANDARD',
          created_by_system: true
        }
      },
      create: {
        id: generateRiskProfileId(),
        tenantId: tenant.id,
        name: 'Standard Risk Profile',
        description: 'Default profile for customers with LOW risk level. Applies standard monitoring and compliance rules.',
        ruleCategory: 'TRANSACTION',
        isActive: true,
        isSystemGenerated: true,
        priority: 100,
        assignmentPriority: 100,
        metadata: {
          auto_assignment_criteria: {
            customer_risk_level: 'LOW'
          },
          system_profile_type: 'STANDARD',
          created_by_system: true
        },
        createdBy: 'SYSTEM_AUTO_GENERATED'
      }
    });

    // Enhanced Monitoring Profile (MEDIUM risk customers)
    const enhancedProfile = await prisma.riskProfile.upsert({
      where: {
        tenantId_name: {
          tenantId: tenant.id,
          name: 'Enhanced Monitoring Profile'
        }
      },
      update: {
        description: 'Default profile for customers with MEDIUM risk level. Applies enhanced monitoring and additional compliance checks.',
        isActive: true,
        metadata: {
          auto_assignment_criteria: {
            customer_risk_level: 'MEDIUM'
          },
          system_profile_type: 'ENHANCED',
          created_by_system: true
        }
      },
      create: {
        id: generateRiskProfileId(),
        tenantId: tenant.id,
        name: 'Enhanced Monitoring Profile',
        description: 'Default profile for customers with MEDIUM risk level. Applies enhanced monitoring and additional compliance checks.',
        ruleCategory: 'BEHAVIORAL',
        isActive: true,
        isSystemGenerated: true,
        priority: 200,
        assignmentPriority: 200,
        metadata: {
          auto_assignment_criteria: {
            customer_risk_level: 'MEDIUM'
          },
          system_profile_type: 'ENHANCED',
          created_by_system: true
        },
        createdBy: 'SYSTEM_AUTO_GENERATED'
      }
    });

    // High Risk Profile (HIGH/CRITICAL risk customers)
    const highRiskProfile = await prisma.riskProfile.upsert({
      where: {
        tenantId_name: {
          tenantId: tenant.id,
          name: 'High Risk Profile'
        }
      },
      update: {
        description: 'Default profile for customers with HIGH or CRITICAL risk level. Applies strict monitoring, enhanced due diligence, and comprehensive compliance rules.',
        isActive: true,
        metadata: {
          auto_assignment_criteria: {
            customer_risk_level: ['HIGH', 'CRITICAL']
          },
          system_profile_type: 'HIGH_RISK',
          created_by_system: true
        }
      },
      create: {
        id: generateRiskProfileId(),
        tenantId: tenant.id,
        name: 'High Risk Profile',
        description: 'Default profile for customers with HIGH or CRITICAL risk level. Applies strict monitoring, enhanced due diligence, and comprehensive compliance rules.',
        ruleCategory: 'ANOMALY',
        isActive: true,
        isSystemGenerated: true,
        priority: 300,
        assignmentPriority: 300,
        metadata: {
          auto_assignment_criteria: {
            customer_risk_level: ['HIGH', 'CRITICAL']
          },
          system_profile_type: 'HIGH_RISK',
          created_by_system: true
        },
        createdBy: 'SYSTEM_AUTO_GENERATED'
      }
    });

    console.log(`   ✅ Standard Risk Profile: ${standardProfile.id}`);
    console.log(`   ✅ Enhanced Monitoring Profile: ${enhancedProfile.id}`);
    console.log(`   ✅ High Risk Profile: ${highRiskProfile.id}`);

    // Create default rules for each profile
    await seedDefaultRules(tenant.id, tenant.name, standardProfile.id, enhancedProfile.id, highRiskProfile.id);
  }

  // Verification
  console.log('\n📊 Verification Results:');
  
  const profileCount = await prisma.riskProfile.count({
    where: { isSystemGenerated: true }
  });
  console.log(`✅ Total Risk Profiles Created: ${profileCount}`);

  const ruleCount = await prisma.rule.count({
    where: { 
      riskProfile: { 
        isSystemGenerated: true 
      } 
    }
  });
  console.log(`✅ Total Rules Created: ${ruleCount}`);

  // Show breakdown by profile type
  const profileBreakdown = await prisma.riskProfile.groupBy({
    by: ['name'],
    where: { isSystemGenerated: true },
    _count: { id: true }
  });

  console.log('\n📋 Profile Breakdown:');
  profileBreakdown.forEach(profile => {
    console.log(`   ${profile.name}: ${profile._count.id} instances`);
  });

  // Show rules by category
  const ruleBreakdown = await prisma.rule.groupBy({
    by: ['ruleCategory'],
    where: { 
      riskProfile: { 
        isSystemGenerated: true 
      } 
    },
    _count: { id: true }
  });

  console.log('\n📋 Rules by Category:');
  ruleBreakdown.forEach(category => {
    console.log(`   ${category.ruleCategory}: ${category._count.id} rules`);
  });

  console.log('\n🎉 Risk Profile System Seeding Completed Successfully!');
}

async function seedDefaultRules(tenantId: string, tenantName: string, standardProfileId: string, enhancedProfileId: string, highRiskProfileId: string) {
  console.log(`   📋 Creating default rules for ${tenantName}...`);

  // Standard Risk Profile Rules
  await prisma.rule.upsert({
    where: {
      tenantId_riskProfileId_name: {
        tenantId,
        riskProfileId: standardProfileId,
        name: 'Large Transaction Alert'
      }
    },
    update: {},
    create: {
      id: generateRuleId(),
      tenantId,
      riskProfileId: standardProfileId,
      name: 'Large Transaction Alert',
      description: 'Generate alert for transactions exceeding standard threshold for low-risk customers',
      ruleType: 'THRESHOLD',
      ruleCategory: 'TRANSACTION',
      conditions: {
        type: 'AND',
        conditions: [{
          field: 'transaction.amount',
          operator: 'GREATER_THAN',
          value: 10000,
          metadata: { currency: 'USD' }
        }]
      },
      actions: {
        actions: [{
          type: 'GENERATE_ALERT',
          severity: 'LOW',
          message: 'Large transaction detected for standard risk customer',
          metadata: { auto_resolve_hours: 24 }
        }]
      },
      thresholds: {
        amount_threshold: 10000,
        alert_threshold: 1
      },
      priority: 100,
      isActive: true,
      metadata: {
        system_generated: true,
        rule_template: 'STANDARD_LARGE_TRANSACTION',
        compliance_category: 'TRANSACTION_MONITORING'
      },
      createdBy: 'SYSTEM_AUTO_GENERATED'
    }
  });

  // Enhanced Monitoring Profile Rules
  await prisma.rule.upsert({
    where: {
      tenantId_riskProfileId_name: {
        tenantId,
        riskProfileId: enhancedProfileId,
        name: 'Medium Risk Transaction Alert'
      }
    },
    update: {},
    create: {
      id: generateRuleId(),
      tenantId,
      riskProfileId: enhancedProfileId,
      name: 'Medium Risk Transaction Alert',
      description: 'Generate alert for transactions exceeding medium-risk threshold',
      ruleType: 'THRESHOLD',
      ruleCategory: 'TRANSACTION',
      conditions: {
        type: 'AND',
        conditions: [{
          field: 'transaction.amount',
          operator: 'GREATER_THAN',
          value: 5000,
          metadata: { currency: 'USD' }
        }]
      },
      actions: {
        actions: [{
          type: 'GENERATE_ALERT',
          severity: 'MEDIUM',
          message: 'Transaction exceeds medium-risk threshold',
          metadata: { auto_resolve_hours: 12 }
        }, {
          type: 'REQUIRE_ENHANCED_DUE_DILIGENCE',
          value: true,
          metadata: { edd_type: 'TRANSACTION_REVIEW' }
        }]
      },
      thresholds: {
        amount_threshold: 5000,
        alert_threshold: 1
      },
      priority: 150,
      isActive: true,
      metadata: {
        system_generated: true,
        rule_template: 'ENHANCED_TRANSACTION_MONITORING',
        compliance_category: 'ENHANCED_DUE_DILIGENCE'
      },
      createdBy: 'SYSTEM_AUTO_GENERATED'
    }
  });

  // High Risk Profile Rules
  await prisma.rule.upsert({
    where: {
      tenantId_riskProfileId_name: {
        tenantId,
        riskProfileId: highRiskProfileId,
        name: 'High Risk Transaction Alert'
      }
    },
    update: {},
    create: {
      id: generateRuleId(),
      tenantId,
      riskProfileId: highRiskProfileId,
      name: 'High Risk Transaction Alert',
      description: 'Immediate alert for any significant transaction from high-risk customers',
      ruleType: 'THRESHOLD',
      ruleCategory: 'TRANSACTION',
      conditions: {
        type: 'AND',
        conditions: [{
          field: 'transaction.amount',
          operator: 'GREATER_THAN',
          value: 1000,
          metadata: { currency: 'USD' }
        }]
      },
      actions: {
        actions: [{
          type: 'GENERATE_ALERT',
          severity: 'HIGH',
          message: 'Transaction from high-risk customer requires immediate review',
          metadata: { immediate_notification: true, auto_resolve_hours: 2 }
        }, {
          type: 'REQUIRE_ENHANCED_DUE_DILIGENCE',
          value: true,
          metadata: { edd_type: 'COMPREHENSIVE_REVIEW' }
        }, {
          type: 'FLAG_FOR_REVIEW',
          severity: 'HIGH',
          message: 'High-risk customer transaction flagged',
          metadata: { priority: 'URGENT' }
        }]
      },
      thresholds: {
        amount_threshold: 1000,
        alert_threshold: 1
      },
      priority: 400,
      isActive: true,
      metadata: {
        system_generated: true,
        rule_template: 'HIGH_RISK_TRANSACTION_MONITORING',
        compliance_category: 'HIGH_RISK_CUSTOMER_MONITORING'
      },
      createdBy: 'SYSTEM_AUTO_GENERATED'
    }
  });

  console.log(`      ✅ Created 3 default rules`);
}

main()
  .catch((e) => {
    console.error('❌ Risk Profile seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
