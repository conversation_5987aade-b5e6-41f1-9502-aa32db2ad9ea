generator client {
  provider = "prisma-client-js"
  output   = "../../../node_modules/.prisma/tenant-client"
}

datasource db {
  provider = "postgresql"
  url      = env("TENANT_DATABASE_URL")
}

model Tenant {
  id                  String                    @id @db.VarChar(35)
  name                String                    @db.VarChar(255)
  displayName         String?                   @map("display_name") @db.VarChar(255)
  code                String?                   @unique @db.VarChar(50)
  industry            String?                   @db.VarChar(100)
  jurisdiction        String?                   @db.VarChar(100)
  plan                String                    @default("basic") @db.VarChar(50)
  status              String                    @default("active") @db.VarChar(20)
  settings            Json?                     @default("{}")
  createdAt           DateTime                  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt           DateTime                  @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  deletedAt           DateTime?                 @map("deleted_at") @db.Timestamptz(6)
  onboardingWorkflows OnboardingWorkflow[]
  onboardingSessions  TenantOnboardingSession[]
  configurations      TenantConfiguration[]
  apiKeys             TenantApiKey[]
  webhookKeys         TenantWebhookKey[]

  @@index([code], map: "idx_tenants_code")
  @@index([createdAt], map: "idx_tenants_created_at")
  @@index([status], map: "idx_tenants_status")
  @@map("tenants")
}

model TenantOnboardingSession {
  id          String                     @id @db.VarChar(35)
  tenantId    String                     @map("tenant_id") @db.VarChar(35)
  status      OnboardingStatus           @default(PENDING)
  currentStep Int                        @default(1) @map("current_step")
  totalSteps  Int                        @default(6) @map("total_steps")
  startedAt   DateTime                   @default(now()) @map("started_at") @db.Timestamptz(6)
  completedAt DateTime?                  @map("completed_at") @db.Timestamptz(6)
  expiresAt   DateTime                   @map("expires_at") @db.Timestamptz(6)
  createdBy   String                     @map("created_by") @db.VarChar(255)
  metadata    Json?                      @default("{}")
  createdAt   DateTime                   @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt   DateTime                   @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  documents   TenantOnboardingDocument[]
  tenant      Tenant                     @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  steps       TenantOnboardingStep[]

  @@index([tenantId], map: "idx_onboarding_sessions_tenant_id")
  @@index([createdAt], map: "idx_onboarding_sessions_created_at")
  @@map("tenant_onboarding_sessions")
}

model TenantOnboardingStep {
  id               String                  @id @db.VarChar(35)
  sessionId        String                  @map("session_id") @db.VarChar(35)
  stepNumber       Int                     @map("step_number")
  stepType         OnboardingStepType      @map("step_type")
  status           OnboardingStepStatus    @default(NOT_STARTED)
  data             Json?                   @default("{}")
  validationErrors Json?                   @map("validation_errors")
  startedAt        DateTime?               @map("started_at") @db.Timestamptz(6)
  completedAt      DateTime?               @map("completed_at") @db.Timestamptz(6)
  createdAt        DateTime                @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt        DateTime                @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  session          TenantOnboardingSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@unique([sessionId, stepNumber], map: "unique_session_step")
  @@index([sessionId], map: "idx_onboarding_steps_session_id")
  @@map("tenant_onboarding_steps")
}

model TenantOnboardingDocument {
  id                 String                     @id @db.VarChar(35)
  sessionId          String                     @map("session_id") @db.VarChar(35)
  stepId             String?                    @map("step_id") @db.VarChar(35)
  documentType       DocumentType               @map("document_type")
  fileName           String                     @map("file_name") @db.VarChar(255)
  filePath           String                     @map("file_path") @db.VarChar(500)
  fileSize           BigInt                     @map("file_size")
  mimeType           String                     @map("mime_type") @db.VarChar(100)
  verificationStatus DocumentVerificationStatus @default(PENDING) @map("verification_status")
  verificationNotes  String?                    @map("verification_notes")
  verifiedBy         String?                    @map("verified_by") @db.VarChar(255)
  verifiedAt         DateTime?                  @map("verified_at") @db.Timestamptz(6)
  metadata           Json?                      @default("{}")
  uploadedAt         DateTime                   @default(now()) @map("uploaded_at") @db.Timestamptz(6)
  createdAt          DateTime                   @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt          DateTime                   @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  session            TenantOnboardingSession    @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@index([sessionId], map: "idx_onboarding_documents_session_id")
  @@index([documentType], map: "idx_onboarding_documents_type")
  @@map("tenant_onboarding_documents")
}

model OnboardingWorkflow {
  id                      String           @id @db.VarChar(35)
  workflowId              String           @unique @map("workflow_id") @db.VarChar(100)
  tenantId                String           @map("tenant_id") @db.VarChar(35)
  tenantCode              String           @map("tenant_code") @db.VarChar(50)
  status                  WorkflowStatus   @default(INITIATED)
  institutionName         String           @map("institution_name") @db.VarChar(255)
  institutionType         InstitutionType  @map("institution_type")
  subscriptionPlan        SubscriptionPlan @map("subscription_plan")
  primaryContactName      String           @map("primary_contact_name") @db.VarChar(255)
  primaryContactEmail     String           @map("primary_contact_email") @db.VarChar(255)
  primaryContactPhone     String           @map("primary_contact_phone") @db.VarChar(50)
  charterNumber           String?          @map("charter_number") @db.VarChar(100)
  fdicCert                String?          @map("fdic_cert") @db.VarChar(100)
  expectedGoLive          DateTime         @map("expected_go_live") @db.Date
  estimatedCompletionDate DateTime         @map("estimated_completion_date") @db.Date
  assignedSpecialist      String           @map("assigned_specialist") @db.VarChar(100)
  welcomeEmailSent        Boolean          @default(false) @map("welcome_email_sent")
  welcomeEmailSentAt      DateTime?        @map("welcome_email_sent_at") @db.Timestamptz(6)
  metadata                Json?            @default("{}")
  initiatedAt             DateTime         @default(now()) @map("initiated_at") @db.Timestamptz(6)
  completedAt             DateTime?        @map("completed_at") @db.Timestamptz(6)
  createdAt               DateTime         @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt               DateTime         @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  tasks                   OnboardingTask[]
  tenant                  Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId], map: "idx_onboarding_workflows_tenant_id")
  @@index([workflowId], map: "idx_onboarding_workflows_workflow_id")
  @@index([status], map: "idx_onboarding_workflows_status")
  @@index([assignedSpecialist], map: "idx_onboarding_workflows_specialist")
  @@map("onboarding_workflows")
}

model OnboardingTask {
  id          String             @id @db.VarChar(35)
  workflowId  String             @map("workflow_id") @db.VarChar(35)
  taskId      String             @map("task_id") @db.VarChar(100)
  name        String             @db.VarChar(255)
  description String?
  status      TaskStatus         @default(PENDING)
  assignee    String             @db.VarChar(100)
  dueDate     DateTime           @map("due_date") @db.Date
  startedAt   DateTime?          @map("started_at") @db.Timestamptz(6)
  completedAt DateTime?          @map("completed_at") @db.Timestamptz(6)
  priority    Int                @default(1)
  dependsOn   String[]           @map("depends_on")
  metadata    Json?              @default("{}")
  createdAt   DateTime           @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt   DateTime           @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  workflow    OnboardingWorkflow @relation(fields: [workflowId], references: [id], onDelete: Cascade)

  @@unique([workflowId, taskId], map: "unique_workflow_task")
  @@index([workflowId], map: "idx_onboarding_tasks_workflow_id")
  @@index([status], map: "idx_onboarding_tasks_status")
  @@index([assignee], map: "idx_onboarding_tasks_assignee")
  @@index([dueDate], map: "idx_onboarding_tasks_due_date")
  @@map("onboarding_tasks")
}

model TenantConfiguration {
  id                String   @id @db.VarChar(35)
  tenantId          String   @map("tenant_id") @db.VarChar(35)
  configurationType String   @map("configuration_type") @db.VarChar(100)
  configurationData Json     @map("configuration_data")
  isActive          Boolean  @default(true) @map("is_active")
  createdAt         DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt         DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  tenant            Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId], map: "idx_tenant_configurations_tenant_id")
  @@index([configurationType], map: "idx_tenant_configurations_type")
  @@map("tenant_configurations")
}

enum TenantStatus {
  ONBOARDING
  ACTIVE
  SUSPENDED
  INACTIVE
  REJECTED
}

enum OrganizationType {
  BANK
  CREDIT_UNION
  FINTECH
  PAYMENT_PROCESSOR
  MONEY_TRANSMITTER
  INVESTMENT_FIRM
}

enum OnboardingStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
  CANCELLED
}

enum OnboardingStepType {
  ORGANIZATION_INFO
  COMPLIANCE_SETUP
  TECHNICAL_CONFIG
  ADMIN_USER
  DOCUMENT_VERIFICATION
  REVIEW_ACTIVATION
}

enum OnboardingStepStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  SKIPPED
  FAILED
}

enum DocumentType {
  BANKING_LICENSE
  INCORPORATION_CERTIFICATE
  TAX_CERTIFICATE
  BUSINESS_REGISTRATION
  INSURANCE_CERTIFICATE
  AML_POLICY
  OTHER
}

enum DocumentVerificationStatus {
  PENDING
  VERIFIED
  REJECTED
  EXPIRED
}

enum WorkflowStatus {
  INITIATED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  FAILED
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
  FAILED
}

enum SubscriptionPlan {
  BASIC
  PREMIUM
  ENTERPRISE
}

enum InstitutionType {
  COMMUNITY_BANK
  INVESTMENT_BANK
  CREDIT_UNION
  FINTECH
  PAYMENT_PROCESSOR
  MICROFINANCE
}

enum KeyEnvironment {
  LIVE
  TEST
}

model TenantApiKey {
  id          String         @id @db.VarChar(35)
  tenantId    String         @map("tenant_id") @db.VarChar(35)
  name        String         @db.VarChar(100)
  environment KeyEnvironment
  keyPrefix   String         @map("key_prefix") @db.VarChar(20)
  keyHash     String         @map("key_hash") @db.VarChar(255)
  lastUsedAt  DateTime?      @map("last_used_at") @db.Timestamptz(6)
  expiresAt   DateTime?      @map("expires_at") @db.Timestamptz(6)
  isActive    Boolean        @default(true) @map("is_active")
  createdAt   DateTime       @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt   DateTime       @updatedAt @map("updated_at") @db.Timestamptz(6)
  
  tenant      Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  
  @@index([tenantId, environment])
  @@index([keyHash])
  @@map("tenant_api_keys")
}

model TenantWebhookKey {
  id          String         @id @db.VarChar(35)
  tenantId    String         @map("tenant_id") @db.VarChar(35)
  name        String         @db.VarChar(100)
  environment KeyEnvironment
  keyPrefix   String         @map("key_prefix") @db.VarChar(20)
  keyHash     String         @map("key_hash") @db.VarChar(255)
  lastUsedAt  DateTime?      @map("last_used_at") @db.Timestamptz(6)
  expiresAt   DateTime?      @map("expires_at") @db.Timestamptz(6)
  isActive    Boolean        @default(true) @map("is_active")
  createdAt   DateTime       @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt   DateTime       @updatedAt @map("updated_at") @db.Timestamptz(6)
  
  tenant      Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  
  @@index([tenantId, environment])
  @@index([keyHash])
  @@map("tenant_webhook_keys")
}
