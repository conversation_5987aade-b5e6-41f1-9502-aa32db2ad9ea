/* eslint-disable @nx/enforce-module-boundaries */

import { createId } from '@paralleldrive/cuid2';
import { PrismaClient } from '../../../node_modules/.prisma/tenant-client/index.js';

function generateRuleId(): string {
  return `rule_${createId()}`.substring(0, 35);
}

const prisma = new PrismaClient();

async function main() {
  console.log('🛡️  Seeding Comprehensive Risk Profile Rules...');

  // Get all existing risk profiles
  const profiles = await prisma.riskProfile.findMany({
    where: { isSystemGenerated: true },
    include: { tenant: { select: { name: true } } }
  });

  if (profiles.length === 0) {
    console.log('❌ No system-generated risk profiles found. Please run basic seeding first.');
    process.exit(1);
  }

  console.log(`📊 Found ${profiles.length} risk profiles to enhance with comprehensive rules`);

  for (const profile of profiles) {
    console.log(`\n🏢 Adding comprehensive rules for ${profile.tenant.name} - ${profile.name}`);

    if (profile.name === 'Standard Risk Profile') {
      await addStandardRiskRules(profile.tenantId, profile.id);
    } else if (profile.name === 'Enhanced Monitoring Profile') {
      await addEnhancedMonitoringRules(profile.tenantId, profile.id);
    } else if (profile.name === 'High Risk Profile') {
      await addHighRiskRules(profile.tenantId, profile.id);
    }
  }

  // Final verification
  const totalRules = await prisma.rule.count({
    where: { riskProfile: { isSystemGenerated: true } }
  });

  console.log(`\n🎉 Comprehensive rule seeding completed! Total rules: ${totalRules}`);
}

async function addStandardRiskRules(tenantId: string, profileId: string) {
  // High Frequency Transaction Pattern
  await prisma.rule.upsert({
    where: {
      tenantId_riskProfileId_name: {
        tenantId,
        riskProfileId: profileId,
        name: 'High Frequency Transaction Pattern'
      }
    },
    update: {},
    create: {
      id: generateRuleId(),
      tenantId,
      riskProfileId: profileId,
      name: 'High Frequency Transaction Pattern',
      description: 'Monitor for unusual transaction frequency patterns in standard risk customers',
      ruleType: 'FREQUENCY_BASED',
      ruleCategory: 'FREQUENCY',
      conditions: {
        type: 'AND',
        conditions: [{
          field: 'transaction.count',
          operator: 'GREATER_THAN',
          value: 20,
          metadata: { time_window: '24_hours' }
        }, {
          field: 'transaction.total_amount',
          operator: 'GREATER_THAN',
          value: 50000,
          metadata: { time_window: '24_hours' }
        }]
      },
      actions: {
        actions: [{
          type: 'GENERATE_ALERT',
          severity: 'MEDIUM',
          message: 'High frequency transaction pattern detected',
          metadata: { requires_review: true }
        }]
      },
      thresholds: {
        frequency_threshold: 20,
        amount_threshold: 50000,
        time_window_hours: 24
      },
      priority: 200,
      isActive: true,
      metadata: {
        system_generated: true,
        rule_template: 'STANDARD_FREQUENCY_MONITORING',
        compliance_category: 'BEHAVIORAL_MONITORING'
      },
      createdBy: 'SYSTEM_AUTO_GENERATED'
    }
  });

  // Basic Geographic Monitoring
  await prisma.rule.upsert({
    where: {
      tenantId_riskProfileId_name: {
        tenantId,
        riskProfileId: profileId,
        name: 'High Risk Country Transaction'
      }
    },
    update: {},
    create: {
      id: generateRuleId(),
      tenantId,
      riskProfileId: profileId,
      name: 'High Risk Country Transaction',
      description: 'Monitor transactions to/from high-risk countries for standard customers',
      ruleType: 'GEOGRAPHIC',
      ruleCategory: 'GEOGRAPHIC',
      conditions: {
        type: 'OR',
        conditions: [{
          field: 'transaction.destination_country',
          operator: 'IN',
          value: ['AF', 'IR', 'KP', 'SY', 'MM'],
          metadata: { list_type: 'OFAC_SANCTIONS' }
        }, {
          field: 'transaction.origin_country',
          operator: 'IN',
          value: ['AF', 'IR', 'KP', 'SY', 'MM'],
          metadata: { list_type: 'OFAC_SANCTIONS' }
        }]
      },
      actions: {
        actions: [{
          type: 'FLAG_FOR_REVIEW',
          severity: 'HIGH',
          message: 'Transaction involves high-risk jurisdiction',
          metadata: { requires_manual_review: true, escalation_required: true }
        }]
      },
      thresholds: {
        risk_score_threshold: 75
      },
      priority: 300,
      isActive: true,
      metadata: {
        system_generated: true,
        rule_template: 'STANDARD_GEOGRAPHIC_MONITORING',
        compliance_category: 'SANCTIONS_SCREENING',
        country_codes: ['AF', 'IR', 'KP', 'SY', 'MM']
      },
      createdBy: 'SYSTEM_AUTO_GENERATED'
    }
  });

  console.log('   ✅ Added 2 additional standard risk rules');
}

async function addEnhancedMonitoringRules(tenantId: string, profileId: string) {
  // Behavioral Pattern Detection
  await prisma.rule.upsert({
    where: {
      tenantId_riskProfileId_name: {
        tenantId,
        riskProfileId: profileId,
        name: 'Unusual Behavioral Pattern'
      }
    },
    update: {},
    create: {
      id: generateRuleId(),
      tenantId,
      riskProfileId: profileId,
      name: 'Unusual Behavioral Pattern',
      description: 'Detect unusual behavioral patterns for medium-risk customers',
      ruleType: 'PATTERN',
      ruleCategory: 'BEHAVIORAL',
      conditions: {
        type: 'OR',
        conditions: [{
          field: 'customer.transaction_time_pattern',
          operator: 'NOT_CONTAINS',
          value: 'business_hours',
          metadata: { pattern_type: 'TIME_ANOMALY' }
        }, {
          field: 'customer.velocity_change',
          operator: 'GREATER_THAN',
          value: 300,
          metadata: { baseline_period: '30_days', unit: 'percent' }
        }]
      },
      actions: {
        actions: [{
          type: 'GENERATE_ALERT',
          severity: 'MEDIUM',
          message: 'Unusual behavioral pattern detected for medium-risk customer',
          metadata: { pattern_analysis_required: true }
        }]
      },
      thresholds: {
        velocity_threshold: 300,
        pattern_confidence: 0.75
      },
      priority: 175,
      isActive: true,
      metadata: {
        system_generated: true,
        rule_template: 'ENHANCED_BEHAVIORAL_MONITORING',
        compliance_category: 'BEHAVIORAL_ANALYSIS'
      },
      createdBy: 'SYSTEM_AUTO_GENERATED'
    }
  });

  // Channel Risk Monitoring
  await prisma.rule.upsert({
    where: {
      tenantId_riskProfileId_name: {
        tenantId,
        riskProfileId: profileId,
        name: 'High Risk Channel Usage'
      }
    },
    update: {},
    create: {
      id: generateRuleId(),
      tenantId,
      riskProfileId: profileId,
      name: 'High Risk Channel Usage',
      description: 'Monitor usage of high-risk transaction channels',
      ruleType: 'CONDITION',
      ruleCategory: 'CHANNEL',
      conditions: {
        type: 'AND',
        conditions: [{
          field: 'transaction.channel',
          operator: 'IN',
          value: ['ATM_INTERNATIONAL', 'WIRE_TRANSFER', 'CRYPTO_EXCHANGE'],
          metadata: { risk_level: 'HIGH' }
        }, {
          field: 'transaction.amount',
          operator: 'GREATER_THAN',
          value: 2500,
          metadata: { currency: 'USD' }
        }]
      },
      actions: {
        actions: [{
          type: 'FLAG_FOR_REVIEW',
          severity: 'MEDIUM',
          message: 'High-risk channel usage detected',
          metadata: { review_type: 'CHANNEL_RISK_ASSESSMENT' }
        }]
      },
      thresholds: {
        amount_threshold: 2500,
        channel_risk_score: 7
      },
      priority: 200,
      isActive: true,
      metadata: {
        system_generated: true,
        rule_template: 'ENHANCED_CHANNEL_MONITORING',
        compliance_category: 'CHANNEL_RISK_MANAGEMENT'
      },
      createdBy: 'SYSTEM_AUTO_GENERATED'
    }
  });

  console.log('   ✅ Added 2 additional enhanced monitoring rules');
}

async function addHighRiskRules(tenantId: string, profileId: string) {
  // Strict Velocity Monitoring
  await prisma.rule.upsert({
    where: {
      tenantId_riskProfileId_name: {
        tenantId,
        riskProfileId: profileId,
        name: 'High Risk Velocity Monitoring'
      }
    },
    update: {},
    create: {
      id: generateRuleId(),
      tenantId,
      riskProfileId: profileId,
      name: 'High Risk Velocity Monitoring',
      description: 'Strict velocity monitoring for high-risk customers with immediate blocking',
      ruleType: 'FREQUENCY_BASED',
      ruleCategory: 'FREQUENCY',
      conditions: {
        type: 'OR',
        conditions: [{
          field: 'transaction.count',
          operator: 'GREATER_THAN',
          value: 5,
          metadata: { time_window: '1_hour' }
        }, {
          field: 'transaction.total_amount',
          operator: 'GREATER_THAN',
          value: 10000,
          metadata: { time_window: '24_hours' }
        }]
      },
      actions: {
        actions: [{
          type: 'BLOCK_TRANSACTION',
          severity: 'CRITICAL',
          message: 'Transaction velocity exceeded for high-risk customer',
          metadata: { block_duration_hours: 24, requires_manual_unblock: true }
        }, {
          type: 'GENERATE_ALERT',
          severity: 'CRITICAL',
          message: 'Critical velocity threshold breached',
          metadata: { escalate_immediately: true }
        }]
      },
      thresholds: {
        hourly_transaction_limit: 5,
        daily_amount_limit: 10000
      },
      priority: 500,
      isActive: true,
      metadata: {
        system_generated: true,
        rule_template: 'HIGH_RISK_VELOCITY_CONTROL',
        compliance_category: 'TRANSACTION_BLOCKING'
      },
      createdBy: 'SYSTEM_AUTO_GENERATED'
    }
  });

  // Counterparty Risk Assessment
  await prisma.rule.upsert({
    where: {
      tenantId_riskProfileId_name: {
        tenantId,
        riskProfileId: profileId,
        name: 'High Risk Counterparty Screening'
      }
    },
    update: {},
    create: {
      id: generateRuleId(),
      tenantId,
      riskProfileId: profileId,
      name: 'High Risk Counterparty Screening',
      description: 'Enhanced screening for counterparties in transactions with high-risk customers',
      ruleType: 'CONDITION',
      ruleCategory: 'COUNTERPARTY',
      conditions: {
        type: 'OR',
        conditions: [{
          field: 'counterparty.risk_score',
          operator: 'GREATER_THAN',
          value: 70,
          metadata: { score_type: 'COMPOSITE_RISK' }
        }, {
          field: 'counterparty.sanctions_match',
          operator: 'EQUALS',
          value: true,
          metadata: { match_type: 'FUZZY_OR_EXACT' }
        }]
      },
      actions: {
        actions: [{
          type: 'BLOCK_TRANSACTION',
          severity: 'HIGH',
          message: 'Transaction blocked due to high-risk counterparty',
          metadata: { requires_compliance_approval: true }
        }]
      },
      thresholds: {
        counterparty_risk_threshold: 70,
        sanctions_tolerance: 0
      },
      priority: 550,
      isActive: true,
      metadata: {
        system_generated: true,
        rule_template: 'HIGH_RISK_COUNTERPARTY_SCREENING',
        compliance_category: 'COUNTERPARTY_RISK_MANAGEMENT'
      },
      createdBy: 'SYSTEM_AUTO_GENERATED'
    }
  });

  // Advanced Anomaly Detection
  await prisma.rule.upsert({
    where: {
      tenantId_riskProfileId_name: {
        tenantId,
        riskProfileId: profileId,
        name: 'Advanced Anomaly Detection'
      }
    },
    update: {},
    create: {
      id: generateRuleId(),
      tenantId,
      riskProfileId: profileId,
      name: 'Advanced Anomaly Detection',
      description: 'Machine learning-based anomaly detection for high-risk customers',
      ruleType: 'PATTERN',
      ruleCategory: 'ANOMALY',
      conditions: {
        type: 'OR',
        conditions: [{
          field: 'ml_model.anomaly_score',
          operator: 'GREATER_THAN',
          value: 0.85,
          metadata: { model_version: 'v2.1', confidence_threshold: 0.85 }
        }, {
          field: 'transaction.pattern_deviation',
          operator: 'GREATER_THAN',
          value: 3.0,
          metadata: { baseline_period: '90_days', deviation_type: 'STANDARD_DEVIATION' }
        }]
      },
      actions: {
        actions: [{
          type: 'FLAG_FOR_REVIEW',
          severity: 'HIGH',
          message: 'Anomalous transaction pattern detected by ML model',
          metadata: { review_priority: 'HIGH', ml_analysis_required: true }
        }]
      },
      thresholds: {
        anomaly_threshold: 0.85,
        deviation_threshold: 3.0
      },
      priority: 450,
      isActive: true,
      metadata: {
        system_generated: true,
        rule_template: 'HIGH_RISK_ANOMALY_DETECTION',
        compliance_category: 'ADVANCED_ANALYTICS',
        ml_model_enabled: true
      },
      createdBy: 'SYSTEM_AUTO_GENERATED'
    }
  });

  console.log('   ✅ Added 3 additional high-risk rules');
}

main()
  .catch((e) => {
    console.error('❌ Comprehensive rule seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
