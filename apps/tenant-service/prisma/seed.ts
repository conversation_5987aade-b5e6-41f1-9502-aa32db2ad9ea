/* eslint-disable @nx/enforce-module-boundaries */

import { createId } from '@paralleldrive/cuid2';
import * as bcrypt from 'bcryptjs';
import { PrismaClient } from '../../../node_modules/.prisma/tenant-client/index.js';

// CUID generation functions for seeding
function generateTenantId(): string {
  return `ten_${createId()}`.substring(0, 35);
}

function generateCuid(prefix: string): string {
  return `${prefix}${createId()}`.substring(0, 35);
}

// CUID prefixes
const CUID_PREFIXES = {
  TENANT_API_KEY: 'tak_',
  TENANT_WEBHOOK_KEY: 'twk_',
  TENANT_CONFIGURATION: 'tcf_',
};

// Key generation functions
function generateApiKey(environment: string): { key: string; prefix: string } {
  const prefix = environment === 'LIVE' ? 'qeep_live_' : 'qeep_test_';
  const secret = Math.random().toString(36).substring(2, 34);
  const key = `${prefix}${secret}`;
  return { key, prefix };
}

function generateWebhookKey(environment: string): { key: string; prefix: string } {
  const prefix = environment === 'LIVE' ? 'whk_live_' : 'whk_test_';
  const secret = Math.random().toString(36).substring(2, 34);
  const key = `${prefix}${secret}`;
  return { key, prefix };
}

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding Tenant Service database...');

  // Generate CUID for Ronna Bank tenant
  const ronnaTenantId = generateTenantId();

  // Create Ronna Bank tenant with CUID
  const ronnaTenant = await prisma.tenant.upsert({
    where: { code: 'ronna-bank' },
    update: {},
    create: {
      id: ronnaTenantId,
      name: 'ronna-bank',
      code: 'ronna-bank',
      displayName: 'Ronna Bank',
      industry: 'FINANCIAL_SERVICES',
      jurisdiction: 'GHANA',
      plan: 'enterprise',
      status: 'ACTIVE',
      settings: {
        aml_rules: {
          cash_transaction_threshold: 50000, // 50,000 GHS (approx $4,200 USD)
          structuring_threshold: 15000, // 15,000 GHS (approx $1,260 USD)
          velocity_rules: {
            daily_limit: 200000, // 200,000 GHS
            weekly_limit: 1000000, // 1M GHS
            monthly_limit: 4000000, // 4M GHS
          },
          high_risk_countries: ['AF', 'IR', 'KP', 'SY', 'MM', 'BY'],
          sanctions_screening: true,
          enhanced_due_diligence: true,
        },
        compliance_settings: {
          sar_filing_enabled: true,
          ctr_filing_enabled: true,
          regulatory_jurisdiction: 'GH',
          reporting_currency: 'GHS',
          central_bank: 'Bank of Ghana',
          fic_reporting: true, // Financial Intelligence Centre reporting
        },
        notification_settings: {
          email_alerts: true,
          sms_alerts: true,
          dashboard_notifications: true,
          escalation_enabled: true,
          whatsapp_alerts: true, // Popular in Ghana
        },
        features: {
          advanced_analytics: true,
          custom_rules: true,
          api_access: true,
          white_labeling: true,
          real_time_monitoring: true,
          mobile_money_integration: true, // Key for Ghana
          ussd_integration: true,
        },
        limits: {
          users: 500,
          customers: 50000,
          transactions_per_month: 5000000,
          api_calls_per_day: 100000,
        },
        localization: {
          primary_language: 'en',
          supported_languages: ['en', 'tw', 'ak'], // English, Twi, Akan
          timezone: 'Africa/Accra',
          date_format: 'DD/MM/YYYY',
        },
      },
    },
  });

  console.log(`✅ Created Ronna Bank tenant: Ronna Bank (${ronnaTenant.name})`);

  // Create tenant configuration for AML rules
  await prisma.tenantConfiguration.create({
    data: {
      id: generateCuid(CUID_PREFIXES.TENANT_CONFIGURATION),
      tenantId: ronnaTenant.id,
      configurationType: 'AML_RULES',
      configurationData: {
        transaction_monitoring: {
          real_time_enabled: true,
          batch_processing_enabled: true,
          alert_generation_enabled: true,
          mobile_money_monitoring: true,
          cross_border_monitoring: true,
        },
        risk_scoring: {
          customer_risk_enabled: true,
          transaction_risk_enabled: true,
          geographic_risk_enabled: true,
          occupation_risk_enabled: true,
          mobile_money_risk_enabled: true,
        },
        compliance_reporting: {
          automated_sar_generation: true,
          automated_ctr_generation: true,
          regulatory_reporting_enabled: true,
          fic_reporting_enabled: true,
          bog_reporting_enabled: true, // Bank of Ghana
        },
      },
      isActive: true,
    },
  });

  console.log(`✅ Created AML configuration for Ronna Bank tenant`);

  // Create API keys for Ronna Bank tenant
  const testApiKeyData = generateApiKey('TEST');
  const testApiKeyHash = await bcrypt.hash(testApiKeyData.key, 10);

  await prisma.tenantApiKey.create({
    data: {
      id: generateCuid(CUID_PREFIXES.TENANT_API_KEY),
      tenantId: ronnaTenant.id,
      name: 'Test Environment Key',
      environment: 'TEST',
      keyPrefix: testApiKeyData.prefix,
      keyHash: testApiKeyHash,
      lastUsedAt: null,
      expiresAt: null,
      isActive: true,
    },
  });

  const liveApiKeyData = generateApiKey('LIVE');
  const liveApiKeyHash = await bcrypt.hash(liveApiKeyData.key, 10);

  await prisma.tenantApiKey.create({
    data: {
      id: generateCuid(CUID_PREFIXES.TENANT_API_KEY),
      tenantId: ronnaTenant.id,
      name: 'Live Environment Key',
      environment: 'LIVE',
      keyPrefix: liveApiKeyData.prefix,
      keyHash: liveApiKeyHash,
      lastUsedAt: null,
      expiresAt: null,
      isActive: true,
    },
  });

  console.log(`✅ Created API keys for Ronna Bank tenant`);

  // Create webhook keys for Ronna Bank tenant
  const testWebhookKeyData = generateWebhookKey('TEST');
  const testWebhookKeyHash = await bcrypt.hash(testWebhookKeyData.key, 10);

  await prisma.tenantWebhookKey.create({
    data: {
      id: generateCuid(CUID_PREFIXES.TENANT_WEBHOOK_KEY),
      tenantId: ronnaTenant.id,
      name: 'Test Webhook Key',
      environment: 'TEST',
      keyPrefix: testWebhookKeyData.prefix,
      keyHash: testWebhookKeyHash,
      lastUsedAt: null,
      expiresAt: null,
      isActive: true,
    },
  });

  const liveWebhookKeyData = generateWebhookKey('LIVE');
  const liveWebhookKeyHash = await bcrypt.hash(liveWebhookKeyData.key, 10);

  await prisma.tenantWebhookKey.create({
    data: {
      id: generateCuid(CUID_PREFIXES.TENANT_WEBHOOK_KEY),
      tenantId: ronnaTenant.id,
      name: 'Live Webhook Key',
      environment: 'LIVE',
      keyPrefix: liveWebhookKeyData.prefix,
      keyHash: liveWebhookKeyHash,
      lastUsedAt: null,
      expiresAt: null,
      isActive: true,
    },
  });

  console.log(`✅ Created webhook keys for Ronna Bank tenant`);

  // Create additional Ghanaian financial institutions
  const additionalTenants = [
    {
      id: generateTenantId(),
      name: 'ecobank-ghana',
      code: 'ecobank-ghana',
      displayName: 'Ecobank Ghana',
      industry: 'FINANCIAL_SERVICES',
      jurisdiction: 'GHANA',
      plan: 'professional',
      status: 'ACTIVE',
    },
    {
      id: generateTenantId(),
      name: 'absa-bank-ghana',
      code: 'absa-bank-ghana',
      displayName: 'Absa Bank Ghana',
      industry: 'FINANCIAL_SERVICES',
      jurisdiction: 'GHANA',
      plan: 'enterprise',
      status: 'ACTIVE',
    },
    {
      id: generateTenantId(),
      name: 'mtn-momo',
      code: 'mtn-momo',
      displayName: 'MTN Mobile Money',
      industry: 'FINTECH',
      jurisdiction: 'GHANA',
      plan: 'professional',
      status: 'ACTIVE',
    },
  ];

  console.log('🏢 Creating additional test tenants...');

  for (const tenantData of additionalTenants) {
    const tenant = await prisma.tenant.upsert({
      where: { id: tenantData.id },
      update: {},
      create: tenantData,
    });

    console.log(`   ✅ Created tenant: ${tenantData.displayName} (${tenant.name})`);
  }

  console.log('');
  console.log('✅ Tenant Service database seeding completed!');
  console.log('');
  console.log('🏢 Available Tenants:');
  console.log('   ronna-bank         - Ronna Bank (enterprise) - Primary development tenant');
  console.log('   ecobank-ghana      - Ecobank Ghana (professional)');
  console.log('   absa-bank-ghana    - Absa Bank Ghana (enterprise)');
  console.log('   mtn-momo           - MTN Mobile Money (professional)');
  console.log('');
  console.log('🔧 Ronna Bank Configuration:');
  console.log('   - Website: ronna.com.gh');
  console.log('   - Industry: Financial Services');
  console.log('   - Jurisdiction: Ghana');
  console.log('   - Currency: Ghana Cedis (GHS)');
  console.log('   - AML Rules: Configured with Ghana-specific thresholds');
  console.log('   - Compliance: SAR/CTR filing enabled, FIC reporting');
  console.log('   - Features: Mobile money integration, USSD support');
  console.log('   - Languages: English, Twi, Akan');
}

main()
  .catch((e) => {
    console.error('❌ Tenant Service database seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
