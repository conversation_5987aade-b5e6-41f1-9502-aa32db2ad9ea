/* eslint-disable @nx/enforce-module-boundaries */

import { createId } from '@paralleldrive/cuid2';
import * as bcrypt from 'bcryptjs';
import { PrismaClient } from '../../../node_modules/.prisma/tenant-client/index.js';

// CUID generation functions for seeding
function generateTenantId(): string {
  return `ten_${createId()}`.substring(0, 35);
}

function generateCuid(prefix: string): string {
  return `${prefix}${createId()}`.substring(0, 35);
}

// CUID prefixes
const CUID_PREFIXES = {
  TENANT_API_KEY: 'tak_',
  TENANT_WEBHOOK_KEY: 'twk_',
  TENANT_CONFIGURATION: 'tcf_',
};

// Key generation functions
function generateApiKey(environment: string): { key: string; prefix: string } {
  const prefix = environment === 'LIVE' ? 'qeep_live_' : 'qeep_test_';
  const secret = Math.random().toString(36).substring(2, 34);
  const key = `${prefix}${secret}`;
  return { key, prefix };
}

function generateWebhookKey(environment: string): { key: string; prefix: string } {
  const prefix = environment === 'LIVE' ? 'whk_live_' : 'whk_test_';
  const secret = Math.random().toString(36).substring(2, 34);
  const key = `${prefix}${secret}`;
  return { key, prefix };
}

const prisma = new PrismaClient();

// Risk Profile seeding functions
function generateRiskProfileId(): string {
  return `rp_${createId()}`.substring(0, 35);
}

function generateRuleId(): string {
  return `rule_${createId()}`.substring(0, 35);
}

async function seedRiskProfiles() {
  console.log('🛡️  Creating default risk profiles and rules...');

  // Get all existing tenants
  const tenants = await prisma.tenant.findMany({
    where: { deletedAt: null },
    select: { id: true, name: true },
  });

  if (tenants.length === 0) {
    console.log('⚠️  No tenants found. Skipping risk profile seeding.');
    return;
  }

  console.log(`📊 Creating risk profiles for ${tenants.length} tenants`);

  for (const tenant of tenants) {
    console.log(`  Creating profiles for tenant: ${tenant.name}`);

    // Standard Risk Profile (LOW risk customers)
    const standardProfile = await prisma.riskProfile.upsert({
      where: {
        tenantId_name: {
          tenantId: tenant.id,
          name: 'Standard Risk Profile',
        },
      },
      update: {},
      create: {
        id: generateRiskProfileId(),
        tenantId: tenant.id,
        name: 'Standard Risk Profile',
        description: 'Default profile for customers with LOW risk level. Applies standard monitoring and compliance rules.',
        ruleCategory: 'TRANSACTION',
        isActive: true,
        isSystemGenerated: true,
        priority: 100,
        assignmentPriority: 100,
        metadata: {
          auto_assignment_criteria: {
            customer_risk_level: 'LOW',
          },
          system_profile_type: 'STANDARD',
          created_by_system: true,
        },
        createdBy: 'SYSTEM_AUTO_GENERATED',
      },
    });

    // Enhanced Monitoring Profile (MEDIUM risk customers)
    const enhancedProfile = await prisma.riskProfile.upsert({
      where: {
        tenantId_name: {
          tenantId: tenant.id,
          name: 'Enhanced Monitoring Profile',
        },
      },
      update: {},
      create: {
        id: generateRiskProfileId(),
        tenantId: tenant.id,
        name: 'Enhanced Monitoring Profile',
        description: 'Default profile for customers with MEDIUM risk level. Applies enhanced monitoring and additional compliance checks.',
        ruleCategory: 'BEHAVIORAL',
        isActive: true,
        isSystemGenerated: true,
        priority: 200,
        assignmentPriority: 200,
        metadata: {
          auto_assignment_criteria: {
            customer_risk_level: 'MEDIUM',
          },
          system_profile_type: 'ENHANCED',
          created_by_system: true,
        },
        createdBy: 'SYSTEM_AUTO_GENERATED',
      },
    });

    // High Risk Profile (HIGH/CRITICAL risk customers)
    const highRiskProfile = await prisma.riskProfile.upsert({
      where: {
        tenantId_name: {
          tenantId: tenant.id,
          name: 'High Risk Profile',
        },
      },
      update: {},
      create: {
        id: generateRiskProfileId(),
        tenantId: tenant.id,
        name: 'High Risk Profile',
        description: 'Default profile for customers with HIGH or CRITICAL risk level. Applies strict monitoring, enhanced due diligence, and comprehensive compliance rules.',
        ruleCategory: 'ANOMALY',
        isActive: true,
        isSystemGenerated: true,
        priority: 300,
        assignmentPriority: 300,
        metadata: {
          auto_assignment_criteria: {
            customer_risk_level: ['HIGH', 'CRITICAL'],
          },
          system_profile_type: 'HIGH_RISK',
          created_by_system: true,
        },
        createdBy: 'SYSTEM_AUTO_GENERATED',
      },
    });

    // Create default rules for each profile
    await seedDefaultRules(tenant.id, standardProfile.id, enhancedProfile.id, highRiskProfile.id);
  }

  console.log('✅ Risk profiles and rules created successfully');
}

async function seedDefaultRules(tenantId: string, standardProfileId: string, enhancedProfileId: string, highRiskProfileId: string) {
  // Standard Risk Profile Rules
  await prisma.rule.upsert({
    where: {
      tenantId_riskProfileId_name: {
        tenantId,
        riskProfileId: standardProfileId,
        name: 'Large Transaction Alert',
      },
    },
    update: {},
    create: {
      id: generateRuleId(),
      tenantId,
      riskProfileId: standardProfileId,
      name: 'Large Transaction Alert',
      description: 'Generate alert for transactions exceeding standard threshold for low-risk customers',
      ruleType: 'THRESHOLD',
      ruleCategory: 'TRANSACTION',
      conditions: {
        type: 'AND',
        conditions: [
          {
            field: 'transaction.amount',
            operator: 'GREATER_THAN',
            value: 10000,
            metadata: { currency: 'USD' },
          },
        ],
      },
      actions: {
        actions: [
          {
            type: 'GENERATE_ALERT',
            severity: 'LOW',
            message: 'Large transaction detected for standard risk customer',
            metadata: { auto_resolve_hours: 24 },
          },
        ],
      },
      thresholds: {
        amount_threshold: 10000,
        alert_threshold: 1,
      },
      priority: 100,
      isActive: true,
      metadata: {
        system_generated: true,
        rule_template: 'STANDARD_LARGE_TRANSACTION',
        compliance_category: 'TRANSACTION_MONITORING',
      },
      createdBy: 'SYSTEM_AUTO_GENERATED',
    },
  });

  // Enhanced Monitoring Profile Rules
  await prisma.rule.upsert({
    where: {
      tenantId_riskProfileId_name: {
        tenantId,
        riskProfileId: enhancedProfileId,
        name: 'Medium Risk Transaction Alert',
      },
    },
    update: {},
    create: {
      id: generateRuleId(),
      tenantId,
      riskProfileId: enhancedProfileId,
      name: 'Medium Risk Transaction Alert',
      description: 'Generate alert for transactions exceeding medium-risk threshold',
      ruleType: 'THRESHOLD',
      ruleCategory: 'TRANSACTION',
      conditions: {
        type: 'AND',
        conditions: [
          {
            field: 'transaction.amount',
            operator: 'GREATER_THAN',
            value: 5000,
            metadata: { currency: 'USD' },
          },
        ],
      },
      actions: {
        actions: [
          {
            type: 'GENERATE_ALERT',
            severity: 'MEDIUM',
            message: 'Transaction exceeds medium-risk threshold',
            metadata: { auto_resolve_hours: 12 },
          },
          {
            type: 'REQUIRE_ENHANCED_DUE_DILIGENCE',
            value: true,
            metadata: { edd_type: 'TRANSACTION_REVIEW' },
          },
        ],
      },
      thresholds: {
        amount_threshold: 5000,
        alert_threshold: 1,
      },
      priority: 150,
      isActive: true,
      metadata: {
        system_generated: true,
        rule_template: 'ENHANCED_TRANSACTION_MONITORING',
        compliance_category: 'ENHANCED_DUE_DILIGENCE',
      },
      createdBy: 'SYSTEM_AUTO_GENERATED',
    },
  });

  // High Risk Profile Rules
  await prisma.rule.upsert({
    where: {
      tenantId_riskProfileId_name: {
        tenantId,
        riskProfileId: highRiskProfileId,
        name: 'High Risk Transaction Alert',
      },
    },
    update: {},
    create: {
      id: generateRuleId(),
      tenantId,
      riskProfileId: highRiskProfileId,
      name: 'High Risk Transaction Alert',
      description: 'Immediate alert for any significant transaction from high-risk customers',
      ruleType: 'THRESHOLD',
      ruleCategory: 'TRANSACTION',
      conditions: {
        type: 'AND',
        conditions: [
          {
            field: 'transaction.amount',
            operator: 'GREATER_THAN',
            value: 1000,
            metadata: { currency: 'USD' },
          },
        ],
      },
      actions: {
        actions: [
          {
            type: 'GENERATE_ALERT',
            severity: 'HIGH',
            message: 'Transaction from high-risk customer requires immediate review',
            metadata: { immediate_notification: true, auto_resolve_hours: 2 },
          },
          {
            type: 'REQUIRE_ENHANCED_DUE_DILIGENCE',
            value: true,
            metadata: { edd_type: 'COMPREHENSIVE_REVIEW' },
          },
          {
            type: 'FLAG_FOR_REVIEW',
            severity: 'HIGH',
            message: 'High-risk customer transaction flagged',
            metadata: { priority: 'URGENT' },
          },
        ],
      },
      thresholds: {
        amount_threshold: 1000,
        alert_threshold: 1,
      },
      priority: 400,
      isActive: true,
      metadata: {
        system_generated: true,
        rule_template: 'HIGH_RISK_TRANSACTION_MONITORING',
        compliance_category: 'HIGH_RISK_CUSTOMER_MONITORING',
      },
      createdBy: 'SYSTEM_AUTO_GENERATED',
    },
  });
}

async function main() {
  console.log('🌱 Seeding Tenant Service database...');

  // Generate CUID for Ronna Bank tenant
  const ronnaTenantId = generateTenantId();

  // Create Ronna Bank tenant with CUID
  const ronnaTenant = await prisma.tenant.upsert({
    where: { code: 'ronna-bank' },
    update: {},
    create: {
      id: ronnaTenantId,
      name: 'ronna-bank',
      code: 'ronna-bank',
      displayName: 'Ronna Bank',
      industry: 'FINANCIAL_SERVICES',
      jurisdiction: 'GHANA',
      plan: 'enterprise',
      status: 'ACTIVE',
      settings: {
        aml_rules: {
          cash_transaction_threshold: 50000, // 50,000 GHS (approx $4,200 USD)
          structuring_threshold: 15000, // 15,000 GHS (approx $1,260 USD)
          velocity_rules: {
            daily_limit: 200000, // 200,000 GHS
            weekly_limit: 1000000, // 1M GHS
            monthly_limit: 4000000, // 4M GHS
          },
          high_risk_countries: ['AF', 'IR', 'KP', 'SY', 'MM', 'BY'],
          sanctions_screening: true,
          enhanced_due_diligence: true,
        },
        compliance_settings: {
          sar_filing_enabled: true,
          ctr_filing_enabled: true,
          regulatory_jurisdiction: 'GH',
          reporting_currency: 'GHS',
          central_bank: 'Bank of Ghana',
          fic_reporting: true, // Financial Intelligence Centre reporting
        },
        notification_settings: {
          email_alerts: true,
          sms_alerts: true,
          dashboard_notifications: true,
          escalation_enabled: true,
          whatsapp_alerts: true, // Popular in Ghana
        },
        features: {
          advanced_analytics: true,
          custom_rules: true,
          api_access: true,
          white_labeling: true,
          real_time_monitoring: true,
          mobile_money_integration: true, // Key for Ghana
          ussd_integration: true,
        },
        limits: {
          users: 500,
          customers: 50000,
          transactions_per_month: 5000000,
          api_calls_per_day: 100000,
        },
        localization: {
          primary_language: 'en',
          supported_languages: ['en', 'tw', 'ak'], // English, Twi, Akan
          timezone: 'Africa/Accra',
          date_format: 'DD/MM/YYYY',
        },
      },
    },
  });

  console.log(`✅ Created Ronna Bank tenant: Ronna Bank (${ronnaTenant.name})`);

  // Create tenant configuration for AML rules
  await prisma.tenantConfiguration.create({
    data: {
      id: generateCuid(CUID_PREFIXES.TENANT_CONFIGURATION),
      tenantId: ronnaTenant.id,
      configurationType: 'AML_RULES',
      configurationData: {
        transaction_monitoring: {
          real_time_enabled: true,
          batch_processing_enabled: true,
          alert_generation_enabled: true,
          mobile_money_monitoring: true,
          cross_border_monitoring: true,
        },
        risk_scoring: {
          customer_risk_enabled: true,
          transaction_risk_enabled: true,
          geographic_risk_enabled: true,
          occupation_risk_enabled: true,
          mobile_money_risk_enabled: true,
        },
        compliance_reporting: {
          automated_sar_generation: true,
          automated_ctr_generation: true,
          regulatory_reporting_enabled: true,
          fic_reporting_enabled: true,
          bog_reporting_enabled: true, // Bank of Ghana
        },
      },
      isActive: true,
    },
  });

  console.log(`✅ Created AML configuration for Ronna Bank tenant`);

  // Create API keys for Ronna Bank tenant
  const testApiKeyData = generateApiKey('TEST');
  const testApiKeyHash = await bcrypt.hash(testApiKeyData.key, 10);

  await prisma.tenantApiKey.create({
    data: {
      id: generateCuid(CUID_PREFIXES.TENANT_API_KEY),
      tenantId: ronnaTenant.id,
      name: 'Test Environment Key',
      environment: 'TEST',
      keyPrefix: testApiKeyData.prefix,
      keyHash: testApiKeyHash,
      lastUsedAt: null,
      expiresAt: null,
      isActive: true,
    },
  });

  const liveApiKeyData = generateApiKey('LIVE');
  const liveApiKeyHash = await bcrypt.hash(liveApiKeyData.key, 10);

  await prisma.tenantApiKey.create({
    data: {
      id: generateCuid(CUID_PREFIXES.TENANT_API_KEY),
      tenantId: ronnaTenant.id,
      name: 'Live Environment Key',
      environment: 'LIVE',
      keyPrefix: liveApiKeyData.prefix,
      keyHash: liveApiKeyHash,
      lastUsedAt: null,
      expiresAt: null,
      isActive: true,
    },
  });

  console.log(`✅ Created API keys for Ronna Bank tenant`);

  // Create webhook keys for Ronna Bank tenant
  const testWebhookKeyData = generateWebhookKey('TEST');
  const testWebhookKeyHash = await bcrypt.hash(testWebhookKeyData.key, 10);

  await prisma.tenantWebhookKey.create({
    data: {
      id: generateCuid(CUID_PREFIXES.TENANT_WEBHOOK_KEY),
      tenantId: ronnaTenant.id,
      name: 'Test Webhook Key',
      environment: 'TEST',
      keyPrefix: testWebhookKeyData.prefix,
      keyHash: testWebhookKeyHash,
      lastUsedAt: null,
      expiresAt: null,
      isActive: true,
    },
  });

  const liveWebhookKeyData = generateWebhookKey('LIVE');
  const liveWebhookKeyHash = await bcrypt.hash(liveWebhookKeyData.key, 10);

  await prisma.tenantWebhookKey.create({
    data: {
      id: generateCuid(CUID_PREFIXES.TENANT_WEBHOOK_KEY),
      tenantId: ronnaTenant.id,
      name: 'Live Webhook Key',
      environment: 'LIVE',
      keyPrefix: liveWebhookKeyData.prefix,
      keyHash: liveWebhookKeyHash,
      lastUsedAt: null,
      expiresAt: null,
      isActive: true,
    },
  });

  console.log(`✅ Created webhook keys for Ronna Bank tenant`);

  // Create additional Ghanaian financial institutions
  const additionalTenants = [
    {
      id: generateTenantId(),
      name: 'ecobank-ghana',
      code: 'ecobank-ghana',
      displayName: 'Ecobank Ghana',
      industry: 'FINANCIAL_SERVICES',
      jurisdiction: 'GHANA',
      plan: 'professional',
      status: 'ACTIVE',
    },
    {
      id: generateTenantId(),
      name: 'absa-bank-ghana',
      code: 'absa-bank-ghana',
      displayName: 'Absa Bank Ghana',
      industry: 'FINANCIAL_SERVICES',
      jurisdiction: 'GHANA',
      plan: 'enterprise',
      status: 'ACTIVE',
    },
    {
      id: generateTenantId(),
      name: 'mtn-momo',
      code: 'mtn-momo',
      displayName: 'MTN Mobile Money',
      industry: 'FINTECH',
      jurisdiction: 'GHANA',
      plan: 'professional',
      status: 'ACTIVE',
    },
  ];

  console.log('🏢 Creating additional test tenants...');

  for (const tenantData of additionalTenants) {
    const tenant = await prisma.tenant.upsert({
      where: { id: tenantData.id },
      update: {},
      create: tenantData,
    });

    console.log(`   ✅ Created tenant: ${tenantData.displayName} (${tenant.name})`);
  }

  // Seed risk profiles and rules for all tenants
  await seedRiskProfiles();

  console.log('');
  console.log('✅ Tenant Service database seeding completed!');
  console.log('');
  console.log('🏢 Available Tenants:');
  console.log('   ronna-bank         - Ronna Bank (enterprise) - Primary development tenant');
  console.log('   ecobank-ghana      - Ecobank Ghana (professional)');
  console.log('   absa-bank-ghana    - Absa Bank Ghana (enterprise)');
  console.log('   mtn-momo           - MTN Mobile Money (professional)');
  console.log('');
  console.log('🔧 Ronna Bank Configuration:');
  console.log('   - Website: ronna.com.gh');
  console.log('   - Industry: Financial Services');
  console.log('   - Jurisdiction: Ghana');
  console.log('   - Currency: Ghana Cedis (GHS)');
  console.log('   - AML Rules: Configured with Ghana-specific thresholds');
  console.log('   - Compliance: SAR/CTR filing enabled, FIC reporting');
  console.log('   - Features: Mobile money integration, USSD support');
  console.log('   - Languages: English, Twi, Akan');
}

main()
  .catch((e) => {
    console.error('❌ Tenant Service database seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
