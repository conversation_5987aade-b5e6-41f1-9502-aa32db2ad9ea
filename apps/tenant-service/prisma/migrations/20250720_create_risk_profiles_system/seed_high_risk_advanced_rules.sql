-- Seed Advanced High Risk Profile Rules
-- This script contains the most sophisticated rules for high-risk customers

-- Helper function to generate rule IDs (redefine for this file)
CREATE OR REPLACE FUNCTION generate_rule_id() RETURNS TEXT AS $$
BEGIN
    RETURN 'rule_' || LOWER(REPLACE(gen_random_uuid()::text, '-', ''));
END;
$$ LANGUAGE plpgsql;

-- Helper function to get risk profile ID by name and tenant (redefine for this file)
CREATE OR REPLACE FUNCTION get_risk_profile_id(tenant_id_param TEXT, profile_name TEXT) RETURNS TEXT AS $$
DECLARE
    profile_id TEXT;
BEGIN
    SELECT id INTO profile_id 
    FROM risk_profiles 
    WHERE tenant_id = tenant_id_param 
    AND name = profile_name 
    AND is_system_generated = true;
    
    RETURN profile_id;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- HIGH RISK PROFILE RULES (HIGH/CRITICAL risk customers) - ADVANCED
-- =============================================================================

-- High Risk Profile: Strict Frequency Monitoring
INSERT INTO rules (
    id, tenant_id, risk_profile_id, name, description, rule_type, rule_category,
    conditions, actions, thresholds, priority, is_active, metadata, created_at, updated_at, created_by
)
SELECT 
    generate_rule_id(),
    t.id as tenant_id,
    get_risk_profile_id(t.id, 'High Risk Profile') as risk_profile_id,
    'High Risk Velocity Monitoring' as name,
    'Strict velocity monitoring for high-risk customers with immediate blocking' as description,
    'FREQUENCY_BASED'::RuleType as rule_type,
    'FREQUENCY'::RuleCategory as rule_category,
    jsonb_build_object(
        'type', 'OR',
        'conditions', jsonb_build_array(
            jsonb_build_object(
                'field', 'transaction.count',
                'operator', 'GREATER_THAN',
                'value', 5,
                'metadata', jsonb_build_object('time_window', '1_hour')
            ),
            jsonb_build_object(
                'field', 'transaction.total_amount',
                'operator', 'GREATER_THAN',
                'value', 10000,
                'metadata', jsonb_build_object('time_window', '24_hours')
            ),
            jsonb_build_object(
                'field', 'transaction.count',
                'operator', 'GREATER_THAN',
                'value', 10,
                'metadata', jsonb_build_object('time_window', '24_hours')
            )
        )
    ) as conditions,
    jsonb_build_object(
        'actions', jsonb_build_array(
            jsonb_build_object(
                'type', 'BLOCK_TRANSACTION',
                'severity', 'CRITICAL',
                'message', 'Transaction velocity exceeded for high-risk customer',
                'metadata', jsonb_build_object('block_duration_hours', 24, 'requires_manual_unblock', true)
            ),
            jsonb_build_object(
                'type', 'GENERATE_ALERT',
                'severity', 'CRITICAL',
                'message', 'Critical velocity threshold breached',
                'metadata', jsonb_build_object('escalate_immediately', true)
            )
        )
    ) as actions,
    jsonb_build_object(
        'hourly_transaction_limit', 5,
        'daily_amount_limit', 10000,
        'daily_transaction_limit', 10
    ) as thresholds,
    500 as priority,
    true as is_active,
    jsonb_build_object(
        'system_generated', true,
        'rule_template', 'HIGH_RISK_VELOCITY_CONTROL',
        'compliance_category', 'TRANSACTION_BLOCKING'
    ) as metadata,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as updated_at,
    'SYSTEM_AUTO_GENERATED' as created_by
FROM tenants t
WHERE t.deleted_at IS NULL;

-- High Risk Profile: Comprehensive Geographic Restrictions
INSERT INTO rules (
    id, tenant_id, risk_profile_id, name, description, rule_type, rule_category,
    conditions, actions, thresholds, priority, is_active, metadata, created_at, updated_at, created_by
)
SELECT 
    generate_rule_id(),
    t.id as tenant_id,
    get_risk_profile_id(t.id, 'High Risk Profile') as risk_profile_id,
    'Comprehensive Geographic Restrictions' as name,
    'Block transactions to/from restricted countries and monitor medium-risk jurisdictions' as description,
    'GEOGRAPHIC'::RuleType as rule_type,
    'GEOGRAPHIC'::RuleCategory as rule_category,
    jsonb_build_object(
        'type', 'OR',
        'conditions', jsonb_build_array(
            jsonb_build_object(
                'field', 'transaction.destination_country',
                'operator', 'IN',
                'value', jsonb_build_array('AF', 'IR', 'KP', 'SY', 'MM', 'CU', 'SD'),
                'metadata', jsonb_build_object('list_type', 'BLOCKED_COUNTRIES', 'action', 'BLOCK')
            ),
            jsonb_build_object(
                'field', 'transaction.origin_country',
                'operator', 'IN',
                'value', jsonb_build_array('AF', 'IR', 'KP', 'SY', 'MM', 'CU', 'SD'),
                'metadata', jsonb_build_object('list_type', 'BLOCKED_COUNTRIES', 'action', 'BLOCK')
            ),
            jsonb_build_object(
                'field', 'transaction.destination_country',
                'operator', 'IN',
                'value', jsonb_build_array('PK', 'BD', 'LB', 'JO', 'EG', 'TR'),
                'metadata', jsonb_build_object('list_type', 'MONITORED_COUNTRIES', 'action', 'MONITOR')
            )
        )
    ) as conditions,
    jsonb_build_object(
        'actions', jsonb_build_array(
            jsonb_build_object(
                'type', 'BLOCK_TRANSACTION',
                'severity', 'CRITICAL',
                'message', 'Transaction blocked due to geographic restrictions',
                'metadata', jsonb_build_object('block_reason', 'GEOGRAPHIC_RESTRICTION')
            ),
            jsonb_build_object(
                'type', 'GENERATE_ALERT',
                'severity', 'CRITICAL',
                'message', 'High-risk customer attempted restricted geographic transaction',
                'metadata', jsonb_build_object('compliance_escalation', true)
            )
        )
    ) as actions,
    jsonb_build_object(
        'geographic_risk_threshold', 90
    ) as thresholds,
    600 as priority,
    true as is_active,
    jsonb_build_object(
        'system_generated', true,
        'rule_template', 'HIGH_RISK_GEOGRAPHIC_CONTROL',
        'compliance_category', 'GEOGRAPHIC_RESTRICTIONS',
        'blocked_countries', jsonb_build_array('AF', 'IR', 'KP', 'SY', 'MM', 'CU', 'SD'),
        'monitored_countries', jsonb_build_array('PK', 'BD', 'LB', 'JO', 'EG', 'TR')
    ) as metadata,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as updated_at,
    'SYSTEM_AUTO_GENERATED' as created_by
FROM tenants t
WHERE t.deleted_at IS NULL;

-- High Risk Profile: Counterparty Risk Assessment
INSERT INTO rules (
    id, tenant_id, risk_profile_id, name, description, rule_type, rule_category,
    conditions, actions, thresholds, priority, is_active, metadata, created_at, updated_at, created_by
)
SELECT 
    generate_rule_id(),
    t.id as tenant_id,
    get_risk_profile_id(t.id, 'High Risk Profile') as risk_profile_id,
    'High Risk Counterparty Screening' as name,
    'Enhanced screening for counterparties in transactions with high-risk customers' as description,
    'CONDITION'::RuleType as rule_type,
    'COUNTERPARTY'::RuleCategory as rule_category,
    jsonb_build_object(
        'type', 'OR',
        'conditions', jsonb_build_array(
            jsonb_build_object(
                'field', 'counterparty.risk_score',
                'operator', 'GREATER_THAN',
                'value', 70,
                'metadata', jsonb_build_object('score_type', 'COMPOSITE_RISK')
            ),
            jsonb_build_object(
                'field', 'counterparty.sanctions_match',
                'operator', 'EQUALS',
                'value', true,
                'metadata', jsonb_build_object('match_type', 'FUZZY_OR_EXACT')
            ),
            jsonb_build_object(
                'field', 'counterparty.pep_status',
                'operator', 'EQUALS',
                'value', true,
                'metadata', jsonb_build_object('pep_level', 'ANY')
            )
        )
    ) as conditions,
    jsonb_build_object(
        'actions', jsonb_build_array(
            jsonb_build_object(
                'type', 'BLOCK_TRANSACTION',
                'severity', 'HIGH',
                'message', 'Transaction blocked due to high-risk counterparty',
                'metadata', jsonb_build_object('requires_compliance_approval', true)
            ),
            jsonb_build_object(
                'type', 'REQUIRE_ENHANCED_DUE_DILIGENCE',
                'value', true,
                'metadata', jsonb_build_object('edd_type', 'COUNTERPARTY_INVESTIGATION')
            )
        )
    ) as actions,
    jsonb_build_object(
        'counterparty_risk_threshold', 70,
        'sanctions_tolerance', 0
    ) as thresholds,
    550 as priority,
    true as is_active,
    jsonb_build_object(
        'system_generated', true,
        'rule_template', 'HIGH_RISK_COUNTERPARTY_SCREENING',
        'compliance_category', 'COUNTERPARTY_RISK_MANAGEMENT'
    ) as metadata,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as updated_at,
    'SYSTEM_AUTO_GENERATED' as created_by
FROM tenants t
WHERE t.deleted_at IS NULL;

-- High Risk Profile: Anomaly Detection
INSERT INTO rules (
    id, tenant_id, risk_profile_id, name, description, rule_type, rule_category,
    conditions, actions, thresholds, priority, is_active, metadata, created_at, updated_at, created_by
)
SELECT 
    generate_rule_id(),
    t.id as tenant_id,
    get_risk_profile_id(t.id, 'High Risk Profile') as risk_profile_id,
    'Advanced Anomaly Detection' as name,
    'Machine learning-based anomaly detection for high-risk customers' as description,
    'PATTERN'::RuleType as rule_type,
    'ANOMALY'::RuleCategory as rule_category,
    jsonb_build_object(
        'type', 'OR',
        'conditions', jsonb_build_array(
            jsonb_build_object(
                'field', 'ml_model.anomaly_score',
                'operator', 'GREATER_THAN',
                'value', 0.85,
                'metadata', jsonb_build_object('model_version', 'v2.1', 'confidence_threshold', 0.85)
            ),
            jsonb_build_object(
                'field', 'transaction.pattern_deviation',
                'operator', 'GREATER_THAN',
                'value', 3.0,
                'metadata', jsonb_build_object('baseline_period', '90_days', 'deviation_type', 'STANDARD_DEVIATION')
            )
        )
    ) as conditions,
    jsonb_build_object(
        'actions', jsonb_build_array(
            jsonb_build_object(
                'type', 'FLAG_FOR_REVIEW',
                'severity', 'HIGH',
                'message', 'Anomalous transaction pattern detected by ML model',
                'metadata', jsonb_build_object('review_priority', 'HIGH', 'ml_analysis_required', true)
            ),
            jsonb_build_object(
                'type', 'GENERATE_ALERT',
                'severity', 'HIGH',
                'message', 'ML anomaly detection triggered',
                'metadata', jsonb_build_object('model_confidence', 'HIGH')
            )
        )
    ) as actions,
    jsonb_build_object(
        'anomaly_threshold', 0.85,
        'deviation_threshold', 3.0
    ) as thresholds,
    450 as priority,
    true as is_active,
    jsonb_build_object(
        'system_generated', true,
        'rule_template', 'HIGH_RISK_ANOMALY_DETECTION',
        'compliance_category', 'ADVANCED_ANALYTICS',
        'ml_model_enabled', true
    ) as metadata,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as updated_at,
    'SYSTEM_AUTO_GENERATED' as created_by
FROM tenants t
WHERE t.deleted_at IS NULL;

-- Clean up helper functions
DROP FUNCTION IF EXISTS generate_rule_id();
DROP FUNCTION IF EXISTS get_risk_profile_id(TEXT, TEXT);
