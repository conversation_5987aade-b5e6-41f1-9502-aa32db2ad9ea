-- Seed Enhanced Monitoring and High Risk Profile Rules
-- This script continues the default rules seeding for medium and high-risk profiles

-- Helper function to generate rule IDs (redefine for this file)
CREATE OR REPLACE FUNCTION generate_rule_id() RETURNS TEXT AS $$
BEGIN
    RETURN 'rule_' || LOWER(REPLACE(gen_random_uuid()::text, '-', ''));
END;
$$ LANGUAGE plpgsql;

-- Helper function to get risk profile ID by name and tenant (redefine for this file)
CREATE OR REPLACE FUNCTION get_risk_profile_id(tenant_id_param TEXT, profile_name TEXT) RETURNS TEXT AS $$
DECLARE
    profile_id TEXT;
BEGIN
    SELECT id INTO profile_id 
    FROM risk_profiles 
    WHERE tenant_id = tenant_id_param 
    AND name = profile_name 
    AND is_system_generated = true;
    
    RETURN profile_id;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- ENHANCED MONITORING PROFILE RULES (MEDIUM risk customers) - CONTINUED
-- =============================================================================

-- Enhanced Profile: Behavioral Pattern Detection
INSERT INTO rules (
    id, tenant_id, risk_profile_id, name, description, rule_type, rule_category,
    conditions, actions, thresholds, priority, is_active, metadata, created_at, updated_at, created_by
)
SELECT 
    generate_rule_id(),
    t.id as tenant_id,
    get_risk_profile_id(t.id, 'Enhanced Monitoring Profile') as risk_profile_id,
    'Unusual Behavioral Pattern' as name,
    'Detect unusual behavioral patterns for medium-risk customers' as description,
    'PATTERN'::RuleType as rule_type,
    'BEHAVIORAL'::RuleCategory as rule_category,
    jsonb_build_object(
        'type', 'OR',
        'conditions', jsonb_build_array(
            jsonb_build_object(
                'field', 'customer.transaction_time_pattern',
                'operator', 'NOT_CONTAINS',
                'value', 'business_hours',
                'metadata', jsonb_build_object('pattern_type', 'TIME_ANOMALY')
            ),
            jsonb_build_object(
                'field', 'customer.velocity_change',
                'operator', 'GREATER_THAN',
                'value', 300,
                'metadata', jsonb_build_object('baseline_period', '30_days', 'unit', 'percent')
            )
        )
    ) as conditions,
    jsonb_build_object(
        'actions', jsonb_build_array(
            jsonb_build_object(
                'type', 'GENERATE_ALERT',
                'severity', 'MEDIUM',
                'message', 'Unusual behavioral pattern detected for medium-risk customer',
                'metadata', jsonb_build_object('pattern_analysis_required', true)
            )
        )
    ) as actions,
    jsonb_build_object(
        'velocity_threshold', 300,
        'pattern_confidence', 0.75
    ) as thresholds,
    175 as priority,
    true as is_active,
    jsonb_build_object(
        'system_generated', true,
        'rule_template', 'ENHANCED_BEHAVIORAL_MONITORING',
        'compliance_category', 'BEHAVIORAL_ANALYSIS'
    ) as metadata,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as updated_at,
    'SYSTEM_AUTO_GENERATED' as created_by
FROM tenants t
WHERE t.deleted_at IS NULL;

-- Enhanced Profile: Channel Risk Monitoring
INSERT INTO rules (
    id, tenant_id, risk_profile_id, name, description, rule_type, rule_category,
    conditions, actions, thresholds, priority, is_active, metadata, created_at, updated_at, created_by
)
SELECT 
    generate_rule_id(),
    t.id as tenant_id,
    get_risk_profile_id(t.id, 'Enhanced Monitoring Profile') as risk_profile_id,
    'High Risk Channel Usage' as name,
    'Monitor usage of high-risk transaction channels' as description,
    'CONDITION'::RuleType as rule_type,
    'CHANNEL'::RuleCategory as rule_category,
    jsonb_build_object(
        'type', 'AND',
        'conditions', jsonb_build_array(
            jsonb_build_object(
                'field', 'transaction.channel',
                'operator', 'IN',
                'value', jsonb_build_array('ATM_INTERNATIONAL', 'WIRE_TRANSFER', 'CRYPTO_EXCHANGE'),
                'metadata', jsonb_build_object('risk_level', 'HIGH')
            ),
            jsonb_build_object(
                'field', 'transaction.amount',
                'operator', 'GREATER_THAN',
                'value', 2500,
                'metadata', jsonb_build_object('currency', 'USD')
            )
        )
    ) as conditions,
    jsonb_build_object(
        'actions', jsonb_build_array(
            jsonb_build_object(
                'type', 'FLAG_FOR_REVIEW',
                'severity', 'MEDIUM',
                'message', 'High-risk channel usage detected',
                'metadata', jsonb_build_object('review_type', 'CHANNEL_RISK_ASSESSMENT')
            )
        )
    ) as actions,
    jsonb_build_object(
        'amount_threshold', 2500,
        'channel_risk_score', 7
    ) as thresholds,
    200 as priority,
    true as is_active,
    jsonb_build_object(
        'system_generated', true,
        'rule_template', 'ENHANCED_CHANNEL_MONITORING',
        'compliance_category', 'CHANNEL_RISK_MANAGEMENT'
    ) as metadata,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as updated_at,
    'SYSTEM_AUTO_GENERATED' as created_by
FROM tenants t
WHERE t.deleted_at IS NULL;

-- =============================================================================
-- HIGH RISK PROFILE RULES (HIGH/CRITICAL risk customers)
-- Comprehensive monitoring with strict thresholds and immediate actions
-- =============================================================================

-- High Risk Profile: Very Low Transaction Threshold
INSERT INTO rules (
    id, tenant_id, risk_profile_id, name, description, rule_type, rule_category,
    conditions, actions, thresholds, priority, is_active, metadata, created_at, updated_at, created_by
)
SELECT 
    generate_rule_id(),
    t.id as tenant_id,
    get_risk_profile_id(t.id, 'High Risk Profile') as risk_profile_id,
    'High Risk Transaction Alert' as name,
    'Immediate alert for any significant transaction from high-risk customers' as description,
    'THRESHOLD'::RuleType as rule_type,
    'TRANSACTION'::RuleCategory as rule_category,
    jsonb_build_object(
        'type', 'AND',
        'conditions', jsonb_build_array(
            jsonb_build_object(
                'field', 'transaction.amount',
                'operator', 'GREATER_THAN',
                'value', 1000,
                'metadata', jsonb_build_object('currency', 'USD')
            )
        )
    ) as conditions,
    jsonb_build_object(
        'actions', jsonb_build_array(
            jsonb_build_object(
                'type', 'GENERATE_ALERT',
                'severity', 'HIGH',
                'message', 'Transaction from high-risk customer requires immediate review',
                'metadata', jsonb_build_object('immediate_notification', true, 'auto_resolve_hours', 2)
            ),
            jsonb_build_object(
                'type', 'REQUIRE_ENHANCED_DUE_DILIGENCE',
                'value', true,
                'metadata', jsonb_build_object('edd_type', 'COMPREHENSIVE_REVIEW')
            ),
            jsonb_build_object(
                'type', 'FLAG_FOR_REVIEW',
                'severity', 'HIGH',
                'message', 'High-risk customer transaction flagged',
                'metadata', jsonb_build_object('priority', 'URGENT')
            )
        )
    ) as actions,
    jsonb_build_object(
        'amount_threshold', 1000,
        'alert_threshold', 1
    ) as thresholds,
    400 as priority,
    true as is_active,
    jsonb_build_object(
        'system_generated', true,
        'rule_template', 'HIGH_RISK_TRANSACTION_MONITORING',
        'compliance_category', 'HIGH_RISK_CUSTOMER_MONITORING'
    ) as metadata,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as updated_at,
    'SYSTEM_AUTO_GENERATED' as created_by
FROM tenants t
WHERE t.deleted_at IS NULL;
