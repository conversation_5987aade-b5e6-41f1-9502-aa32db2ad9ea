-- CreateEnum for Rule Categories
CREATE TYPE "RuleCategory" AS ENUM ('TRANSACTION', 'BEHAVIOR<PERSON>', 'GEOGRAPH<PERSON>', 'FREQUENCY', 'CHANNEL', 'COUNTERPARTY', 'ANOMALY');

-- <PERSON><PERSON><PERSON>num for Rule Types  
CREATE TYPE "RuleType" AS ENUM ('THRESHOLD', 'CONDITION', 'PATTERN', 'GEOGRAPHIC', 'FREQUENCY_BASED', 'AMOUNT_BASED', 'CUSTOM');

-- CreateEnum for Assignment Types
CREATE TYPE "AssignmentType" AS ENUM ('AUTOMATIC', 'MANUAL', 'INHERITED');

-- Risk Profiles Table (formerly rule_categories)
CREATE TABLE "risk_profiles" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "rule_category" "RuleCategory" NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "is_system_generated" BOOLEAN NOT NULL DEFAULT false,
    "priority" INTEGER NOT NULL DEFAULT 0,
    "assignment_priority" INTEGER NOT NULL DEFAULT 0,
    "metadata" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT,

    CONSTRAINT "risk_profiles_pkey" PRIMARY KEY ("id")
);

-- Rules Table
CREATE TABLE "rules" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "risk_profile_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "rule_type" "RuleType" NOT NULL,
    "rule_category" "RuleCategory" NOT NULL,
    "conditions" JSONB NOT NULL,
    "actions" JSONB NOT NULL,
    "thresholds" JSONB,
    "priority" INTEGER NOT NULL DEFAULT 0,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "effective_from" TIMESTAMP(3),
    "effective_until" TIMESTAMP(3),
    "metadata" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT,

    CONSTRAINT "rules_pkey" PRIMARY KEY ("id")
);

-- Customer Risk Profile Assignments Table
CREATE TABLE "customer_risk_profile_assignments" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "customer_id" TEXT NOT NULL,
    "risk_profile_id" TEXT NOT NULL,
    "assignment_type" "AssignmentType" NOT NULL,
    "assignment_criteria" JSONB,
    "assignment_priority" INTEGER NOT NULL DEFAULT 0,
    "allow_override" BOOLEAN NOT NULL DEFAULT false,
    "assigned_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "assigned_by" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "effective_from" TIMESTAMP(3),
    "effective_until" TIMESTAMP(3),
    "metadata" JSONB,

    CONSTRAINT "customer_risk_profile_assignments_pkey" PRIMARY KEY ("id")
);

-- Rule Execution History Table
CREATE TABLE "rule_execution_history" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "rule_id" TEXT NOT NULL,
    "customer_id" TEXT,
    "risk_profile_id" TEXT NOT NULL,
    "execution_context" JSONB,
    "rule_result" JSONB,
    "actions_taken" JSONB,
    "executed_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "execution_time_ms" INTEGER,
    "priority_used" INTEGER,

    CONSTRAINT "rule_execution_history_pkey" PRIMARY KEY ("id")
);

-- Create Unique Constraints
ALTER TABLE "risk_profiles" ADD CONSTRAINT "risk_profiles_tenant_id_name_key" UNIQUE ("tenant_id", "name");
ALTER TABLE "rules" ADD CONSTRAINT "rules_tenant_id_risk_profile_id_name_key" UNIQUE ("tenant_id", "risk_profile_id", "name");
ALTER TABLE "customer_risk_profile_assignments" ADD CONSTRAINT "customer_risk_profile_assignments_tenant_id_customer_id_risk_pr_key" UNIQUE ("tenant_id", "customer_id", "risk_profile_id");

-- Create Foreign Key Constraints
ALTER TABLE "rules" ADD CONSTRAINT "rules_risk_profile_id_fkey" FOREIGN KEY ("risk_profile_id") REFERENCES "risk_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "customer_risk_profile_assignments" ADD CONSTRAINT "customer_risk_profile_assignments_risk_profile_id_fkey" FOREIGN KEY ("risk_profile_id") REFERENCES "risk_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "rule_execution_history" ADD CONSTRAINT "rule_execution_history_rule_id_fkey" FOREIGN KEY ("rule_id") REFERENCES "rules"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "rule_execution_history" ADD CONSTRAINT "rule_execution_history_risk_profile_id_fkey" FOREIGN KEY ("risk_profile_id") REFERENCES "risk_profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Create Indexes for Performance
CREATE INDEX "risk_profiles_tenant_id_is_active_idx" ON "risk_profiles"("tenant_id", "is_active");
CREATE INDEX "risk_profiles_rule_category_idx" ON "risk_profiles"("rule_category");
CREATE INDEX "risk_profiles_is_system_generated_idx" ON "risk_profiles"("is_system_generated");
CREATE INDEX "risk_profiles_assignment_priority_idx" ON "risk_profiles"("assignment_priority");

CREATE INDEX "rules_tenant_id_risk_profile_id_idx" ON "rules"("tenant_id", "risk_profile_id");
CREATE INDEX "rules_tenant_id_is_active_idx" ON "rules"("tenant_id", "is_active");
CREATE INDEX "rules_rule_category_idx" ON "rules"("rule_category");
CREATE INDEX "rules_effective_period_idx" ON "rules"("effective_from", "effective_until");
CREATE INDEX "rules_priority_idx" ON "rules"("priority");

CREATE INDEX "customer_risk_profile_assignments_tenant_id_customer_id_idx" ON "customer_risk_profile_assignments"("tenant_id", "customer_id");
CREATE INDEX "customer_risk_profile_assignments_tenant_id_risk_profile_id_idx" ON "customer_risk_profile_assignments"("tenant_id", "risk_profile_id");
CREATE INDEX "customer_risk_profile_assignments_assignment_type_idx" ON "customer_risk_profile_assignments"("assignment_type");
CREATE INDEX "customer_risk_profile_assignments_assignment_priority_idx" ON "customer_risk_profile_assignments"("assignment_priority");
CREATE INDEX "customer_risk_profile_assignments_is_active_idx" ON "customer_risk_profile_assignments"("is_active");

CREATE INDEX "rule_execution_history_tenant_id_executed_at_idx" ON "rule_execution_history"("tenant_id", "executed_at");
CREATE INDEX "rule_execution_history_rule_id_executed_at_idx" ON "rule_execution_history"("rule_id", "executed_at");
CREATE INDEX "rule_execution_history_customer_id_executed_at_idx" ON "rule_execution_history"("customer_id", "executed_at");
CREATE INDEX "rule_execution_history_risk_profile_id_idx" ON "rule_execution_history"("risk_profile_id");
