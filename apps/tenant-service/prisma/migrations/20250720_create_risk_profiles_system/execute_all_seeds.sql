-- Master Seed Execution Script for Risk Profile Management System
-- This script executes all seeding operations in the correct order

-- =============================================================================
-- STEP 1: Create Default Risk Profiles for All Tenants
-- =============================================================================

\i seed_default_profiles.sql

-- =============================================================================
-- STEP 2: Create Default Rules for Standard Risk Profile
-- =============================================================================

\i seed_default_rules.sql

-- =============================================================================
-- STEP 3: Create Enhanced Monitoring and Initial High Risk Rules
-- =============================================================================

\i seed_enhanced_high_risk_rules.sql

-- =============================================================================
-- STEP 4: Create Advanced High Risk Profile Rules
-- =============================================================================

\i seed_high_risk_advanced_rules.sql

-- =============================================================================
-- VERIFICATION QUERIES
-- =============================================================================

-- Verify risk profiles were created
SELECT 
    t.name as tenant_name,
    rp.name as risk_profile_name,
    rp.rule_category,
    rp.is_system_generated,
    rp.priority,
    rp.assignment_priority
FROM tenants t
JOIN risk_profiles rp ON t.id = rp.tenant_id
WHERE rp.is_system_generated = true
ORDER BY t.name, rp.assignment_priority;

-- Verify rules were created
SELECT 
    t.name as tenant_name,
    rp.name as risk_profile_name,
    r.name as rule_name,
    r.rule_type,
    r.rule_category,
    r.priority,
    r.is_active
FROM tenants t
JOIN risk_profiles rp ON t.id = rp.tenant_id
JOIN rules r ON rp.id = r.risk_profile_id
WHERE rp.is_system_generated = true
ORDER BY t.name, rp.assignment_priority, r.priority DESC;

-- Count summary
SELECT 
    'Risk Profiles Created' as metric,
    COUNT(*) as count
FROM risk_profiles 
WHERE is_system_generated = true

UNION ALL

SELECT 
    'Rules Created' as metric,
    COUNT(*) as count
FROM rules r
JOIN risk_profiles rp ON r.risk_profile_id = rp.id
WHERE rp.is_system_generated = true

UNION ALL

SELECT 
    'Tenants with Profiles' as metric,
    COUNT(DISTINCT rp.tenant_id) as count
FROM risk_profiles rp
WHERE rp.is_system_generated = true;

-- Rules by category summary
SELECT 
    r.rule_category,
    COUNT(*) as rule_count,
    COUNT(DISTINCT rp.tenant_id) as tenant_count
FROM rules r
JOIN risk_profiles rp ON r.risk_profile_id = rp.id
WHERE rp.is_system_generated = true
GROUP BY r.rule_category
ORDER BY rule_count DESC;
