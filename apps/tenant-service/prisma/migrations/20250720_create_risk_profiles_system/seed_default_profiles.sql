-- Seed Default System-Generated Risk Profiles for All Tenants
-- This script creates the three default risk profiles that are automatically assigned based on customer risk levels

-- Function to generate risk profile IDs
CREATE OR REPLACE FUNCTION generate_risk_profile_id() RETURNS TEXT AS $$
BEGIN
    RETURN 'rp_' || LOWER(REPLACE(gen_random_uuid()::text, '-', ''));
END;
$$ LANGUAGE plpgsql;

-- Insert default risk profiles for each existing tenant
INSERT INTO risk_profiles (
    id,
    tenant_id,
    name,
    description,
    rule_category,
    is_active,
    is_system_generated,
    priority,
    assignment_priority,
    metadata,
    created_at,
    updated_at,
    created_by
)
SELECT 
    generate_risk_profile_id(),
    t.id as tenant_id,
    'Standard Risk Profile' as name,
    'Default profile for customers with LOW risk level. Applies standard monitoring and compliance rules.' as description,
    'TRANSACTION'::RuleCategory as rule_category,
    true as is_active,
    true as is_system_generated,
    100 as priority,
    100 as assignment_priority,
    jsonb_build_object(
        'auto_assignment_criteria', jsonb_build_object(
            'customer_risk_level', 'LOW'
        ),
        'system_profile_type', 'STANDARD',
        'created_by_system', true
    ) as metadata,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as updated_at,
    'SYSTEM_AUTO_GENERATED' as created_by
FROM tenants t
WHERE t.deleted_at IS NULL
UNION ALL
SELECT 
    generate_risk_profile_id(),
    t.id as tenant_id,
    'Enhanced Monitoring Profile' as name,
    'Default profile for customers with MEDIUM risk level. Applies enhanced monitoring and additional compliance checks.' as description,
    'BEHAVIORAL'::RuleCategory as rule_category,
    true as is_active,
    true as is_system_generated,
    200 as priority,
    200 as assignment_priority,
    jsonb_build_object(
        'auto_assignment_criteria', jsonb_build_object(
            'customer_risk_level', 'MEDIUM'
        ),
        'system_profile_type', 'ENHANCED',
        'created_by_system', true
    ) as metadata,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as updated_at,
    'SYSTEM_AUTO_GENERATED' as created_by
FROM tenants t
WHERE t.deleted_at IS NULL
UNION ALL
SELECT 
    generate_risk_profile_id(),
    t.id as tenant_id,
    'High Risk Profile' as name,
    'Default profile for customers with HIGH or CRITICAL risk level. Applies strict monitoring, enhanced due diligence, and comprehensive compliance rules.' as description,
    'ANOMALY'::RuleCategory as rule_category,
    true as is_active,
    true as is_system_generated,
    300 as priority,
    300 as assignment_priority,
    jsonb_build_object(
        'auto_assignment_criteria', jsonb_build_object(
            'customer_risk_level', jsonb_build_array('HIGH', 'CRITICAL')
        ),
        'system_profile_type', 'HIGH_RISK',
        'created_by_system', true
    ) as metadata,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as updated_at,
    'SYSTEM_AUTO_GENERATED' as created_by
FROM tenants t
WHERE t.deleted_at IS NULL;

-- Create default rules for Standard Risk Profile
INSERT INTO rules (
    id,
    tenant_id,
    risk_profile_id,
    name,
    description,
    rule_type,
    rule_category,
    conditions,
    actions,
    thresholds,
    priority,
    is_active,
    effective_from,
    effective_until,
    metadata,
    created_at,
    updated_at,
    created_by
)
SELECT 
    'rule_' || LOWER(REPLACE(gen_random_uuid()::text, '-', '')) as id,
    rp.tenant_id,
    rp.id as risk_profile_id,
    'Standard Transaction Monitoring' as name,
    'Basic transaction monitoring for standard risk customers' as description,
    'THRESHOLD'::RuleType as rule_type,
    'TRANSACTION'::RuleCategory as rule_category,
    jsonb_build_object(
        'type', 'AND',
        'conditions', jsonb_build_array(
            jsonb_build_object(
                'field', 'transaction.amount',
                'operator', 'GREATER_THAN',
                'value', 10000
            )
        )
    ) as conditions,
    jsonb_build_object(
        'actions', jsonb_build_array(
            jsonb_build_object(
                'type', 'GENERATE_ALERT',
                'severity', 'LOW',
                'message', 'Large transaction detected for standard risk customer'
            )
        )
    ) as actions,
    jsonb_build_object(
        'amount_threshold', 10000,
        'frequency_threshold', 5
    ) as thresholds,
    100 as priority,
    true as is_active,
    CURRENT_TIMESTAMP as effective_from,
    NULL as effective_until,
    jsonb_build_object(
        'system_generated', true,
        'rule_template', 'STANDARD_TRANSACTION_MONITORING'
    ) as metadata,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as updated_at,
    'SYSTEM_AUTO_GENERATED' as created_by
FROM risk_profiles rp
WHERE rp.is_system_generated = true 
AND rp.name = 'Standard Risk Profile';

-- Drop the temporary function
DROP FUNCTION generate_risk_profile_id();
