-- Seed Default Rules for System-Generated Risk Profiles
-- This script creates comprehensive default rules for each risk profile type
-- Rules cover all major categories and demonstrate different rule types

-- Helper function to generate rule IDs
CREATE OR REPLACE FUNCTION generate_rule_id() RETURNS TEXT AS $$
BEGIN
    RETURN 'rule_' || LOWER(REPLACE(gen_random_uuid()::text, '-', ''));
END;
$$ LANGUAGE plpgsql;

-- Helper function to get risk profile ID by name and tenant
CREATE OR REPLACE FUNCTION get_risk_profile_id(tenant_id_param TEXT, profile_name TEXT) RETURNS TEXT AS $$
DECLARE
    profile_id TEXT;
BEGIN
    SELECT id INTO profile_id 
    FROM risk_profiles 
    WHERE tenant_id = tenant_id_param 
    AND name = profile_name 
    AND is_system_generated = true;
    
    RETURN profile_id;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- STANDARD RISK PROFILE RULES (LOW risk customers)
-- Basic monitoring with standard thresholds
-- =============================================================================

INSERT INTO rules (
    id, tenant_id, risk_profile_id, name, description, rule_type, rule_category,
    conditions, actions, thresholds, priority, is_active, metadata, created_at, updated_at, created_by
)
SELECT 
    generate_rule_id(),
    t.id as tenant_id,
    get_risk_profile_id(t.id, 'Standard Risk Profile') as risk_profile_id,
    'Large Transaction Alert' as name,
    'Generate alert for transactions exceeding standard threshold for low-risk customers' as description,
    'THRESHOLD'::RuleType as rule_type,
    'TRANSACTION'::RuleCategory as rule_category,
    jsonb_build_object(
        'type', 'AND',
        'conditions', jsonb_build_array(
            jsonb_build_object(
                'field', 'transaction.amount',
                'operator', 'GREATER_THAN',
                'value', 10000,
                'metadata', jsonb_build_object('currency', 'USD')
            )
        )
    ) as conditions,
    jsonb_build_object(
        'actions', jsonb_build_array(
            jsonb_build_object(
                'type', 'GENERATE_ALERT',
                'severity', 'LOW',
                'message', 'Large transaction detected for standard risk customer',
                'metadata', jsonb_build_object('auto_resolve_hours', 24)
            )
        )
    ) as actions,
    jsonb_build_object(
        'amount_threshold', 10000,
        'alert_threshold', 1
    ) as thresholds,
    100 as priority,
    true as is_active,
    jsonb_build_object(
        'system_generated', true,
        'rule_template', 'STANDARD_LARGE_TRANSACTION',
        'compliance_category', 'TRANSACTION_MONITORING'
    ) as metadata,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as updated_at,
    'SYSTEM_AUTO_GENERATED' as created_by
FROM tenants t
WHERE t.deleted_at IS NULL;

-- Standard Risk Profile: High Frequency Transaction Monitoring
INSERT INTO rules (
    id, tenant_id, risk_profile_id, name, description, rule_type, rule_category,
    conditions, actions, thresholds, priority, is_active, metadata, created_at, updated_at, created_by
)
SELECT 
    generate_rule_id(),
    t.id as tenant_id,
    get_risk_profile_id(t.id, 'Standard Risk Profile') as risk_profile_id,
    'High Frequency Transaction Pattern' as name,
    'Monitor for unusual transaction frequency patterns in standard risk customers' as description,
    'FREQUENCY_BASED'::RuleType as rule_type,
    'FREQUENCY'::RuleCategory as rule_category,
    jsonb_build_object(
        'type', 'AND',
        'conditions', jsonb_build_array(
            jsonb_build_object(
                'field', 'transaction.count',
                'operator', 'GREATER_THAN',
                'value', 20,
                'metadata', jsonb_build_object('time_window', '24_hours')
            ),
            jsonb_build_object(
                'field', 'transaction.total_amount',
                'operator', 'GREATER_THAN',
                'value', 50000,
                'metadata', jsonb_build_object('time_window', '24_hours')
            )
        )
    ) as conditions,
    jsonb_build_object(
        'actions', jsonb_build_array(
            jsonb_build_object(
                'type', 'GENERATE_ALERT',
                'severity', 'MEDIUM',
                'message', 'High frequency transaction pattern detected',
                'metadata', jsonb_build_object('requires_review', true)
            )
        )
    ) as actions,
    jsonb_build_object(
        'frequency_threshold', 20,
        'amount_threshold', 50000,
        'time_window_hours', 24
    ) as thresholds,
    200 as priority,
    true as is_active,
    jsonb_build_object(
        'system_generated', true,
        'rule_template', 'STANDARD_FREQUENCY_MONITORING',
        'compliance_category', 'BEHAVIORAL_MONITORING'
    ) as metadata,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as updated_at,
    'SYSTEM_AUTO_GENERATED' as created_by
FROM tenants t
WHERE t.deleted_at IS NULL;

-- Standard Risk Profile: Basic Geographic Monitoring
INSERT INTO rules (
    id, tenant_id, risk_profile_id, name, description, rule_type, rule_category,
    conditions, actions, thresholds, priority, is_active, metadata, created_at, updated_at, created_by
)
SELECT 
    generate_rule_id(),
    t.id as tenant_id,
    get_risk_profile_id(t.id, 'Standard Risk Profile') as risk_profile_id,
    'High Risk Country Transaction' as name,
    'Monitor transactions to/from high-risk countries for standard customers' as description,
    'GEOGRAPHIC'::RuleType as rule_type,
    'GEOGRAPHIC'::RuleCategory as rule_category,
    jsonb_build_object(
        'type', 'OR',
        'conditions', jsonb_build_array(
            jsonb_build_object(
                'field', 'transaction.destination_country',
                'operator', 'IN',
                'value', jsonb_build_array('AF', 'IR', 'KP', 'SY', 'MM'),
                'metadata', jsonb_build_object('list_type', 'OFAC_SANCTIONS')
            ),
            jsonb_build_object(
                'field', 'transaction.origin_country',
                'operator', 'IN',
                'value', jsonb_build_array('AF', 'IR', 'KP', 'SY', 'MM'),
                'metadata', jsonb_build_object('list_type', 'OFAC_SANCTIONS')
            )
        )
    ) as conditions,
    jsonb_build_object(
        'actions', jsonb_build_array(
            jsonb_build_object(
                'type', 'FLAG_FOR_REVIEW',
                'severity', 'HIGH',
                'message', 'Transaction involves high-risk jurisdiction',
                'metadata', jsonb_build_object('requires_manual_review', true, 'escalation_required', true)
            )
        )
    ) as actions,
    jsonb_build_object(
        'risk_score_threshold', 75
    ) as thresholds,
    300 as priority,
    true as is_active,
    jsonb_build_object(
        'system_generated', true,
        'rule_template', 'STANDARD_GEOGRAPHIC_MONITORING',
        'compliance_category', 'SANCTIONS_SCREENING',
        'country_codes', jsonb_build_array('AF', 'IR', 'KP', 'SY', 'MM')
    ) as metadata,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as updated_at,
    'SYSTEM_AUTO_GENERATED' as created_by
FROM tenants t
WHERE t.deleted_at IS NULL;

-- =============================================================================
-- ENHANCED MONITORING PROFILE RULES (MEDIUM risk customers)
-- Enhanced monitoring with stricter thresholds and additional checks
-- =============================================================================

-- Enhanced Profile: Lower Transaction Threshold
INSERT INTO rules (
    id, tenant_id, risk_profile_id, name, description, rule_type, rule_category,
    conditions, actions, thresholds, priority, is_active, metadata, created_at, updated_at, created_by
)
SELECT
    generate_rule_id(),
    t.id as tenant_id,
    get_risk_profile_id(t.id, 'Enhanced Monitoring Profile') as risk_profile_id,
    'Medium Risk Transaction Alert' as name,
    'Generate alert for transactions exceeding medium-risk threshold' as description,
    'THRESHOLD'::RuleType as rule_type,
    'TRANSACTION'::RuleCategory as rule_category,
    jsonb_build_object(
        'type', 'AND',
        'conditions', jsonb_build_array(
            jsonb_build_object(
                'field', 'transaction.amount',
                'operator', 'GREATER_THAN',
                'value', 5000,
                'metadata', jsonb_build_object('currency', 'USD')
            )
        )
    ) as conditions,
    jsonb_build_object(
        'actions', jsonb_build_array(
            jsonb_build_object(
                'type', 'GENERATE_ALERT',
                'severity', 'MEDIUM',
                'message', 'Transaction exceeds medium-risk threshold',
                'metadata', jsonb_build_object('auto_resolve_hours', 12)
            ),
            jsonb_build_object(
                'type', 'REQUIRE_ENHANCED_DUE_DILIGENCE',
                'value', true,
                'metadata', jsonb_build_object('edd_type', 'TRANSACTION_REVIEW')
            )
        )
    ) as actions,
    jsonb_build_object(
        'amount_threshold', 5000,
        'alert_threshold', 1
    ) as thresholds,
    150 as priority,
    true as is_active,
    jsonb_build_object(
        'system_generated', true,
        'rule_template', 'ENHANCED_TRANSACTION_MONITORING',
        'compliance_category', 'ENHANCED_DUE_DILIGENCE'
    ) as metadata,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as updated_at,
    'SYSTEM_AUTO_GENERATED' as created_by
FROM tenants t
WHERE t.deleted_at IS NULL;
