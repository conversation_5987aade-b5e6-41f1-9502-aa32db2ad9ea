# Default Risk Profile Rules Documentation

This document describes the comprehensive set of default rules created for each system-generated risk profile in the Qeep platform.

## Overview

The system automatically creates three risk profiles for each tenant with progressively stricter monitoring rules:

1. **Standard Risk Profile** (LOW risk customers) - 3 basic monitoring rules
2. **Enhanced Monitoring Profile** (MEDIUM risk customers) - 5 enhanced monitoring rules  
3. **High Risk Profile** (HIGH/CRITICAL risk customers) - 7 comprehensive monitoring rules

## Rule Categories Covered

- **TRANSACTION** - Amount-based transaction monitoring
- **BEHAVIORAL** - Pattern and behavioral analysis
- **GEOGRAPHIC** - Country and jurisdiction-based restrictions
- **FREQUENCY** - Velocity and frequency monitoring
- **CHANNEL** - Transaction channel risk assessment
- **COUNTERPARTY** - Third-party risk screening
- **ANOMALY** - Advanced anomaly detection

## Standard Risk Profile Rules

### 1. Large Transaction Alert
- **Type**: THRESHOLD
- **Category**: TRANSACTION
- **Trigger**: Transactions > $10,000 USD
- **Action**: Generate LOW severity alert
- **Purpose**: Basic monitoring for significant transactions

### 2. High Frequency Transaction Pattern
- **Type**: FREQUENCY_BASED
- **Category**: FREQUENCY
- **Trigger**: >20 transactions OR >$50,000 in 24 hours
- **Action**: Generate MEDIUM severity alert
- **Purpose**: Detect unusual transaction velocity

### 3. High Risk Country Transaction
- **Type**: GEOGRAPHIC
- **Category**: GEOGRAPHIC
- **Trigger**: Transactions to/from sanctioned countries (AF, IR, KP, SY, MM)
- **Action**: Flag for manual review with HIGH severity
- **Purpose**: Basic sanctions compliance

## Enhanced Monitoring Profile Rules

### 1. Medium Risk Transaction Alert
- **Type**: THRESHOLD
- **Category**: TRANSACTION
- **Trigger**: Transactions > $5,000 USD (lower threshold)
- **Action**: Generate MEDIUM alert + Require Enhanced Due Diligence
- **Purpose**: Stricter transaction monitoring

### 2. Unusual Behavioral Pattern
- **Type**: PATTERN
- **Category**: BEHAVIORAL
- **Trigger**: Off-hours transactions OR >300% velocity increase
- **Action**: Generate MEDIUM alert for pattern analysis
- **Purpose**: Behavioral anomaly detection

### 3. High Risk Channel Usage
- **Type**: CONDITION
- **Category**: CHANNEL
- **Trigger**: High-risk channels (ATM_INTERNATIONAL, WIRE_TRANSFER, CRYPTO_EXCHANGE) + >$2,500
- **Action**: Flag for channel risk assessment
- **Purpose**: Monitor risky transaction channels

## High Risk Profile Rules

### 1. High Risk Transaction Alert
- **Type**: THRESHOLD
- **Category**: TRANSACTION
- **Trigger**: Transactions > $1,000 USD (very low threshold)
- **Action**: HIGH alert + Enhanced DD + Flag for review
- **Purpose**: Immediate notification for any significant activity

### 2. High Risk Velocity Monitoring
- **Type**: FREQUENCY_BASED
- **Category**: FREQUENCY
- **Trigger**: >5 transactions/hour OR >$10,000/day OR >10 transactions/day
- **Action**: BLOCK transaction + CRITICAL alert
- **Purpose**: Strict velocity controls with blocking

### 3. Comprehensive Geographic Restrictions
- **Type**: GEOGRAPHIC
- **Category**: GEOGRAPHIC
- **Trigger**: Blocked countries (AF, IR, KP, SY, MM, CU, SD) OR monitored countries (PK, BD, LB, JO, EG, TR)
- **Action**: BLOCK transaction + CRITICAL alert
- **Purpose**: Comprehensive geographic controls

### 4. High Risk Counterparty Screening
- **Type**: CONDITION
- **Category**: COUNTERPARTY
- **Trigger**: Counterparty risk score >70 OR sanctions match OR PEP status
- **Action**: BLOCK transaction + Enhanced DD
- **Purpose**: Strict counterparty risk management

### 5. Advanced Anomaly Detection
- **Type**: PATTERN
- **Category**: ANOMALY
- **Trigger**: ML anomaly score >0.85 OR pattern deviation >3.0 standard deviations
- **Action**: Flag for review + HIGH alert
- **Purpose**: Machine learning-based anomaly detection

## Rule Priority System

Rules are assigned priorities that determine execution order:
- **Standard Profile**: 100-300 (basic monitoring)
- **Enhanced Profile**: 150-200 (medium priority)
- **High Risk Profile**: 400-600 (highest priority)

Higher priority rules are evaluated first and can override lower priority rules.

## Geographic Country Codes

### Blocked Countries (High Risk Profile)
- **AF** - Afghanistan
- **IR** - Iran
- **KP** - North Korea
- **SY** - Syria
- **MM** - Myanmar
- **CU** - Cuba
- **SD** - Sudan

### Monitored Countries (Enhanced/High Risk)
- **PK** - Pakistan
- **BD** - Bangladesh
- **LB** - Lebanon
- **JO** - Jordan
- **EG** - Egypt
- **TR** - Turkey

## Action Types Used

- **GENERATE_ALERT** - Create alert with specified severity
- **FLAG_FOR_REVIEW** - Mark for manual review
- **REQUIRE_ENHANCED_DUE_DILIGENCE** - Trigger EDD process
- **BLOCK_TRANSACTION** - Prevent transaction execution
- **SEND_NOTIFICATION** - Send real-time notifications

## Severity Levels

- **LOW** - Informational alerts, auto-resolve in 24 hours
- **MEDIUM** - Requires attention, auto-resolve in 12 hours
- **HIGH** - Urgent review required, auto-resolve in 2 hours
- **CRITICAL** - Immediate action required, manual resolution

## Customization Guidelines

Tenants can:
1. Modify thresholds in existing rules
2. Disable system rules (not recommended)
3. Create additional custom rules
4. Adjust priority levels
5. Modify action configurations

## Compliance Considerations

These default rules provide baseline compliance coverage for:
- **AML/BSA** - Anti-Money Laundering regulations
- **OFAC** - Sanctions screening
- **KYC** - Know Your Customer requirements
- **Transaction Monitoring** - Suspicious activity detection

## File Structure

- `seed_default_profiles.sql` - Creates the three risk profiles
- `seed_default_rules.sql` - Standard risk profile rules
- `seed_enhanced_high_risk_rules.sql` - Enhanced and initial high-risk rules
- `seed_high_risk_advanced_rules.sql` - Advanced high-risk rules
- `execute_all_seeds.sql` - Master execution script

## Execution

To seed all default rules:
```sql
\i execute_all_seeds.sql
```

Or execute individual files in order:
1. `seed_default_profiles.sql`
2. `seed_default_rules.sql`
3. `seed_enhanced_high_risk_rules.sql`
4. `seed_high_risk_advanced_rules.sql`
