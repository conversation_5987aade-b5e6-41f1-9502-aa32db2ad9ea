# Tenant Service

A comprehensive multi-tenant management service built with NestJS, providing tenant provisioning, management, and configuration capabilities.

## 🏗️ Architecture Overview

The Tenant Service follows a clean, modular architecture with clear separation of concerns:

```bash
tenant-service/
├── src/
│   ├── app.module.ts              # Main application module
│   ├── main.ts                    # Application entry point
│   │
│   ├── config/                    # Configuration management
│   │   ├── config.module.ts       # Configuration module
│   │   ├── config.service.ts      # Centralized config service
│   │   ├── database.config.ts     # Database configuration
│   │   └── tenant.config.ts       # Tenant-specific configuration
│   │
│   ├── core/                      # Core business logic & shared types
│   │   ├── enums/                 # Business enums
│   │   │   └── tenant-status.enum.ts
│   │   ├── interfaces/            # TypeScript interfaces
│   │   │   └── tenant.interface.ts
│   │   ├── constants/             # Application constants
│   │   │   └── tenant.constants.ts
│   │   └── types/                 # Custom types
│   │
│   ├── modules/                   # Feature modules
│   │   ├── tenant/                # Main tenant module
│   │   │   ├── tenant.controller.ts    # REST API endpoints
│   │   │   ├── tenant.service.ts       # Business logic
│   │   │   ├── tenant.module.ts        # Module definition
│   │   │   ├── tenant.repository.ts    # Data access layer
│   │   │   ├── tenant.grpc.controller.ts # gRPC endpoints
│   │   │   ├── tenant.seed.ts          # Database seeding
│   │   │   ├── dto/                    # Data transfer objects
│   │   │   ├── entities/               # Domain entities
│   │   │   └── events/                 # Domain events
│   │   │
│   │   ├── onboarding/            # Tenant onboarding logic
│   │   │   └── ...
│   │   │
│   │   └── [future modules]/      # Billing, settings, etc.
│   │
│   ├── database/                  # Database layer
│   │   ├── prisma.service.ts      # Prisma service
│   │   └── migrations/            # Database migrations
│   │
│   ├── events/                    # Event-driven architecture
│   │   ├── handlers/              # Event handlers
│   │   ├── producers/             # Event producers
│   │   └── consumers/             # Event consumers
│   │
│   ├── middleware/                # Custom middleware
│   │   └── tenant-context.middleware.ts
│   │
│   ├── interceptors/              # Custom interceptors
│   └── guards/                    # Custom guards
│
├── test/                          # Test files
├── prisma/                        # Prisma schema & migrations
├── .env                          # Environment variables
├── Dockerfile                    # Docker configuration
├── docker-compose.yml           # Docker Compose setup
└── README.md                     # This file
```

## 🚀 Features

### Core Tenant Management

- ✅ **CRUD Operations**: Create, read, update, delete tenants
- ✅ **Status Management**: Active, inactive, suspended, pending states
- ✅ **Settings Management**: Customizable tenant-specific settings
- ✅ **Search & Filtering**: Advanced tenant search and filtering
- ✅ **Pagination**: Efficient data pagination for large datasets

### API Interfaces

- ✅ **REST API**: Complete HTTP REST API with OpenAPI documentation
- ✅ **gRPC API**: High-performance gRPC interface for service-to-service communication
- ✅ **Authentication**: JWT-based authentication and authorization
- ✅ **Validation**: Comprehensive input validation and sanitization

### Data Management

- ✅ **Repository Pattern**: Clean data access layer with repository pattern
- ✅ **Database Migrations**: Automated database schema management
- ✅ **Seeding**: Development data seeding capabilities
- ✅ **Transactions**: Database transaction support for data consistency

### Configuration & Environment

- ✅ **Environment-based Config**: Separate configurations for dev/staging/production
- ✅ **Type-safe Configuration**: Strongly typed configuration with validation
- ✅ **Default Settings**: Configurable default tenant settings
- ✅ **Feature Flags**: Environment-based feature toggles

## 📋 API Endpoints

### REST API (`/api/v1/tenants`)

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/` | List all tenants (with pagination/filtering) |
| `GET` | `/:id` | Get tenant by ID |
| `POST` | `/` | Create new tenant |
| `PUT` | `/:id` | Update tenant |
| `DELETE` | `/:id` | Delete tenant |
| `PUT` | `/:id/status` | Update tenant status |
| `GET` | `/:id/stats` | Get tenant statistics |
| `GET` | `/stats/overview` | Get management overview |

### gRPC API

- `GetTenant(GetTenantRequest) → GetTenantResponse`
- `CreateTenant(CreateTenantRequest) → CreateTenantResponse`
- `UpdateTenant(UpdateTenantRequest) → UpdateTenantResponse`
- `ListTenants(ListTenantsRequest) → ListTenantsResponse`

## 🛠️ Development

### Prerequisites

- Node.js 18+
- PostgreSQL 14+
- Redis (optional, for caching)

### Setup

```bash
# Install dependencies
npm install

# Setup environment
cp .env.example .env

# Run database migrations
npx prisma migrate dev

# Seed development data
npm run seed

# Start development server
npm run dev
```

### Environment Variables

```env
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/tenant_service"
DB_SCHEMA="tenant_service"

# Server
PORT=3003
GRPC_PORT=3014
NODE_ENV=development
LOG_LEVEL=debug

# JWT
JWT_SECRET="your-secret-key"
JWT_EXPIRES_IN="24h"

# Tenant Configuration
DEFAULT_TENANT_PLAN="basic"
MAX_TENANTS_PER_USER=5
AUTO_PROVISION_TENANTS=true
```

## 🧪 Testing

```bash
# Unit tests
npm run test

# Integration tests
npm run test:e2e

# Test coverage
npm run test:cov
```

## 📦 Deployment

### Docker

```bash
# Build image
docker build -t tenant-service .

# Run container
docker run -p 3003:3003 -p 3014:3014 tenant-service
```

### Docker Compose

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f tenant-service
```

## 🔧 Configuration

The service uses a hierarchical configuration system:

1. **Default values** in configuration files
2. **Environment variables** override defaults
3. **Runtime configuration** for dynamic settings

### Key Configuration Areas

- **Database**: Connection, pooling, migrations
- **Tenant Defaults**: Default settings for new tenants
- **Limits & Quotas**: Resource limits per tenant
- **Security**: Authentication, authorization, rate limiting
- **Features**: Feature flags and toggles

## 📊 Monitoring & Observability

- **Health Checks**: `/health` endpoint for service health
- **Metrics**: Prometheus-compatible metrics
- **Logging**: Structured logging with correlation IDs
- **Tracing**: Distributed tracing support

## 🔐 Security

- **JWT Authentication**: Secure token-based authentication
- **Input Validation**: Comprehensive input sanitization
- **Rate Limiting**: API rate limiting and throttling
- **CORS**: Configurable CORS policies
- **Security Headers**: Standard security headers

## 🚦 Status

- ✅ **Production Ready**: Core tenant management
- 🚧 **In Development**: Advanced features (billing, analytics)
- 📋 **Planned**: Multi-region support, advanced monitoring

## 📝 Contributing

1. Follow the established folder structure
2. Add tests for new features
3. Update documentation
4. Follow TypeScript best practices
5. Use conventional commits

## 🔄 Migration from Old Structure

### Removed Duplicates (Now in @qeep/common)

The following components were removed from the service as they're now provided by the shared common library:

#### Removed Local Components

- ❌ `src/common/interceptors/` → ✅ `@qeep/common` (ResponseFormatInterceptor, etc.)
- ❌ `src/common/pipes/` → ✅ `@qeep/common` (ValidationPipe, etc.)
- ❌ `src/common/utils/` → ✅ `@qeep/common` (Shared utilities)
- ❌ Local JWT guards → ✅ `@qeep/common` (JwtAuthGuard)
- ❌ Local exception filters → ✅ `@qeep/common` (ResponseFormatExceptionFilter)
- ❌ Local configuration → ✅ Centralized config service

#### New Structure Benefits

- 🎯 **Single Responsibility**: Each folder has a clear, focused purpose
- 🔧 **Maintainability**: Easier to locate and modify specific functionality
- 📦 **Reusability**: Core components shared via common library
- 🧪 **Testability**: Clear separation makes testing more straightforward
- 📈 **Scalability**: Easy to add new modules without cluttering

#### Migration Guide

1. **Imports Updated**: All common functionality now imported from `@qeep/common`
2. **Config Centralized**: Configuration moved to dedicated `config/` folder
3. **Repository Pattern**: Data access layer separated into repositories
4. **Event-Driven**: Event handlers prepared for future event-driven features
5. **Type Safety**: Core types and interfaces in dedicated `core/` folder

### Folder Purpose Guide

| Folder | Purpose | Examples |
|--------|---------|----------|
| `config/` | Environment & app configuration | Database config, tenant defaults |
| `core/` | Business logic, types, constants | Enums, interfaces, business rules |
| `modules/` | Feature modules | Tenant, billing, settings |
| `database/` | Data access layer | Prisma service, migrations |
| `events/` | Event-driven architecture | Event handlers, producers |
| `middleware/` | Request/response processing | Tenant context, logging |
| `interceptors/` | Cross-cutting concerns | Custom interceptors (if needed) |
| `guards/` | Authorization & access control | Custom guards (if needed) |

## 📄 License

[Your License Here]
