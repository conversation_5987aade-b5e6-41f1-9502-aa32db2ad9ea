import { 
  Body, 
  Controller, 
  Delete, 
  Get, 
  HttpCode, 
  HttpStatus, 
  Param, 
  Post, 
  Put, 
  Query, 
  UseGuards, 
  UseInterceptors 
} from '@nestjs/common';
import {
  JwtAuthGuard,
  TenantPermission,
  TenantRole,
  RequirePermissions,
  RequireRoles,
  ResponseFormatInterceptor,
  ZodValidationPipe,
} from '@qeep/common';
import {
  CreateRuleRequestDto,
  CreateRuleRequestSchema,
  UpdateRuleRequestDto,
  UpdateRuleRequestSchema,
  RuleQueryDto,
} from '@qeep/contracts/tenant/dtos';
import { RuleCategory, RuleType } from '@qeep/contracts/tenant/interfaces';
import { RuleService } from '../services/rule.service';

@Controller('tenants/:tenant_id/rules')
@UseGuards(JwtAuthGuard)
@UseInterceptors(ResponseFormatInterceptor)
export class RuleController {
  constructor(private readonly ruleService: RuleService) {}

  @Get()
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_RULES)
  async getAllRules(
    @Param('tenant_id') tenantId: string,
    @Query() query: RuleQueryDto
  ) {
    return await this.ruleService.getAllRules(tenantId, query);
  }

  @Get('active')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_RULES)
  async getActiveRules(
    @Param('tenant_id') tenantId: string,
    @Query('risk_profile_id') riskProfileId?: string
  ) {
    return await this.ruleService.getActiveRules(tenantId, riskProfileId);
  }

  @Get('category/:category')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_RULES)
  async getRulesByCategory(
    @Param('tenant_id') tenantId: string,
    @Param('category') category: RuleCategory
  ) {
    return await this.ruleService.getRulesByCategory(tenantId, category);
  }

  @Get('type/:rule_type')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_RULES)
  async getRulesByType(
    @Param('tenant_id') tenantId: string,
    @Param('rule_type') ruleType: RuleType
  ) {
    return await this.ruleService.getRulesByType(tenantId, ruleType);
  }

  @Get('effective')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_RULES)
  async getEffectiveRules(
    @Param('tenant_id') tenantId: string,
    @Query('date') date?: string
  ) {
    const effectiveDate = date ? new Date(date) : undefined;
    return await this.ruleService.getEffectiveRules(tenantId, effectiveDate);
  }

  @Get('search')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_RULES)
  async searchRules(
    @Param('tenant_id') tenantId: string,
    @Query('q') searchTerm: string,
    @Query('category') category?: RuleCategory
  ) {
    return await this.ruleService.searchRules(tenantId, searchTerm, category);
  }

  @Post('validate/conditions')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER)
  @RequirePermissions(TenantPermission.MANAGE_RULES)
  async validateRuleConditions(
    @Param('tenant_id') tenantId: string,
    @Body() body: { conditions: any }
  ) {
    const isValid = await this.ruleService.validateRuleConditions(body.conditions);
    return { valid: isValid };
  }

  @Post('validate/actions')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER)
  @RequirePermissions(TenantPermission.MANAGE_RULES)
  async validateRuleActions(
    @Param('tenant_id') tenantId: string,
    @Body() body: { actions: any }
  ) {
    const isValid = await this.ruleService.validateRuleActions(body.actions);
    return { valid: isValid };
  }

  @Get(':rule_id')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_RULES)
  async getRuleById(
    @Param('tenant_id') tenantId: string,
    @Param('rule_id') ruleId: string
  ) {
    return await this.ruleService.getRuleById(tenantId, ruleId);
  }

  @Put(':rule_id')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER)
  @RequirePermissions(TenantPermission.MANAGE_RULES)
  async updateRule(
    @Param('tenant_id') tenantId: string,
    @Param('rule_id') ruleId: string,
    @Body(new ZodValidationPipe(UpdateRuleRequestSchema)) updateDto: UpdateRuleRequestDto
  ) {
    return await this.ruleService.updateRule(tenantId, ruleId, updateDto);
  }

  @Delete(':rule_id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER)
  @RequirePermissions(TenantPermission.MANAGE_RULES)
  async deleteRule(
    @Param('tenant_id') tenantId: string,
    @Param('rule_id') ruleId: string
  ) {
    await this.ruleService.deleteRule(tenantId, ruleId);
  }
}

@Controller('tenants/:tenant_id/risk-profiles/:risk_profile_id/rules')
@UseGuards(JwtAuthGuard)
@UseInterceptors(ResponseFormatInterceptor)
export class RiskProfileRuleController {
  constructor(private readonly ruleService: RuleService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER)
  @RequirePermissions(TenantPermission.MANAGE_RULES)
  async createRule(
    @Param('tenant_id') tenantId: string,
    @Param('risk_profile_id') riskProfileId: string,
    @Body(new ZodValidationPipe(CreateRuleRequestSchema)) createDto: CreateRuleRequestDto
  ) {
    return await this.ruleService.createRule(tenantId, riskProfileId, createDto);
  }

  @Get()
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_RULES)
  async getRulesByRiskProfile(
    @Param('tenant_id') tenantId: string,
    @Param('risk_profile_id') riskProfileId: string
  ) {
    return await this.ruleService.getRulesByRiskProfile(tenantId, riskProfileId);
  }
}
