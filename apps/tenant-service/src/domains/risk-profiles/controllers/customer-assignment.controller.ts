import { 
  Body, 
  Controller, 
  Delete, 
  Get, 
  HttpCode, 
  HttpStatus, 
  Param, 
  Post, 
  Put, 
  Query, 
  UseGuards, 
  UseInterceptors 
} from '@nestjs/common';
import {
  JwtAuthGuard,
  TenantPermission,
  TenantRole,
  RequirePermissions,
  RequireRoles,
  ResponseFormatInterceptor,
  ZodValidationPipe,
} from '@qeep/common';
import {
  AssignCustomerToRiskProfileRequestDto,
  AssignCustomerToRiskProfileRequestSchema,
  UpdateCustomerRiskProfileAssignmentRequestDto,
  UpdateCustomerRiskProfileAssignmentRequestSchema,
  CustomerAssignmentQueryDto,
  AutoAssignCustomersRequestDto,
  AutoAssignCustomersRequestSchema,
  BulkAssignmentRequestDto,
} from '@qeep/contracts/tenant/dtos';
import { CustomerAssignmentService } from '../services/customer-assignment.service';

@Controller('tenants/:tenant_id/customers/:customer_id/risk-profile-assignments')
@UseGuards(JwtAuthGuard)
@UseInterceptors(ResponseFormatInterceptor)
export class CustomerRiskProfileAssignmentController {
  constructor(private readonly customerAssignmentService: CustomerAssignmentService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER)
  @RequirePermissions(TenantPermission.MANAGE_CUSTOMER_ASSIGNMENTS)
  async assignCustomerToRiskProfile(
    @Param('tenant_id') tenantId: string,
    @Param('customer_id') customerId: string,
    @Body(new ZodValidationPipe(AssignCustomerToRiskProfileRequestSchema)) assignDto: AssignCustomerToRiskProfileRequestDto
  ) {
    return await this.customerAssignmentService.assignCustomerToRiskProfile(tenantId, customerId, assignDto);
  }

  @Get()
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_CUSTOMER_ASSIGNMENTS)
  async getCustomerAssignments(
    @Param('tenant_id') tenantId: string,
    @Param('customer_id') customerId: string
  ) {
    return await this.customerAssignmentService.getCustomerAssignments(tenantId, customerId);
  }
}

@Controller('tenants/:tenant_id/customer-assignments')
@UseGuards(JwtAuthGuard)
@UseInterceptors(ResponseFormatInterceptor)
export class CustomerAssignmentController {
  constructor(private readonly customerAssignmentService: CustomerAssignmentService) {}

  @Get()
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_CUSTOMER_ASSIGNMENTS)
  async getAllCustomerAssignments(
    @Param('tenant_id') tenantId: string,
    @Query() query: CustomerAssignmentQueryDto
  ) {
    return await this.customerAssignmentService.getAllCustomerAssignments(tenantId, query);
  }

  @Get('automatic')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_CUSTOMER_ASSIGNMENTS)
  async getAutomaticAssignments(
    @Param('tenant_id') tenantId: string
  ) {
    return await this.customerAssignmentService.getAutomaticAssignments(tenantId);
  }

  @Get('manual')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_CUSTOMER_ASSIGNMENTS)
  async getManualAssignments(
    @Param('tenant_id') tenantId: string
  ) {
    return await this.customerAssignmentService.getManualAssignments(tenantId);
  }

  @Post('auto-assign')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER)
  @RequirePermissions(TenantPermission.MANAGE_CUSTOMER_ASSIGNMENTS)
  async autoAssignCustomers(
    @Param('tenant_id') tenantId: string,
    @Body(new ZodValidationPipe(AutoAssignCustomersRequestSchema)) autoAssignDto: AutoAssignCustomersRequestDto
  ) {
    return await this.customerAssignmentService.autoAssignCustomers(tenantId, autoAssignDto);
  }

  @Post('bulk-assign')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER)
  @RequirePermissions(TenantPermission.MANAGE_CUSTOMER_ASSIGNMENTS)
  async bulkAssignCustomers(
    @Param('tenant_id') tenantId: string,
    @Body() bulkAssignDto: BulkAssignmentRequestDto
  ) {
    return await this.customerAssignmentService.bulkAssignCustomers(tenantId, bulkAssignDto);
  }

  @Get(':assignment_id')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_CUSTOMER_ASSIGNMENTS)
  async getAssignmentById(
    @Param('tenant_id') tenantId: string,
    @Param('assignment_id') assignmentId: string
  ) {
    return await this.customerAssignmentService.getAssignmentById(tenantId, assignmentId);
  }

  @Put(':assignment_id')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER)
  @RequirePermissions(TenantPermission.MANAGE_CUSTOMER_ASSIGNMENTS)
  async updateCustomerAssignment(
    @Param('tenant_id') tenantId: string,
    @Param('assignment_id') assignmentId: string,
    @Body(new ZodValidationPipe(UpdateCustomerRiskProfileAssignmentRequestSchema)) updateDto: UpdateCustomerRiskProfileAssignmentRequestDto
  ) {
    return await this.customerAssignmentService.updateCustomerAssignment(tenantId, assignmentId, updateDto);
  }

  @Delete(':assignment_id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER)
  @RequirePermissions(TenantPermission.MANAGE_CUSTOMER_ASSIGNMENTS)
  async removeCustomerAssignment(
    @Param('tenant_id') tenantId: string,
    @Param('assignment_id') assignmentId: string
  ) {
    await this.customerAssignmentService.removeCustomerAssignment(tenantId, assignmentId);
  }
}

@Controller('tenants/:tenant_id/risk-profiles/:risk_profile_id/assignments')
@UseGuards(JwtAuthGuard)
@UseInterceptors(ResponseFormatInterceptor)
export class RiskProfileAssignmentController {
  constructor(private readonly customerAssignmentService: CustomerAssignmentService) {}

  @Get()
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_CUSTOMER_ASSIGNMENTS)
  async getAssignmentsByRiskProfile(
    @Param('tenant_id') tenantId: string,
    @Param('risk_profile_id') riskProfileId: string
  ) {
    return await this.customerAssignmentService.getAssignmentsByRiskProfile(tenantId, riskProfileId);
  }
}
