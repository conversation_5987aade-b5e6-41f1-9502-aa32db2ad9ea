import { 
  Body, 
  Controller, 
  Delete, 
  Get, 
  HttpCode, 
  HttpStatus, 
  Param, 
  Post, 
  Put, 
  Query, 
  UseInterceptors 
} from '@nestjs/common';
import { ResponseFormatInterceptor } from '@qeep/common';
import {
  CreateRuleRequestDto,
  UpdateRuleRequestDto,
  RuleQueryDto,
} from '@qeep/contracts/tenant/dtos';
import { RuleCategory, RuleType } from '@qeep/contracts/tenant/interfaces';
import { RuleService } from '../services/rule.service';

@Controller('tenants/:tenant_id/rules')
@UseInterceptors(ResponseFormatInterceptor)
export class RuleController {
  constructor(private readonly ruleService: RuleService) {}

  @Get()
  async getAllRules(
    @Param('tenant_id') tenantId: string,
    @Query() query: RuleQueryDto
  ) {
    return await this.ruleService.getAllRules(tenantId, query);
  }

  @Get('active')
  async getActiveRules(
    @Param('tenant_id') tenantId: string,
    @Query('risk_profile_id') riskProfileId?: string
  ) {
    return await this.ruleService.getActiveRules(tenantId, riskProfileId);
  }

  @Get('category/:category')
  async getRulesByCategory(
    @Param('tenant_id') tenantId: string,
    @Param('category') category: RuleCategory
  ) {
    return await this.ruleService.getRulesByCategory(tenantId, category);
  }

  @Get('type/:rule_type')
  async getRulesByType(
    @Param('tenant_id') tenantId: string,
    @Param('rule_type') ruleType: RuleType
  ) {
    return await this.ruleService.getRulesByType(tenantId, ruleType);
  }

  @Get('effective')
  async getEffectiveRules(
    @Param('tenant_id') tenantId: string,
    @Query('date') date?: string
  ) {
    const effectiveDate = date ? new Date(date) : undefined;
    return await this.ruleService.getEffectiveRules(tenantId, effectiveDate);
  }

  @Get('search')
  async searchRules(
    @Param('tenant_id') tenantId: string,
    @Query('q') searchTerm: string,
    @Query('category') category?: RuleCategory
  ) {
    return await this.ruleService.searchRules(tenantId, searchTerm, category);
  }

  @Post('validate/conditions')
  async validateRuleConditions(
    @Param('tenant_id') tenantId: string,
    @Body() body: { conditions: any }
  ) {
    const isValid = await this.ruleService.validateRuleConditions(body.conditions);
    return { valid: isValid };
  }

  @Post('validate/actions')
  async validateRuleActions(
    @Param('tenant_id') tenantId: string,
    @Body() body: { actions: any }
  ) {
    const isValid = await this.ruleService.validateRuleActions(body.actions);
    return { valid: isValid };
  }

  @Get(':rule_id')
  async getRuleById(
    @Param('tenant_id') tenantId: string,
    @Param('rule_id') ruleId: string
  ) {
    return await this.ruleService.getRuleById(tenantId, ruleId);
  }

  @Put(':rule_id')
  async updateRule(
    @Param('tenant_id') tenantId: string,
    @Param('rule_id') ruleId: string,
    @Body() updateDto: UpdateRuleRequestDto
  ) {
    return await this.ruleService.updateRule(tenantId, ruleId, updateDto);
  }

  @Delete(':rule_id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteRule(
    @Param('tenant_id') tenantId: string,
    @Param('rule_id') ruleId: string
  ) {
    await this.ruleService.deleteRule(tenantId, ruleId);
  }
}

@Controller('tenants/:tenant_id/risk-profiles/:risk_profile_id/rules')
@UseInterceptors(ResponseFormatInterceptor)
export class RiskProfileRuleController {
  constructor(private readonly ruleService: RuleService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async createRule(
    @Param('tenant_id') tenantId: string,
    @Param('risk_profile_id') riskProfileId: string,
    @Body() createDto: CreateRuleRequestDto
  ) {
    return await this.ruleService.createRule(tenantId, riskProfileId, createDto);
  }

  @Get()
  async getRulesByRiskProfile(
    @Param('tenant_id') tenantId: string,
    @Param('risk_profile_id') riskProfileId: string
  ) {
    return await this.ruleService.getRulesByRiskProfile(tenantId, riskProfileId);
  }
}
