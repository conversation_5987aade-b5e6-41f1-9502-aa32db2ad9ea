import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Post, Put, Query, UseGuards, UseInterceptors } from '@nestjs/common';
import { ResponseFormatInterceptor } from '@qeep/common';
import { CreateRiskProfileRequestDto, RiskProfileQueryDto, UpdateRiskProfileRequestDto } from '@qeep/contracts/tenant/dtos';
import { RuleCategory } from '@qeep/contracts/tenant/interfaces';
import { RiskProfileService } from '../services/risk-profile.service';

@Controller('tenants/:tenant_id/risk-profiles')
@UseGuards(JwtAuthGuard)
@UseInterceptors(ResponseFormatInterceptor)
export class RiskProfileController {
  constructor(private readonly riskProfileService: RiskProfileService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER)
  @RequirePermissions(TenantPermission.MANAGE_RISK_PROFILES)
  async createRiskProfile(@Param('tenant_id') tenantId: string, @Body(new ZodValidationPipe(CreateRiskProfileRequestSchema)) createDto: CreateRiskProfileRequestDto) {
    return await this.riskProfileService.createRiskProfile(tenantId, createDto);
  }

  @Get()
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_RISK_PROFILES)
  async getAllRiskProfiles(@Param('tenant_id') tenantId: string, @Query() query: RiskProfileQueryDto) {
    return await this.riskProfileService.getAllRiskProfiles(tenantId, query);
  }

  @Get('system-generated')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_RISK_PROFILES)
  async getSystemGeneratedProfiles(@Param('tenant_id') tenantId: string) {
    return await this.riskProfileService.getSystemGeneratedProfiles(tenantId);
  }

  @Get('custom')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_RISK_PROFILES)
  async getCustomProfiles(@Param('tenant_id') tenantId: string) {
    return await this.riskProfileService.getCustomProfiles(tenantId);
  }

  @Get('search')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_RISK_PROFILES)
  async searchRiskProfiles(@Param('tenant_id') tenantId: string, @Query('q') searchTerm: string, @Query('category') category?: RuleCategory) {
    return await this.riskProfileService.searchRiskProfiles(tenantId, searchTerm, category);
  }

  @Get('stats')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_RISK_PROFILES)
  async getRiskProfileStats(@Param('tenant_id') tenantId: string) {
    return await this.riskProfileService.getRiskProfileStats(tenantId);
  }

  @Get(':risk_profile_id')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_RISK_PROFILES)
  async getRiskProfileById(@Param('tenant_id') tenantId: string, @Param('risk_profile_id') riskProfileId: string) {
    return await this.riskProfileService.getRiskProfileById(tenantId, riskProfileId);
  }

  @Put(':risk_profile_id')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER)
  @RequirePermissions(TenantPermission.MANAGE_RISK_PROFILES)
  async updateRiskProfile(
    @Param('tenant_id') tenantId: string,
    @Param('risk_profile_id') riskProfileId: string,
    @Body(new ZodValidationPipe(UpdateRiskProfileRequestSchema)) updateDto: UpdateRiskProfileRequestDto,
  ) {
    return await this.riskProfileService.updateRiskProfile(tenantId, riskProfileId, updateDto);
  }

  @Delete(':risk_profile_id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER)
  @RequirePermissions(TenantPermission.MANAGE_RISK_PROFILES)
  async deleteRiskProfile(@Param('tenant_id') tenantId: string, @Param('risk_profile_id') riskProfileId: string) {
    await this.riskProfileService.deleteRiskProfile(tenantId, riskProfileId);
  }
}

@Controller('tenants/:tenant_id/analytics')
@UseGuards(JwtAuthGuard)
@UseInterceptors(ResponseFormatInterceptor)
export class RiskProfileAnalyticsController {
  constructor(private readonly riskProfileService: RiskProfileService) {}

  @Get('risk-profile-stats')
  @RequireRoles(TenantRole.TENANT_ADMIN, TenantRole.COMPLIANCE_OFFICER, TenantRole.RISK_ANALYST)
  @RequirePermissions(TenantPermission.VIEW_RISK_PROFILES)
  async getRiskProfileStats(@Param('tenant_id') tenantId: string) {
    return await this.riskProfileService.getRiskProfileStats(tenantId);
  }
}
