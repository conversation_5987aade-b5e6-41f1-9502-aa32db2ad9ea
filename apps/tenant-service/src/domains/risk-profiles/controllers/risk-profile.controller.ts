import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Post, Put, Query, UseInterceptors } from '@nestjs/common';
import { ResponseFormatInterceptor } from '@qeep/common';
import { CreateRiskProfileRequestDto, RiskProfileQueryDto, UpdateRiskProfileRequestDto } from '@qeep/contracts/tenant/dtos';
import { RuleCategory } from '@qeep/contracts/tenant/interfaces';
import { RiskProfileService } from '../services/risk-profile.service';

@Controller('tenants/:tenant_id/risk-profiles')
@UseInterceptors(ResponseFormatInterceptor)
export class RiskProfileController {
  constructor(private readonly riskProfileService: RiskProfileService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async createRiskProfile(@Param('tenant_id') tenantId: string, @Body() createDto: CreateRiskProfileRequestDto) {
    return await this.riskProfileService.createRiskProfile(tenantId, createDto);
  }

  @Get()
  async getAllRiskProfiles(@Param('tenant_id') tenantId: string, @Query() query: RiskProfileQueryDto) {
    return await this.riskProfileService.getAllRiskProfiles(tenantId, query);
  }

  @Get('system-generated')
  async getSystemGeneratedProfiles(@Param('tenant_id') tenantId: string) {
    return await this.riskProfileService.getSystemGeneratedProfiles(tenantId);
  }

  @Get('custom')
  async getCustomProfiles(@Param('tenant_id') tenantId: string) {
    return await this.riskProfileService.getCustomProfiles(tenantId);
  }

  @Get('search')
  async searchRiskProfiles(@Param('tenant_id') tenantId: string, @Query('q') searchTerm: string, @Query('category') category?: RuleCategory) {
    return await this.riskProfileService.searchRiskProfiles(tenantId, searchTerm, category);
  }

  @Get('stats')
  async getRiskProfileStats(@Param('tenant_id') tenantId: string) {
    return await this.riskProfileService.getRiskProfileStats(tenantId);
  }

  @Get(':risk_profile_id')
  async getRiskProfileById(@Param('tenant_id') tenantId: string, @Param('risk_profile_id') riskProfileId: string) {
    return await this.riskProfileService.getRiskProfileById(tenantId, riskProfileId);
  }

  @Put(':risk_profile_id')
  async updateRiskProfile(@Param('tenant_id') tenantId: string, @Param('risk_profile_id') riskProfileId: string, @Body() updateDto: UpdateRiskProfileRequestDto) {
    return await this.riskProfileService.updateRiskProfile(tenantId, riskProfileId, updateDto);
  }

  @Delete(':risk_profile_id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteRiskProfile(@Param('tenant_id') tenantId: string, @Param('risk_profile_id') riskProfileId: string) {
    await this.riskProfileService.deleteRiskProfile(tenantId, riskProfileId);
  }
}

@Controller('tenants/:tenant_id/analytics')
@UseInterceptors(ResponseFormatInterceptor)
export class RiskProfileAnalyticsController {
  constructor(private readonly riskProfileService: RiskProfileService) {}

  @Get('risk-profile-stats')
  async getRiskProfileStats(@Param('tenant_id') tenantId: string) {
    return await this.riskProfileService.getRiskProfileStats(tenantId);
  }
}
