import { 
  Body, 
  Controller, 
  Delete, 
  Get, 
  HttpCode, 
  HttpStatus, 
  Param, 
  Post, 
  Put, 
  Query, 
  UseInterceptors 
} from '@nestjs/common';
import { ResponseFormatInterceptor } from '@qeep/common';
import {
  AssignCustomerToRiskProfileRequestDto,
  UpdateCustomerRiskProfileAssignmentRequestDto,
  CustomerAssignmentQueryDto,
  AutoAssignCustomersRequestDto,
  BulkAssignmentRequestDto,
} from '@qeep/contracts/tenant/dtos';
import { CustomerAssignmentService } from '../services/customer-assignment.service';

@Controller('tenants/:tenant_id/customers/:customer_id/risk-profile-assignments')
@UseInterceptors(ResponseFormatInterceptor)
export class CustomerRiskProfileAssignmentController {
  constructor(private readonly customerAssignmentService: CustomerAssignmentService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async assignCustomerToRiskProfile(
    @Param('tenant_id') tenantId: string,
    @Param('customer_id') customerId: string,
    @Body() assignDto: AssignCustomerToRiskProfileRequestDto
  ) {
    return await this.customerAssignmentService.assignCustomerToRiskProfile(tenantId, customerId, assignDto);
  }

  @Get()
  async getCustomerAssignments(
    @Param('tenant_id') tenantId: string,
    @Param('customer_id') customerId: string
  ) {
    return await this.customerAssignmentService.getCustomerAssignments(tenantId, customerId);
  }
}

@Controller('tenants/:tenant_id/customer-assignments')
@UseInterceptors(ResponseFormatInterceptor)
export class CustomerAssignmentController {
  constructor(private readonly customerAssignmentService: CustomerAssignmentService) {}

  @Get()
  async getAllCustomerAssignments(
    @Param('tenant_id') tenantId: string,
    @Query() query: CustomerAssignmentQueryDto
  ) {
    return await this.customerAssignmentService.getAllCustomerAssignments(tenantId, query);
  }

  @Get('automatic')
  async getAutomaticAssignments(
    @Param('tenant_id') tenantId: string
  ) {
    return await this.customerAssignmentService.getAutomaticAssignments(tenantId);
  }

  @Get('manual')
  async getManualAssignments(
    @Param('tenant_id') tenantId: string
  ) {
    return await this.customerAssignmentService.getManualAssignments(tenantId);
  }

  @Post('auto-assign')
  async autoAssignCustomers(
    @Param('tenant_id') tenantId: string,
    @Body() autoAssignDto: AutoAssignCustomersRequestDto
  ) {
    return await this.customerAssignmentService.autoAssignCustomers(tenantId, autoAssignDto);
  }

  @Post('bulk-assign')
  async bulkAssignCustomers(
    @Param('tenant_id') tenantId: string,
    @Body() bulkAssignDto: BulkAssignmentRequestDto
  ) {
    return await this.customerAssignmentService.bulkAssignCustomers(tenantId, bulkAssignDto);
  }

  @Get(':assignment_id')
  async getAssignmentById(
    @Param('tenant_id') tenantId: string,
    @Param('assignment_id') assignmentId: string
  ) {
    return await this.customerAssignmentService.getAssignmentById(tenantId, assignmentId);
  }

  @Put(':assignment_id')
  async updateCustomerAssignment(
    @Param('tenant_id') tenantId: string,
    @Param('assignment_id') assignmentId: string,
    @Body() updateDto: UpdateCustomerRiskProfileAssignmentRequestDto
  ) {
    return await this.customerAssignmentService.updateCustomerAssignment(tenantId, assignmentId, updateDto);
  }

  @Delete(':assignment_id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async removeCustomerAssignment(
    @Param('tenant_id') tenantId: string,
    @Param('assignment_id') assignmentId: string
  ) {
    await this.customerAssignmentService.removeCustomerAssignment(tenantId, assignmentId);
  }
}

@Controller('tenants/:tenant_id/risk-profiles/:risk_profile_id/assignments')
@UseInterceptors(ResponseFormatInterceptor)
export class RiskProfileAssignmentController {
  constructor(private readonly customerAssignmentService: CustomerAssignmentService) {}

  @Get()
  async getAssignmentsByRiskProfile(
    @Param('tenant_id') tenantId: string,
    @Param('risk_profile_id') riskProfileId: string
  ) {
    return await this.customerAssignmentService.getAssignmentsByRiskProfile(tenantId, riskProfileId);
  }
}
