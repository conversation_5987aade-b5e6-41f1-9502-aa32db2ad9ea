/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable } from '@nestjs/common';
import { Prisma, RiskProfile } from '@prisma/tenant-client';
import { BaseRepository, IBaseRepository } from '@qeep/common';
import { IRiskProfile, RuleCategory } from '@qeep/contracts/tenant/interfaces';
import { TenantPrismaService } from '../../../database/prisma.service';

/**
 * Risk Profile Repository Interface
 * Defines all risk profile-specific data access operations
 */
export interface IRiskProfileRepository extends IBaseRepository<RiskProfile, Prisma.RiskProfileCreateInput, Prisma.RiskProfileUpdateInput> {
  // Risk profile-specific methods that return IRiskProfile types
  getRiskProfileById(id: string): Promise<IRiskProfile | null>;
  getAllRiskProfiles(options?: any): Promise<IRiskProfile[]>;
  findRiskProfilesWithOptions(options: any): Promise<IRiskProfile[]>;

  // Search and filtering
  findByName(tenantId: string, name: string): Promise<IRiskProfile | null>;
  findByTenant(tenantId: string, options?: { limit?: number; offset?: number }): Promise<IRiskProfile[]>;
  searchRiskProfiles(tenantId: string, searchTerm: string): Promise<IRiskProfile[]>;
  
  // Business logic queries
  findActiveRiskProfiles(tenantId: string): Promise<IRiskProfile[]>;
  findSystemGeneratedProfiles(tenantId: string): Promise<IRiskProfile[]>;
  findCustomProfiles(tenantId: string): Promise<IRiskProfile[]>;
  findByRuleCategory(tenantId: string, category: RuleCategory): Promise<IRiskProfile[]>;
  
  // Statistics and counts
  countByTenant(tenantId: string): Promise<number>;
  countActiveByTenant(tenantId: string): Promise<number>;
  countByCategory(tenantId: string, category: RuleCategory): Promise<number>;
  
  // Validation operations
  existsByName(tenantId: string, name: string, excludeId?: string): Promise<boolean>;
  
  // Auto-assignment helpers
  findDefaultProfileForRiskLevel(tenantId: string, riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'): Promise<IRiskProfile | null>;
}

/**
 * Risk Profile Repository Implementation
 * Handles all risk profile entity data access operations
 */
@Injectable()
export class RiskProfileRepository extends BaseRepository<RiskProfile, Prisma.RiskProfileCreateInput, Prisma.RiskProfileUpdateInput> implements IRiskProfileRepository {
  constructor(private readonly tenantPrisma: TenantPrismaService) {
    super(tenantPrisma);
  }

  protected get model() {
    return this.tenantPrisma.riskProfile;
  }

  /**
   * Transform Prisma RiskProfile to IRiskProfile interface
   */
  private transformToInterface(riskProfile: RiskProfile): IRiskProfile {
    return {
      id: riskProfile.id,
      tenantId: riskProfile.tenantId,
      name: riskProfile.name,
      description: riskProfile.description || undefined,
      ruleCategory: riskProfile.ruleCategory as RuleCategory,
      isActive: riskProfile.isActive,
      isSystemGenerated: riskProfile.isSystemGenerated,
      priority: riskProfile.priority,
      assignmentPriority: riskProfile.assignmentPriority,
      metadata: riskProfile.metadata ? (riskProfile.metadata as Record<string, any>) : undefined,
      createdAt: riskProfile.createdAt,
      updatedAt: riskProfile.updatedAt,
      createdBy: riskProfile.createdBy || undefined,
    };
  }

  /**
   * Get risk profile by ID returning IRiskProfile
   */
  async getRiskProfileById(id: string): Promise<IRiskProfile | null> {
    try {
      const riskProfile = await this.model.findUnique({
        where: { id },
      });
      return riskProfile ? this.transformToInterface(riskProfile) : null;
    } catch (error) {
      throw new Error(`Failed to find risk profile by ID ${id}: ${error.message}`);
    }
  }

  /**
   * Get all risk profiles returning IRiskProfile[]
   */
  async getAllRiskProfiles(options?: any): Promise<IRiskProfile[]> {
    try {
      const riskProfiles = await this.model.findMany(options);
      return riskProfiles.map((riskProfile: RiskProfile) => this.transformToInterface(riskProfile));
    } catch (error) {
      throw new Error(`Failed to find risk profiles: ${error.message}`);
    }
  }

  /**
   * Find risk profiles with full Prisma options and return transformed IRiskProfile objects
   */
  async findRiskProfilesWithOptions(options: any): Promise<IRiskProfile[]> {
    try {
      const riskProfiles = await this.model.findMany(options);
      return riskProfiles.map((riskProfile: RiskProfile) => this.transformToInterface(riskProfile));
    } catch (error) {
      throw new Error(`Failed to find risk profiles with options: ${error.message}`);
    }
  }

  /**
   * Find risk profile by name within tenant
   */
  async findByName(tenantId: string, name: string): Promise<IRiskProfile | null> {
    try {
      const riskProfile = await this.model.findUnique({
        where: {
          tenantId_name: {
            tenantId,
            name,
          },
        },
      });
      return riskProfile ? this.transformToInterface(riskProfile) : null;
    } catch (error) {
      throw new Error(`Failed to find risk profile by name ${name}: ${error.message}`);
    }
  }

  /**
   * Find risk profiles by tenant with pagination
   */
  async findByTenant(tenantId: string, options?: { limit?: number; offset?: number }): Promise<IRiskProfile[]> {
    try {
      const riskProfiles = await this.model.findMany({
        where: { tenantId },
        take: options?.limit,
        skip: options?.offset,
        orderBy: { createdAt: 'desc' },
      });
      return riskProfiles.map((riskProfile: RiskProfile) => this.transformToInterface(riskProfile));
    } catch (error) {
      throw new Error(`Failed to find risk profiles by tenant ${tenantId}: ${error.message}`);
    }
  }

  /**
   * Search risk profiles by name or description
   */
  async searchRiskProfiles(tenantId: string, searchTerm: string): Promise<IRiskProfile[]> {
    try {
      const riskProfiles = await this.model.findMany({
        where: {
          tenantId,
          OR: [
            { name: { contains: searchTerm, mode: 'insensitive' } },
            { description: { contains: searchTerm, mode: 'insensitive' } },
          ],
        },
        orderBy: { createdAt: 'desc' },
      });
      return riskProfiles.map((riskProfile: RiskProfile) => this.transformToInterface(riskProfile));
    } catch (error) {
      throw new Error(`Failed to search risk profiles: ${error.message}`);
    }
  }

  /**
   * Find active risk profiles for tenant
   */
  async findActiveRiskProfiles(tenantId: string): Promise<IRiskProfile[]> {
    try {
      const riskProfiles = await this.model.findMany({
        where: {
          tenantId,
          isActive: true,
        },
        orderBy: { assignmentPriority: 'desc' },
      });
      return riskProfiles.map((riskProfile: RiskProfile) => this.transformToInterface(riskProfile));
    } catch (error) {
      throw new Error(`Failed to find active risk profiles: ${error.message}`);
    }
  }

  /**
   * Find system-generated risk profiles for tenant
   */
  async findSystemGeneratedProfiles(tenantId: string): Promise<IRiskProfile[]> {
    try {
      const riskProfiles = await this.model.findMany({
        where: {
          tenantId,
          isSystemGenerated: true,
        },
        orderBy: { assignmentPriority: 'desc' },
      });
      return riskProfiles.map((riskProfile: RiskProfile) => this.transformToInterface(riskProfile));
    } catch (error) {
      throw new Error(`Failed to find system-generated risk profiles: ${error.message}`);
    }
  }

  /**
   * Find custom (non-system) risk profiles for tenant
   */
  async findCustomProfiles(tenantId: string): Promise<IRiskProfile[]> {
    try {
      const riskProfiles = await this.model.findMany({
        where: {
          tenantId,
          isSystemGenerated: false,
        },
        orderBy: { createdAt: 'desc' },
      });
      return riskProfiles.map((riskProfile: RiskProfile) => this.transformToInterface(riskProfile));
    } catch (error) {
      throw new Error(`Failed to find custom risk profiles: ${error.message}`);
    }
  }

  /**
   * Find risk profiles by rule category
   */
  async findByRuleCategory(tenantId: string, category: RuleCategory): Promise<IRiskProfile[]> {
    try {
      const riskProfiles = await this.model.findMany({
        where: {
          tenantId,
          ruleCategory: category,
        },
        orderBy: { priority: 'desc' },
      });
      return riskProfiles.map((riskProfile: RiskProfile) => this.transformToInterface(riskProfile));
    } catch (error) {
      throw new Error(`Failed to find risk profiles by category ${category}: ${error.message}`);
    }
  }

  /**
   * Count risk profiles by tenant
   */
  async countByTenant(tenantId: string): Promise<number> {
    try {
      return await this.model.count({ where: { tenantId } });
    } catch (error) {
      throw new Error(`Failed to count risk profiles by tenant ${tenantId}: ${error.message}`);
    }
  }

  /**
   * Count active risk profiles by tenant
   */
  async countActiveByTenant(tenantId: string): Promise<number> {
    try {
      return await this.model.count({ 
        where: { 
          tenantId,
          isActive: true 
        } 
      });
    } catch (error) {
      throw new Error(`Failed to count active risk profiles by tenant ${tenantId}: ${error.message}`);
    }
  }

  /**
   * Count risk profiles by category
   */
  async countByCategory(tenantId: string, category: RuleCategory): Promise<number> {
    try {
      return await this.model.count({ 
        where: { 
          tenantId,
          ruleCategory: category 
        } 
      });
    } catch (error) {
      throw new Error(`Failed to count risk profiles by category ${category}: ${error.message}`);
    }
  }

  /**
   * Check if risk profile exists by name
   */
  async existsByName(tenantId: string, name: string, excludeId?: string): Promise<boolean> {
    try {
      const where: any = { tenantId, name };
      if (excludeId) {
        where.id = { not: excludeId };
      }

      const count = await this.count(where);
      return count > 0;
    } catch (error) {
      throw new Error(`Failed to check if risk profile exists by name: ${error.message}`);
    }
  }

  /**
   * Find default risk profile for customer risk level
   */
  async findDefaultProfileForRiskLevel(tenantId: string, riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'): Promise<IRiskProfile | null> {
    try {
      const profileNameMap = {
        LOW: 'Standard Risk Profile',
        MEDIUM: 'Enhanced Monitoring Profile',
        HIGH: 'High Risk Profile',
        CRITICAL: 'High Risk Profile', // HIGH and CRITICAL use same profile
      };

      const riskProfile = await this.model.findFirst({
        where: {
          tenantId,
          name: profileNameMap[riskLevel],
          isSystemGenerated: true,
          isActive: true,
        },
      });

      return riskProfile ? this.transformToInterface(riskProfile) : null;
    } catch (error) {
      throw new Error(`Failed to find default profile for risk level ${riskLevel}: ${error.message}`);
    }
  }
}
