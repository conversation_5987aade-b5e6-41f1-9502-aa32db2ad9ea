/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable } from '@nestjs/common';
import { Prisma, CustomerRiskProfileAssignment } from '@prisma/tenant-client';
import { BaseRepository, IBaseRepository } from '@qeep/common';
import { ICustomerRiskProfileAssignment, AssignmentType } from '@qeep/contracts/tenant/interfaces';
import { TenantPrismaService } from '../../../database/prisma.service';

/**
 * Customer Risk Profile Assignment Repository Interface
 * Defines all customer assignment-specific data access operations
 */
export interface ICustomerRiskProfileAssignmentRepository extends IBaseRepository<CustomerRiskProfileAssignment, Prisma.CustomerRiskProfileAssignmentCreateInput, Prisma.CustomerRiskProfileAssignmentUpdateInput> {
  // Assignment-specific methods that return ICustomerRiskProfileAssignment types
  getAssignmentById(id: string): Promise<ICustomerRiskProfileAssignment | null>;
  getAllAssignments(options?: any): Promise<ICustomerRiskProfileAssignment[]>;
  findAssignmentsWithOptions(options: any): Promise<ICustomerRiskProfileAssignment[]>;

  // Customer-focused queries
  findByCustomer(tenantId: string, customerId: string): Promise<ICustomerRiskProfileAssignment[]>;
  findActiveByCustomer(tenantId: string, customerId: string): Promise<ICustomerRiskProfileAssignment[]>;
  findByCustomerAndProfile(tenantId: string, customerId: string, riskProfileId: string): Promise<ICustomerRiskProfileAssignment | null>;
  
  // Risk profile-focused queries
  findByRiskProfile(tenantId: string, riskProfileId: string, options?: { limit?: number; offset?: number }): Promise<ICustomerRiskProfileAssignment[]>;
  findActiveByRiskProfile(tenantId: string, riskProfileId: string): Promise<ICustomerRiskProfileAssignment[]>;
  
  // Assignment type queries
  findByAssignmentType(tenantId: string, assignmentType: AssignmentType): Promise<ICustomerRiskProfileAssignment[]>;
  findAutomaticAssignments(tenantId: string): Promise<ICustomerRiskProfileAssignment[]>;
  findManualAssignments(tenantId: string): Promise<ICustomerRiskProfileAssignment[]>;
  
  // Effective assignments (within date range)
  findEffectiveAssignments(tenantId: string, customerId?: string, date?: Date): Promise<ICustomerRiskProfileAssignment[]>;
  
  // Bulk operations
  findByCustomers(tenantId: string, customerIds: string[]): Promise<ICustomerRiskProfileAssignment[]>;
  findByRiskProfiles(tenantId: string, riskProfileIds: string[]): Promise<ICustomerRiskProfileAssignment[]>;
  
  // Statistics and counts
  countByTenant(tenantId: string): Promise<number>;
  countByCustomer(tenantId: string, customerId: string): Promise<number>;
  countByRiskProfile(tenantId: string, riskProfileId: string): Promise<number>;
  countByAssignmentType(tenantId: string, assignmentType: AssignmentType): Promise<number>;
  countActiveByTenant(tenantId: string): Promise<number>;
  
  // Validation operations
  existsAssignment(tenantId: string, customerId: string, riskProfileId: string): Promise<boolean>;
  hasConflictingAssignment(tenantId: string, customerId: string, riskProfileId: string, assignmentType: AssignmentType): Promise<boolean>;
  
  // Assignment management
  deactivateAssignment(tenantId: string, customerId: string, riskProfileId: string): Promise<boolean>;
  deactivateCustomerAssignments(tenantId: string, customerId: string): Promise<number>;
  deactivateRiskProfileAssignments(tenantId: string, riskProfileId: string): Promise<number>;
}

/**
 * Customer Risk Profile Assignment Repository Implementation
 * Handles all customer assignment entity data access operations
 */
@Injectable()
export class CustomerRiskProfileAssignmentRepository extends BaseRepository<CustomerRiskProfileAssignment, Prisma.CustomerRiskProfileAssignmentCreateInput, Prisma.CustomerRiskProfileAssignmentUpdateInput> implements ICustomerRiskProfileAssignmentRepository {
  constructor(private readonly tenantPrisma: TenantPrismaService) {
    super(tenantPrisma);
  }

  protected get model() {
    return this.tenantPrisma.customerRiskProfileAssignment;
  }

  /**
   * Transform Prisma CustomerRiskProfileAssignment to ICustomerRiskProfileAssignment interface
   */
  private transformToInterface(assignment: CustomerRiskProfileAssignment): ICustomerRiskProfileAssignment {
    return {
      id: assignment.id,
      tenantId: assignment.tenantId,
      customerId: assignment.customerId,
      riskProfileId: assignment.riskProfileId,
      assignmentType: assignment.assignmentType as AssignmentType,
      assignmentCriteria: assignment.assignmentCriteria ? (assignment.assignmentCriteria as Record<string, any>) : undefined,
      assignmentPriority: assignment.assignmentPriority,
      allowOverride: assignment.allowOverride,
      assignedAt: assignment.assignedAt,
      assignedBy: assignment.assignedBy || undefined,
      isActive: assignment.isActive,
      effectiveFrom: assignment.effectiveFrom || undefined,
      effectiveUntil: assignment.effectiveUntil || undefined,
      metadata: assignment.metadata ? (assignment.metadata as Record<string, any>) : undefined,
    };
  }

  /**
   * Get assignment by ID returning ICustomerRiskProfileAssignment
   */
  async getAssignmentById(id: string): Promise<ICustomerRiskProfileAssignment | null> {
    try {
      const assignment = await this.model.findUnique({
        where: { id },
      });
      return assignment ? this.transformToInterface(assignment) : null;
    } catch (error) {
      throw new Error(`Failed to find assignment by ID ${id}: ${error.message}`);
    }
  }

  /**
   * Get all assignments returning ICustomerRiskProfileAssignment[]
   */
  async getAllAssignments(options?: any): Promise<ICustomerRiskProfileAssignment[]> {
    try {
      const assignments = await this.model.findMany(options);
      return assignments.map((assignment: CustomerRiskProfileAssignment) => this.transformToInterface(assignment));
    } catch (error) {
      throw new Error(`Failed to find assignments: ${error.message}`);
    }
  }

  /**
   * Find assignments with full Prisma options and return transformed objects
   */
  async findAssignmentsWithOptions(options: any): Promise<ICustomerRiskProfileAssignment[]> {
    try {
      const assignments = await this.model.findMany(options);
      return assignments.map((assignment: CustomerRiskProfileAssignment) => this.transformToInterface(assignment));
    } catch (error) {
      throw new Error(`Failed to find assignments with options: ${error.message}`);
    }
  }

  /**
   * Find all assignments for a customer
   */
  async findByCustomer(tenantId: string, customerId: string): Promise<ICustomerRiskProfileAssignment[]> {
    try {
      const assignments = await this.model.findMany({
        where: { tenantId, customerId },
        orderBy: { assignmentPriority: 'desc' },
      });
      return assignments.map((assignment: CustomerRiskProfileAssignment) => this.transformToInterface(assignment));
    } catch (error) {
      throw new Error(`Failed to find assignments by customer ${customerId}: ${error.message}`);
    }
  }

  /**
   * Find active assignments for a customer
   */
  async findActiveByCustomer(tenantId: string, customerId: string): Promise<ICustomerRiskProfileAssignment[]> {
    try {
      const assignments = await this.model.findMany({
        where: { 
          tenantId, 
          customerId,
          isActive: true 
        },
        orderBy: { assignmentPriority: 'desc' },
      });
      return assignments.map((assignment: CustomerRiskProfileAssignment) => this.transformToInterface(assignment));
    } catch (error) {
      throw new Error(`Failed to find active assignments by customer ${customerId}: ${error.message}`);
    }
  }

  /**
   * Find specific assignment by customer and risk profile
   */
  async findByCustomerAndProfile(tenantId: string, customerId: string, riskProfileId: string): Promise<ICustomerRiskProfileAssignment | null> {
    try {
      const assignment = await this.model.findUnique({
        where: {
          tenantId_customerId_riskProfileId: {
            tenantId,
            customerId,
            riskProfileId,
          },
        },
      });
      return assignment ? this.transformToInterface(assignment) : null;
    } catch (error) {
      throw new Error(`Failed to find assignment by customer and profile: ${error.message}`);
    }
  }

  /**
   * Find assignments by risk profile with pagination
   */
  async findByRiskProfile(tenantId: string, riskProfileId: string, options?: { limit?: number; offset?: number }): Promise<ICustomerRiskProfileAssignment[]> {
    try {
      const assignments = await this.model.findMany({
        where: { tenantId, riskProfileId },
        take: options?.limit,
        skip: options?.offset,
        orderBy: { assignedAt: 'desc' },
      });
      return assignments.map((assignment: CustomerRiskProfileAssignment) => this.transformToInterface(assignment));
    } catch (error) {
      throw new Error(`Failed to find assignments by risk profile ${riskProfileId}: ${error.message}`);
    }
  }

  /**
   * Find active assignments by risk profile
   */
  async findActiveByRiskProfile(tenantId: string, riskProfileId: string): Promise<ICustomerRiskProfileAssignment[]> {
    try {
      const assignments = await this.model.findMany({
        where: { 
          tenantId, 
          riskProfileId,
          isActive: true 
        },
        orderBy: { assignmentPriority: 'desc' },
      });
      return assignments.map((assignment: CustomerRiskProfileAssignment) => this.transformToInterface(assignment));
    } catch (error) {
      throw new Error(`Failed to find active assignments by risk profile ${riskProfileId}: ${error.message}`);
    }
  }

  /**
   * Find assignments by assignment type
   */
  async findByAssignmentType(tenantId: string, assignmentType: AssignmentType): Promise<ICustomerRiskProfileAssignment[]> {
    try {
      const assignments = await this.model.findMany({
        where: { 
          tenantId, 
          assignmentType: assignmentType 
        },
        orderBy: { assignedAt: 'desc' },
      });
      return assignments.map((assignment: CustomerRiskProfileAssignment) => this.transformToInterface(assignment));
    } catch (error) {
      throw new Error(`Failed to find assignments by type ${assignmentType}: ${error.message}`);
    }
  }

  /**
   * Find automatic assignments
   */
  async findAutomaticAssignments(tenantId: string): Promise<ICustomerRiskProfileAssignment[]> {
    return this.findByAssignmentType(tenantId, AssignmentType.AUTOMATIC);
  }

  /**
   * Find manual assignments
   */
  async findManualAssignments(tenantId: string): Promise<ICustomerRiskProfileAssignment[]> {
    return this.findByAssignmentType(tenantId, AssignmentType.MANUAL);
  }

  /**
   * Find effective assignments (within date range)
   */
  async findEffectiveAssignments(tenantId: string, customerId?: string, date: Date = new Date()): Promise<ICustomerRiskProfileAssignment[]> {
    try {
      const where: any = {
        tenantId,
        isActive: true,
        OR: [
          { effectiveFrom: null },
          { effectiveFrom: { lte: date } },
        ],
        AND: [
          {
            OR: [
              { effectiveUntil: null },
              { effectiveUntil: { gte: date } },
            ],
          },
        ],
      };

      if (customerId) {
        where.customerId = customerId;
      }

      const assignments = await this.model.findMany({
        where,
        orderBy: { assignmentPriority: 'desc' },
      });
      return assignments.map((assignment: CustomerRiskProfileAssignment) => this.transformToInterface(assignment));
    } catch (error) {
      throw new Error(`Failed to find effective assignments: ${error.message}`);
    }
  }

  /**
   * Find assignments for multiple customers
   */
  async findByCustomers(tenantId: string, customerIds: string[]): Promise<ICustomerRiskProfileAssignment[]> {
    try {
      const assignments = await this.model.findMany({
        where: { 
          tenantId, 
          customerId: { in: customerIds } 
        },
        orderBy: { assignmentPriority: 'desc' },
      });
      return assignments.map((assignment: CustomerRiskProfileAssignment) => this.transformToInterface(assignment));
    } catch (error) {
      throw new Error(`Failed to find assignments by customers: ${error.message}`);
    }
  }

  /**
   * Find assignments for multiple risk profiles
   */
  async findByRiskProfiles(tenantId: string, riskProfileIds: string[]): Promise<ICustomerRiskProfileAssignment[]> {
    try {
      const assignments = await this.model.findMany({
        where: { 
          tenantId, 
          riskProfileId: { in: riskProfileIds } 
        },
        orderBy: { assignmentPriority: 'desc' },
      });
      return assignments.map((assignment: CustomerRiskProfileAssignment) => this.transformToInterface(assignment));
    } catch (error) {
      throw new Error(`Failed to find assignments by risk profiles: ${error.message}`);
    }
  }

  /**
   * Count assignments by tenant
   */
  async countByTenant(tenantId: string): Promise<number> {
    try {
      return await this.model.count({ where: { tenantId } });
    } catch (error) {
      throw new Error(`Failed to count assignments by tenant ${tenantId}: ${error.message}`);
    }
  }

  /**
   * Count assignments by customer
   */
  async countByCustomer(tenantId: string, customerId: string): Promise<number> {
    try {
      return await this.model.count({ 
        where: { 
          tenantId,
          customerId 
        } 
      });
    } catch (error) {
      throw new Error(`Failed to count assignments by customer ${customerId}: ${error.message}`);
    }
  }

  /**
   * Count assignments by risk profile
   */
  async countByRiskProfile(tenantId: string, riskProfileId: string): Promise<number> {
    try {
      return await this.model.count({ 
        where: { 
          tenantId,
          riskProfileId 
        } 
      });
    } catch (error) {
      throw new Error(`Failed to count assignments by risk profile ${riskProfileId}: ${error.message}`);
    }
  }

  /**
   * Count assignments by assignment type
   */
  async countByAssignmentType(tenantId: string, assignmentType: AssignmentType): Promise<number> {
    try {
      return await this.model.count({ 
        where: { 
          tenantId,
          assignmentType: assignmentType 
        } 
      });
    } catch (error) {
      throw new Error(`Failed to count assignments by type ${assignmentType}: ${error.message}`);
    }
  }

  /**
   * Count active assignments by tenant
   */
  async countActiveByTenant(tenantId: string): Promise<number> {
    try {
      return await this.model.count({ 
        where: { 
          tenantId,
          isActive: true 
        } 
      });
    } catch (error) {
      throw new Error(`Failed to count active assignments by tenant ${tenantId}: ${error.message}`);
    }
  }

  /**
   * Check if assignment exists
   */
  async existsAssignment(tenantId: string, customerId: string, riskProfileId: string): Promise<boolean> {
    try {
      const count = await this.model.count({
        where: { tenantId, customerId, riskProfileId }
      });
      return count > 0;
    } catch (error) {
      throw new Error(`Failed to check if assignment exists: ${error.message}`);
    }
  }

  /**
   * Check for conflicting assignments
   */
  async hasConflictingAssignment(tenantId: string, customerId: string, riskProfileId: string, assignmentType: AssignmentType): Promise<boolean> {
    try {
      // Check for existing assignment with different type
      const count = await this.model.count({
        where: { 
          tenantId, 
          customerId, 
          riskProfileId,
          assignmentType: { not: assignmentType },
          isActive: true
        }
      });
      return count > 0;
    } catch (error) {
      throw new Error(`Failed to check for conflicting assignment: ${error.message}`);
    }
  }

  /**
   * Deactivate specific assignment
   */
  async deactivateAssignment(tenantId: string, customerId: string, riskProfileId: string): Promise<boolean> {
    try {
      const result = await this.model.updateMany({
        where: { tenantId, customerId, riskProfileId },
        data: { isActive: false }
      });
      return result.count > 0;
    } catch (error) {
      throw new Error(`Failed to deactivate assignment: ${error.message}`);
    }
  }

  /**
   * Deactivate all assignments for a customer
   */
  async deactivateCustomerAssignments(tenantId: string, customerId: string): Promise<number> {
    try {
      const result = await this.model.updateMany({
        where: { tenantId, customerId },
        data: { isActive: false }
      });
      return result.count;
    } catch (error) {
      throw new Error(`Failed to deactivate customer assignments: ${error.message}`);
    }
  }

  /**
   * Deactivate all assignments for a risk profile
   */
  async deactivateRiskProfileAssignments(tenantId: string, riskProfileId: string): Promise<number> {
    try {
      const result = await this.model.updateMany({
        where: { tenantId, riskProfileId },
        data: { isActive: false }
      });
      return result.count;
    } catch (error) {
      throw new Error(`Failed to deactivate risk profile assignments: ${error.message}`);
    }
  }
}
