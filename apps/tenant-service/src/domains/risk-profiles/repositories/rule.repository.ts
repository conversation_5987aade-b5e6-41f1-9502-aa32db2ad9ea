/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable } from '@nestjs/common';
import { Prisma, Rule } from '@prisma/tenant-client';
import { BaseRepository, IBaseRepository } from '@qeep/common';
import { IRule, IRuleAction, IRuleCondition, RuleCategory, RuleType } from '@qeep/contracts/tenant/interfaces';
import { TenantPrismaService } from '../../../database/prisma.service';

/**
 * Rule Repository Interface
 * Defines all rule-specific data access operations
 */
export interface IRuleRepository extends IBaseRepository<Rule, Prisma.RuleCreateInput, Prisma.RuleUpdateInput> {
  // Rule-specific methods that return IRule types
  getRuleById(id: string): Promise<IRule | null>;
  getAllRules(options?: any): Promise<IRule[]>;
  findRulesWithOptions(options: any): Promise<IRule[]>;

  // Search and filtering
  findByName(tenantId: string, riskProfileId: string, name: string): Promise<IRule | null>;
  findByRiskProfile(tenantId: string, riskProfileId: string, options?: { limit?: number; offset?: number }): Promise<IRule[]>;
  findByTenant(tenantId: string, options?: { limit?: number; offset?: number }): Promise<IRule[]>;
  searchRules(tenantId: string, searchTerm: string): Promise<IRule[]>;

  // Business logic queries
  findActiveRules(tenantId: string, riskProfileId?: string): Promise<IRule[]>;
  findRulesByCategory(tenantId: string, category: RuleCategory): Promise<IRule[]>;
  findRulesByType(tenantId: string, ruleType: RuleType): Promise<IRule[]>;
  findEffectiveRules(tenantId: string, date?: Date): Promise<IRule[]>;
  findRulesByPriority(tenantId: string, minPriority?: number): Promise<IRule[]>;

  // Customer-specific rule queries
  findApplicableRulesForCustomer(tenantId: string, customerId: string): Promise<IRule[]>;
  findRulesForRiskProfiles(tenantId: string, riskProfileIds: string[]): Promise<IRule[]>;

  // Statistics and counts
  countByTenant(tenantId: string): Promise<number>;
  countByRiskProfile(tenantId: string, riskProfileId: string): Promise<number>;
  countActiveByTenant(tenantId: string): Promise<number>;
  countByCategory(tenantId: string, category: RuleCategory): Promise<number>;
  countByType(tenantId: string, ruleType: RuleType): Promise<number>;

  // Validation operations
  existsByName(tenantId: string, riskProfileId: string, name: string, excludeId?: string): Promise<boolean>;
  validateRuleConditions(conditions: IRuleCondition): Promise<boolean>;
  validateRuleActions(actions: IRuleAction): Promise<boolean>;
}

/**
 * Rule Repository Implementation
 * Handles all rule entity data access operations
 */
@Injectable()
export class RuleRepository extends BaseRepository<Rule, Prisma.RuleCreateInput, Prisma.RuleUpdateInput> implements IRuleRepository {
  constructor(private readonly tenantPrisma: TenantPrismaService) {
    super(tenantPrisma);
  }

  protected get model() {
    return this.tenantPrisma.rule;
  }

  /**
   * Transform Prisma Rule to IRule interface
   */
  private transformToInterface(rule: Rule): IRule {
    return {
      id: rule.id,
      tenantId: rule.tenantId,
      riskProfileId: rule.riskProfileId,
      name: rule.name,
      description: rule.description || undefined,
      ruleType: rule.ruleType as RuleType,
      ruleCategory: rule.ruleCategory as RuleCategory,
      conditions: rule.conditions as unknown as IRuleCondition,
      actions: rule.actions as unknown as IRuleAction,
      thresholds: rule.thresholds ? (rule.thresholds as Record<string, number>) : undefined,
      priority: rule.priority,
      isActive: rule.isActive,
      effectiveFrom: rule.effectiveFrom || undefined,
      effectiveUntil: rule.effectiveUntil || undefined,
      metadata: rule.metadata ? (rule.metadata as Record<string, any>) : undefined,
      createdAt: rule.createdAt,
      updatedAt: rule.updatedAt,
      createdBy: rule.createdBy || undefined,
    };
  }

  /**
   * Get rule by ID returning IRule
   */
  async getRuleById(id: string): Promise<IRule | null> {
    try {
      const rule = await this.model.findUnique({
        where: { id },
      });
      return rule ? this.transformToInterface(rule) : null;
    } catch (error) {
      throw new Error(`Failed to find rule by ID ${id}: ${error.message}`);
    }
  }

  /**
   * Get all rules returning IRule[]
   */
  async getAllRules(options?: any): Promise<IRule[]> {
    try {
      const rules = await this.model.findMany(options);
      return rules.map((rule: Rule) => this.transformToInterface(rule));
    } catch (error) {
      throw new Error(`Failed to find rules: ${error.message}`);
    }
  }

  /**
   * Find rules with full Prisma options and return transformed IRule objects
   */
  async findRulesWithOptions(options: any): Promise<IRule[]> {
    try {
      const rules = await this.model.findMany(options);
      return rules.map((rule: Rule) => this.transformToInterface(rule));
    } catch (error) {
      throw new Error(`Failed to find rules with options: ${error.message}`);
    }
  }

  /**
   * Find rule by name within risk profile
   */
  async findByName(tenantId: string, riskProfileId: string, name: string): Promise<IRule | null> {
    try {
      const rule = await this.model.findUnique({
        where: {
          tenantId_riskProfileId_name: {
            tenantId,
            riskProfileId,
            name,
          },
        },
      });
      return rule ? this.transformToInterface(rule) : null;
    } catch (error) {
      throw new Error(`Failed to find rule by name ${name}: ${error.message}`);
    }
  }

  /**
   * Find rules by risk profile with pagination
   */
  async findByRiskProfile(tenantId: string, riskProfileId: string, options?: { limit?: number; offset?: number }): Promise<IRule[]> {
    try {
      const rules = await this.model.findMany({
        where: { tenantId, riskProfileId },
        take: options?.limit,
        skip: options?.offset,
        orderBy: { priority: 'desc' },
      });
      return rules.map((rule: Rule) => this.transformToInterface(rule));
    } catch (error) {
      throw new Error(`Failed to find rules by risk profile ${riskProfileId}: ${error.message}`);
    }
  }

  /**
   * Find rules by tenant with pagination
   */
  async findByTenant(tenantId: string, options?: { limit?: number; offset?: number }): Promise<IRule[]> {
    try {
      const rules = await this.model.findMany({
        where: { tenantId },
        take: options?.limit,
        skip: options?.offset,
        orderBy: { createdAt: 'desc' },
      });
      return rules.map((rule: Rule) => this.transformToInterface(rule));
    } catch (error) {
      throw new Error(`Failed to find rules by tenant ${tenantId}: ${error.message}`);
    }
  }

  /**
   * Search rules by name or description
   */
  async searchRules(tenantId: string, searchTerm: string): Promise<IRule[]> {
    try {
      const rules = await this.model.findMany({
        where: {
          tenantId,
          OR: [{ name: { contains: searchTerm, mode: 'insensitive' } }, { description: { contains: searchTerm, mode: 'insensitive' } }],
        },
        orderBy: { priority: 'desc' },
      });
      return rules.map((rule: Rule) => this.transformToInterface(rule));
    } catch (error) {
      throw new Error(`Failed to search rules: ${error.message}`);
    }
  }

  /**
   * Find active rules for tenant or specific risk profile
   */
  async findActiveRules(tenantId: string, riskProfileId?: string): Promise<IRule[]> {
    try {
      const where: any = {
        tenantId,
        isActive: true,
      };

      if (riskProfileId) {
        where.riskProfileId = riskProfileId;
      }

      const rules = await this.model.findMany({
        where,
        orderBy: { priority: 'desc' },
      });
      return rules.map((rule: Rule) => this.transformToInterface(rule));
    } catch (error) {
      throw new Error(`Failed to find active rules: ${error.message}`);
    }
  }

  /**
   * Find rules by category
   */
  async findRulesByCategory(tenantId: string, category: RuleCategory): Promise<IRule[]> {
    try {
      const rules = await this.model.findMany({
        where: {
          tenantId,
          ruleCategory: category,
        },
        orderBy: { priority: 'desc' },
      });
      return rules.map((rule: Rule) => this.transformToInterface(rule));
    } catch (error) {
      throw new Error(`Failed to find rules by category ${category}: ${error.message}`);
    }
  }

  /**
   * Find rules by type
   */
  async findRulesByType(tenantId: string, ruleType: RuleType): Promise<IRule[]> {
    try {
      const rules = await this.model.findMany({
        where: {
          tenantId,
          ruleType: ruleType,
        },
        orderBy: { priority: 'desc' },
      });
      return rules.map((rule: Rule) => this.transformToInterface(rule));
    } catch (error) {
      throw new Error(`Failed to find rules by type ${ruleType}: ${error.message}`);
    }
  }

  /**
   * Find effective rules (within date range)
   */
  async findEffectiveRules(tenantId: string, date: Date = new Date()): Promise<IRule[]> {
    try {
      const rules = await this.model.findMany({
        where: {
          tenantId,
          isActive: true,
          OR: [{ effectiveFrom: null }, { effectiveFrom: { lte: date } }],
          AND: [
            {
              OR: [{ effectiveUntil: null }, { effectiveUntil: { gte: date } }],
            },
          ],
        },
        orderBy: { priority: 'desc' },
      });
      return rules.map((rule: Rule) => this.transformToInterface(rule));
    } catch (error) {
      throw new Error(`Failed to find effective rules: ${error.message}`);
    }
  }

  /**
   * Find rules by minimum priority
   */
  async findRulesByPriority(tenantId: string, minPriority: number = 0): Promise<IRule[]> {
    try {
      const rules = await this.model.findMany({
        where: {
          tenantId,
          priority: { gte: minPriority },
        },
        orderBy: { priority: 'desc' },
      });
      return rules.map((rule: Rule) => this.transformToInterface(rule));
    } catch (error) {
      throw new Error(`Failed to find rules by priority: ${error.message}`);
    }
  }

  /**
   * Find applicable rules for a customer (based on their risk profile assignments)
   * This method will be implemented after customer assignment repository is created
   */
  async findApplicableRulesForCustomer(tenantId: string, customerId: string): Promise<IRule[]> {
    try {
      // This will be implemented in Phase 2 when we integrate with customer service
      // For now, return empty array
      return [];
    } catch (error) {
      throw new Error(`Failed to find applicable rules for customer ${customerId}: ${error.message}`);
    }
  }

  /**
   * Find rules for multiple risk profiles
   */
  async findRulesForRiskProfiles(tenantId: string, riskProfileIds: string[]): Promise<IRule[]> {
    try {
      const rules = await this.model.findMany({
        where: {
          tenantId,
          riskProfileId: { in: riskProfileIds },
          isActive: true,
        },
        orderBy: { priority: 'desc' },
      });
      return rules.map((rule: Rule) => this.transformToInterface(rule));
    } catch (error) {
      throw new Error(`Failed to find rules for risk profiles: ${error.message}`);
    }
  }

  /**
   * Count rules by tenant
   */
  async countByTenant(tenantId: string): Promise<number> {
    try {
      return await this.model.count({ where: { tenantId } });
    } catch (error) {
      throw new Error(`Failed to count rules by tenant ${tenantId}: ${error.message}`);
    }
  }

  /**
   * Count rules by risk profile
   */
  async countByRiskProfile(tenantId: string, riskProfileId: string): Promise<number> {
    try {
      return await this.model.count({
        where: {
          tenantId,
          riskProfileId,
        },
      });
    } catch (error) {
      throw new Error(`Failed to count rules by risk profile ${riskProfileId}: ${error.message}`);
    }
  }

  /**
   * Count active rules by tenant
   */
  async countActiveByTenant(tenantId: string): Promise<number> {
    try {
      return await this.model.count({
        where: {
          tenantId,
          isActive: true,
        },
      });
    } catch (error) {
      throw new Error(`Failed to count active rules by tenant ${tenantId}: ${error.message}`);
    }
  }

  /**
   * Count rules by category
   */
  async countByCategory(tenantId: string, category: RuleCategory): Promise<number> {
    try {
      return await this.model.count({
        where: {
          tenantId,
          ruleCategory: category,
        },
      });
    } catch (error) {
      throw new Error(`Failed to count rules by category ${category}: ${error.message}`);
    }
  }

  /**
   * Count rules by type
   */
  async countByType(tenantId: string, ruleType: RuleType): Promise<number> {
    try {
      return await this.model.count({
        where: {
          tenantId,
          ruleType: ruleType,
        },
      });
    } catch (error) {
      throw new Error(`Failed to count rules by type ${ruleType}: ${error.message}`);
    }
  }

  /**
   * Check if rule exists by name within risk profile
   */
  async existsByName(tenantId: string, riskProfileId: string, name: string, excludeId?: string): Promise<boolean> {
    try {
      const where: any = { tenantId, riskProfileId, name };
      if (excludeId) {
        where.id = { not: excludeId };
      }

      const count = await this.count(where);
      return count > 0;
    } catch (error) {
      throw new Error(`Failed to check if rule exists by name: ${error.message}`);
    }
  }

  /**
   * Validate rule conditions structure
   */
  async validateRuleConditions(conditions: IRuleCondition): Promise<boolean> {
    try {
      // Basic validation - ensure required fields exist
      if (!conditions.type || !conditions.conditions || !Array.isArray(conditions.conditions)) {
        return false;
      }

      // Validate each condition
      for (const condition of conditions.conditions) {
        if (!condition.field || !condition.operator || condition.value === undefined) {
          return false;
        }
      }

      return true;
    } catch (error) {
      throw new Error(`Failed to validate rule conditions: ${error.message}`);
    }
  }

  /**
   * Validate rule actions structure
   */
  async validateRuleActions(actions: IRuleAction): Promise<boolean> {
    try {
      // Basic validation - ensure required fields exist
      if (!actions.actions || !Array.isArray(actions.actions) || actions.actions.length === 0) {
        return false;
      }

      // Validate each action
      for (const action of actions.actions) {
        if (!action.type) {
          return false;
        }
      }

      return true;
    } catch (error) {
      throw new Error(`Failed to validate rule actions: ${error.message}`);
    }
  }
}
