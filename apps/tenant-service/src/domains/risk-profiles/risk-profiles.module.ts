import { Module } from '@nestjs/common';
import { DatabaseModule } from '../../database/database.module';

// Controllers
import { CustomerAssignmentController, CustomerRiskProfileAssignmentController, RiskProfileAssignmentController } from './controllers/customer-assignment-simple.controller';
import { RiskProfileAnalyticsController, RiskProfileController } from './controllers/risk-profile-simple.controller';
import { RiskProfileRuleController, RuleController } from './controllers/rule-simple.controller';

// Services
import { CustomerAssignmentService, RiskProfileService, RuleService } from './services';

// Repositories
import { CustomerRiskProfileAssignmentRepository, RiskProfileRepository, RuleRepository } from './repositories';

@Module({
  imports: [DatabaseModule],
  controllers: [
    // Risk Profile Controllers
    RiskProfileController,
    RiskProfileAnalyticsController,

    // Rule Controllers
    RuleController,
    RiskProfileRuleController,

    // Customer Assignment Controllers
    CustomerRiskProfileAssignmentController,
    CustomerAssignmentController,
    RiskProfileAssignmentController,
  ],
  providers: [
    // Services
    RiskProfileService,
    RuleService,
    CustomerAssignmentService,

    // Repositories
    RiskProfileRepository,
    RuleRepository,
    CustomerRiskProfileAssignmentRepository,
  ],
  exports: [
    // Export services for use in other modules
    RiskProfileService,
    RuleService,
    CustomerAssignmentService,

    // Export repositories for use in other modules
    RiskProfileRepository,
    RuleRepository,
    CustomerRiskProfileAssignmentRepository,
  ],
})
export class RiskProfilesModule {}
