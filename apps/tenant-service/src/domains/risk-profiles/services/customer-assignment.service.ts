/* eslint-disable @typescript-eslint/no-explicit-any */
import { ConflictException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { createId } from '@paralleldrive/cuid2';
import {
  AssignCustomerToRiskProfileRequestDto,
  UpdateCustomerRiskProfileAssignmentRequestDto,
  CustomerAssignmentQueryDto,
  CustomerRiskProfileAssignmentResponseDto,
  CustomerAssignmentListResponseDto,
  SingleCustomerAssignmentResponseDto,
  AutoAssignCustomersRequestDto,
  AutoAssignmentResultDto,
  BulkAssignmentRequestDto,
  BulkAssignmentResultDto
} from '@qeep/contracts/tenant/dtos';
import { ICustomerRiskProfileAssignment, AssignmentType } from '@qeep/contracts/tenant/interfaces';
import { CustomerRiskProfileAssignmentRepository } from '../repositories/customer-risk-profile-assignment.repository';
import { RiskProfileRepository } from '../repositories/risk-profile.repository';

@Injectable()
export class CustomerAssignmentService {
  private readonly logger = new Logger(CustomerAssignmentService.name);

  constructor(
    private readonly assignmentRepository: CustomerRiskProfileAssignmentRepository,
    private readonly riskProfileRepository: RiskProfileRepository
  ) {}

  /**
   * Assign customer to risk profile
   */
  async assignCustomerToRiskProfile(tenantId: string, customerId: string, assignDto: AssignCustomerToRiskProfileRequestDto): Promise<SingleCustomerAssignmentResponseDto> {
    // Verify risk profile exists and belongs to tenant
    const riskProfile = await this.riskProfileRepository.getRiskProfileById(assignDto.risk_profile_id);
    if (!riskProfile || riskProfile.tenantId !== tenantId) {
      throw new NotFoundException(`Risk profile with ID ${assignDto.risk_profile_id} not found`);
    }

    // Check if assignment already exists
    const existingAssignment = await this.assignmentRepository.findByCustomerAndProfile(tenantId, customerId, assignDto.risk_profile_id);
    if (existingAssignment && existingAssignment.isActive) {
      throw new ConflictException(`Customer is already assigned to this risk profile`);
    }

    const assignment = await this.assignmentRepository.create({
      id: `assign_${createId()}`.substring(0, 35),
      tenantId,
      customerId,
      riskProfileId: assignDto.risk_profile_id,
      assignmentType: assignDto.assignment_type as AssignmentType,
      assignmentCriteria: assignDto.assignment_criteria || {},
      assignmentPriority: assignDto.assignment_priority || 100,
      allowOverride: assignDto.allow_override ?? true,
      assignedAt: new Date(),
      assignedBy: 'USER_ASSIGNED', // TODO: Get from auth context
      isActive: true,
      effectiveFrom: assignDto.effective_from ? new Date(assignDto.effective_from) : new Date(),
      effectiveUntil: assignDto.effective_until ? new Date(assignDto.effective_until) : undefined,
      metadata: assignDto.metadata || {},
    });

    this.logger.log(`Customer ${customerId} assigned to risk profile ${assignDto.risk_profile_id}`);
    
    return {
      assignment: this.transformToResponseDto(assignment, riskProfile.name)
    };
  }

  /**
   * Get customer risk profile assignments
   */
  async getCustomerAssignments(tenantId: string, customerId: string): Promise<CustomerAssignmentListResponseDto> {
    const assignments = await this.assignmentRepository.findByCustomer(tenantId, customerId);
    
    // Get risk profile names for each assignment
    const assignmentsWithNames = await Promise.all(
      assignments.map(async (assignment) => {
        const riskProfile = await this.riskProfileRepository.getRiskProfileById(assignment.riskProfileId);
        return this.transformToResponseDto(assignment, riskProfile?.name);
      })
    );
    
    return {
      assignments: assignmentsWithNames,
      pagination: {
        page: 1,
        limit: assignments.length,
        total: assignments.length,
        total_pages: 1,
        has_next: false,
        has_prev: false,
      }
    };
  }

  /**
   * Get all customer assignments for a tenant
   */
  async getAllCustomerAssignments(tenantId: string, query: CustomerAssignmentQueryDto): Promise<CustomerAssignmentListResponseDto> {
    const options = {
      where: {
        tenantId,
        ...(query.assignment_type && { assignmentType: query.assignment_type }),
        ...(query.is_active !== undefined && { isActive: query.is_active }),
      },
      orderBy: {
        [query.sort_by || 'assignedAt']: query.sort_order || 'desc'
      },
      skip: ((query.page || 1) - 1) * (query.limit || 20),
      take: query.limit || 20,
    };

    const [assignments, total] = await Promise.all([
      this.assignmentRepository.findAssignmentsWithOptions(options),
      this.assignmentRepository.countByTenant(tenantId)
    ]);

    const totalPages = Math.ceil(total / (query.limit || 20));
    const currentPage = query.page || 1;

    // Get risk profile names for each assignment
    const assignmentsWithNames = await Promise.all(
      assignments.map(async (assignment) => {
        const riskProfile = await this.riskProfileRepository.getRiskProfileById(assignment.riskProfileId);
        return this.transformToResponseDto(assignment, riskProfile?.name);
      })
    );

    return {
      assignments: assignmentsWithNames,
      pagination: {
        page: currentPage,
        limit: query.limit || 20,
        total,
        total_pages: totalPages,
        has_next: currentPage < totalPages,
        has_prev: currentPage > 1,
      }
    };
  }

  /**
   * Get assignment by ID
   */
  async getAssignmentById(tenantId: string, assignmentId: string): Promise<SingleCustomerAssignmentResponseDto> {
    const assignment = await this.assignmentRepository.getAssignmentById(assignmentId);
    
    if (!assignment || assignment.tenantId !== tenantId) {
      throw new NotFoundException(`Assignment with ID ${assignmentId} not found`);
    }

    // Get risk profile name
    const riskProfile = await this.riskProfileRepository.getRiskProfileById(assignment.riskProfileId);
    
    return {
      assignment: this.transformToResponseDto(assignment, riskProfile?.name)
    };
  }

  /**
   * Update customer assignment
   */
  async updateCustomerAssignment(tenantId: string, assignmentId: string, updateDto: UpdateCustomerRiskProfileAssignmentRequestDto): Promise<SingleCustomerAssignmentResponseDto> {
    const existingAssignment = await this.assignmentRepository.getAssignmentById(assignmentId);
    
    if (!existingAssignment || existingAssignment.tenantId !== tenantId) {
      throw new NotFoundException(`Assignment with ID ${assignmentId} not found`);
    }

    const updatedAssignment = await this.assignmentRepository.update(assignmentId, {
      ...(updateDto.assignment_priority !== undefined && { assignmentPriority: updateDto.assignment_priority }),
      ...(updateDto.allow_override !== undefined && { allowOverride: updateDto.allow_override }),
      ...(updateDto.is_active !== undefined && { isActive: updateDto.is_active }),
      ...(updateDto.effective_until && { effectiveUntil: new Date(updateDto.effective_until) }),
      ...(updateDto.metadata && { metadata: updateDto.metadata }),
    });

    this.logger.log(`Assignment updated successfully: ${assignmentId}`);
    
    // Get risk profile name
    const riskProfile = await this.riskProfileRepository.getRiskProfileById(updatedAssignment.riskProfileId);
    
    return {
      assignment: this.transformToResponseDto(updatedAssignment, riskProfile?.name)
    };
  }

  /**
   * Remove customer assignment (soft delete)
   */
  async removeCustomerAssignment(tenantId: string, assignmentId: string): Promise<void> {
    const existingAssignment = await this.assignmentRepository.getAssignmentById(assignmentId);
    
    if (!existingAssignment || existingAssignment.tenantId !== tenantId) {
      throw new NotFoundException(`Assignment with ID ${assignmentId} not found`);
    }

    await this.assignmentRepository.delete(assignmentId);
    this.logger.log(`Assignment removed successfully: ${assignmentId}`);
  }

  /**
   * Get assignments by risk profile
   */
  async getAssignmentsByRiskProfile(tenantId: string, riskProfileId: string): Promise<CustomerAssignmentListResponseDto> {
    // Verify risk profile exists and belongs to tenant
    const riskProfile = await this.riskProfileRepository.getRiskProfileById(riskProfileId);
    if (!riskProfile || riskProfile.tenantId !== tenantId) {
      throw new NotFoundException(`Risk profile with ID ${riskProfileId} not found`);
    }

    const assignments = await this.assignmentRepository.findByRiskProfile(tenantId, riskProfileId);
    
    return {
      assignments: assignments.map(assignment => this.transformToResponseDto(assignment, riskProfile.name)),
      pagination: {
        page: 1,
        limit: assignments.length,
        total: assignments.length,
        total_pages: 1,
        has_next: false,
        has_prev: false,
      }
    };
  }

  /**
   * Get automatic assignments
   */
  async getAutomaticAssignments(tenantId: string): Promise<CustomerAssignmentListResponseDto> {
    const assignments = await this.assignmentRepository.findAutomaticAssignments(tenantId);
    
    // Get risk profile names for each assignment
    const assignmentsWithNames = await Promise.all(
      assignments.map(async (assignment) => {
        const riskProfile = await this.riskProfileRepository.getRiskProfileById(assignment.riskProfileId);
        return this.transformToResponseDto(assignment, riskProfile?.name);
      })
    );
    
    return {
      assignments: assignmentsWithNames,
      pagination: {
        page: 1,
        limit: assignments.length,
        total: assignments.length,
        total_pages: 1,
        has_next: false,
        has_prev: false,
      }
    };
  }

  /**
   * Get manual assignments
   */
  async getManualAssignments(tenantId: string): Promise<CustomerAssignmentListResponseDto> {
    const assignments = await this.assignmentRepository.findManualAssignments(tenantId);
    
    // Get risk profile names for each assignment
    const assignmentsWithNames = await Promise.all(
      assignments.map(async (assignment) => {
        const riskProfile = await this.riskProfileRepository.getRiskProfileById(assignment.riskProfileId);
        return this.transformToResponseDto(assignment, riskProfile?.name);
      })
    );
    
    return {
      assignments: assignmentsWithNames,
      pagination: {
        page: 1,
        limit: assignments.length,
        total: assignments.length,
        total_pages: 1,
        has_next: false,
        has_prev: false,
      }
    };
  }

  /**
   * Auto-assign customers based on criteria
   */
  async autoAssignCustomers(tenantId: string, autoAssignDto: AutoAssignCustomersRequestDto): Promise<AutoAssignmentResultDto> {
    // TODO: Implement auto-assignment logic based on customer risk levels
    // This would typically integrate with the customer service to get customer data
    
    this.logger.log(`Auto-assignment ${autoAssignDto.dry_run ? 'dry run' : 'execution'} for tenant ${tenantId}`);
    
    return {
      dry_run: autoAssignDto.dry_run,
      total_customers_evaluated: 0,
      customers_to_assign: 0,
      assignments: [],
      conflicts: [],
      executed_at: new Date().toISOString(),
    };
  }

  /**
   * Bulk assign customers to risk profile
   */
  async bulkAssignCustomers(tenantId: string, bulkAssignDto: BulkAssignmentRequestDto): Promise<BulkAssignmentResultDto> {
    // Verify risk profile exists and belongs to tenant
    const riskProfile = await this.riskProfileRepository.getRiskProfileById(bulkAssignDto.risk_profile_id);
    if (!riskProfile || riskProfile.tenantId !== tenantId) {
      throw new NotFoundException(`Risk profile with ID ${bulkAssignDto.risk_profile_id} not found`);
    }

    const results: Array<{ customer_id: string; success: boolean; assignment_id?: string; error?: string }> = [];
    
    for (const customerId of bulkAssignDto.customer_ids) {
      try {
        const assignment = await this.assignmentRepository.create({
          id: `assign_${createId()}`.substring(0, 35),
          tenantId,
          customerId,
          riskProfileId: bulkAssignDto.risk_profile_id,
          assignmentType: bulkAssignDto.assignment_type as AssignmentType,
          assignmentCriteria: {},
          assignmentPriority: bulkAssignDto.assignment_priority || 100,
          allowOverride: bulkAssignDto.allow_override ?? true,
          assignedAt: new Date(),
          assignedBy: 'BULK_ASSIGNED',
          isActive: true,
          effectiveFrom: bulkAssignDto.effective_from ? new Date(bulkAssignDto.effective_from) : new Date(),
          effectiveUntil: bulkAssignDto.effective_until ? new Date(bulkAssignDto.effective_until) : undefined,
          metadata: bulkAssignDto.metadata || {},
        });

        results.push({
          customer_id: customerId,
          success: true,
          assignment_id: assignment.id,
        });
      } catch (error) {
        results.push({
          customer_id: customerId,
          success: false,
          error: error.message,
        });
      }
    }

    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;

    this.logger.log(`Bulk assignment completed: ${successful} successful, ${failed} failed`);
    
    return {
      total_requested: bulkAssignDto.customer_ids.length,
      successful,
      failed,
      results,
      executed_at: new Date().toISOString(),
    };
  }

  /**
   * Transform ICustomerRiskProfileAssignment to CustomerRiskProfileAssignmentResponseDto
   */
  private transformToResponseDto(assignment: ICustomerRiskProfileAssignment, riskProfileName?: string): CustomerRiskProfileAssignmentResponseDto {
    return {
      id: assignment.id,
      tenant_id: assignment.tenantId,
      customer_id: assignment.customerId,
      risk_profile_id: assignment.riskProfileId,
      assignment_type: assignment.assignmentType,
      assignment_criteria: assignment.assignmentCriteria,
      assignment_priority: assignment.assignmentPriority,
      allow_override: assignment.allowOverride,
      assigned_at: assignment.assignedAt.toISOString(),
      assigned_by: assignment.assignedBy,
      is_active: assignment.isActive,
      effective_from: assignment.effectiveFrom?.toISOString(),
      effective_until: assignment.effectiveUntil?.toISOString(),
      metadata: assignment.metadata,
      risk_profile_name: riskProfileName,
      // TODO: Add customer_name when customer service integration is available
    };
  }
}
