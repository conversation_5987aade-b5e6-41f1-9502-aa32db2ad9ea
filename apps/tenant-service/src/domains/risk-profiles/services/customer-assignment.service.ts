/* eslint-disable @typescript-eslint/no-explicit-any */
import { ConflictException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { createId } from '@paralleldrive/cuid2';
import {
  AssignCustomerToRiskProfileRequestDto,
  AutoAssignCustomersRequestDto,
  AutoAssignmentResultDto,
  BulkAssignmentRequestDto,
  BulkAssignmentResultDto,
  CustomerAssignmentListResponseDto,
  CustomerAssignmentQueryDto,
  CustomerRiskProfileAssignmentResponseDto,
  SingleCustomerAssignmentResponseDto,
  UpdateCustomerRiskProfileAssignmentRequestDto,
} from '@qeep/contracts/tenant/dtos';
import { ICustomerRiskProfileAssignment } from '@qeep/contracts/tenant/interfaces';
import { CustomerRiskProfileAssignmentRepository } from '../repositories/customer-risk-profile-assignment.repository';
import { RiskProfileRepository } from '../repositories/risk-profile.repository';

@Injectable()
export class CustomerAssignmentService {
  private readonly logger = new Logger(CustomerAssignmentService.name);

  constructor(private readonly assignmentRepository: CustomerRiskProfileAssignmentRepository, private readonly riskProfileRepository: RiskProfileRepository) {}

  /**
   * Assign customer to risk profile
   */
  async assignCustomerToRiskProfile(tenantId: string, customerId: string, assignDto: AssignCustomerToRiskProfileRequestDto): Promise<SingleCustomerAssignmentResponseDto> {
    // For now, take the first risk profile ID (the schema supports multiple but we'll implement single assignment)
    const riskProfileId = assignDto.riskProfileIds[0];

    // Verify risk profile exists and belongs to tenant
    const riskProfile = await this.riskProfileRepository.getRiskProfileById(riskProfileId);
    if (!riskProfile || riskProfile.tenantId !== tenantId) {
      throw new NotFoundException(`Risk profile with ID ${riskProfileId} not found`);
    }

    // Check if assignment already exists
    const existingAssignment = await this.assignmentRepository.findByCustomerAndProfile(tenantId, customerId, riskProfileId);
    if (existingAssignment && existingAssignment.isActive) {
      throw new ConflictException(`Customer is already assigned to this risk profile`);
    }

    const assignment = await this.assignmentRepository.create({
      id: `assign_${createId()}`.substring(0, 35),
      tenantId,
      customerId,
      riskProfileId: riskProfileId,
      assignmentType: assignDto.assignmentType as any, // Cast to handle enum differences
      assignmentCriteria: assignDto.assignmentCriteria || {},
      assignmentPriority: assignDto.assignmentPriority || 100,
      allowOverride: assignDto.allowOverride ?? true,
      assignedAt: new Date(),
      assignedBy: 'USER_ASSIGNED', // TODO: Get from auth context
      isActive: true,
      effectiveFrom: assignDto.effectiveFrom ? new Date(assignDto.effectiveFrom) : new Date(),
      effectiveUntil: assignDto.effectiveUntil ? new Date(assignDto.effectiveUntil) : undefined,
      metadata: assignDto.metadata || {},
    } as any); // Cast the entire object to handle type differences

    this.logger.log(`Customer ${customerId} assigned to risk profile ${riskProfileId}`);

    return {
      assignment: this.transformToResponseDto(assignment as any, riskProfile.name),
    };
  }

  /**
   * Get customer risk profile assignments
   */
  async getCustomerAssignments(tenantId: string, customerId: string): Promise<CustomerAssignmentListResponseDto> {
    const assignments = await this.assignmentRepository.findByCustomer(tenantId, customerId);

    // Get risk profile names for each assignment
    const assignmentsWithNames = await Promise.all(
      assignments.map(async (assignment) => {
        const riskProfile = await this.riskProfileRepository.getRiskProfileById(assignment.riskProfileId);
        return this.transformToResponseDto(assignment, riskProfile?.name);
      }),
    );

    return {
      assignments: assignmentsWithNames,
      pagination: {
        page: 1,
        limit: assignments.length,
        total: assignments.length,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      },
    };
  }

  /**
   * Get all customer assignments for a tenant
   */
  async getAllCustomerAssignments(tenantId: string, query: CustomerAssignmentQueryDto): Promise<CustomerAssignmentListResponseDto> {
    const options = {
      where: {
        tenantId,
        ...(query.assignmentType && { assignmentType: query.assignmentType }),
        ...(query.isActive !== undefined && { isActive: query.isActive }),
      },
      orderBy: {
        [query.sortBy || 'assignedAt']: query.sortOrder || 'desc',
      },
      skip: ((query.page || 1) - 1) * (query.limit || 20),
      take: query.limit || 20,
    };

    const [assignments, total] = await Promise.all([this.assignmentRepository.findAssignmentsWithOptions(options), this.assignmentRepository.countByTenant(tenantId)]);

    const totalPages = Math.ceil(total / (query.limit || 20));
    const currentPage = query.page || 1;

    // Get risk profile names for each assignment
    const assignmentsWithNames = await Promise.all(
      assignments.map(async (assignment) => {
        const riskProfile = await this.riskProfileRepository.getRiskProfileById(assignment.riskProfileId);
        return this.transformToResponseDto(assignment, riskProfile?.name);
      }),
    );

    return {
      assignments: assignmentsWithNames,
      pagination: {
        page: currentPage,
        limit: query.limit || 20,
        total,
        totalPages: totalPages,
        hasNext: currentPage < totalPages,
        hasPrev: currentPage > 1,
      },
    };
  }

  /**
   * Get assignment by ID
   */
  async getAssignmentById(tenantId: string, assignmentId: string): Promise<SingleCustomerAssignmentResponseDto> {
    const assignment = await this.assignmentRepository.getAssignmentById(assignmentId);

    if (!assignment || assignment.tenantId !== tenantId) {
      throw new NotFoundException(`Assignment with ID ${assignmentId} not found`);
    }

    // Get risk profile name
    const riskProfile = await this.riskProfileRepository.getRiskProfileById(assignment.riskProfileId);

    return {
      assignment: this.transformToResponseDto(assignment, riskProfile?.name),
    };
  }

  /**
   * Update customer assignment
   */
  async updateCustomerAssignment(tenantId: string, assignmentId: string, updateDto: UpdateCustomerRiskProfileAssignmentRequestDto): Promise<SingleCustomerAssignmentResponseDto> {
    const existingAssignment = await this.assignmentRepository.getAssignmentById(assignmentId);

    if (!existingAssignment || existingAssignment.tenantId !== tenantId) {
      throw new NotFoundException(`Assignment with ID ${assignmentId} not found`);
    }

    const updatedAssignment = await this.assignmentRepository.update(assignmentId, {
      ...(updateDto.assignmentPriority !== undefined && { assignmentPriority: updateDto.assignmentPriority }),
      ...(updateDto.allowOverride !== undefined && { allowOverride: updateDto.allowOverride }),
      ...(updateDto.isActive !== undefined && { isActive: updateDto.isActive }),
      ...(updateDto.effectiveUntil && { effectiveUntil: new Date(updateDto.effectiveUntil) }),
      ...(updateDto.metadata && { metadata: updateDto.metadata }),
    });

    this.logger.log(`Assignment updated successfully: ${assignmentId}`);

    // Get risk profile name
    const riskProfile = await this.riskProfileRepository.getRiskProfileById(updatedAssignment.riskProfileId);

    return {
      assignment: this.transformToResponseDto(updatedAssignment as any, riskProfile?.name),
    };
  }

  /**
   * Remove customer assignment (soft delete)
   */
  async removeCustomerAssignment(tenantId: string, assignmentId: string): Promise<void> {
    const existingAssignment = await this.assignmentRepository.getAssignmentById(assignmentId);

    if (!existingAssignment || existingAssignment.tenantId !== tenantId) {
      throw new NotFoundException(`Assignment with ID ${assignmentId} not found`);
    }

    await this.assignmentRepository.delete(assignmentId);
    this.logger.log(`Assignment removed successfully: ${assignmentId}`);
  }

  /**
   * Get assignments by risk profile
   */
  async getAssignmentsByRiskProfile(tenantId: string, riskProfileId: string): Promise<CustomerAssignmentListResponseDto> {
    // Verify risk profile exists and belongs to tenant
    const riskProfile = await this.riskProfileRepository.getRiskProfileById(riskProfileId);
    if (!riskProfile || riskProfile.tenantId !== tenantId) {
      throw new NotFoundException(`Risk profile with ID ${riskProfileId} not found`);
    }

    const assignments = await this.assignmentRepository.findByRiskProfile(tenantId, riskProfileId);

    return {
      assignments: assignments.map((assignment) => this.transformToResponseDto(assignment, riskProfile.name)),
      pagination: {
        page: 1,
        limit: assignments.length,
        total: assignments.length,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      },
    };
  }

  /**
   * Get automatic assignments
   */
  async getAutomaticAssignments(tenantId: string): Promise<CustomerAssignmentListResponseDto> {
    const assignments = await this.assignmentRepository.findAutomaticAssignments(tenantId);

    // Get risk profile names for each assignment
    const assignmentsWithNames = await Promise.all(
      assignments.map(async (assignment) => {
        const riskProfile = await this.riskProfileRepository.getRiskProfileById(assignment.riskProfileId);
        return this.transformToResponseDto(assignment, riskProfile?.name);
      }),
    );

    return {
      assignments: assignmentsWithNames,
      pagination: {
        page: 1,
        limit: assignments.length,
        total: assignments.length,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      },
    };
  }

  /**
   * Get manual assignments
   */
  async getManualAssignments(tenantId: string): Promise<CustomerAssignmentListResponseDto> {
    const assignments = await this.assignmentRepository.findManualAssignments(tenantId);

    // Get risk profile names for each assignment
    const assignmentsWithNames = await Promise.all(
      assignments.map(async (assignment) => {
        const riskProfile = await this.riskProfileRepository.getRiskProfileById(assignment.riskProfileId);
        return this.transformToResponseDto(assignment, riskProfile?.name);
      }),
    );

    return {
      assignments: assignmentsWithNames,
      pagination: {
        page: 1,
        limit: assignments.length,
        total: assignments.length,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      },
    };
  }

  /**
   * Auto-assign customers based on criteria
   */
  async autoAssignCustomers(tenantId: string, autoAssignDto: AutoAssignCustomersRequestDto): Promise<AutoAssignmentResultDto> {
    // TODO: Implement auto-assignment logic based on customer risk levels
    // This would typically integrate with the customer service to get customer data

    this.logger.log(`Auto-assignment ${autoAssignDto.dryRun ? 'dry run' : 'execution'} for tenant ${tenantId}`);

    return {
      dryRun: autoAssignDto.dryRun,
      totalCustomersEvaluated: 0,
      customersToAssign: 0,
      assignments: [],
      conflicts: [],
      executedAt: new Date().toISOString(),
    };
  }

  /**
   * Bulk assign customers to risk profile
   */
  async bulkAssignCustomers(tenantId: string, bulkAssignDto: BulkAssignmentRequestDto): Promise<BulkAssignmentResultDto> {
    // Verify risk profile exists and belongs to tenant
    const riskProfile = await this.riskProfileRepository.getRiskProfileById(bulkAssignDto.riskProfileId);
    if (!riskProfile || riskProfile.tenantId !== tenantId) {
      throw new NotFoundException(`Risk profile with ID ${bulkAssignDto.riskProfileId} not found`);
    }

    const results: Array<{ customerId: string; success: boolean; assignmentId?: string; error?: string }> = [];

    for (const customerId of bulkAssignDto.customerIds) {
      try {
        const assignment = await this.assignmentRepository.create({
          id: `assign_${createId()}`.substring(0, 35),
          tenantId,
          customerId,
          riskProfileId: bulkAssignDto.riskProfileId,
          assignmentType: bulkAssignDto.assignmentType as any, // Cast to handle enum differences
          assignmentCriteria: {},
          assignmentPriority: bulkAssignDto.assignmentPriority || 100,
          allowOverride: bulkAssignDto.allowOverride ?? true,
          assignedAt: new Date(),
          assignedBy: 'BULK_ASSIGNED',
          isActive: true,
          effectiveFrom: bulkAssignDto.effectiveFrom ? new Date(bulkAssignDto.effectiveFrom) : new Date(),
          effectiveUntil: bulkAssignDto.effectiveUntil ? new Date(bulkAssignDto.effectiveUntil) : undefined,
          metadata: bulkAssignDto.metadata || {},
        } as any); // Cast the entire object to handle type differences

        results.push({
          customerId: customerId,
          success: true,
          assignmentId: assignment.id,
        });
      } catch (error) {
        results.push({
          customerId: customerId,
          success: false,
          error: error.message,
        });
      }
    }

    const successful = results.filter((r) => r.success).length;
    const failed = results.filter((r) => !r.success).length;

    this.logger.log(`Bulk assignment completed: ${successful} successful, ${failed} failed`);

    return {
      totalRequested: bulkAssignDto.customerIds.length,
      successful,
      failed,
      results,
      executedAt: new Date().toISOString(),
    };
  }

  /**
   * Transform ICustomerRiskProfileAssignment to CustomerRiskProfileAssignmentResponseDto
   */
  private transformToResponseDto(assignment: ICustomerRiskProfileAssignment, riskProfileName?: string): CustomerRiskProfileAssignmentResponseDto {
    return {
      id: assignment.id,
      tenantId: assignment.tenantId,
      customerId: assignment.customerId,
      riskProfileId: assignment.riskProfileId,
      assignmentType: assignment.assignmentType,
      assignmentCriteria: assignment.assignmentCriteria,
      assignmentPriority: assignment.assignmentPriority,
      allowOverride: assignment.allowOverride,
      assignedAt: assignment.assignedAt.toISOString(),
      assignedBy: assignment.assignedBy,
      isActive: assignment.isActive,
      effectiveFrom: assignment.effectiveFrom?.toISOString(),
      effectiveUntil: assignment.effectiveUntil?.toISOString(),
      metadata: assignment.metadata,
      riskProfileName: riskProfileName,
      // TODO: Add customerName when customer service integration is available
    };
  }
}
