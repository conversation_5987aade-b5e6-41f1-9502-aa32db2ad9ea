/* eslint-disable @typescript-eslint/no-explicit-any */
import { ConflictException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { createId } from '@paralleldrive/cuid2';
import {
  CreateRuleRequestDto,
  UpdateRuleRequestDto,
  RuleQueryDto,
  RuleResponseDto,
  RuleListResponseDto,
  SingleRuleResponseDto
} from '@qeep/contracts/tenant/dtos';
import { IRule, RuleCategory, RuleType } from '@qeep/contracts/tenant/interfaces';
import { RuleRepository } from '../repositories/rule.repository';
import { RiskProfileRepository } from '../repositories/risk-profile.repository';

@Injectable()
export class RuleService {
  private readonly logger = new Logger(RuleService.name);

  constructor(
    private readonly ruleRepository: RuleRepository,
    private readonly riskProfileRepository: RiskProfileRepository
  ) {}

  /**
   * Create a new rule
   */
  async createRule(tenantId: string, riskProfileId: string, createDto: CreateRuleRequestDto): Promise<SingleRuleResponseDto> {
    // Verify risk profile exists and belongs to tenant
    const riskProfile = await this.riskProfileRepository.getRiskProfileById(riskProfileId);
    if (!riskProfile || riskProfile.tenantId !== tenantId) {
      throw new NotFoundException(`Risk profile with ID ${riskProfileId} not found`);
    }

    // Check if rule with this name already exists in the risk profile
    const existingRule = await this.ruleRepository.existsByName(tenantId, riskProfileId, createDto.name);
    if (existingRule) {
      throw new ConflictException(`Rule with name '${createDto.name}' already exists in this risk profile`);
    }

    // Validate rule conditions and actions
    await this.validateRuleConditions(createDto.conditions);
    await this.validateRuleActions(createDto.actions);

    const rule = await this.ruleRepository.create({
      id: `rule_${createId()}`.substring(0, 35),
      tenantId,
      riskProfileId,
      name: createDto.name,
      description: createDto.description,
      ruleType: createDto.rule_type as RuleType,
      ruleCategory: createDto.rule_category as RuleCategory,
      conditions: createDto.conditions as any,
      actions: createDto.actions as any,
      thresholds: createDto.thresholds || {},
      priority: createDto.priority || 0,
      isActive: true,
      effectiveFrom: createDto.effective_from ? new Date(createDto.effective_from) : undefined,
      effectiveUntil: createDto.effective_until ? new Date(createDto.effective_until) : undefined,
      metadata: createDto.metadata || {},
      createdBy: 'USER_CREATED', // TODO: Get from auth context
    });

    this.logger.log(`Rule created successfully: ${rule.id}`);
    
    return {
      rule: this.transformToResponseDto(rule, riskProfile.name)
    };
  }

  /**
   * Get all rules for a tenant
   */
  async getAllRules(tenantId: string, query: RuleQueryDto): Promise<RuleListResponseDto> {
    const options = {
      where: {
        tenantId,
        ...(query.search && {
          OR: [
            { name: { contains: query.search, mode: 'insensitive' } },
            { description: { contains: query.search, mode: 'insensitive' } }
          ]
        }),
        ...(query.rule_category && { ruleCategory: query.rule_category }),
        ...(query.rule_type && { ruleType: query.rule_type }),
        ...(query.is_active !== undefined && { isActive: query.is_active }),
      },
      orderBy: {
        [query.sort_by || 'priority']: query.sort_order || 'desc'
      },
      skip: ((query.page || 1) - 1) * (query.limit || 20),
      take: query.limit || 20,
    };

    const [rules, total] = await Promise.all([
      this.ruleRepository.findRulesWithOptions(options),
      this.ruleRepository.countByTenant(tenantId)
    ]);

    const totalPages = Math.ceil(total / (query.limit || 20));
    const currentPage = query.page || 1;

    return {
      rules: rules.map(rule => this.transformToResponseDto(rule)),
      pagination: {
        page: currentPage,
        limit: query.limit || 20,
        total,
        total_pages: totalPages,
        has_next: currentPage < totalPages,
        has_prev: currentPage > 1,
      }
    };
  }

  /**
   * Get rules by risk profile
   */
  async getRulesByRiskProfile(tenantId: string, riskProfileId: string): Promise<RuleListResponseDto> {
    // Verify risk profile exists and belongs to tenant
    const riskProfile = await this.riskProfileRepository.getRiskProfileById(riskProfileId);
    if (!riskProfile || riskProfile.tenantId !== tenantId) {
      throw new NotFoundException(`Risk profile with ID ${riskProfileId} not found`);
    }

    const rules = await this.ruleRepository.findByRiskProfile(tenantId, riskProfileId);
    
    return {
      rules: rules.map(rule => this.transformToResponseDto(rule, riskProfile.name)),
      pagination: {
        page: 1,
        limit: rules.length,
        total: rules.length,
        total_pages: 1,
        has_next: false,
        has_prev: false,
      }
    };
  }

  /**
   * Get rule by ID
   */
  async getRuleById(tenantId: string, ruleId: string): Promise<SingleRuleResponseDto> {
    const rule = await this.ruleRepository.getRuleById(ruleId);
    
    if (!rule || rule.tenantId !== tenantId) {
      throw new NotFoundException(`Rule with ID ${ruleId} not found`);
    }

    // Get risk profile name for response
    const riskProfile = await this.riskProfileRepository.getRiskProfileById(rule.riskProfileId);
    
    return {
      rule: this.transformToResponseDto(rule, riskProfile?.name)
    };
  }

  /**
   * Update rule
   */
  async updateRule(tenantId: string, ruleId: string, updateDto: UpdateRuleRequestDto): Promise<SingleRuleResponseDto> {
    const existingRule = await this.ruleRepository.getRuleById(ruleId);
    
    if (!existingRule || existingRule.tenantId !== tenantId) {
      throw new NotFoundException(`Rule with ID ${ruleId} not found`);
    }

    // Check for name conflicts if name is being updated
    if (updateDto.name && updateDto.name !== existingRule.name) {
      const conflictingRule = await this.ruleRepository.existsByName(tenantId, existingRule.riskProfileId, updateDto.name, ruleId);
      if (conflictingRule) {
        throw new ConflictException(`Rule with name '${updateDto.name}' already exists in this risk profile`);
      }
    }

    // Validate conditions and actions if provided
    if (updateDto.conditions) {
      await this.validateRuleConditions(updateDto.conditions);
    }
    if (updateDto.actions) {
      await this.validateRuleActions(updateDto.actions);
    }

    const updatedRule = await this.ruleRepository.update(ruleId, {
      ...(updateDto.name && { name: updateDto.name }),
      ...(updateDto.description !== undefined && { description: updateDto.description }),
      ...(updateDto.conditions && { conditions: updateDto.conditions as any }),
      ...(updateDto.actions && { actions: updateDto.actions as any }),
      ...(updateDto.thresholds && { thresholds: updateDto.thresholds }),
      ...(updateDto.priority !== undefined && { priority: updateDto.priority }),
      ...(updateDto.is_active !== undefined && { isActive: updateDto.is_active }),
      ...(updateDto.effective_from && { effectiveFrom: new Date(updateDto.effective_from) }),
      ...(updateDto.effective_until && { effectiveUntil: new Date(updateDto.effective_until) }),
      ...(updateDto.metadata && { metadata: updateDto.metadata }),
    });

    this.logger.log(`Rule updated successfully: ${ruleId}`);
    
    // Get risk profile name for response
    const riskProfile = await this.riskProfileRepository.getRiskProfileById(updatedRule.riskProfileId);
    
    return {
      rule: this.transformToResponseDto(updatedRule, riskProfile?.name)
    };
  }

  /**
   * Delete rule (soft delete)
   */
  async deleteRule(tenantId: string, ruleId: string): Promise<void> {
    const existingRule = await this.ruleRepository.getRuleById(ruleId);
    
    if (!existingRule || existingRule.tenantId !== tenantId) {
      throw new NotFoundException(`Rule with ID ${ruleId} not found`);
    }

    await this.ruleRepository.delete(ruleId);
    this.logger.log(`Rule deleted successfully: ${ruleId}`);
  }

  /**
   * Get active rules
   */
  async getActiveRules(tenantId: string, riskProfileId?: string): Promise<RuleListResponseDto> {
    const rules = await this.ruleRepository.findActiveRules(tenantId, riskProfileId);
    
    return {
      rules: rules.map(rule => this.transformToResponseDto(rule)),
      pagination: {
        page: 1,
        limit: rules.length,
        total: rules.length,
        total_pages: 1,
        has_next: false,
        has_prev: false,
      }
    };
  }

  /**
   * Get rules by category
   */
  async getRulesByCategory(tenantId: string, category: RuleCategory): Promise<RuleListResponseDto> {
    const rules = await this.ruleRepository.findRulesByCategory(tenantId, category);
    
    return {
      rules: rules.map(rule => this.transformToResponseDto(rule)),
      pagination: {
        page: 1,
        limit: rules.length,
        total: rules.length,
        total_pages: 1,
        has_next: false,
        has_prev: false,
      }
    };
  }

  /**
   * Get rules by type
   */
  async getRulesByType(tenantId: string, ruleType: RuleType): Promise<RuleListResponseDto> {
    const rules = await this.ruleRepository.findRulesByType(tenantId, ruleType);
    
    return {
      rules: rules.map(rule => this.transformToResponseDto(rule)),
      pagination: {
        page: 1,
        limit: rules.length,
        total: rules.length,
        total_pages: 1,
        has_next: false,
        has_prev: false,
      }
    };
  }

  /**
   * Get effective rules at a specific date
   */
  async getEffectiveRules(tenantId: string, date?: Date): Promise<RuleListResponseDto> {
    const rules = await this.ruleRepository.findEffectiveRules(tenantId, date);
    
    return {
      rules: rules.map(rule => this.transformToResponseDto(rule)),
      pagination: {
        page: 1,
        limit: rules.length,
        total: rules.length,
        total_pages: 1,
        has_next: false,
        has_prev: false,
      }
    };
  }

  /**
   * Search rules
   */
  async searchRules(tenantId: string, searchTerm: string, category?: RuleCategory): Promise<RuleListResponseDto> {
    const rules = await this.ruleRepository.searchRules(tenantId, searchTerm);
    
    const filteredRules = category 
      ? rules.filter(rule => rule.ruleCategory === category)
      : rules;
    
    return {
      rules: filteredRules.map(rule => this.transformToResponseDto(rule)),
      pagination: {
        page: 1,
        limit: filteredRules.length,
        total: filteredRules.length,
        total_pages: 1,
        has_next: false,
        has_prev: false,
      }
    };
  }

  /**
   * Validate rule conditions
   */
  async validateRuleConditions(conditions: any): Promise<boolean> {
    return await this.ruleRepository.validateRuleConditions(conditions);
  }

  /**
   * Validate rule actions
   */
  async validateRuleActions(actions: any): Promise<boolean> {
    return await this.ruleRepository.validateRuleActions(actions);
  }

  /**
   * Transform IRule to RuleResponseDto
   */
  private transformToResponseDto(rule: IRule, riskProfileName?: string): RuleResponseDto {
    return {
      id: rule.id,
      tenant_id: rule.tenantId,
      risk_profile_id: rule.riskProfileId,
      name: rule.name,
      description: rule.description,
      rule_type: rule.ruleType,
      rule_category: rule.ruleCategory,
      conditions: rule.conditions as any,
      actions: rule.actions as any,
      thresholds: rule.thresholds,
      priority: rule.priority,
      is_active: rule.isActive,
      effective_from: rule.effectiveFrom?.toISOString(),
      effective_until: rule.effectiveUntil?.toISOString(),
      metadata: rule.metadata,
      created_at: rule.createdAt.toISOString(),
      updated_at: rule.updatedAt.toISOString(),
      created_by: rule.createdBy,
      risk_profile_name: riskProfileName,
    };
  }
}
