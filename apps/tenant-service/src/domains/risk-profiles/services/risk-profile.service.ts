/* eslint-disable @typescript-eslint/no-explicit-any */
import { ConflictException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { createId } from '@paralleldrive/cuid2';
import {
  CreateRiskProfileRequestDto,
  UpdateRiskProfileRequestDto,
  RiskProfileQueryDto,
  RiskProfileResponseDto,
  RiskProfileListResponseDto,
  SingleRiskProfileResponseDto,
  RiskProfileStatsDto
} from '@qeep/contracts/tenant/dtos';
import { IRiskProfile, RuleCategory } from '@qeep/contracts/tenant/interfaces';
import { RiskProfileRepository } from '../repositories/risk-profile.repository';

@Injectable()
export class RiskProfileService {
  private readonly logger = new Logger(RiskProfileService.name);

  constructor(private readonly riskProfileRepository: RiskProfileRepository) {}

  /**
   * Create a new risk profile
   */
  async createRiskProfile(tenantId: string, createDto: CreateRiskProfileRequestDto): Promise<SingleRiskProfileResponseDto> {
    // Check if risk profile with this name already exists
    const existingProfile = await this.riskProfileRepository.findByName(tenantId, createDto.name);
    
    if (existingProfile) {
      throw new ConflictException(`Risk profile with name '${createDto.name}' already exists`);
    }

    const riskProfile = await this.riskProfileRepository.create({
      id: `rp_${createId()}`.substring(0, 35),
      tenantId,
      name: createDto.name,
      description: createDto.description,
      ruleCategory: createDto.rule_category as RuleCategory,
      isActive: true,
      isSystemGenerated: false,
      priority: createDto.priority || 0,
      assignmentPriority: createDto.assignment_priority || 0,
      metadata: createDto.metadata || {},
      createdBy: 'USER_CREATED', // TODO: Get from auth context
    });

    this.logger.log(`Risk profile created successfully: ${riskProfile.id}`);
    
    return {
      risk_profile: this.transformToResponseDto(riskProfile)
    };
  }

  /**
   * Get all risk profiles for a tenant
   */
  async getAllRiskProfiles(tenantId: string, query: RiskProfileQueryDto): Promise<RiskProfileListResponseDto> {
    const options = {
      where: {
        tenantId,
        ...(query.search && {
          OR: [
            { name: { contains: query.search, mode: 'insensitive' } },
            { description: { contains: query.search, mode: 'insensitive' } }
          ]
        }),
        ...(query.rule_category && { ruleCategory: query.rule_category }),
        ...(query.is_active !== undefined && { isActive: query.is_active }),
        ...(query.is_system_generated !== undefined && { isSystemGenerated: query.is_system_generated }),
      },
      orderBy: {
        [query.sort_by || 'createdAt']: query.sort_order || 'desc'
      },
      skip: ((query.page || 1) - 1) * (query.limit || 20),
      take: query.limit || 20,
    };

    const [riskProfiles, total] = await Promise.all([
      this.riskProfileRepository.findRiskProfilesWithOptions(options),
      this.riskProfileRepository.countByTenant(tenantId)
    ]);

    const totalPages = Math.ceil(total / (query.limit || 20));
    const currentPage = query.page || 1;

    return {
      risk_profiles: riskProfiles.map(profile => this.transformToResponseDto(profile)),
      pagination: {
        page: currentPage,
        limit: query.limit || 20,
        total,
        total_pages: totalPages,
        has_next: currentPage < totalPages,
        has_prev: currentPage > 1,
      }
    };
  }

  /**
   * Get risk profile by ID
   */
  async getRiskProfileById(tenantId: string, riskProfileId: string): Promise<SingleRiskProfileResponseDto> {
    const riskProfile = await this.riskProfileRepository.getRiskProfileById(riskProfileId);
    
    if (!riskProfile || riskProfile.tenantId !== tenantId) {
      throw new NotFoundException(`Risk profile with ID ${riskProfileId} not found`);
    }

    return {
      risk_profile: this.transformToResponseDto(riskProfile)
    };
  }

  /**
   * Update risk profile
   */
  async updateRiskProfile(tenantId: string, riskProfileId: string, updateDto: UpdateRiskProfileRequestDto): Promise<SingleRiskProfileResponseDto> {
    const existingProfile = await this.riskProfileRepository.getRiskProfileById(riskProfileId);
    
    if (!existingProfile || existingProfile.tenantId !== tenantId) {
      throw new NotFoundException(`Risk profile with ID ${riskProfileId} not found`);
    }

    // Check for name conflicts if name is being updated
    if (updateDto.name && updateDto.name !== existingProfile.name) {
      const conflictingProfile = await this.riskProfileRepository.findByName(tenantId, updateDto.name);
      if (conflictingProfile) {
        throw new ConflictException(`Risk profile with name '${updateDto.name}' already exists`);
      }
    }

    const updatedProfile = await this.riskProfileRepository.update(riskProfileId, {
      ...(updateDto.name && { name: updateDto.name }),
      ...(updateDto.description !== undefined && { description: updateDto.description }),
      ...(updateDto.priority !== undefined && { priority: updateDto.priority }),
      ...(updateDto.assignment_priority !== undefined && { assignmentPriority: updateDto.assignment_priority }),
      ...(updateDto.is_active !== undefined && { isActive: updateDto.is_active }),
      ...(updateDto.metadata && { metadata: updateDto.metadata }),
    });

    this.logger.log(`Risk profile updated successfully: ${riskProfileId}`);
    
    return {
      risk_profile: this.transformToResponseDto(updatedProfile)
    };
  }

  /**
   * Delete risk profile (soft delete)
   */
  async deleteRiskProfile(tenantId: string, riskProfileId: string): Promise<void> {
    const existingProfile = await this.riskProfileRepository.getRiskProfileById(riskProfileId);
    
    if (!existingProfile || existingProfile.tenantId !== tenantId) {
      throw new NotFoundException(`Risk profile with ID ${riskProfileId} not found`);
    }

    if (existingProfile.isSystemGenerated) {
      throw new ConflictException('Cannot delete system-generated risk profiles');
    }

    await this.riskProfileRepository.delete(riskProfileId);
    this.logger.log(`Risk profile deleted successfully: ${riskProfileId}`);
  }

  /**
   * Get system-generated risk profiles
   */
  async getSystemGeneratedProfiles(tenantId: string): Promise<RiskProfileListResponseDto> {
    const profiles = await this.riskProfileRepository.findSystemGeneratedProfiles(tenantId);
    
    return {
      risk_profiles: profiles.map(profile => this.transformToResponseDto(profile)),
      pagination: {
        page: 1,
        limit: profiles.length,
        total: profiles.length,
        total_pages: 1,
        has_next: false,
        has_prev: false,
      }
    };
  }

  /**
   * Get custom risk profiles
   */
  async getCustomProfiles(tenantId: string): Promise<RiskProfileListResponseDto> {
    const profiles = await this.riskProfileRepository.findCustomProfiles(tenantId);
    
    return {
      risk_profiles: profiles.map(profile => this.transformToResponseDto(profile)),
      pagination: {
        page: 1,
        limit: profiles.length,
        total: profiles.length,
        total_pages: 1,
        has_next: false,
        has_prev: false,
      }
    };
  }

  /**
   * Search risk profiles
   */
  async searchRiskProfiles(tenantId: string, searchTerm: string, category?: RuleCategory): Promise<RiskProfileListResponseDto> {
    const profiles = await this.riskProfileRepository.searchRiskProfiles(tenantId, searchTerm);
    
    const filteredProfiles = category 
      ? profiles.filter(profile => profile.ruleCategory === category)
      : profiles;
    
    return {
      risk_profiles: filteredProfiles.map(profile => this.transformToResponseDto(profile)),
      pagination: {
        page: 1,
        limit: filteredProfiles.length,
        total: filteredProfiles.length,
        total_pages: 1,
        has_next: false,
        has_prev: false,
      }
    };
  }

  /**
   * Get risk profile statistics
   */
  async getRiskProfileStats(tenantId: string): Promise<RiskProfileStatsDto> {
    const [
      totalProfiles,
      systemGenerated,
      customProfiles,
      activeProfiles,
      // TODO: Add rule and assignment counts when those services are implemented
    ] = await Promise.all([
      this.riskProfileRepository.countByTenant(tenantId),
      this.riskProfileRepository.findSystemGeneratedProfiles(tenantId).then(profiles => profiles.length),
      this.riskProfileRepository.findCustomProfiles(tenantId).then(profiles => profiles.length),
      this.riskProfileRepository.countActiveByTenant(tenantId),
    ]);

    // Get category breakdown
    const categories = Object.values(RuleCategory);
    const byCategoryPromises = categories.map(async category => {
      const count = await this.riskProfileRepository.countByCategory(tenantId, category);
      return { category, count };
    });
    
    const categoryResults = await Promise.all(byCategoryPromises);
    const byCategory = categoryResults.reduce((acc, { category, count }) => {
      acc[category] = count;
      return acc;
    }, {} as Record<string, number>);

    return {
      total_risk_profiles: totalProfiles,
      system_generated: systemGenerated,
      custom_profiles: customProfiles,
      active_profiles: activeProfiles,
      inactive_profiles: totalProfiles - activeProfiles,
      by_category: byCategory,
      total_rules: 0, // TODO: Implement when rule service is ready
      active_rules: 0, // TODO: Implement when rule service is ready
      total_assignments: 0, // TODO: Implement when assignment service is ready
      active_assignments: 0, // TODO: Implement when assignment service is ready
      by_assignment_type: {}, // TODO: Implement when assignment service is ready
    };
  }

  /**
   * Transform IRiskProfile to RiskProfileResponseDto
   */
  private transformToResponseDto(profile: IRiskProfile): RiskProfileResponseDto {
    return {
      id: profile.id,
      tenant_id: profile.tenantId,
      name: profile.name,
      description: profile.description,
      rule_category: profile.ruleCategory,
      is_active: profile.isActive,
      is_system_generated: profile.isSystemGenerated,
      priority: profile.priority,
      assignment_priority: profile.assignmentPriority,
      metadata: profile.metadata,
      created_at: profile.createdAt.toISOString(),
      updated_at: profile.updatedAt.toISOString(),
      created_by: profile.createdBy,
      // TODO: Add rules_count and customers_count when those relationships are implemented
    };
  }
}
