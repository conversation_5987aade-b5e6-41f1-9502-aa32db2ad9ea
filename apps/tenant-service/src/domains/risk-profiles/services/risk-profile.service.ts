/* eslint-disable @typescript-eslint/no-explicit-any */
import { ConflictException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { createId } from '@paralleldrive/cuid2';
import {
  CreateRiskProfileRequestDto,
  RiskProfileListResponseDto,
  RiskProfileQueryDto,
  RiskProfileResponseDto,
  RiskProfileStatsDto,
  SingleRiskProfileResponseDto,
  UpdateRiskProfileRequestDto,
} from '@qeep/contracts/tenant/dtos';
import { IRiskProfile, RuleCategory } from '@qeep/contracts/tenant/interfaces';
import { RiskProfileRepository } from '../repositories/risk-profile.repository';

@Injectable()
export class RiskProfileService {
  private readonly logger = new Logger(RiskProfileService.name);

  constructor(private readonly riskProfileRepository: RiskProfileRepository) {}

  /**
   * Create a new risk profile
   */
  async createRiskProfile(tenantId: string, createDto: CreateRiskProfileRequestDto): Promise<SingleRiskProfileResponseDto> {
    // Check if risk profile with this name already exists
    const existingProfile = await this.riskProfileRepository.findByName(tenantId, createDto.name);

    if (existingProfile) {
      throw new ConflictException(`Risk profile with name '${createDto.name}' already exists`);
    }

    const riskProfile = await this.riskProfileRepository.create({
      id: `rp_${createId()}`.substring(0, 35),
      tenantId,
      name: createDto.name,
      description: createDto.description,
      ruleCategory: createDto.ruleCategory as any, // Cast to handle enum differences
      isActive: true,
      isSystemGenerated: false,
      priority: createDto.priority || 0,
      assignmentPriority: createDto.assignmentPriority || 0,
      metadata: createDto.metadata || {},
      createdBy: 'USER_CREATED', // TODO: Get from auth context
    } as any); // Cast the entire object to handle type differences

    this.logger.log(`Risk profile created successfully: ${riskProfile.id}`);

    return {
      riskProfile: this.transformToResponseDto(riskProfile as any),
    };
  }

  /**
   * Get all risk profiles for a tenant
   */
  async getAllRiskProfiles(tenantId: string, query: RiskProfileQueryDto): Promise<RiskProfileListResponseDto> {
    const options = {
      where: {
        tenantId,
        ...(query.search && {
          OR: [{ name: { contains: query.search, mode: 'insensitive' } }, { description: { contains: query.search, mode: 'insensitive' } }],
        }),
        ...(query.ruleCategory && { ruleCategory: query.ruleCategory }),
        ...(query.isActive !== undefined && { isActive: query.isActive }),
        ...(query.isSystemGenerated !== undefined && { isSystemGenerated: query.isSystemGenerated }),
      },
      orderBy: {
        [query.sortBy || 'createdAt']: query.sortOrder || 'desc',
      },
      skip: ((query.page || 1) - 1) * (query.limit || 20),
      take: query.limit || 20,
    };

    const [riskProfiles, total] = await Promise.all([this.riskProfileRepository.findRiskProfilesWithOptions(options), this.riskProfileRepository.countByTenant(tenantId)]);

    const totalPages = Math.ceil(total / (query.limit || 20));
    const currentPage = query.page || 1;

    return {
      riskProfiles: riskProfiles.map((profile) => this.transformToResponseDto(profile)),
      pagination: {
        page: currentPage,
        limit: query.limit || 20,
        total,
        totalPages: totalPages,
        hasNext: currentPage < totalPages,
        hasPrev: currentPage > 1,
      },
    };
  }

  /**
   * Get risk profile by ID
   */
  async getRiskProfileById(tenantId: string, riskProfileId: string): Promise<SingleRiskProfileResponseDto> {
    const riskProfile = await this.riskProfileRepository.getRiskProfileById(riskProfileId);

    if (!riskProfile || riskProfile.tenantId !== tenantId) {
      throw new NotFoundException(`Risk profile with ID ${riskProfileId} not found`);
    }

    return {
      riskProfile: this.transformToResponseDto(riskProfile),
    };
  }

  /**
   * Update risk profile
   */
  async updateRiskProfile(tenantId: string, riskProfileId: string, updateDto: UpdateRiskProfileRequestDto): Promise<SingleRiskProfileResponseDto> {
    const existingProfile = await this.riskProfileRepository.getRiskProfileById(riskProfileId);

    if (!existingProfile || existingProfile.tenantId !== tenantId) {
      throw new NotFoundException(`Risk profile with ID ${riskProfileId} not found`);
    }

    // Check for name conflicts if name is being updated
    if (updateDto.name && updateDto.name !== existingProfile.name) {
      const conflictingProfile = await this.riskProfileRepository.findByName(tenantId, updateDto.name);
      if (conflictingProfile) {
        throw new ConflictException(`Risk profile with name '${updateDto.name}' already exists`);
      }
    }

    const updatedProfile = await this.riskProfileRepository.update(riskProfileId, {
      ...(updateDto.name && { name: updateDto.name }),
      ...(updateDto.description !== undefined && { description: updateDto.description }),
      ...(updateDto.priority !== undefined && { priority: updateDto.priority }),
      ...(updateDto.assignmentPriority !== undefined && { assignmentPriority: updateDto.assignmentPriority }),
      ...(updateDto.isActive !== undefined && { isActive: updateDto.isActive }),
      ...(updateDto.metadata && { metadata: updateDto.metadata }),
    });

    this.logger.log(`Risk profile updated successfully: ${riskProfileId}`);

    return {
      riskProfile: this.transformToResponseDto(updatedProfile as any),
    };
  }

  /**
   * Delete risk profile (soft delete)
   */
  async deleteRiskProfile(tenantId: string, riskProfileId: string): Promise<void> {
    const existingProfile = await this.riskProfileRepository.getRiskProfileById(riskProfileId);

    if (!existingProfile || existingProfile.tenantId !== tenantId) {
      throw new NotFoundException(`Risk profile with ID ${riskProfileId} not found`);
    }

    if (existingProfile.isSystemGenerated) {
      throw new ConflictException('Cannot delete system-generated risk profiles');
    }

    await this.riskProfileRepository.delete(riskProfileId);
    this.logger.log(`Risk profile deleted successfully: ${riskProfileId}`);
  }

  /**
   * Get system-generated risk profiles
   */
  async getSystemGeneratedProfiles(tenantId: string): Promise<RiskProfileListResponseDto> {
    const profiles = await this.riskProfileRepository.findSystemGeneratedProfiles(tenantId);

    return {
      riskProfiles: profiles.map((profile) => this.transformToResponseDto(profile)),
      pagination: {
        page: 1,
        limit: profiles.length,
        total: profiles.length,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      },
    };
  }

  /**
   * Get custom risk profiles
   */
  async getCustomProfiles(tenantId: string): Promise<RiskProfileListResponseDto> {
    const profiles = await this.riskProfileRepository.findCustomProfiles(tenantId);

    return {
      riskProfiles: profiles.map((profile) => this.transformToResponseDto(profile)),
      pagination: {
        page: 1,
        limit: profiles.length,
        total: profiles.length,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      },
    };
  }

  /**
   * Search risk profiles
   */
  async searchRiskProfiles(tenantId: string, searchTerm: string, category?: RuleCategory): Promise<RiskProfileListResponseDto> {
    const profiles = await this.riskProfileRepository.searchRiskProfiles(tenantId, searchTerm);

    const filteredProfiles = category ? profiles.filter((profile) => profile.ruleCategory === category) : profiles;

    return {
      riskProfiles: filteredProfiles.map((profile) => this.transformToResponseDto(profile)),
      pagination: {
        page: 1,
        limit: filteredProfiles.length,
        total: filteredProfiles.length,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      },
    };
  }

  /**
   * Get risk profile statistics
   */
  async getRiskProfileStats(tenantId: string): Promise<RiskProfileStatsDto> {
    const [
      totalProfiles,
      systemGenerated,
      customProfiles,
      activeProfiles,
      // TODO: Add rule and assignment counts when those services are implemented
    ] = await Promise.all([
      this.riskProfileRepository.countByTenant(tenantId),
      this.riskProfileRepository.findSystemGeneratedProfiles(tenantId).then((profiles) => profiles.length),
      this.riskProfileRepository.findCustomProfiles(tenantId).then((profiles) => profiles.length),
      this.riskProfileRepository.countActiveByTenant(tenantId),
    ]);

    // Get category breakdown
    const categories = Object.values(RuleCategory);
    const byCategoryPromises = categories.map(async (category) => {
      const count = await this.riskProfileRepository.countByCategory(tenantId, category);
      return { category, count };
    });

    const categoryResults = await Promise.all(byCategoryPromises);
    const byCategory = categoryResults.reduce((acc, { category, count }) => {
      acc[category] = count;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalRiskProfiles: totalProfiles,
      systemGenerated: systemGenerated,
      customProfiles: customProfiles,
      activeProfiles: activeProfiles,
      inactiveProfiles: totalProfiles - activeProfiles,
      byCategory: byCategory,
      totalRules: 0, // TODO: Implement when rule service is ready
      activeRules: 0, // TODO: Implement when rule service is ready
      totalAssignments: 0, // TODO: Implement when assignment service is ready
      activeAssignments: 0, // TODO: Implement when assignment service is ready
      byAssignmentType: {}, // TODO: Implement when assignment service is ready
    };
  }

  /**
   * Transform IRiskProfile to RiskProfileResponseDto
   */
  private transformToResponseDto(profile: IRiskProfile): RiskProfileResponseDto {
    return {
      id: profile.id,
      tenantId: profile.tenantId,
      name: profile.name,
      description: profile.description,
      ruleCategory: profile.ruleCategory,
      isActive: profile.isActive,
      isSystemGenerated: profile.isSystemGenerated,
      priority: profile.priority,
      assignmentPriority: profile.assignmentPriority,
      metadata: profile.metadata,
      createdAt: profile.createdAt.toISOString(),
      updatedAt: profile.updatedAt.toISOString(),
      createdBy: profile.createdBy,
      // TODO: Add rulesCount and customersCount when those relationships are implemented
    };
  }
}
