import { Injectable, Logger, NotFoundException, ConflictException } from '@nestjs/common';
import { generateCuid, CUID_PREFIXES } from '@qeep/common';
import { TenantPrismaService } from '../../../database/prisma.service';
import { KeyGeneratorService } from '../../api-keys/services/key-generator.service';
import {
	CreateWebhookKeyRequestDto,
	CreateWebhookKeyResponseDto,
	ListWebhookKeysQueryDto,
	ListWebhookKeysResponseDto,
	WebhookKeyListItemDto,
	RotateWebhookKeyRequestDto,
	RotateWebhookKeyResponseDto,
	KeyEnvironment,
} from '@qeep/contracts';
// Using generic types instead of specific Prisma types
import * as bcrypt from 'bcryptjs';

@Injectable()
export class WebhookKeysService {
	private readonly logger = new Logger(WebhookKeysService.name);

	constructor(
		private readonly prisma: TenantPrismaService,
		private readonly keyGenerator: KeyGeneratorService,
	) {}

	async createWebhookKey(
		tenantId: string,
		createDto: CreateWebhookKeyRequestDto,
	): Promise<CreateWebhookKeyResponseDto> {
		this.logger.log(`Creating webhook key for tenant ${tenantId}: ${createDto.name}`);

		// Check if webhook key with same name exists for this tenant and environment
		const existingKey = await this.prisma.tenantWebhookKey.findFirst({
			where: {
				tenantId,
				name: createDto.name,
				environment: this.convertToContractEnum(createDto.environment),
			},
		});

		if (existingKey) {
			throw new ConflictException(
				`Webhook key with name '${createDto.name}' already exists for ${createDto.environment} environment`,
			);
		}

		// Generate webhook key
		const webhookKeyObj = this.keyGenerator.generateWebhookKey(createDto.environment);
		const hashedKey = await bcrypt.hash(webhookKeyObj.key, 12);

		// Create webhook key record
		const webhookKeyRecord = await this.prisma.tenantWebhookKey.create({
			data: {
				id: generateCuid(CUID_PREFIXES.TENANT_WEBHOOK_KEY),
				tenantId,
				name: createDto.name,
				// description field is not in the DTO, removing
				environment: this.convertToContractEnum(createDto.environment),
				keyHash: hashedKey,
				keyPrefix: webhookKeyObj.prefix,
				isActive: true,
				expiresAt: createDto.expiresAt ? new Date(createDto.expiresAt) : null,
			},
		});

		this.logger.log(`Created webhook key ${webhookKeyRecord.id} for tenant ${tenantId}`);

		return {
			id: webhookKeyRecord.id,
			name: webhookKeyRecord.name,
			// description field is not in the response type, removing
			environment: this.convertToPrismaEnum(webhookKeyRecord.environment),
			keyPrefix: webhookKeyRecord.keyPrefix,
			key: webhookKeyObj.key, // Only returned on creation
			isActive: webhookKeyRecord.isActive,
			expiresAt: webhookKeyRecord.expiresAt?.toISOString(),
			createdAt: webhookKeyRecord.createdAt.toISOString(),
			// updatedAt field is not in the response type, removing
		};
	}

	async listWebhookKeys(
		tenantId: string,
		queryDto: ListWebhookKeysQueryDto,
	): Promise<ListWebhookKeysResponseDto> {
		this.logger.log(`Listing webhook keys for tenant ${tenantId}`);

		// Define a properly typed where object for Prisma query
		const where: {
			tenantId: string;
			environment?: 'LIVE' | 'TEST';
			isActive?: boolean;
		} = {
			tenantId
		}

		// Apply filters
		if (queryDto.environment) {
			// Convert from KeyEnvironment enum to Prisma expected string enum
			where.environment = this.convertToContractEnum(queryDto.environment);
		}

		if (queryDto.isActive !== undefined) {
			where.isActive = queryDto.isActive;
		}

		// Search property doesn't exist in the DTO, commenting out search functionality
		/*
		if (queryDto.search) {
			where.OR = [
				{ name: { contains: queryDto.search, mode: 'insensitive' } },
			];
		}
		*/

		// Get total count
		const total = await this.prisma.tenantWebhookKey.count({ where });

		// Get paginated results
		const skip = queryDto.page && queryDto.limit ? (queryDto.page - 1) * queryDto.limit : 0;
		const take = queryDto.limit || 10;

		const webhookKeys = await this.prisma.tenantWebhookKey.findMany({
			where,
			skip,
			take,
			orderBy: { createdAt: 'desc' },
		});

		const items: WebhookKeyListItemDto[] = webhookKeys.map((key) => ({
			id: key.id,
			name: key.name,
			// description field removed as it's not in the DTO type
			environment: this.convertToPrismaEnum(key.environment),
			keyPrefix: key.keyPrefix,
			isActive: key.isActive,
			lastUsedAt: key.lastUsedAt?.toISOString(),
			expiresAt: key.expiresAt?.toISOString(),
			createdAt: key.createdAt.toISOString(),
			updatedAt: key.updatedAt.toISOString(),
		}));

		return {
			webhookKeys: items,
			pagination: {
				page: queryDto.page || 1,
				limit: take,
				total,
				totalPages: Math.ceil(total / take),
			},
		};
	}

	async rotateWebhookKey(
		tenantId: string,
		keyId: string,
		rotateDto: RotateWebhookKeyRequestDto,
	): Promise<RotateWebhookKeyResponseDto> {
		this.logger.log(`Rotating webhook key ${keyId} for tenant ${tenantId}`);

		// Find existing webhook key
		const existingKey = await this.prisma.tenantWebhookKey.findFirst({
			where: { id: keyId, tenantId },
		});

		if (!existingKey) {
			throw new NotFoundException(`Webhook key with ID ${keyId} not found`);
		}

		// Generate new webhook key
		const newWebhookKeyObj = this.keyGenerator.generateWebhookKey(
			this.convertToPrismaEnum(existingKey.environment),
		);
		const hashedKey = await bcrypt.hash(newWebhookKeyObj.key, 12);

		// Update webhook key record
		const updatedKey = await this.prisma.tenantWebhookKey.update({
			where: { id: keyId },
			data: {
				keyHash: hashedKey,
				expiresAt: rotateDto.expiresAt ? new Date(rotateDto.expiresAt) : existingKey.expiresAt,
				updatedAt: new Date(),
			},
		});

		this.logger.log(`Rotated webhook key ${keyId} for tenant ${tenantId}`);

		return {
			id: updatedKey.id,
			name: updatedKey.name,
			environment: this.convertToPrismaEnum(updatedKey.environment),
			keyPrefix: updatedKey.keyPrefix,
			key: newWebhookKeyObj.key, // Only returned on rotation
			isActive: updatedKey.isActive,
			expiresAt: updatedKey.expiresAt?.toISOString(),
			createdAt: updatedKey.createdAt.toISOString(),
			rotatedAt: updatedKey.updatedAt.toISOString(),
		};
	}

	async deleteWebhookKey(tenantId: string, keyId: string): Promise<void> {
		this.logger.log(`Deleting webhook key ${keyId} for tenant ${tenantId}`);

		const existingKey = await this.prisma.tenantWebhookKey.findFirst({
			where: { id: keyId, tenantId },
		});

		if (!existingKey) {
			throw new NotFoundException(`Webhook key with ID ${keyId} not found`);
		}

		await this.prisma.tenantWebhookKey.delete({
			where: { id: keyId },
		});

		this.logger.log(`Deleted webhook key ${keyId} for tenant ${tenantId}`);
	}

	async toggleWebhookKeyStatus(tenantId: string, keyId: string): Promise<WebhookKeyListItemDto> {
		this.logger.log(`Toggling webhook key ${keyId} status for tenant ${tenantId}`);

		const existingKey = await this.prisma.tenantWebhookKey.findFirst({
			where: { id: keyId, tenantId },
		});

		if (!existingKey) {
			throw new NotFoundException(`Webhook key with ID ${keyId} not found`);
		}

		const updatedKey = await this.prisma.tenantWebhookKey.update({
			where: { id: keyId },
			data: {
				isActive: !existingKey.isActive,
				updatedAt: new Date(),
			},
		});

		this.logger.log(`Toggled webhook key ${keyId} status to ${updatedKey.isActive} for tenant ${tenantId}`);

		return {
			id: updatedKey.id,
			name: updatedKey.name,
			// description field removed as it's not in the DTO type
			environment: this.convertToPrismaEnum(updatedKey.environment),
			keyPrefix: updatedKey.keyPrefix,
			isActive: updatedKey.isActive,
			lastUsedAt: updatedKey.lastUsedAt?.toISOString(),
			expiresAt: updatedKey.expiresAt?.toISOString(),
			createdAt: updatedKey.createdAt.toISOString(),
			// updatedAt field is not in the response type, removing
		};
	}

	/**
	 * Convert contract enum to Prisma enum
	 */
	private convertToContractEnum(environment: KeyEnvironment): 'LIVE' | 'TEST' {
		return environment === KeyEnvironment.LIVE ? 'LIVE' : 'TEST';
	}

	/**
	 * Convert Prisma enum to contract enum
	 */
	private convertToPrismaEnum(environment: 'LIVE' | 'TEST'): KeyEnvironment {
		return environment === 'LIVE' ? KeyEnvironment.LIVE : KeyEnvironment.TEST;
	}
}
