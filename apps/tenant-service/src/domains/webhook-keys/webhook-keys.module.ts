import { Modu<PERSON> } from '@nestjs/common';
import { WebhookKeysController } from './controllers/webhook-keys.controller';
import { WebhookKeysService } from './services/webhook-keys.service';
import { TenantPrismaService } from '../../database/prisma.service';
import { KeyGeneratorService } from '../api-keys/services/key-generator.service';

@Module({
	controllers: [WebhookKeysController],
	providers: [
		WebhookKeysService,
		KeyGeneratorService,
		TenantPrismaService,
	],
	exports: [WebhookKeysService],
})
export class WebhookKeysModule {}
