import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>, TenantWebhookKey } from '@prisma/tenant-client';
import { CUID_PREFIXES, generateCuid } from '@qeep/common';
import { CreateWebhookKeyData, KeyEnvironment, UpdateWebhookKeyData, WebhookKeyEntity, WebhookKeyFilters } from '@qeep/contracts/tenant';
import { TenantPrismaService } from '../../../database/prisma.service';

@Injectable()
export class WebhookKeysRepository {
  constructor(private readonly prisma: TenantPrismaService) {}

  /**
   * Create a new webhook key
   */
  async create(data: CreateWebhookKeyData): Promise<WebhookKeyEntity> {
    const webhookKey = await this.prisma.tenantWebhookKey.create({
      data: {
        id: generateCuid(CUID_PREFIXES.TENANT_WEBHOOK_KEY),
        tenantId: data.tenantId,
        name: data.name,
        environment: this.convertEnvironmentToPrisma(data.environment),
        keyPrefix: data.keyPrefix,
        keyHash: data.keyHash,
        expiresAt: data.expiresAt || null,
        isActive: data.isActive ?? true,
      },
    });

    return this.mapToEntity(webhookKey);
  }

  /**
   * Find webhook key by ID and tenant ID
   */
  async findById(id: string, tenantId: string): Promise<WebhookKeyEntity | null> {
    const webhookKey = await this.prisma.tenantWebhookKey.findFirst({
      where: { id, tenantId },
    });

    return webhookKey ? this.mapToEntity(webhookKey) : null;
  }

  /**
   * Find webhook key by name, environment, and tenant ID
   */
  async findByName(name: string, environment: KeyEnvironment, tenantId: string): Promise<WebhookKeyEntity | null> {
    const webhookKey = await this.prisma.tenantWebhookKey.findFirst({
      where: {
        name,
        environment: this.convertEnvironmentToPrisma(environment),
        tenantId,
      },
    });

    return webhookKey ? this.mapToEntity(webhookKey) : null;
  }

  /**
   * Find many webhook keys with filters and pagination
   */
  async findMany(params: { filters: WebhookKeyFilters; skip?: number; take?: number; orderBy?: Prisma.TenantWebhookKeyOrderByWithRelationInput }): Promise<WebhookKeyEntity[]> {
    const { filters, skip, take, orderBy } = params;
    const where = this.buildWhereClause(filters);

    const webhookKeys = await this.prisma.tenantWebhookKey.findMany({
      where,
      skip,
      take,
      orderBy: orderBy || { createdAt: 'desc' },
    });

    return webhookKeys.map(this.mapToEntity);
  }

  /**
   * Count webhook keys with filters
   */
  async count(filters: WebhookKeyFilters): Promise<number> {
    const where = this.buildWhereClause(filters);
    return this.prisma.tenantWebhookKey.count({ where });
  }

  /**
   * Update webhook key by ID and tenant ID
   */
  async update(id: string, tenantId: string, data: UpdateWebhookKeyData): Promise<WebhookKeyEntity | null> {
    try {
      const updateData: Prisma.TenantWebhookKeyUpdateInput = {};

      if (data.name !== undefined) updateData.name = data.name;
      if (data.keyPrefix !== undefined) updateData.keyPrefix = data.keyPrefix;
      if (data.keyHash !== undefined) updateData.keyHash = data.keyHash;
      if (data.lastUsedAt !== undefined) updateData.lastUsedAt = data.lastUsedAt;
      if (data.expiresAt !== undefined) updateData.expiresAt = data.expiresAt;
      if (data.isActive !== undefined) updateData.isActive = data.isActive;

      const webhookKey = await this.prisma.tenantWebhookKey.update({
        where: { id },
        data: updateData,
      });

      return this.mapToEntity(webhookKey);
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return null; // Record not found
      }
      throw error;
    }
  }

  /**
   * Delete webhook key by ID and tenant ID
   */
  async delete(id: string, tenantId: string): Promise<boolean> {
    try {
      // Verify the key belongs to the tenant before deletion
      const existingKey = await this.findById(id, tenantId);
      if (!existingKey) {
        return false;
      }

      await this.prisma.tenantWebhookKey.delete({
        where: { id },
      });
      return true;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return false; // Record not found
      }
      throw error;
    }
  }

  /**
   * Build Prisma where clause from filters
   */
  private buildWhereClause(filters: WebhookKeyFilters): Prisma.TenantWebhookKeyWhereInput {
    const where: Prisma.TenantWebhookKeyWhereInput = {
      tenantId: filters.tenantId,
    };

    if (filters.environment) {
      where.environment = this.convertEnvironmentToPrisma(filters.environment);
    }

    if (filters.isActive !== undefined) {
      where.isActive = filters.isActive;
    }

    if (filters.search) {
      where.OR = [{ name: { contains: filters.search, mode: 'insensitive' } }];
    }

    return where;
  }

  /**
   * Map Prisma model to entity interface
   */
  private mapToEntity(webhookKey: TenantWebhookKey): WebhookKeyEntity {
    return {
      id: webhookKey.id,
      tenantId: webhookKey.tenantId,
      name: webhookKey.name,
      environment: this.convertEnvironmentToContract(webhookKey.environment),
      keyPrefix: webhookKey.keyPrefix,
      keyHash: webhookKey.keyHash,
      lastUsedAt: webhookKey.lastUsedAt,
      expiresAt: webhookKey.expiresAt,
      isActive: webhookKey.isActive,
      createdAt: webhookKey.createdAt,
      updatedAt: webhookKey.updatedAt,
    };
  }

  /**
   * Convert contract enum to Prisma enum
   */
  private convertEnvironmentToPrisma(environment: KeyEnvironment): 'LIVE' | 'TEST' {
    return environment === KeyEnvironment.LIVE ? 'LIVE' : 'TEST';
  }

  /**
   * Convert Prisma enum to contract enum
   */
  private convertEnvironmentToContract(environment: 'LIVE' | 'TEST'): KeyEnvironment {
    return environment === 'LIVE' ? KeyEnvironment.LIVE : KeyEnvironment.TEST;
  }
}
