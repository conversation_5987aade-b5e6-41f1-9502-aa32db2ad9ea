import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Logger, Param, Post, Put, Query } from '@nestjs/common';
import { ResponseUtil, SnakeToCamelPipe, ZodValidationPipe } from '@qeep/common';
import {
  CreateWebhookKeyRequestDto,
  CreateWebhookKeySchema,
  ListWebhookKeysQueryDto,
  ListWebhookKeysSchema,
  RotateWebhookKeyRequestDto,
  RotateWebhookKeySchema,
} from '@qeep/contracts/tenant';
import { WebhookKeysService } from '../services/webhook-keys.service';

@Controller('tenants/:tenantId/webhook-keys')
export class WebhookKeysController {
  private readonly logger = new Logger(WebhookKeysController.name);

  constructor(private readonly webhookKeysService: WebhookKeysService) {}

  /**
   * Create a new webhook key for a tenant
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  async createWebhookKey(@Param('tenantId') tenantId: string, @Body(new SnakeToCamelPipe(), new ZodValidationPipe(CreateWebhookKeySchema)) createDto: CreateWebhookKeyRequestDto) {
    this.logger.log(`Creating webhook key for tenant ${tenantId}: ${createDto.name}`);

    try {
      const webhookKey = await this.webhookKeysService.createWebhookKey(tenantId, createDto);

      return ResponseUtil.success(webhookKey, 'Webhook key created successfully', HttpStatus.CREATED);
    } catch (error) {
      this.logger.error(`Failed to create webhook key for tenant ${tenantId}:`, error);
      throw error;
    }
  }

  /**
   * List webhook keys for a tenant with optional filtering and pagination
   */
  @Get()
  async listWebhookKeys(@Param('tenantId') tenantId: string, @Query(new SnakeToCamelPipe(), new ZodValidationPipe(ListWebhookKeysSchema)) queryDto: ListWebhookKeysQueryDto) {
    this.logger.log(`Listing webhook keys for tenant ${tenantId}`);

    try {
      const result = await this.webhookKeysService.listWebhookKeys(tenantId, queryDto);

      return ResponseUtil.success(result, 'Webhook keys retrieved successfully');
    } catch (error) {
      this.logger.error(`Failed to list webhook keys for tenant ${tenantId}:`, error);
      throw error;
    }
  }

  /**
   * Rotate an existing webhook key (generate new key)
   */
  @Put(':keyId/rotate')
  async rotateWebhookKey(
    @Param('tenantId') tenantId: string,
    @Param('keyId') keyId: string,
    @Body(new SnakeToCamelPipe(), new ZodValidationPipe(RotateWebhookKeySchema)) rotateDto: RotateWebhookKeyRequestDto,
  ) {
    this.logger.log(`Rotating webhook key ${keyId} for tenant ${tenantId}`);

    try {
      const webhookKey = await this.webhookKeysService.rotateWebhookKey(tenantId, keyId, rotateDto);

      return ResponseUtil.success(webhookKey, 'Webhook key rotated successfully');
    } catch (error) {
      this.logger.error(`Failed to rotate webhook key ${keyId} for tenant ${tenantId}:`, error);
      throw error;
    }
  }

  /**
   * Delete a webhook key
   */
  @Delete(':keyId')
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteWebhookKey(@Param('tenantId') tenantId: string, @Param('keyId') keyId: string) {
    this.logger.log(`Deleting webhook key ${keyId} for tenant ${tenantId}`);

    try {
      await this.webhookKeysService.deleteWebhookKey(tenantId, keyId);

      return ResponseUtil.success(null, 'Webhook key deleted successfully', HttpStatus.NO_CONTENT);
    } catch (error) {
      this.logger.error(`Failed to delete webhook key ${keyId} for tenant ${tenantId}:`, error);
      throw error;
    }
  }

  /**
   * Toggle webhook key active status
   */
  @Put(':keyId/toggle')
  async toggleWebhookKeyStatus(@Param('tenantId') tenantId: string, @Param('keyId') keyId: string) {
    this.logger.log(`Toggling webhook key ${keyId} status for tenant ${tenantId}`);

    try {
      const webhookKey = await this.webhookKeysService.toggleWebhookKeyStatus(tenantId, keyId);

      return ResponseUtil.success(webhookKey, 'Webhook key status updated successfully');
    } catch (error) {
      this.logger.error(`Failed to toggle webhook key ${keyId} status for tenant ${tenantId}:`, error);
      throw error;
    }
  }
}
