import { Modu<PERSON> } from '@nestjs/common';
import { ApiKeysController } from './controllers/api-keys.controller';
import { ApiKeysService } from './services/api-keys.service';
import { KeyGeneratorService } from './services/key-generator.service';
import { TenantPrismaService } from '../../database/prisma.service';

@Module({
  controllers: [ApiKeysController],
  providers: [
    ApiKeysService,
    KeyGeneratorService,
    TenantPrismaService,
  ],
  exports: [
    ApiKeysService,
    KeyGeneratorService,
  ],
})
export class ApiKeysModule {}
