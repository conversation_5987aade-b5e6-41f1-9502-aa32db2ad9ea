import { Injectable } from '@nestjs/common';
import { KeyEnvironment } from '@qeep/contracts/tenant';
import { randomBytes } from 'crypto';

@Injectable()
export class KeyGeneratorService {
  /**
   * Generate a new API key with the appropriate prefix
   */
  generateApiKey(environment: KeyEnvironment): { key: string; prefix: string } {
    const prefix = environment === KeyEnvironment.LIVE ? 'qeep_live_' : 'qeep_test_';
    const secret = randomBytes(32).toString('hex');
    const key = `${prefix}${secret}`;

    return { key, prefix };
  }

  /**
   * Generate a new webhook key with the appropriate prefix
   */
  generateWebhookKey(environment: KeyEnvironment): { key: string; prefix: string } {
    const prefix = environment === KeyEnvironment.LIVE ? 'whk_live_' : 'whk_test_';
    const secret = randomBytes(32).toString('hex');
    const key = `${prefix}${secret}`;

    return { key, prefix };
  }

  /**
   * Extract tenant ID from API key (for validation)
   */
  extractKeyInfo(key: string): { prefix: string; environment: KeyEnvironment | null } {
    if (key.startsWith('qeep_live_')) {
      return { prefix: 'qeep_live_', environment: KeyEnvironment.LIVE };
    }
    if (key.startsWith('qeep_test_')) {
      return { prefix: 'qeep_test_', environment: KeyEnvironment.TEST };
    }
    if (key.startsWith('whk_live_')) {
      return { prefix: 'whk_live_', environment: KeyEnvironment.LIVE };
    }
    if (key.startsWith('whk_test_')) {
      return { prefix: 'whk_test_', environment: KeyEnvironment.TEST };
    }

    return { prefix: '', environment: null };
  }
}
