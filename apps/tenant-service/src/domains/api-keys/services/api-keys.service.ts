import { ConflictException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { KeyEnvironment } from '@prisma/tenant-client';
import { generateCuid, CUID_PREFIXES } from '@qeep/common';
import {
  KeyEnvironment as ContractKeyEnvironment,
  CreateApiKeyRequestDto,
  CreateApiKeyResponseDto,
  ListApiKeysQueryDto,
  ListApiKeysResponseDto,
  RotateApiKeyRequestDto,
  RotateApiKeyResponseDto,
} from '@qeep/contracts';
import { compare, hash } from 'bcryptjs';
import { TenantPrismaService } from '../../../database/prisma.service';
import { KeyGeneratorService } from './key-generator.service';

@Injectable()
export class ApiKeysService {
  private readonly logger = new Logger(ApiKeysService.name);

  /**
   * Convert contract KeyEnvironment to Prisma KeyEnvironment
   */
  private convertEnvironment(env: ContractKeyEnvironment): KeyEnvironment {
    return env as unknown as KeyEnvironment;
  }

  /**
   * Convert Prisma KeyEnvironment to contract KeyEnvironment
   */
  private convertEnvironmentToContract(env: KeyEnvironment): ContractKeyEnvironment {
    return env as unknown as ContractKeyEnvironment;
  }

  constructor(private readonly prisma: TenantPrismaService, private readonly keyGenerator: KeyGeneratorService) {}

  /**
   * Create a new API key for a tenant
   */
  async createApiKey(tenantId: string, createDto: CreateApiKeyRequestDto): Promise<CreateApiKeyResponseDto> {
    this.logger.log(`Creating API key for tenant ${tenantId}: ${createDto.name}`);

    // Generate the API key
    const { key, prefix } = this.keyGenerator.generateApiKey(createDto.environment);

    // Hash the key for storage
    const keyHash = await hash(key, 10);

    // Generate unique ID
    const keyId = generateCuid(CUID_PREFIXES.TENANT_API_KEY);

    try {
      // Create the API key in database
      const apiKey = await this.prisma.tenantApiKey.create({
        data: {
          id: keyId,
          tenantId,
          name: createDto.name,
          environment: this.convertEnvironment(createDto.environment),
          keyPrefix: prefix,
          keyHash,
          expiresAt: createDto.expiresAt ? new Date(createDto.expiresAt) : null,
        },
      });

      this.logger.log(`API key created successfully: ${keyId}`);

      return {
        id: apiKey.id,
        name: apiKey.name,
        environment: this.convertEnvironmentToContract(apiKey.environment),
        key, // Only returned on creation
        keyPrefix: apiKey.keyPrefix,
        isActive: apiKey.isActive,
        createdAt: apiKey.createdAt.toISOString(),
        expiresAt: apiKey.expiresAt?.toISOString(),
        lastUsedAt: apiKey.lastUsedAt?.toISOString(),
      };
    } catch (error) {
      this.logger.error(`Failed to create API key: ${error.message}`);
      throw new ConflictException('Failed to create API key');
    }
  }

  /**
   * List API keys for a tenant
   */
  async listApiKeys(tenantId: string, queryDto: ListApiKeysQueryDto): Promise<ListApiKeysResponseDto> {
    this.logger.log(`Listing API keys for tenant ${tenantId}`);

    const { environment, isActive, page = 1, limit = 20 } = queryDto;
    const skip = (page - 1) * limit;

    const where = {
      tenantId,
      ...(environment && { environment: this.convertEnvironment(environment) }),
      ...(isActive !== undefined && { isActive }),
    };

    const [apiKeys, total] = await Promise.all([
      this.prisma.tenantApiKey.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.tenantApiKey.count({ where }),
    ]);

    const data = apiKeys.map((key) => ({
      id: key.id,
      name: key.name,
      environment: this.convertEnvironmentToContract(key.environment),
      keyPrefix: key.keyPrefix,
      isActive: key.isActive,
      createdAt: key.createdAt.toISOString(),
      expiresAt: key.expiresAt?.toISOString(),
      lastUsedAt: key.lastUsedAt?.toISOString(),
    }));

    return {
      apiKeys: data,
      pagination: {
        page: queryDto.page || 1,
        limit: limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Rotate an API key
   */
  async rotateApiKey(tenantId: string, keyId: string, rotateDto: RotateApiKeyRequestDto): Promise<RotateApiKeyResponseDto> {
    this.logger.log(`Rotating API key ${keyId} for tenant ${tenantId}`);

    // Find the existing key
    const existingKey = await this.prisma.tenantApiKey.findFirst({
      where: { id: keyId, tenantId },
    });

    if (!existingKey) {
      throw new NotFoundException('API key not found');
    }

    // Generate new key
    const { key } = this.keyGenerator.generateApiKey(this.convertEnvironmentToContract(existingKey.environment));
    const keyHash = await hash(key, 10);

    try {
      // Update the key
      const updatedKey = await this.prisma.tenantApiKey.update({
        where: { id: keyId },
        data: {
          keyHash,
          expiresAt: rotateDto.expiresAt ? new Date(rotateDto.expiresAt) : existingKey.expiresAt,
          updatedAt: new Date(),
        },
      });

      this.logger.log(`API key rotated successfully: ${keyId}`);

      return {
        id: updatedKey.id,
        name: updatedKey.name,
        environment: this.convertEnvironmentToContract(updatedKey.environment),
        key, // Only returned on rotation
        keyPrefix: updatedKey.keyPrefix,
        isActive: updatedKey.isActive,
        createdAt: updatedKey.createdAt.toISOString(),
        rotatedAt: updatedKey.updatedAt.toISOString(),
        expiresAt: updatedKey.expiresAt?.toISOString(),
      };
    } catch (error) {
      this.logger.error(`Failed to rotate API key: ${error.message}`);
      throw new ConflictException('Failed to rotate API key');
    }
  }

  /**
   * Delete an API key
   */
  async deleteApiKey(tenantId: string, keyId: string): Promise<void> {
    this.logger.log(`Deleting API key ${keyId} for tenant ${tenantId}`);

    const existingKey = await this.prisma.tenantApiKey.findFirst({
      where: { id: keyId, tenantId },
    });

    if (!existingKey) {
      throw new NotFoundException('API key not found');
    }

    try {
      await this.prisma.tenantApiKey.delete({
        where: { id: keyId },
      });

      this.logger.log(`API key deleted successfully: ${keyId}`);
    } catch (error) {
      this.logger.error(`Failed to delete API key: ${error.message}`);
      throw new ConflictException('Failed to delete API key');
    }
  }

  /**
   * Toggle API key active status
   */
  async toggleApiKey(tenantId: string, keyId: string): Promise<void> {
    this.logger.log(`Toggling API key ${keyId} for tenant ${tenantId}`);

    const existingKey = await this.prisma.tenantApiKey.findFirst({
      where: { id: keyId, tenantId },
    });

    if (!existingKey) {
      throw new NotFoundException('API key not found');
    }

    try {
      await this.prisma.tenantApiKey.update({
        where: { id: keyId },
        data: { isActive: !existingKey.isActive },
      });

      this.logger.log(`API key toggled successfully: ${keyId}`);
    } catch (error) {
      this.logger.error(`Failed to toggle API key: ${error.message}`);
      throw new ConflictException('Failed to toggle API key');
    }
  }

  /**
   * Validate an API key
   */
  async validateApiKey(key: string): Promise<{ tenantId: string; keyId: string } | null> {
    try {
      // Find all active API keys and check against hash
      const apiKeys = await this.prisma.tenantApiKey.findMany({
        where: {
          isActive: true,
          OR: [{ expiresAt: null }, { expiresAt: { gt: new Date() } }],
        },
      });

      for (const apiKey of apiKeys) {
        const isValid = await compare(key, apiKey.keyHash);
        if (isValid) {
          // Update last used timestamp
          await this.prisma.tenantApiKey.update({
            where: { id: apiKey.id },
            data: { lastUsedAt: new Date() },
          });

          return {
            tenantId: apiKey.tenantId,
            keyId: apiKey.id,
          };
        }
      }

      return null;
    } catch (error) {
      this.logger.error(`Failed to validate API key: ${error.message}`);
      return null;
    }
  }
}
