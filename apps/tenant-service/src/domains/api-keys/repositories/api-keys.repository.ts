import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>, Tenant<PERSON><PERSON><PERSON><PERSON> } from '@prisma/tenant-client';
import { CUID_PREFIXES, generateCuid } from '@qeep/common';
import { ApiKeyEntity, ApiKeyFilters, CreateApiKeyData, KeyEnvironment, UpdateApiKeyData } from '@qeep/contracts/tenant';
import { TenantPrismaService } from '../../../database/prisma.service';

@Injectable()
export class ApiKeysRepository {
  constructor(private readonly prisma: TenantPrismaService) {}

  /**
   * Create a new API key
   */
  async create(data: CreateApiKeyData): Promise<ApiKeyEntity> {
    const apiKey = await this.prisma.tenantApiKey.create({
      data: {
        id: generateCuid(CUID_PREFIXES.TENANT_API_KEY),
        tenantId: data.tenantId,
        name: data.name,
        environment: this.convertEnvironmentToPrisma(data.environment),
        keyPrefix: data.keyPrefix,
        keyHash: data.keyHash,
        expiresAt: data.expiresAt || null,
        isActive: data.isActive ?? true,
      },
    });

    return this.mapToEntity(apiKey);
  }

  /**
   * Find API key by ID and tenant ID
   */
  async findById(id: string, tenantId: string): Promise<ApiKeyEntity | null> {
    const apiKey = await this.prisma.tenantApiKey.findFirst({
      where: { id, tenantId },
    });

    return apiKey ? this.mapToEntity(apiKey) : null;
  }

  /**
   * Find API key by name, environment, and tenant ID
   */
  async findByName(name: string, environment: KeyEnvironment, tenantId: string): Promise<ApiKeyEntity | null> {
    const apiKey = await this.prisma.tenantApiKey.findFirst({
      where: {
        name,
        environment: this.convertEnvironmentToPrisma(environment),
        tenantId,
      },
    });

    return apiKey ? this.mapToEntity(apiKey) : null;
  }

  /**
   * Find many API keys with filters and pagination
   */
  async findMany(params: { filters: ApiKeyFilters; skip?: number; take?: number; orderBy?: Prisma.TenantApiKeyOrderByWithRelationInput }): Promise<ApiKeyEntity[]> {
    const { filters, skip, take, orderBy } = params;
    const where = this.buildWhereClause(filters);

    const apiKeys = await this.prisma.tenantApiKey.findMany({
      where,
      skip,
      take,
      orderBy: orderBy || { createdAt: 'desc' },
    });

    return apiKeys.map(this.mapToEntity);
  }

  /**
   * Count API keys with filters
   */
  async count(filters: ApiKeyFilters): Promise<number> {
    const where = this.buildWhereClause(filters);
    return this.prisma.tenantApiKey.count({ where });
  }

  /**
   * Update API key by ID and tenant ID
   */
  async update(id: string, tenantId: string, data: UpdateApiKeyData): Promise<ApiKeyEntity | null> {
    try {
      const updateData: Prisma.TenantApiKeyUpdateInput = {};

      if (data.name !== undefined) updateData.name = data.name;
      if (data.keyPrefix !== undefined) updateData.keyPrefix = data.keyPrefix;
      if (data.keyHash !== undefined) updateData.keyHash = data.keyHash;
      if (data.lastUsedAt !== undefined) updateData.lastUsedAt = data.lastUsedAt;
      if (data.expiresAt !== undefined) updateData.expiresAt = data.expiresAt;
      if (data.isActive !== undefined) updateData.isActive = data.isActive;

      const apiKey = await this.prisma.tenantApiKey.update({
        where: { id },
        data: updateData,
      });

      return this.mapToEntity(apiKey);
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return null; // Record not found
      }
      throw error;
    }
  }

  /**
   * Delete API key by ID and tenant ID
   */
  async delete(id: string, tenantId: string): Promise<boolean> {
    try {
      // Verify the key belongs to the tenant before deletion
      const existingKey = await this.findById(id, tenantId);
      if (!existingKey) {
        return false;
      }

      await this.prisma.tenantApiKey.delete({
        where: { id },
      });
      return true;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return false; // Record not found
      }
      throw error;
    }
  }

  /**
   * Build Prisma where clause from filters
   */
  private buildWhereClause(filters: ApiKeyFilters): Prisma.TenantApiKeyWhereInput {
    const where: Prisma.TenantApiKeyWhereInput = {
      tenantId: filters.tenantId,
    };

    if (filters.environment) {
      where.environment = this.convertEnvironmentToPrisma(filters.environment);
    }

    if (filters.isActive !== undefined) {
      where.isActive = filters.isActive;
    }

    if (filters.search) {
      where.OR = [{ name: { contains: filters.search, mode: 'insensitive' } }];
    }

    return where;
  }

  /**
   * Map Prisma model to entity interface
   */
  private mapToEntity(apiKey: TenantApiKey): ApiKeyEntity {
    return {
      id: apiKey.id,
      tenantId: apiKey.tenantId,
      name: apiKey.name,
      environment: this.convertEnvironmentToContract(apiKey.environment),
      keyPrefix: apiKey.keyPrefix,
      keyHash: apiKey.keyHash,
      lastUsedAt: apiKey.lastUsedAt,
      expiresAt: apiKey.expiresAt,
      isActive: apiKey.isActive,
      createdAt: apiKey.createdAt,
      updatedAt: apiKey.updatedAt,
    };
  }

  /**
   * Convert contract enum to Prisma enum
   */
  private convertEnvironmentToPrisma(environment: KeyEnvironment): 'LIVE' | 'TEST' {
    return environment === KeyEnvironment.LIVE ? 'LIVE' : 'TEST';
  }

  /**
   * Convert Prisma enum to contract enum
   */
  private convertEnvironmentToContract(environment: 'LIVE' | 'TEST'): KeyEnvironment {
    return environment === 'LIVE' ? KeyEnvironment.LIVE : KeyEnvironment.TEST;
  }
}
