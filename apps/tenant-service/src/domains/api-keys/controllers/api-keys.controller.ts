import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Logger, Param, Post, Put, Query } from '@nestjs/common';
import { ResponseUtil, SnakeToCamelPipe, ZodValidationPipe } from '@qeep/common';
import { CreateApiKeyRequestDto, CreateApiKeySchema, ListApiKeysQueryDto, ListApiKeysSchema, RotateApiKeyRequestDto, RotateApiKeySchema } from '@qeep/contracts/tenant';
import { ApiKeysService } from '../services/api-keys.service';

@Controller('tenants/:tenantId/api-keys')
export class ApiKeysController {
  private readonly logger = new Logger(ApiKeysController.name);

  constructor(private readonly apiKeysService: ApiKeysService) {}

  /**
   * Create a new API key for a tenant
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  async createApiKey(@Param('tenantId') tenantId: string, @Body(new SnakeToCamelPipe(), new ZodValidationPipe(CreateApiKeySchema)) createDto: CreateApiKeyRequestDto) {
    this.logger.log(`Creating API key for tenant ${tenantId}: ${createDto.name}`);

    try {
      const result = await this.apiKeysService.createApiKey(tenantId, createDto);

      return ResponseUtil.success(result, 'API key created successfully', HttpStatus.CREATED);
    } catch (error) {
      this.logger.error(`Failed to create API key: ${error.message}`);
      throw error;
    }
  }

  /**
   * List API keys for a tenant
   */
  @Get()
  async listApiKeys(@Param('tenantId') tenantId: string, @Query(new SnakeToCamelPipe(), new ZodValidationPipe(ListApiKeysSchema)) queryDto: ListApiKeysQueryDto) {
    this.logger.log(`Listing API keys for tenant ${tenantId}`);

    try {
      const result = await this.apiKeysService.listApiKeys(tenantId, queryDto);

      return ResponseUtil.success(result, 'API keys retrieved successfully');
    } catch (error) {
      this.logger.error(`Failed to list API keys: ${error.message}`);
      throw error;
    }
  }

  /**
   * Rotate an API key
   */
  @Put(':keyId/rotate')
  async rotateApiKey(
    @Param('tenantId') tenantId: string,
    @Param('keyId') keyId: string,
    @Body(new SnakeToCamelPipe(), new ZodValidationPipe(RotateApiKeySchema)) rotateDto: RotateApiKeyRequestDto,
  ) {
    this.logger.log(`Rotating API key ${keyId} for tenant ${tenantId}`);

    try {
      const result = await this.apiKeysService.rotateApiKey(tenantId, keyId, rotateDto);

      return ResponseUtil.success(result, 'API key rotated successfully');
    } catch (error) {
      this.logger.error(`Failed to rotate API key: ${error.message}`);
      throw error;
    }
  }

  /**
   * Delete an API key
   */
  @Delete(':keyId')
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteApiKey(@Param('tenantId') tenantId: string, @Param('keyId') keyId: string) {
    this.logger.log(`Deleting API key ${keyId} for tenant ${tenantId}`);

    try {
      await this.apiKeysService.deleteApiKey(tenantId, keyId);

      return ResponseUtil.success(null, 'API key deleted successfully', HttpStatus.NO_CONTENT);
    } catch (error) {
      this.logger.error(`Failed to delete API key: ${error.message}`);
      throw error;
    }
  }

  /**
   * Toggle API key active status
   */
  @Put(':keyId/toggle')
  async toggleApiKey(@Param('tenantId') tenantId: string, @Param('keyId') keyId: string) {
    this.logger.log(`Toggling API key ${keyId} for tenant ${tenantId}`);

    try {
      await this.apiKeysService.toggleApiKey(tenantId, keyId);

      return ResponseUtil.success(null, 'API key status toggled successfully');
    } catch (error) {
      this.logger.error(`Failed to toggle API key: ${error.message}`);
      throw error;
    }
  }
}
