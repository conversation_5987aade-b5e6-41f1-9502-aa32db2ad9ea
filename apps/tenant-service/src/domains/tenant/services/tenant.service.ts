/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ConflictException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { TenantStatus } from '@prisma/tenant-client';
import { generateTenantId, CUID_PREFIXES } from '@qeep/common';
import { TenantRepository } from '../repositories/tenant.repository';

@Injectable()
export class TenantService {
  private readonly logger = new Logger(TenantService.name);

  constructor(private readonly tenantRepository: TenantRepository) {}

  async createTenant(createTenantDto: any) {
    const { name, code, settings } = createTenantDto;

    // Check if tenant with this code already exists
    const existingTenant = await this.tenantRepository.findByCode(code);

    if (existingTenant) {
      throw new ConflictException(`Tenant with code '${code}' already exists`);
    }

    const tenant = await this.tenantRepository.create({
      id: generateTenantId(),
      name,
      code,
      status: 'ACTIVE',
      settings: settings || {},
    });

    this.logger.log(`Tenant created successfully with ID: ${tenant.id}`);
    return tenant;
  }

  async getTenantById(tenantId: string): Promise<any> {
    const tenant = await this.tenantRepository.findById(tenantId);

    if (!tenant) {
      throw new NotFoundException(`Tenant with ID ${tenantId} not found`);
    }

    return tenant;
  }

  async getTenantByCode(code: string): Promise<any> {
    const tenant = await this.tenantRepository.findByCode(code);

    if (!tenant) {
      throw new NotFoundException('Tenant not found');
    }

    return tenant;
  }

  async validateTenantCode(code: string): Promise<{ valid: boolean; tenant?: any }> {
    try {
      const tenant = await this.getTenantByCode(code);

      if (tenant.status !== 'ACTIVE') {
        this.logger.warn(`Tenant code ${code} is not active: ${tenant.status}`);
        return { valid: false };
      }

      this.logger.log(`Tenant code ${code} is valid`);

      return { valid: true, tenant };
    } catch (error) {
      this.logger.warn(`Tenant code validation failed: ${code}`);
      return { valid: false };
    }
  }

  async updateTenant(id: string, updateTenantDto: any) {
    const { name, settings } = updateTenantDto;

    // Check if tenant exists
    const existingTenant = await this.tenantRepository.findById(id);

    if (!existingTenant) {
      throw new NotFoundException(`Tenant with ID ${id} not found`);
    }

    const tenant = await this.tenantRepository.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(settings && { settings }),
      },
    });

    this.logger.log(`Tenant updated successfully: ${id}`);
    return tenant;
  }

  async getAllTenants(query?: any) {
    if (!query) {
      // Legacy behavior - return all tenants
      this.logger.log('Getting all tenants (legacy)');
      return this.tenantRepository.findMany({
        orderBy: { createdAt: 'desc' },
      });
    }

    const { page = 1, limit = 10, search, status, sortBy = 'createdAt', sortOrder = 'desc' } = query;

    const skip = (page - 1) * limit;
    const take = Math.min(limit, 100); // Max 100 items per page

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [{ name: { contains: search, mode: 'insensitive' } }, { code: { contains: search, mode: 'insensitive' } }];
    }

    if (status) {
      // Map lowercase status to uppercase enum values
      const statusMap: Record<string, string> = {
        active: 'ACTIVE',
        inactive: 'INACTIVE',
        suspended: 'SUSPENDED',
      };
      where.status = statusMap[status] || status;
    }

    // Get total count
    const total = await this.tenantRepository.count(where);

    // Get tenants
    const tenants = await this.tenantRepository.findMany({
      where,
      skip,
      take,
      orderBy: { [sortBy]: sortOrder },
      select: {
        id: true,
        name: true,
        code: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        settings: true,
      },
    });

    const totalPages = Math.ceil(total / take);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return {
      tenants,
      pagination: {
        page,
        limit: take,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
    };
  }

  async getActiveTenants(): Promise<any[]> {
    this.logger.log('Getting active tenants');
    return this.tenantRepository.findActive();
  }

  async deleteTenant(id: string) {
    // Check if tenant exists
    const tenant = await this.tenantRepository.findById(id);

    if (!tenant) {
      throw new NotFoundException(`Tenant with ID ${id} not found`);
    }

    await this.tenantRepository.delete({ id });

    this.logger.log(`Tenant deleted successfully: ${id}`);
    return {
      id,
      message: 'Tenant deleted successfully',
    };
  }

  async updateTenantStatus(id: string, status: TenantStatus | string) {
    // Check if tenant exists
    const existingTenant = await this.tenantRepository.findById(id);

    if (!existingTenant) {
      throw new NotFoundException(`Tenant with ID ${id} not found`);
    }

    // Handle status mapping if needed
    let mappedStatus = status;
    if (typeof status === 'string') {
      const statusMap: Record<string, string> = {
        active: 'ACTIVE',
        inactive: 'INACTIVE',
        suspended: 'SUSPENDED',
      };
      mappedStatus = statusMap[status] || status;
    }

    const tenant = await this.tenantRepository.updateStatus(id, mappedStatus as string);

    this.logger.log(`Tenant status updated successfully: ${id} -> ${mappedStatus}`);
    return tenant;
  }

  async getTenantStats(id: string) {
    // Check if tenant exists
    const tenant = await this.tenantRepository.findById(id);

    if (!tenant) {
      throw new NotFoundException(`Tenant with ID ${id} not found`);
    }

    // Get tenant statistics
    const stats = await this.tenantRepository.findMany({
      where: { id },
      select: {
        id: true,
        name: true,
        code: true,
        status: true,
        createdAt: true,
      },
    });

    return stats[0];
  }

  async getOverviewStats() {
    const overview = await this.tenantRepository.getOverviewStats();
    const recentTenants = await this.tenantRepository.getRecentTenants(5);

    return {
      overview,
      recentTenants,
    };
  }
}
