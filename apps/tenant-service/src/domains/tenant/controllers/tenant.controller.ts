import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, ParseUUIDPipe, Post, Put, Query, UseGuards, UseInterceptors } from '@nestjs/common';
import {
  JwtAuthGuard,
  PlatformPermission,
  PlatformRole,
  RequirePermissions,
  RequireRoles,
  ResponseFormatInterceptor,
  TenantPermission,
  TenantRole,
  ZodValidationPipe,
} from '@qeep/common';
import {
  TenantCreateRequestDto,
  TenantCreateRequestSchema,
  TenantQueryRequestDto,
  TenantStatusUpdateRequestDto,
  TenantStatusUpdateRequestSchema,
  TenantUpdateRequestDto,
  TenantUpdateRequestSchema,
} from '@qeep/contracts/tenant';

import { TenantService } from '../services/tenant.service';

@Controller('tenants')
@UseGuards(JwtAuthGuard) // TODO: Add RbacGuard when RBAC is fully implemented
@UseInterceptors(ResponseFormatInterceptor)
export class TenantController {
  constructor(private readonly tenantService: TenantService) {}

  @Get()
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR, TenantRole.TENANT_ADMIN)
  async getAllTenants(@Query() query: TenantQueryRequestDto) {
    // Use direct mapping for transformation
    const serviceQuery = {
      page: query.page,
      limit: query.limit,
      search: query.search,
      status: query.status?.toLowerCase() as 'active' | 'inactive' | 'suspended' | undefined,
      sortBy: query.sort_by ? (query.sort_by === 'created_at' ? 'created_at' : query.sort_by === 'updated_at' ? 'updated_at' : query.sort_by) : undefined,
      sortOrder: query.sort_order,
    };

    const result = await this.tenantService.getAllTenants(serviceQuery);

    return result;
  }

  @Get(':id')
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR, TenantRole.TENANT_ADMIN)
  async getTenantById(@Param('id', ParseUUIDPipe) id: string) {
    return this.tenantService.getTenantById(id);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR)
  @RequirePermissions(PlatformPermission.TENANT_CREATE)
  async createTenant(@Body(new ZodValidationPipe(TenantCreateRequestSchema)) createTenantDto: TenantCreateRequestDto) {
    // Direct mapping for transformation
    const internalData = {
      name: createTenantDto.name,
      code: createTenantDto.code,
      description: createTenantDto.description,
      domain: createTenantDto.domain,
      logoUrl: createTenantDto.logo_url,
      websiteUrl: createTenantDto.website_url,
      plan: createTenantDto.plan,
      maxUsers: createTenantDto.max_users,
      settings: createTenantDto.settings,
      metadata: createTenantDto.metadata,
    };

    const result = await this.tenantService.createTenant(internalData);

    return result;
  }

  @Put(':id')
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR, TenantRole.TENANT_ADMIN)
  @RequirePermissions(PlatformPermission.TENANT_UPDATE_ALL, TenantPermission.TENANT_CONFIG_UPDATE)
  async updateTenant(@Param('id', ParseUUIDPipe) id: string, @Body(new ZodValidationPipe(TenantUpdateRequestSchema)) updateTenantDto: TenantUpdateRequestDto) {
    // Direct mapping for transformation
    const internalData = {
      name: updateTenantDto.name,
      description: updateTenantDto.description,
      domain: updateTenantDto.domain,
      logoUrl: updateTenantDto.logo_url,
      websiteUrl: updateTenantDto.website_url,
      plan: updateTenantDto.plan,
      maxUsers: updateTenantDto.max_users,
      settings: updateTenantDto.settings,
      metadata: updateTenantDto.metadata,
    };

    const result = await this.tenantService.updateTenant(id, internalData);

    return result;
  }

  @Delete(':id')
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR)
  @RequirePermissions(PlatformPermission.TENANT_DELETE)
  async deleteTenant(@Param('id', ParseUUIDPipe) id: string) {
    return this.tenantService.deleteTenant(id);
  }

  @Put(':id/status')
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR)
  @RequirePermissions(PlatformPermission.TENANT_SUSPEND, PlatformPermission.TENANT_ACTIVATE)
  async updateTenantStatus(@Param('id', ParseUUIDPipe) id: string, @Body(new ZodValidationPipe(TenantStatusUpdateRequestSchema)) statusUpdate: TenantStatusUpdateRequestDto) {
    // Direct access to status field
    const status = statusUpdate.status.toLowerCase() as 'active' | 'inactive' | 'suspended';
    const result = await this.tenantService.updateTenantStatus(id, status);

    return result;
  }

  @Get(':id/stats')
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR, TenantRole.TENANT_ADMIN)
  async getTenantStats(@Param('id', ParseUUIDPipe) id: string) {
    return this.tenantService.getTenantStats(id);
  }

  @Get('stats/overview')
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR)
  @RequirePermissions(PlatformPermission.PLATFORM_ANALYTICS_READ)
  async getOverviewStats() {
    return this.tenantService.getOverviewStats();
  }
}
