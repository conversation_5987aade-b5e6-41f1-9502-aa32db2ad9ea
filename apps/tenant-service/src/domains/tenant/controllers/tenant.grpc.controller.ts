/* eslint-disable @typescript-eslint/no-explicit-any */
import { <PERSON>, Logger } from '@nestjs/common';
import {
  CreateTenantRequest,
  CreateTenantResponse,
  GetTenantByCodeRequest,
  GetTenantByCodeResponse,
  GetTenantRequest,
  GetTenantResponse,
  TenantTenant,
  TenantTenantStatus,
  UpdateTenantRequest,
  UpdateTenantResponse,
  ValidateTenantCodeRequest,
  ValidateTenantCodeResponse,
} from '@qeep/proto';

import { GrpcMethod } from '@nestjs/microservices';
import { TenantService } from '../services/tenant.service';

@Controller()
export class TenantGrpcController {
  private readonly logger = new Logger(TenantGrpcController.name);

  constructor(private readonly tenantService: TenantService) {}

  @GrpcMethod('TenantService', 'CreateTenant')
  async createTenant(request: CreateTenantRequest): Promise<CreateTenantResponse> {
    this.logger.log(`gRPC CreateTenant request for code: ${request.code}`);

    try {
      const createDto = {
        code: request.code,
        name: request.name,
      };
      const tenant = await this.tenantService.createTenant(createDto);

      return {
        success: true,
        tenant: this.mapTenantToProto(tenant),
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`CreateTenant failed: ${error.message}`, error.stack);
      return {
        success: false,
        tenant: undefined,
        error: {
          code: 'TENANT_CREATION_FAILED',
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }

  @GrpcMethod('TenantService', 'GetTenant')
  async getTenant(request: GetTenantRequest): Promise<GetTenantResponse> {
    this.logger.log(`gRPC GetTenant request for ID: ${request.tenantId}`);

    try {
      const tenant = await this.tenantService.getTenantById(request.tenantId);

      return {
        success: true,
        tenant: this.mapTenantToProto(tenant),
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`GetTenant failed: ${error.message}`, error.stack);
      return {
        success: false,
        tenant: undefined,
        error: {
          code: 'TENANT_NOT_FOUND',
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }

  @GrpcMethod('TenantService', 'GetTenantByCode')
  async getTenantByCode(request: GetTenantByCodeRequest): Promise<GetTenantByCodeResponse> {
    this.logger.log(`gRPC GetTenantByCode request for code: ${request.code}`);

    try {
      const tenant = await this.tenantService.getTenantByCode(request.code);

      return {
        success: true,
        tenant: this.mapTenantToProto(tenant),
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`GetTenantByCode failed: ${error.message}`, error.stack);
      return {
        success: false,
        tenant: undefined,
        error: {
          code: 'TENANT_NOT_FOUND',
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }

  @GrpcMethod('TenantService', 'ValidateTenantCode')
  async validateTenantCode(request: ValidateTenantCodeRequest): Promise<ValidateTenantCodeResponse> {
    this.logger.log(`gRPC ValidateTenantCode request for code: ${request}`);

    try {
      const result = await this.tenantService.validateTenantCode(request.code);

      return {
        valid: result.valid,
        tenant: result.tenant ? this.mapTenantToProto(result.tenant) : undefined,
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`ValidateTenantCode failed: ${error.message}`, error.stack);
      return {
        valid: false,
        tenant: undefined,
        error: {
          code: 'TENANT_VALIDATION_FAILED',
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }

  @GrpcMethod('TenantService', 'UpdateTenant')
  async updateTenant(request: UpdateTenantRequest): Promise<UpdateTenantResponse> {
    this.logger.log(`gRPC UpdateTenant request for ID: ${request.tenantId}`);

    try {
      const updateDto = {
        name: request.name,
      };
      const tenant = await this.tenantService.updateTenant(request.tenantId, updateDto);

      return {
        success: true,
        tenant: this.mapTenantToProto(tenant),
        error: undefined,
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    } catch (error) {
      this.logger.error(`UpdateTenant failed: ${error.message}`, error.stack);
      return {
        success: false,
        tenant: undefined,
        error: {
          code: 'TENANT_UPDATE_FAILED',
          message: error.message,
          details: {},
          traceId: request.metadata?.requestId || '',
        },
        metadata: {
          requestId: request.metadata?.requestId || '',
          timestamp: new Date(),
          processingTime: 0,
        },
      };
    }
  }

  private mapTenantToProto(tenant: any): TenantTenant {
    return {
      id: tenant.id,
      code: tenant.code,
      name: tenant.name,
      description: tenant.description || '',
      status: this.mapEntityStatusToProto(tenant.status),
      createdAt: tenant.createdAt,
      updatedAt: tenant.updatedAt,
    };
  }

  private mapEntityStatusToProto(status: string): TenantTenantStatus {
    switch (status) {
      case 'ACTIVE':
        return TenantTenantStatus.TENANT_STATUS_ACTIVE;
      case 'INACTIVE':
        return TenantTenantStatus.TENANT_STATUS_INACTIVE;
      case 'SUSPENDED':
        return TenantTenantStatus.TENANT_STATUS_SUSPENDED;
      case 'TRIAL':
        return TenantTenantStatus.TENANT_STATUS_TRIAL;
      default:
        return TenantTenantStatus.TENANT_STATUS_UNKNOWN;
    }
  }

  private mapProtoStatusToEntity(status: TenantTenantStatus): 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'TRIAL' {
    switch (status) {
      case TenantTenantStatus.TENANT_STATUS_ACTIVE:
        return 'ACTIVE';
      case TenantTenantStatus.TENANT_STATUS_INACTIVE:
        return 'INACTIVE';
      case TenantTenantStatus.TENANT_STATUS_SUSPENDED:
        return 'SUSPENDED';
      case TenantTenantStatus.TENANT_STATUS_TRIAL:
        return 'TRIAL';
      default:
        return 'TRIAL';
    }
  }
}
