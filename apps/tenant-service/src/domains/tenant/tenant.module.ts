import { Modu<PERSON> } from '@nestjs/common';
import { TenantPrismaService } from '../../database/prisma.service';
import { TenantController } from './controllers/tenant.controller';
import { TenantGrpcController } from './controllers/tenant.grpc.controller';
import { TenantRepository } from './repositories/tenant.repository';
import { TenantService } from './services/tenant.service';

@Module({
  controllers: [TenantController, TenantGrpcController],
  providers: [TenantService, TenantRepository, TenantPrismaService],
  exports: [TenantService, TenantRepository, TenantPrismaService],
})
export class TenantModule {}
