/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable } from '@nestjs/common';
import { <PERSON>risma, Tenant } from '@prisma/tenant-client';
import { BaseRepository, IBaseRepository } from '@qeep/common';
import { ITenant } from '@qeep/contracts/tenant/interfaces';
import { TenantPrismaService } from '../../../database/prisma.service';

/**
 * Configuration for tenant transformation options
 */
interface TenantTransformOptions {
  includeApiKeys?: boolean;
  includeWebhooks?: boolean;
  includeOnboarding?: boolean;
}

/**
 * Tenant Repository Interface
 * Defines all tenant-specific data access operations
 */
export interface ITenantRepository extends IBaseRepository<Tenant, Prisma.TenantCreateInput, Prisma.TenantUpdateInput> {
  // Tenant-specific methods that return ITenant types
  getTenantById(id: string): Promise<ITenant | null>;
  getAllTenants(options?: any): Promise<ITenant[]>;

  // Core tenant operations
  findByCode(code: string): Promise<ITenant | null>;
  findByDomain(domain: string): Promise<ITenant | null>;

  // Search and filtering
  searchTenants(searchTerm: string): Promise<ITenant[]>;
  getRecentTenants(limit?: number): Promise<ITenant[]>;

  // Business logic queries
  findActiveTenants(): Promise<ITenant[]>;
  findTenantsByPlan(plan: string): Promise<ITenant[]>;
  findTenantsByStatus(status: string): Promise<ITenant[]>;

  // Validation operations
  existsByCode(code: string, excludeId?: string): Promise<boolean>;
  existsByDomain(domain: string, excludeId?: string): Promise<boolean>;
}

@Injectable()
export class TenantRepository extends BaseRepository<Tenant, Prisma.TenantCreateInput, Prisma.TenantUpdateInput> implements ITenantRepository {
  constructor(private readonly tenantPrisma: TenantPrismaService) {
    super(tenantPrisma);
  }

  protected get model() {
    return this.tenantPrisma.tenant;
  }

  /**
   * Transform Prisma Tenant to ITenant interface with configurable relationships
   */
  private transformToInterface(tenant: Tenant | any, options?: TenantTransformOptions): ITenant {
    const baseTenant = {
      ...tenant,
      status: tenant.status as any,
      plan: tenant.plan as any,
      settings: tenant.settings as any,
      metadata: tenant.metadata as any,
    };

    // Remove relationships if they exist but weren't requested
    if ('apiKeys' in tenant && !options?.includeApiKeys) {
      const { apiKeys, ...tenantWithoutApiKeys } = baseTenant;
      return tenantWithoutApiKeys;
    }

    return baseTenant;
  }

  /**
   * Get tenant by ID returning ITenant
   */
  async getTenantById(id: string): Promise<ITenant | null> {
    try {
      const tenant = await this.model.findUnique({
        where: { id },
      });
      return tenant ? this.transformToInterface(tenant) : null;
    } catch (error) {
      throw new Error(`Failed to find tenant by ID ${id}: ${error.message}`);
    }
  }

  /**
   * Get all tenants returning ITenant[]
   */
  async getAllTenants(options?: any): Promise<ITenant[]> {
    try {
      const tenants = await this.model.findMany(options);
      return tenants.map((tenant: Tenant) => this.transformToInterface(tenant));
    } catch (error) {
      throw new Error(`Failed to find tenants: ${error.message}`);
    }
  }

  async findByCode(code: string): Promise<ITenant | null> {
    try {
      const tenant = await this.model.findUnique({
        where: { code },
      });
      return tenant ? this.transformToInterface(tenant) : null;
    } catch (error) {
      throw new Error(`Failed to find tenant by code ${code}: ${error.message}`);
    }
  }

  async findByDomain(domain: string): Promise<ITenant | null> {
    try {
      // Since domain is not a unique field in the schema, use findFirst instead
      const tenant = await this.model.findFirst({
        where: {
          settings: {
            path: ['domain'],
            equals: domain,
          },
        },
      });
      return tenant ? this.transformToInterface(tenant) : null;
    } catch (error) {
      throw new Error(`Failed to find tenant by domain ${domain}: ${error.message}`);
    }
  }

  async searchTenants(searchTerm: string): Promise<ITenant[]> {
    try {
      const tenants = await this.model.findMany({
        where: {
          AND: [
            { deletedAt: null },
            {
              OR: [{ name: { contains: searchTerm, mode: 'insensitive' } }, { code: { contains: searchTerm, mode: 'insensitive' } }],
            },
          ],
        },
        orderBy: { createdAt: 'desc' },
      });
      return tenants.map((tenant: Tenant) => this.transformToInterface(tenant));
    } catch (error) {
      throw new Error(`Failed to search tenants: ${error.message}`);
    }
  }

  async getRecentTenants(limit = 5): Promise<ITenant[]> {
    try {
      const tenants = await this.model.findMany({
        take: limit,
        where: { deletedAt: null },
        orderBy: { createdAt: 'desc' },
      });
      return tenants.map((tenant: Tenant) => this.transformToInterface(tenant));
    } catch (error) {
      throw new Error(`Failed to get recent tenants: ${error.message}`);
    }
  }

  async findActiveTenants(): Promise<ITenant[]> {
    try {
      const tenants = await this.model.findMany({
        where: {
          status: 'ACTIVE',
          deletedAt: null,
        },
        orderBy: { name: 'asc' },
      });
      return tenants.map((tenant: Tenant) => this.transformToInterface(tenant));
    } catch (error) {
      throw new Error(`Failed to find active tenants: ${error.message}`);
    }
  }

  async findTenantsByPlan(plan: string): Promise<ITenant[]> {
    try {
      const tenants = await this.model.findMany({
        where: {
          plan: plan as any,
          deletedAt: null,
        },
        orderBy: { name: 'asc' },
      });
      return tenants.map((tenant: Tenant) => this.transformToInterface(tenant));
    } catch (error) {
      throw new Error(`Failed to find tenants by plan ${plan}: ${error.message}`);
    }
  }

  async findTenantsByStatus(status: string): Promise<ITenant[]> {
    try {
      const tenants = await this.model.findMany({
        where: {
          status: status as any,
          deletedAt: null,
        },
        orderBy: { name: 'asc' },
      });
      return tenants.map((tenant: Tenant) => this.transformToInterface(tenant));
    } catch (error) {
      throw new Error(`Failed to find tenants by status ${status}: ${error.message}`);
    }
  }

  async existsByCode(code: string, excludeId?: string): Promise<boolean> {
    try {
      const where: Prisma.TenantWhereInput = {
        code,
        deletedAt: null,
      };

      if (excludeId) {
        where.id = { not: excludeId };
      }

      const count = await this.count(where);
      return count > 0;
    } catch (error) {
      throw new Error(`Failed to check if tenant exists by code: ${error.message}`);
    }
  }

  async existsByDomain(domain: string, excludeId?: string): Promise<boolean> {
    try {
      const where: Prisma.TenantWhereInput = {
        settings: {
          path: ['domain'],
          equals: domain,
        },
        deletedAt: null,
      };

      if (excludeId) {
        where.id = { not: excludeId };
      }

      const count = await this.count(where);
      return count > 0;
    } catch (error) {
      throw new Error(`Failed to check if tenant exists by domain: ${error.message}`);
    }
  }

  async getOverviewStats(): Promise<{
    totalTenants: number;
    activeTenants: number;
    inactiveTenants: number;
    suspendedTenants: number;
    pendingTenants: number;
  }> {
    const [totalTenants, activeTenants, inactiveTenants, suspendedTenants, pendingTenants] = await Promise.all([
      this.count({ deletedAt: null }),
      this.count({ status: 'ACTIVE', deletedAt: null }),
      this.count({ status: 'INACTIVE', deletedAt: null }),
      this.count({ status: 'SUSPENDED', deletedAt: null }),
      this.count({ status: 'PENDING', deletedAt: null }),
    ]);

    return {
      totalTenants,
      activeTenants,
      inactiveTenants,
      suspendedTenants,
      pendingTenants,
    };
  }

  async updateStatus(id: string, status: string): Promise<ITenant> {
    try {
      const tenant = await this.model.update({
        where: { id },
        data: { status },
      });
      return this.transformToInterface(tenant);
    } catch (error) {
      throw new Error(`Failed to update tenant status: ${error.message}`);
    }
  }

  async updateSettings(id: string, settings: any): Promise<ITenant> {
    try {
      const tenant = await this.model.update({
        where: { id },
        data: { settings },
      });
      return this.transformToInterface(tenant);
    } catch (error) {
      throw new Error(`Failed to update tenant settings: ${error.message}`);
    }
  }
}
