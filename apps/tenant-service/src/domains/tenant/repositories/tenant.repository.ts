import { <PERSON>rism<PERSON>, Tenant } from '@prisma/tenant-client';

import { Injectable } from '@nestjs/common';
import { TenantPrismaService } from '../../../database/prisma.service';

@Injectable()
export class TenantRepository {
  constructor(private readonly prisma: TenantPrismaService) {}

  async create(data: Prisma.TenantCreateInput): Promise<Tenant> {
    return this.prisma.tenant.create({ data });
  }

  async findById(id: string): Promise<Tenant | null> {
    return this.prisma.tenant.findUnique({
      where: { id },
    });
  }

  async findByCode(code: string): Promise<Tenant | null> {
    return this.prisma.tenant.findUnique({
      where: { code },
    });
  }

  async findMany(params: {
    skip?: number;
    take?: number;
    where?: Prisma.TenantWhereInput;
    orderBy?: Prisma.TenantOrderByWithRelationInput;
    select?: Prisma.TenantSelect;
  }): Promise<Tenant[]> {
    const { skip, take, where, orderBy, select } = params;
    return this.prisma.tenant.findMany({
      skip,
      take,
      where,
      orderBy,
      select,
    });
  }

  async count(where?: Prisma.TenantWhereInput): Promise<number> {
    return this.prisma.tenant.count({ where });
  }

  async update(params: { where: Prisma.TenantWhereUniqueInput; data: Prisma.TenantUpdateInput }): Promise<Tenant> {
    const { where, data } = params;
    return this.prisma.tenant.update({
      where,
      data,
    });
  }

  async delete(where: Prisma.TenantWhereUniqueInput): Promise<Tenant> {
    return this.prisma.tenant.delete({ where });
  }

  async softDelete(id: string): Promise<Tenant> {
    return this.prisma.tenant.update({
      where: { id },
      data: {
        status: 'DELETED',
        deletedAt: new Date(),
      },
    });
  }

  async findActive(): Promise<Tenant[]> {
    return this.prisma.tenant.findMany({
      where: {
        status: 'ACTIVE',
        deletedAt: null,
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findByStatus(status: string): Promise<Tenant[]> {
    return this.prisma.tenant.findMany({
      where: {
        status,
        deletedAt: null,
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async getOverviewStats(): Promise<{
    totalTenants: number;
    activeTenants: number;
    inactiveTenants: number;
    suspendedTenants: number;
    pendingTenants: number;
  }> {
    const [totalTenants, activeTenants, inactiveTenants, suspendedTenants, pendingTenants] = await Promise.all([
      this.count({ deletedAt: null }),
      this.count({ status: 'ACTIVE', deletedAt: null }),
      this.count({ status: 'INACTIVE', deletedAt: null }),
      this.count({ status: 'SUSPENDED', deletedAt: null }),
      this.count({ status: 'PENDING', deletedAt: null }),
    ]);

    return {
      totalTenants,
      activeTenants,
      inactiveTenants,
      suspendedTenants,
      pendingTenants,
    };
  }

  async getRecentTenants(limit = 5): Promise<Tenant[]> {
    return this.prisma.tenant.findMany({
      take: limit,
      where: { deletedAt: null },
      orderBy: { createdAt: 'desc' },
    });
  }

  async searchTenants(searchTerm: string): Promise<Tenant[]> {
    return this.prisma.tenant.findMany({
      where: {
        AND: [
          { deletedAt: null },
          {
            OR: [{ name: { contains: searchTerm, mode: 'insensitive' } }, { code: { contains: searchTerm, mode: 'insensitive' } }],
          },
        ],
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async existsByCode(code: string, excludeId?: string): Promise<boolean> {
    const where: Prisma.TenantWhereInput = {
      code,
      deletedAt: null,
    };

    if (excludeId) {
      where.id = { not: excludeId };
    }

    const count = await this.count(where);
    return count > 0;
  }

  async updateStatus(id: string, status: string): Promise<Tenant> {
    return this.prisma.tenant.update({
      where: { id },
      data: { status },
    });
  }

  async updateSettings(id: string, settings: any): Promise<Tenant> {
    return this.prisma.tenant.update({
      where: { id },
      data: { settings },
    });
  }
}
