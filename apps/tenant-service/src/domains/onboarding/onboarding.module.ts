import { Modu<PERSON> } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigService, ProtoConfigService } from '@qeep/common';
import { TenantPrismaService } from '../../database/prisma.service';
import { OnboardingController } from './controllers/onboarding.controller';
import { OnboardingGrpcController } from './controllers/onboarding.grpc.controller';
import { NotificationService } from './services/notification.service';
import { OnboardingService } from './services/onboarding.service';

/**
 * Onboarding Module
 *
 * Handles tenant onboarding workflows including HTTP and gRPC endpoints.
 * Provides onboarding initiation, progress tracking, and workflow management.
 */
@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: 'NOTIFICATION_PACKAGE',
        imports: [],
        useFactory: (configService: ConfigService, protoConfigService: ProtoConfigService) => ({
          transport: Transport.GRPC,
          options: {
            package: 'notification',
            protoPath: protoConfigService.getProtoPath('notification', 'notification.proto'),
            url: configService.getServiceUrl('notification-service-grpc'),
            loader: protoConfigService.getLoaderOptions(),
            channelOptions: protoConfigService.getChannelOptions(),
          },
        }),
        inject: [ConfigService, ProtoConfigService],
      },
    ]),
  ],
  controllers: [OnboardingController, OnboardingGrpcController],
  providers: [OnboardingService, NotificationService, TenantPrismaService],
  exports: [OnboardingService],
})
export class OnboardingModule {}
