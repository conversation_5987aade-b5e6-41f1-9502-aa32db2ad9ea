/* eslint-disable @typescript-eslint/no-explicit-any */
import { BadRequestException, Body, Controller, Get, HttpCode, HttpStatus, Logger, Param, Post, Put, UseGuards } from '@nestjs/common';
import { JwtAuthGuard, PlatformRole, RequireRoles, SnakeToCamelPipe, StandardApiResponse, ZodValidationPipe } from '@qeep/common';
import {
  CompleteOnboardingRequestSchema,
  EnvironmentSwitchRequestSchema,
  OnboardingInitiateRequestSchema,
  RegenerateCredentialsRequestSchema,
  TaskUpdateRequestSchema,
} from '@qeep/contracts/tenant';
import { OnboardingService } from '../services/onboarding.service';

/**
 * Onboarding Controller
 *
 * Handles HTTP endpoints for tenant onboarding workflows.
 * Requires platform administrator authentication for all endpoints.
 */
@UseGuards(JwtAuthGuard)
@Controller('/tenants/onboarding')
export class OnboardingController {
  private readonly logger = new Logger(OnboardingController.name);

  constructor(private readonly onboardingService: OnboardingService) {}

  /**
   * Initiate onboarding for a new financial institution
   *
   * @param requestBody - Onboarding initiation data
   * @returns Onboarding workflow details
   */
  @Post('initiate')
  @HttpCode(HttpStatus.CREATED)
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR)
  async initiateOnboarding(
    @Body(new SnakeToCamelPipe(), new ZodValidationPipe(OnboardingInitiateRequestSchema))
    onboardingRequestDto: any,
  ): Promise<StandardApiResponse<any>> {
    try {
      // Call service to initiate onboarding
      const result = await this.onboardingService.initiateOnboarding(onboardingRequestDto);

      return {
        success: true,
        statusCode: HttpStatus.CREATED,
        message: 'Onboarding initiated successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Onboarding failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get onboarding workflow status and progress
   *
   * @param workflowId - Workflow unique identifier
   * @returns Workflow status and progress information
   */
  @Get(':workflowId/status')
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR)
  async getWorkflowStatus(@Param('workflowId') workflowId: string): Promise<StandardApiResponse<any>> {
    this.logger.log(`Getting workflow status for: ${workflowId}`);

    try {
      const result = await this.onboardingService.getWorkflowStatus(workflowId);

      return {
        success: true,
        statusCode: HttpStatus.OK,
        message: 'Workflow status retrieved successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to get workflow status for ${workflowId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update individual task status
   *
   * @param workflowId - Workflow unique identifier
   * @param taskId - Task unique identifier
   * @param requestBody - Task update data
   * @returns Updated task information
   */
  @Put(':workflowId/tasks/:taskId')
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR)
  async updateTaskStatus(
    @Param('workflowId') workflowId: string,
    @Param('taskId') taskId: string,
    @Body(new SnakeToCamelPipe(), new ZodValidationPipe(TaskUpdateRequestSchema)) taskUpdateDto: any,
  ): Promise<StandardApiResponse<any>> {
    this.logger.log(`Updating task ${taskId} for workflow ${workflowId}`);

    try {
      const result = await this.onboardingService.updateTaskStatus(workflowId, taskId, taskUpdateDto);

      return {
        success: true,
        statusCode: HttpStatus.OK,
        message: 'Task updated successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to update task ${taskId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get all tasks for a workflow
   *
   * @param workflowId - Workflow unique identifier
   * @returns List of all tasks with their current status
   */
  @Get(':workflowId/tasks')
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR)
  async getWorkflowTasks(@Param('workflowId') workflowId: string): Promise<StandardApiResponse<any>> {
    this.logger.log(`Getting tasks for workflow: ${workflowId}`);

    try {
      const result = await this.onboardingService.getWorkflowTasks(workflowId);

      return {
        success: true,
        statusCode: HttpStatus.OK,
        message: 'Tasks retrieved successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to get tasks for workflow ${workflowId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Complete the onboarding process
   *
   * @param workflowId - Workflow unique identifier
   * @param requestBody - Completion request data
   * @returns Completion status and credentials information
   */
  @Post(':workflowId/complete')
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR)
  async completeOnboarding(
    @Param('workflowId') workflowId: string,
    @Body(new SnakeToCamelPipe(), new ZodValidationPipe(CompleteOnboardingRequestSchema))
    completeRequestDto: any,
  ): Promise<StandardApiResponse<any>> {
    this.logger.log(`Completing onboarding for workflow: ${workflowId}`);

    try {
      const result = await this.onboardingService.completeOnboarding(workflowId, completeRequestDto);

      return {
        success: true,
        statusCode: HttpStatus.OK,
        message: 'Onboarding completed successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to complete onboarding for ${workflowId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get API credentials for a tenant
   *
   * @param workflowId - Workflow unique identifier
   * @returns API keys and webhook secrets for both test and live environments
   */
  @Get(':workflowId/credentials')
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR)
  async getApiCredentials(@Param('workflowId') workflowId: string): Promise<StandardApiResponse<any>> {
    this.logger.log(`Getting API credentials for workflow: ${workflowId}`);

    try {
      const result = await this.onboardingService.getApiCredentials(workflowId);

      return {
        success: true,
        statusCode: HttpStatus.OK,
        message: 'API credentials retrieved successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to get credentials for workflow ${workflowId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Regenerate API keys or webhook secrets
   *
   * @param workflowId - Workflow unique identifier
   * @param requestBody - Regeneration request data
   * @returns Updated API credentials
   */
  @Post(':workflowId/credentials/regenerate')
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR)
  async regenerateCredentials(
    @Param('workflowId') workflowId: string,
    @Body(new SnakeToCamelPipe(), new ZodValidationPipe(RegenerateCredentialsRequestSchema))
    regenerateRequestDto: any,
  ): Promise<StandardApiResponse<any>> {
    this.logger.log(`Regenerating credentials for workflow: ${workflowId}`);

    try {
      const result = await this.onboardingService.regenerateCredentials(workflowId, regenerateRequestDto);

      return {
        success: true,
        statusCode: HttpStatus.OK,
        message: 'Credentials regenerated successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to regenerate credentials for ${workflowId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Switch between test and live environments
   *
   * @param workflowId - Workflow unique identifier
   * @param env - Environment to switch (test or live)
   * @param requestBody - Environment switch request data
   * @returns Environment switch status
   */
  @Put(':workflowId/environment/:env')
  @RequireRoles(PlatformRole.PLATFORM_ADMINISTRATOR)
  async switchEnvironment(
    @Param('workflowId') workflowId: string,
    @Param('env') env: string,
    @Body(new SnakeToCamelPipe(), new ZodValidationPipe(EnvironmentSwitchRequestSchema))
    switchRequestDto: any,
  ): Promise<StandardApiResponse<any>> {
    this.logger.log(`Switching ${env} environment for workflow: ${workflowId}`);

    try {
      // Validate environment parameter
      if (env !== 'test' && env !== 'live') {
        throw new BadRequestException('Environment must be either "test" or "live"');
      }

      const result = await this.onboardingService.switchEnvironment(workflowId, env as 'test' | 'live', switchRequestDto);

      return {
        success: true,
        statusCode: HttpStatus.OK,
        message: 'Environment switched successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Failed to switch environment for ${workflowId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Health check endpoint for onboarding service
   */
  @Post('health')
  async health(): Promise<StandardApiResponse<{ status: string; timestamp: string }>> {
    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: 'Onboarding service is healthy',
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
      },
    };
  }
}
