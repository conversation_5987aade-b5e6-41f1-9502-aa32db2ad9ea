/* eslint-disable @typescript-eslint/no-explicit-any */
import { Controller, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { OnboardingService } from '../services/onboarding.service';

/**
 * Onboarding gRPC Controller
 *
 * Handles gRPC endpoints for internal service-to-service communication
 * for tenant onboarding workflows.
 */
@Controller()
export class OnboardingGrpcController {
  private readonly logger = new Logger(OnboardingGrpcController.name);

  constructor(private readonly onboardingService: OnboardingService) {}

  /**
   * gRPC method for initiating onboarding
   * Used for internal service communication
   */
  @GrpcMethod('OnboardingService', 'InitiateOnboarding')
  async initiateOnboarding(data: any) {
    this.logger.log(`gRPC: Initiating onboarding for institution: ${data.institutionName}`);

    try {
      const workflow = await this.onboardingService.initiateOnboarding(data);

      return {
        success: true,
        message: 'Onboarding initiated successfully via gRPC',
        data: workflow,
      };
    } catch (error) {
      this.logger.error(`gRPC: Failed to initiate onboarding:`, error);
      throw error;
    }
  }

  /**
   * gRPC health check method
   */
  @GrpcMethod('OnboardingService', 'HealthCheck')
  async healthCheck() {
    return {
      success: true,
      message: 'Onboarding gRPC service is healthy',
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
      },
    };
  }
}
