import { Injectable, Logger } from '@nestjs/common';
import { ClientProxy, ClientProxyFactory, Transport } from '@nestjs/microservices';
import { ProtoConfigService } from '@qeep/common';
import { firstValueFrom } from 'rxjs';

/**
 * Notification Service Client
 *
 * Handles communication with the notification service for sending emails
 * and other notifications related to onboarding workflows.
 */
@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);
  private readonly client: ClientProxy | null;

  constructor(private readonly protoConfigService: ProtoConfigService) {
    // Create gRPC client for notification service (optional for testing)
    try {
      this.client = ClientProxyFactory.create({
        transport: Transport.GRPC,
        options: {
          package: 'notification',
          protoPath: this.protoConfigService.getProtoPath('notification', 'notification.proto'),
          url: process.env.NOTIFICATION_SERVICE_URL || 'localhost:3015',
          loader: this.protoConfigService.getLoaderOptions(),
          channelOptions: this.protoConfigService.getChannelOptions(),
        },
      });
    } catch (error) {
      this.logger.warn('Failed to initialize notification service client. Email notifications will be disabled.', error.message);
      this.client = null;
    }
  }

  /**
   * Send welcome email for onboarding
   */
  async sendOnboardingWelcomeEmail(data: {
    email: string;
    institutionName: string;
    contactName: string;
    workflowId: string;
    tenantCode: string;
  }): Promise<{ success: boolean; notificationId?: string; error?: string }> {
    this.logger.log(`Sending onboarding welcome email to ${data.email} for ${data.institutionName}`);

    // If notification client is not available, simulate success for testing
    if (!this.client) {
      this.logger.warn('Notification service client not available. Simulating email send for testing.');
      return {
        success: true,
        notificationId: `simulated-${Date.now()}`,
      };
    }

    try {
      const emailRequest = {
        recipientEmail: data.email,
        recipientName: data.contactName,
        subject: `Welcome to Qeep - ${data.institutionName} Onboarding Started`,
        templateSlug: 'onboarding-welcome-email',
        templateData: {
          contactName: data.contactName,
          institutionName: data.institutionName,
          workflowId: data.workflowId,
          tenantCode: data.tenantCode,
          loginUrl: 'https://app.qeep.com/login',
          supportEmail: '<EMAIL>',
          currentYear: new Date().getFullYear().toString(),
          onboardingPortalUrl: `https://app.qeep.com/onboarding/${data.workflowId}`,
          estimatedCompletionDays: '21',
        },
        tenantCode: data.tenantCode,
        metadata: {
          requestId: `onboarding-${data.workflowId}`,
          workflowId: data.workflowId,
          tenantCode: data.tenantCode,
          institutionName: data.institutionName,
          timestamp: new Date(),
          type: 'onboarding_welcome',
        },
      };

      // Send email via gRPC to notification service
      const response = await firstValueFrom(this.client.send('sendEmail', emailRequest));

      if (response.success) {
        this.logger.log(`Onboarding welcome email sent successfully to ${data.email}: ${response.notificationId}`);
        return {
          success: true,
          notificationId: response.notificationId,
        };
      } else {
        this.logger.error(`Failed to send onboarding welcome email to ${data.email}: ${response.error}`);
        return {
          success: false,
          error: response.error || 'Unknown error occurred',
        };
      }
    } catch (error) {
      this.logger.error(`Error sending onboarding welcome email to ${data.email}: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Send onboarding progress update email
   */
  async sendOnboardingProgressEmail(data: {
    email: string;
    institutionName: string;
    contactName: string;
    workflowId: string;
    tenantCode: string;
    completedTasks: number;
    totalTasks: number;
    nextSteps: string[];
  }): Promise<{ success: boolean; notificationId?: string; error?: string }> {
    this.logger.log(`Sending onboarding progress email to ${data.email} for ${data.institutionName}`);

    try {
      const progressPercentage = Math.round((data.completedTasks / data.totalTasks) * 100);

      const emailRequest = {
        recipientEmail: data.email,
        recipientName: data.contactName,
        subject: `Onboarding Progress Update - ${data.institutionName} (${progressPercentage}% Complete)`,
        templateSlug: 'onboarding-progress-email',
        templateData: {
          contactName: data.contactName,
          institutionName: data.institutionName,
          workflowId: data.workflowId,
          tenantCode: data.tenantCode,
          completedTasks: data.completedTasks.toString(),
          totalTasks: data.totalTasks.toString(),
          progressPercentage: progressPercentage.toString(),
          nextSteps: data.nextSteps,
          onboardingPortalUrl: `https://app.qeep.com/onboarding/${data.workflowId}`,
          supportEmail: '<EMAIL>',
          currentYear: new Date().getFullYear().toString(),
        },
        tenantCode: data.tenantCode,
        metadata: {
          requestId: `progress-${data.workflowId}-${Date.now()}`,
          workflowId: data.workflowId,
          tenantCode: data.tenantCode,
          institutionName: data.institutionName,
          progressPercentage,
          timestamp: new Date(),
          type: 'onboarding_progress',
        },
      };

      const response = await firstValueFrom(this.client.send('sendEmail', emailRequest));

      if (response.success) {
        this.logger.log(`Onboarding progress email sent successfully to ${data.email}: ${response.notificationId}`);
        return {
          success: true,
          notificationId: response.notificationId,
        };
      } else {
        this.logger.error(`Failed to send onboarding progress email to ${data.email}: ${response.error}`);
        return {
          success: false,
          error: response.error || 'Unknown error occurred',
        };
      }
    } catch (error) {
      this.logger.error(`Error sending onboarding progress email to ${data.email}: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Send onboarding completion email
   */
  async sendOnboardingCompletionEmail(data: {
    email: string;
    institutionName: string;
    contactName: string;
    workflowId: string;
    tenantCode: string;
    goLiveDate: string;
  }): Promise<{ success: boolean; notificationId?: string; error?: string }> {
    this.logger.log(`Sending onboarding completion email to ${data.email} for ${data.institutionName}`);

    try {
      const emailRequest = {
        recipientEmail: data.email,
        recipientName: data.contactName,
        subject: `Congratulations! ${data.institutionName} Onboarding Complete`,
        templateSlug: 'onboarding-completion-email',
        templateData: {
          contactName: data.contactName,
          institutionName: data.institutionName,
          workflowId: data.workflowId,
          tenantCode: data.tenantCode,
          goLiveDate: data.goLiveDate,
          loginUrl: 'https://app.qeep.com/login',
          dashboardUrl: `https://app.qeep.com/dashboard?tenant=${data.tenantCode}`,
          supportEmail: '<EMAIL>',
          currentYear: new Date().getFullYear().toString(),
        },
        tenantCode: data.tenantCode,
        metadata: {
          requestId: `completion-${data.workflowId}`,
          workflowId: data.workflowId,
          tenantCode: data.tenantCode,
          institutionName: data.institutionName,
          goLiveDate: data.goLiveDate,
          timestamp: new Date(),
          type: 'onboarding_completion',
        },
      };

      const response = await firstValueFrom(this.client.send('sendEmail', emailRequest));

      if (response.success) {
        this.logger.log(`Onboarding completion email sent successfully to ${data.email}: ${response.notificationId}`);
        return {
          success: true,
          notificationId: response.notificationId,
        };
      } else {
        this.logger.error(`Failed to send onboarding completion email to ${data.email}: ${response.error}`);
        return {
          success: false,
          error: response.error || 'Unknown error occurred',
        };
      }
    } catch (error) {
      this.logger.error(`Error sending onboarding completion email to ${data.email}: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Close the gRPC client connection
   */
  async onModuleDestroy() {
    if (this.client) {
      await this.client.close();
    }
  }
}
