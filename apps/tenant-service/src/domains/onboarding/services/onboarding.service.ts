/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable, Logger } from '@nestjs/common';
import { InstitutionType, OnboardingPhase, OnboardingStatus, OnboardingTaskCategory, OnboardingTaskPriority, OnboardingTaskStatus, SubscriptionPlan } from '@qeep/contracts';
import { generateTenantId, generateCuid, CUID_PREFIXES } from '@qeep/common';
import { TenantPrismaService } from '../../../database/prisma.service';
import {
  BusinessRuleViolationException,
  DatabaseOperationException,
  InvalidGoLiveDateException,
  OnboardingWorkflowCreationException,
  OnboardingWorkflowNotFoundException,
  SpecialistAssignmentException,
  TaskGenerationException,
  TenantAlreadyExistsException,
} from '../exceptions/onboarding.exceptions';
import { NotificationService } from './notification.service';

/**
 * Onboarding Service
 *
 * Handles tenant onboarding workflows including initiation, progress tracking,
 * and coordination with other services for complete onboarding experience.
 */
@Injectable()
export class OnboardingService {
  private readonly logger = new Logger(OnboardingService.name);

  constructor(private readonly prisma: TenantPrismaService, private readonly notificationService: NotificationService) {}

  /**
   * Initiate onboarding for a new financial institution
   * Creates onboarding workflow, generates tasks, assigns specialist, and sends welcome email
   */
  async initiateOnboarding(data: any): Promise<any> {
    this.logger.log(`Initiating onboarding for institution: ${data.institutionName}`);

    try {
      // Validate business rules
      this.validateOnboardingData(data);

      // Generate unique tenant code
      const tenantCode = this.generateTenantCode(data.institutionName);

      // Check if tenant already exists
      let existingTenant: { id: string; code: string } | null;
      try {
        existingTenant = await this.prisma.tenant.findUnique({
          where: { code: tenantCode },
        });
      } catch (error) {
        this.logger.error(`Database error while checking existing tenant: ${error instanceof Error ? error.message : 'Unknown error'}`);
        throw new DatabaseOperationException('findUnique', 'tenant', error instanceof Error ? error : new Error(String(error)));
      }

      if (existingTenant) {
        this.logger.warn(`Tenant already exists: ${data.institutionName} with code ${tenantCode}`);
        throw new TenantAlreadyExistsException(data.institutionName, tenantCode);
      }

      // Create tenant record
      let tenant: { id: string; code: string };
      try {
        tenant = await this.prisma.tenant.create({
          data: {
            id: generateTenantId(),
            code: tenantCode,
            name: data.institutionName,
            status: 'onboarding',
            settings: {
              institutionType: data.institutionType,
              primaryContact: JSON.parse(JSON.stringify(data.primaryContact)),
              regulatoryInfo: JSON.parse(JSON.stringify(data.regulatoryInfo)),
              expectedGoLive: data.expectedGoLive,
              subscriptionPlan: data.subscriptionPlan,
              onboardingInitiated: new Date().toISOString(),
            },
          },
        });
        this.logger.log(`Tenant created successfully: ${tenant.id} with code ${tenantCode}`);
      } catch (error) {
        this.logger.error(`Failed to create tenant for ${data.institutionName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        throw new DatabaseOperationException('create', 'tenant', error instanceof Error ? error : new Error(String(error)));
      }

      // Generate workflow ID
      const workflowId = generateCuid(CUID_PREFIXES.ONBOARDING_WORKFLOW);

      // Assign onboarding specialist (simplified - would integrate with user service)
      let assignedSpecialist: string;
      try {
        assignedSpecialist = this.assignOnboardingSpecialist(data.subscriptionPlan);
        this.logger.log(`Specialist assigned: ${assignedSpecialist} for ${data.subscriptionPlan} plan`);
      } catch (error) {
        this.logger.error(`Failed to assign specialist for ${data.subscriptionPlan} plan: ${error instanceof Error ? error.message : 'Unknown error'}`);
        throw new SpecialistAssignmentException(data.subscriptionPlan, error instanceof Error ? error : new Error(String(error)));
      }

      // Generate onboarding tasks
      let tasks: { id: string; name: string; status: string; assignee: string; dueDate: string }[];
      try {
        tasks = this.generateOnboardingTasks(data.institutionType, data.subscriptionPlan);
        this.logger.log(`Generated ${tasks.length} onboarding tasks for ${data.institutionType} institution`);
      } catch (error) {
        this.logger.error(`Failed to generate tasks for ${data.institutionType} with ${data.subscriptionPlan} plan: ${error instanceof Error ? error.message : 'Unknown error'}`);
        throw new TaskGenerationException(data.institutionType, data.subscriptionPlan, error instanceof Error ? error : new Error(String(error)));
      }

      // Calculate estimated completion date (simplified calculation)
      const estimatedCompletionDate = this.calculateEstimatedCompletion(data.expectedGoLive, tasks.length);

      // Create onboarding workflow record in dedicated table
      let workflow: { id: string; workflowId: string };
      try {
        workflow = await this.prisma.onboardingWorkflow.create({
          data: {
            id: workflowId,
            workflowId,
            tenantId: tenant.id,
            tenantCode: tenant.code,
            status: 'INITIATED',
            institutionName: data.institutionName,
            institutionType: data.institutionType,
            subscriptionPlan: data.subscriptionPlan,
            primaryContactName: data.primaryContact.name,
            primaryContactEmail: data.primaryContact.email,
            primaryContactPhone: data.primaryContact.phone,
            charterNumber: data.regulatoryInfo.charterNumber,
            fdicCert: data.regulatoryInfo.fdicCert,
            expectedGoLive: new Date(data.expectedGoLive),
            estimatedCompletionDate: new Date(estimatedCompletionDate),
            assignedSpecialist,
            welcomeEmailSent: false,
          },
        });
        this.logger.log(`Onboarding workflow created: ${workflowId}`);
      } catch (error) {
        this.logger.error(`Failed to create onboarding workflow for ${data.institutionName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        throw new OnboardingWorkflowCreationException(data.institutionName, error instanceof Error ? error : new Error(String(error)));
      }

      // Create onboarding tasks in dedicated table
      try {
        const taskPromises = tasks.map((task) =>
          this.prisma.onboardingTask.create({
            data: {
              id: generateCuid(CUID_PREFIXES.ONBOARDING_STEP),
              workflowId: workflow.id,
              taskId: task.id,
              name: task.name,
              status: 'PENDING',
              assignee: task.assignee,
              dueDate: new Date(task.dueDate),
              priority: 1,
              dependsOn: [],
            },
          }),
        );

        await Promise.all(taskPromises);
        this.logger.log(`Created ${tasks.length} onboarding tasks for workflow ${workflowId}`);
      } catch (error) {
        this.logger.error(`Failed to create onboarding tasks for workflow ${workflowId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        throw new DatabaseOperationException('create', 'onboardingTask', error instanceof Error ? error : new Error(String(error)));
      }

      // Send welcome email and update workflow (non-blocking)
      try {
        await this.sendWelcomeEmail(data.primaryContact.email, data.institutionName, workflowId, data.primaryContact.name, tenant.code);
        this.logger.log(`Welcome email sent to ${data.primaryContact.email}`);
      } catch (error) {
        // Log error but don't fail the onboarding process
        this.logger.warn(`Failed to send welcome email to ${data.primaryContact.email}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      // Mark welcome email as sent
      await this.prisma.onboardingWorkflow.update({
        where: { id: workflow.id },
        data: {
          welcomeEmailSent: true,
          welcomeEmailSentAt: new Date(),
        },
      });

      this.logger.log(`Onboarding workflow created successfully: ${workflowId}`);

      return {
        workflowId,
        tenantCode: tenant.code,
        status: OnboardingStatus.INITIATED,
        assignedSpecialist,
        estimatedCompletionDate,
        welcomeEmailSent: false, // TODO: Implement welcome email functionality
        tasksGenerated: tasks.map((task) => ({
          id: task.id,
          name: task.name,
          status: OnboardingTaskStatus.PENDING,
          assignee: task.assignee,
          dueDate: task.dueDate,
        })),
        message: `Onboarding workflow initiated successfully for ${data.institutionName}`,
      };
    } catch (error) {
      this.logger.error(`Failed to initiate onboarding for ${data.institutionName}:`, error);
      throw error;
    }
  }

  /**
   * Get onboarding workflow status and progress
   */
  async getWorkflowStatus(workflowId: string): Promise<any> {
    this.logger.log(`Getting workflow status for: ${workflowId}`);

    try {
      const workflow = await this.prisma.onboardingWorkflow.findUnique({
        where: { workflowId },
        include: {
          tasks: true,
        },
      });

      if (!workflow) {
        throw new OnboardingWorkflowNotFoundException(workflowId);
      }

      const completedTasks = workflow.tasks.filter((task) => task.status === 'COMPLETED').length;
      const totalTasks = workflow.tasks.length;
      const progressPercentage = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

      return {
        workflowId: workflow.workflowId,
        tenantCode: workflow.tenantCode,
        institutionName: workflow.institutionName,
        status: workflow.status as OnboardingStatus,
        progressPercentage,
        assignedSpecialist: workflow.assignedSpecialist,
        estimatedCompletionDate: workflow.estimatedCompletionDate.toISOString(),
        welcomeEmailSent: workflow.welcomeEmailSent,
        currentPhase: this.getCurrentPhase(workflow.tasks),
        completedTasks,
        totalTasks,
        createdAt: workflow.createdAt.toISOString(),
        updatedAt: workflow.updatedAt.toISOString(),
      };
    } catch (error) {
      if (error instanceof OnboardingWorkflowNotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to get workflow status for ${workflowId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw new DatabaseOperationException('findUnique', 'onboardingWorkflow', error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Update individual task status
   */
  async updateTaskStatus(workflowId: string, taskId: string, updateData: any): Promise<any> {
    this.logger.log(`Updating task ${taskId} for workflow ${workflowId}`);

    try {
      // Verify workflow exists
      const workflow = await this.prisma.onboardingWorkflow.findUnique({
        where: { workflowId },
      });

      if (!workflow) {
        throw new OnboardingWorkflowNotFoundException(workflowId);
      }

      // Update the task
      const updatedTask = await this.prisma.onboardingTask.update({
        where: {
          workflowId_taskId: {
            workflowId: workflow.id,
            taskId,
          },
        },
        data: {
          status: updateData.status as OnboardingTaskStatus,
          metadata: {
            notes: updateData.notes,
            actualHours: updateData.actualHours,
          },
          updatedAt: new Date(),
        },
      });

      this.logger.log(`Task ${taskId} updated successfully`);

      const metadata = (updatedTask.metadata as Record<string, unknown>) || {};

      return {
        id: updatedTask.taskId,
        name: updatedTask.name,
        description: updatedTask.description || '',
        status: updatedTask.status as OnboardingTaskStatus,
        assignee: updatedTask.assignee,
        priority: updatedTask.priority,
        estimatedHours: metadata.estimatedHours || 4,
        actualHours: metadata.actualHours || undefined,
        dueDate: updatedTask.dueDate.toISOString(),
        dependencies: updatedTask.dependsOn,
        phase: metadata.phase || 'INITIAL_SETUP',
        category: metadata.category || 'ADMINISTRATIVE',
        notes: metadata.notes || undefined,
        createdAt: updatedTask.createdAt.toISOString(),
        updatedAt: updatedTask.updatedAt.toISOString(),
      };
    } catch (error) {
      if (error instanceof OnboardingWorkflowNotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to update task ${taskId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw new DatabaseOperationException('update', 'onboardingTask', error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Get all tasks for a workflow
   */
  async getWorkflowTasks(workflowId: string): Promise<any> {
    this.logger.log(`Getting tasks for workflow: ${workflowId}`);

    try {
      const workflow = await this.prisma.onboardingWorkflow.findUnique({
        where: { workflowId },
        include: {
          tasks: {
            orderBy: { createdAt: 'asc' },
          },
        },
      });

      if (!workflow) {
        throw new OnboardingWorkflowNotFoundException(workflowId);
      }

      const tasks = workflow.tasks.map((task) => {
        const metadata = (task.metadata as Record<string, unknown>) || {};
        return {
          id: task.taskId,
          name: task.name,
          description: task.description || '',
          status: task.status as OnboardingTaskStatus,
          assignee: task.assignee,
          priority: task.priority,
          estimatedHours: metadata.estimatedHours || 4,
          actualHours: metadata.actualHours || undefined,
          dueDate: task.dueDate.toISOString(),
          dependencies: task.dependsOn,
          phase: metadata.phase || 'INITIAL_SETUP',
          category: metadata.category || 'ADMINISTRATIVE',
          notes: metadata.notes || undefined,
          createdAt: task.createdAt.toISOString(),
          updatedAt: task.updatedAt.toISOString(),
        };
      });

      const total = tasks.length;
      const completed = tasks.filter((task) => task.status === OnboardingTaskStatus.COMPLETED).length;
      const inProgress = tasks.filter((task) => task.status === OnboardingTaskStatus.IN_PROGRESS).length;
      const pending = tasks.filter((task) => task.status === OnboardingTaskStatus.PENDING).length;
      const progressPercentage = total > 0 ? Math.round((completed / total) * 100) : 0;

      return {
        workflowId,
        tasks,
        summary: {
          total,
          completed,
          inProgress,
          pending,
          progressPercentage,
        },
      };
    } catch (error) {
      if (error instanceof OnboardingWorkflowNotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to get tasks for workflow ${workflowId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw new DatabaseOperationException('findUnique', 'onboardingWorkflow', error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Complete the onboarding process
   */
  async completeOnboarding(workflowId: string, requestData: any): Promise<any> {
    this.logger.log(`Completing onboarding for workflow: ${workflowId}`);

    try {
      const workflow = await this.prisma.onboardingWorkflow.findUnique({
        where: { workflowId },
        include: { tasks: true },
      });

      if (!workflow) {
        throw new OnboardingWorkflowNotFoundException(workflowId);
      }

      // Check if all tasks are completed
      const incompleteTasks = workflow.tasks.filter((task) => task.status !== 'COMPLETED');
      if (incompleteTasks.length > 0) {
        throw new BusinessRuleViolationException('Cannot complete onboarding with incomplete tasks', {
          incompleteTasks: incompleteTasks.map((t) => t.taskId),
        });
      }

      // Update workflow status
      const updatedWorkflow = await this.prisma.onboardingWorkflow.update({
        where: { workflowId },
        data: {
          status: 'COMPLETED',
          completedAt: new Date(),
          metadata: {
            completionNotes: requestData.notes,
            activateLive: requestData.activateLive,
          },
        },
      });

      // Generate API credentials (in real implementation)
      this.logger.log(`API credentials would be generated for tenant: ${workflow.tenantCode}`);

      this.logger.log(`Onboarding completed for workflow: ${workflowId}`);

      return {
        workflowId,
        tenantCode: workflow.tenantCode,
        status: 'COMPLETED',
        completedAt: updatedWorkflow.completedAt?.toISOString() || new Date().toISOString(),
        credentialsGenerated: true,
        testEnvironmentActive: true,
        liveEnvironmentActive: requestData.activateLive || false,
        message: `Onboarding completed successfully for ${workflow.institutionName}`,
      };
    } catch (error) {
      if (error instanceof OnboardingWorkflowNotFoundException || error instanceof BusinessRuleViolationException) {
        throw error;
      }
      this.logger.error(`Failed to complete onboarding for ${workflowId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw new DatabaseOperationException('update', 'onboardingWorkflow', error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Get API credentials for a tenant
   */
  async getApiCredentials(workflowId: string): Promise<any> {
    this.logger.log(`Getting API credentials for workflow: ${workflowId}`);

    try {
      const workflow = await this.prisma.onboardingWorkflow.findUnique({
        where: { workflowId },
      });

      if (!workflow) {
        throw new OnboardingWorkflowNotFoundException(workflowId);
      }

      // In a real implementation, you would fetch from a secure credentials store
      // For now, we'll generate mock credentials
      return {
        tenantCode: workflow.tenantCode,
        testEnvironment: {
          apiKey: `test_${workflow.tenantCode}_${Date.now()}`,
          apiSecret: `test_secret_${workflow.tenantCode}`,
          webhookSecret: `test_webhook_${workflow.tenantCode}`,
          baseUrl: 'https://api-test.qeep.com',
          isActive: true,
        },
        liveEnvironment: {
          apiKey: `live_${workflow.tenantCode}_${Date.now()}`,
          apiSecret: `live_secret_${workflow.tenantCode}`,
          webhookSecret: `live_webhook_${workflow.tenantCode}`,
          baseUrl: 'https://api.qeep.com',
          isActive: workflow.status === 'COMPLETED',
        },
        lastUpdated: new Date().toISOString(),
      };
    } catch (error) {
      if (error instanceof OnboardingWorkflowNotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to get credentials for ${workflowId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw new DatabaseOperationException('findUnique', 'onboardingWorkflow', error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Regenerate API credentials
   */
  async regenerateCredentials(workflowId: string, requestData: any): Promise<any> {
    this.logger.log(`Regenerating credentials for workflow: ${workflowId}, environment: ${requestData.environment}, type: ${requestData.credentialType}`);

    try {
      const workflow = await this.prisma.onboardingWorkflow.findUnique({
        where: { workflowId },
      });

      if (!workflow) {
        throw new OnboardingWorkflowNotFoundException(workflowId);
      }

      // Log the regeneration for audit purposes
      this.logger.warn(
        `Credentials regenerated for ${workflow.tenantCode}: ${requestData.environment}/${requestData.credentialType}. Reason: ${requestData.reason || 'Not specified'}`,
      );

      // Return updated credentials (in real implementation, this would update the secure store)
      return this.getApiCredentials(workflowId);
    } catch (error) {
      if (error instanceof OnboardingWorkflowNotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to regenerate credentials for ${workflowId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw new DatabaseOperationException('findUnique', 'onboardingWorkflow', error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Switch environment activation status
   */
  async switchEnvironment(workflowId: string, environment: 'test' | 'live', requestData: any): Promise<any> {
    this.logger.log(`Switching ${environment} environment for workflow: ${workflowId}, activate: ${requestData.activate}`);

    try {
      const workflow = await this.prisma.onboardingWorkflow.findUnique({
        where: { workflowId },
      });

      if (!workflow) {
        throw new OnboardingWorkflowNotFoundException(workflowId);
      }

      // Validate business rules
      if (environment === 'live' && requestData.activate && workflow.status !== 'COMPLETED') {
        throw new BusinessRuleViolationException('Cannot activate live environment for incomplete onboarding', {
          workflowStatus: workflow.status,
        });
      }

      // Log the environment switch
      this.logger.log(
        `Environment switch: ${workflow.tenantCode} ${environment} environment ${requestData.activate ? 'activated' : 'deactivated'}. Reason: ${
          requestData.reason || 'Not specified'
        }`,
      );

      return {
        tenantCode: workflow.tenantCode,
        environment,
        isActive: requestData.activate,
        switchedAt: new Date().toISOString(),
        message: `${environment} environment ${requestData.activate ? 'activated' : 'deactivated'} successfully`,
      };
    } catch (error) {
      if (error instanceof OnboardingWorkflowNotFoundException || error instanceof BusinessRuleViolationException) {
        throw error;
      }
      this.logger.error(`Failed to switch environment for ${workflowId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw new DatabaseOperationException('findUnique', 'onboardingWorkflow', error instanceof Error ? error : new Error(String(error)));
    }
  }

  // Private helper methods

  /**
   * Validate onboarding data for business rules
   */
  private validateOnboardingData(data: any): void {
    // Validate go-live date is in the future with sufficient lead time
    const goLiveDate = new Date(data.expectedGoLive);
    const today = new Date();
    const minimumLeadTime = 14; // 14 days minimum lead time
    const minimumDate = new Date(today.getTime() + minimumLeadTime * 24 * 60 * 60 * 1000);

    if (goLiveDate < minimumDate) {
      throw new InvalidGoLiveDateException(data.expectedGoLive, minimumDate.toISOString().split('T')[0]);
    }

    // Validate institution name is not empty or too short
    if (!data.institutionName || data.institutionName.trim().length < 2) {
      throw new BusinessRuleViolationException('Institution name must be at least 2 characters long', { institutionName: data.institutionName });
    }

    // Validate contact email domain (basic check)
    const emailDomain = data.primaryContact.email.split('@')[1];
    if (!emailDomain || emailDomain.length < 3) {
      throw new BusinessRuleViolationException('Primary contact email must have a valid domain', { email: data.primaryContact.email });
    }

    // Validate phone number format (basic check)
    const phoneRegex = /^[+]?[1-9][\d]{0,15}$/;
    if (!phoneRegex.test(data.primaryContact.phone.replace(/[\s\-()]/g, ''))) {
      throw new BusinessRuleViolationException('Primary contact phone number format is invalid', { phone: data.primaryContact.phone });
    }

    // Validate regulatory information
    if (!data.regulatoryInfo.charterNumber || data.regulatoryInfo.charterNumber.trim().length < 3) {
      throw new BusinessRuleViolationException('Charter number must be at least 3 characters long', { charterNumber: data.regulatoryInfo.charterNumber });
    }

    if (!data.regulatoryInfo.fdicCert || data.regulatoryInfo.fdicCert.trim().length < 3) {
      throw new BusinessRuleViolationException('FDIC certificate must be at least 3 characters long', { fdicCert: data.regulatoryInfo.fdicCert });
    }

    // Validate institution name doesn't contain prohibited words
    const prohibitedWords = ['test', 'demo', 'sample', 'fake'];
    const institutionNameLower = data.institutionName.toLowerCase();
    const hasProhibitedWord = prohibitedWords.some((word) => institutionNameLower.includes(word));
    if (hasProhibitedWord) {
      throw new BusinessRuleViolationException('Institution name contains prohibited words', { institutionName: data.institutionName, prohibitedWords });
    }

    // Validate go-live date is not too far in the future (max 1 year)
    const maxFutureDate = new Date(today.getTime() + 365 * 24 * 60 * 60 * 1000);
    if (goLiveDate > maxFutureDate) {
      throw new BusinessRuleViolationException('Go-live date cannot be more than 1 year in the future', {
        expectedGoLive: data.expectedGoLive,
        maxDate: maxFutureDate.toISOString().split('T')[0],
      });
    }
  }

  /**
   * Generate tenant code from institution name
   */
  private generateTenantCode(institutionName: string): string {
    const baseCode = institutionName
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '')
      .substring(0, 12);

    // Add timestamp suffix to ensure uniqueness
    const timestamp = Date.now().toString().slice(-4);
    return `${baseCode}-${timestamp}`;
  }

  /**
   * Assign onboarding specialist based on subscription plan
   */
  private assignOnboardingSpecialist(subscriptionPlan: SubscriptionPlan): string {
    const specialists = {
      [SubscriptionPlan.BASIC]: 'specialist-basic-001',
      [SubscriptionPlan.PREMIUM]: 'specialist-premium-001',
      [SubscriptionPlan.ENTERPRISE]: 'specialist-enterprise-001',
    };

    return specialists[subscriptionPlan] || 'specialist-default-001';
  }

  /**
   * Generate onboarding tasks based on institution type and subscription plan
   * Creates a comprehensive, sequenced task list with dependencies and priorities
   */
  private generateOnboardingTasks(institutionType: any, subscriptionPlan: any) {
    const tasks: any[] = [];
    let taskCounter = 1;

    // Phase 1: Initial Setup and Documentation (Days 1-5)
    tasks.push(...this.generateInitialSetupTasks(taskCounter));
    taskCounter += 4;

    // Phase 2: Regulatory and Compliance (Days 3-10)
    tasks.push(...this.generateRegulatoryTasks(taskCounter, institutionType));
    taskCounter += this.getRegulatoryTaskCount(institutionType);

    // Phase 3: Technical Infrastructure (Days 5-15)
    tasks.push(...this.generateTechnicalTasks(taskCounter, subscriptionPlan));
    taskCounter += this.getTechnicalTaskCount(subscriptionPlan);

    // Phase 4: Institution-Specific Configuration (Days 8-18)
    tasks.push(...this.generateInstitutionSpecificTasks(taskCounter, institutionType, subscriptionPlan));
    taskCounter += this.getInstitutionSpecificTaskCount(institutionType, subscriptionPlan);

    // Phase 5: Testing and Validation (Days 15-22)
    tasks.push(...this.generateTestingTasks(taskCounter, subscriptionPlan));
    taskCounter += this.getTestingTaskCount(subscriptionPlan);

    // Phase 6: Training and Go-Live (Days 20-28)
    tasks.push(...this.generateGoLiveTasks(taskCounter, subscriptionPlan));

    // Convert to simple format for now (until we implement full optimization)
    return this.convertToSimpleFormat(tasks);
  }

  /**
   * Generate Phase 1: Initial Setup and Documentation tasks
   */
  private generateInitialSetupTasks(startId: number): any[] {
    return [
      {
        id: `task-${String(startId).padStart(3, '0')}`,
        name: 'Account Setup and Verification',
        description: 'Create tenant account, verify contact information, and establish initial communication channels',
        status: 'pending',
        assignee: 'onboarding-coordinator',
        priority: OnboardingTaskPriority.CRITICAL,
        estimatedHours: 2,
        dueDate: this.addDays(new Date(), 1),
        dependencies: [],
        phase: OnboardingPhase.INITIAL_SETUP,
        category: OnboardingTaskCategory.ADMINISTRATIVE,
      },
      {
        id: `task-${String(startId + 1).padStart(3, '0')}`,
        name: 'Document Collection',
        description: 'Collect required regulatory documents, licenses, and institutional information',
        status: 'pending',
        assignee: 'compliance-coordinator',
        priority: OnboardingTaskPriority.CRITICAL,
        estimatedHours: 4,
        dueDate: this.addDays(new Date(), 2),
        dependencies: [`task-${String(startId).padStart(3, '0')}`],
        phase: OnboardingPhase.INITIAL_SETUP,
        category: OnboardingTaskCategory.DOCUMENTATION,
      },
      {
        id: `task-${String(startId + 2).padStart(3, '0')}`,
        name: 'Initial Risk Assessment',
        description: 'Conduct preliminary risk assessment and determine compliance requirements',
        status: 'pending',
        assignee: 'risk-analyst',
        priority: OnboardingTaskPriority.HIGH,
        estimatedHours: 6,
        dueDate: this.addDays(new Date(), 3),
        dependencies: [`task-${String(startId + 1).padStart(3, '0')}`],
        phase: OnboardingPhase.INITIAL_SETUP,
        category: OnboardingTaskCategory.RISK_MANAGEMENT,
      },
      {
        id: `task-${String(startId + 3).padStart(3, '0')}`,
        name: 'Project Kickoff Meeting',
        description: 'Schedule and conduct project kickoff meeting with all stakeholders',
        status: 'pending',
        assignee: 'project-manager',
        priority: OnboardingTaskPriority.HIGH,
        estimatedHours: 2,
        dueDate: this.addDays(new Date(), 4),
        dependencies: [`task-${String(startId + 2).padStart(3, '0')}`],
        phase: OnboardingPhase.INITIAL_SETUP,
        category: OnboardingTaskCategory.PROJECT_MANAGEMENT,
      },
    ];
  }

  /**
   * Generate Phase 2: Regulatory and Compliance tasks
   */
  private generateRegulatoryTasks(startId: number, institutionType: InstitutionType): any[] {
    const baseTasks: any[] = [
      {
        id: `task-${String(startId).padStart(3, '0')}`,
        name: 'Regulatory Documentation Review',
        description: 'Review and validate all regulatory documentation and licenses',
        status: 'pending',
        assignee: 'compliance-specialist',
        priority: OnboardingTaskPriority.CRITICAL,
        estimatedHours: 8,
        dueDate: this.addDays(new Date(), 5),
        dependencies: [`task-002`], // Document Collection
        phase: OnboardingPhase.REGULATORY_COMPLIANCE,
        category: OnboardingTaskCategory.REGULATORY_COMPLIANCE,
      },
      {
        id: `task-${String(startId + 1).padStart(3, '0')}`,
        name: 'AML/KYC Policy Configuration',
        description: 'Configure Anti-Money Laundering and Know Your Customer policies',
        status: 'pending',
        assignee: 'compliance-specialist',
        priority: OnboardingTaskPriority.CRITICAL,
        estimatedHours: 6,
        dueDate: this.addDays(new Date(), 7),
        dependencies: [`task-${String(startId).padStart(3, '0')}`],
        phase: OnboardingPhase.REGULATORY_COMPLIANCE,
        category: OnboardingTaskCategory.REGULATORY_COMPLIANCE,
      },
    ];

    // Add institution-specific regulatory tasks
    if (institutionType === InstitutionType.CREDIT_UNION) {
      baseTasks.push({
        id: `task-${String(startId + 2).padStart(3, '0')}`,
        name: 'Credit Union Regulatory Compliance',
        description: 'Configure credit union specific regulatory requirements and member protections',
        status: 'pending',
        assignee: 'compliance-specialist',
        priority: OnboardingTaskPriority.HIGH,
        estimatedHours: 4,
        dueDate: this.addDays(new Date(), 8),
        dependencies: [`task-${String(startId + 1).padStart(3, '0')}`],
        phase: OnboardingPhase.REGULATORY_COMPLIANCE,
        category: OnboardingTaskCategory.REGULATORY_COMPLIANCE,
      });
    } else if (institutionType === InstitutionType.INVESTMENT_BANK) {
      baseTasks.push({
        id: `task-${String(startId + 2).padStart(3, '0')}`,
        name: 'Investment Banking Compliance Setup',
        description: 'Configure investment banking specific compliance and trading regulations',
        status: 'pending',
        assignee: 'compliance-specialist',
        priority: OnboardingTaskPriority.HIGH,
        estimatedHours: 6,
        dueDate: this.addDays(new Date(), 9),
        dependencies: [`task-${String(startId + 1).padStart(3, '0')}`],
        phase: OnboardingPhase.REGULATORY_COMPLIANCE,
        category: OnboardingTaskCategory.REGULATORY_COMPLIANCE,
      });
    }

    return baseTasks;
  }

  /**
   * Generate Phase 3: Technical Infrastructure tasks
   */
  private generateTechnicalTasks(startId: number, subscriptionPlan: SubscriptionPlan): any[] {
    const baseTasks: any[] = [
      {
        id: `task-${String(startId).padStart(3, '0')}`,
        name: 'Technical Infrastructure Setup',
        description: 'Set up basic technical infrastructure and system access',
        status: 'pending',
        assignee: 'tech-team',
        priority: OnboardingTaskPriority.HIGH,
        estimatedHours: 8,
        dueDate: this.addDays(new Date(), 7),
        dependencies: [`task-003`], // Initial Risk Assessment
        phase: OnboardingPhase.TECHNICAL_INFRASTRUCTURE,
        category: OnboardingTaskCategory.TECHNICAL_INFRASTRUCTURE,
      },
    ];

    // Add premium/enterprise specific tasks
    if (subscriptionPlan === SubscriptionPlan.PREMIUM || subscriptionPlan === SubscriptionPlan.ENTERPRISE) {
      baseTasks.push({
        id: `task-${String(startId + 1).padStart(3, '0')}`,
        name: 'Advanced Security Configuration',
        description: 'Configure advanced security features and monitoring',
        status: 'pending',
        assignee: 'security-team',
        priority: OnboardingTaskPriority.HIGH,
        estimatedHours: 6,
        dueDate: this.addDays(new Date(), 10),
        dependencies: [`task-${String(startId).padStart(3, '0')}`],
        phase: OnboardingPhase.TECHNICAL_INFRASTRUCTURE,
        category: OnboardingTaskCategory.SECURITY,
      });
    }

    return baseTasks;
  }

  /**
   * Generate Phase 4: Institution-Specific Configuration tasks
   */
  private generateInstitutionSpecificTasks(
    startId: number,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _institutionType: InstitutionType,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _subscriptionPlan: SubscriptionPlan,
  ): any[] {
    const baseTasks: any[] = [
      {
        id: `task-${String(startId).padStart(3, '0')}`,
        name: 'Institution Configuration',
        description: 'Configure institution-specific settings and parameters',
        status: 'pending',
        assignee: 'config-team',
        priority: OnboardingTaskPriority.HIGH,
        estimatedHours: 4,
        dueDate: this.addDays(new Date(), 12),
        dependencies: [`task-${String(startId - 1).padStart(3, '0')}`], // Previous phase
        phase: OnboardingPhase.INSTITUTION_CONFIGURATION,
        category: OnboardingTaskCategory.CONFIGURATION,
      },
    ];

    return baseTasks;
  }

  /**
   * Generate Phase 5: Testing and Validation tasks
   */
  private generateTestingTasks(
    startId: number,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _subscriptionPlan: SubscriptionPlan,
  ): any[] {
    return [
      {
        id: `task-${String(startId).padStart(3, '0')}`,
        name: 'System Testing',
        description: 'Conduct comprehensive system testing and validation',
        status: 'pending',
        assignee: 'qa-team',
        priority: OnboardingTaskPriority.HIGH,
        estimatedHours: 8,
        dueDate: this.addDays(new Date(), 18),
        dependencies: [`task-${String(startId - 1).padStart(3, '0')}`],
        phase: OnboardingPhase.TESTING_VALIDATION,
        category: OnboardingTaskCategory.TESTING,
      },
    ];
  }

  /**
   * Generate Phase 6: Training and Go-Live tasks
   */
  private generateGoLiveTasks(
    startId: number,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _subscriptionPlan: SubscriptionPlan,
  ): any[] {
    return [
      {
        id: `task-${String(startId).padStart(3, '0')}`,
        name: 'User Training',
        description: 'Conduct user training sessions and provide documentation',
        status: 'pending',
        assignee: 'training-team',
        priority: OnboardingTaskPriority.MEDIUM,
        estimatedHours: 6,
        dueDate: this.addDays(new Date(), 22),
        dependencies: [`task-${String(startId - 1).padStart(3, '0')}`],
        phase: OnboardingPhase.TRAINING_GO_LIVE,
        category: OnboardingTaskCategory.TRAINING,
      },
      {
        id: `task-${String(startId + 1).padStart(3, '0')}`,
        name: 'Go-Live Preparation',
        description: 'Final preparations and go-live readiness check',
        status: 'pending',
        assignee: 'launch-team',
        priority: OnboardingTaskPriority.CRITICAL,
        estimatedHours: 4,
        dueDate: this.addDays(new Date(), 25),
        dependencies: [`task-${String(startId).padStart(3, '0')}`],
        phase: OnboardingPhase.TRAINING_GO_LIVE,
        category: OnboardingTaskCategory.GO_LIVE,
      },
    ];
  }

  /**
   * Get count of regulatory tasks for a given institution type
   */
  private getRegulatoryTaskCount(institutionType: InstitutionType): number {
    let baseCount = 2; // Base regulatory tasks

    // Add institution-specific tasks
    if (institutionType === InstitutionType.CREDIT_UNION || institutionType === InstitutionType.INVESTMENT_BANK) {
      baseCount += 1;
    }

    return baseCount;
  }

  /**
   * Get count of technical tasks for a given subscription plan
   */
  private getTechnicalTaskCount(subscriptionPlan: SubscriptionPlan): number {
    let baseCount = 1; // Base technical tasks

    // Add premium/enterprise specific tasks
    if (subscriptionPlan === SubscriptionPlan.PREMIUM || subscriptionPlan === SubscriptionPlan.ENTERPRISE) {
      baseCount += 1;
    }

    return baseCount;
  }

  /**
   * Get count of institution-specific tasks
   */
  private getInstitutionSpecificTaskCount(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _institutionType: InstitutionType,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _subscriptionPlan: SubscriptionPlan,
  ): number {
    return 1; // Base configuration task
  }

  /**
   * Get count of testing tasks
   */
  private getTestingTaskCount(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _subscriptionPlan: SubscriptionPlan,
  ): number {
    return 1; // Base testing task
  }

  /**
   * Convert complex OnboardingTask objects to simple format for API response
   */
  private convertToSimpleFormat(tasks: any[]) {
    return tasks.map((task) => ({
      id: task.id,
      name: task.name,
      status: task.status,
      assignee: task.assignee,
      dueDate: task.dueDate.toISOString().split('T')[0], // Format as YYYY-MM-DD
    }));
  }

  /**
   * Determine current phase based on task progress
   */
  private getCurrentPhase(tasks: Array<{ status: string; phase?: string }>): string {
    if (!tasks || tasks.length === 0) {
      return 'INITIAL_SETUP';
    }

    // Simple logic: return the phase of the first incomplete task
    const incompleteTask = tasks.find((task) => task.status !== 'COMPLETED');
    if (incompleteTask && incompleteTask.phase) {
      return incompleteTask.phase;
    }

    // If all tasks are complete, return the last phase
    return 'TRAINING_GO_LIVE';
  }

  /**
   * Calculate estimated completion date
   */
  private calculateEstimatedCompletion(expectedGoLive: string, taskCount: number): string {
    const goLiveDate = new Date(expectedGoLive);
    const estimatedDays = Math.max(21, taskCount * 3); // Minimum 21 days, or 3 days per task
    const estimatedDate = new Date(goLiveDate.getTime() - estimatedDays * 24 * 60 * 60 * 1000);
    return estimatedDate.toISOString().split('T')[0];
  }

  /**
   * Add days to a date
   */
  private addDays(date: Date, days: number): Date {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  }

  /**
   * Send welcome email via notification service
   */
  private async sendWelcomeEmail(email: string, institutionName: string, workflowId: string, contactName: string, tenantCode: string): Promise<void> {
    this.logger.log(`Sending welcome email to ${email} for ${institutionName} (workflow: ${workflowId})`);

    try {
      const result = await this.notificationService.sendOnboardingWelcomeEmail({
        email,
        institutionName,
        contactName,
        workflowId,
        tenantCode,
      });

      if (result.success) {
        this.logger.log(`Welcome email sent successfully to ${email}: ${result.notificationId}`);
      } else {
        this.logger.error(`Failed to send welcome email to ${email}: ${result.error}`);
        // Don't throw error to prevent onboarding failure due to email issues
      }
    } catch (error) {
      this.logger.error(`Error sending welcome email to ${email}: ${error.message}`, error.stack);
      // Don't throw error to prevent onboarding failure due to email issues
    }
  }
}
