import { BadRequestException, ConflictException, NotFoundException, InternalServerErrorException } from '@nestjs/common';

/**
 * Custom exceptions for onboarding domain
 */

export class OnboardingException extends Error {
  constructor(message: string, public readonly code: string, public readonly statusCode = 500) {
    super(message);
    this.name = 'OnboardingException';
  }
}

export class TenantAlreadyExistsException extends ConflictException {
  constructor(institutionName: string, tenantCode?: string) {
    const message = tenantCode 
      ? `Institution '${institutionName}' already exists with tenant code: ${tenantCode}`
      : `Institution '${institutionName}' already exists`;
    
    super({
      message,
      error: 'TENANT_ALREADY_EXISTS',
      statusCode: 409,
      details: {
        institutionName,
        tenantCode,
      },
    });
  }
}

export class InvalidOnboardingDataException extends BadRequestException {
  constructor(field: string, value: unknown, reason: string) {
    super({
      message: `Invalid onboarding data: ${field} is ${reason}`,
      error: 'INVALID_ONBOARDING_DATA',
      statusCode: 400,
      details: {
        field,
        value,
        reason,
      },
    });
  }
}

export class OnboardingWorkflowNotFoundException extends NotFoundException {
  constructor(workflowId: string) {
    super({
      message: `Onboarding workflow not found: ${workflowId}`,
      error: 'ONBOARDING_WORKFLOW_NOT_FOUND',
      statusCode: 404,
      details: {
        workflowId,
      },
    });
  }
}

export class OnboardingWorkflowCreationException extends InternalServerErrorException {
  constructor(institutionName: string, originalError?: Error) {
    super({
      message: `Failed to create onboarding workflow for ${institutionName}`,
      error: 'ONBOARDING_WORKFLOW_CREATION_FAILED',
      statusCode: 500,
      details: {
        institutionName,
        originalError: originalError?.message,
      },
    });
  }
}

export class NotificationServiceException extends InternalServerErrorException {
  constructor(email: string, notificationType: string, originalError?: Error) {
    super({
      message: `Failed to send ${notificationType} notification to ${email}`,
      error: 'NOTIFICATION_SERVICE_FAILED',
      statusCode: 500,
      details: {
        email,
        notificationType,
        originalError: originalError?.message,
      },
    });
  }
}

export class DatabaseOperationException extends InternalServerErrorException {
  constructor(operation: string, entity: string, originalError?: Error) {
    super({
      message: `Database operation failed: ${operation} on ${entity}`,
      error: 'DATABASE_OPERATION_FAILED',
      statusCode: 500,
      details: {
        operation,
        entity,
        originalError: originalError?.message,
      },
    });
  }
}

export class BusinessRuleViolationException extends BadRequestException {
  constructor(rule: string, details: Record<string, unknown>) {
    super({
      message: `Business rule violation: ${rule}`,
      error: 'BUSINESS_RULE_VIOLATION',
      statusCode: 400,
      details: {
        rule,
        ...details,
      },
    });
  }
}

export class InvalidGoLiveDateException extends BadRequestException {
  constructor(providedDate: string, minimumDate: string) {
    super({
      message: `Invalid go-live date: ${providedDate}. Must be at least ${minimumDate}`,
      error: 'INVALID_GO_LIVE_DATE',
      statusCode: 400,
      details: {
        providedDate,
        minimumDate,
        reason: 'Go-live date must be in the future with sufficient lead time',
      },
    });
  }
}

export class SpecialistAssignmentException extends InternalServerErrorException {
  constructor(subscriptionPlan: string, originalError?: Error) {
    super({
      message: `Failed to assign onboarding specialist for subscription plan: ${subscriptionPlan}`,
      error: 'SPECIALIST_ASSIGNMENT_FAILED',
      statusCode: 500,
      details: {
        subscriptionPlan,
        originalError: originalError?.message,
      },
    });
  }
}

export class TaskGenerationException extends InternalServerErrorException {
  constructor(institutionType: string, subscriptionPlan: string, originalError?: Error) {
    super({
      message: `Failed to generate onboarding tasks for ${institutionType} with ${subscriptionPlan} plan`,
      error: 'TASK_GENERATION_FAILED',
      statusCode: 500,
      details: {
        institutionType,
        subscriptionPlan,
        originalError: originalError?.message,
      },
    });
  }
}

/**
 * Error codes for consistent error handling
 */
export const OnboardingErrorCodes = {
  TENANT_ALREADY_EXISTS: 'TENANT_ALREADY_EXISTS',
  INVALID_ONBOARDING_DATA: 'INVALID_ONBOARDING_DATA',
  ONBOARDING_WORKFLOW_NOT_FOUND: 'ONBOARDING_WORKFLOW_NOT_FOUND',
  ONBOARDING_WORKFLOW_CREATION_FAILED: 'ONBOARDING_WORKFLOW_CREATION_FAILED',
  NOTIFICATION_SERVICE_FAILED: 'NOTIFICATION_SERVICE_FAILED',
  DATABASE_OPERATION_FAILED: 'DATABASE_OPERATION_FAILED',
  BUSINESS_RULE_VIOLATION: 'BUSINESS_RULE_VIOLATION',
  INVALID_GO_LIVE_DATE: 'INVALID_GO_LIVE_DATE',
  SPECIALIST_ASSIGNMENT_FAILED: 'SPECIALIST_ASSIGNMENT_FAILED',
  TASK_GENERATION_FAILED: 'TASK_GENERATION_FAILED',
} as const;

export type OnboardingErrorCode = typeof OnboardingErrorCodes[keyof typeof OnboardingErrorCodes];
