import { Injectable, Logger } from '@nestjs/common';

import { TenantCreatedEvent } from '../tenant-created.event';

@Injectable()
export class TenantCreatedHandler {
  private readonly logger = new Logger(TenantCreatedHandler.name);

  async handle(event: TenantCreatedEvent) {
    this.logger.log(`Handling TenantCreatedEvent for tenant: ${event.tenant.code}`);

    try {
      // Perform post-creation tasks
      await this.sendWelcomeNotification(event);
      await this.setupDefaultResources(event);
      await this.notifyExternalSystems(event);
      
      this.logger.log(`Successfully processed TenantCreatedEvent for tenant: ${event.tenant.code}`);
    } catch (error) {
      this.logger.error(`Error processing TenantCreatedEvent for tenant: ${event.tenant.code}`, error);
      // Consider implementing retry logic or dead letter queue
    }
  }

  private async sendWelcomeNotification(event: TenantCreatedEvent) {
    // Send welcome email or notification
    this.logger.debug(`Sending welcome notification for tenant: ${event.tenant.code}`);
    
    // Implementation would integrate with notification service
    // await this.notificationService.sendWelcomeEmail({
    //   tenantId: event.tenant.id,
    //   tenantName: event.tenant.name,
    //   ownerEmail: event.ownerEmail,
    // });
  }

  private async setupDefaultResources(event: TenantCreatedEvent) {
    // Setup default resources for the new tenant
    this.logger.debug(`Setting up default resources for tenant: ${event.tenant.code}`);
    
    // Implementation would create default:
    // - User roles and permissions
    // - Default settings
    // - Sample data (if applicable)
    // - API keys
    // - Webhooks
  }

  private async notifyExternalSystems(event: TenantCreatedEvent) {
    // Notify external systems about the new tenant
    this.logger.debug(`Notifying external systems about tenant: ${event.tenant.code}`);
    
    // Implementation would notify:
    // - Billing system
    // - Analytics system
    // - Monitoring system
    // - Third-party integrations
  }
}
