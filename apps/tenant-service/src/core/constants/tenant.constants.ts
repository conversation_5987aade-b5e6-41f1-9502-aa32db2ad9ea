export const TENANT_CONSTANTS = {
  // Default values
  DEFAULT_PLAN: 'BASIC',
  DEFAULT_STATUS: 'ACTIVE',
  DEFAULT_THEME: 'light',
  DEFAULT_TIMEZONE: 'UTC',
  DEFAULT_LANGUAGE: 'en',
  DEFAULT_CURRENCY: 'USD',

  // Limits
  MAX_TENANT_NAME_LENGTH: 100,
  MIN_TENANT_NAME_LENGTH: 2,
  MAX_TENANT_CODE_LENGTH: 50,
  MIN_TENANT_CODE_LENGTH: 2,
  MAX_TENANTS_PER_USER: 5,
  MAX_USERS_PER_TENANT: 100,
  MAX_STORAGE_PER_TENANT_MB: 1024,
  MAX_API_CALLS_PER_MONTH: 10000,

  // Validation patterns
  TENANT_CODE_PATTERN: /^[a-zA-Z0-9_-]+$/,
  DOMAIN_PATTERN: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,

  // Cache keys
  CACHE_KEYS: {
    TENANT_BY_ID: 'tenant:id:',
    TENANT_BY_CODE: 'tenant:code:',
    TENANT_SETTINGS: 'tenant:settings:',
    TENANT_STATS: 'tenant:stats:',
    USER_TENANTS: 'user:tenants:',
  },

  // Cache TTL (in seconds)
  CACHE_TTL: {
    TENANT_DATA: 300, // 5 minutes
    TENANT_SETTINGS: 600, // 10 minutes
    TENANT_STATS: 60, // 1 minute
    USER_TENANTS: 300, // 5 minutes
  },

  // Events
  EVENTS: {
    TENANT_CREATED: 'tenant.created',
    TENANT_UPDATED: 'tenant.updated',
    TENANT_DELETED: 'tenant.deleted',
    TENANT_STATUS_CHANGED: 'tenant.status.changed',
    TENANT_PLAN_CHANGED: 'tenant.plan.changed',
    TENANT_USER_ADDED: 'tenant.user.added',
    TENANT_USER_REMOVED: 'tenant.user.removed',
    TENANT_SETTINGS_UPDATED: 'tenant.settings.updated',
  },

  // Error codes
  ERROR_CODES: {
    TENANT_NOT_FOUND: 'TENANT_NOT_FOUND',
    TENANT_ALREADY_EXISTS: 'TENANT_ALREADY_EXISTS',
    TENANT_CODE_TAKEN: 'TENANT_CODE_TAKEN',
    TENANT_INACTIVE: 'TENANT_INACTIVE',
    TENANT_SUSPENDED: 'TENANT_SUSPENDED',
    TENANT_LIMIT_EXCEEDED: 'TENANT_LIMIT_EXCEEDED',
    INVALID_TENANT_CODE: 'INVALID_TENANT_CODE',
    INVALID_TENANT_SETTINGS: 'INVALID_TENANT_SETTINGS',
  },

  // Billing
  BILLING: {
    TRIAL_PERIOD_DAYS: 14,
    GRACE_PERIOD_DAYS: 7,
    DEFAULT_TAX_RATE: 0.0,
    SUPPORTED_CURRENCIES: ['USD', 'EUR', 'GBP', 'CAD', 'AUD'],
  },

  // Security
  SECURITY: {
    MAX_LOGIN_ATTEMPTS: 5,
    LOCKOUT_DURATION_MINUTES: 15,
    SESSION_TIMEOUT_MINUTES: 60,
    PASSWORD_MIN_LENGTH: 8,
  },
} as const;
