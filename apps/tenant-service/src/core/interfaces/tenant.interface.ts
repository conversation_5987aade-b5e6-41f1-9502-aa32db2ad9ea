import { TenantStatus, TenantPlan, TenantType, BillingCycle } from '../enums/tenant-status.enum';

export interface ITenant {
  id: string;
  code: string;
  name: string;
  status: TenantStatus;
  plan: TenantPlan;
  type?: TenantType;
  settings: ITenantSettings;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

export interface ITenantSettings {
  theme?: string;
  timezone?: string;
  dateFormat?: string;
  currency?: string;
  language?: string;
  notifications?: {
    email?: boolean;
    sms?: boolean;
    push?: boolean;
  };
  features?: {
    advanced_analytics?: boolean;
    custom_branding?: boolean;
    api_access?: boolean;
    webhooks?: boolean;
  };
  limits?: {
    maxUsers?: number;
    maxStorage?: number;
    maxApiCalls?: number;
  };
}

export interface ITenantBilling {
  id: string;
  tenantId: string;
  plan: TenantPlan;
  billingCycle: BillingCycle;
  amount: number;
  currency: string;
  nextBillingDate: Date;
  isActive: boolean;
  trialEndsAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface ITenantDomain {
  id: string;
  tenantId: string;
  domain: string;
  isCustom: boolean;
  isVerified: boolean;
  sslEnabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ITenantUser {
  id: string;
  tenantId: string;
  userId: string;
  role: string;
  permissions: string[];
  isActive: boolean;
  joinedAt: Date;
  lastActiveAt?: Date;
}

export interface ITenantStats {
  totalUsers: number;
  activeUsers: number;
  storageUsed: number;
  apiCallsThisMonth: number;
  lastActivity: Date;
}

export interface ITenantOverview {
  totalTenants: number;
  activeTenants: number;
  inactiveTenants: number;
  suspendedTenants: number;
  pendingTenants: number;
  recentTenants: ITenant[];
}
