import { Injectable, Logger, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';

import { TenantService } from '../domains/tenant/services/tenant.service';

export interface TenantRequest extends Request {
  tenant?: {
    id: string;
    code: string;
    name: string;
    status: string;
    settings: any;
  };
}

@Injectable()
export class TenantContextMiddleware implements NestMiddleware {
  private readonly logger = new Logger(TenantContextMiddleware.name);

  constructor(private readonly tenantService: TenantService) {}

  async use(req: TenantRequest, res: Response, next: NextFunction) {
    try {
      // Extract tenant identifier from various sources
      const tenantId = this.extractTenantId(req);

      if (tenantId) {
        // Load tenant data and attach to request
        const tenant = await this.tenantService.getTenantById(tenantId);
        if (tenant) {
          req.tenant = {
            id: tenant.id,
            code: tenant.code,
            name: tenant.name,
            status: tenant.status,
            settings: tenant.settings,
          };

          this.logger.debug(`Tenant context set: ${tenant.code} (${tenant.id})`);
        } else {
          this.logger.warn(`Tenant not found: ${tenantId}`);
        }
      }
    } catch (error) {
      this.logger.error(`Error setting tenant context: ${error.message}`);
    }

    next();
  }

  private extractTenantId(req: TenantRequest): string | null {
    // Try to extract tenant ID from various sources:

    // 1. From URL path parameter
    if (req.params?.tenantId) {
      return req.params.tenantId;
    }

    // 2. From query parameter
    if (req.query?.tenantId) {
      return req.query.tenantId as string;
    }

    // 3. From custom header
    if (req.headers['x-tenant-id']) {
      const tenantId = req.headers['x-tenant-id'] as string;
      if (tenantId.includes(',')) {
        const parts = tenantId.split(',').map((part) => part.trim());
        return parts[0]; // Take the first part
      }
      return req.headers['x-tenant-id'] as string;
    }

    // 4. From subdomain (if using subdomain-based tenant routing)
    const host = req.headers.host;
    if (host) {
      const subdomain = host.split('.')[0];
      if (subdomain && subdomain !== 'www' && subdomain !== 'api') {
        return subdomain;
      }
    }

    // 5. From JWT token (if tenant ID is embedded in the token)
    // This would require JWT parsing logic here or in a guard

    return null;
  }
}
