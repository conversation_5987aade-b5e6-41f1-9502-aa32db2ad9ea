import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/tenant-client';
import { BasePrismaService } from '@qeep/common';

@Injectable()
export class TenantPrismaService extends BasePrismaService {
  protected prismaClient: PrismaClient;

  constructor() {
    super();
    this.prismaClient = new PrismaClient({
      log: ['error'],
      errorFormat: 'colorless',
    });
  }

  // Expose Prisma client methods
  get tenant() {
    return this.prismaClient.tenant;
  }

  get tenantOnboardingSession() {
    return this.prismaClient.tenantOnboardingSession;
  }

  get tenantOnboardingStep() {
    return this.prismaClient.tenantOnboardingStep;
  }

  get tenantOnboardingDocument() {
    return this.prismaClient.tenantOnboardingDocument;
  }

  get onboardingWorkflow() {
    return this.prismaClient.onboardingWorkflow;
  }

  get onboardingTask() {
    return this.prismaClient.onboardingTask;
  }

  get tenantApiKey() {
    return this.prismaClient.tenantApiKey;
  }

  get tenantWebhookKey() {
    return this.prismaClient.tenantWebhookKey;
  }

  // Risk Profile Management models
  get riskProfile() {
    return this.prismaClient.riskProfile;
  }

  get rule() {
    return this.prismaClient.rule;
  }

  get customerRiskProfileAssignment() {
    return this.prismaClient.customerRiskProfileAssignment;
  }

  get ruleExecutionHistory() {
    return this.prismaClient.ruleExecutionHistory;
  }

  get $queryRaw() {
    return this.prismaClient.$queryRaw.bind(this.prismaClient);
  }

  get $transaction() {
    return this.prismaClient.$transaction.bind(this.prismaClient);
  }
}
