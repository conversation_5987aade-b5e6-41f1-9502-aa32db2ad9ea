import { Test, TestingModule } from '@nestjs/testing';
import { PrismaService } from './prisma.service';

describe('PrismaService', () => {
  let service: PrismaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PrismaService],
    }).compile();

    service = module.get<PrismaService>(PrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should extend PrismaClient', () => {
    expect(service).toHaveProperty('tenant');
    expect(service).toHaveProperty('$connect');
    expect(service).toHaveProperty('$disconnect');
  });

  describe('connection management', () => {
    it('should have connect method', () => {
      expect(typeof service.onModuleInit).toBe('function');
    });

    it('should have disconnect method', () => {
      expect(typeof service.onModuleDestroy).toBe('function');
    });
  });

  describe('database operations', () => {
    it('should have tenant model available', () => {
      expect(service.tenant).toBeDefined();
      expect(typeof service.tenant.findFirst).toBe('function');
      expect(typeof service.tenant.create).toBe('function');
      expect(typeof service.tenant.update).toBe('function');
      expect(typeof service.tenant.delete).toBe('function');
    });
  });
});
