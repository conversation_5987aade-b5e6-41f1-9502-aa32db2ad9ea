import { registerAs } from '@nestjs/config';

export const databaseConfig = registerAs('database', () => ({
  url: process.env.DATABASE_URL,
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT, 10) || 5432,
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'tenant_service',
  schema: process.env.DB_SCHEMA || 'tenant_service',
  ssl: process.env.DB_SSL === 'true',
  logging: process.env.DB_LOGGING === 'true',
  synchronize: process.env.DB_SYNCHRONIZE === 'true',
  migrationsRun: process.env.DB_MIGRATIONS_RUN === 'true',
  maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS, 10) || 100,
  acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT, 10) || 60000,
  timeout: parseInt(process.env.DB_TIMEOUT, 10) || 60000,
  releaseTimeout: parseInt(process.env.DB_RELEASE_TIMEOUT, 10) || 60000,
  statement_timeout: parseInt(process.env.DB_STATEMENT_TIMEOUT, 10) || 60000,
  idle_in_transaction_session_timeout: parseInt(process.env.DB_IDLE_TIMEOUT, 10) || 60000,
}));
