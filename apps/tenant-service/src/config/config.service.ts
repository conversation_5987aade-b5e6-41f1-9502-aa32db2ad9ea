import { Injectable } from '@nestjs/common';
import { ConfigService as NestConfigService } from '@nestjs/config';

@Injectable()
export class ConfigService {
  constructor(private readonly configService: NestConfigService) {}

  get port(): number {
    return this.configService.get<number>('PORT', 3003);
  }

  get grpcPort(): number {
    return this.configService.get<number>('TENANT_SERVICE_GRPC_PORT', 3014);
  }

  get environment(): string {
    return this.configService.get<string>('NODE_ENV', 'development');
  }

  get logLevel(): string {
    return this.configService.get<string>('LOG_LEVEL', 'debug');
  }

  // Database configuration
  get databaseUrl(): string {
    return this.configService.get<string>('DATABASE_URL');
  }

  get databaseConfig() {
    return this.configService.get('database');
  }

  // Tenant configuration
  get tenantConfig() {
    return this.configService.get('tenant');
  }

  get defaultTenantSettings() {
    return this.configService.get('tenant.defaultSettings', {});
  }

  get maxTenantsPerUser(): number {
    return this.configService.get<number>('tenant.maxTenantsPerUser', 5);
  }

  // JWT configuration
  get jwtSecret(): string {
    return this.configService.get<string>('JWT_SECRET', 'development-secret');
  }

  get jwtExpiresIn(): string {
    return this.configService.get<string>('JWT_EXPIRES_IN', '24h');
  }

  // Redis configuration (for caching/sessions)
  get redisUrl(): string {
    return this.configService.get<string>('REDIS_URL', 'redis://localhost:6379');
  }

  // External service URLs
  get userServiceUrl(): string {
    return this.configService.get<string>('USER_SERVICE_URL', 'localhost:3001');
  }

  get notificationServiceUrl(): string {
    return this.configService.get<string>('NOTIFICATION_SERVICE_URL', 'localhost:3002');
  }
}
