import { registerAs } from '@nestjs/config';

export const tenantConfig = registerAs('tenant', () => ({
  // Default tenant settings
  defaultSettings: {
    theme: process.env.DEFAULT_THEME || 'light',
    timezone: process.env.DEFAULT_TIMEZONE || 'UTC',
    dateFormat: process.env.DEFAULT_DATE_FORMAT || 'YYYY-MM-DD',
    currency: process.env.DEFAULT_CURRENCY || 'USD',
    language: process.env.DEFAULT_LANGUAGE || 'en',
    notifications: {
      email: process.env.DEFAULT_EMAIL_NOTIFICATIONS === 'true',
      sms: process.env.DEFAULT_SMS_NOTIFICATIONS === 'false',
      push: process.env.DEFAULT_PUSH_NOTIFICATIONS === 'true',
    },
    features: {
      advanced_analytics: process.env.DEFAULT_ADVANCED_ANALYTICS === 'false',
      custom_branding: process.env.DEFAULT_CUSTOM_BRANDING === 'false',
      api_access: process.env.DEFAULT_API_ACCESS === 'true',
      webhooks: process.env.DEFAULT_WEBHOOKS === 'false',
    },
  },

  // Tenant limits and quotas
  limits: {
    maxTenantsPerUser: parseInt(process.env.MAX_TENANTS_PER_USER, 10) || 5,
    maxUsersPerTenant: parseInt(process.env.MAX_USERS_PER_TENANT, 10) || 100,
    maxStoragePerTenant: parseInt(process.env.MAX_STORAGE_PER_TENANT, 10) || 1024, // MB
    maxApiCallsPerMonth: parseInt(process.env.MAX_API_CALLS_PER_MONTH, 10) || 10000,
  },

  // Tenant provisioning settings
  provisioning: {
    autoProvision: process.env.AUTO_PROVISION_TENANTS === 'true',
    defaultPlan: process.env.DEFAULT_TENANT_PLAN || 'basic',
    trialPeriodDays: parseInt(process.env.TRIAL_PERIOD_DAYS, 10) || 14,
    requireEmailVerification: process.env.REQUIRE_EMAIL_VERIFICATION === 'true',
    requireAdminApproval: process.env.REQUIRE_ADMIN_APPROVAL === 'false',
  },

  // Domain and routing settings
  domains: {
    allowCustomDomains: process.env.ALLOW_CUSTOM_DOMAINS === 'true',
    defaultSubdomain: process.env.DEFAULT_SUBDOMAIN || 'app',
    baseDomain: process.env.BASE_DOMAIN || 'localhost',
    sslRequired: process.env.SSL_REQUIRED === 'true',
  },

  // Billing and subscription settings
  billing: {
    enabled: process.env.BILLING_ENABLED === 'true',
    currency: process.env.BILLING_CURRENCY || 'USD',
    taxRate: parseFloat(process.env.TAX_RATE) || 0.0,
    gracePeriodDays: parseInt(process.env.GRACE_PERIOD_DAYS, 10) || 7,
  },

  // Security settings
  security: {
    enforceStrongPasswords: process.env.ENFORCE_STRONG_PASSWORDS === 'true',
    sessionTimeoutMinutes: parseInt(process.env.SESSION_TIMEOUT_MINUTES, 10) || 60,
    maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS, 10) || 5,
    lockoutDurationMinutes: parseInt(process.env.LOCKOUT_DURATION_MINUTES, 10) || 15,
  },
}));
