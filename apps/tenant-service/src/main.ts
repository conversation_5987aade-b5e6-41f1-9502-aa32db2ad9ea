/**
 * Qeep Tenant Service
 * Handles multi-tenant organization management and configuration
 */

import { Logger, VersioningType } from '@nestjs/common';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { CaseTransformValidationPipe, ProtoConfigService } from '@qeep/common';

import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ConfigService } from './config/config.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  const protoConfigService = app.get(ProtoConfigService);

  // Enable global validation with case transformation
  app.useGlobalPipes(new CaseTransformValidationPipe());

  // Global prefix for all routes
  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);

  // Enable API versioning
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
    prefix: 'v',
  });

  // Set up gRPC microservice
  const grpcPort = configService.grpcPort;
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.GRPC,
    options: {
      package: 'tenant',
      protoPath: protoConfigService.getProtoPath('tenant', 'tenant.proto'),
      url: `0.0.0.0:${grpcPort}`,
      loader: protoConfigService.getLoaderOptions(),
    },
  });

  // Start all microservices
  await app.startAllMicroservices();

  // Use specific port for Tenant Service HTTP
  const port = configService.port;
  const host = '0.0.0.0';

  await app.listen(port, host);

  Logger.log(`🏢 Tenant Service HTTP is running on: http://${host}:${port}/${globalPrefix}`);
  Logger.log(`🔌 Tenant Service gRPC is running on: ${host}:${grpcPort}`);
  Logger.log(`📚 Environment: ${configService.environment}`);
  Logger.log(`🔧 Log Level: ${configService.logLevel}`);
}

bootstrap();
