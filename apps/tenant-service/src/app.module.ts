import { CommonModule } from '@qeep/common';

import { Module } from '@nestjs/common';
import { ConfigModule } from './config/config.module';
import { AppController } from './controllers/app.controller';
import { ApiKeysModule } from './domains/api-keys/api-keys.module';
import { OnboardingModule } from './domains/onboarding/onboarding.module';
import { RiskProfilesModule } from './domains/risk-profiles/risk-profiles.module';
import { TenantModule } from './domains/tenant/tenant.module';
import { WebhookKeysModule } from './domains/webhook-keys/webhook-keys.module';
import { AppService } from './services/app.service';

@Module({
  imports: [
    CommonModule.forRoot({
      enableTelemetry: false,
      enableGlobalTenantInterceptor: false,
      enableCircuitBreaker: false,
      enableRateLimiting: false,
      enableSecurityHeaders: false,
    }),
    ConfigModule,
    TenantModule,
    OnboardingModule,
    RiskProfilesModule,
    ApiKeysModule,
    WebhookKeysModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
