{"name": "tenant-service", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/tenant-service/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "tenant-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "tenant-service:build:development"}, "production": {"buildTarget": "tenant-service:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}