#!/usr/bin/env node

const { readFileSync } = require('fs');
const { join } = require('path');

// Import the database service from the tenant service
const { TenantPrismaService } = require('../src/database/prisma.service');

async function main() {
  const prisma = new TenantPrismaService();

  try {
    console.log('🌱 Starting Risk Profile System Seeding...');

    // Read and execute seed files in order
    const seedFiles = ['seed_default_profiles.sql', 'seed_default_rules.sql', 'seed_enhanced_high_risk_rules.sql', 'seed_high_risk_advanced_rules.sql'];

    for (const fileName of seedFiles) {
      console.log(`📄 Executing ${fileName}...`);

      const filePath = join(__dirname, '../prisma/migrations/20250720_create_risk_profiles_system', fileName);
      const sqlContent = readFileSync(filePath, 'utf-8');

      // Split by semicolon and execute each statement
      const statements = sqlContent
        .split(';')
        .map((stmt) => stmt.trim())
        .filter((stmt) => stmt.length > 0 && !stmt.startsWith('--') && !stmt.startsWith('\\i'));

      for (const statement of statements) {
        if (statement.trim() && !statement.includes('DROP FUNCTION')) {
          try {
            await prisma.$executeRawUnsafe(statement);
          } catch (error) {
            console.warn(`⚠️  Warning executing statement: ${error.message}`);
            // Continue with other statements
          }
        }
      }

      console.log(`✅ Completed ${fileName}`);
    }

    // Verify the seeding
    console.log('\n📊 Verification Results:');

    const profileCount = await prisma.riskProfile.count({
      where: { isSystemGenerated: true },
    });
    console.log(`✅ Risk Profiles Created: ${profileCount}`);

    const ruleCount = await prisma.rule.count({
      where: {
        riskProfile: {
          isSystemGenerated: true,
        },
      },
    });
    console.log(`✅ Rules Created: ${ruleCount}`);

    const tenantCount = await prisma.riskProfile.groupBy({
      by: ['tenantId'],
      where: { isSystemGenerated: true },
    });
    console.log(`✅ Tenants with Profiles: ${tenantCount.length}`);

    // Show breakdown by profile type
    const profileBreakdown = await prisma.riskProfile.groupBy({
      by: ['name'],
      where: { isSystemGenerated: true },
      _count: { id: true },
    });

    console.log('\n📋 Profile Breakdown:');
    profileBreakdown.forEach((profile) => {
      console.log(`  ${profile.name}: ${profile._count.id} instances`);
    });

    // Show rules by category
    const ruleBreakdown = await prisma.rule.groupBy({
      by: ['ruleCategory'],
      where: {
        riskProfile: {
          isSystemGenerated: true,
        },
      },
      _count: { id: true },
    });

    console.log('\n📋 Rules by Category:');
    ruleBreakdown.forEach((category) => {
      console.log(`  ${category.ruleCategory}: ${category._count.id} rules`);
    });

    console.log('\n🎉 Risk Profile System Seeding Completed Successfully!');
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main().catch((error) => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
