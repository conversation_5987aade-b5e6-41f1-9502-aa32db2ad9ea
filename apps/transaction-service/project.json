{"name": "transaction-service", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "transaction-service/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["--node-env=production"]}, "configurations": {"development": {"args": ["--node-env=development"]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "transaction-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "transaction-service:build:development"}, "production": {"buildTarget": "transaction-service:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}