import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@qeep/common';
import { ProcessTransactionRequestDto } from '@qeep/contracts/transaction';
import { AlertSeverity, AlertType, AmlRiskLevel, TransactionStatus as AmlTransactionStatus } from '@qeep/proto';

/**
 * AML Service Client (Enhanced Mock Implementation)
 *
 * Provides enhanced mock AML risk evaluation functionality.
 * Ready for gRPC integration when AML service is deployed.
 *
 * This implementation provides sophisticated risk assessment logic
 * that closely mimics what a real AML service would provide.
 */
@Injectable()
export class AMLServiceClient {
  private readonly logger = new Logger(AMLServiceClient.name);
  private readonly useRealService: boolean;

  constructor(private readonly configService: ConfigService) {
    this.useRealService = this.configService.get('AML_SERVICE_ENABLED') === 'true';
    this.logger.log(`AML service client initialized (${this.useRealService ? 'real' : 'enhanced mock'} implementation)`);
  }

  /**
   * Evaluate transaction risk using enhanced mock AML logic
   */
  async evaluateTransactionRisk(
    tenantId: string,
    request: ProcessTransactionRequestDto,
    options: {
      includeHistoricalAnalysis?: boolean;
      includePatternDetection?: boolean;
      timeoutMs?: number;
    } = {},
  ): Promise<{
    riskScore: number;
    riskLevel: AmlRiskLevel;
    status: AmlTransactionStatus;
    alerts: Array<{
      type: AlertType;
      severity: AlertSeverity;
      message: string;
      ruleId?: string;
      confidence?: number;
    }>;
    evaluationId: string;
  }> {
    const startTime = Date.now();
    const requestId = `aml_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    this.logger.log(`Enhanced AML evaluation for transaction ${request.transactionId}`);

    // Simulate realistic processing delay
    await new Promise((resolve) => setTimeout(resolve, 100 + Math.random() * 200));

    const alerts: Array<{
      type: AlertType;
      severity: AlertSeverity;
      message: string;
      ruleId?: string;
      confidence?: number;
    }> = [];

    let riskScore = 0;
    let status = AmlTransactionStatus.TRANSACTION_STATUS_APPROVED;

    // 1. Enhanced amount-based risk assessment
    const amountRisk = this.assessAmountRisk(request.amount);
    riskScore += amountRisk.score;
    if (amountRisk.alert) alerts.push(amountRisk.alert);

    // 2. Enhanced time-based risk assessment
    const timeRisk = this.assessTimeRisk(request.timestamp);
    riskScore += timeRisk.score;
    if (timeRisk.alert) alerts.push(timeRisk.alert);

    // 3. Geographic risk assessment
    const geoRisk = this.assessGeographicRisk(request.metadata);
    riskScore += geoRisk.score;
    if (geoRisk.alert) alerts.push(geoRisk.alert);

    // 4. Velocity risk assessment (mock)
    const velocityRisk = this.assessVelocityRisk(request);
    riskScore += velocityRisk.score;
    if (velocityRisk.alert) alerts.push(velocityRisk.alert);

    // 5. Pattern detection (if enabled)
    if (options.includePatternDetection) {
      const patternRisk = this.assessPatternRisk(request);
      riskScore += patternRisk.score;
      if (patternRisk.alert) alerts.push(patternRisk.alert);
    }

    // 6. Customer risk factors
    const customerRisk = this.assessCustomerRisk(request);
    riskScore += customerRisk.score;
    if (customerRisk.alert) alerts.push(customerRisk.alert);

    // Determine final status and risk level
    if (riskScore >= 80) {
      status = AmlTransactionStatus.TRANSACTION_STATUS_BLOCKED;
    } else if (riskScore >= 50) {
      status = AmlTransactionStatus.TRANSACTION_STATUS_FLAGGED;
    } else if (riskScore >= 25) {
      status = AmlTransactionStatus.TRANSACTION_STATUS_PENDING_REVIEW;
    }

    const riskLevel =
      riskScore >= 80
        ? AmlRiskLevel.RISK_LEVEL_CRITICAL
        : riskScore >= 60
        ? AmlRiskLevel.RISK_LEVEL_HIGH
        : riskScore >= 30
        ? AmlRiskLevel.RISK_LEVEL_MEDIUM
        : AmlRiskLevel.RISK_LEVEL_LOW;

    const processingTime = Date.now() - startTime;
    this.logger.log(
      `Enhanced AML evaluation completed for ${request.transactionId}: ` +
        `score=${riskScore}, level=${riskLevel}, status=${status}, alerts=${alerts.length}, time=${processingTime}ms`,
    );

    return {
      riskScore: Math.min(riskScore, 100),
      riskLevel,
      status,
      alerts,
      evaluationId: requestId,
    };
  }

  /**
   * Health check for AML service
   */
  async isHealthy(): Promise<boolean> {
    return true; // Enhanced mock implementation always returns healthy
  }

  // ============================================================================
  // PRIVATE RISK ASSESSMENT METHODS
  // ============================================================================

  private assessAmountRisk(amount: number): { score: number; alert?: any } {
    if (amount >= 1000000) {
      return {
        score: 60,
        alert: {
          type: AlertType.ALERT_TYPE_AMOUNT_THRESHOLD,
          severity: AlertSeverity.ALERT_SEVERITY_CRITICAL,
          message: `Very high amount transaction: $${amount.toLocaleString()}`,
          ruleId: 'AMOUNT_1M',
          confidence: 0.95,
        },
      };
    } else if (amount >= 500000) {
      return {
        score: 40,
        alert: {
          type: AlertType.ALERT_TYPE_AMOUNT_THRESHOLD,
          severity: AlertSeverity.ALERT_SEVERITY_HIGH,
          message: `High amount transaction: $${amount.toLocaleString()}`,
          ruleId: 'AMOUNT_500K',
          confidence: 0.9,
        },
      };
    } else if (amount >= 100000) {
      return {
        score: 25,
        alert: {
          type: AlertType.ALERT_TYPE_AMOUNT_THRESHOLD,
          severity: AlertSeverity.ALERT_SEVERITY_MEDIUM,
          message: `Medium-high amount transaction: $${amount.toLocaleString()}`,
          ruleId: 'AMOUNT_100K',
          confidence: 0.8,
        },
      };
    } else if (amount >= 10000) {
      return {
        score: 10,
        alert: {
          type: AlertType.ALERT_TYPE_AMOUNT_THRESHOLD,
          severity: AlertSeverity.ALERT_SEVERITY_LOW,
          message: `Reportable amount transaction: $${amount.toLocaleString()}`,
          ruleId: 'AMOUNT_10K',
          confidence: 0.7,
        },
      };
    }

    return { score: 0 };
  }

  private assessTimeRisk(timestamp: string): { score: number; alert?: any } {
    const date = new Date(timestamp);
    const hour = date.getHours();
    const dayOfWeek = date.getDay();

    // Weekend risk
    if (dayOfWeek === 0 || dayOfWeek === 6) {
      return {
        score: 10,
        alert: {
          type: AlertType.ALERT_TYPE_TIME_ANOMALY,
          severity: AlertSeverity.ALERT_SEVERITY_LOW,
          message: 'Transaction on weekend',
          ruleId: 'TIME_WEEKEND',
          confidence: 0.6,
        },
      };
    }

    // Late night/early morning risk
    if (hour < 6 || hour > 22) {
      return {
        score: 15,
        alert: {
          type: AlertType.ALERT_TYPE_TIME_ANOMALY,
          severity: AlertSeverity.ALERT_SEVERITY_MEDIUM,
          message: `Transaction at unusual hour: ${hour}:00`,
          ruleId: 'TIME_UNUSUAL',
          confidence: 0.75,
        },
      };
    }

    return { score: 0 };
  }

  private assessGeographicRisk(metadata?: Record<string, any>): { score: number; alert?: any } {
    const countryCode = metadata?.countryCode;
    if (!countryCode) return { score: 0 };

    const highRiskCountries = ['CN', 'RU', 'IR', 'KP', 'AF', 'SY', 'MM'];
    const mediumRiskCountries = ['PK', 'BD', 'LK', 'VE', 'CU'];

    if (highRiskCountries.includes(countryCode)) {
      return {
        score: 35,
        alert: {
          type: AlertType.ALERT_TYPE_GEOGRAPHIC_ANOMALY,
          severity: AlertSeverity.ALERT_SEVERITY_HIGH,
          message: `Transaction from high-risk jurisdiction: ${countryCode}`,
          ruleId: 'GEO_HIGH_RISK',
          confidence: 0.9,
        },
      };
    } else if (mediumRiskCountries.includes(countryCode)) {
      return {
        score: 20,
        alert: {
          type: AlertType.ALERT_TYPE_GEOGRAPHIC_ANOMALY,
          severity: AlertSeverity.ALERT_SEVERITY_MEDIUM,
          message: `Transaction from medium-risk jurisdiction: ${countryCode}`,
          ruleId: 'GEO_MEDIUM_RISK',
          confidence: 0.75,
        },
      };
    }

    return { score: 0 };
  }

  private assessVelocityRisk(request: ProcessTransactionRequestDto): { score: number; alert?: any } {
    // Mock velocity assessment based on transaction characteristics
    const hour = new Date(request.timestamp).getHours();

    // Simulate high velocity during business hours for large amounts
    if (request.amount > 50000 && hour >= 9 && hour <= 17) {
      return {
        score: 20,
        alert: {
          type: AlertType.ALERT_TYPE_VELOCITY_CHECK,
          severity: AlertSeverity.ALERT_SEVERITY_MEDIUM,
          message: 'High-value transaction during peak hours (velocity risk)',
          ruleId: 'VELOCITY_PEAK',
          confidence: 0.65,
        },
      };
    }

    return { score: 0 };
  }

  private assessPatternRisk(request: ProcessTransactionRequestDto): { score: number; alert?: any } {
    // Check for round number patterns
    if (request.amount % 10000 === 0 && request.amount >= 50000) {
      return {
        score: 15,
        alert: {
          type: AlertType.ALERT_TYPE_PATTERN_DETECTION,
          severity: AlertSeverity.ALERT_SEVERITY_MEDIUM,
          message: `Suspicious round number pattern: $${request.amount.toLocaleString()}`,
          ruleId: 'PATTERN_ROUND',
          confidence: 0.7,
        },
      };
    }

    // Check for structuring patterns (just below reporting thresholds)
    const thresholds = [10000, 5000, 3000];
    for (const threshold of thresholds) {
      if (request.amount > threshold * 0.95 && request.amount < threshold) {
        return {
          score: 30,
          alert: {
            type: AlertType.ALERT_TYPE_PATTERN_DETECTION,
            severity: AlertSeverity.ALERT_SEVERITY_HIGH,
            message: `Potential structuring: $${request.amount.toLocaleString()} just below $${threshold.toLocaleString()} threshold`,
            ruleId: 'PATTERN_STRUCTURE',
            confidence: 0.85,
          },
        };
      }
    }

    return { score: 0 };
  }

  private assessCustomerRisk(request: ProcessTransactionRequestDto): { score: number; alert?: any } {
    // Mock customer risk assessment based on metadata
    const metadata = request.metadata || {};

    // Check for high-risk customer indicators
    if (metadata.customerType === 'high_risk' || metadata.isPEP === true) {
      return {
        score: 25,
        alert: {
          type: AlertType.ALERT_TYPE_COMPLIANCE,
          severity: AlertSeverity.ALERT_SEVERITY_HIGH,
          message: 'Transaction from high-risk customer profile',
          ruleId: 'CUSTOMER_HIGH_RISK',
          confidence: 0.8,
        },
      };
    }

    // Check for unverified KYC
    if (metadata.kycStatus !== 'verified') {
      return {
        score: 15,
        alert: {
          type: AlertType.ALERT_TYPE_COMPLIANCE,
          severity: AlertSeverity.ALERT_SEVERITY_MEDIUM,
          message: 'Transaction from customer with unverified KYC',
          ruleId: 'CUSTOMER_KYC',
          confidence: 0.75,
        },
      };
    }

    return { score: 0 };
  }
}
