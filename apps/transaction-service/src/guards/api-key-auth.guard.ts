import { Injectable, CanActivate, ExecutionContext, UnauthorizedException, Logger } from '@nestjs/common';
import { Request } from 'express';

/**
 * Mock API Key Authentication Guard
 * Validates API keys for tenant authentication (mocked implementation)
 */
@Injectable()
export class ApiKeyAuthGuard implements CanActivate {
  private readonly logger = new Logger(ApiKeyAuthGuard.name);

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request & { tenantId?: string }>();
    
    // Extract API key from Authorization header
    const authHeader = request.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedException('API key is required');
    }

    const apiKey = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // Mock API key validation
    const tenantId = await this.validateApiKey(apiKey);
    if (!tenantId) {
      throw new UnauthorizedException('Invalid API key');
    }

    // Set tenant context for the request
    request.tenantId = tenantId;
    
    this.logger.debug(`API key validated for tenant: ${tenantId}`);
    return true;
  }

  /**
   * Mock API key validation
   * In real implementation, this would query the tenant database
   */
  private async validateApiKey(apiKey: string): Promise<string | null> {
    // Mock validation logic - accept any API key that starts with 'qeep_'
    if (!apiKey.startsWith('qeep_')) {
      return null;
    }

    // Extract tenant ID from API key (mock format: qeep_tenant_<tenantId>_<random>)
    const parts = apiKey.split('_');
    if (parts.length >= 3 && parts[1] === 'tenant') {
      const tenantId = parts[2];
      
      // Mock tenant validation - accept any tenant ID
      if (tenantId && tenantId.length > 0) {
        return tenantId;
      }
    }

    return null;
  }
}
