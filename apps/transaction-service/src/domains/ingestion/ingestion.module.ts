import { Module } from '@nestjs/common';
import { AMLServiceClient } from '../../clients/aml-service.client';
import { TransactionPrismaService } from '../../database/prisma.service';
import { TransactionAlertRepository } from '../../repositories/transaction-alert.repository';
import { TransactionRepository } from '../../repositories/transaction.repository';
import { TransactionIngestionController } from './controllers/transaction-ingestion.controller';
import { TransactionIngestionService } from './services/transaction-ingestion.service';

/**
 * Ingestion Module
 * Handles transaction ingestion and real-time processing
 */
@Module({
  controllers: [TransactionIngestionController],
  providers: [TransactionIngestionService, TransactionPrismaService, TransactionRepository, TransactionAlertRepository, AMLServiceClient],
  exports: [TransactionIngestionService],
})
export class IngestionModule {}
