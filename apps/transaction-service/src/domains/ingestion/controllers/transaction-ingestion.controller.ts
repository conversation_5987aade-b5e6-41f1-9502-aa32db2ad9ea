import { Body, Controller, Logger, Post, UseGuards } from '@nestjs/common';
import { ResponseUtil, StandardApiResponse, TenantId, ZodValidationPipe } from '@qeep/common';
import { 
  ProcessTransactionRequestDto, 
  ProcessTransactionResponseDto,
  ProcessTransactionRequestSchema
} from '@qeep/contracts';
import { ApiKeyAuthGuard } from '../../../guards/api-key-auth.guard';
import { TransactionIngestionService } from '../services/transaction-ingestion.service';

/**
 * Transaction Ingestion Controller
 * Handles synchronous transaction processing requests from tenants
 */
@Controller('transactions')
@UseGuards(ApiKeyAuthGuard)
export class TransactionIngestionController {
  private readonly logger = new Logger(TransactionIngestionController.name);

  constructor(private readonly transactionIngestionService: TransactionIngestionService) {}

  /**
   * Process a transaction synchronously
   *
   * @param request - Transaction processing request
   * @param tenantId - Tenant ID extracted from API key
   * @returns Synchronous transaction processing response
   */
  @Post('process')
  async processTransaction(
    @Body(new ZodValidationPipe(ProcessTransactionRequestSchema))
    request: ProcessTransactionRequestDto,
    @TenantId() tenantId: string,
  ): Promise<StandardApiResponse<ProcessTransactionResponseDto>> {
    this.logger.log(`Received transaction processing request from tenant ${tenantId}: ${request.transactionId}`);

    try {
      const result = await this.transactionIngestionService.processTransaction(tenantId, request);

      if (result.success) {
        return ResponseUtil.success(result, `Transaction ${request.transactionId} processed successfully`);
      } else {
        return ResponseUtil.badRequest(`Transaction ${request.transactionId} processing failed`, result);
      }
    } catch (error) {
      this.logger.error(`Failed to process transaction ${request.transactionId}:`, error);

      return ResponseUtil.error(error instanceof Error ? error.message : 'Unknown error', 500, 'TRANSACTION_PROCESSING_ERROR', { transactionId: request.transactionId });
    }
  }
}
