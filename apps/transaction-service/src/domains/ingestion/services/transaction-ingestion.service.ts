import { Injectable, Logger } from '@nestjs/common';
import { TransactionStatus as PrismaTransactionStatus } from '@prisma/transaction-client';
import { generateTransactionId } from '@qeep/common';
import { ProcessTransactionRequestDto, ProcessTransactionResponseDto, Transaction, TransactionAlertSeverity, TransactionAlertType } from '@qeep/contracts/transaction';
import { AlertSeverity, AlertType, TransactionStatus as AmlTransactionStatus } from '@qeep/proto';
import { AMLServiceClient } from '../../../clients/aml-service.client';
import { TransactionAlertRepository } from '../../../repositories/transaction-alert.repository';
import { TransactionRepository } from '../../../repositories/transaction.repository';

// Use the correct TransactionStatus enum from the transaction domain
const TransactionStatus = Transaction.Enums.TransactionStatus;
type TransactionStatusType = Transaction.Enums.TransactionStatus;

@Injectable()
export class TransactionIngestionService {
  private readonly logger = new Logger(TransactionIngestionService.name);

  constructor(
    private readonly transactionRepository: TransactionRepository,
    private readonly transactionAlertRepository: TransactionAlertRepository,
    private readonly amlServiceClient: AMLServiceClient,
  ) {}

  /**
   * Process a transaction synchronously
   * This is the main entry point for transaction ingestion
   */
  async processTransaction(tenantId: string, request: ProcessTransactionRequestDto): Promise<ProcessTransactionResponseDto> {
    const startTime = Date.now();
    const internalTransactionId = generateTransactionId();

    this.logger.log(`Processing transaction ${request.transactionId} for tenant ${tenantId}`);

    try {
      // Step 1: Validate transaction data (basic validation already done by schema)
      this.validateTransactionData(request);

      // Step 2: Perform risk evaluation (mocked for now)
      const riskEvaluation = await this.evaluateTransactionRisk(request);

      // Step 3: Store transaction (mocked for now)
      await this.storeTransaction(tenantId, internalTransactionId, request, riskEvaluation);

      // Step 4: Generate response
      const processingTime = Date.now() - startTime;

      const response: ProcessTransactionResponseDto = {
        success: true,
        transactionId: internalTransactionId,
        status: riskEvaluation.status,
        riskScore: riskEvaluation.riskScore,
        alerts: riskEvaluation.alerts,
        processingTime,
        timestamp: new Date().toISOString(),
      };

      this.logger.log(`Transaction ${request.transactionId} processed successfully with status ${riskEvaluation.status}`);
      return response;
    } catch (error) {
      this.logger.error(`Failed to process transaction ${request.transactionId}:`, error);

      // Return error response
      const processingTime = Date.now() - startTime;
      return {
        success: false,
        transactionId: internalTransactionId,
        status: TransactionStatus.BLOCKED,
        riskScore: 100,
        alerts: [
          {
            type: TransactionAlertType.COMPLIANCE,
            severity: TransactionAlertSeverity.CRITICAL,
            message: 'Transaction processing failed due to system error',
          },
        ],
        processingTime,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Validate transaction data for business rules
   */
  private validateTransactionData(request: ProcessTransactionRequestDto): void {
    // Basic business validation
    if (request.amount <= 0) {
      throw new Error('Transaction amount must be positive');
    }

    if (request.fromAccount.accountId === request.toAccount.accountId) {
      throw new Error('Source and destination accounts cannot be the same');
    }

    // Add more validation rules as needed
  }

  /**
   * Evaluate transaction risk using AML service
   * Calls the real AML service via gRPC with fallback logic
   */
  private async evaluateTransactionRisk(request: ProcessTransactionRequestDto): Promise<{
    status: TransactionStatusType;
    riskScore: number;
    alerts: Array<{
      type: TransactionAlertType;
      severity: TransactionAlertSeverity;
      message: string;
    }>;
  }> {
    this.logger.log(`Evaluating transaction risk for ${request.transactionId}`);

    try {
      // Call AML service for risk evaluation
      const amlResult = await this.amlServiceClient.evaluateTransactionRisk(
        'default-tenant', // TODO: Get from request context
        request,
        {
          includeHistoricalAnalysis: true,
          includePatternDetection: true,
          timeoutMs: 5000,
        },
      );

      // Transform AML response to internal format
      const alerts = amlResult.alerts.map((alert) => ({
        type: this.mapAmlAlertTypeToContract(alert.type),
        severity: this.mapAmlAlertSeverityToContract(alert.severity),
        message: alert.message,
      }));

      const status = this.mapAmlStatusToContract(amlResult.status);

      this.logger.log(`AML evaluation completed for ${request.transactionId}: score=${amlResult.riskScore}, status=${status}`);

      return {
        status,
        riskScore: amlResult.riskScore,
        alerts,
      };
    } catch (error) {
      this.logger.error(`AML service evaluation failed for ${request.transactionId}:`, error);

      // Fallback to basic risk evaluation
      return this.performBasicRiskEvaluation(request);
    }
  }

  /**
   * Fallback risk evaluation when AML service is unavailable
   */
  private performBasicRiskEvaluation(request: ProcessTransactionRequestDto): {
    status: TransactionStatusType;
    riskScore: number;
    alerts: Array<{
      type: TransactionAlertType;
      severity: TransactionAlertSeverity;
      message: string;
    }>;
  } {
    this.logger.warn(`Using basic risk evaluation for ${request.transactionId}`);

    const alerts = [];
    let riskScore = 0;
    let status = TransactionStatus.APPROVED;

    // Basic amount-based risk assessment
    if (request.amount > 50000) {
      alerts.push({
        type: TransactionAlertType.AMOUNT_THRESHOLD,
        severity: TransactionAlertSeverity.HIGH,
        message: 'High amount transaction detected',
      });
      riskScore += 60;
    } else if (request.amount > 10000) {
      alerts.push({
        type: TransactionAlertType.AMOUNT_THRESHOLD,
        severity: TransactionAlertSeverity.MEDIUM,
        message: 'Medium amount transaction detected',
      });
      riskScore += 30;
    }

    // Time-based check
    const hour = new Date(request.timestamp).getHours();
    if (hour < 6 || hour > 22) {
      alerts.push({
        type: TransactionAlertType.TIME_ANOMALY,
        severity: TransactionAlertSeverity.LOW,
        message: 'Transaction occurred outside normal hours',
      });
      riskScore += 10;
    }

    // Determine status based on risk score
    if (riskScore >= 70) {
      status = TransactionStatus.BLOCKED;
    } else if (riskScore >= 30) {
      status = TransactionStatus.FLAGGED;
    }

    return {
      status,
      riskScore,
      alerts,
    };
  }

  /**
   * Map AML alert type to contract alert type
   */
  private mapAmlAlertTypeToContract(amlType: AlertType): TransactionAlertType {
    switch (amlType) {
      case AlertType.ALERT_TYPE_AMOUNT_THRESHOLD:
        return TransactionAlertType.AMOUNT_THRESHOLD;
      case AlertType.ALERT_TYPE_VELOCITY_CHECK:
        return TransactionAlertType.VELOCITY_CHECK;
      case AlertType.ALERT_TYPE_TIME_ANOMALY:
        return TransactionAlertType.TIME_ANOMALY;
      case AlertType.ALERT_TYPE_GEOGRAPHIC_ANOMALY:
        return TransactionAlertType.GEOGRAPHIC_ANOMALY;
      case AlertType.ALERT_TYPE_SANCTIONS_MATCH:
        return TransactionAlertType.FRAUD; // Map to FRAUD since SANCTIONS_MATCH doesn't exist in contract
      case AlertType.ALERT_TYPE_PEP_MATCH:
        return TransactionAlertType.ACCOUNT_RISK;
      case AlertType.ALERT_TYPE_ADVERSE_MEDIA:
        return TransactionAlertType.ACCOUNT_RISK;
      case AlertType.ALERT_TYPE_COMPLIANCE:
        return TransactionAlertType.COMPLIANCE;
      case AlertType.ALERT_TYPE_PATTERN_DETECTION:
        return TransactionAlertType.SUSPICIOUS_PATTERN;
      case AlertType.ALERT_TYPE_BLACKLIST_MATCH:
        return TransactionAlertType.FRAUD;
      default:
        return TransactionAlertType.COMPLIANCE;
    }
  }

  /**
   * Map AML alert severity to contract alert severity
   */
  private mapAmlAlertSeverityToContract(amlSeverity: AlertSeverity): TransactionAlertSeverity {
    switch (amlSeverity) {
      case AlertSeverity.ALERT_SEVERITY_LOW:
        return TransactionAlertSeverity.LOW;
      case AlertSeverity.ALERT_SEVERITY_MEDIUM:
        return TransactionAlertSeverity.MEDIUM;
      case AlertSeverity.ALERT_SEVERITY_HIGH:
        return TransactionAlertSeverity.HIGH;
      case AlertSeverity.ALERT_SEVERITY_CRITICAL:
        return TransactionAlertSeverity.CRITICAL;
      default:
        return TransactionAlertSeverity.LOW;
    }
  }

  /**
   * Map AML transaction status to contract transaction status
   */
  private mapAmlStatusToContract(amlStatus: AmlTransactionStatus): TransactionStatusType {
    switch (amlStatus) {
      case AmlTransactionStatus.TRANSACTION_STATUS_APPROVED:
        return TransactionStatus.APPROVED;
      case AmlTransactionStatus.TRANSACTION_STATUS_FLAGGED:
        return TransactionStatus.FLAGGED;
      case AmlTransactionStatus.TRANSACTION_STATUS_BLOCKED:
        return TransactionStatus.BLOCKED;
      case AmlTransactionStatus.TRANSACTION_STATUS_PENDING_REVIEW:
        return TransactionStatus.FLAGGED; // Map pending review to flagged
      default:
        return TransactionStatus.APPROVED;
    }
  }

  /**
   * Map contract transaction status to Prisma transaction status
   */
  private mapContractStatusToPrisma(contractStatus: TransactionStatusType): PrismaTransactionStatus {
    switch (contractStatus) {
      case TransactionStatus.APPROVED:
        return PrismaTransactionStatus.APPROVED;
      case TransactionStatus.FLAGGED:
        return PrismaTransactionStatus.FLAGGED;
      case TransactionStatus.BLOCKED:
        return PrismaTransactionStatus.BLOCKED;
      default:
        return PrismaTransactionStatus.APPROVED;
    }
  }

  /**
   * Store transaction in database with alerts
   * Uses database transaction to ensure data consistency
   */
  private async storeTransaction(
    tenantId: string,
    internalTransactionId: string,
    request: ProcessTransactionRequestDto,
    riskEvaluation: {
      status: TransactionStatusType;
      riskScore: number;
      alerts: Array<{
        type: TransactionAlertType;
        severity: TransactionAlertSeverity;
        message: string;
      }>;
    },
  ): Promise<void> {
    this.logger.debug(`Storing transaction ${internalTransactionId} for tenant ${tenantId}`);

    try {
      // Create transaction record
      const transaction = await this.transactionRepository.create({
        tenantId,
        externalId: request.transactionId,
        amount: request.amount,
        currency: request.currency,
        fromAccountId: request.fromAccount.accountId,
        fromCustomerId: request.fromAccount.customerId,
        fromAccountType: request.fromAccount.accountType,
        toAccountId: request.toAccount.accountId,
        toCustomerId: request.toAccount.customerId,
        toAccountType: request.toAccount.accountType,
        description: request.description,
        metadata: request.metadata,
        status: this.mapContractStatusToPrisma(riskEvaluation.status),
        riskScore: riskEvaluation.riskScore,
        processingTime: 0, // Will be updated later
        transactionTime: new Date(request.timestamp),
      });

      // Create alerts if any exist
      if (riskEvaluation.alerts && riskEvaluation.alerts.length > 0) {
        const alertsData = riskEvaluation.alerts.map((alert) => ({
          transactionId: transaction.id,
          type: alert.type,
          severity: alert.severity,
          message: alert.message,
        }));

        await this.transactionAlertRepository.createMany(alertsData);
      }

      this.logger.log(`Transaction ${internalTransactionId} stored successfully with ${riskEvaluation.alerts.length} alerts`);
    } catch (error) {
      this.logger.error(`Failed to store transaction ${internalTransactionId}:`, error);
      throw new Error(`Database storage failed: ${error.message}`);
    }
  }
}
