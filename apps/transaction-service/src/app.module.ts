import { Modu<PERSON> } from '@nestjs/common';
import { CommonModule } from '@qeep/common';
import { AppController } from './controllers/app.controller';
import { IngestionModule } from './domains/ingestion';
import { AppService } from './services/app.service';
import { TransactionPrismaService } from './database/prisma.service';
import { TransactionRepository } from './repositories/transaction.repository';
import { TransactionAlertRepository } from './repositories/transaction-alert.repository';

@Module({
  imports: [CommonModule.forRoot(), IngestionModule],
  controllers: [AppController],
  providers: [
    AppService,
    TransactionPrismaService,
    TransactionRepository,
    TransactionAlertRepository,
  ],
  exports: [
    TransactionPrismaService,
    TransactionRepository,
    TransactionAlertRepository,
  ],
})
export class AppModule {}
