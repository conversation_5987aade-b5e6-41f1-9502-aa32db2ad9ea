/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import { Logger, VersioningType } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@qeep/common';
// import { ProtoConfigService } from '@qeep/common';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Dynamically import ConfigService from lazy-loaded common library
  const configService = app.get(ConfigService);
  // const protoConfigService = app.get(ProtoConfigService);

  // Global prefix for all routes
  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);

  // Enable API versioning
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
    prefix: 'v',
  });

  // Use specific port for Auth Service HTTP
  const httpPort = configService.getServicePort('transaction-service');
  const host = configService.getServiceHost('transaction-service');

  // Start HTTP server
  await app.listen(httpPort);

  Logger.log(`🚀 Transaction Service is running on: http://${host}:${httpPort}/${globalPrefix}`);
  Logger.log(`📚 Environment: ${configService.getNodeEnv()}`);
  Logger.log(`🔧 Log Level: ${configService.getLogLevel()}`);
}

bootstrap();
