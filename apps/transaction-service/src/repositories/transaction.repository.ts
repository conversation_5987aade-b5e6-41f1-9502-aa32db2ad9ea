import { Injectable } from '@nestjs/common';
import { Prisma, Transaction, TransactionStatus } from '@prisma/transaction-client';
import { CUID_PREFIXES, generateCuid } from '@qeep/common';
import { TransactionPrismaService } from '../database/prisma.service';

export interface CreateTransactionData {
  tenantId: string;
  externalId: string;
  amount: number;
  currency: string;
  fromAccountId: string;
  fromCustomerId: string;
  fromAccountType: string;
  toAccountId: string;
  toCustomerId: string;
  toAccountType: string;
  description?: string;
  metadata?: Record<string, any>;
  status?: TransactionStatus;
  riskScore?: number;
  processingTime: number;
  transactionTime: Date;
}

export interface UpdateTransactionData {
  status?: TransactionStatus;
  riskScore?: number;
  processingTime?: number;
  metadata?: any;
}

@Injectable()
export class TransactionRepository {
  constructor(private readonly prisma: TransactionPrismaService) {}

  /**
   * Create a new transaction with CUID-prefixed ID
   */
  async create(data: CreateTransactionData): Promise<Transaction> {
    return this.prisma.transaction.create({
      data: {
        id: generateCuid(CUID_PREFIXES.TRANSACTION),
        tenantId: data.tenantId,
        externalId: data.externalId,
        amount: data.amount,
        currency: data.currency,
        fromAccountId: data.fromAccountId,
        fromCustomerId: data.fromCustomerId,
        fromAccountType: data.fromAccountType,
        toAccountId: data.toAccountId,
        toCustomerId: data.toCustomerId,
        toAccountType: data.toAccountType,
        description: data.description,
        metadata: data.metadata,
        status: data.status || TransactionStatus.APPROVED,
        riskScore: data.riskScore || 0,
        processingTime: data.processingTime,
        transactionTime: data.transactionTime,
      },
      include: {
        alerts: true,
      },
    });
  }

  /**
   * Find transaction by ID
   */
  async findById(id: string): Promise<Transaction | null> {
    return this.prisma.transaction.findUnique({
      where: { id },
      include: {
        alerts: true,
      },
    });
  }

  /**
   * Find transaction by external ID and tenant ID
   */
  async findByExternalId(tenantId: string, externalId: string): Promise<Transaction | null> {
    return this.prisma.transaction.findUnique({
      where: {
        tenantId_externalId: {
          tenantId,
          externalId,
        },
      },
      include: {
        alerts: true,
      },
    });
  }

  /**
   * Update transaction status and other fields
   */
  async updateStatus(id: string, data: UpdateTransactionData): Promise<Transaction> {
    return this.prisma.transaction.update({
      where: { id },
      data: {
        status: data.status,
        riskScore: data.riskScore,
        processingTime: data.processingTime,
        metadata: data.metadata,
        updatedAt: new Date(),
      },
      include: {
        alerts: true,
      },
    });
  }

  /**
   * Find transactions by tenant with pagination and filters
   */
  async findByTenant(
    tenantId: string,
    options: {
      skip?: number;
      take?: number;
      status?: TransactionStatus;
      fromDate?: Date;
      toDate?: Date;
      orderBy?: Prisma.TransactionOrderByWithRelationInput;
    } = {},
  ): Promise<Transaction[]> {
    const where: Prisma.TransactionWhereInput = {
      tenantId,
    };

    if (options.status) {
      where.status = options.status;
    }

    if (options.fromDate || options.toDate) {
      where.transactionTime = {};
      if (options.fromDate) {
        where.transactionTime.gte = options.fromDate;
      }
      if (options.toDate) {
        where.transactionTime.lte = options.toDate;
      }
    }

    return this.prisma.transaction.findMany({
      where,
      skip: options.skip,
      take: options.take,
      orderBy: options.orderBy || { transactionTime: 'desc' },
      include: {
        alerts: true,
      },
    });
  }

  /**
   * Count transactions by tenant with filters
   */
  async countByTenant(
    tenantId: string,
    options: {
      status?: TransactionStatus;
      fromDate?: Date;
      toDate?: Date;
    } = {},
  ): Promise<number> {
    const where: Prisma.TransactionWhereInput = {
      tenantId,
    };

    if (options.status) {
      where.status = options.status;
    }

    if (options.fromDate || options.toDate) {
      where.transactionTime = {};
      if (options.fromDate) {
        where.transactionTime.gte = options.fromDate;
      }
      if (options.toDate) {
        where.transactionTime.lte = options.toDate;
      }
    }

    return this.prisma.transaction.count({ where });
  }
}
