/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable } from '@nestjs/common';
import { TransactionAlert, TransactionAlertSeverity, TransactionAlertType } from '@prisma/transaction-client';
import { CUID_PREFIXES, generateCuid } from '@qeep/common';
import { TransactionPrismaService } from '../database/prisma.service';

export interface CreateTransactionAlertData {
  transactionId: string;
  type: TransactionAlertType;
  severity: TransactionAlertSeverity;
  message: string;
}

@Injectable()
export class TransactionAlertRepository {
  constructor(private readonly prisma: TransactionPrismaService) {}

  /**
   * Create a single transaction alert
   */
  async create(data: CreateTransactionAlertData): Promise<TransactionAlert> {
    return this.prisma.transactionAlert.create({
      data: {
        id: generateCuid(CUID_PREFIXES.TRANSACTION_ALERT),
        transactionId: data.transactionId,
        type: data.type,
        severity: data.severity,
        message: data.message,
      },
    });
  }

  /**
   * Create multiple transaction alerts in bulk
   */
  async createMany(alerts: CreateTransactionAlertData[]): Promise<void> {
    const alertsWithIds = alerts.map((alert) => ({
      id: generateCuid(CUID_PREFIXES.TRANSACTION_ALERT),
      transactionId: alert.transactionId,
      type: alert.type,
      severity: alert.severity,
      message: alert.message,
    }));

    await this.prisma.transactionAlert.createMany({
      data: alertsWithIds,
    });
  }

  /**
   * Find alerts by transaction ID
   */
  async findByTransactionId(transactionId: string): Promise<TransactionAlert[]> {
    return this.prisma.transactionAlert.findMany({
      where: { transactionId },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Find alerts by type and severity with pagination
   */
  async findByTypeAndSeverity(
    type?: TransactionAlertType,
    severity?: TransactionAlertSeverity,
    options: {
      skip?: number;
      take?: number;
      fromDate?: Date;
      toDate?: Date;
    } = {},
  ): Promise<TransactionAlert[]> {
    const where: any = {};

    if (type) {
      where.type = type;
    }

    if (severity) {
      where.severity = severity;
    }

    if (options.fromDate || options.toDate) {
      where.createdAt = {};
      if (options.fromDate) {
        where.createdAt.gte = options.fromDate;
      }
      if (options.toDate) {
        where.createdAt.lte = options.toDate;
      }
    }

    return this.prisma.transactionAlert.findMany({
      where,
      skip: options.skip,
      take: options.take,
      orderBy: { createdAt: 'desc' },
      include: {
        transaction: {
          select: {
            id: true,
            tenantId: true,
            externalId: true,
            amount: true,
            currency: true,
            status: true,
            riskScore: true,
          },
        },
      },
    });
  }

  /**
   * Count alerts by type and severity
   */
  async countByTypeAndSeverity(
    type?: TransactionAlertType,
    severity?: TransactionAlertSeverity,
    options: {
      fromDate?: Date;
      toDate?: Date;
    } = {},
  ): Promise<number> {
    const where: any = {};

    if (type) {
      where.type = type;
    }

    if (severity) {
      where.severity = severity;
    }

    if (options.fromDate || options.toDate) {
      where.createdAt = {};
      if (options.fromDate) {
        where.createdAt.gte = options.fromDate;
      }
      if (options.toDate) {
        where.createdAt.lte = options.toDate;
      }
    }

    return this.prisma.transactionAlert.count({ where });
  }
}
