generator client {
  provider = "prisma-client-js"
  output   = "../../../node_modules/.prisma/transaction-client"
}

datasource db {
  provider = "postgresql"
  url      = env("TRANSACTION_DATABASE_URL")
}

// Transaction Service Prisma Schema
// Manages transaction data, processing results, and audit trails

enum TransactionStatus {
  APPROVED
  FLAGGED
  BLOCKED
}

enum TransactionAlertSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum TransactionAlertType {
  VELOCITY_CHECK
  AMOUNT_THRESHOLD
  SUSPICIOUS_PATTERN
  GEOGRAPHIC_ANOMALY
  TIME_ANOMALY
  ACCOUNT_RISK
  COMPLIANCE
  FRAUD
}

// ============================================================================
// TRANSACTION MANAGEMENT
// ============================================================================

model Transaction {
  id              String            @id @db.VarChar(35)
  tenantId        String            @map("tenant_id") @db.VarChar(35)
  externalId      String            @map("external_id") @db.VarChar(255)
  amount          Decimal           @db.Decimal(15, 2)
  currency        String            @db.VarChar(3)
  fromAccountId   String            @map("from_account_id") @db.VarChar(255)
  fromCustomerId  String            @map("from_customer_id") @db.VarChar(255)
  fromAccountType String            @map("from_account_type") @db.VarChar(50)
  toAccountId     String            @map("to_account_id") @db.VarChar(255)
  toCustomerId    String            @map("to_customer_id") @db.VarChar(255)
  toAccountType   String            @map("to_account_type") @db.VarChar(50)
  description     String?           @db.Text
  metadata        Json?
  status          TransactionStatus @default(APPROVED)
  riskScore       Int               @map("risk_score") @default(0)
  processingTime  Int               @map("processing_time") // milliseconds
  transactionTime DateTime          @map("transaction_time")
  createdAt       DateTime          @default(now()) @map("created_at")
  updatedAt       DateTime          @updatedAt @map("updated_at")

  // Relations
  alerts TransactionAlert[]

  @@unique([tenantId, externalId])
  @@index([tenantId])
  @@index([status])
  @@index([riskScore])
  @@index([transactionTime])
  @@map("transactions")
}

model TransactionAlert {
  id            String                   @id @db.VarChar(35)
  transactionId String                   @map("transaction_id") @db.VarChar(35)
  type          TransactionAlertType
  severity      TransactionAlertSeverity
  message       String                   @db.Text
  createdAt     DateTime                 @default(now()) @map("created_at")

  // Relations
  transaction Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)

  @@index([transactionId])
  @@index([type])
  @@index([severity])
  @@map("transaction_alerts")
}
