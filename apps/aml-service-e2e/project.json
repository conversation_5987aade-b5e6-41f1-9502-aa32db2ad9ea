{"name": "aml-service-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["aml-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "aml-service-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["aml-service:build", "aml-service:serve"]}}}