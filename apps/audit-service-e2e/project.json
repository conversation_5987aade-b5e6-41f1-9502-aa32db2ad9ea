{"name": "audit-service-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["audit-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/audit-service-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["audit-service:build", "audit-service:serve"]}}}