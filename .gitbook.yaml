# GitBook configuration for Qeep Backend Documentation

# Root directory for documentation
root: ./docs/

# Structure configuration
structure:
  readme: README.md
  summary: SUMMARY.md

# Format configuration
format: markdown

# Redirects (if needed)
redirects:
  # Example: old-path: new-path

# Git integration
git:
  # Branch to sync with GitBook
  branch: main

  # Paths to include/exclude
  include:
    - docs/**

  exclude:
    - node_modules/**
    - dist/**
    - .nx/**
    - tmp/**

# PDF generation settings
pdf:
  fontSize: 12
  paperSize: a4
  margin:
    top: 56
    bottom: 56
    left: 62
    right: 62
  headerTemplate: '<div style="font-size:9px; text-align:center; width:100%; margin:0 auto; color:#aaa;">Qeep Backend Documentation</div>'
  footerTemplate: '<div style="font-size:9px; text-align:center; width:100%; margin:0 auto; color:#aaa;">Page <span class="pageNumber"></span> of <span class="totalPages"></span></div>'
