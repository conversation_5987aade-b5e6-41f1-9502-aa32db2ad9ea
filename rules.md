# Qeep Platform Development Rules

This document outlines the coding standards, architectural patterns, and best practices for the Qeep platform - a microservices-based financial technology system built with TypeScript, NestJS, Nx monorepo, and gRPC communication.

**Important**: These rules are designed for active development of new features and services. This document does not address legacy system migration or maintenance scenarios.

## TypeScript General Guidelines

### Basic Principles

- **Language**: Use English for all code and documentation
- **Type Safety**: Always declare the type of each variable and function (parameters and return value)
  - Avoid using `any`
  - Create necessary types when needed
- **Documentation**: Use JSDoc to document public classes and methods
- **Code Structure**: Don't leave blank lines within a function
- **Module Design**: One export per file

### Nomenclature

- **Classes**: Use PascalCase
- **Variables, Functions, Methods**: Use camelCase
- **Files and Directories**: Use kebab-case
- **Environment Variables**: Use UPPERCASE
  - Avoid magic numbers and define constants
- **Function Names**: Start each function with a verb
- **Boolean Variables**: Use verbs (e.g., `isLoading`, `hasError`, `canDelete`)
- **Spelling**: Use complete words instead of abbreviations and correct spelling
  - **Exceptions for standard abbreviations**: API, URL, etc.
  - **Well-known abbreviations**:
    - `i`, `j` for loops
    - `err` for errors
    - `ctx` for contexts
    - `req`, `res`, `next` for middleware function parameters

### Functions

> Note: These guidelines apply to both functions and methods.

- **Length**: Write short functions with a single purpose (less than 20 instructions)
- **Naming**: Name functions with a verb and something else
  - If it returns a boolean: use `isX`, `hasX`, `canX`, etc.
  - If it doesn't return anything: use `executeX`, `saveX`, etc.
- **Structure**: Avoid nesting blocks by:
  - Early checks and returns
  - Extraction to utility functions
- **Higher-Order Functions**: Use `map`, `filter`, `reduce`, etc. to avoid function nesting
  - Use arrow functions for simple functions (less than 3 instructions)
  - Use named functions for non-simple functions
- **Parameters**: Use default parameter values instead of checking for null or undefined
- **RO-RO Pattern**: Reduce function parameters using Receive an Object, Return an Object
  - Use an object to pass multiple parameters
  - Use an object to return results
  - Declare necessary types for input arguments and output
- **Abstraction**: Use a single level of abstraction

### Data

- **Type Safety**: Don't abuse primitive types and encapsulate data in composite types
- **Validation**: Avoid data validations in functions and use classes with internal validation
- **Immutability**: Prefer immutability for data
  - Use `readonly` for data that doesn't change
  - Use `as const` for literals that don't change

### Classes

- **SOLID Principles**: Follow SOLID principles
- **Composition**: Prefer composition over inheritance
- **Contracts**: Declare interfaces to define contracts
- **Size**: Write small classes with a single purpose
  - Less than 200 instructions
  - Less than 10 public methods
  - Less than 10 properties

### Exceptions

- **Usage**: Use exceptions to handle errors you don't expect
- **Catching**: If you catch an exception, it should be to:
  - Fix an expected problem
  - Add context
  - Otherwise, use a global handler

### Testing

- **Convention**: Follow the Arrange-Act-Assert convention for tests
- **Variable Naming**: Name test variables clearly
  - Follow the convention: `inputX`, `mockX`, `actualX`, `expectedX`, etc.
- **Unit Tests**: Write unit tests for each public function
  - Use test doubles to simulate dependencies
  - Exception: Third-party dependencies that are not expensive to execute
- **Acceptance Tests**: Write acceptance tests for each module
  - Follow the Given-When-Then convention

## Qeep Platform Architecture

### Monorepo Structure (Nx)

- **Workspace**: Nx monorepo with multiple applications and shared libraries
- **Apps Directory**: Contains all microservices (`apps/`)
  - Each service has its own domain and responsibility
  - Services include: `auth-service`, `user-service`, `tenant-service`, `notification-service`, `audit-service`, `customer-service`, etc.
  - Each service has corresponding e2e test applications
- **Libs Directory**: Contains shared libraries (`libs/`)
  - `libs/common`: Shared utilities, configurations, and common functionality
  - `libs/contracts`: Centralized DTOs, interfaces, and schemas
  - `libs/types`: Proto-generated types and shared TypeScript types
- **Proto Directory**: Protocol Buffer definitions for gRPC communication

### Microservices Architecture

- **Service Communication**: gRPC for inter-service communication
- **API Gateway**: Nginx reverse proxy for external API access
- **Service Discovery**: Each service runs on dedicated ports (HTTP + gRPC)
- **Domain-Driven Design**: Each service owns its domain and data
- **Database Ownership**: Each service has its own dedicated database - no shared databases between services
- **Event-Driven**: Services communicate through events and gRPC calls

### Contracts Library (`libs/contracts`)

**All DTOs, interfaces, and schemas are centralized in the contracts library:**

- **Domain Organization**: Organized by business domains (auth, user, tenant, customer, etc.)
- **Zod Validation**: Runtime type checking with Zod schemas
- **TypeScript Interfaces**: Compile-time type safety
- **Shared Enums**: Consistent constants across services
- **API Response Formats**: Standardized response structures

**Contract Structure:**

```text
libs/contracts/src/
├── auth/           # Authentication contracts
├── user/           # User management contracts
├── tenant/         # Tenant management contracts
├── customer/       # Customer contracts
├── notification/   # Notification contracts
├── audit/          # Audit logging contracts
├── shared/         # Cross-domain shared contracts
└── index.ts        # Centralized exports
```

### Common Module (`libs/common`)

**Shared functionality across all services:**

- **Config**: Global configuration management
- **Decorators**: Custom NestJS decorators
- **JWT Validation**: Authentication middleware
- **RBAC**: Role-based access control
- **Rate Limiting**: Request throttling
- **Circuit Breaker**: Fault tolerance patterns
- **Telemetry**: Monitoring and observability
- **Security Headers**: HTTP security middleware
- **Response Transformation**: Standardized API responses
- **Tenant Management**: Multi-tenancy support
- **Service Proxy**: gRPC client abstractions

### gRPC Communication

- **Protocol Buffers**: Defined in `proto/` directory
- **Service-to-Service**: All internal communication uses gRPC
- **Type Generation**: Proto files generate TypeScript types in `libs/types`
- **Health Checks**: Common health check proto for all services
- **Dual Transport**: Each service runs both HTTP (external) and gRPC (internal)

### API Gateway (Nginx)

- **Reverse Proxy**: Nginx routes external requests to appropriate services
- **Load Balancing**: Distributes requests across service instances
- **Rate Limiting**: Request throttling at gateway level
- **SSL Termination**: HTTPS handling
- **Request Logging**: Centralized access logging

### Testing Strategy

- **BDD-First**: All endpoints must have corresponding `.feature` files in `features/` directory
- **Framework**: Jest for unit and integration testing
- **BDD Testing**: Cucumber for behavior-driven development tests (primary testing approach)
- **E2E Testing**: Dedicated e2e applications for each service
- **Service Testing**: Test each controller and service independently
- **Contract Testing**: Validate gRPC contracts between services
- **Infrastructure Testing**: Docker-based test environments
- **Smoke Tests**: Health check endpoints for service monitoring

**Test Organization:**

- **BDD Features**: `features/` directory with Gherkin scenarios (required for every endpoint)
- **Step Definitions**: Cucumber step implementations for BDD scenarios
- **Unit tests**: `*.spec.ts` files alongside source code
- **Integration tests**: `*.integration.spec.ts` files
- **E2E tests**: Separate applications (`*-service-e2e`)

**BDD Workflow:**

1. Write `.feature` file describing endpoint behavior
2. Get feature file approved by stakeholders
3. Implement Cucumber step definitions
4. Run BDD tests (should fail initially)
5. Implement endpoint to satisfy BDD scenarios
6. Verify all BDD tests pass

## Qeep Platform File Structure

```text
qeep-backend/
├── apps/                           # Microservices
│   ├── auth-service/              # Authentication service
│   ├── user-service/              # User management service
│   ├── tenant-service/            # Tenant management service
│   ├── notification-service/      # Notification service
│   ├── audit-service/             # Audit logging service
│   ├── customer-service/          # Customer management service
│   └── *-service-e2e/            # E2E test applications
├── libs/                          # Shared libraries
│   ├── common/                    # Shared utilities and middleware
│   │   └── src/lib/
│   │       ├── config/           # Configuration management
│   │       ├── decorators/       # Custom decorators
│   │       ├── jwt-validation/   # JWT middleware
│   │       ├── rbac/            # Role-based access control
│   │       ├── rate-limiting/   # Rate limiting
│   │       ├── circuit-breaker/ # Circuit breaker pattern
│   │       ├── telemetry/       # Monitoring and metrics
│   │       ├── security-headers/# Security middleware
│   │       ├── response-format/ # Response transformation
│   │       ├── tenant/          # Multi-tenancy support
│   │       ├── service-proxy/   # gRPC client abstractions
│   │       └── utils/           # Utility functions
│   ├── contracts/                 # Centralized contracts
│   │   └── src/
│   │       ├── auth/            # Auth DTOs and interfaces
│   │       ├── user/            # User DTOs and interfaces
│   │       ├── tenant/          # Tenant DTOs and interfaces
│   │       ├── customer/        # Customer DTOs and interfaces
│   │       ├── notification/    # Notification DTOs
│   │       ├── audit/           # Audit DTOs
│   │       └── shared/          # Cross-domain contracts
│   └── types/                     # Proto-generated types
├── proto/                         # Protocol Buffer definitions
│   ├── auth/                     # Auth service protos
│   ├── user/                     # User service protos
│   ├── tenant/                   # Tenant service protos
│   ├── notification/             # Notification service protos
│   └── common/                   # Shared protos (health checks)
├── infrastructure/                # Infrastructure as code
│   ├── docker/                   # Docker configurations
│   └── nginx/                    # Nginx configurations
├── features/                      # BDD test scenarios
├── scripts/                       # Development and deployment scripts
└── tests/                         # Shared test utilities
```

### Service Structure Template

```text
apps/[service-name]/
├── src/
│   ├── app/
│   │   ├── controllers/          # HTTP controllers
│   │   ├── services/             # Business logic
│   │   ├── grpc/                 # gRPC controllers
│   │   └── app.module.ts         # Service module
│   ├── main.ts                   # Service bootstrap
│   └── environments/             # Environment configs
├── project.json                  # Nx project configuration
└── tsconfig.json                 # TypeScript configuration
```

## Development Workflow

### BDD-First Development Process

**Every endpoint must follow this strict workflow:**

1. **Feature File Creation**: Create a `.feature` file in the `features/` directory describing the endpoint behavior using Gherkin syntax
2. **Feature Approval**: The feature file must be reviewed and approved before any implementation begins
3. **Contract Definition**: Define contracts in `libs/contracts` based on approved feature requirements
4. **Proto Definition**: Create/update proto files for gRPC communication
5. **Type Generation**: Run `pnpm proto:generate-nx` to generate types
6. **BDD Test Implementation**: Implement the BDD scenarios in Cucumber step definitions
7. **Service Implementation**: Implement business logic in service modules to satisfy BDD scenarios
8. **Testing**: Write additional unit and integration tests
9. **Documentation**: Update service documentation and API specs

**No endpoint implementation should begin without an approved .feature file.**

### Inter-Service Communication

1. **gRPC First**: Use gRPC for all service-to-service communication
2. **Contract Validation**: Ensure contracts are shared and validated
3. **Error Handling**: Implement proper gRPC error handling
4. **Circuit Breakers**: Use circuit breaker pattern for fault tolerance
5. **Monitoring**: Add telemetry and monitoring for service calls

### Deployment

1. **Build**: Use Nx build system (`nx build [service-name]`)
2. **Testing**: Run all test suites before deployment
3. **Infrastructure**: Use Docker containers for deployment
4. **Gateway**: Configure Nginx routes for new endpoints
5. **Monitoring**: Set up service monitoring and alerting

## Code Quality Checklist

### TypeScript & NestJS

- [ ] All variables and functions have explicit types
- [ ] Functions are short and have a single purpose
- [ ] Naming conventions are followed consistently
- [ ] Classes follow SOLID principles
- [ ] JSDoc documentation is provided for public APIs
- [ ] Error handling is implemented appropriately
- [ ] Code is modular and reusable

### Microservices Architecture

- [ ] Contracts are defined in `libs/contracts`
- [ ] gRPC services are properly defined in proto files
- [ ] Service communication uses gRPC exclusively
- [ ] Each service has its own dedicated database (no shared databases)
- [ ] Services are stateless and horizontally scalable
- [ ] Circuit breakers are implemented for external calls
- [ ] Proper logging and monitoring are in place

### Testing Standards

- [ ] Every endpoint has a corresponding `.feature` file in `features/` directory
- [ ] Feature files are approved before implementation begins
- [ ] BDD scenarios are implemented with Cucumber step definitions
- [ ] All BDD tests pass before endpoint is considered complete
- [ ] Unit tests cover all business logic
- [ ] Integration tests validate service interactions
- [ ] E2E tests cover complete user workflows
- [ ] gRPC contracts are tested between services
- [ ] Performance tests validate service scalability

### Security Standards

- [ ] JWT validation is implemented for authentication
- [ ] RBAC is enforced for authorization
- [ ] Rate limiting is configured appropriately
- [ ] Security headers are set correctly
- [ ] Input validation uses Zod schemas
- [ ] Sensitive data is properly encrypted

### Operations Standards

- [ ] Health check endpoints are implemented
- [ ] Metrics and telemetry are collected
- [ ] Proper error logging is in place
- [ ] Service discovery is configured
- [ ] Load balancing is set up correctly
- [ ] Backup and recovery procedures are documented
