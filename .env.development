# Qeep Development Environment Configuration
# This file contains all environment variables for local development
#
# DEVELOPMENT MODES:
# 1. Hybrid Development (Recommended): Infrastructure in Docker, Services local
#    - Run: pnpm run dev:hybrid:start
#    - Uses localhost for database/redis connections
#    - Enables NX hot reload for all services
#    - Better debugging and development experience
#
# 2. Full Docker Development: All services in Docker containers
#    - Run: pnpm run docker:dev:start
#    - Uses docker service names for connections
#    - Good for testing container behavior
#    - Limited hot reload due to NX daemon issues

# =============================================================================
# APPLICATION PORTS
# =============================================================================

# API Gateway (Main entry point)
API_GATEWAY_PORT=3000
API_GATEWAY_HOST=0.0.0.0

# Core Platform Services (HTTP)
AUTH_SERVICE_PORT=3001
AUTH_SERVICE_HOST=0.0.0.0

USER_SERVICE_PORT=3002
USER_SERVICE_HOST=0.0.0.0

TENANT_SERVICE_PORT=3003
TENANT_SERVICE_HOST=0.0.0.0

NOTIFICATION_SERVICE_PORT=3004
NOTIFICATION_SERVICE_HOST=0.0.0.0

AUDIT_SERVICE_PORT=3005
AUDIT_SERVICE_HOST=0.0.0.0

TRANSACTION_SERVICE_PORT=3006
TRANSACTION_SERVICE_HOST=0.0.0.0

AML_SERVICE_PORT=3007
AML_SERVICE_HOST=0.0.0.0

CUSTOMER_SERVICE_PORT=3008
CUSTOMER_SERVICE_HOST=0.0.0.0

SURVEILLANCE_SERVICE_PORT=3009
SURVEILLANCE_SERVICE_HOST=0.0.0.0

INTEGRATION_SERVICE_PORT=3010
INTEGRATION_SERVICE_HOST=0.0.0.0

MONITORING_SERVICE_PORT=3012
MONITORING_SERVICE_HOST=0.0.0.0

# Email Providers
DEFAULT_EMAIL_PROVIDER=smtp
EMAIL_TIMEOUT=30000
EMAIL_RETRY_ATTEMPTS=3
EMAIL_RATE_LIMIT=100
EMAIL_DAILY_LIMIT=10000

# Resend Configuration
# Resend Configuration
RESEND_API_KEY=re_5CWsDqoA_9VZdNSpkAtAq1CkxdvkgY5oQ
RESEND_FROM_EMAIL=<EMAIL>
RESEND_FROM_NAME=Qeep
RESEND_WEBHOOK_SECRET=whsec_tcoxYIS/6tL2xxCUO4TTvnPF72s2rt2V

# SendGrid Configuration (optional)
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=Qeep
SENDGRID_WEBHOOK_SECRET=your_sendgrid_webhook_secret

# Mailgun Configuration (optional)
MAILGUN_API_KEY=your_mailgun_api_key
MAILGUN_DOMAIN=your_mailgun_domain
MAILGUN_FROM_EMAIL=<EMAIL>
MAILGUN_FROM_NAME=Qeep
MAILGUN_BASE_URL=https://api.mailgun.net
MAILGUN_WEBHOOK_SECRET=your_mailgun_webhook_secret

# SMS Providers
DEFAULT_SMS_PROVIDER=twilio
SMS_TIMEOUT=30000
SMS_RETRY_ATTEMPTS=3
SMS_RATE_LIMIT=50
SMS_DAILY_LIMIT=1000

# Twilio Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_FROM_NUMBER=your_twilio_phone_number
TWILIO_WEBHOOK_SECRET=your_twilio_webhook_secret

# Application URLs
APP_URL=https://app.qeep.com
SUPPORT_EMAIL=<EMAIL>

# Template Configuration
TEMPLATE_CACHE_SIZE=1000
TEMPLATE_CACHE_TTL=3600

# gRPC Service Ports
AUTH_SERVICE_GRPC_PORT=3021
USER_SERVICE_GRPC_PORT=3022
TENANT_SERVICE_GRPC_PORT=3023
NOTIFICATION_SERVICE_GRPC_PORT=3024
AUDIT_SERVICE_GRPC_PORT=3025
TRANSACTION_SERVICE_GRPC_PORT=3026
AML_SERVICE_GRPC_PORT=3027
CUSTOMER_SERVICE_GRPC_PORT=3028
SURVEILLANCE_SERVICE_GRPC_PORT=3029
INTEGRATION_SERVICE_GRPC_PORT=3030
MONITORING_SERVICE_GRPC_PORT=3031

# =============================================================================
# SERVICE URLS (for inter-service communication)
# =============================================================================

# HTTP Service URLs
AUTH_SERVICE_URL=http://localhost:${AUTH_SERVICE_PORT}
USER_SERVICE_URL=http://localhost:${USER_SERVICE_PORT}
TENANT_SERVICE_URL=http://localhost:${TENANT_SERVICE_PORT}
NOTIFICATION_SERVICE_URL=http://localhost:${NOTIFICATION_SERVICE_PORT}
AUDIT_SERVICE_URL=http://localhost:${AUDIT_SERVICE_PORT}
TRANSACTION_SERVICE_URL=http://localhost:${TRANSACTION_SERVICE_PORT}
AML_SERVICE_URL=http://localhost:${AML_SERVICE_PORT}
CUSTOMER_SERVICE_URL=http://localhost:${CUSTOMER_SERVICE_PORT}
SURVEILLANCE_SERVICE_URL=http://localhost:${SURVEILLANCE_SERVICE_PORT}
INTEGRATION_SERVICE_URL=http://localhost:${INTEGRATION_SERVICE_PORT}
MONITORING_SERVICE_URL=http://localhost:${MONITORING_SERVICE_PORT}

# gRPC Service URLs
AUTH_SERVICE_GRPC_URL=localhost:${AUTH_SERVICE_GRPC_PORT}
USER_SERVICE_GRPC_URL=localhost:${USER_SERVICE_GRPC_PORT}
TENANT_SERVICE_GRPC_URL=localhost:${TENANT_SERVICE_GRPC_PORT}
NOTIFICATION_SERVICE_GRPC_URL=localhost:${NOTIFICATION_SERVICE_GRPC_PORT}
AUDIT_SERVICE_GRPC_URL=localhost:${AUDIT_SERVICE_GRPC_PORT}
TRANSACTION_SERVICE_GRPC_URL=localhost:${TRANSACTION_SERVICE_GRPC_PORT}
AML_SERVICE_GRPC_URL=localhost:${AML_SERVICE_GRPC_PORT}
CUSTOMER_SERVICE_GRPC_URL=localhost:${CUSTOMER_SERVICE_GRPC_PORT}
SURVEILLANCE_SERVICE_GRPC_URL=localhost:${SURVEILLANCE_SERVICE_GRPC_PORT}
INTEGRATION_SERVICE_GRPC_URL=localhost:${INTEGRATION_SERVICE_GRPC_PORT}
MONITORING_SERVICE_GRPC_URL=localhost:${MONITORING_SERVICE_GRPC_PORT}

# Docker gRPC Service URLs (for container communication)
AUTH_SERVICE_GRPC_URL_DOCKER=auth-service:${AUTH_SERVICE_GRPC_PORT}
USER_SERVICE_GRPC_URL_DOCKER=user-service:${USER_SERVICE_GRPC_PORT}
TENANT_SERVICE_GRPC_URL_DOCKER=tenant-service:${TENANT_SERVICE_GRPC_PORT}
NOTIFICATION_SERVICE_GRPC_URL_DOCKER=notification-service:${NOTIFICATION_SERVICE_GRPC_PORT}
AUDIT_SERVICE_GRPC_URL_DOCKER=audit-service:${AUDIT_SERVICE_GRPC_PORT}
TRANSACTION_SERVICE_GRPC_URL_DOCKER=transaction-service:${TRANSACTION_SERVICE_GRPC_PORT}
AML_SERVICE_GRPC_URL_DOCKER=aml-service:${AML_SERVICE_GRPC_PORT}
CUSTOMER_SERVICE_GRPC_URL_DOCKER=customer-service:${CUSTOMER_SERVICE_GRPC_PORT}
SURVEILLANCE_SERVICE_GRPC_URL_DOCKER=surveillance-service:${SURVEILLANCE_SERVICE_GRPC_PORT}
INTEGRATION_SERVICE_GRPC_URL_DOCKER=integration-service:${INTEGRATION_SERVICE_GRPC_PORT}
MONITORING_SERVICE_GRPC_URL_DOCKER=monitoring-service:${MONITORING_SERVICE_GRPC_PORT}

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Infrastructure
# For hybrid development (infrastructure in Docker, services local)
POSTGRES_HOST=localhost
POSTGRES_PORT=5433
POSTGRES_ROOT_USER=qeep_dev
POSTGRES_ROOT_PASSWORD=qeep_password_dev

# Service-specific database configurations
# Each service has its own dedicated database and user for better isolation

# Auth Service Database
AUTH_DB_NAME=qeep_auth
AUTH_DB_USER=qeep_auth_user
AUTH_DB_PASSWORD=qeep_auth_password

# User Service Database
USER_DB_NAME=qeep_user
USER_DB_USER=qeep_user_user
USER_DB_PASSWORD=qeep_user_password

# Tenant Service Database
TENANT_DB_NAME=qeep_tenant
TENANT_DB_USER=qeep_tenant_user
TENANT_DB_PASSWORD=qeep_tenant_password

# Notification Service Database
NOTIFICATION_DB_NAME=qeep_notification
NOTIFICATION_DB_USER=qeep_notification_user
NOTIFICATION_DB_PASSWORD=qeep_notification_password

# Audit Service Database
AUDIT_DB_NAME=qeep_audit
AUDIT_DB_USER=qeep_audit_user
AUDIT_DB_PASSWORD=qeep_audit_password

# Transaction Service Database
TRANSACTION_DB_NAME=qeep_transaction
TRANSACTION_DB_USER=qeep_transaction_user
TRANSACTION_DB_PASSWORD=qeep_transaction_password

# AML Service Database
AML_DB_NAME=qeep_aml
AML_DB_USER=qeep_aml_user
AML_DB_PASSWORD=qeep_aml_password

# Customer Service Database
CUSTOMER_DB_NAME=qeep_customer
CUSTOMER_DB_USER=qeep_customer_user
CUSTOMER_DB_PASSWORD=qeep_customer_password

# Surveillance Service Database
SURVEILLANCE_DB_NAME=qeep_surveillance
SURVEILLANCE_DB_USER=qeep_surveillance_user
SURVEILLANCE_DB_PASSWORD=qeep_surveillance_password

# Integration Service Database
INTEGRATION_DB_NAME=qeep_integration
INTEGRATION_DB_USER=qeep_integration_user
INTEGRATION_DB_PASSWORD=qeep_integration_password

# Monitoring Service Database
MONITORING_DB_NAME=qeep_monitoring
MONITORING_DB_USER=qeep_monitoring_user
MONITORING_DB_PASSWORD=qeep_monitoring_password

# Database URLs for local services connecting to Docker infrastructure
# Each service connects to its own dedicated database using public schema
DATABASE_URL=postgresql://${POSTGRES_ROOT_USER}:${POSTGRES_ROOT_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/qeep_dev?schema=public
AUTH_DATABASE_URL=postgresql://${AUTH_DB_USER}:${AUTH_DB_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${AUTH_DB_NAME}?schema=public
USER_DATABASE_URL=postgresql://${USER_DB_USER}:${USER_DB_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${USER_DB_NAME}?schema=public
TENANT_DATABASE_URL=postgresql://${TENANT_DB_USER}:${TENANT_DB_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${TENANT_DB_NAME}?schema=public
NOTIFICATION_DATABASE_URL=postgresql://${NOTIFICATION_DB_USER}:${NOTIFICATION_DB_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${NOTIFICATION_DB_NAME}?schema=public
AUDIT_DATABASE_URL=postgresql://${AUDIT_DB_USER}:${AUDIT_DB_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${AUDIT_DB_NAME}?schema=public
TRANSACTION_DATABASE_URL=postgresql://${TRANSACTION_DB_USER}:${TRANSACTION_DB_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${TRANSACTION_DB_NAME}?schema=public
AML_DATABASE_URL=postgresql://${AML_DB_USER}:${AML_DB_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${AML_DB_NAME}?schema=public
CUSTOMER_DATABASE_URL=postgresql://${CUSTOMER_DB_USER}:${CUSTOMER_DB_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${CUSTOMER_DB_NAME}?schema=public
SURVEILLANCE_DATABASE_URL=postgresql://${SURVEILLANCE_DB_USER}:${SURVEILLANCE_DB_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${SURVEILLANCE_DB_NAME}?schema=public
INTEGRATION_DATABASE_URL=postgresql://${INTEGRATION_DB_USER}:${INTEGRATION_DB_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${INTEGRATION_DB_NAME}?schema=public
MONITORING_DATABASE_URL=postgresql://${MONITORING_DB_USER}:${MONITORING_DB_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${MONITORING_DB_NAME}?schema=public

# =============================================================================
# NGINX PROXY CONFIGURATION
# =============================================================================

# Nginx Proxy Settings
NGINX_ENABLED=true
NGINX_PORT=8080
NGINX_HTTPS_PORT=8443
NGINX_LOG_LEVEL=warn
NGINX_PROXY_URL=http://localhost:8080

# Nginx Upstream Configuration (for OrbStack hybrid development)
NGINX_UPSTREAM_API_GATEWAY=host.orb.internal:3000
NGINX_UPSTREAM_AUTH_SERVICE=host.orb.internal:3001
NGINX_UPSTREAM_USER_SERVICE=host.orb.internal:3002
NGINX_UPSTREAM_TENANT_SERVICE=host.orb.internal:3003
NGINX_UPSTREAM_NOTIFICATION_SERVICE=host.orb.internal:3004
NGINX_UPSTREAM_AUDIT_SERVICE=host.orb.internal:3005

# Transaction Monitoring Services Nginx Upstream Configuration
NGINX_UPSTREAM_TRANSACTION_SERVICE=host.orb.internal:3006
NGINX_UPSTREAM_AML_SERVICE=host.orb.internal:3007
NGINX_UPSTREAM_CUSTOMER_SERVICE=host.orb.internal:3008
NGINX_UPSTREAM_SURVEILLANCE_SERVICE=host.orb.internal:3009
NGINX_UPSTREAM_INTEGRATION_SERVICE=host.orb.internal:3010
NGINX_UPSTREAM_MONITORING_SERVICE=host.orb.internal:3012

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# For hybrid development (infrastructure in Docker, services local)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis_dev_password
REDIS_URL=redis://:redis_dev_password@localhost:6379

# For full Docker development (all services in containers)
REDIS_HOST_DOCKER=redis
REDIS_PORT_DOCKER=6379
REDIS_URL_DOCKER=redis://:redis_dev_password@redis:6379

# =============================================================================
# EMAIL CONFIGURATION (Development)
# =============================================================================

# Mailpit - Email Testing & Debugging
MAILPIT_WEB_PORT=8026    # Web UI port (avoiding conflict with Supabase)
MAILPIT_SMTP_PORT=1026   # SMTP server port (avoiding conflict with Supabase)

# SMTP Configuration for development (using Mailpit)
SMTP_HOST=localhost
SMTP_PORT=${MAILPIT_SMTP_PORT}
SMTP_USER=""             # Mailpit accepts any credentials
SMTP_PASSWORD=""         # Mailpit accepts any credentials
SMTP_SECURE=false        # No TLS for development
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME="Qeep Development"

# Email URLs for different environments
EMAIL_SMTP_URL=smtp://${SMTP_HOST}:${SMTP_PORT}
EMAIL_SMTP_URL_DOCKER=smtp://mailpit:1025

# =============================================================================
# NOTIFICATION SERVICE EMAIL CONFIGURATION
# =============================================================================

# Default Email Provider (smtp for development with Mailpit)
DEFAULT_EMAIL_PROVIDER=smtp

# SMTP Configuration for Notification Service (uses Mailpit settings above)
# Note: SMTP_HOST and SMTP_PORT are already defined above for Mailpit
SMTP_IGNORE_TLS=true
SMTP_REQUIRE_TLS=false

# Email Service Configuration
EMAIL_TIMEOUT=30000
EMAIL_RETRY_ATTEMPTS=3
EMAIL_RATE_LIMIT=100
EMAIL_DAILY_LIMIT=10000

# =============================================================================
# KAFKA CONFIGURATION
# =============================================================================

KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=qeep-dev
KAFKA_GROUP_ID=qeep-dev-group

# Kafka Topics
KAFKA_TOPIC_USER_EVENTS=user.events
KAFKA_TOPIC_AUTH_EVENTS=auth.events
KAFKA_TOPIC_TENANT_EVENTS=tenant.events
KAFKA_TOPIC_NOTIFICATION_EVENTS=notification.events
KAFKA_TOPIC_AUDIT_EVENTS=audit.events
KAFKA_TOPIC_TRANSACTION_EVENTS=transaction.events
KAFKA_TOPIC_AML_EVENTS=aml.events
KAFKA_TOPIC_CUSTOMER_EVENTS=customer.events
KAFKA_TOPIC_SURVEILLANCE_EVENTS=surveillance.events
KAFKA_TOPIC_INTEGRATION_EVENTS=integration.events
KAFKA_TOPIC_MONITORING_EVENTS=monitoring.events

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# Auth0 Configuration
AUTH0_DOMAIN=qeep-dev.us.auth0.com
AUTH0_CLIENT_ID=your_dev_client_id
AUTH0_CLIENT_SECRET=your_dev_client_secret
AUTH0_AUDIENCE=https://api.qeep.dev

# Email Service (for notifications) - Using Mailpit
SMTP_HOST=localhost
SMTP_PORT=1026
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=<EMAIL>

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Environment
NODE_ENV=development
LOG_LEVEL=debug

# Security
JWT_SECRET=dev_jwt_secret_key_change_in_production
JWT_REFRESH_SECRET=dev_jwt_refresh_secret_key_change_in_production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# API Configuration
API_RATE_LIMIT=1000
API_TIMEOUT=30000

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:4200
CORS_CREDENTIALS=true

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================

# Prometheus
PROMETHEUS_PORT=9090
PROMETHEUS_HOST=localhost

# Grafana
GRAFANA_PORT=3100
GRAFANA_HOST=localhost
GRAFANA_ADMIN_PASSWORD=grafana_admin_password

# Jaeger Tracing
JAEGER_ENDPOINT=http://localhost:14268/api/traces
JAEGER_SERVICE_NAME=qeep

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================

# Hot Reload
WATCH_MODE=true
HOT_RELOAD=true

# Debug Configuration
DEBUG_PORT=9229
DEBUG_HOST=localhost

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable features for development
ENABLE_SWAGGER=true
ENABLE_METRICS=true
ENABLE_TRACING=true
ENABLE_RATE_LIMITING=true
ENABLE_CORS=true

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================

# Rate Limiting Feature Toggle
RATE_LIMITING_ENABLED=true

# Redis Configuration for Rate Limiting (hybrid development)
RATE_LIMITING_REDIS_HOST=localhost
RATE_LIMITING_REDIS_PORT=6379
RATE_LIMITING_REDIS_PASSWORD=redis_dev_password
RATE_LIMITING_REDIS_DB=2
RATE_LIMITING_REDIS_KEY_PREFIX=rate-limit

# Redis Configuration for Rate Limiting (full Docker development)
RATE_LIMITING_REDIS_HOST_DOCKER=redis
RATE_LIMITING_REDIS_PORT_DOCKER=6379

# Rate Limiting Rules (requests per minute)
RATE_LIMIT_PER_USER=100
RATE_LIMIT_PER_TENANT=1000
RATE_LIMIT_PER_IP=50
RATE_LIMIT_PER_ENDPOINT=200

# Rate Limiting Time Windows (seconds)
RATE_LIMIT_TTL_USER=60
RATE_LIMIT_TTL_TENANT=60
RATE_LIMIT_TTL_IP=60
RATE_LIMIT_TTL_ENDPOINT=60

# Block Duration (seconds) - how long to block after limit exceeded
RATE_LIMIT_BLOCK_DURATION_USER=300
RATE_LIMIT_BLOCK_DURATION_TENANT=300
RATE_LIMIT_BLOCK_DURATION_IP=600
RATE_LIMIT_BLOCK_DURATION_ENDPOINT=300

# Rate Limiting Features
RATE_LIMITING_ENABLE_METRICS=true
RATE_LIMITING_ENABLE_LOGGING=true

# Custom Rate Limits for Testing
RATE_LIMIT_HEALTH_CHECK=5
RATE_LIMIT_HEALTH_CHECK_TTL=60
RATE_LIMIT_HEALTH_CHECK_BLOCK=120

RATE_LIMIT_K8S_READINESS=100
RATE_LIMIT_K8S_READINESS_TTL=60
RATE_LIMIT_K8S_READINESS_BLOCK=60

RATE_LIMIT_K8S_LIVENESS=100
RATE_LIMIT_K8S_LIVENESS_TTL=60
RATE_LIMIT_K8S_LIVENESS_BLOCK=60

RATE_LIMIT_DETAILED_HEALTH=3
RATE_LIMIT_DETAILED_HEALTH_TTL=60
RATE_LIMIT_DETAILED_HEALTH_BLOCK=300

# =============================================================================
# SECURITY HEADERS CONFIGURATION
# =============================================================================

# HSTS (HTTP Strict Transport Security)
SECURITY_HEADERS_HSTS_ENABLED=false
SECURITY_HEADERS_HSTS_MAX_AGE=31536000
SECURITY_HEADERS_HSTS_INCLUDE_SUBDOMAINS=true
SECURITY_HEADERS_HSTS_PRELOAD=true

# Content Security Policy
SECURITY_HEADERS_CSP_ENABLED=true
SECURITY_HEADERS_CSP_REPORT_ONLY=true
SECURITY_HEADERS_CSP_DEFAULT_SRC='self'
SECURITY_HEADERS_CSP_SCRIPT_SRC="'self','unsafe-inline','unsafe-eval',localhost:*,127.0.0.1:*"
SECURITY_HEADERS_CSP_STYLE_SRC="'self','unsafe-inline'"
SECURITY_HEADERS_CSP_IMG_SRC="'self',data:,https:,http:"
SECURITY_HEADERS_CSP_CONNECT_SRC="'self',localhost:*,127.0.0.1:*,ws:,wss:"
SECURITY_HEADERS_CSP_FONT_SRC="'self',data:"
SECURITY_HEADERS_CSP_OBJECT_SRC='none'
SECURITY_HEADERS_CSP_UPGRADE_INSECURE_REQUESTS=false
SECURITY_HEADERS_CSP_BLOCK_MIXED_CONTENT=false

# Frame Options
SECURITY_HEADERS_FRAME_OPTIONS_ENABLED=true
SECURITY_HEADERS_FRAME_OPTIONS_POLICY=DENY

# Content Type Options
SECURITY_HEADERS_CONTENT_TYPE_OPTIONS_ENABLED=true
SECURITY_HEADERS_CONTENT_TYPE_OPTIONS_NOSNIFF=true

# XSS Protection
SECURITY_HEADERS_XSS_PROTECTION_ENABLED=true
SECURITY_HEADERS_XSS_PROTECTION_MODE=1; mode=block

# Referrer Policy
SECURITY_HEADERS_REFERRER_POLICY_ENABLED=true
SECURITY_HEADERS_REFERRER_POLICY=strict-origin-when-cross-origin

# Permissions Policy
SECURITY_HEADERS_PERMISSIONS_POLICY_ENABLED=true

# =============================================================================
# CIRCUIT BREAKER CONFIGURATION
# =============================================================================

# Cross-Origin Policies
SECURITY_HEADERS_COEP_ENABLED=false
SECURITY_HEADERS_COEP_POLICY=unsafe-none
SECURITY_HEADERS_COOP_ENABLED=true
SECURITY_HEADERS_COOP_POLICY=same-origin-allow-popups
SECURITY_HEADERS_CORP_ENABLED=true
SECURITY_HEADERS_CORP_POLICY=same-site

# Circuit Breaker Configuration
CIRCUIT_BREAKER_ENABLED=true
CIRCUIT_BREAKER_FAILURE_THRESHOLD=50
CIRCUIT_BREAKER_TIMEOUT=30000
CIRCUIT_BREAKER_REQUEST_VOLUME_THRESHOLD=10
CIRCUIT_BREAKER_SLEEP_WINDOW=30000
CIRCUIT_BREAKER_MAX_RETRIES=3
CIRCUIT_BREAKER_ENABLE_METRICS=true
CIRCUIT_BREAKER_ENABLE_LOGGING=true

# Service-specific Circuit Breaker Settings
CIRCUIT_BREAKER_AUTH_ENABLED=true
CIRCUIT_BREAKER_AUTH_FAILURE_THRESHOLD=50
CIRCUIT_BREAKER_AUTH_TIMEOUT=30000

# =============================================================================
# TENANT INTERCEPTOR CONFIGURATION
# =============================================================================

# Enable/disable tenant interceptor globally
TENANT_INTERCEPTOR_ENABLED=true

# Whether to validate tenant against tenant service
TENANT_INTERCEPTOR_VALIDATE=true

# Whether to require tenant for all requests (can be overridden per endpoint)
TENANT_INTERCEPTOR_REQUIRE=false

# Enable tenant information caching
TENANT_INTERCEPTOR_CACHE=true

# Cache TTL in seconds (default: 300 = 5 minutes)
TENANT_INTERCEPTOR_CACHE_TTL=300

# Header names for tenant extraction
TENANT_CODE_HEADER=x-tenant-code
TENANT_ID_HEADER=x-tenant-id

# Query parameter names for tenant extraction
TENANT_CODE_QUERY_PARAM=tenant_code
TENANT_ID_QUERY_PARAM=tenant_id

# JWT claim names for tenant extraction
JWT_TENANT_ID_CLAIM=tenant_id
JWT_TENANT_CODE_CLAIM=tenant_code

# Enable debug logging for tenant interceptor
TENANT_INTERCEPTOR_DEBUG=false

CIRCUIT_BREAKER_USER_ENABLED=true
CIRCUIT_BREAKER_USER_FAILURE_THRESHOLD=50
CIRCUIT_BREAKER_USER_TIMEOUT=30000

CIRCUIT_BREAKER_TENANT_ENABLED=true
CIRCUIT_BREAKER_TENANT_FAILURE_THRESHOLD=50
CIRCUIT_BREAKER_TENANT_TIMEOUT=30000

CIRCUIT_BREAKER_NOTIFICATION_ENABLED=true
CIRCUIT_BREAKER_NOTIFICATION_FAILURE_THRESHOLD=50
CIRCUIT_BREAKER_NOTIFICATION_TIMEOUT=30000

# =============================================================================
# ELASTICSEARCH (if needed for search)
# =============================================================================

ELASTICSEARCH_NODE=http://localhost:9200
ELASTICSEARCH_USERNAME=elastic
ELASTICSEARCH_PASSWORD=elastic_dev_password

# =============================================================================
# NEO4J (if needed for graph analysis)
# =============================================================================

NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=neo4j_dev_password
