{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals", "!{workspaceRoot}/libs/proto/src/generated/**/*"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.mjs", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s", "!{workspaceRoot}/libs/proto/src/generated/**/*"], "sharedGlobals": ["{workspaceRoot}/.github/workflows/ci.yml"]}, "nxCloudId": "", "plugins": [{"plugin": "@nx/webpack/plugin", "options": {"buildTargetName": "build", "serveTargetName": "serve", "previewTargetName": "preview", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "test"}, "exclude": ["apps/auth-service-e2e/**/*", "apps/user-service-e2e/**/*", "apps/notification-service-e2e/**/*", "apps/tenant-service-e2e/**/*", "apps/audit-service-e2e/**/*", "apps/transaction-service-e2e/**/*", "apps/aml-service-e2e/**/*", "apps/integration-service-e2e/**/*", "apps/monitoring-service-e2e/**/*", "apps/customer-service-e2e/**/*"]}], "targetDefaults": {"@nx/js:tsc": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}}}